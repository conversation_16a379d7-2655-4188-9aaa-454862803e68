## Relevant Files

- `src/docs/openapi/openapi.yaml` - Main OpenAPI specification file.
- `src/docs/openapi/split/paths/quiz_ai-create.yaml` - Defines the POST /quiz/ai-create endpoint.
- `src/docs/openapi/split/paths/quiz_ai-create_status_{jobId}.yaml` - Defines the GET /quiz/ai-create/status/{jobId} endpoint.
- `src/docs/openapi/split/components/schemas/AiQuizCreationRequest.yaml` - Defines the request schema for AI quiz creation.
- `src/docs/openapi/split/components/schemas/AiQuizCreationResponse.yaml` - Defines the response schema for AI quiz creation initiation.
- `src/docs/openapi/split/components/schemas/AiQuizCreationStatusResponse.yaml` - Defines the response schema for AI quiz creation job status.
- `src/main/java/com/f_quiz/quiz/api/QuizController.java` - Controller for handling quiz-related requests.
- `src/test/java/com/f_quiz/quiz/api/QuizControllerWebTest.java` - MockMVC tests for the QuizController.
- `src/main/java/com/f_quiz/quiz/service/QuizService.java` - Service layer for quiz logic.
- `src/test/java/com/f_quiz/quiz/service/QuizServiceTest.java` - Unit tests for the QuizService.
- `src/main/java/com/f_quiz/quiz/repository/QuizRepository.java` - Repository for accessing quiz data.
- `src/dbTest/java/com/f_quiz/quiz/repository/QuizRepositoryDbTest.java` - Database tests for the QuizRepository.
- `src/main/java/com/f_quiz/quiz/domain/Quiz.java` - Domain model for a quiz.
- `src/main/java/com/f_quiz/quiz/domain/User.java` - Domain model for a user (to associate quiz ownership).
- `src/main/java/com/f_quiz/quiz/job/AiQuizCreationJob.java` - New asynchronous job for AI quiz creation.
- `src/main/java/com/f_quiz/quiz/job/JobScheduler.java` - Existing job scheduler.
- `src/main/java/com/f_quiz/quiz/exception/TooManyRequestsException.java` - Existing or new exception for rate limiting.
- `src/main/resources/application.yaml` - Configuration file for roles and potentially job resources.
- `f_quiz_generation/src/quiz_gemini.py` - Python script for AI interaction.
- `f_quiz_generation/src/gen_import_quiz.py` - Python script for CSV validation.
- `src/acceptanceTest/java/com/f_quiz/acceptance/AiQuizCreationAcceptanceTest.java` - Acceptance tests for the AI quiz creation feature.
- `src/main/java/fukoka/fquiz/service/UserStatsService.java` - Existing service for user statistics, including job scheduling pattern.
- `build/generated/src/main/java/fukoka/fquiz/api/model/AiQuizCreationRequest.java` - Generated model for AI quiz creation request.
- `build/generated/src/main/java/fukoka/fquiz/api/model/AiQuizCreationResponse.java` - Generated model for AI quiz creation response.

### Notes

- Unit tests should typically be placed alongside the code files they are testing (e.g., `MyComponent.tsx` and `MyComponent.test.tsx` in the same directory).
- Use `npx jest [optional/path/to/test/file]` to run tests. Running without a path executes all tests found by the Jest configuration.

## Tasks

- [x] 1.0 Update OpenAPI Specification
  - [x] 1.1 Identify the appropriate split file in `src/docs/openapi/splits` for quiz-related endpoints (likely `quiz.yaml`).
  - [x] 1.2 Add the new `/quiz/ai-create` path definition (POST) to the identified split file, including request body schema.
  - [x] 1.3 Add the new `/quiz/ai-create/status/{jobId}` path definition (GET) to the identified split file, including response schema.
  - [x] 1.4 Define or reference necessary schemas for request and response payloads within the splits or a dedicated schemas file.
  - [x] 1.5 Consider adding a new tag for "AI quiz creation" or updating an existing one in the main `openapi.yaml` or a relevant split.
- [x] 2.0 Implement Backend Endpoint for AI Quiz Creation
  - [x] 2.1 Define the `POST /quiz/ai-create` endpoint in `QuizController`.
  - [ ] 2.2 Delegate user authentication and role validation to a service method.
  - [ ] 2.3 Implement daily limit enforcement via a service method.
  - [ ] 2.4 Handle `TooManyRequestsException` thrown by the service and return `HTTP 429 Too Many Requests`.
  - [ ] 2.5 Delegate initiating the asynchronous AI quiz creation job to a service method.
  - [ ] 2.6 Return `HTTP 202 Accepted` status code with a job identifier provided by the service.
  - [ ] 2.7 Write unit tests for the controller logic (outside of Spring), mocking the service, including tests for unauthorized access (non-paying roles).
  - [ ] 2.8 Study an existing MockMVC test class (e.g., `QuizControllerWebTest.java`) to understand the structure and patterns.
  - [ ] 2.9 Write MockMVC tests for endpoint validation, serialization/deserialization, error handling, and access control (`QuizControllerWebTest`).
- [ ] 3.0 Implement Backend Endpoint for AI Quiz Status Tracking
  - [ ] 3.1 Define the `GET /quiz/ai-create/status/{jobId}` endpoint in `QuizController`.
  - [ ] 3.2 Delegate retrieving the status and attempt count for the specified job ID to a service method.
  - [ ] 3.3 Return the status and attempt count provided by the service in the response.
  - [ ] 3.4 Write unit tests for the controller logic, mocking the service, including tests for unauthorized access to other users' job statuses.
  - [ ] 3.5 Study an existing MockMVC test class (e.g., `QuizControllerWebTest.java`) to understand the structure and patterns.
  - [ ] 3.6 Write MockMVC tests for the endpoint, including access control tests.
- [ ] 4.0 Implement Asynchronous AI Quiz Creation Job
  - [ ] 4.1 Define a new `Runnable` task class for AI quiz creation (e.g., `AiQuizCreationTask`).
  - [ ] 4.2 Implement the `run` method in `AiQuizCreationTask` to contain the job execution logic, utilizing service methods for business logic.
  - [ ] 4.3 Utilize `f_quiz_generation/src/quiz_gemini.py` for AI interaction within a service method called by the task.
  - [ ] 4.4 Implement batching and retry logic to obtain 50 valid questions (up to 4 attempts) within a service method called by the task.
  - [ ] 4.5 Utilize `f_quiz_generation/src/gen_import_quiz.py` for CSV validation within a service method called by the task.
  - [ ] 4.6 Discard invalid questions and attempt to generate replacements via service calls from the task.
  - [ ] 4.7 Update job status to 'completed' or 'failed' via a service method called by the task.
  - [ ] 4.8 Associate the created quiz with the initiating user's `userId` via a service method called by the task.
  - [ ] 4.9 Submit an instance of `AiQuizCreationTask` to an `ExecutorService` from the service layer.
  - [ ] 4.10 Write unit tests for the `AiQuizCreationTask` logic, mocking service and external dependencies.
- [ ] 5.0 Update Database and Backend Logic for Quiz Ownership and Exclusion
  - [ ] 5.1 Modify the `Quiz` domain model to include the owner's `userId`.
  - [ ] 5.2 Update the database schema to add a `userId` column to the quizzes table, potentially as a foreign key to the users table.
  - [ ] 5.3 Update `QuizRepository` to handle the new `userId` field.
  - [ ] 5.4 Write database tests for `QuizRepository` changes, ensuring correct association with `userId`.
  - [ ] 5.5 Modify the random quiz selection logic in the service layer to exclude quizzes where the `userId` column is not null.
  - [ ] 5.6 Write unit tests for the modified random quiz selection logic in the service, ensuring quizzes with owners are excluded for all users except the owner (if applicable, though PRD says private to owner).
- [ ] 6.0 Add Acceptance Tests
  - [ ] 6.1 Base acceptance tests on the OpenAPI specification to ensure coverage of the API contract.
  - [ ] 6.2 Write acceptance tests covering the end-to-end flow of AI quiz creation and status tracking, including tests for different user roles (paying vs non-paying).
  - [ ] 6.3 Ensure tests cover successful creation, daily limit enforcement, job failure scenarios, and unauthorized access attempts.
- [ ] 7.0 Update Pricing Plan Benefits (if backend driven)
  - [ ] 7.1 Include "Custom Quiz Generation" as a benefit for paying plans in `application.yaml` or relevant configuration.
- [ ] 8.0 Ensure Checkstyle and Gradle Build Pass
  - [ ] 8.1 Run `./gradlew clean build` and fix any errors.
  - [ ] 8.2 Run `./gradlew bootBuildImage` and fix any errors.
  - [ ] 8.3 Run `./gradlew cleanAcceptanceTest acceptanceTest` and fix any errors.
