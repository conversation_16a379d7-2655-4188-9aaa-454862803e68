python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT 1-Click'                     --category b65b6b5a-0cf7-44f1-8855-18dfc5d0cc8e --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_1_click_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_1_click_developer.json --title 'AWS IoT 1-Click for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Analytics'       --category d5729f63-4e3d-450c-bba2-8a740f3bb013 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_analytics_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_analytics_developer.json --title 'AWS IoT Analytics for developers'  
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Button'                      --category 597f65f7-6900-4f23-9a96-0f2d7e658680 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_button_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_button_developer.json --title 'AWS IoT Button for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Core'                        --category 02f0ec3b-0a4e-467a-9d95-a9c7f5354c06 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_core_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_core_developer.json --title 'AWS IoT Core for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Device Defender'             --category 6aeb8f56-69ad-4d97-8675-ff121875bb52 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_device_defender_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_device_defender_developer.json --title 'AWS IoT Device Defender for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Device Management'           --category 555ecd60-fdeb-4ed0-a057-7fb59bc9acab --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_device_management_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_device_management_developer.json --title 'AWS IoT Device Management for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Events'                      --category b6ccb117-9432-49b6-8876-e0b589aff194 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_events_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_events_developer.json --title 'AWS IoT Events for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT ExpressLink'                 --category acc752a1-58c9-4bbc-98d3-8cdc655dc81d --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_expresslink_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_expresslink_developer.json --title 'AWS IoT ExpressLink for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT FleetWise'                   --category b364c94f-2b0b-4448-813a-3c3a3a5403e0 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_fleetwise_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_fleetwise_developer.json --title 'AWS IoT FleetWise for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT Greengrass'                  --category fa287c3b-b098-4653-9bc8-d7b23b1ea49b --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_greengrass_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_greengrass_developer.json --title 'AWS IoT Greengrass for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT SiteWise'                    --category fdf1ca28-13e9-4689-859d-f4084147fb7f --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_sitewise_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_sitewise_developer.json --title 'AWS IoT SiteWise for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS IoT TwinMaker'                   --category 8d8b1670-8285-4c69-bf94-6eb5e022723a --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_iot_twinmaker_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_iot_twinmaker_developer.json --title 'AWS IoT TwinMaker for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Partner Device Catalog'          --category e8eca716-0b6b-414b-8553-602f8e8cbcd8 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/aws_partner_device_catalog_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/aws_partner_device_catalog_developer.json --title 'AWS Partner Device Catalog for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'FreeRTOS'                            --category d058ebe9-a8fc-4188-a20d-bb7e9c298cdd --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/internet_of_things_\(iot\)/freertos_developer.csv --metadata-file src/main/data/imports/metadata/internet_of_things_\(iot\)/freertos_developer.json --title 'FreeRTOS for developers'