#!/usr/bin/env bash

main_topic='End user computing'
categories=$(cat <<EOF
Amazon AppStream 2.0
Amazon WorkSpaces
Amazon WorkSpaces Core
Amazon WorkSpaces Thin Client
Amazon Workspaces Web
EOF
)

readarray -t categories_array <<< "$categories"

lc_main_topic=$(echo "$main_topic" | tr '[:upper:]' '[:lower:]' | tr ' ' '_' | sed -e 's/,//g')
echo "" > src/main/data/imports/metadata/${lc_main_topic}/_${lc_main_topic}_developer_gemini.sh
for category in "${categories_array[@]}"; do
    lc_category=$(echo "$category" | tr '[:upper:]' '[:lower:]' | tr ' ' '_')
    echo "sleep 60 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic \"${category}\" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/${lc_main_topic}/${lc_category}_developer.csv --metadata-file src/main/data/imports/metadata/${lc_main_topic}/${lc_category}_developer.json --title \"${category} for developers\"" >> src/main/data/imports/metadata/${lc_main_topic}/_${lc_main_topic}_developer_gemini.sh
done