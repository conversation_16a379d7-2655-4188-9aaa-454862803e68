
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon AppStream 2.0" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/end_user_computing/amazon_appstream_2.0_developer.csv --metadata-file src/main/data/imports/metadata/end_user_computing/amazon_appstream_2.0_developer.json --title "Amazon AppStream 2.0 for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon WorkSpaces" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/end_user_computing/amazon_workspaces_developer.csv --metadata-file src/main/data/imports/metadata/end_user_computing/amazon_workspaces_developer.json --title "Amazon WorkSpaces for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon WorkSpaces Core" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/end_user_computing/amazon_workspaces_core_developer.csv --metadata-file src/main/data/imports/metadata/end_user_computing/amazon_workspaces_core_developer.json --title "Amazon WorkSpaces Core for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon WorkSpaces Thin Client" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/end_user_computing/amazon_workspaces_thin_client_developer.csv --metadata-file src/main/data/imports/metadata/end_user_computing/amazon_workspaces_thin_client_developer.json --title "Amazon WorkSpaces Thin Client for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Workspaces Web" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/end_user_computing/amazon_workspaces_web_developer.csv --metadata-file src/main/data/imports/metadata/end_user_computing/amazon_workspaces_web_developer.json --title "Amazon Workspaces Web for developers"
