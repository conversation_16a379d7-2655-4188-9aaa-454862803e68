
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Cognito" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_cognito_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_cognito_developer.json --title "Amazon Cognito for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Detective" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_detective_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_detective_developer.json --title "Amazon Detective for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon GuardDuty" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_guardduty_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_guardduty_developer.json --title "Amazon GuardDuty for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Inspector" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_inspector_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_inspector_developer.json --title "Amazon Inspector for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Macie" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_macie_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_macie_developer.json --title "Amazon Macie for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Security Lake" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_security_lake_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_security_lake_developer.json --title "Amazon Security Lake for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "Amazon Verified Permissions" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/amazon_verified_permissions_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/amazon_verified_permissions_developer.json --title "Amazon Verified Permissions for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Artifact" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_artifact_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_artifact_developer.json --title "AWS Artifact for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Audit Manager" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_audit_manager_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_audit_manager_developer.json --title "AWS Audit Manager for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Certificate Manager" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_certificate_manager_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_certificate_manager_developer.json --title "AWS Certificate Manager for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS CloudHSM" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_cloudhsm_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_cloudhsm_developer.json --title "AWS CloudHSM for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Directory Service" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_directory_service_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_directory_service_developer.json --title "AWS Directory Service for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Firewall Manager" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_firewall_manager_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_firewall_manager_developer.json --title "AWS Firewall Manager for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Identity and Access Management" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_identity_and_access_management_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_identity_and_access_management_developer.json --title "AWS Identity and Access Management (IAM) for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Key Management Service" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_key_management_service_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_key_management_service_developer.json --title "AWS Key Management Service for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Network Firewall" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_network_firewall_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_network_firewall_developer.json --title "AWS Network Firewall for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Resource Access Manager" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_resource_access_manager_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_resource_access_manager_developer.json --title "AWS Resource Access Manager for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Secrets Manager" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_secrets_manager_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_secrets_manager_developer.json --title "AWS Secrets Manager for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Security Hub" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_security_hub_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_security_hub_developer.json --title "AWS Security Hub for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS Shield" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_shield_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_shield_developer.json --title "AWS Shield for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS IAM Identity Center" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_iam_identity_center_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_iam_identity_center_developer.json --title "AWS IAM Identity Center for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS WAF" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_waf_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_waf_developer.json --title "AWS WAF for developers"
sleep 10 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic "AWS WAF Captcha" --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/security_identity_and_compliance/aws_waf_captcha_developer.csv --metadata-file src/main/data/imports/metadata/security_identity_and_compliance/aws_waf_captcha_developer.json --title "AWS WAF Captcha for developers"
