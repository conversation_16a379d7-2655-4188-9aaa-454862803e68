!#!/bin/bash
# This script generates quizzes for the Cloud Financial Management Developer role using the Gemini model.
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Application Cost Profiler' --category 82867ca6-d2ca-481f-bd8b-970bfae066eb --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_application_cost_profiler_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_application_cost_profiler_developer.json --title 'AWS Application Cost Profiler for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Billing Conductor' --category a78e94bb-0354-409b-a1ba-b0f3a8a7eeec --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_billing_conductor_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_billing_conductor_developer.json --title 'AWS Billing Conductor for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Cost Explorer' --category 10026c3c-2d7e-4c34-8f3f-00c4c43daf2d --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_cost_explorer_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_cost_explorer_developer.json --title 'AWS Cost Explorer for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Budgets' --category fd1ade39-4283-45ae-8a62-f822fc09ce65 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_budgets_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_budgets_developer.json --title 'AWS Budgets for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Cost and Usage Report' --category a666654e-1c9a-4c72-b169-411100464f90 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_cost_and_usage_report_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_cost_and_usage_report_developer.json --title 'AWS Cost and Usage Report for developers'
sleep 30 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Reserved Instance (RI) reporting' --category 6ae540ff-4a5a-404a-814d-1d06b8957062 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_reserved_instance_reporting_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_reserved_instance_reporting_developer.json --title 'AWS Reserved Instance (RI) reporting for developers'
sleep 30 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Saving Plans' --category 158cd67b-77b7-4f65-9e6a-d46d8ded9f74 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/cloud_financial_management/aws_saving_plans_developer.csv --metadata-file src/main/data/imports/metadata/cloud_financial_management/aws_saving_plans_developer.json --title 'AWS Saving Plans for developers'
