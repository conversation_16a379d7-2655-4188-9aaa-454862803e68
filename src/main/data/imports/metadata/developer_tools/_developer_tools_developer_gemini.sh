#!/bin/bash
# This script generates quizzes for the Developer Tools Developer role using the Gemini model.
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon CodeCatalyst' --category 888c005f-1fa3-4e2e-801a-86190a37ea67 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/amazon_codecatalyst_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/amazon_codecatalyst_developer.json --title 'Amazon CodeCatalyst for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon CodeCommit' --category 9499902e-8e6f-4152-a7ef-31e6099dd794 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/amazon_codecommit_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/amazon_codecommit_developer.json --title 'Amazon CodeCommit for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon Corretto' --category bcbc4c9f-9779-4c0b-b7c6-0b5821306c7c --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/amazon_corretto_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/amazon_corretto_developer.json --title 'Amazon Corretto for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Application Composer' --category f751ebe8-6d35-42d5-8f65-01e7d257ace0 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_application_composer_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_application_composer_developer.json --title 'AWS Application Composer for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Cloud9' --category ebfc2c4b-c148-4723-b489-f84fe3edd405 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_cloud9_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_cloud9_developer.json --title 'AWS Cloud9 for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CloudShell' --category e8267fa8-5cc5-4838-be45-58fb3cf24418 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_cloudshell_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_cloudshell_developer.json --title 'AWS CloudShell for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodeArtifact' --category 26efb8ee-f5b3-4901-882e-4f60bcf9fc0a --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codeartifact_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codeartifact_developer.json --title 'AWS CodeArtifact for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodeBuild' --category b112d93d-f9a8-429e-ba13-59474521350a --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codebuild_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codebuild_developer.json --title 'AWS CodeBuild for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodeCommit' --category 9499902e-8e6f-4152-a7ef-31e6099dd794 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codecommit_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codecommit_developer.json --title 'AWS CodeCommit for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodeDeploy' --category 6a6e8153-dd5c-438a-b046-d8dd0cbeeb4a --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codedeploy_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codedeploy_developer.json --title 'AWS CodeDeploy for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodePipeline' --category 3cf3a25f-8a93-4e62-aae1-6d3333b6ad20 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codepipeline_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codepipeline_developer.json --title 'AWS CodePipeline for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS CodeStar' --category 2c273778-74f2-4129-bcb4-0a23d64c8c89 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_codestar_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_codestar_developer.json --title 'AWS CodeStar for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS Fault Injection Service' --category f5d8e41e-c08a-49c8-8baf-993e176662aa --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_fault_injection_service_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_fault_injection_service_developer.json --title 'AWS Fault Injection Service for developers'
sleep 120 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS X-Ray' --category dceee62f-02f8-4669-98e4-64ea849ce9ee --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/developer_tools/aws_x-ray_developer.csv --metadata-file src/main/data/imports/metadata/developer_tools/aws_x-ray_developer.json --title 'AWS X-Ray for developers'
