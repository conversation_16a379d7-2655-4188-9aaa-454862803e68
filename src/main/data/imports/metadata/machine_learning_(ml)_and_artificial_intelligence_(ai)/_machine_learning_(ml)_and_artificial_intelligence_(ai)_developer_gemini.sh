#!/bin/bash
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Augmented AI' --category caccc131-35c5-4d66-afb9-66a78d202ad1 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_augmented_ai_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_augmented_ai_developer.json --title 'Amazon Augmented AI for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Bedrock' --category 19472651-f764-4754-ac18-6c31e0fe1367 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_bedrock_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_bedrock_developer.json --title 'Amazon Bedrock for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon CodeGuru' --category f812363d-b702-4905-a340-a87990bdca6e --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_codeguru_developer_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_codeguru_developer.json --title 'Amazon CodeGuru for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Comprehend' --category ca5c6d1b-b5c0-4eb9-877a-e621688f4ef9 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_comprehend_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_comprehend_developer.json --title 'Amazon Comprehend for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Comprehend Medical' --category ea6f9846-a2fa-4568-b1fa-2ec232e933df --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_comprehend_medical_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_comprehend_medical_developer.json --title 'Amazon Comprehend Medical for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon DevOps Guru' --category 0c87ba2d-4cc8-4dc7-80bc-39157ef656e6 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_devops_guru_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_devops_guru_developer.json --title 'Amazon DevOps Guru for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Forecast' --category 1ce75477-9ba4-428b-a7ca-a3de9d61946c --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_forecast_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_forecast_developer.json --title 'Amazon Forecast for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Fraud Detector' --category 03e18a22-28a8-4c1b-a3d7-d32ac720f4e3 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_fraud_detector_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_fraud_detector_developer.json --title 'Amazon Fraud Detector for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Kendra for developers' --category 0f22a3ab-a0a3-4ff9-a30e-a166b134f252 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_kendra_developer_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_kendra_developer_developer.json --title 'Amazon Kendra for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Lex' --category ab820813-fc8d-46c4-bbb9-4172f66e1af0 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lex_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lex_developer.json --title 'Amazon Lex for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Lookout for Equipment' --category 93e2584b-efd4-4f9a-990b-997589cfe126 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_equipment_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_equipment_developer.json --title 'Amazon Lookout for Equipment for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Lookout for Metrics' --category 69087488-2e63-4ecb-a425-d12a3a83dee3 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_metrics_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_metrics_developer.json --title 'Amazon Lookout for Metrics for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Lookout for Vision' --category 07ef215e-cbe9-49a7-a647-0aa28bbd8da7 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_vision_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_lookout_for_vision_developer.json --title 'Amazon Lookout for Vision for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Monitron' --category b038969b-330a-49a3-a7b8-ca7154a442bb --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_monitron_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_monitron_developer.json --title 'Amazon Monitron for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon PartyRock' --category c40b485f-2565-4634-92da-c11f47007102 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_partyrock_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_partyrock_developer.json --title 'Amazon PartyRock for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Personalize' --category 94309f94-9bc3-4a3c-98e0-bc62c46e5274 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_personalize_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_personalize_developer.json --title 'Amazon Personalize for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Polly' --category f92a4848-211b-4bdd-a717-94abec48a3b2 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_polly_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_polly_developer.json --title 'Amazon Polly for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Q' --category 2bb46cfd-fe6f-4e3c-8f82-d9a7537d112f --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_q_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_q_developer_developer.json --title 'Amazon Q for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Rekognition' --category 32ea19b8-b73c-4b7c-9577-15defebbeef8 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_rekognition_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_rekognition_developer.json --title 'Amazon Rekognition for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon SageMaker' --category d0cf823f-a498-4706-b77b-bc8568ad1e75 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_sagemaker_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_sagemaker_developer.json --title 'Amazon SageMaker for developers'

sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Textract' --category da3368aa-fcaa-43d2-9107-6bb7ac209655 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_textract_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_textract_developer.json --title 'Amazon Textract for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Transcribe' --category d8f5ad96-20db-48d1-9540-06eed1da66af --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_transcribe_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_transcribe_developer.json --title 'Amazon Transcribe for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'Amazon Translate' --category 77e0497b-afa6-411c-8135-4dba6e5b441e --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_translate_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/amazon_translate_developer.json --title 'Amazon Translate for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS DeepComposer' --category 82b3cfe8-d739-40ff-94e5-baf068121e31 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deepcomposer_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deepcomposer_developer.json --title 'AWS DeepComposer for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS DeepLens' --category 1e58cb8f-e157-4494-a44f-b0428977456b --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deeplens_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deeplens_developer.json --title 'AWS DeepLens for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS DeepRacer' --category d3740f6c-0cd0-46fe-8912-e2e5e3baf2cf --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deepracer_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_deepracer_developer.json --title 'AWS DeepRacer for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS HealthLake' --category 86445a20-8789-4676-9c8a-104e58868f2c --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_healthlake_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_healthlake_developer.json --title 'AWS HealthLake for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS HealthScribe' --category a417a7a0-91af-40ff-b47d-0759cdcb46ff --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_healthscribe_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_healthscribe_developer.json --title 'AWS HealthScribe for developers'
sleep 0 && python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 0 --topic 'AWS Panorama' --category f3bfe40b-cedf-4a01-abe0-30c8ef1c9b38 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_panorama_developer.csv --metadata-file src/main/data/imports/metadata/machine_learning_\(ml\)_and_artificial_intelligence_\(ai\)/aws_panorama_developer.json --title 'AWS Panorama for developers'

