python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon AppFlow'                                         --category 0be58445-f388-4593-8995-34ef29f7c643 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/application_integration/amazon_appflow_developer.csv --metadata-file src/main/data/imports/metadata/application_integration/amazon_appflow_developer.json --title 'Amazon AppFlow for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'AWS B2B Data Interchange'                                --category 926001aa-db34-4c4a-9cdd-b4a73a3fc604 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/application_integration/aws_b2b_data_interchange_developer.csv --metadata-file src/main/data/imports/metadata/application_integration/aws_b2b_data_interchange_developer.json --title 'AWS B2B Data Interchange for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon Managed Workflows for Apache Airflow (MWAA)'     --category f1c00310-537c-427a-8454-8ec04bddb11c --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/application_integration/amazon_managed_workflows_for_apache_airflow_developer.csv --metadata-file src/main/data/imports/metadata/application_integration/amazon_managed_workflows_for_apache_airflow_developer.json --title 'Amazon Managed Workflows for Apache Airflow (MWAA) for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon MQ'                                              --category 6857787b-9610-4db9-abf1-f0346e9c5fa0 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/application_integration/amazon_mq_developer.csv --metadata-file src/main/data/imports/metadata/application_integration/amazon_mq_developer.json --title 'Amazon MQ for developers'
python3 ./f_quiz_generation/src/gen_import_quiz.py --questions 100 --topic 'Amazon Simple Workflow Service'                         --category 567016d1-889e-446d-a00b-348396985b49 --required-roles aws --guidance-file prompts/quizzes_developer.prompt.md --csv-file src/main/data/csv/application_integration/amazon_simple_workflow_service_developer.csv --metadata-file src/main/data/imports/metadata/application_integration/amazon_simple_workflow_service_developer.json --title 'Amazon Simple Workflow Service for developers'