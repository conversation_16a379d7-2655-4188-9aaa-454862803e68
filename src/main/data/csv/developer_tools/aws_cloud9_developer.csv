"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Cloud9, what is the primary function of a Cloud9 environment?","A preconfigured development environment in the cloud","A service for managing AWS IAM roles","A tool for monitoring AWS resource utilisation","A platform for hosting static websites","A Cloud9 environment provides a preconfigured and ready-to-use development environment, accessible through a web browser."
"Which programming languages are natively supported in AWS Cloud9 environments?","JavaScript, Python, PHP","Java, C++, C#","Ruby, Go, Swift","Kotl<PERSON>, Scala, Rust","AWS Cloud9 comes with native support for JavaScript, Python, and PHP, making it easy to start developing in these languages."
"What type of user interface does AWS Cloud9 provide for interacting with the development environment?","Web-based IDE","Command-line interface only","Native desktop application","Mobile application","Cloud9 is a web-based IDE, meaning you access it through a web browser."
"What is a key benefit of using AWS Cloud9 over a local IDE?","Simplified collaboration and sharing of development environments","Higher performance for computationally intensive tasks","Greater customisation options for the IDE","Lower costs for small projects","Cloud9 simplifies collaboration by allowing developers to easily share their development environments."
"How does AWS Cloud9 handle code collaboration among multiple developers?","It allows real-time collaborative coding within the same environment","It uses a Git-based version control system","It requires manual code merging","It uses a proprietary locking mechanism","Cloud9 allows multiple developers to code in real-time within the same environment."
"Which AWS service is deeply integrated with AWS Cloud9 for serverless application development?","AWS Lambda","Amazon EC2","Amazon S3","Amazon DynamoDB","AWS Lambda integrates well with Cloud9, allowing developers to easily create, test, and deploy serverless applications."
"In AWS Cloud9, what is the purpose of the 'runner' configuration?","To define how to execute your application","To manage environment variables","To configure AWS IAM roles","To set up debugging tools","The 'runner' configuration in Cloud9 defines how to execute your application (e.g., running a Node.js server)."
"How can you connect an AWS Cloud9 environment to an existing Amazon EC2 instance?","By specifying the EC2 instance ID when creating the environment","By using SSH keys to establish a connection","By using the AWS Management Console","By using the Cloud9 CLI","You can connect a Cloud9 environment to an existing EC2 instance by specifying its ID when creating the environment."
"Which debugger does AWS Cloud9 integrate with for debugging Node.js applications?","Node Inspector","GDB","LLDB","Xdebug","Cloud9 integrates with Node Inspector, a powerful debugger for Node.js applications."
"What is the purpose of the AWS Cloud9 IDE's built-in terminal?","To execute shell commands and interact with the underlying operating system","To manage AWS resources","To edit configuration files","To run database queries","The built-in terminal allows you to execute shell commands and interact with the operating system of the Cloud9 environment."
"How does AWS Cloud9 support version control?","It has built-in integration with Git","It has built-in integration with SVN","It provides a proprietary version control system","It does not support version control directly","Cloud9 has built-in integration with Git, a widely used distributed version control system."
"What is the purpose of the AWS Cloud9 welcome screen?","To provide quick access to common tasks and documentation","To display system resource utilisation","To show the latest AWS news","To allow configuring the IDE's theme","The welcome screen provides quick access to common tasks, documentation, and tutorials, helping users get started quickly."
"How can you change the theme and layout of the AWS Cloud9 IDE?","Through the IDE's settings menu","By editing a configuration file","By using the AWS CLI","Through the AWS Management Console","You can change the theme and layout of the Cloud9 IDE through the IDE's settings menu."
"What security feature does AWS Cloud9 use to control access to the development environment?","AWS IAM roles and policies","SSH keys","Username and password authentication","Multi-factor authentication","Cloud9 uses AWS IAM roles and policies to control access to the development environment."
"How can you share an AWS Cloud9 environment with another user?","By inviting the user through the IDE","By sharing the environment's URL","By creating a user account within the environment","By granting the user access to the underlying EC2 instance","You can share a Cloud9 environment by inviting the user through the IDE, granting them access based on their IAM permissions."
"What AWS resource is typically created when you create a new AWS Cloud9 environment that is not connected to an existing EC2 instance?","An Amazon EC2 instance","An Amazon S3 bucket","An Amazon RDS database","An AWS Lambda function","When you create a new Cloud9 environment, it usually launches an EC2 instance behind the scenes if you aren't connecting to an existing one."
"Which of the following is NOT a component of the AWS Cloud9 IDE?","A built-in code editor","A terminal","A debugger","A resource monitor for CPU usage on the local machine","Cloud9 offers a code editor, terminal, and debugger, but no specific resource monitor for CPU usage on the local machine (it monitors the remote EC2 instance's resources)."
"What is a common use case for using AWS Cloud9 for teaching programming?","Simplified environment setup for students","Cost savings on development tools","Integration with machine learning services","Compliance with industry regulations","Cloud9 simplifies environment setup for students, eliminating the need to install and configure software locally."
"How do you update the software packages within an AWS Cloud9 environment?","By using the package manager in the terminal (e.g., `apt`, `yum`, `npm`)","By using the AWS Management Console","By using the Cloud9 IDE's settings menu","By restarting the environment","You can update software packages by using the package manager in the terminal (e.g., `apt update` for Ubuntu)."
"Can you use AWS Cloud9 to develop applications that interact with other AWS services?","Yes, with proper IAM permissions","No, Cloud9 is only for local development","Only for specific AWS services","Only for web applications","Yes, you can develop applications that interact with other AWS services, as long as the Cloud9 environment has the correct IAM permissions."
"What type of operating system does the underlying Amazon EC2 instance running the AWS Cloud9 environment typically use?","Amazon Linux or Ubuntu","Windows Server","macOS","Red Hat Enterprise Linux","The EC2 instances behind Cloud9 environments typically run Amazon Linux or Ubuntu."
"What is the purpose of the 'AWS Cloud9' directory created in the environment?","It stores environment-specific configuration files","It stores application code","It stores AWS credentials","It stores temporary files","The 'AWS Cloud9' directory stores environment-specific configuration files."
"Which of these is NOT a benefit of using AWS Cloud9?","Automatic code completion","Real-time collaborative editing","Integrated debugging","Offline development","Cloud9 is a cloud-based IDE and requires an internet connection, so offline development is not possible."
"How does AWS Cloud9 help to manage credentials when accessing AWS services?","It uses IAM roles associated with the EC2 instance","It stores credentials in a configuration file","It requires manual entry of AWS credentials each time","It uses the AWS CLI's default profile","Cloud9 leverages IAM roles attached to the EC2 instance, simplifying credential management."
"Which of the following actions can you perform directly within the AWS Cloud9 IDE?","Deploy code to AWS Lambda","Create an S3 bucket","Launch an EC2 instance","Configure a VPC","You can deploy code to AWS Lambda directly from the Cloud9 IDE."
"What does the term 'Cloud9 runner' generally refer to?","A configuration to execute the code","A tool for testing code","A debugging tool","A deployment script","A Cloud9 runner is a configuration that tells Cloud9 how to execute your code (e.g., `node server.js`)."
"How can you view the logs generated by your application running in an AWS Cloud9 environment?","In the Cloud9 terminal or through CloudWatch logs","By downloading log files from the EC2 instance","By using the Cloud9 IDE's built-in log viewer","By querying the AWS CloudTrail service","You can view application logs in the Cloud9 terminal or, if properly configured, through CloudWatch Logs."
"How can you quickly create a new file in AWS Cloud9?","Right-click in the file explorer and select 'New File'","Use the AWS CLI","Drag and drop a file from your local machine","Use a command only in the terminal","You can quickly create a new file by right-clicking in the file explorer and selecting 'New File'."
"When using Cloud9 for serverless development, which file type is typically used to define the infrastructure?","YAML or JSON (CloudFormation or SAM)","Python","HTML","CSS","YAML or JSON are commonly used with CloudFormation or SAM to define the infrastructure for serverless applications."
"What is the main advantage of using Cloud9 over other cloud based IDEs when working with AWS services?","Seamless integration with AWS services","Lower cost than other IDEs","More available storage space","Faster processing speeds","Cloud9 offers seamless integration with AWS services, simplifying development and deployment."
"You are developing a Python application in AWS Cloud9. Which tool can you use to manage the application's dependencies?","pip","apt","npm","yum","`pip` is the package installer for Python and is used to manage dependencies."
"How do you connect your AWS Cloud9 environment to a GitHub repository?","By cloning the repository using Git in the terminal","By importing the repository through the AWS Management Console","By using a built-in GitHub integration in the Cloud9 IDE","By creating a personal access token for your account","You clone the repository using Git commands in the Cloud9 terminal."
"What is the purpose of the 'AWS Cloud9 instances' section in the AWS Management Console?","To manage existing Cloud9 environments","To create new Cloud9 environments","To monitor CloudWatch metrics","To configure IAM roles","The 'AWS Cloud9 instances' section in the AWS Management Console allows you to manage your existing Cloud9 environments."
"If your AWS Cloud9 environment is running slow, what is the first thing you should check?","The EC2 instance type and its utilisation","The network connection speed","The IDE's configuration settings","The number of files in the project","Check the EC2 instance type and its utilisation (CPU, memory, disk I/O) as this often impacts performance."
"What is the default timeout for an AWS Cloud9 environment to automatically stop when idle?","30 minutes","1 hour","15 minutes","2 hours","The default timeout for an AWS Cloud9 environment is 30 minutes."
"What is the maximum number of AWS Cloud9 environments that can be created in an AWS account?","The limit depends on the region and account type","Unlimited","5","10","The limit depends on the region and account type and are soft limits that can be raised."
"What type of database can be integrated with AWS Cloud9 for local development and testing?","SQLite","MySQL","PostgreSQL","MongoDB","SQLite is a lightweight, file-based database that is often used for local development and testing in Cloud9."
"What is the default user account on a Cloud9 EC2 instance?","ubuntu or ec2-user","root","admin","cloud9-user","The default user account on a Cloud9 EC2 instance is typically either `ubuntu` (for Ubuntu-based environments) or `ec2-user` (for Amazon Linux)."
"How can you persist data stored within an AWS Cloud9 environment even after the environment is stopped?","By storing the data in a persistent storage volume (e.g., EBS)","By storing the data in an S3 bucket","By storing the data in a DynamoDB table","By storing the data in a CloudWatch log","Data should be stored on a persistent EBS volume, which persists even when the environment (and EC2 instance) is stopped."
"What is the purpose of the 'share' button on the AWS Cloud9 IDE?","To share the environment with other users","To share a link to a specific file in the environment","To share the environment's terminal output","To share the environment's AWS credentials","The 'share' button is used to invite other users to collaborate in the Cloud9 environment."
"Which tool would you use to quickly provision multiple identical AWS Cloud9 environments for a class of students?","CloudFormation or Terraform","The AWS Management Console","The AWS CLI","AWS CodeDeploy","Infrastructure as code tools like CloudFormation or Terraform allow you to define and provision infrastructure, including Cloud9 environments, in a repeatable manner."
"You need to install a specific version of Node.js in your AWS Cloud9 environment. How would you do this?","Using nvm (Node Version Manager) in the terminal","Through the Cloud9 IDE's settings menu","By using the AWS CLI","By manually downloading and installing the Node.js binaries","`nvm` (Node Version Manager) is the standard tool for managing multiple Node.js versions."
"What is the purpose of the 'go to anything' feature in the AWS Cloud9 IDE?","To quickly navigate to files, symbols, and lines of code","To search for AWS resources","To view the AWS service health dashboard","To access documentation for AWS services","'Go to Anything' allows you to quickly navigate to files, symbols, and specific lines of code within your project."
"How can you protect your AWS Cloud9 environment from unauthorized access?","By configuring strong IAM policies and limiting access to the EC2 instance","By enabling multi-factor authentication","By storing credentials in environment variables","By using a VPN","Strong IAM policies are essential to restrict access to the Cloud9 environment and the underlying resources. Access to the EC2 instance running your environment should also be limited."
"Which of the following AWS services can be easily configured directly within the AWS Cloud9 environment using the AWS Explorer pane?","AWS Lambda, EC2, S3","AWS IAM, CloudWatch, CloudTrail","AWS KMS, VPC, Route53","AWS Glue, Athena, EMR","The AWS Explorer pane offers direct integration and configuration options for services like AWS Lambda, EC2 and S3."
"You want to prevent the accidental deletion of your AWS Cloud9 environment. What AWS service can you use?","IAM policies with resource-level permissions","CloudTrail","CloudWatch Events","AWS Config","IAM policies can be configured to restrict deletion actions on specific Cloud9 environments, preventing accidental deletion."
"When developing an application in AWS Cloud9, what is the easiest way to access the AWS credentials?","Using the IAM role attached to the Cloud9 EC2 instance","By storing the credentials in environment variables","By configuring the AWS CLI with your credentials","By using a shared credentials file","Using the IAM role attached to the EC2 instance is the recommended and easiest way to securely access AWS credentials."
"Which action is required to enable HTTPS for your application running inside your Cloud9 environment?","Configuring a web server (e.g., Apache or Nginx) and obtaining an SSL/TLS certificate","Enabling HTTPS in the Cloud9 IDE settings","Using the AWS Certificate Manager","Associating an Elastic IP address","HTTPS requires configuring a web server like Apache or Nginx and obtaining an SSL/TLS certificate to secure the communication."
"How can you customize the keyboard shortcuts in AWS Cloud9?","Through the IDE's settings menu","By editing a configuration file","Using the AWS CLI","Through the AWS Management Console","You can customize keyboard shortcuts through the Cloud9 IDE's settings menu."
"In AWS Cloud9, what is the significance of the region where the environment is created?","It affects the proximity to AWS resources and latency","It determines the programming languages available","It defines the cost of the environment","It determines the security policies applied","The region where the environment is created affects the proximity to other AWS resources and network latency."
