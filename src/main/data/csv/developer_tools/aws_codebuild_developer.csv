"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS CodeBuild, what does the 'buildspec.yml' file define?","The build commands and settings for a project","The AWS account credentials to use","The source code repository URL","The IAM role for CodeBuild","The 'buildspec.yml' file is a YAML file that specifies the commands that CodeBuild uses to build your project.  It defines the build environment, build commands, and artifacts to be produced."
"Which AWS service does CodeBuild integrate with to store build artifacts?","Amazon S3","Amazon EBS","Amazon EFS","Amazon Glacier","CodeBuild can store build artifacts, such as compiled code or deployment packages, in an Amazon S3 bucket."
"What is the purpose of 'phases' in the CodeBuild 'buildspec.yml' file?","To define the stages of the build process","To specify environment variables","To set up notifications","To manage IAM permissions","The 'phases' section in 'buildspec.yml' allows you to define the different stages of the build process, such as install, pre_build, build, and post_build."
"Which of the following is a valid source type for AWS CodeBuild?","GitHub","Bitbucket","Amazon S3","All of the above","CodeBuild supports GitHub, Bitbucket, and Amazon S3 as source repositories."
"What type of compute environment does AWS CodeBuild provide?","Managed compute","EC2 instance","Lambda function","Container service","CodeBuild provides a managed compute environment, meaning AWS handles the underlying infrastructure and scaling."
"How can you trigger a CodeBuild build?","Through the AWS Management Console","Using the AWS CLI or SDK","By configuring webhooks","All of the above","CodeBuild builds can be triggered through the console, CLI/SDK, or by configuring webhooks to respond to source code changes."
"Which IAM permission is required for CodeBuild to access source code in a private GitHub repository?","`codebuild:BatchGetBuilds`","`codebuild:CreateProject`","`codebuild:StartBuild`","`secretsmanager:GetSecretValue`","The IAM role used by CodeBuild needs permission to access the GitHub repository, typically requiring the use of a secret to access the repository."
"What is the purpose of environment variables in AWS CodeBuild?","To pass configuration information to the build process","To store sensitive information, such as passwords","To define the build environment type","To specify the build timeout duration","Environment variables can be used to pass configuration information, such as API keys or database connection strings, to the build process."
"How can you access the logs generated by AWS CodeBuild?","Amazon CloudWatch Logs","Amazon S3","Amazon EBS","AWS Config","CodeBuild integrates with Amazon CloudWatch Logs, allowing you to access and analyse build logs."
"What type of artifact can CodeBuild produce?","Compiled code","Deployment packages","Docker images","All of the above","CodeBuild can produce various types of artifacts, including compiled code, deployment packages, and Docker images, depending on the build configuration."
"In CodeBuild, what is the purpose of the 'cache' configuration?","To speed up subsequent builds by reusing build outputs","To store build logs","To encrypt build artifacts","To manage environment variables","The 'cache' configuration in CodeBuild can be used to cache build outputs and dependencies, which can significantly speed up subsequent builds."
"Which build environment image types does AWS CodeBuild offer?","Amazon Linux 2","Ubuntu","Windows Server","All of the above","CodeBuild provides a selection of build environment images, including Amazon Linux 2, Ubuntu, and Windows Server, allowing you to choose the appropriate environment for your project."
"How does AWS CodeBuild integrate with AWS CloudFormation?","CodeBuild can deploy CloudFormation stacks as part of the build process","CodeBuild can generate CloudFormation templates","CloudFormation can trigger CodeBuild builds","All of the above","CodeBuild can be integrated with CloudFormation in several ways, including deploying stacks, generating templates, and being triggered by CloudFormation events."
"What is the purpose of the 'report-group' in AWS CodeBuild?","To collect and display test results","To manage environment variables","To define build phases","To configure caching","The 'report-group' feature in CodeBuild allows you to collect and display test results, such as unit test results or code coverage reports."
"How can you secure sensitive information, such as API keys, in AWS CodeBuild?","Using AWS Secrets Manager","Using environment variables directly in buildspec.yml","Storing them in the source code repository","Using IAM roles","AWS Secrets Manager allows you to securely store and manage sensitive information, which can then be accessed by CodeBuild during the build process."
"What is the maximum build timeout duration for AWS CodeBuild?","8 hours","1 hour","24 hours","12 hours","The maximum build timeout duration for AWS CodeBuild is 8 hours."
"How can you specify a specific Docker image to use for your CodeBuild environment?","By specifying the image in the 'environment' section of the buildspec.yml file","By creating a custom AMI","By using an AWS Lambda function","By selecting a pre-defined environment image","You can specify a specific Docker image to use for your build environment in the 'environment' section of the 'buildspec.yml' file."
"What is the purpose of the 'artifacts' section in the CodeBuild 'buildspec.yml' file?","To specify the files or directories to be packaged as build outputs","To define the build environment","To configure caching","To manage environment variables","The 'artifacts' section in the 'buildspec.yml' file allows you to specify the files or directories to be packaged as build outputs, such as compiled code or deployment packages."
"Which service can be used to receive notifications about CodeBuild build status changes?","AWS CloudWatch Events (EventBridge)","Amazon SNS","Amazon SQS","AWS Config","AWS CloudWatch Events (now known as EventBridge) can be used to receive notifications about CodeBuild build status changes, allowing you to automate actions based on build events."
"What does AWS CodeBuild use to control access to the resources it needs to perform a build?","IAM role","Security group","Network ACL","AWS KMS","CodeBuild uses an IAM role to grant the necessary permissions to access resources like S3 buckets, source code repositories, and other AWS services."
"Which of the following is NOT a valid phase in the AWS CodeBuild buildspec?","deploy","install","build","post_build","The 'deploy' phase is not a standard phase in the CodeBuild buildspec. Common phases are 'install', 'pre_build', 'build', and 'post_build'."
"What is the purpose of the 'pre_build' phase in AWS CodeBuild?","To run commands before the main build commands","To run commands after the main build commands","To install dependencies","To define environment variables","The 'pre_build' phase allows you to run commands before the main build commands, such as setting up the environment or fetching dependencies."
"How can you configure AWS CodeBuild to use a private VPC?","By specifying the VPC ID and subnet IDs in the CodeBuild project settings","By using a public subnet","By configuring a VPN connection","By creating a NAT gateway","To configure CodeBuild to use a private VPC, you need to specify the VPC ID and subnet IDs in the CodeBuild project settings, ensuring that CodeBuild can access resources within your private network."
"What is the main benefit of using CodeBuild's managed compute environment compared to self-managed build servers?","Reduced operational overhead","Greater control over the build environment","Lower cost","Faster build times","CodeBuild's managed compute environment reduces operational overhead by eliminating the need to manage build servers, allowing you to focus on your build process."
"Which of the following can be specified as the compute platform in a CodeBuild project?","Linux","Windows","Arm","All of the above","CodeBuild supports Linux, Windows, and Arm compute platforms, allowing you to choose the platform that best suits your project's requirements."
"What is the purpose of the 'privileged mode' setting in AWS CodeBuild?","To allow Docker commands within the build environment","To grant administrator access to the build container","To enable caching","To disable security features","The 'privileged mode' setting in CodeBuild allows Docker commands to be executed within the build environment, which is necessary for building Docker images."
"How can you trigger a CodeBuild build when changes are pushed to a CodeCommit repository?","By configuring a CloudWatch Events (EventBridge) rule","By using an SNS topic","By setting up a Lambda function","By manually starting the build","You can trigger a CodeBuild build automatically when changes are pushed to a CodeCommit repository by configuring a CloudWatch Events (EventBridge) rule that monitors the CodeCommit repository for changes."
"Which AWS service can you use to automatically provision and manage the infrastructure required for CodeBuild?","AWS CloudFormation","AWS Config","AWS Systems Manager","AWS OpsWorks","AWS CloudFormation can be used to automatically provision and manage the infrastructure required for CodeBuild, such as IAM roles, VPC configurations, and S3 buckets."
"What is the purpose of CodeBuild's 'build badge'?","To display the build status on a website or in a repository","To provide a link to the build logs","To grant access to the build project","To trigger a new build","CodeBuild's 'build badge' can be embedded on a website or in a repository to display the current build status, providing a visual indicator of build health."
"How can you improve the security of your AWS CodeBuild builds?","By using IAM roles with least privilege","By encrypting build artifacts","By using private VPCs","All of the above","You can improve the security of your CodeBuild builds by using IAM roles with least privilege, encrypting build artifacts, and using private VPCs to isolate your build environment."
"Which CodeBuild feature allows you to run multiple builds concurrently within a single project?","Concurrency limit","Build batch","Build queue","Parallel builds","The concurrency limit feature in CodeBuild allows you to control the number of builds that can run concurrently within a single project, preventing resource exhaustion."
"Which of the following is a valid artifact type that CodeBuild can create?","ZIP","WAR","JAR","All of the above","CodeBuild can create ZIP, WAR, and JAR files as artifacts, depending on your project's requirements."
"How can you integrate CodeBuild with a CI/CD pipeline?","By using AWS CodePipeline","By using Jenkins","By using GitLab CI","All of the above","CodeBuild can be integrated with various CI/CD pipeline tools, including AWS CodePipeline, Jenkins, and GitLab CI."
"What is the purpose of the `PROJECT_NAME` environment variable in AWS CodeBuild?","It automatically provides the name of the CodeBuild project.","It specifies the region in which the project runs.","It defines the build environment type.","It sets the build timeout duration.","The `PROJECT_NAME` environment variable automatically provides the name of the CodeBuild project, which can be useful for logging or other build-related tasks."
"In CodeBuild, which action can you take if a build fails consistently due to a dependency issue?","Modify the 'install' phase in the buildspec to correctly install the dependency.","Increase the build timeout.","Change the IAM role.","Switch to a different compute environment.","If a build fails consistently due to a dependency issue, you should modify the 'install' phase in the buildspec to ensure that the dependency is correctly installed."
"You need to share build artifacts produced by CodeBuild with external stakeholders securely. What is the recommended approach?","Store the artifacts in an S3 bucket with pre-signed URLs.","Email the artifacts directly.","Store the artifacts in a public S3 bucket.","Use an unencrypted zip file.","Storing the artifacts in an S3 bucket and generating pre-signed URLs provides a secure way to share the artifacts with external stakeholders, as it allows you to control access and expiration."
"What is the purpose of the AWS CodeBuild local cache feature?","Speeding up builds by reusing previously downloaded dependencies","To avoid having to push artefacts to a cloud based storage solution","Store credentials for accessing source code repositories","Provide a secure location to store environment variables","The AWS CodeBuild local cache feature speeds up builds by reusing previously downloaded dependencies, reducing the time it takes to fetch dependencies from external sources."
"When using CodeBuild with AWS CodePipeline, what is the recommended way to pass parameters between build stages?","Using environment variables defined in CodePipeline.","Storing parameters in an S3 bucket.","Embedding parameters in the source code.","Manually configuring each CodeBuild project.","Using environment variables defined in CodePipeline allows you to pass parameters between build stages, providing a flexible and automated way to configure your builds."
"What is the key benefit of using CodeBuild with a Docker image stored in Amazon ECR?","Consistent and reproducible build environments.","Simplified buildspec configuration.","Automated scaling of build resources.","Reduced network latency.","Using CodeBuild with a Docker image stored in Amazon ECR ensures consistent and reproducible build environments, as the build environment is defined by the Docker image."
"You have multiple CodeBuild projects that use similar build commands. How can you avoid duplicating the build logic in each project?","Create a custom Docker image with the common commands.","Use AWS Lambda functions for the build process.","Store the commands in a shared text file.","Configure each project independently.","Creating a custom Docker image with the common commands allows you to reuse the build logic across multiple CodeBuild projects, reducing duplication and improving maintainability."
"What is the significance of the `BUILD_SUCCEEDED` environment variable in AWS CodeBuild?","Indicates whether the previous build stage was successful","Specifies the number of successful builds","Defines the actions to take after a successful build","Stores the artifacts from the last successful build","The `BUILD_SUCCEEDED` environment variable indicates whether the previous build stage was successful, allowing you to conditionally execute commands based on the build status."
"Your CodeBuild project requires access to resources in a different AWS account. What is the recommended approach?","Configure cross-account IAM roles","Grant the CodeBuild IAM role full access to all AWS services","Create a new CodeBuild project in the other account","Share the AWS credentials directly","Configuring cross-account IAM roles allows you to grant CodeBuild access to resources in a different AWS account securely, following the principle of least privilege."
"You want to run static code analysis as part of your CodeBuild process. Which phase of the buildspec is most suitable for this?","post_build","install","pre_build","build","The 'post_build' phase is most suitable for running static code analysis, as it allows you to analyse the code after the build process is complete and before the artifacts are packaged."
"What is the primary difference between using a buildspec file in the source code repository versus specifying the buildspec directly in the CodeBuild console?","The buildspec in the source code repository promotes version control.","Specifying the buildspec in the console is more secure.","The buildspec in the console allows access to more AWS resources.","There is no functional difference.","Using a buildspec file in the source code repository promotes version control, allowing you to track changes to the build process along with the source code."
"What is the purpose of enabling 'CloudWatch connection' when setting up CodeBuild?","To access CodeBuild logs in CloudWatch.","To enable caching.","To allow CodeBuild to access to other resources.","To enable integration with other AWS services.","Enabling 'CloudWatch connection' when setting up CodeBuild allows you to access CodeBuild logs in CloudWatch, providing a centralised location for monitoring and troubleshooting your builds."
"How can you enforce that all code pushed to a CodeCommit repository must pass CodeBuild tests before being merged?","By configuring branch protections in CodeCommit.","By using IAM policies.","By manually reviewing each commit.","By disabling direct commits to the main branch.","Configuring branch protections in CodeCommit ensures that all code pushed to a repository must pass CodeBuild tests before being merged, helping to maintain code quality and stability."
"You need to ensure that your CodeBuild project always uses the latest version of a dependency from a private repository. How can you achieve this?","Use dynamic versioning in your buildspec and configure authentication for the private repository.","Hardcode the version in the buildspec.","Store the dependency directly in the CodeBuild project.","Manually update the version each time a new release is available.","Using dynamic versioning in your buildspec and configuring authentication for the private repository ensures that your CodeBuild project always uses the latest version of the dependency."
"What is the purpose of the AWS CodeBuild badge?","To embed build status on external sites.","To track the history of failed builds.","To manage environment variables.","To trigger builds.","The purpose of the AWS CodeBuild badge is to embed build status on external sites, providing a quick visual indicator of the health of your builds."
"In AWS CodeBuild, what is the primary purpose of a 'buildspec.yml' file?","To define the build commands and environment settings","To store the source code of the application","To manage IAM roles for CodeBuild","To configure network settings for the build environment","The 'buildspec.yml' file is a YAML file that defines the set of commands and related settings that CodeBuild uses to perform the build."
"Which of the following AWS services can be used as a source code repository for AWS CodeBuild?","AWS CodeCommit","Amazon SQS","Amazon SNS","AWS CloudTrail","AWS CodeCommit is a fully managed source control service that hosts secure Git-based repositories, making it suitable as a source for CodeBuild."
"What type of environment variables can be defined within an AWS CodeBuild project?","Plaintext, Parameter Store, Secrets Manager","Only Plaintext","Only Parameter Store","Only Secrets Manager","CodeBuild allows you to define environment variables in plaintext, retrieve them from AWS Systems Manager Parameter Store, or AWS Secrets Manager for sensitive data."
"What is the purpose of 'phases' in the buildspec.yml file within AWS CodeBuild?","To define the different stages of the build process","To specify the programming language used in the project","To manage dependencies","To define the AWS region for the build","Phases in the buildspec.yml file represent distinct stages of the build process, such as install, pre_build, build, and post_build."
"Which of the following build environments does AWS CodeBuild support?","Linux, Windows, and macOS","Only Linux","Only Windows","Only macOS","CodeBuild supports Linux, Windows, and macOS build environments, offering flexibility for various project requirements."
"What is the function of the 'artifacts' section in the buildspec.yml file for AWS CodeBuild?","To specify the files or directories to be archived after the build","To define the build commands","To manage environment variables","To specify the source code location","The 'artifacts' section is used to define the files or directories that should be archived as build outputs after the build process is complete."
"What is the use of AWS CodeBuild's cache feature?","To store frequently accessed dependencies to speed up builds","To store the build logs","To store the source code","To store the buildspec.yml file","The cache feature in CodeBuild stores frequently accessed dependencies (like packages) to reduce download times and accelerate subsequent builds."
"How can you trigger an AWS CodeBuild project automatically upon a code commit to a repository?","Using AWS CodePipeline or CloudWatch Events (EventBridge)","Manually through the AWS console","Using AWS Lambda alone","Using AWS Config","AWS CodePipeline can be configured to automatically trigger a CodeBuild project when changes are committed to a source code repository. CloudWatch Events (EventBridge) is an alternative."
"Which AWS service can be integrated with AWS CodeBuild to create a continuous integration and continuous delivery (CI/CD) pipeline?","AWS CodePipeline","AWS CloudFormation","AWS CloudWatch","AWS IAM","AWS CodePipeline is designed to orchestrate CI/CD workflows, allowing seamless integration with CodeBuild for automated builds and deployments."
"What is the purpose of the 'privileged mode' setting in AWS CodeBuild?","To allow Docker commands to be executed during the build process","To grant access to all AWS resources","To disable logging","To skip the test phase","Privileged mode is required when your build process needs to execute Docker commands or access Docker daemon functionalities."
"In AWS CodeBuild, what does the 'pre_build' phase in the buildspec.yml typically involve?","Installing dependencies and setting up the build environment","Running tests","Compiling source code","Deploying the application","The 'pre_build' phase usually involves tasks like installing dependencies, setting environment variables, and preparing the build environment."
"How can you secure sensitive information, such as API keys, within an AWS CodeBuild project?","By storing them in AWS Systems Manager Parameter Store or AWS Secrets Manager and referencing them as environment variables","By embedding them directly in the buildspec.yml file","By hardcoding them in the source code","By storing them in Amazon S3 with public access","Storing sensitive information in AWS Systems Manager Parameter Store or AWS Secrets Manager is the recommended approach for securing credentials in CodeBuild."
"What is the maximum build timeout duration that can be configured for an AWS CodeBuild project?","8 hours","1 hour","12 hours","24 hours","The maximum build timeout duration for an AWS CodeBuild project is 8 hours (480 minutes)."
"Which of the following is an advantage of using AWS CodeBuild over managing your own build servers?","No infrastructure to manage","Greater control over the build environment","Lower cost for short builds","Faster build times","AWS CodeBuild eliminates the need to provision, manage, and scale build servers, reducing operational overhead."
"Which of the following is NOT a valid build status reported by AWS CodeBuild?","Pending","In progress","Succeeded","Deleted","'Deleted' is not a valid build status reported by CodeBuild. The valid states include: SUCCEEDED, FAILED, FAULT, TIMED_OUT, IN_PROGRESS, STOPPED."
"What is the purpose of the 'source' section in the buildspec.yml file?","To define the location of the source code repository","To specify the build environment","To define the build commands","To specify the artifact location","The 'source' section defines the source code repository and how CodeBuild should retrieve the source code."
"How can you monitor the progress and results of AWS CodeBuild projects?","Using the AWS CodeBuild console, CloudWatch, and CloudTrail","Only through the AWS CodeBuild console","Only through CloudWatch","Only through CloudTrail","The AWS CodeBuild console provides a user interface to monitor build progress. CloudWatch offers metrics and logs, and CloudTrail tracks API calls."
"What is the purpose of setting the 'compute type' in an AWS CodeBuild project?","To specify the amount of compute resources allocated to the build","To specify the operating system","To specify the programming language","To specify the source code repository","The compute type determines the compute resources (CPU and memory) that are allocated to the build environment."
"What type of builds does AWS CodeBuild perform by default?","Clean builds","Incremental builds","Delta builds","Mirror builds","By default, AWS CodeBuild performs clean builds, starting with a fresh environment for each build."
"Which AWS service can be used to store build artifacts produced by AWS CodeBuild?","Amazon S3","Amazon EBS","Amazon EC2","Amazon RDS","Amazon S3 is commonly used to store build artifacts produced by CodeBuild, such as compiled binaries, deployment packages, and test reports."
"What type of authentication is required for AWS CodeBuild to access a private Git repository?","IAM role with permissions to access the repository","Username and password","SSH key pair","API key","An IAM role with appropriate permissions is required for CodeBuild to securely access a private Git repository."
"What is the purpose of 'reports' section in the buildspec.yml file within AWS CodeBuild?","To define how the test results are collected and stored","To specify the build commands","To define environment variables","To specify the source code location","The 'reports' section is used to define how the test results are collected and stored, allowing you to view detailed test reports in the CodeBuild console."
"How can you run unit tests as part of your AWS CodeBuild process?","By including the test commands in the 'build' phase of the buildspec.yml file","By using a separate AWS service","By configuring CodeBuild to automatically detect unit tests","By using a dedicated testing framework","You can run unit tests by adding the necessary commands to execute the tests in the 'build' phase of the buildspec.yml file."
"Which of the following actions CANNOT be performed in the 'post_build' phase of an AWS CodeBuild project?","Deployment to an environment","Compilation of code","Pushing Docker images to a repository","Sending notifications","Code compilation happens in the build phase, not the post_build phase."
"Which of the following is NOT a supported source code provider for AWS CodeBuild?","GitHub Enterprise","Bitbucket","AWS CodeDeploy","AWS CodeCommit","AWS CodeDeploy is a deployment service, not a source code provider."
"What is the purpose of the 'phases' section in the buildspec.yml file within AWS CodeBuild?","To define the different stages of the build process","To specify the programming language used in the project","To manage dependencies","To define the AWS region for the build","Phases in the buildspec.yml file represent distinct stages of the build process, such as install, pre_build, build, and post_build."
"What is the purpose of the 'environment' section in the buildspec.yml file within AWS CodeBuild?","To define environment variables for the build process","To specify the programming language used in the project","To manage dependencies","To define the AWS region for the build","The 'environment' section allows you to define environment variables that will be available during the build process."
"What happens if an AWS CodeBuild build fails?","CodeBuild stops the build process and reports the error","CodeBuild continues with the next phase","CodeBuild retries the failed phase automatically","CodeBuild ignores the error and completes the build","If a build fails, CodeBuild stops the build process and reports the error in the console and logs."
"Which statement is true regarding AWS CodeBuild projects?","Each project must have a unique name within an AWS account and region","Project names can be duplicated across regions","Project names are globally unique across all AWS accounts","Project names only need to be unique within a specific build","CodeBuild project names must be unique within an AWS account and region."
"Which of the following is an advantage of using AWS CodeBuild with AWS CodePipeline?","Automated build and deployment workflow","Simplified debugging","Reduced infrastructure costs","Faster build times","Integrating CodeBuild with CodePipeline enables a fully automated CI/CD workflow, from code commit to deployment."
"How can you configure AWS CodeBuild to send notifications about build status changes?","By integrating with Amazon SNS","By integrating with Amazon SQS","By integrating with Amazon S3","By integrating with Amazon CloudFront","CodeBuild can be integrated with Amazon SNS to send notifications about build status changes, such as build start, success, or failure."
"Which of the following features helps optimise AWS CodeBuild build times?","Using caching to store frequently used dependencies","Increasing the build timeout","Using a larger compute type","Enabling privileged mode","Caching stores frequently used dependencies, which reduces the time it takes to download and install them during subsequent builds."
"What is the purpose of the 'artifacts' section in the buildspec.yml file for AWS CodeBuild?","To specify the files or directories to be archived after the build","To define the build commands","To manage environment variables","To specify the source code location","The 'artifacts' section is used to define the files or directories that should be archived as build outputs after the build process is complete."
"What is the primary purpose of AWS CodeBuild?","To build and test code","To deploy applications","To manage infrastructure","To monitor application performance","AWS CodeBuild is a fully managed build service that compiles source code, runs tests, and produces software packages that are ready to deploy."
"Which of the following is a valid build environment image provided by AWS CodeBuild?","aws/codebuild/standard:6.0","aws/codebuild/standard:nodejs-16","aws/codebuild/standard:docker-experimental","aws/codebuild/standard:ubuntu-base","`aws/codebuild/standard:6.0` is a valid base image provided by AWS CodeBuild with a standard set of tools and libraries."
"What security best practice should you implement when using AWS CodeBuild?","Grant CodeBuild the least privilege necessary to access AWS resources","Grant CodeBuild full administrative access","Store sensitive data in plaintext in the buildspec.yml file","Disable CloudTrail logging for CodeBuild projects","Following the principle of least privilege, grant CodeBuild only the permissions it needs to access specific AWS resources."
"Which AWS service can be used to store Docker images built by AWS CodeBuild?","Amazon Elastic Container Registry (ECR)","Amazon S3","Amazon EBS","Amazon RDS","Amazon ECR is a fully managed Docker container registry that allows you to store, manage, and deploy Docker container images."
"Which file is used to specify the build commands and settings for an AWS CodeBuild project?","buildspec.yml","Dockerfile","package.json","Jenkinsfile","The `buildspec.yml` file defines the build environment and build steps for an AWS CodeBuild project."
"Which of the following actions is performed in the 'install' phase of an AWS CodeBuild project?","Installing dependencies and tools required for the build","Compiling the source code","Running unit tests","Deploying the application","The 'install' phase is used to install dependencies and tools required for the build, such as package managers or build tools."
"Which of the following is NOT a valid trigger event for an AWS CodeBuild project?","Code commit to an AWS CodeCommit repository","Push to a GitHub repository","Scheduled event via CloudWatch Events (EventBridge)","Manual approval","'Manual approval' is not a valid trigger event for CodeBuild. CodeBuild is automatically triggered by a source code change."
"You want to create a buildspec file in AWS CodeBuild that allows it to upload its logs to S3. Which phase is most appropriate for this task?","post_build","install","pre_build","build","The post_build phase is where you can upload your logs after the entire build is over."
"You are experiencing slow build times in your AWS CodeBuild project. What should you consider doing to potentially improve build times?","Enable caching of dependencies","Disable CloudWatch Logs","Reduce the compute type","Increase the build timeout","Enabling caching of dependencies will reduce build times as it avoids re-downloading dependencies on each build."
"What should you do to ensure that your AWS CodeBuild project always uses the latest version of your dependencies?","Invalidate the cache","Increase the compute type","Disable logging","Remove environment variables","You must invalidate the cache to get the latest version of your dependencies."
"Which AWS service would you use to orchestrate multiple AWS CodeBuild projects into a continuous integration and continuous delivery pipeline?","AWS CodePipeline","AWS CodeCommit","AWS CodeDeploy","AWS CodeArtifact","AWS CodePipeline is specifically designed to orchestrate multiple steps in a CI/CD pipeline, including AWS CodeBuild projects."
"What type of resource is required for AWS CodeBuild to access other AWS services like S3 or CodeCommit?","IAM role","EC2 Instance","API Gateway","Lambda Function","An IAM role is required for CodeBuild to assume permissions to access other AWS services on your behalf."
"Which section of the buildspec.yml file can you use to define secrets that should be retrieved from AWS Secrets Manager?","environment","parameters","cache","artifacts","The environment section of the buildspec file can define secrets that should be retrieved from AWS Secrets Manager."
"You need to use a custom Docker image for your AWS CodeBuild project. Where should this image be stored?","Amazon Elastic Container Registry (ECR)","Amazon S3","Amazon EBS","AWS CodeCommit","Custom Docker images should be stored in ECR and referenced by CodeBuild."
"Which buildspec phase is executed before the 'build' phase?","pre_build","install","post_build","deploy","The pre_build phase is executed before the build phase."
"You want to run integration tests that require access to resources in a VPC. What configuration is required in AWS CodeBuild?","Configure VPC access in the CodeBuild project settings","Integration tests cannot be run in a VPC","Create a Lambda function to access the VPC","Use a NAT Gateway","VPC access needs to be configured in the CodeBuild project settings to allow the build environment to access resources within a VPC."
"In AWS CodeBuild, what does the term 'artifact' refer to?","The output of the build process","The source code repository","The build environment","The buildspec file","In CodeBuild, an artifact is the output of the build process."
