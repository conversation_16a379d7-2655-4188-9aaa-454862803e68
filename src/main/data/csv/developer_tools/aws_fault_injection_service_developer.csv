"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Fault Injection Service (FIS), what is an action?","A specific task to inject a fault","A metric used to monitor the experiment","A type of IAM role","A method for analyzing experiment results","An action defines the specific fault or disruption that you want to inject into your system during an FIS experiment.  It's the core of what FIS does."
"What is the purpose of a target in an AWS FIS experiment?","To define the resources affected by the experiment","To define the success criteria of the experiment","To set the duration of the experiment","To specify the IAM permissions for the experiment","The target specifies the AWS resources (e.g., EC2 instances, ECS tasks) that will be subjected to the fault injection actions during the FIS experiment.  It defines the scope of the experiment."
"Which of the following is a valid action that can be performed using AWS FIS?","Terminate EC2 instances","Create an S3 bucket","Launch a new VPC","Update a Route53 record","AWS FIS allows you to terminate EC2 instances, which is a common way to simulate failures and test the resilience of your applications."
"How does AWS FIS ensure safety during experiments?","By providing stop conditions","By automatically rolling back changes","By requiring manual approval for each action","By limiting the duration of experiments to 5 minutes","AWS FIS uses stop conditions to automatically halt an experiment if certain thresholds are breached, such as high CPU utilisation or increased error rates. This ensures that experiments don't cause catastrophic failures."
"What IAM permissions are required to run an AWS FIS experiment?","Permissions to access and modify the targeted resources and FIS itself","Permissions to access CloudWatch metrics only","Permissions to create IAM roles only","Permissions to access CloudTrail logs only","To run an FIS experiment, the IAM role used must have permissions to access and modify the resources targeted by the experiment, as well as permissions to manage FIS resources."
"When creating an AWS FIS experiment template, what does the 'target selection mode' determine?","How targets are selected for the experiment","The type of fault injected during the experiment","The duration of the experiment","The IAM role used to execute the experiment","The 'target selection mode' determines how FIS selects the resources that will be targeted by the experiment (e.g., all instances in an Auto Scaling group or a percentage of instances)."
"Which AWS service is commonly used to monitor the impact of AWS FIS experiments in real-time?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch is used for monitoring metrics, logs, and events, making it suitable to observe the impact of FIS experiments on your system's performance and health."
"What is the significance of 'stop conditions' in an AWS FIS experiment template?","They define the criteria to automatically stop the experiment if the system becomes unstable","They specify the resources targeted by the experiment","They define the actions to be performed during the experiment","They specify the start time of the experiment","Stop conditions are essential for safety. They define thresholds that, when breached, trigger the automatic termination of the experiment to prevent further damage."
"What is the relationship between an AWS FIS experiment template and an AWS FIS experiment?","The experiment template defines the configuration, and the experiment is the actual execution","The experiment is a read-only copy of the experiment template","The experiment template is automatically created after an experiment is run","The experiment template is a suggestion that is used by the experiment","The experiment template acts as a blueprint, defining the parameters of the fault injection. The experiment is the instantiation and execution of that template."
"Which of the following AWS services can be targeted by an AWS FIS experiment?","EC2","S3","Lambda","DynamoDB","FIS directly supports injecting faults into EC2 instances, allowing you to test the resilience of applications running on them."
"When designing an AWS FIS experiment, why is it important to start with small-scale experiments?","To minimise the potential impact on production systems","To reduce the cost of running the experiment","To simplify the analysis of the results","To speed up the experiment execution time","Starting with small-scale experiments reduces the blast radius, allowing you to learn and refine your approach before impacting a larger portion of your production environment."
"Which of the following is NOT a supported action type in AWS FIS?","Injecting latency into API calls","Terminating EC2 instances","Deleting S3 buckets","Creating CPU stress on EC2 instances","While FIS can inject latency and create CPU stress, it does not directly support deleting S3 buckets."
"In AWS FIS, what does the 'scope' of a target define?","The set of resources that are eligible to be targeted","The duration of the experiment","The actions to be performed during the experiment","The IAM role used to execute the experiment","The scope of a target defines the subset of resources (e.g., specific EC2 instances within an Auto Scaling group) that will be affected by the actions."
"What is the purpose of using tags when defining targets in an AWS FIS experiment?","To identify resources to be targeted","To track the cost of running the experiment","To define the stop conditions for the experiment","To specify the duration of the experiment","Tags can be used to select specific resources based on their attributes, making it easier to target a precise set of resources for your experiment."
"How can you use AWS FIS to test the resilience of a microservices architecture?","By injecting faults into individual microservices","By terminating entire EC2 instances","By blocking network traffic to specific IP addresses","By modifying the DNS records of the microservices","FIS allows you to isolate individual microservices and introduce faults to test how the system handles failures in one component without affecting others."
"What is the primary benefit of using AWS FIS over manually injecting faults?","FIS provides a controlled and automated environment for fault injection","FIS is cheaper than manually injecting faults","FIS is faster than manually injecting faults","FIS provides more detailed reporting than manually injecting faults","FIS provides a safe, controlled, and automated environment, ensuring that fault injection experiments are repeatable and manageable."
"Which AWS service can be integrated with AWS FIS to trigger experiments based on specific events or conditions?","Amazon CloudWatch Events (EventBridge)","AWS Lambda","AWS Step Functions","AWS CodePipeline","CloudWatch Events (EventBridge) can be used to trigger FIS experiments in response to specific events, such as a deployment or a change in resource utilisation."
"When using AWS FIS to test the resilience of an Auto Scaling group, what is a common action to perform?","Terminate instances within the Auto Scaling group","Increase the desired capacity of the Auto Scaling group","Update the launch configuration of the Auto Scaling group","Change the minimum size of the Auto Scaling group","Terminating instances in the Auto Scaling group is a common action to simulate instance failures and test the group's ability to scale and maintain availability."
"How does AWS FIS help improve the reliability of applications?","By proactively identifying weaknesses and vulnerabilities","By automatically fixing bugs in the code","By providing detailed performance reports","By optimising the infrastructure costs","FIS helps identify weaknesses in your application's architecture and deployment, allowing you to address them before they cause real-world problems."
"What is the role of the AWS FIS service-linked role?","To grant FIS permissions to manage AWS resources","To allow users to access FIS experiments","To define the stop conditions for FIS experiments","To encrypt the data generated by FIS experiments","The service-linked role provides FIS with the necessary permissions to access and modify AWS resources on your behalf during the experiment."
"Which of the following is a best practice when using AWS FIS in a production environment?","Schedule experiments during off-peak hours","Run experiments with a large scope","Disable stop conditions","Ignore monitoring alerts during the experiment","Scheduling experiments during off-peak hours reduces the potential impact on users and minimises disruptions to production workloads."
"In AWS FIS, what is the purpose of the 'selection mode' when defining a target?","To determine how the target resources are chosen","To specify the type of fault injected","To define the duration of the experiment","To set the IAM permissions for the experiment","The selection mode determines how FIS identifies the specific resources within the target that will be subjected to the fault injection actions."
"Which of the following is a potential benefit of using AWS FIS to test disaster recovery procedures?","Validating that the recovery procedures work as expected","Reducing the cost of running the disaster recovery site","Automating the creation of backups","Simplifying the process of patching servers","FIS can be used to simulate disaster scenarios and validate that your recovery procedures are effective in restoring your application to a functional state."
"How can you use AWS FIS to test the performance of your application under degraded network conditions?","By injecting latency into network traffic","By increasing the CPU utilisation on EC2 instances","By reducing the memory available to the application","By simulating disk I/O bottlenecks","FIS can inject latency into network traffic to simulate the effects of network congestion or failures, allowing you to assess your application's performance under these conditions."
"What is a key consideration when choosing the target resources for an AWS FIS experiment?","The criticality of the targeted resources","The cost of the targeted resources","The location of the targeted resources","The size of the targeted resources","You need to consider the criticality of the targeted resources. You want to avoid causing major disruptions to critical services. Start small and understand the impact."
"When using AWS FIS to test the resilience of a database, what is a common action?","Simulating a database failover","Increasing the size of the database instance","Adding more memory to the database instance","Changing the database password","Simulating a database failover is a common way to test the ability of your application to handle database outages and switch to a backup or replica."
"Which AWS service provides detailed audit logs of all actions performed by AWS FIS experiments?","AWS CloudTrail","Amazon CloudWatch Logs","AWS Config","AWS X-Ray","AWS CloudTrail tracks all API calls made to AWS services, including FIS, providing a comprehensive audit trail of experiment activities."
"How can you use AWS FIS to validate the effectiveness of your monitoring and alerting systems?","By injecting faults and verifying that alerts are triggered","By simulating network outages and verifying that users are notified","By increasing the CPU utilisation on EC2 instances and verifying that alarms are raised","By changing the database password and verifying that security alerts are generated","FIS can be used to intentionally introduce failures and verify that your monitoring and alerting systems detect and respond to those failures as expected."
"What is the purpose of the 'roleArn' parameter in an AWS FIS experiment template?","To specify the IAM role that FIS will use to execute the experiment","To define the stop conditions for the experiment","To specify the resources to be targeted","To set the duration of the experiment","The 'roleArn' parameter specifies the IAM role that FIS will assume to perform actions on your behalf during the experiment. This role needs the necessary permissions to access and modify the targeted resources."
"What is the maximum duration for AWS FIS experiments?","24 hours","1 hour","12 hours","30 minutes","The maximum duration for an AWS FIS experiment is 24 hours."
"What type of resources does AWS FIS directly support fault injection on?","EC2 instances, ECS tasks, EKS pods, RDS instances","S3 buckets, Lambda functions, DynamoDB tables, API Gateway APIs","CloudFront distributions, VPCs, Route 53 records, IAM roles","SQS queues, SNS topics, CloudWatch alarms, CloudTrail logs","AWS FIS directly supports injecting faults on EC2 instances, ECS tasks, EKS pods, and RDS instances."
"In AWS FIS, what does the term 'Experiment Template' refer to?","The configuration definition for an experiment","The service that runs experiments","The set of actions executed in an experiment","The historical record of past experiments","The Experiment Template is the configuration definition for an experiment. It contains information about the target resources, actions, and stop conditions."
"What is the first step you should perform before running an AWS FIS experiment in a production environment?","Perform the experiment in a non-production environment","Create a backup of your data","Disable monitoring systems","Increase the capacity of the target resources","You should always perform the experiment in a non-production environment first to understand the potential impact and refine the experiment configuration."
"What is a 'Stop Condition' in the context of AWS FIS experiments?","A condition that, when met, causes the experiment to automatically stop","A condition that defines the success criteria of the experiment","A condition that triggers the start of the experiment","A condition that logs the results of the experiment","A Stop Condition is a condition that, when met, causes the experiment to automatically stop to prevent further damage or disruption."
"Why should you monitor metrics, logs, and events during an AWS FIS experiment?","To understand the impact of the experiment on the system","To reduce the cost of the experiment","To automatically fix any issues that arise during the experiment","To generate reports for compliance purposes","Monitoring metrics, logs, and events allows you to observe the behaviour of the system in response to the injected faults and understand the impact of the experiment."
"What is the purpose of adding tags to AWS resources before using them as targets in FIS experiments?","To easily identify and select the resources for the experiment","To encrypt the data on the resources","To reduce the cost of the resources","To improve the performance of the resources","Adding tags to AWS resources allows you to easily identify and select specific resources as targets for your FIS experiments based on their tags."
"How can you use AWS FIS to test the failover mechanism of a multi-AZ RDS instance?","By rebooting the primary instance with failover","By increasing the storage capacity of the instance","By changing the instance type","By modifying the security group rules","Rebooting the primary instance with failover in AWS FIS can simulate a failure and test whether the failover mechanism works as expected."
"When using AWS FIS, what is an 'Action ID'?","A unique identifier for each action defined in an experiment template","The AWS account ID used to run the experiment","The ID of the IAM role used in the experiment","The ID of the CloudWatch alarm used as a stop condition","The Action ID is a unique identifier for each action defined in an experiment template, allowing you to reference specific actions in the experiment configuration."
"What is the recommended approach for managing the scope of an AWS FIS experiment in a production environment?","Start with a small scope and gradually increase it","Run experiments on all resources simultaneously","Disable stop conditions","Ignore monitoring alerts","Start with a small scope to minimise the potential impact and gradually increase it as you gain confidence and understanding of the system's behaviour."
"Which of the following is a good practice when defining 'stop conditions' in AWS FIS?","Use CloudWatch alarms that monitor critical system metrics","Disable CloudWatch alarms during the experiment","Set very high thresholds for CloudWatch alarms","Ignore CloudWatch alarm status during the experiment","It's a good practice to use CloudWatch alarms that monitor critical system metrics as stop conditions to automatically halt the experiment if the system becomes unstable."
"What does AWS FIS use to determine which AWS resources to target for fault injection?","Target filters","IAM roles","VPC settings","CloudTrail logs","Target filters are used by AWS FIS to select the specific AWS resources to be targeted for fault injection, allowing you to control the scope of the experiment."
"You want to use AWS FIS to simulate the loss of network connectivity to an EC2 instance. Which action should you use?","aws:network:disrupt-connectivity","aws:ec2:terminate-instances","aws:ecs:stop-task","aws:rds:reboot-db-instance","The `aws:network:disrupt-connectivity` action is used to simulate the loss of network connectivity to an EC2 instance."
"What AWS service should be used to analyze the long-term impact and trends observed during AWS FIS experiments?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides the necessary features for analysing long-term trends and impact observed during and after AWS FIS experiments."
"Which AWS FIS action targets containerized applications?","aws:ecs:stop-task","aws:ec2:terminate-instances","aws:rds:reboot-db-instance","aws:network:disrupt-connectivity","The `aws:ecs:stop-task` action targets containerized applications."
"What is the final step that should be performed once the FIS Experiment is completed?","Evaluate the results, refine the system, and repeat","Scale up the target resources","Delete the experiment template","Disable all monitoring","The final step is always to evaluate the results, refine the system, and repeat to validate the improvements. This iterative process is crucial for building resilient systems."
"What does FIS stand for in AWS FIS?","Fault Injection Simulator","Failure Isolation Service","Fast Instance Spawner","Federated Identity System","FIS stands for Fault Injection Simulator."
"With AWS Fault Injection Service (FIS), what type of experiment targets the underlying infrastructure?","Infrastructure experiment","Application experiment","Service experiment","Network experiment","Infrastructure experiments target the underlying compute, network, and storage resources, providing insights into how your application behaves when these resources are impaired."
"In AWS FIS, what is the main purpose of a fault injection experiment?","To proactively identify and mitigate potential weaknesses in a system","To fix coding errors","To increase the number of users","To reduce the price of services","The purpose of fault injection experiments is to proactively identify and mitigate potential weaknesses in a system by intentionally introducing faults."
"Which AWS service is commonly used to monitor the behaviour of applications and infrastructure during an AWS FIS experiment?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch is the primary service used to monitor the performance and health of applications and infrastructure during an FIS experiment."
"When configuring an AWS FIS experiment, what defines the specific action to be performed, such as stopping an EC2 instance?","Action","Target","Template","Experiment Configuration","The action defines the specific fault to be injected, such as stopping an EC2 instance or injecting latency."
"What is an AWS FIS 'target' in the context of an experiment?","The resources on which the fault injection experiment will run","The budget allocated for the experiment","The number of users impacted by the experiment","The time of day the experiment is run","A target is the specific resource (e.g., EC2 instance, RDS database) on which the fault injection experiment will be executed."
"What is the purpose of the AWS FIS service role?","To grant FIS permissions to perform actions on AWS resources","To define the IAM permissions for users accessing FIS","To log all FIS experiment activity","To encrypt FIS experiment data","The FIS service role grants FIS the necessary permissions to perform actions on AWS resources during an experiment, such as stopping an EC2 instance."
"Which of the following is a potential benefit of using AWS Fault Injection Service (FIS)?","Improved application resilience","Reduced infrastructure costs","Faster application deployment","Simplified compliance reporting","AWS FIS helps improve application resilience by identifying and mitigating potential weaknesses through controlled experiments."
"In AWS FIS, what type of experiment is suitable for testing how an application responds to network disruptions?","Network experiment","Compute experiment","Storage experiment","Database experiment","Network experiments are specifically designed to simulate network disruptions such as latency, packet loss, and connectivity issues."
"What is the primary function of an AWS FIS experiment template?","To define the configuration of a fault injection experiment","To store the results of an experiment","To automate the deployment of applications","To manage user access to AWS resources","The experiment template defines the configuration of a fault injection experiment, including the target resources, actions to be performed, and stop conditions."
"When designing an AWS FIS experiment, what is a 'stop condition'?","A criteria that automatically stops the experiment","A waiting period before the experiment starts","A maximum budget for the experiment","A notification sent when the experiment begins","A stop condition is a criterion that automatically stops the experiment if certain conditions are met, preventing unintended consequences."
"Which AWS service can be used to create alarms that trigger stop conditions in AWS FIS experiments?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS IAM","Amazon CloudWatch alarms can be used to monitor metrics and trigger stop conditions in FIS experiments, ensuring that experiments are automatically terminated if thresholds are breached."
"What type of action can you perform on an EC2 instance using AWS FIS?","Stop an instance","Increase instance size","Change the operating system","Add more storage","AWS FIS allows you to perform actions such as stopping an EC2 instance to simulate failures and test the resilience of your application."
"What is the recommended first step when using AWS FIS for the first time?","Start with a small-scale experiment in a non-production environment","Run a large-scale experiment in production","Delete all resources before starting","Skip the monitoring phase","It's recommended to start with a small-scale experiment in a non-production environment to understand how FIS works and to minimise potential impact."
"Which of the following is a key consideration when planning an AWS FIS experiment?","Defining clear objectives and metrics","Ignoring potential impact on production systems","Running the experiment without monitoring","Skipping the rollback plan","Defining clear objectives and metrics is crucial for a successful FIS experiment, allowing you to measure the impact of the injected faults and validate the resilience of your application."
"What is the primary purpose of the 'Experiment Target Resolver' in AWS FIS?","To dynamically identify the resources to inject faults into","To fix identified bugs","To manage experiment permissions","To store experiment data","The 'Experiment Target Resolver' is used to dynamically identify the resources (e.g., EC2 instances, RDS databases) on which the fault injection experiment will be executed."
"Which AWS service provides detailed logs of all actions performed within an AWS FIS experiment?","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS X-Ray","AWS CloudTrail provides detailed logs of all actions performed within an FIS experiment, enabling auditing and troubleshooting."
"What is a common use case for performing latency injection using AWS FIS?","Simulating network congestion","Reducing CPU utilisation","Increasing storage capacity","Improving application performance","Latency injection is used to simulate network congestion and test how an application behaves under increased network latency."
"What does the AWS FIS console allow you to do?","Create, manage, and run fault injection experiments","Configure security groups","Manage user accounts","Deploy applications","The AWS FIS console provides a user-friendly interface for creating, managing, and running fault injection experiments."
"Which of the following best describes the relationship between AWS FIS and chaos engineering?","AWS FIS enables chaos engineering practices","AWS FIS replaces chaos engineering","AWS FIS is unrelated to chaos engineering","AWS FIS is a subset of DevOps","AWS FIS is a tool that enables chaos engineering practices by providing a controlled and automated way to inject faults into systems."
"What is the role of the 'Experiment Template' in AWS Fault Injection Simulator?","It defines the scope and actions of the experiment","It displays the results of the experiment","It lists the pricing of AWS Services","It manages security policies","The experiment template contains the definition of the FIS experiment, which includes the actions that you define to inject failures into your system and the target AWS resources that you are injecting failure into."
"Why is it important to monitor your systems during an AWS FIS experiment?","To understand the impact of the injected faults in real-time","To prevent experiment from running","To save on AWS Costs","To generate alerts","Monitoring systems during an FIS experiment is crucial for understanding the real-time impact of injected faults and validating the resilience of your application."
"Which AWS service is frequently used with AWS Fault Injection Service for logging and auditing FIS experiment events?","AWS CloudTrail","AWS Config","Amazon S3","Amazon EC2","AWS CloudTrail is used for logging and auditing all the API calls and actions related to FIS experiments."
"What should you consider before running an experiment with AWS FIS on a production system?","Implement a rollback plan","Disable all logging","Run it during peak hours","Ignore alerts","Implementing a rollback plan before experimenting in production is critical to minimize any potential disruptions in service."
"What kind of fault injection is AWS FIS best suited for?","Controlled, automated, and safe fault injection","Unplanned manual interruption of services","Changing application code","Data corruption","AWS FIS is designed for controlled, automated, and safe fault injection. It allows for repeatable and predictable experiments."
"What does an AWS FIS 'action' specify within an experiment template?","The type of fault to inject and the resources to target","The monitoring tools to be used","The cost of the experiment","The user roles allowed","The action in FIS experiment templates specifies the type of fault to inject, such as stopping an EC2 instance, and the resources to target."
"Why should you run AWS FIS experiments in a non-production environment first?","To minimize the risk of disrupting production workloads","To save costs","To bypass security checks","To test the experiment setup","Running FIS experiments in a non-production environment first helps to minimize the risk of disrupting production workloads and validate your experiment setup."
"Which is a primary advantage of using AWS Fault Injection Simulator (FIS) for conducting chaos engineering experiments?","It allows for easy automation and controlled execution","It automatically fixes the issues","It replaces the need for monitoring","It increases infrastructure costs","FIS allows for easy automation and controlled execution of fault injection experiments, making chaos engineering practices more accessible."
"How do you typically specify the resources that are affected by a fault injection action in AWS FIS?","By defining an experiment target","By creating a security group","By assigning IAM roles","By manually tagging resources","Resources are typically specified in FIS by defining an experiment target that selects the resources to be affected by the fault injection action."
"What is a key security consideration when using AWS Fault Injection Service (FIS)?","Ensuring the FIS role has minimal necessary permissions","Disabling logging","Granting full administrator access to the FIS role","Allowing unrestricted access to the FIS console","A key security consideration is ensuring that the FIS role has only the minimal necessary permissions to perform the actions required for the experiment."
"Which of these components defines the scope and duration of a fault injection experiment within AWS FIS?","Experiment Template","IAM Role","CloudWatch Dashboard","VPC Configuration","The Experiment Template defines the scope (targets, actions) and duration (stop conditions) of the fault injection experiment."
"What is the most crucial aspect to consider when designing an AWS FIS experiment to test system recovery capabilities?","Ensure that rollback procedures are well-defined and tested","Maximise the number of targets for injection","Disable automatic scaling","Ignore any error messages","Ensuring that rollback procedures are well-defined and tested is crucial when testing system recovery, as this allows for quick restoration in case of unexpected issues."
"Which of the following actions is not natively supported by AWS FIS?","Modifying application code","Stopping EC2 instances","Injecting latency","Simulating CPU stress","AWS FIS does not natively support modifying application code directly; it's more about influencing environment factors."
"How does AWS FIS assist in improving the Mean Time To Recovery (MTTR) of applications?","By identifying failure points and validating recovery mechanisms","By fixing bugs automatically","By reducing the number of incidents","By increasing the size of the EC2 instances","FIS aids in improving MTTR by helping identify failure points and allowing validation of recovery mechanisms."
"What role does monitoring play during an AWS FIS experiment, and what are some common monitoring tools used?","Monitoring helps in understanding the impact, using tools like CloudWatch","Monitoring the tool that applies the injection","Tools to reduce compute costs","Reporting","Monitoring helps in understanding the impact of the injected faults in real-time, and common tools include CloudWatch and X-Ray."
"When using AWS FIS, what is the purpose of specifying a 'Target Filter'?","To narrow down the resources to inject faults into","To block requests to injected targets","To monitor network performance","To increase logging verbosity","The purpose of a 'Target Filter' is to narrow down the selection of resources that will be targeted by the fault injection experiment."
"What can be achieved by injecting latency into network traffic using AWS FIS?","Simulate slow network conditions and test application performance under stress","Speed up the network","Prevent network traffic","Protect the network from DDoS attacks","Injecting latency is useful for simulating slow network conditions and testing how applications perform under stress."
"In the context of AWS FIS, what is a 'fault'?","An impairment to the system, like stopping an EC2 instance or injecting network latency","A user error","A security vulnerability","A fix to a software bug","In AWS FIS, a 'fault' is an impairment or failure introduced into the system for testing, like stopping an EC2 instance or injecting network latency."
"How does running AWS FIS experiments contribute to a shift-left testing approach?","By enabling testing earlier in the development lifecycle","By delaying test to the end","By making all developers be testers","By eliminating the need for a QA team","FIS allows for testing earlier in the development lifecycle by identifying weaknesses proactively."
"What is the benefit of defining stop conditions in AWS FIS using CloudWatch alarms?","Automated termination of experiments based on real-time metrics","Automated log rotation","Automated cost optimisation","Automated backups","Using CloudWatch alarms enables automated termination of experiments based on real-time metrics, preventing unintended consequences."
"What type of resilience testing can AWS FIS facilitate by injecting faults like CPU stress?","Testing application responsiveness under high load","Testing backup schedules","Testing security","Testing the number of users","By injecting CPU stress, FIS can facilitate testing application responsiveness and resilience under high load."
"Which AWS service integrates with AWS FIS to help visualise the impact of experiments?","Amazon CloudWatch","AWS IAM","AWS Config","AWS Trusted Advisor","Amazon CloudWatch integrates with FIS to visualise metrics and the impact of fault injection experiments."
"What is an essential pre-requisite when designing an AWS FIS experiment that modifies network configurations?","Having network firewall rules that allow inbound connections","Having a way to rollback network changes","Having network ACLs setup","Having encrypted connections","It's crucial to have a way to rollback network changes to avoid persisting any negative impacts from the experiment."
"When should you introduce AWS FIS into a development pipeline?","After testing and initial deployment","Before the first deployment","In place of the testing","Only if there is a problem","FIS is best introduced after testing and initial deployment, when you have a baseline understanding of how your application behaves."
"Which of the following strategies would not be conducive to a successful FIS experiment?","Limiting blast radius and starting small","Running large experiments in the live environment","Starting with small scale","Limiting the scope of the experiment","Running large experiments in the live environment is not conducive to a successful FIS experiment."
"What is the purpose of creating a detailed rollback strategy when using AWS Fault Injection Service?","To recover quickly from any unexpected issues during the experiment","To reduce the costs incurred during the experiment","To monitor the application","To improve compliance","A detailed rollback strategy helps ensure quick recovery from any unexpected issues that may arise during the experiment, reducing the impact on production systems."
"You want to test the resilience of your application to EC2 instance failures using AWS FIS. Which type of action would you typically use?","aws:ec2:stop-instances","aws:rds:reboot-db-instance","aws:eks:terminate-node","aws:s3:delete-objects","The `aws:ec2:stop-instances` action allows you to simulate EC2 instance failures by stopping instances."
"How does AWS FIS support the principles of chaos engineering?","By providing a controlled and repeatable way to inject faults into systems","By automatically fixing problems","By completely automating testing","By removing the need for human intervention","AWS FIS supports chaos engineering by enabling controlled and repeatable fault injection, allowing teams to proactively identify and mitigate weaknesses in their systems."
"What is an important consideration when setting up IAM permissions for AWS FIS?","Grant least privilege access to the FIS service role","Grant the FIS service role full administrative access","Ignore IAM policies","Grant all access","Granting least privilege access to the FIS service role is a crucial security best practice to minimize the potential impact of any misconfiguration or compromise."
"What is the relationship between AWS FIS and observability?","AWS FIS relies on observability tools to monitor the impact of fault injection","AWS FIS replaces the need for observability","AWS FIS is used to manage the observability","AWS FIS automatically creates observability dashboards","FIS relies on observability tools (like CloudWatch, X-Ray) to monitor and analyse the impact of fault injection experiments on the system."
