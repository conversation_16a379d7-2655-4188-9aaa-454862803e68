"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS CodePipeline, what does a 'Stage' represent?","A logical unit of the workflow that performs actions on the code","A repository for storing the source code","A notification system for pipeline events","A service for monitoring pipeline performance","A stage represents a distinct phase in the release process, such as Build, Test, or Deploy.  It contains actions that perform a specific function."
"What is the purpose of an 'Action' within a Stage in AWS CodePipeline?","To perform a specific task, like building code or deploying an application","To define the overall structure of the pipeline","To store the configuration details of the pipeline","To manage user permissions for the pipeline","An action performs a specific task, such as building code, running tests, or deploying the application. It is the fundamental unit of work in a stage."
"Which AWS service is commonly used as a source provider for AWS CodePipeline?","AWS CodeCommit","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS CodeCommit is a fully-managed source control service that is often used to store the source code for applications deployed using CodePipeline."
"Which of the following best describes the purpose of AWS CodePipeline?","Automates the software release process","Monitors application performance in real-time","Manages infrastructure as code","Provides a serverless compute environment","CodePipeline automates the build, test, and deployment phases of your release process every time there is a code change, based on the release model you define."
"What is the role of the 'Artifacts' in AWS CodePipeline?","To store the output of each stage of the pipeline","To define the security permissions of the pipeline","To manage the IAM roles of the pipeline","To visualise the pipeline execution status","Artifacts are the files and data that are passed between stages in a pipeline. They represent the output of one stage and the input for the next."
"How can you trigger a CodePipeline pipeline manually?","Using the AWS CLI or AWS Console","By modifying the IAM role","By updating the CloudWatch alarms","By changing the VPC settings","You can manually start a pipeline execution through the AWS CLI or the AWS Management Console."
"In AWS CodePipeline, what is the purpose of an approval action?","To require manual approval before proceeding to the next stage","To automatically approve all changes","To skip a stage in the pipeline","To trigger a CloudWatch alarm","An approval action pauses the pipeline execution and requires a manual approval before the pipeline can proceed to the next stage."
"Which of the following is a benefit of using AWS CodePipeline?","Faster and more reliable software releases","Reduced compute costs","Simplified infrastructure management","Automated security patching","CodePipeline automates the software release process, resulting in faster and more reliable releases."
"What type of actions are used in AWS CodePipeline to build and test software?","Build and Test Actions","Approval Actions","Source Actions","Deploy Actions","Build and Test actions run your tests and builds the code."
"In AWS CodePipeline, what is the purpose of 'transition' between stages?","To define the sequence of stages in the pipeline","To store the build artifacts","To configure IAM roles","To monitor pipeline health","A transition defines the link between stages, specifying the order in which they are executed and how artifacts are passed between them."
"Which AWS service can be integrated with AWS CodePipeline for deployment?","AWS CodeDeploy","AWS Lambda","Amazon S3","AWS IAM","AWS CodeDeploy is a deployment service that automates application deployments to various compute services such as EC2, Lambda, and on-premises servers. It is commonly integrated with CodePipeline."
"What is the difference between 'Source' and 'Deploy' stages in AWS CodePipeline?","'Source' retrieves code; 'Deploy' deploys the application","'Source' builds the code; 'Deploy' tests the code","'Source' requires manual approval; 'Deploy' is automated","'Source' monitors performance; 'Deploy' manages infrastructure","The Source stage fetches the source code from a repository, while the Deploy stage deploys the application to the target environment."
"How can you monitor the progress and status of an AWS CodePipeline pipeline?","Using the AWS Management Console or AWS CLI","Using AWS Config","Using AWS Trusted Advisor","Using AWS Systems Manager","The AWS Management Console and CLI provide tools to visualise the pipeline execution status, logs, and any errors that may occur."
"What IAM permissions are required for an IAM role used by AWS CodePipeline?","Permissions to access source, build, and deployment resources","Permissions to manage AWS accounts","Permissions to modify network configurations","Permissions to access billing information","The IAM role used by CodePipeline must have permissions to access the source code repository, build service, deployment service, and any other AWS resources used by the pipeline."
"Which of the following is a valid source provider for AWS CodePipeline?","GitHub","AWS CloudWatch Events","AWS CloudFormation","AWS Lambda","GitHub is a popular source code repository that can be integrated as a source provider in CodePipeline."
"What does it mean to 'fail a stage' in AWS CodePipeline?","The pipeline execution stops and requires manual intervention","The pipeline continues to the next stage automatically","The stage is skipped and marked as successful","The stage retries automatically without intervention","Failing a stage means that an action within the stage has failed, causing the pipeline execution to stop.  Manual intervention is usually required to resolve the issue."
"What is the purpose of a 'Webhook' in the context of AWS CodePipeline?","To automatically trigger the pipeline when changes are made to the source code","To send notifications when the pipeline fails","To define the IAM roles for the pipeline","To configure the build environment","A webhook allows CodePipeline to automatically detect changes to the source code repository and trigger the pipeline execution."
"Which AWS service can you use to store artifacts generated by AWS CodePipeline?","Amazon S3","Amazon EBS","Amazon Glacier","Amazon RDS","Amazon S3 is commonly used as an artifact store for CodePipeline. It provides scalable and durable storage for the files generated during pipeline execution."
"In AWS CodePipeline, what is the purpose of parameter overrides in the deploy stage?","To customize deployment configurations for different environments","To define IAM roles for the deploy stage","To monitor resource utilisation during deployment","To roll back deployments in case of failure","Parameter overrides allow you to customize the deployment configurations for different environments, such as development, testing, and production."
"What is the best way to ensure that sensitive information is not exposed in your AWS CodePipeline configuration?","Use AWS Secrets Manager to store credentials","Store credentials directly in the CodePipeline configuration","Use hardcoded passwords","Use plain text files","AWS Secrets Manager is the best practice to store and manage sensitive information securely and avoid exposing it in the CodePipeline configuration."
"What can be done to automatically retry a failed action in AWS CodePipeline?","Configure a retry policy within the action's settings","Manually restart the entire pipeline","There is no option to retry","Change the source code and trigger the pipeline again","While not directly configurable, you can use CloudWatch Events to detect a failed Action and trigger another execution, effectively creating a retry mechanism.  There is no direct retry feature in CodePipeline actions."
"Which of the following AWS services integrates with AWS CodePipeline for infrastructure provisioning and management?","AWS CloudFormation","Amazon SQS","Amazon SNS","AWS KMS","AWS CloudFormation is infrastructure as code service that allows you to define and provision AWS resources using templates, which can be integrated with CodePipeline for automated infrastructure deployment."
"You want to implement a Blue/Green deployment strategy using AWS CodePipeline.  What steps might you take?","Use CodeDeploy to manage the deployment to two separate environments, switching traffic after verification","Use CodeCommit to manage two separate code branches","Use CloudWatch to monitor the application health","Use IAM to manage permissions for each environment","Blue/Green deployments involve deploying a new version of the application to a separate environment (Green), verifying its functionality, and then switching traffic from the old environment (Blue) to the new one. CodeDeploy helps to automate this process."
"Which of the following can be used to implement automated testing within an AWS CodePipeline?","AWS CodeBuild","AWS CloudWatch","AWS IAM","AWS CloudTrail","AWS CodeBuild provides a managed build service that can be used to execute automated tests as part of a CodePipeline pipeline."
"What is the purpose of the 'Source Action' in AWS CodePipeline?","To retrieve the source code from a repository","To build the application","To deploy the application","To test the application","The Source action retrieves the source code from a source code repository such as AWS CodeCommit, GitHub, or Bitbucket."
"How does AWS CodePipeline handle security and access control?","Using IAM roles and policies","Using network ACLs","Using security groups","Using AWS Config rules","CodePipeline uses IAM roles and policies to control access to the pipeline and the AWS resources it interacts with."
"Which statement is true about concurrent executions in AWS CodePipeline?","CodePipeline supports concurrent executions of the same pipeline","CodePipeline only allows one execution at a time","CodePipeline will always drop executions if there are concurrent executions","CodePipeline allows only 10 concurrent executions","CodePipeline supports concurrent executions of the same pipeline, allowing multiple code changes to be processed simultaneously."
"In AWS CodePipeline, how can you implement a rollback strategy?","By creating a separate pipeline to deploy the previous version of the application","By using AWS Config to revert changes","By manually restoring the previous version from a backup","By using CloudWatch alarms to trigger a rollback","A rollback strategy can be implemented by creating a separate pipeline that deploys the previous version of the application or by configuring the existing pipeline to revert to the previous deployment."
"What is the function of the 'AWS CloudFormation Create/Update Stack' action in AWS CodePipeline?","To deploy or update infrastructure defined in a CloudFormation template","To monitor CloudFormation stack events","To delete CloudFormation stacks","To validate CloudFormation templates","This action allows you to automate the deployment or update of infrastructure defined in a CloudFormation template as part of your CodePipeline workflow."
"How can you integrate third-party testing tools with AWS CodePipeline?","By using custom actions or AWS Lambda functions","By using AWS Config rules","By using CloudWatch Events","By using AWS IAM roles","Custom actions and AWS Lambda functions can be used to integrate third-party testing tools into a CodePipeline pipeline."
"Which of the following is a limitation of AWS CodePipeline?","Limited number of concurrent executions","Inability to integrate with third-party tools","Lack of support for manual approval actions","Inability to trigger pipelines automatically","AWS CodePipeline does have limitations on the number of concurrent executions. While this limit is often sufficient, it is a factor to consider for very high-volume deployments."
"Which AWS service can be used to receive notifications about AWS CodePipeline events?","Amazon SNS","AWS CloudTrail","AWS Config","Amazon SQS","Amazon SNS can be used to receive notifications about CodePipeline events, such as pipeline execution status changes, stage transitions, and action failures."
"In AWS CodePipeline, how can you configure a pipeline to deploy to multiple environments (e.g., dev, test, prod)?","By creating separate stages for each environment","By using AWS Config rules for each environment","By using CloudWatch Events to trigger deployments","By creating separate IAM roles for each environment","You can configure a pipeline to deploy to multiple environments by creating separate stages for each environment, each with its own deployment actions and configurations."
"What is the primary benefit of automating deployments with AWS CodePipeline?","Reduced human error and faster deployment cycles","Reduced infrastructure costs","Improved security posture","Simplified compliance management","Automation reduces the risk of human error and speeds up the deployment process, leading to faster release cycles."
"Which action type in AWS CodePipeline is responsible for copying artifacts from one location to another?","Invoke","Deploy","Source","Build","The 'Invoke' action type is often used for custom actions such as copying artifacts from one location to another, using a Lambda function, for instance."
"How do you ensure that your AWS CodePipeline is following security best practices?","By regularly reviewing and updating IAM roles and policies","By using network ACLs","By using security groups","By enabling AWS CloudTrail","Regularly reviewing and updating IAM roles and policies associated with your CodePipeline is crucial for ensuring that it adheres to security best practices and that permissions are appropriately scoped."
"What is the benefit of using Infrastructure as Code (IaC) with AWS CodePipeline?","Automated and repeatable infrastructure deployments","Reduced infrastructure costs","Improved security posture","Simplified compliance management","IaC, often using AWS CloudFormation, enables automated and repeatable infrastructure deployments, ensuring consistency and reducing manual errors."
"What is the purpose of AWS CodePipeline's 'input artifacts' and 'output artifacts' when configuring Actions?","Input artifacts are used as input to the action and output artifacts are the result of the action","Input artifacts are not used in actions, output artifacts are always required","Input artifacts are output to the final action, output artifacts are only used if the action fails","Input and output artifacts refer to the same objects","Input artifacts are used as input to the action and output artifacts are the result of the action, passing data and dependencies between stages."
"How do you manage configuration settings that differ across environments when using AWS CodePipeline?","Using Parameter Overrides or Configuration Files for each environment","Storing all configuration settings in AWS Secrets Manager","Using environment variables defined at the CodePipeline level","Hardcoding configuration settings directly into the application code","Using Parameter Overrides or separate Configuration Files tailored for each environment allows you to adapt the deployment process without changing the core logic of the pipeline."
"Which of the following can be used to trigger an AWS CodePipeline from an external event?","AWS CloudWatch Events (EventBridge)","AWS Config","AWS IAM","AWS CloudTrail","AWS CloudWatch Events (now known as Amazon EventBridge) can be configured to trigger a CodePipeline in response to external events."
"What is the purpose of 'polling' in AWS CodePipeline when configured with a source like AWS CodeCommit?","Checking for changes in the source code repository","Configuring IAM permissions","Sending notifications","Monitoring pipeline health","Polling is used to periodically check for changes in the source code repository, triggering the pipeline when changes are detected. Webhooks are better for triggering when changes occur."
"How can you version control your AWS CodePipeline configurations?","By storing the CodePipeline configuration as code in a repository like AWS CodeCommit","By using AWS Config to track changes to the pipeline","By manually backing up the CodePipeline configuration","By using CloudWatch Events to monitor the pipeline","Storing the CodePipeline configuration as code in a repository allows you to version control it, enabling you to track changes, revert to previous versions, and manage the configuration as code."
"Which of the following is NOT a native integration for source control with AWS CodePipeline?","Subversion","AWS CodeCommit","GitHub","Bitbucket","Subversion (SVN) is not directly supported as a native integration for source control in AWS CodePipeline without using custom actions or workarounds."
"How do you define the order of execution for stages in AWS CodePipeline?","By configuring transitions between stages","By setting priorities for each stage","By defining dependencies between stages","By naming the stages in a specific order","Transitions define the order of execution between stages by linking them together. This specifies which stage should run after another."
"In AWS CodePipeline, what is the purpose of the 'Invalidation' action when used with Amazon CloudFront?","To clear the CloudFront cache after a deployment","To encrypt the data in CloudFront","To monitor CloudFront performance","To create a new CloudFront distribution","The 'Invalidation' action is used to clear the CloudFront cache, ensuring that users receive the latest version of the content after a deployment."
"How can you ensure consistent deployment configurations across multiple AWS regions when using AWS CodePipeline?","By using CloudFormation StackSets or similar tools within the pipeline","By manually configuring each region separately","By using AWS Config to enforce compliance","By using AWS IAM to manage permissions","Using CloudFormation StackSets, or similar infrastructure-as-code tools, allows you to deploy and manage resources consistently across multiple AWS regions."
"What is the relationship between AWS CodePipeline and AWS CloudTrail?","AWS CloudTrail records API calls made to AWS CodePipeline, providing an audit trail","AWS CloudTrail triggers AWS CodePipeline","AWS CodePipeline monitors AWS CloudTrail logs","AWS CodePipeline configures AWS CloudTrail","AWS CloudTrail records API calls made to AWS CodePipeline, providing an audit trail for security and compliance purposes."
"How would you prevent a specific user from approving actions in an AWS CodePipeline?","By modifying the IAM policy associated with the user's role","By using network ACLs","By configuring AWS Config rules","By setting permissions at the stage level","Modifying the IAM policy associated with the user's role is the proper way to manage user permissions for approving actions within AWS CodePipeline."
"In AWS CodePipeline, what is the purpose of an 'Action'?","Defines a single task to be performed in a stage","Defines the overall pipeline structure","Defines the permissions for the pipeline","Defines the source code repository","An action represents a single task or operation within a stage, such as building code, running tests, or deploying an application."
"What is the primary function of a 'Stage' within AWS CodePipeline?","To group related actions together","To define the trigger for the pipeline","To specify the output artifact location","To manage user access to the pipeline","A stage groups related actions together into a logical phase of the release process. This helps to organise and visualise the workflow."
"Which AWS service is commonly used as a source provider for AWS CodePipeline?","AWS CodeCommit","Amazon S3","AWS Lambda","Amazon EC2","AWS CodeCommit is a fully managed source control service that integrates directly with CodePipeline to provide source code."
"When a CodePipeline execution fails, what is the first step you should take to troubleshoot the issue?","Examine the logs for the failed action","Restart the entire pipeline","Increase the timeout for the action","Disable the failing stage","Examining the logs provides detailed information about the error that occurred during the action execution."
"Which of the following is a benefit of using AWS CodePipeline?","Automating the software release process","Reducing the cost of infrastructure","Replacing manual testing processes","Managing database backups","CodePipeline automates the steps required to release software, making the process faster and more reliable."
"What type of artifacts does AWS CodePipeline use to pass data between stages?","Amazon S3 objects","AWS Lambda functions","Amazon EC2 instances","AWS IAM roles","CodePipeline uses S3 buckets to store and manage the artifacts that are passed between stages in the pipeline."
"What is the purpose of the 'Transition' in AWS CodePipeline?","To move artifacts between stages","To define the security roles for the pipeline","To specify the build environment","To trigger the pipeline manually","Transitions control the flow of artifacts between stages, ensuring that only successful stages pass on to the next phase."
"Which of the following action types is used to deploy an application in AWS CodePipeline?","Deploy","Build","Source","Test","The 'Deploy' action type is specifically designed to deploy applications to various environments, such as EC2, ECS, or Lambda."
"How can you trigger an AWS CodePipeline pipeline execution?","By committing changes to the source code repository","By manually starting the pipeline","By creating an AWS CloudWatch event","All of the above","CodePipeline pipelines can be triggered by source code changes, manual starts, or CloudWatch events."
"Which AWS service can be integrated with AWS CodePipeline to perform automated testing?","AWS CodeBuild","Amazon CloudWatch","AWS CloudTrail","Amazon SQS","AWS CodeBuild can be used to run automated tests as part of a CodePipeline pipeline, ensuring code quality and reliability."
"In AWS CodePipeline, what is the purpose of the 'Build' stage?","To compile and package the source code","To deploy the application","To configure infrastructure","To manage user access","The 'Build' stage is where the source code is compiled, packaged, and prepared for deployment."
"How can you define the build environment for an AWS CodePipeline action?","Using an AWS CodeBuild project","Using an Amazon EC2 instance","Using an AWS Lambda function","Using an Amazon S3 bucket","AWS CodeBuild projects define the environment and build instructions for actions within a CodePipeline."
"Which of the following is NOT a native integration for AWS CodePipeline?","GitHub","AWS CodeCommit","Bitbucket","Azure DevOps","Azure DevOps is not a native integration for AWS CodePipeline. Native integrations include AWS CodeCommit, GitHub, and Bitbucket."
"What IAM permission is required for AWS CodePipeline to access AWS resources on your behalf?","An IAM service role","An IAM user","An IAM group","An IAM policy","CodePipeline requires an IAM service role that grants it permissions to access and manage AWS resources required for the pipeline."
"How can you rollback a failed deployment in AWS CodePipeline?","By manually deploying a previous version of the application","By using a rollback action in the pipeline","By pausing the pipeline execution","By deleting the pipeline","Rollbacks typically involve manually deploying a previous, known-good version of the application.  There is no automatic rollback feature in CodePipeline."
"Which AWS service can be used to store and version control infrastructure as code (IaC) templates for AWS CodePipeline?","AWS CodeCommit","Amazon S3","AWS CloudWatch Logs","AWS CloudTrail","AWS CodeCommit is ideal for storing and version controlling infrastructure as code (IaC) templates."
"What is the function of the 'Artifact Store' in AWS CodePipeline?","To store the pipeline's artifacts and intermediate files","To store the pipeline's configuration","To store the pipeline's execution history","To store the pipeline's IAM roles","The Artifact Store, typically an S3 bucket, is where CodePipeline stores all the artifacts and intermediate files generated during the pipeline's execution."
"Which type of action can be used to perform static code analysis in AWS CodePipeline?","Invoke","Test","Deploy","Source","A 'Test' action can be configured to run static code analysis tools to identify potential issues in the code."
"How can you configure AWS CodePipeline to notify you of pipeline events, such as failures or successes?","Using Amazon CloudWatch Events","Using Amazon SQS","Using AWS Lambda","Using Amazon SNS","Amazon CloudWatch Events (now Amazon EventBridge) can trigger actions based on CodePipeline events, allowing you to send notifications via SNS."
"What is the purpose of the 'Approval' action type in AWS CodePipeline?","To require manual approval before a stage can proceed","To automatically approve all stages","To reject all stages","To skip a stage in the pipeline","The 'Approval' action type allows you to insert a manual approval step into the pipeline, requiring a user to approve the stage before it continues."
"How does AWS CodePipeline handle concurrency by default?","It executes pipelines sequentially","It executes pipelines concurrently","It randomly chooses which pipeline to execute","Concurrency must be manually configured","By default, CodePipeline executes pipelines sequentially, one at a time."
"Which AWS CLI command is used to create a new AWS CodePipeline pipeline?","aws codepipeline create-pipeline","aws pipeline create","aws create-codepipeline","aws new-pipeline","The `aws codepipeline create-pipeline` command is used to define and create a new pipeline."
"Which AWS service is commonly used to orchestrate deployments to container environments like Amazon ECS or EKS from AWS CodePipeline?","AWS CodeDeploy","AWS CloudFormation","Amazon SQS","Amazon SNS","AWS CodeDeploy is commonly used to orchestrate deployments to container environments like ECS or EKS."
"What is the purpose of the 'Source' stage in an AWS CodePipeline pipeline?","To retrieve the source code from a repository","To build the application","To test the application","To deploy the application","The 'Source' stage retrieves the source code from the specified repository, such as CodeCommit, GitHub, or Bitbucket."
"What happens to a CodePipeline execution when an action fails?","The pipeline stops, and the failure must be resolved","The pipeline automatically retries the failed action","The pipeline continues to the next stage","The pipeline rolls back to the previous stage","When an action fails, the pipeline stops at that stage, and the issue must be addressed before the pipeline can proceed."
"How can you control access to AWS CodePipeline pipelines?","Using IAM roles and policies","Using Amazon S3 bucket policies","Using AWS CloudTrail","Using AWS Config","IAM roles and policies are used to control access to CodePipeline pipelines, defining who can view, create, modify, or execute them."
"Which of the following is NOT a valid stage transition configuration option in AWS CodePipeline?","Manual","Automatic","Conditional","Disabled","'Conditional' is not a valid stage transition configuration option. Transitions can be 'Manual' or 'Automatic'."
"What is the function of the 'AWS::CodePipeline::Pipeline' CloudFormation resource?","To define an AWS CodePipeline pipeline in a CloudFormation template","To define an AWS CodeBuild project","To define an AWS CodeCommit repository","To define an AWS CloudWatch event","The 'AWS::CodePipeline::Pipeline' resource is used to define a CodePipeline pipeline within a CloudFormation template."
"How can you parameterise an AWS CodePipeline pipeline?","Using variables in the pipeline definition","Using AWS Systems Manager Parameter Store","Using environment variables in CodeBuild","Using Amazon S3 objects","While environment variables can be used in CodeBuild actions, AWS Systems Manager Parameter Store is the best way to parameterise a CodePipeline, ensuring secure and manageable configuration."
"What does the CodePipeline 'Poll for Source Changes' setting do?","Checks for changes to the source repository automatically","Only runs the pipeline when manually triggered","Deletes the pipeline after each execution","Changes the IAM role used by the pipeline","The 'Poll for Source Changes' setting configures the pipeline to automatically check for changes in the source repository and trigger a new execution when changes are detected."
"Which AWS service can be used to trigger AWS CodePipeline based on a schedule?","Amazon CloudWatch Events (EventBridge)","Amazon SQS","AWS Lambda","Amazon SNS","CloudWatch Events (EventBridge) can be configured to trigger CodePipeline executions based on a defined schedule."
"Which action type is best suited for running integration tests on an application deployed to a staging environment?","Invoke","Deploy","Test","Build","'Test' actions are ideal for running integration tests, often executed after a deployment to a staging environment."
"What is the purpose of the 'AWS CloudFormation Create or Update Stack' action in AWS CodePipeline?","To manage infrastructure deployments","To deploy application code","To run database migrations","To provision compute instances","This action type allows CodePipeline to manage infrastructure deployments by creating or updating CloudFormation stacks."
"How can you integrate third-party testing tools into AWS CodePipeline?","Using custom actions and AWS Lambda functions","Using the AWS CLI directly","By editing the pipeline JSON configuration","By using AWS CloudFormation","Custom actions, often leveraging AWS Lambda functions, provide a flexible way to integrate third-party tools into CodePipeline."
"What is the best practice for managing sensitive information, such as API keys, within an AWS CodePipeline pipeline?","Store them in AWS Secrets Manager and retrieve them in the build process","Store them directly in the pipeline configuration","Store them in environment variables in CodeBuild","Store them in the source code repository","AWS Secrets Manager provides a secure and managed way to store and retrieve sensitive information."
"How can you view the execution history of an AWS CodePipeline pipeline?","In the AWS CodePipeline console","In Amazon CloudWatch Logs","In AWS CloudTrail","In Amazon S3","The AWS CodePipeline console provides a visual representation of the pipeline's execution history, including the status of each stage and action."
"What is the role of AWS Identity and Access Management (IAM) in AWS CodePipeline?","To control access to CodePipeline resources and define permissions","To monitor pipeline activity","To store pipeline artifacts","To define pipeline stages","IAM is used to control access to CodePipeline resources, defining who can perform actions and manage the pipeline."
"What is the purpose of the AWS CodePipeline 'Webhook' integration?","To automatically trigger the pipeline when changes are pushed to a source code repository","To manually trigger the pipeline","To schedule pipeline executions","To monitor the health of the pipeline","Webhooks automatically trigger the pipeline when changes are pushed to a supported source code repository."
"What is the effect of enabling 'Disable stage transition' on a specific stage of an AWS CodePipeline pipeline?","It prevents artifacts from being passed to the next stage","It prevents the stage from executing","It deletes the stage from the pipeline","It hides the stage in the console","Disabling a stage transition prevents artifacts from being passed to the subsequent stage in the pipeline."
"Which of the following AWS services is commonly used for continuous integration and continuous delivery (CI/CD) in AWS?","AWS CodePipeline","Amazon EC2","Amazon S3","AWS Lambda","AWS CodePipeline is specifically designed for CI/CD workflows."
"What is the purpose of the 'inputArtifacts' and 'outputArtifacts' properties in a CodePipeline action configuration?","To define the data dependencies between actions","To define the IAM roles for the action","To define the build environment for the action","To define the source code repository for the action","These properties specify the data that is passed into and produced by the action, creating dependencies and a flow of information."
"What is the maximum number of stages allowed in an AWS CodePipeline pipeline?","There is no fixed limit","5","10","20","There is no fixed number of stages allowed in CodePipeline."
"Which of the following is NOT a valid action provider for the 'Source' stage in AWS CodePipeline?","GitHub","AWS CodeCommit","Bitbucket","AWS Lambda","AWS Lambda is not a valid action provider for the 'Source' stage. The source stage is for retreiving source code"
"How can you promote a build artifact from a staging environment to a production environment using AWS CodePipeline?","By using a manual approval stage","By automatically deploying to production after staging","By manually copying the artifact to the production environment","By deleting the staging environment","A manual approval stage allows you to verify the build artifact in the staging environment before promoting it to production."
"Which of the following is a characteristic of a 'blue/green' deployment strategy using AWS CodePipeline and CodeDeploy?","Two identical environments are maintained, with traffic switched between them","Code is deployed to a single environment","Code is deployed in parallel across multiple regions","Code is tested in production","Blue/green deployments involve maintaining two identical environments (blue and green) and switching traffic between them for seamless updates."
"You need to ensure that changes made to your AWS CodePipeline pipeline configuration are tracked. Which AWS service should you use?","AWS CloudTrail","Amazon CloudWatch","AWS Config","Amazon S3","AWS CloudTrail logs API calls made to AWS services, including changes to CodePipeline configurations."
"How can you reuse CodePipeline components across multiple pipelines?","By using AWS CloudFormation templates","By copying and pasting the pipeline configuration","By using AWS Lambda functions","By using Amazon S3 bucket policies","AWS CloudFormation templates allow you to define and reuse infrastructure as code, including CodePipeline components."
"What is the purpose of the 'Version' attribute in a CodePipeline action configuration?","Specifies the version of the action's provider","Specifies the version of the source code","Specifies the version of the pipeline itself","Specifies the version of the AWS SDK","The 'Version' attribute specifies the version of the action's provider (e.g., the version of the CodeBuild project or the CodeDeploy application)."
"What is the main benefit of integrating AWS CodePipeline with AWS CloudFormation?","Automated infrastructure provisioning and management","Automated application deployment","Automated code testing","Automated database backups","Integrating CodePipeline with CloudFormation enables automated infrastructure provisioning and management as part of the CI/CD process."
"Which of the following is NOT a valid AWS CodePipeline action category?","Source","Build","Deploy","Monitor","'Monitor' is not a valid action category. Valid categories are 'Source', 'Build', 'Deploy', 'Test' and 'Invoke'."
