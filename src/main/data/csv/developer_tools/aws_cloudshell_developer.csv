"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"Regarding Amazon CloudShell, what is the maximum storage capacity provided for your home directory?","1 GB","500 MB","10 GB","Unlimited","Amazon CloudShell provides 1 GB of persistent storage for your home directory."
"When using Amazon CloudShell, which AWS CLI command would you use to list your S3 buckets?","aws s3 ls","aws s3 list-buckets","aws s3 get-buckets","aws s3 show-buckets","The correct AWS CLI command to list S3 buckets is `aws s3 ls`."
"A developer is using Amazon CloudShell and needs to install a specific Python library not pre-installed. How can they achieve this?","Use pip to install the library in the home directory.","They cannot install additional libraries.","Request AWS support to install the library.","Use the yum package manager.","Users can install additional software and libraries using package managers like pip or yum within their CloudShell session, typically in their home directory for persistence."
"In Amazon CloudShell, what is the default shell environment?","Bash","Zsh","Fish","Sh","The default shell environment in Amazon CloudShell is Bash."
"Which IAM permission is required for a user to access and use Amazon CloudShell?","cloudshell:StartSession","cloudshell:AccessShell","cloudshell:ExecuteCommand","cloudshell:Connect","The `cloudshell:StartSession` IAM permission is required to allow a user to start and use an Amazon CloudShell session."
"A developer is working on a project in Amazon CloudShell and needs to transfer files to their local machine. What is the recommended method?","Using the Actions menu to download files.","Using the scp command.","Using the aws s3 cp command to transfer to S3 and then download.","Using an FTP client.","Amazon CloudShell provides an option in the Actions menu to download files directly to your local machine."
"What is the primary purpose of the persistent storage in Amazon CloudShell?","To store session logs.","To store user files, scripts, and configurations across sessions.","To store temporary files for command execution.","To store pre-installed software packages.","The persistent storage in CloudShell is designed to retain user files, scripts, and configurations across different sessions."
"When might you choose to use Amazon CloudShell over a local AWS CLI installation?","When you need a pre-authenticated environment with access to AWS resources.","When you require a highly customised development environment.","When you need to work offline.","When you have strict corporate firewall restrictions.","CloudShell provides a convenient, pre-authenticated environment directly in the browser, eliminating the need for local AWS CLI setup and authentication."
"Which of the following is a pre-installed tool available in Amazon CloudShell?","AWS CDK","Docker","Kubernetes","Terraform","Amazon CloudShell comes with the AWS CDK pre-installed, among other development tools."
"A user is experiencing issues with persistent storage in Amazon CloudShell. What is a common reason for this?","Exceeding the 1 GB storage limit.","Incorrect IAM permissions.","Using an unsupported browser.","The session timing out.","Exceeding the 1 GB storage limit is a common reason for issues with persistent storage in CloudShell."
"How can you upload files from your local machine to your Amazon CloudShell session?","Using the Actions menu to upload files.","Using the scp command.","Using the aws s3 cp command to upload to S3 and then transfer.","Using an FTP client.","Amazon CloudShell provides an option in the Actions menu to upload files from your local machine."
"What is the maximum duration of an idle Amazon CloudShell session before it is automatically terminated?","60 minutes","30 minutes","120 minutes","240 minutes","An idle Amazon CloudShell session is automatically terminated after 60 minutes."
"Which AWS service is integrated with Amazon CloudShell for managing credentials?","AWS IAM","AWS Secrets Manager","AWS Systems Manager Parameter Store","AWS Key Management Service (KMS)","Amazon CloudShell uses your AWS IAM credentials for authentication and authorisation to AWS services."
"A developer wants to use a specific version of the AWS CLI in Amazon CloudShell that is not the default. How can they manage this?","Install the desired version in their home directory.","They cannot change the AWS CLI version.","Request AWS support to change the version.","Use a different AWS service.","Users can install and manage different versions of the AWS CLI within their CloudShell home directory."
"What is the primary benefit of Amazon CloudShell for developers?","Provides a fully managed, browser-based shell for interacting with AWS services.","Offers a complete IDE for application development.","Provides a dedicated virtual machine for each user.","Offers offline access to AWS resources.","CloudShell provides a convenient, browser-based shell environment for quick access and interaction with AWS services."
"Which network protocol is used to connect to the Amazon CloudShell environment?","HTTPS","SSH","RDP","VNC","You connect to the Amazon CloudShell environment through your web browser using HTTPS."
"Can you access resources in a private VPC from Amazon CloudShell?","Yes, if configured with a VPC endpoint.","No, CloudShell is only for public resources.","Only if the VPC has a public IP address.","Only if you use a VPN connection.","You can access resources in a private VPC from CloudShell by configuring a VPC endpoint for CloudShell."
"What happens to your session environment and persistent storage when your Amazon CloudShell session is terminated due to inactivity?","The session environment is lost, but persistent storage is retained.","Both the session environment and persistent storage are lost.","Both the session environment and persistent storage are retained.","Only the session environment is retained.","When a CloudShell session is terminated due to inactivity, the session environment is lost, but the persistent storage (home directory) is retained."
"Which of the following programming languages has pre-installed support in Amazon CloudShell?","All of the above.","Python","Java (JDK)","Node.js","Amazon CloudShell comes with pre-installed support for several programming languages including Python, Java (JDK), and Node.js."
"A developer needs to run a long-running script in Amazon CloudShell. What should they consider to prevent session termination?","Run the script in the background using nohup or screen.","Keep the session active by interacting with it periodically.","Increase the session timeout limit (if possible).","Split the script into smaller parts.","Running the script in the background using tools like `nohup` or `screen` can prevent session termination due to inactivity."
"What is the primary security benefit of using Amazon CloudShell?","It eliminates the need to store AWS credentials on your local machine.","It encrypts all data transferred to and from AWS.","It provides a dedicated firewall for your session.","It automatically scans your commands for malicious activity.","Using CloudShell reduces the security risk by not requiring you to store AWS credentials directly on your local machine."
"Which AWS service provides the underlying compute capacity for Amazon CloudShell?","AWS Fargate","Amazon EC2","AWS Lambda","AWS Batch","Amazon CloudShell runs on AWS Fargate, providing a serverless compute environment."
"A developer wants to customise their Amazon CloudShell environment with specific aliases and functions. Where should they store these configurations for persistence?","In files within their home directory (e.g., .bashrc).","In a separate configuration file in an S3 bucket.","In the AWS Systems Manager Parameter Store.","In the IAM console.","Custom configurations like aliases and functions should be stored in shell configuration files (like `.bashrc`) within the persistent home directory."
"What is the maximum number of concurrent Amazon CloudShell sessions a user can have by default?","One per AWS Region.","Unlimited.","Five per AWS account.","Ten per AWS Region.","By default, a user can have one Amazon CloudShell session per AWS Region."
"Which of the following is NOT a valid way to access Amazon CloudShell?","Via the AWS CLI.","Via the AWS Management Console.","Via the AWS SDKs.","Via a direct URL.","Amazon CloudShell is primarily accessed via the AWS Management Console or a direct URL, not directly through the AWS CLI or SDKs."
"A developer is troubleshooting an issue in Amazon CloudShell and needs to view logs. Where are session logs typically stored?","Session logs are not persistently stored.","In an Amazon CloudWatch Logs group.","In an S3 bucket configured for logging.","In the persistent home directory.","Amazon CloudShell session logs are not persistently stored and are lost after the session ends."
"What is the primary advantage of Amazon CloudShell's integration with the AWS Management Console?","Provides a seamless transition between the console and the command line.","Allows you to manage all AWS services from the console.","Provides a graphical interface for command execution.","Enables offline access to the console.","The integration allows for a smooth workflow between managing resources in the console and using the command line in CloudShell."
"Which of the following is a limitation of Amazon CloudShell?","Limited persistent storage.","Lack of pre-installed development tools.","Inability to access AWS services.","Requires a dedicated EC2 instance.","A key limitation of Amazon CloudShell is the limited 1 GB of persistent storage."
"A developer needs to use Git in Amazon CloudShell. Is Git pre-installed?","Yes, Git is pre-installed.","No, Git needs to be installed manually.","Git is only available if you configure a specific environment.","Git is not supported in CloudShell.","Git is one of the development tools that comes pre-installed in Amazon CloudShell."
"What is the purpose of the 'Actions' menu in the Amazon CloudShell interface?","To upload and download files, and restart the environment.","To manage IAM permissions.","To configure network settings.","To view billing information.","The 'Actions' menu provides options for file uploads/downloads and restarting the CloudShell environment."
"Which AWS service provides the networking for Amazon CloudShell sessions?","Amazon VPC","AWS Direct Connect","AWS Transit Gateway","AWS VPN","Amazon CloudShell sessions utilise Amazon VPC for networking."
"A developer wants to automate a task using a script in Amazon CloudShell. What is the best practice for storing and executing this script?","Store the script in the persistent home directory and execute it from there.","Store the script in an S3 bucket and download it for execution.","Store the script in AWS Systems Manager Parameter Store and retrieve it.","Store the script in AWS Secrets Manager.","Storing scripts in the persistent home directory allows for easy access and execution across sessions."
"What is the primary difference between Amazon CloudShell and AWS Cloud9?","CloudShell is a browser-based shell, while Cloud9 is a cloud-based IDE.","CloudShell provides persistent storage, while Cloud9 does not.","CloudShell supports multiple programming languages, while Cloud9 only supports one.","CloudShell requires a dedicated EC2 instance, while Cloud9 is serverless.","CloudShell is a lightweight shell environment, whereas Cloud9 is a full-featured integrated development environment (IDE)."
"Which of the following is a valid use case for Amazon CloudShell in a development workflow?","Quickly testing AWS CLI commands or scripts.","Developing and debugging large-scale applications.","Managing production databases.","Deploying complex infrastructure using Infrastructure as Code.","CloudShell is ideal for quickly testing commands, running small scripts, and interacting with AWS services ad-hoc."
"How does Amazon CloudShell handle security updates and patching of the underlying environment?","AWS manages the security updates and patching.","Users are responsible for patching the environment.","Updates are applied automatically during session start.","Updates are applied manually by the user.","AWS is responsible for managing the security updates and patching of the Amazon CloudShell environment."
"A developer needs to access a resource in another AWS account from their Amazon CloudShell session. How can this be achieved?","Assuming an IAM role in the target account.","Using the AWS CLI with explicit credentials for the target account.","Configuring a VPC peering connection.","Using AWS Organizations.","Assuming an IAM role in the target account is the standard and secure way to access resources across accounts from CloudShell."
"What is the primary advantage of Amazon CloudShell being pre-authenticated?","You don't need to configure AWS credentials manually.","It provides elevated permissions by default.","It allows access to all AWS services without restrictions.","It enables offline access to AWS resources.","The pre-authenticated nature means you don't need to manually configure or manage AWS credentials within the CloudShell environment."
"Which of the following is NOT a pre-installed package manager in Amazon CloudShell?","Apt","Yum","Pip","Npm","While Yum, Pip, and Npm are pre-installed, Apt is not typically found in the default CloudShell environment."
"A developer is using Amazon CloudShell to interact with an S3 bucket. Which IAM action is required to list objects in a bucket?","s3:ListObjects","s3:GetObject","s3:PutObject","s3:DeleteObject","The `s3:ListObjects` IAM action is required to list the objects within an S3 bucket."
"What is the purpose of the 'Restart Environment' option in Amazon CloudShell?","To reset the session to its default state, clearing temporary files but retaining persistent storage.","To terminate the session permanently.","To upgrade the CloudShell environment to a newer version.","To increase the persistent storage limit.","Restarting the environment resets the session to its initial state, clearing temporary data but preserving the contents of the persistent home directory."
"Which of the following is a valid method for transferring files between your persistent storage and an S3 bucket within Amazon CloudShell?","Using the aws s3 cp command.","Using the Actions menu.","Using an FTP client.","Using the scp command.","The `aws s3 cp` command is the standard way to transfer files between your CloudShell persistent storage and Amazon S3."
"A developer is writing a shell script in Amazon CloudShell that requires access to sensitive information. Where should this information be securely stored and accessed?","AWS Secrets Manager","In a file within the persistent home directory.","In environment variables within the script.","Hardcoded within the script.","AWS Secrets Manager is the recommended service for securely storing and retrieving sensitive information like API keys or database credentials."
"What is the primary use case for the pre-installed AWS CDK in Amazon CloudShell?","Deploying infrastructure using code.","Managing containerized applications.","Developing serverless functions.","Building machine learning models.","The pre-installed AWS CDK allows developers to define and provision cloud infrastructure through code directly from the CloudShell environment."
"Which of the following is a benefit of using Amazon CloudShell for troubleshooting AWS issues?","Provides a consistent environment with pre-installed tools.","Allows for offline troubleshooting.","Provides a graphical interface for debugging.","Automatically identifies and fixes issues.","CloudShell offers a consistent environment with necessary tools, making it useful for troubleshooting AWS-related issues."
"A developer needs to install a custom tool in their Amazon CloudShell environment that requires root privileges. Is this possible?","No, users do not have root privileges in Amazon CloudShell.","Yes, users have full root access.","Root access can be requested from AWS support.","Root access is available for a limited time.","Users do not have root privileges in the Amazon CloudShell environment for security reasons."
"What is the primary advantage of Amazon CloudShell being a browser-based service?","Accessibility from anywhere with an internet connection.","Faster command execution compared to a local terminal.","Offline access to AWS resources.","Lower cost compared to a local machine.","Being browser-based allows access to CloudShell from virtually any device with an internet connection."
"Which of the following is a potential cost consideration when using Amazon CloudShell?","Data transfer out of AWS.","Compute time used by the session.","Persistent storage usage beyond the free tier.","All of the above.","While CloudShell itself is free, associated costs like data transfer out of AWS or usage of other AWS services from CloudShell can incur charges."
"A developer is using Amazon CloudShell to interact with an EC2 instance. Which AWS CLI command would they likely use to connect to the instance via SSH?","ssh -i key.pem ec2-user@instance-ip","aws ec2-instance ssh","aws ec2 start-session","aws ec2 connect","While `aws ec2 start-session` is for Session Manager, the standard SSH command with the key pair is used for direct SSH connections, which can be initiated from CloudShell if the instance is accessible."
"What is the purpose of the pre-installed `jq` tool in Amazon CloudShell?","Processing JSON data.","Managing Git repositories.","Compiling code.","Deploying applications.","The `jq` tool is a lightweight and flexible command-line JSON processor, useful for working with JSON output from AWS CLI commands."
"A developer needs to share a script they created in Amazon CloudShell with a colleague. What is the most straightforward way to do this?","Download the script using the Actions menu and share the file.","Copy and paste the script content.","Share the persistent storage volume.","Use an AWS service like CodeCommit to store and share the script.","Downloading the script using the Actions menu is a straightforward way to get the file to share with a colleague."
"Which of the following is a security best practice when using Amazon CloudShell?","Granting users only the necessary IAM permissions.","Storing sensitive data in the persistent home directory.","Using the same password for your AWS account and CloudShell.","Disabling multi-factor authentication (MFA).","Granting least privilege by providing only necessary IAM permissions is a fundamental security best practice."
"What is the primary benefit of the pre-installed AWS SAM CLI in Amazon CloudShell?","Building and deploying serverless applications.","Managing Kubernetes clusters.","Developing mobile applications.","Creating machine learning models.","The AWS SAM CLI is used for building, testing, and deploying serverless applications defined with the AWS Serverless Application Model (SAM)."
"A developer is using Amazon CloudShell to work with a large file in their persistent storage. What should they consider regarding performance?","The performance of persistent storage is limited compared to other storage options.","Persistent storage performance is equivalent to S3.","Persistent storage performance is equivalent to EBS.","Performance is not a consideration for persistent storage.","The performance of CloudShell's persistent storage is generally suitable for typical shell operations but may be limited for very large files or high-performance workloads compared to services like S3 or EBS."
"Which of the following is a valid method for clearing the persistent storage in Amazon CloudShell?","Using the 'Delete home directory' option in the Actions menu.","Running a specific AWS CLI command.","Deleting all files in the home directory manually.","Persistent storage cannot be cleared.","The 'Delete home directory' option in the Actions menu is the way to clear the persistent storage."
"A developer needs to access a database running on an EC2 instance in a private subnet from Amazon CloudShell. What is a common approach to enable this connectivity?","Using AWS Systems Manager Session Manager to port forward.","Directly connecting using the database client in CloudShell.","Configuring a public IP address for the EC2 instance.","Using a NAT Gateway in the private subnet.","Using AWS Systems Manager Session Manager with port forwarding is a common and secure method to access resources in private subnets from CloudShell."
"What is the primary purpose of the pre-installed `aws-iam-authenticator` in Amazon CloudShell?","Authenticating to Amazon EKS clusters.","Authenticating to the AWS Management Console.","Authenticating to Amazon EC2 instances.","Authenticating to Amazon S3 buckets.","The `aws-iam-authenticator` is used to authenticate to Amazon Elastic Kubernetes Service (EKS) clusters using IAM credentials."
"A developer is using Amazon CloudShell to deploy a CloudFormation stack. Which AWS CLI command would they use?","aws cloudformation deploy","aws cloudformation create-stack","aws cloudformation execute-stack","aws cloudformation run-stack","The `aws cloudformation deploy` command is commonly used to deploy CloudFormation stacks from a template file."
"What is the primary benefit of Amazon CloudShell for learning and experimenting with AWS services?","Provides a low-cost environment for hands-on practice.","Offers guided tutorials for all AWS services.","Provides a simulated AWS environment.","Offers offline access to learning resources.","CloudShell provides a free tier and a readily available environment, making it excellent for hands-on learning and experimentation."
"Which of the following is a valid method for installing additional software in Amazon CloudShell?","Using package managers like yum or pip.","Downloading and running executable files directly.","Using the AWS Marketplace.","Requesting installation from AWS support.","Using pre-installed package managers like yum (for Amazon Linux) or pip (for Python packages) is the standard way to install software."
"A developer needs to configure environment variables for their scripts in Amazon CloudShell. Where should these be set for persistence across sessions?","In shell configuration files like .bashrc or .profile within the home directory.","Using the `export` command directly in the terminal.","In the AWS Systems Manager Parameter Store.","In the IAM console.","Setting environment variables in shell configuration files within the persistent home directory ensures they are loaded in subsequent sessions."
"What is the primary advantage of Amazon CloudShell's integration with the AWS CLI?","Provides a pre-configured and authenticated AWS CLI environment.","Offers a graphical interface for AWS CLI commands.","Enables offline execution of AWS CLI commands.","Provides a dedicated network connection for AWS CLI.","The integration means the AWS CLI is ready to use immediately with your authenticated AWS identity."
"Which of the following is a security consideration when using Amazon CloudShell in a shared account environment?","Ensuring users have appropriate IAM permissions to prevent unauthorized actions.","Sharing the persistent home directory among users.","Storing sensitive data in the persistent home directory.","Using the same SSH key pair for all users.","Properly managing IAM permissions is crucial in a shared account to limit what users can do from their CloudShell sessions."
"A developer is using Amazon CloudShell to build a Docker image. Is Docker supported in Amazon CloudShell?","No, Docker is not supported.","Yes, Docker is pre-installed.","Docker can be installed manually.","Docker is only supported for specific AWS services.","Docker is not pre-installed or directly supported for building images within the standard Amazon CloudShell environment."
"What is the primary purpose of the pre-installed `saml2aws` tool in Amazon CloudShell?","Authenticating to AWS using SAML 2.0 federation.","Managing AWS Service Catalog products.","Deploying AWS Step Functions.","Interacting with Amazon SageMaker endpoints.","The `saml2aws` tool facilitates authentication to AWS using SAML 2.0 federation, often used in enterprise environments."
"A developer needs to access files in their persistent storage from a different AWS Region's Amazon CloudShell session. Is this possible?","No, persistent storage is Region-specific.","Yes, persistent storage is global.","Only if the Regions are in the same AWS account.","Only if the Regions are connected via VPC peering.","Amazon CloudShell's persistent storage is tied to the specific AWS Region where the session is launched."
"What is the primary benefit of using Amazon CloudShell for quick scripting tasks?","Provides a readily available environment without local setup.","Offers advanced debugging capabilities.","Provides a graphical interface for script creation.","Enables offline script execution.","CloudShell's immediate availability and pre-configured environment make it ideal for quick scripting and automation tasks."
"Which of the following is a valid method for transferring files from Amazon CloudShell to an EC2 instance?","Using the scp command if the EC2 instance is accessible.","Using the Actions menu to transfer directly.","Using the aws ec2 cp command.","Using an FTP client.","If the EC2 instance is network accessible from CloudShell, the `scp` command can be used to transfer files."
"What is the primary purpose of the pre-installed `session-manager-plugin` in Amazon CloudShell?","Connecting to EC2 instances via AWS Systems Manager Session Manager.","Managing database sessions.","Connecting to Amazon RDS instances.","Managing network sessions.","The `session-manager-plugin` is required to connect to EC2 instances using AWS Systems Manager Session Manager from the command line."
"A developer is using Amazon CloudShell and needs to increase the available memory for a command. Is this possible?","No, the memory allocation is fixed.","Yes, memory can be adjusted via the Actions menu.","Memory can be increased by upgrading the CloudShell environment.","Memory is automatically scaled based on usage.","The memory allocation for an Amazon CloudShell session is fixed and cannot be adjusted by the user."
"What is the primary advantage of Amazon CloudShell for new AWS users?","Provides a simple and accessible way to interact with AWS services via the command line.","Offers a graphical interface for all AWS services.","Provides a free tier with unlimited usage.","Offers offline access to learning resources.","CloudShell lowers the barrier to entry for using the AWS CLI and interacting with services via the command line for new users."
"Which of the following is a security consideration related to the persistent storage in Amazon CloudShell?","Ensuring sensitive data is not stored unencrypted.","Sharing the persistent storage with other users.","Making the persistent storage publicly accessible.","Disabling versioning on the persistent storage.","Sensitive data stored in the persistent home directory should be encrypted or avoided altogether due to the potential for unauthorized access if the AWS account is compromised."
"A developer needs to install a specific version of Node.js in their Amazon CloudShell environment. How can they achieve this?","Using a Node.js version manager like nvm installed in their home directory.","They cannot install different Node.js versions.","Requesting AWS support to change the Node.js version.","Using the yum package manager.","Installing a Node.js version manager like nvm in the persistent home directory allows users to manage and switch between different Node.js versions.""What is the primary purpose of AWS CloudShell?","To provide a browser-based shell environment for managing AWS resources.","To host static websites.","To run containerised applications.","To store and manage large datasets.","AWS CloudShell is designed to offer a convenient, browser-based shell for interacting with AWS services directly from the AWS Management Console."
"Which programming languages are pre-installed and readily available in AWS CloudShell?","Python, Node.js, and Go","Java, C++, and .NET","Ruby, PHP, and Perl","Scala, Clojure, and Haskell","CloudShell environments come with common development and scripting tools pre-installed, including Python, Node.js, and Go."
"What type of storage is associated with AWS CloudShell?","Persistent storage in your home directory.","Ephemeral storage that is deleted when the session ends.","Storage in an S3 bucket.","Storage in an EBS volume.","CloudShell provides persistent storage within your home directory, allowing you to store scripts, tools, and configuration files between sessions."
"How is AWS CloudShell accessed?","Through the AWS Management Console.","Via a standalone desktop application.","Using a dedicated SSH client.","By installing a browser extension.","CloudShell is directly accessible from the AWS Management Console, making it easy to launch and use without additional software installation."
"Which AWS authentication method does AWS CloudShell automatically use?","The IAM role assigned to the CloudShell environment.","Username and password.","Multi-factor authentication.","Access keys stored in environment variables.","CloudShell automatically authenticates using the IAM role assigned to the CloudShell environment, simplifying the process of interacting with AWS services."
"What happens to files stored in your AWS CloudShell home directory when your session ends?","They persist for future sessions.","They are automatically deleted.","They are moved to an S3 bucket.","They are archived in Glacier.","Data stored in the \\$HOME directory persists between sessions."
"What is the default shell environment in AWS CloudShell?","Bash","Zsh","PowerShell","Fish","Bash is the default shell environment in AWS CloudShell."
"Which AWS CLI version is typically pre-installed in AWS CloudShell?","The latest version, or a relatively recent version.","A very old, stable version.","A specific version based on your AWS account.","No version is pre-installed; you must install it manually.","AWS CloudShell comes with the latest version of the AWS CLI pre-installed to let you start managing services."
"Which of the following actions can you perform using AWS CloudShell?","Deploy infrastructure using AWS CloudFormation.","Monitor network traffic in real-time.","Create and manage IAM users.","Analyse large datasets with Apache Spark.","CloudShell allows you to manage infrastructure as code with CloudFormation directly from the shell."
"What is the primary security benefit of using AWS CloudShell compared to managing AWS resources from your local machine?","CloudShell eliminates the need to manage AWS credentials on your local machine.","CloudShell uses stronger encryption than your local machine.","CloudShell is isolated from the internet.","CloudShell automatically scans for vulnerabilities.","CloudShell's IAM role removes the need to manage long term credentials on your local machine, and reduces the risk of accidental credential exposure."
"What is a limitation of AWS CloudShell in terms of execution time?","Sessions will automatically terminate after a period of inactivity.","Scripts can only run for a maximum of 5 minutes.","You cannot execute long-running processes.","CloudShell restricts the number of concurrent processes.","CloudShell sessions automatically terminate after a period of inactivity. Ensure to re-establish the connection if needed."
"How can you transfer files to and from AWS CloudShell?","Using the upload/download file action in the CloudShell interface.","Directly using SCP or SFTP from your local machine.","By mounting an external drive.","Through Bluetooth file transfer.","AWS CloudShell allows you to upload files from your local machine or download files from the CloudShell environment using the upload/download file action."
"What is the maximum amount of persistent storage available in the AWS CloudShell home directory by default?","1 GB","5 GB","10 GB","500 MB","By default, AWS CloudShell provides 1 GB of persistent storage in your home directory."
"Which of these AWS services is directly integrated and easily accessible from AWS CloudShell?","AWS Systems Manager","Amazon EC2","Amazon S3","Amazon DynamoDB","AWS Systems Manager is integrated, allowing easy access to SSM tools like Session Manager, Patch Manager and others."
"How can you extend the functionality of AWS CloudShell?","By installing custom software packages using package managers like `apt` or `yum`.","By connecting to an external IDE.","By using browser extensions.","By installing plugins from the AWS Marketplace.","You can customise the environment by installing custom software packages using package managers within the shell."
"You want to use `git` in AWS CloudShell. Is it pre-installed?","Yes, `git` is pre-installed.","No, you must install it manually.","`git` is only available for certain AWS regions.","`git` functionality is provided by a different tool.","`git` comes pre-installed in AWS CloudShell, enabling version control operations out of the box."
"What is the recommended way to manage AWS credentials when using the AWS CLI in AWS CloudShell?","Rely on the IAM role assigned to the CloudShell environment.","Manually configure AWS credentials using `aws configure`.","Store AWS credentials in environment variables.","Store AWS credentials in a configuration file in your home directory.","CloudShell automatically authenticates via the IAM role."
"If you are using AWS CloudShell and need to assume a different IAM role, how would you typically accomplish this?","Use the AWS CLI `aws sts assume-role` command.","Modify the CloudShell IAM role in the IAM console.","Reconfigure the AWS CLI with the new role's credentials.","Restart the CloudShell session with the new role.","The \\`aws sts assume-role\\` command is the standard method for assuming a different IAM role within an AWS environment."
"Which of the following is NOT a use case for AWS CloudShell?","Running production workloads.","Quickly prototyping infrastructure changes.","Troubleshooting issues with AWS resources.","Managing AWS resources from a web browser.","CloudShell is not intended for running production workloads due to its ephemeral nature and resource limitations."
"Can you use AWS CloudShell to connect to EC2 instances using SSH?","Yes, you can use CloudShell as an SSH client.","No, CloudShell does not support SSH connections.","SSH connections are only allowed to instances in the same VPC.","You need to install an SSH client first.","CloudShell can be used as an SSH client, allowing you to connect to EC2 instances or other SSH-enabled services."
"What type of compute environment does AWS CloudShell run on?","A managed Amazon Linux 2 environment.","A serverless AWS Lambda function.","An Amazon EC2 instance that you manage.","A container managed by Amazon ECS.","AWS CloudShell runs on a managed Amazon Linux 2 environment, providing a consistent and pre-configured operating system."
"What happens when the AWS CloudShell session times out due to inactivity?","Your shell session terminates, but your files persist.","All files in your home directory are deleted.","Your AWS account is suspended.","Your EC2 instances are automatically stopped.","The shell session terminates but the \\$HOME directory persists."
"If you have a script that requires elevated privileges (sudo), can you run it directly in AWS CloudShell?","Yes, you can use `sudo` without any restrictions.","`sudo` is disabled in AWS CloudShell for security reasons.","You must configure specific permissions for `sudo` access.","You can only use `sudo` for specific AWS commands.","You can use \\`sudo\\` within CloudShell."
"Can you install Docker in AWS CloudShell?","Yes, Docker is pre-installed.","Yes, but you need to install it manually.","No, Docker cannot be installed in AWS CloudShell.","Docker installation requires specific AWS permissions.","While you can install Docker, it may not be the best use case and you should consider the persistent storage limitation"
"Which tool can you use within AWS CloudShell to explore AWS service APIs and build AWS CLI commands interactively?","AWS CLI Autocompletion and Documentation","AWS CloudWatch Logs","AWS CloudTrail Insights","AWS X-Ray","AWS CLI Autocompletion and Documentation provides a convenient way to explore AWS service APIs and build CLI commands interactively within CloudShell."
"What AWS region does CloudShell operate in by default?","The region selected in the AWS Management Console.","us-east-1 (North Virginia).","The region closest to your physical location.","A dedicated CloudShell region.","AWS CloudShell operates in the region selected in the AWS Management Console."
"How can you use AWS CloudShell to interact with resources in a different AWS account?","By assuming a role in the target account using the AWS CLI.","By configuring cross-account access in the IAM console.","By creating a new CloudShell environment in the target account.","By using a shared access key.","You would typically assume a role in the target account, which enables you to access resources using temporary credentials within that account."
"You want to automate tasks using AWS CloudShell. What is a common way to achieve this?","By writing shell scripts and using cron jobs.","By creating AWS Lambda functions and triggering them from CloudShell.","By using AWS Step Functions to orchestrate tasks.","By scheduling tasks directly in the AWS Management Console.","Shell scripts with cron is a common way to achieve task automation inside CloudShell"
"Which of these features is NOT available in AWS CloudShell?","Graphical user interface (GUI) support.","Access to AWS services via the AWS CLI.","Persistent storage for your home directory.","Pre-installed development tools.","CloudShell is a terminal interface without GUI support"
"What is the purpose of the AWS CloudShell 'Exit' command?","To terminate the current CloudShell session.","To close the AWS Management Console.","To restart the CloudShell environment.","To disconnect from the internet.","The \\'exit\\' command is used to terminate the current CloudShell session."
"How do you update the AWS CLI version in AWS CloudShell?","You don't need to, it's managed automatically.","By running `pip install --upgrade awscli`.","By downloading the latest version from the AWS website.","By using the `aws configure` command.","AWS CLI is managed automatically by AWS"
"What is the recommended way to manage sensitive data, such as API keys, when using AWS CloudShell?","Use AWS Secrets Manager and retrieve the secrets programmatically.","Store the secrets in environment variables.","Encrypt the secrets in your home directory.","Hardcode the secrets in your scripts.","Using AWS Secrets Manager helps to securely store and retrieve sensitive data without exposing it directly in your CloudShell environment."
"Can you run graphical applications (GUI) within AWS CloudShell?","No, AWS CloudShell is a purely command-line environment.","Yes, but you need to install a remote desktop client first.","Yes, all graphical applications are pre-installed.","Only specific AWS-provided graphical applications are supported.","CloudShell is a command-line environment and does not support graphical applications."
"What is the most direct way to increase the amount of persistent storage available for your AWS CloudShell home directory?","You cannot increase the storage; 1GB is the maximum.","By upgrading your AWS support plan.","By requesting a storage increase through the AWS Management Console.","By purchasing additional storage from the AWS Marketplace.","The 1GB of persistent storage cannot be increased."
"Which of the following commands is used to check the version of the AWS CLI installed in AWS CloudShell?","aws --version","aws help","aws version","cli version","The command \\`aws --version\\` displays the version information of the AWS CLI."
"How can you view the logs generated by your scripts running in AWS CloudShell?","By redirecting the output to a file and viewing the file contents.","Logs are automatically sent to Amazon CloudWatch Logs.","Logs are only visible during the session and cannot be saved.","By using the AWS CloudTrail console.","You would redirect the standard output or standard error to a file and then view the file content. CloudShell does not automatically send logs to CloudWatch"
"What is the main advantage of using AWS CloudShell over a locally installed terminal for managing AWS resources?","Simplified credential management.","Faster execution speed.","Greater storage capacity.","More advanced debugging tools.","Simplified credential management because CloudShell uses the role assigned to the shell"
"Can you use AWS CloudShell to run commands that modify resources in other AWS regions?","Yes, by configuring the AWS CLI to use the desired region.","No, AWS CloudShell is limited to the region in which it is launched.","You need to create separate CloudShell environments for each region.","You must use the AWS Management Console instead.","You can configure the CLI to manage resource in another region."
"If you want to use a custom tool that is not pre-installed in AWS CloudShell, what is the typical approach?","Install the tool using a package manager like `apt` or `yum`.","Upload the tool as a binary file and execute it.","Use AWS Lambda layers to provide the tool.","Request AWS to pre-install the tool in CloudShell.","Installing with package managers is a common way to install custom tools."
"What happens to your AWS CloudShell environment if you switch to a different AWS region in the AWS Management Console?","A new CloudShell environment is created in the new region.","The existing CloudShell environment is automatically migrated to the new region.","The CloudShell session remains in the original region.","The CloudShell session is terminated.","A new CloudShell environment is created in the new AWS Region"
"Which command is commonly used to list all the S3 buckets in your AWS account from AWS CloudShell?","aws s3 ls","s3 list-buckets","aws s3api list-buckets","ls s3://","`aws s3 ls` is the correct command to list buckets."
"What type of instances are available in AWS CloudShell?","AWS CloudShell uses a managed Amazon Linux 2 environment.","EC2 instances are directly available to the user.","A managed Windows Server.","A container running Ubuntu Linux.","AWS CloudShell uses a managed Amazon Linux 2 environment"
"Can you run interactive commands or scripts which require user input in AWS CloudShell?","Yes, AWS CloudShell fully supports interactive commands and scripts.","Interactive commands are limited to single-line inputs.","AWS CloudShell only supports non-interactive scripts.","You must use a remote desktop client for interactive commands.","Interactive commands are supported by CloudShell."
"Which service allows you to securely store and manage secrets like API keys and passwords in AWS?","AWS Secrets Manager","AWS IAM","AWS KMS","AWS Certificate Manager","AWS Secrets Manager helps you protect secrets needed to access your applications, services, and IT resources."
"How can you download the AWS CloudShell history to your local machine?","By copying the contents of the .bash_history file.","CloudShell history is automatically downloaded.","You must use a specific AWS CLI command to export the history.","Downloading CloudShell history is not supported.","You can copy the history from .bash_history."
"What is the maximum amount of concurrent AWS CloudShell sessions you can have open at one time?","One per region.","Five globally.","Unlimited.","One globally.","You can only have one active session at a time."
"If you want to persist data beyond the 1GB limit of the \$HOME directory in AWS CloudShell, what is the recommended approach?","Store the data in an Amazon S3 bucket.","Use an external hard drive.","Increase the storage of the CloudShell environment.","Store the data in an EBS volume.","S3 is the recommended way to persist the data beyond the limitation of the HOME directory."
"What is the primary purpose of AWS CloudShell?","To provide a browser-based shell for managing AWS resources.","To host static websites.","To store and retrieve large amounts of data.","To run machine learning algorithms.","CloudShell offers a pre-configured, browser-based shell accessible from the AWS Management Console, simplifying AWS resource management via the command line."
"Which programming languages are pre-installed and readily available within AWS CloudShell?","Python and Node.js","Java and C++","Ruby and Go","C# and PHP","AWS CloudShell comes pre-configured with Python and Node.js, allowing users to quickly begin scripting and development tasks without needing to install these languages manually."
"What type of AWS credentials are automatically configured when you launch AWS CloudShell?","Temporary credentials associated with your IAM user or role.","Long-term IAM user credentials.","Root user credentials.","Service-linked role credentials.","CloudShell automatically uses temporary credentials derived from your IAM user or role, providing secure access to AWS services without needing to manually configure credentials."
"What is the default home directory size allocated to each AWS CloudShell user?","5 GB","1 GB","10 GB","500 MB","Each AWS CloudShell user gets 5 GB of persistent storage in their home directory for storing scripts, tools, and other files."
"Can you directly install arbitrary software packages using `sudo apt-get` or `yum` within AWS CloudShell?","No, you cannot install arbitrary packages with elevated privileges.","Yes, you have full root access.","Yes, but only for certain approved packages.","Yes, but you must contact AWS support first.","AWS CloudShell does not grant full root privileges, preventing the installation of arbitrary packages using tools like `sudo apt-get` or `yum`. This enhances security and stability."
"Which of these AWS services is NOT directly integrated with AWS CloudShell for managing resources?","Amazon EC2 Auto Scaling","Amazon S3","Amazon EC2","AWS Lambda","While CloudShell can be used to manage and interact with all listed services via the AWS CLI, it doesn't have specific direct integration with Amazon EC2 Auto Scaling for resource management functions."
"What happens to the files stored in your AWS CloudShell home directory when your session times out?","Files are preserved in the persistent storage.","Files are permanently deleted.","Files are moved to a temporary directory.","Files are encrypted with a different key.","The files in your home directory are stored persistently across sessions and are not deleted when a session times out."
"How can you transfer files from your local machine to AWS CloudShell?","Using the upload file action in the CloudShell interface.","Using FTP directly from CloudShell.","Using a shared Amazon S3 bucket.","Using a direct SSH connection.","CloudShell has a built-in upload file action to transfer files from your local machine to the CloudShell environment."
"Can you use AWS CloudShell to interact with resources in a different AWS region than the one CloudShell is running in?","Yes, by configuring the AWS CLI with the desired region.","No, CloudShell is limited to the region it is launched in.","Yes, but only if you have a VPN connection to the other region.","Yes, but only for S3 buckets.","You can configure the AWS CLI within CloudShell to interact with resources in other AWS regions, providing flexibility in managing resources across different regions."
"What is the default shell environment in AWS CloudShell?","Bash","Zsh","Fish","PowerShell","AWS CloudShell defaults to using the Bash shell environment, providing a familiar command-line experience for many users."
"How is authentication handled when using the AWS CLI within AWS CloudShell?","Authentication is automatically handled using the credentials of the IAM user or role that launched CloudShell.","You must manually configure AWS CLI credentials using `aws configure`.","You must provide your AWS access key and secret key each time you launch CloudShell.","You must use MFA every time to authenticate.","AWS CloudShell automatically handles authentication by using the temporary credentials associated with your IAM user or role."
"What is the recommended way to update the AWS CLI version within AWS CloudShell?","Use the pre-installed `pip` package manager to update the AWS CLI.","You cannot update the AWS CLI version in CloudShell.","Download the latest version from the AWS website and install it manually.","Use the `awscli` command to update the AWS CLI.","The recommended way to update the AWS CLI in CloudShell is using the pre-installed `pip` package manager."
"Can AWS CloudShell be used to run graphical applications that require a graphical user interface (GUI)?","No, AWS CloudShell is a purely command-line environment.","Yes, but you need to install a VNC server.","Yes, but only if you are using a Linux-based operating system.","Yes, but only for certain AWS services.","AWS CloudShell is designed as a command-line environment and does not support running graphical applications with a GUI."
"Which of the following is NOT a typical use case for AWS CloudShell?","Developing and testing serverless applications.","Managing infrastructure as code (IaC) with tools like Terraform.","Running long-running batch processing jobs.","Interacting with AWS services via the AWS CLI.","CloudShell is not intended for long-running batch processing jobs due to its session limits and resource constraints."
"How do you access the persistent storage associated with your AWS CloudShell session?","It is automatically mounted as your home directory (/home/<USER>","You need to manually mount the storage using the `mount` command.","You need to create an EBS volume and attach it to CloudShell.","Persistent storage is only accessible via the AWS Management Console.","The 5GB of persistent storage is automatically mounted as the home directory (/home/<USER>"
"What happens to your CloudShell environment if you exceed the allocated memory or CPU resources?","The session will be terminated.","The session will slow down but continue to run.","The session will be suspended temporarily.","The session will automatically scale up resources.","If you exceed the allocated memory or CPU resources in CloudShell, the session will be terminated to ensure the stability of the environment."
"What is the purpose of the 'Actions' menu in the AWS CloudShell interface?","To manage files, upload data, and restart the shell.","To configure security settings.","To monitor resource utilisation.","To access AWS support documentation.","The 'Actions' menu provides options for managing files, uploading data, and restarting the CloudShell environment."
"How does AWS CloudShell contribute to security best practices when managing AWS resources?","By automatically using temporary credentials, reducing the risk of exposed long-term credentials.","By providing full root access to the underlying operating system.","By storing AWS access keys directly in the CloudShell environment.","By disabling all security controls.","CloudShell enhances security by automatically using temporary credentials, which are more secure than long-term credentials that could be accidentally exposed."
"Which of the following is a limitation of AWS CloudShell?","Limited session duration.","Inability to access the internet.","Inability to use the AWS CLI.","Inability to use Python.","AWS CloudShell has a limited session duration to ensure efficient resource utilisation and security."
"Which AWS service is tightly integrated with AWS CloudShell, allowing you to browse and manage files directly from the console?","AWS S3","AWS Lambda","AWS CloudWatch","AWS IAM","Although CloudShell can interact with S3, it does not provide a direct file-browsing integration with the service."
"What command do you typically use within AWS CloudShell to interact with AWS services programmatically?","aws","ec2","s3","iam","The `aws` command is the primary command-line interface used to interact with AWS services programmatically."
"When would you choose to use AWS CloudShell over a local terminal configured with the AWS CLI?","When you need a pre-configured environment with AWS credentials readily available.","When you need to run CPU-intensive tasks.","When you need to access resources outside of AWS.","When you need to install custom software packages.","CloudShell provides a convenient, pre-configured environment with AWS credentials, simplifying the management of AWS resources."
"What happens if you close your browser window while an AWS CloudShell session is active?","The session will continue to run for a limited time, depending on the inactivity timeout.","The session will be immediately terminated.","The session will be paused and can be resumed later.","The session will automatically save its state to a snapshot.","The session will continue to run for a limited time, depending on the inactivity timeout."
"How can you access the AWS CloudShell service?","From the AWS Management Console.","By installing a CloudShell client on your local machine.","By using an SSH client to connect to a CloudShell endpoint.","By using a dedicated CloudShell mobile app.","AWS CloudShell is accessible directly from the AWS Management Console, making it easy to launch and use without additional software installations."
"You need to quickly test a command against your AWS account without configuring credentials on your local machine. Which service is best suited for this?","AWS CloudShell","AWS CodeCommit","AWS CodeBuild","AWS Cloud9","AWS CloudShell is ideal for quickly testing commands against your AWS account without needing to configure credentials locally."
"How can you increase the storage available to your AWS CloudShell environment if 5 GB is insufficient?","You cannot increase the storage; 5 GB is the maximum.","By attaching an EBS volume to the CloudShell instance.","By contacting AWS support to request additional storage.","By creating a symbolic link to an S3 bucket.","The storage for CloudShell is fixed at 5GB and cannot be increased."
"You are using AWS CloudShell and want to edit a file. What is a common text editor available within the environment?","vim","Notepad","Wordpad","TextEdit","Vim is a widely used text editor that is pre-installed in AWS CloudShell."
"Which of the following actions within AWS CloudShell will NOT trigger a cost in your AWS account?","Using the AWS CLI to list S3 buckets.","Creating an EC2 instance.","Uploading a file to S3.","Downloading a file from S3.","Using the AWS CLI within CloudShell does not directly incur costs unless you are interacting with other AWS resources that have associated charges, such as creating EC2 instances or using S3 storage and data transfer."
"What is a common use case for AWS CloudShell regarding Infrastructure as Code (IaC)?","Deploying infrastructure using Terraform or AWS CloudFormation.","Monitoring CPU usage of EC2 instances.","Creating backups of RDS databases.","Configuring IAM roles and policies.","CloudShell simplifies IaC workflows by providing a pre-configured environment for deploying infrastructure using tools like Terraform or AWS CloudFormation."
"Can you use AWS CloudShell to run Docker containers?","No, Docker is not supported in AWS CloudShell.","Yes, but you need to install Docker first.","Yes, Docker is pre-installed in AWS CloudShell.","Yes, but only for certain AWS services.","Docker is not supported in AWS CloudShell due to security and resource constraints."
"How can you retrieve the AWS account ID within AWS CloudShell using the AWS CLI?","aws sts get-caller-identity --output text --query 'Account'","aws account describe","aws configure get account_id","aws iam list-account-aliases","The command `aws sts get-caller-identity --output text --query 'Account'` retrieves the AWS account ID associated with the current CloudShell session."
"What does AWS CloudShell use to allow access to other AWS resources?","Temporary credentials associated with the IAM role or user","Permanent IAM user credentials","The AWS root account credentials","SSH keys","CloudShell uses temporary credentials based on the IAM user or role to provide secure access to other AWS resources without needing long-term credentials."
"What happens to the software and configurations you install in AWS CloudShell after your session ends?","They are typically saved in the persistent storage area.","They are removed when the session ends.","They are automatically backed up to S3.","They are converted to an AWS CloudFormation template.","Most changes made outside the home directory will not persist between sessions."
"Which of the following is an advantage of using AWS CloudShell for managing your AWS infrastructure?","Eliminates the need to manage a local development environment.","Provides unlimited CPU and memory resources.","Allows you to run graphical applications.","Comes with a pre-configured database server.","AWS CloudShell eliminates the need to manage a local development environment by providing a pre-configured browser-based shell."
"How can you list all the files and directories in your current AWS CloudShell directory?","ls -l","dir","list","show files","`ls -l` is a standard Linux command used to list files and directories with details in the current directory."
"You are working with AWS CloudShell and want to view the contents of a file. What command can you use?","cat filename","show filename","view filename","display filename","The `cat filename` command displays the contents of a file."
"You need to securely store sensitive information, such as API keys, while using AWS CloudShell. What is the recommended approach?","Use AWS Secrets Manager and access the secrets from CloudShell.","Store the keys directly in the CloudShell environment variables.","Encrypt the keys and store them in your home directory.","Store the keys in a public GitHub repository.","Using AWS Secrets Manager allows you to securely store and manage sensitive information, which can then be accessed from CloudShell."
"What is the maximum inactivity period for an AWS CloudShell session before it is automatically terminated?","20 minutes","60 minutes","8 hours","24 hours","AWS CloudShell sessions are automatically terminated after 20 minutes of inactivity."
"How do you create a new directory in AWS CloudShell?","mkdir directory_name","new directory directory_name","create directory_name","make directory directory_name","The `mkdir directory_name` command creates a new directory with the specified name."
"You have made changes to a file in AWS CloudShell and want to save those changes. What should you do?","Save the file using a text editor like vim.","Files are automatically saved.","Use the 'save' command in CloudShell.","CloudShell automatically creates snapshots of your changes.","To save changes in AWS CloudShell, you must save the file using a text editor like Vim or Nano, changes will be then be persisted across sessions."
"What tool is pre-installed on AWS CloudShell to allow you to deploy AWS infrastructure as code?","AWS CloudFormation","AWS CodeDeploy","AWS CodePipeline","AWS CodeBuild","Although you can install other IaC tools, AWS CloudFormation is readily available for deploying infrastructure as code."
"You are working on a project that requires you to install a specific version of Python. Can you install multiple Python versions in AWS CloudShell?","Yes, you can install multiple Python versions using a version manager like `pyenv`.","No, you are limited to the pre-installed Python version.","Yes, but only if you use Docker containers.","Yes, but only if you contact AWS support first.","You can manage multiple Python versions using a version manager like `pyenv` in CloudShell."
"What is the best way to share code between your local machine and AWS CloudShell?","Use the built-in upload/download file actions.","Use AWS CodeCommit.","Use AWS CodePipeline.","Use AWS CodeBuild.","The built-in upload/download file actions are the easiest way to share small code snippets."
"You suspect your AWS CloudShell environment is not working correctly. What's a basic troubleshooting step you can take?","Restart the CloudShell session.","Reinstall AWS CloudShell.","Contact AWS Support immediately.","Delete and recreate your IAM user.","Restarting the CloudShell session is a common first step to resolve environment-related issues."
"Can you run commands in AWS CloudShell that affect resources in multiple AWS accounts?","Yes, but you need to configure cross-account IAM roles.","No, CloudShell is limited to the account it is launched in.","Yes, but only for certain AWS services.","Yes, but you must use the AWS Management Console.","You can interact with resources in other AWS accounts by configuring cross-account IAM roles within CloudShell."
"What is one way to install the AWS CLI in AWS CloudShell if it has somehow been removed?","Use the pre-installed Python pip package manager to install it.","You cannot re-install the AWS CLI in CloudShell.","Download the AWS CLI from the AWS website and install it manually.","Use the CloudShell installer script.","The AWS CLI can be reinstalled using the pre-installed `pip` package manager."
"When using AWS CloudShell, how are your commands executed in relation to the AWS infrastructure?","Commands are executed directly within the AWS infrastructure.","Commands are executed locally within the CloudShell environment and then transmitted to AWS.","Commands are executed in a separate, isolated environment.","Commands are executed on your local machine after being downloaded from AWS.","Commands are executed locally within the CloudShell environment, and then interact with the AWS infrastructure by using the AWS CLI with your credentials."
"You want to use AWS CloudShell for a longer period than the default session allows. What should you do?","Ensure activity within the session to prevent automatic termination.","Request a session extension from AWS Support.","Increase the inactivity timeout in the CloudShell settings.","Use a different AWS service for long-running tasks.","The best practice is to ensure there is ongoing activity in the session to prevent auto termination and/or use another service designed for longer runtimes."
"When working in AWS CloudShell, you need to quickly switch between different AWS accounts. What's the recommended method?","Using AWS STS to assume roles from other accounts.","Logging in with the root user credentials of each account.","Creating separate CloudShell environments for each account.","Using IAM user credentials directly in the CloudShell environment.","Using AWS STS to assume roles in other accounts allows you to temporarily assume the permissions of another IAM role, which grants you access to resources in that account."