"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS CodeArtifact, what is the primary purpose of a repository?","To store and manage software packages and dependencies","To define build pipelines","To manage AWS IAM roles","To monitor application performance","Repositories in CodeArtifact are the central location for storing and managing software packages (like npm, Maven, PyPI packages) and their dependencies."
"What type of access control can you use with AWS CodeArtifact repositories?","IAM policies","AWS Organisations policies","AWS Shield","AWS WAF","CodeArtifact integrates with IAM, allowing you to use IAM policies to control access to repositories and packages."
"Which of the following package formats is supported by AWS CodeArtifact?","npm","JAR","RPM","ISO","AWS CodeArtifact natively supports several package formats, including npm for Node.js packages."
"What is the purpose of an upstream repository in AWS CodeArtifact?","To allow a CodeArtifact repository to inherit packages from another repository","To limit access to a specific set of packages","To create multiple versions of a single package","To trigger automated builds when a new package is published","Upstream repositories enable a CodeArtifact repository to search for and retrieve packages from another repository if they are not found locally."
"Which AWS service can be used to build and publish packages directly to AWS CodeArtifact?","AWS CodeBuild","AWS CodePipeline","AWS CodeDeploy","AWS CodeStar","AWS CodeBuild can be configured to build software packages and then publish them directly to an AWS CodeArtifact repository."
"What is the benefit of using AWS CodeArtifact over public package repositories like npm or Maven Central?","Enhanced security and control over dependencies","Unlimited storage capacity","Faster download speeds in all regions","Automatic vulnerability scanning for all packages","CodeArtifact provides enhanced security and control by allowing you to store and manage packages within your own AWS account, reducing the risk of supply chain attacks."
"What is the 'domain' in AWS CodeArtifact?","A logical grouping of repositories","A virtual private cloud","An IAM role","A deployment pipeline","A domain in CodeArtifact is a container for repositories that share the same permission policies and settings."
"Which of the following AWS CLI commands is used to publish a package to an AWS CodeArtifact repository?","aws codeartifact publish-package","aws codeartifact put-package","aws codeartifact upload-package","aws codeartifact create-package-version","The `aws codeartifact publish-package` command is the correct command used to publish a package to a CodeArtifact repository."
"What does the token obtained from 'aws codeartifact get-authorization-token' provide?","Temporary credentials for accessing the CodeArtifact repository","The ARN of the CodeArtifact domain","The encryption key for CodeArtifact storage","The AWS account ID","The authorization token provides temporary credentials (including a username and password) that are needed to access and authenticate with a CodeArtifact repository."
"In AWS CodeArtifact, what does 'Package Origin' refer to?","The source repository from which a package was originally published","The AWS region where the package is stored","The author of the package","The size of the package","Package Origin refers to the original source from which a package was initially published, allowing you to track the lineage of your dependencies."
"You are working with multiple repositories in AWS CodeArtifact. How do you ensure a consistent set of dependencies across these repositories?","Using upstream repositories","Setting global repository policies","Enabling cross-account access","Enforcing tagging policies","Upstream repositories allow repositories to share and resolve dependencies from one another, ensuring consistency across your development environments."
"Which of the following is a benefit of using AWS CodeArtifact with AWS CodePipeline?","Automated dependency management during build and deployment","Automated cost optimisation","Simplified IAM role management","Automatic scaling of resources","CodeArtifact allows CodePipeline to automatically retrieve dependencies during the build and deployment process, ensuring that the correct versions are used and improving pipeline reliability."
"What is the difference between a 'domain' and a 'repository' in AWS CodeArtifact?","A domain is a container for repositories, providing a scope for permissions and settings.","A repository is a container for domains, providing a scope for permissions and settings.","A domain is used for public packages and a repository is used for private packages.","A repository is used for build artifacts and a domain is used for code.","A domain is a higher-level construct that groups repositories, enabling you to manage permissions and configurations across multiple repositories more effectively."
"Which of the following actions is NOT directly supported by AWS CodeArtifact?","Scanning packages for vulnerabilities","Proxying requests to public package managers","Storing and managing software dependencies","Publishing software packages","While you can integrate CodeArtifact with vulnerability scanning tools, CodeArtifact itself doesn't directly offer vulnerability scanning as a built-in feature."
"How can you provide access to AWS CodeArtifact repositories to developers in a different AWS account?","Using resource-based policies in CodeArtifact","Creating a cross-account IAM role and granting access","Sharing the CodeArtifact domain with the other account","Granting the other account full access to your AWS account","Resource-based policies in CodeArtifact allows to grant cross-account access to other accounts that are not in the same organisation."
"What is the primary use case for the AWS CodeArtifact 'copy' command?","To copy packages between repositories within a domain","To copy entire CodeArtifact domains","To copy artifacts from S3 to CodeArtifact","To copy CodeArtifact configuration settings","The `copy` command in CodeArtifact is used to copy specific packages or package versions from one repository to another within the same domain, enabling you to manage and promote packages through different stages of your development process."
"Which of these features can help you manage the versions of packages stored in AWS CodeArtifact?","Immutable package versions","Automatic versioning","Dependency locking","Scheduled package deletion","AWS CodeArtifact uses immutable package versions so they cannot be overwritten after being published."
"How can you ensure that only approved packages are used in your builds when using AWS CodeArtifact?","By using repository policies to control which packages can be accessed","By creating separate CodeArtifact domains for development and production","By configuring CodeArtifact to only download packages from a specific source","By enabling encryption at rest","Repository policies allow you to define rules that control which packages can be accessed, based on criteria such as the package name, version, or origin."
"What is the recommended method for authenticating with AWS CodeArtifact from a CI/CD pipeline?","Using the AWS CLI to obtain an authorization token","Storing AWS credentials in the pipeline configuration","Using a static username and password","Disabling authentication","The recommended method is to use the AWS CLI to obtain an authorization token, which provides temporary credentials for accessing the CodeArtifact repository. This approach avoids the need to store long-term credentials in the pipeline."
"What does AWS CodeArtifact's integration with AWS CloudTrail provide?","Auditing of all actions performed in CodeArtifact","Real-time monitoring of package downloads","Automated vulnerability scanning","Cost optimisation recommendations","Integration with CloudTrail enables you to audit all actions performed in CodeArtifact, providing a detailed audit trail for security and compliance purposes."
"How does AWS CodeArtifact help mitigate supply chain security risks?","By providing control over the packages used in your applications","By automatically removing unused packages","By encrypting packages at rest and in transit","By providing a built-in firewall","CodeArtifact gives you control over your dependencies by storing them in a private repository, reducing the risk of using compromised or malicious packages from public repositories."
"You are using AWS CodeArtifact with npm. What file must be configured to point to your CodeArtifact repository?","The .npmrc file","The package.json file","The serverless.yml file","The pom.xml file","For npm, you need to configure the `.npmrc` file to specify the registry URL of your CodeArtifact repository. This tells npm where to download and publish packages."
"Which of the following is an advantage of using AWS CodeArtifact with Maven?","Centralised management of Maven dependencies","Faster build times","Automatic generation of Maven projects","Simplified deployment process","AWS CodeArtifact provides a central repository for storing and managing Maven dependencies, making it easier to share and reuse dependencies across projects."
"What is the purpose of the AWS CodeArtifact 'get-repository-endpoint' command?","To retrieve the URL for accessing the repository","To retrieve the IAM role associated with the repository","To retrieve the encryption key for the repository","To retrieve the list of upstream repositories","The `get-repository-endpoint` command returns the URL that you need to configure your package manager (e.g., npm, Maven, pip) to point to your CodeArtifact repository."
"How does AWS CodeArtifact simplify the process of sharing packages between different teams within an organisation?","By providing a central repository with controlled access","By automatically distributing packages to all teams","By providing a built-in messaging system","By providing a shared development environment","CodeArtifact provides a centralised repository that allows different teams to easily share and reuse packages, while also providing controlled access to ensure that only authorised users can access specific packages."
"What feature in AWS CodeArtifact allows you to ensure that a package has not been tampered with after being published?","Package integrity checks","Automated vulnerability scanning","Encryption at rest","Access logging","CodeArtifact performs package integrity checks to ensure that packages have not been tampered with after being published, helping to prevent supply chain attacks."
"Which of the following is a key benefit of using AWS CodeArtifact for Python packages?","Simplified management of Python dependencies","Faster Python code execution","Automated virtual environment creation","Built-in support for Jupyter notebooks","CodeArtifact enables you to manage your Python dependencies more effectively by storing them in a private repository, reducing the risk of dependency conflicts and improving build reproducibility."
"Which AWS service can be integrated with AWS CodeArtifact to enforce coding standards and security best practices?","AWS CodeGuru","AWS Config","AWS Inspector","AWS Trusted Advisor","AWS CodeGuru can be integrated with CodeArtifact to automatically review code changes and identify potential security vulnerabilities and coding standard violations."
"What is the purpose of setting a 'publish' permission on an AWS CodeArtifact repository?","To allow users to publish packages to the repository","To allow users to download packages from the repository","To allow users to delete packages from the repository","To allow users to view the repository settings","Setting the 'publish' permission allows specified users or roles to publish new packages or update existing packages in the CodeArtifact repository."
"What is the relationship between AWS CodeArtifact and AWS Artifact?","AWS CodeArtifact stores software packages, while AWS Artifact provides compliance reports.","AWS CodeArtifact provides compliance reports, while AWS Artifact stores software packages.","AWS CodeArtifact is a replacement for AWS Artifact.","AWS Artifact is a replacement for AWS CodeArtifact.","AWS CodeArtifact is used for storing and managing software packages and dependencies. AWS Artifact is a service that provides on-demand access to AWS security and compliance reports."
"Which of the following is NOT a component of the AWS CodeArtifact service?","Domain","Repository","Package","Buildspec","The AWS CodeArtifact components are Domain, Repository and Packages. Buildspec files are used with AWS CodeBuild and is not a specific component of the service."
"How do you configure AWS CodeArtifact to use a custom domain name for your repository endpoints?","You cannot use custom domain names with CodeArtifact.","By configuring a Route 53 alias record.","By uploading a SSL certificate to CodeArtifact.","By creating an API Gateway in front of CodeArtifact.","Currently AWS CodeArtifact does not support custom domain names. The endpoint URLs are provided by AWS and cannot be changed to a custom domain name."
"When setting up AWS CodeArtifact with a CI/CD system, what security best practice should be followed regarding access tokens?","Use temporary credentials obtained through IAM roles.","Store the access token in plain text in the CI/CD configuration.","Share the same access token across all CI/CD pipelines.","Embed the access token directly in the source code.","Using temporary credentials obtained through IAM roles is the best security practice when setting up AWS CodeArtifact with a CI/CD system, as it minimises the risk of long-term credential exposure."
"What AWS service is best suited for managing secrets (such as CodeArtifact authorization tokens) used in your applications or CI/CD pipelines?","AWS Secrets Manager","AWS IAM","AWS KMS","AWS Certificate Manager","AWS Secrets Manager is the best choice for managing secrets like CodeArtifact authorization tokens, as it provides secure storage, rotation, and access control for sensitive information."
"How does AWS CodeArtifact help improve build reproducibility?","By providing a consistent and versioned source of dependencies","By automatically optimising build scripts","By automatically testing code","By providing real-time build status updates","By providing a consistent and versioned source of dependencies, AWS CodeArtifact ensures that builds are reproducible, as the exact same versions of dependencies are used each time."
"What role does the 'aws codeartifact login' command play in interacting with AWS CodeArtifact repositories?","Configures the local package manager (e.g., npm, pip) to use the CodeArtifact repository.","Creates a new CodeArtifact repository.","Downloads all packages from the CodeArtifact repository.","Deletes all packages from the CodeArtifact repository.","The `aws codeartifact login` command configures your local package manager (such as npm, pip, or Maven) to use the specified CodeArtifact repository as its primary source for packages, streamlining dependency resolution and package management."
"What is the purpose of setting up a 'connection' between a CodeArtifact repository and a source code repository (e.g., GitHub, CodeCommit)?","To automatically trigger builds when code changes are committed.","To enable CodeArtifact to directly modify source code.","To automatically back up source code to CodeArtifact.","To enforce code review processes.","A 'connection' between CodeArtifact and source code repository automatically triggers builds (via CodePipeline) when code changes are committed which simplifies the process of building and publishing packages whenever the code changes."
"How can you restrict access to specific packages within an AWS CodeArtifact repository?","By using repository policies with package name or version conditions","By creating separate repositories for each package","By encrypting individual packages","By disabling package downloads","You can use repository policies with package name or version conditions to control access to specific packages, allowing you to define granular permissions for your dependencies."
"Which AWS service can you use to monitor the usage and performance of your AWS CodeArtifact repositories?","AWS CloudWatch","AWS CloudTrail","AWS X-Ray","AWS Trusted Advisor","AWS CloudWatch can be used to monitor the usage and performance of your CodeArtifact repositories, providing insights into metrics such as request counts, latency, and error rates."
"What type of packages can be stored in CodeArtifact?","Software dependencies","Build artefacts","Application code","Configuration files","Software Dependencies and Build Artefacts"
"What is the difference between CodeCommit and CodeArtifact?","CodeCommit is source control while CodeArtifact is for dependency management","CodeArtifact is source control while CodeCommit is for dependency management","CodeCommit is for artifact storage while CodeArtifact is for source control","CodeArtifact can trigger pipelines while CodeCommit stores all code and binaries","CodeCommit is used for storing source code, while CodeArtifact is used for managing dependencies (software packages) and build artifacts."
"When creating an AWS CodeArtifact repository, what is one of the required configuration parameters?","The repository name","The storage capacity","The encryption key","The AWS region","The repository name is one of the required parameters when creating a CodeArtifact repository. You must provide a unique name for the repository."
"How does AWS CodeArtifact handle authentication for accessing packages?","IAM role-based authentication","Public, unauthenticated access","Username/password authentication","API key-based authentication","AWS CodeArtifact uses IAM (Identity and Access Management) role-based authentication, providing secure and controlled access to packages based on IAM policies and roles."
"Which of the following AWS CodeArtifact commands is used to retrieve the URL of a repository for use with package managers?","aws codeartifact get-repository-endpoint","aws codeartifact get-repository","aws codeartifact describe-repository","aws codeartifact list-repositories","The `aws codeartifact get-repository-endpoint` command is used to retrieve the repository endpoint URL, which is needed to configure package managers like npm, Maven, or pip to access the repository."
"How can you automate the process of publishing packages to AWS CodeArtifact from a CI/CD pipeline?","By using AWS CodeBuild and the AWS CLI","By manually uploading packages through the AWS Management Console","By using AWS CodePipeline to trigger the publishing process","By configuring CodeArtifact to automatically scan repositories for new packages","You can automate publishing packages to CodeArtifact by using AWS CodeBuild in conjunction with the AWS CLI to execute commands for publishing packages as part of your CI/CD pipeline."
"In AWS CodeArtifact, what is the significance of the 'administrator' permission on a repository?","It grants full control over the repository, including permissions management.","It allows users to download packages from the repository.","It allows users to publish packages to the repository.","It allows users to view the repository details.","The 'administrator' permission on a CodeArtifact repository grants full control over the repository, including the ability to manage permissions, delete packages, and perform other administrative tasks."
"What is the maximum number of upstream repositories that can be configured for a CodeArtifact repository?","3","5","10","Unlimited","Each CodeArtifact repository can have up to three upstream repositories, allowing you to chain repositories and resolve dependencies from multiple sources."
"What is one advantage of using CodeArtifact over storing packages directly in S3?","Fine-grained access control over packages","Lower storage costs","Faster download speeds","Automatic vulnerability scanning","CodeArtifact provides fine-grained access control over individual packages, whereas S3 offers object-level access control, making it more difficult to manage permissions for individual dependencies."
"How do you integrate AWS CodeArtifact with an existing build system that uses environment variables for configuration?","By using the AWS CLI to inject CodeArtifact credentials into the environment variables.","By manually setting the environment variables in the build system.","By creating a CodeArtifact profile within the build system.","By directly embedding the CodeArtifact credentials into the build script.","You can integrate CodeArtifact with existing build systems by using the AWS CLI to obtain an authorization token and injecting those credentials as environment variables."
"What is the primary purpose of AWS CodeArtifact?","Securely store and share software packages and dependencies","Manage and deploy containerised applications","Monitor application performance","Automate infrastructure provisioning","CodeArtifact is designed to securely store and share software packages used in development, build, and deployment."
"In AWS CodeArtifact, what is a repository?","A central location for storing software packages","A tool for managing infrastructure as code","A continuous integration service","A code review platform","A repository in CodeArtifact is a central location to store and manage software packages and their dependencies."
"What type of packages can AWS CodeArtifact store?","Maven, npm, PyPI, NuGet, and generic packages","Only Docker images","Only source code files","Only executable files","CodeArtifact supports storing Maven, npm, PyPI, NuGet, and generic packages."
"What is an AWS CodeArtifact domain?","A logical grouping of repositories","A set of IAM permissions","A virtual network","A container for AWS Lambda functions","A domain in CodeArtifact is a logical grouping of repositories that share a common configuration, such as permissions and upstream connections."
"How does AWS CodeArtifact integrate with existing build tools?","By providing standard package management client configurations","By replacing existing build tools","By requiring custom scripting for integration","By monitoring build process logs","CodeArtifact provides standard package management client configurations (e.g., `npmrc` for npm, `settings.xml` for Maven) to integrate with existing build tools."
"What is the benefit of using upstream repositories in AWS CodeArtifact?","Packages from public registries are automatically mirrored to local repositories","It prevents developers from pushing changes to repositories","It provides automated code reviews","It limits the size of packages stored in repositories","Upstream repositories allow CodeArtifact repositories to pull packages from other repositories, including public registries, acting as a cache and providing a single source of truth."
"Which AWS service is typically used for authentication with AWS CodeArtifact?","AWS IAM","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS IAM is used for authentication and authorisation with CodeArtifact, controlling who can access and manage the repositories and packages."
"Which of the following is a valid use case for AWS CodeArtifact?","Centralising package management for multiple development teams","Monitoring network traffic","Analysing application logs","Managing database schemas","CodeArtifact centralises package management, ensuring that all development teams use the same, approved set of dependencies."
"What type of policy is used to control access to AWS CodeArtifact resources?","Resource-based policies","Network access control lists","IAM role trust policies","Data encryption policies","Resource-based policies are attached directly to CodeArtifact domains and repositories to define who can access them and what actions they can perform."
"What is the purpose of AWS CodeArtifact's 'publish' permission?","To allow users to upload packages to a repository","To grant read-only access to packages","To enable cross-account access","To create new repositories","The 'publish' permission allows users to upload (publish) packages to a CodeArtifact repository."
"When configuring an upstream repository in AWS CodeArtifact, what does 'external connection' refer to?","Connecting to a public repository like npmjs.com or Maven Central","Connecting to an AWS Lambda function","Connecting to an Amazon S3 bucket","Connecting to an Amazon EC2 instance","An external connection in CodeArtifact refers to connecting to a public package repository like npmjs.com or Maven Central."
"What action is required to allow AWS CodeArtifact to automatically retrieve packages from an external public registry?","Create an external connection on the repository","Create a rule in AWS Config","Enable CloudTrail logging","Create an AWS Firewall rule","You need to create an external connection on your repository, pointing it to the desired public registry.  This permits automated retrieval."
"How can you ensure that your AWS CodeArtifact repository only contains packages approved by your security team?","By implementing a pull request approval process before package uploads","By using AWS Shield","By using AWS WAF","By enabling AWS GuardDuty","Implementing a pull request approval process (or similar) ensures that all packages are vetted before being uploaded to the repository."
"Which AWS service can be used to audit access to your AWS CodeArtifact repositories?","AWS CloudTrail","AWS X-Ray","AWS Trusted Advisor","AWS Inspector","AWS CloudTrail records API calls made to AWS services, including CodeArtifact, allowing you to audit access and changes."
"If a package is not found in a CodeArtifact repository, what happens if an upstream repository is configured?","CodeArtifact automatically fetches the package from the upstream repository","An error is immediately returned","CodeArtifact searches all repositories in the domain in parallel","CodeArtifact terminates the build process","If a package is not found in a CodeArtifact repository, and an upstream repository is configured, CodeArtifact automatically attempts to retrieve the package from the upstream repository."
"What is the command to publish a package to AWS CodeArtifact using the npm CLI?","npm publish --registry <repository_endpoint>","npm deploy --repository <repository_endpoint>","npm upload --registry <repository_endpoint>","npm push --registry <repository_endpoint>","The correct npm command to publish a package is `npm publish`, and you need to specify the CodeArtifact repository endpoint using the `--registry` flag."
"What is the purpose of the AWS CodeArtifact repository endpoint?","It specifies the URL for accessing the repository","It defines the IAM permissions for the repository","It determines the storage capacity of the repository","It sets the retention period for packages","The repository endpoint is the URL used by package management clients (like npm, Maven, pip) to access the repository."
"Which AWS CLI command is used to create a new AWS CodeArtifact repository?","aws codeartifact create-repository","aws codeartifact new-repository","aws codeartifact make-repository","aws codeartifact init-repository","The correct AWS CLI command to create a new CodeArtifact repository is `aws codeartifact create-repository`."
"What is the maximum size limit for a single package asset uploaded to AWS CodeArtifact?","500 GB","10 GB","100 MB","1 GB","The maximum size limit for a single package asset uploaded to CodeArtifact is 500 GB."
"How can you share packages stored in an AWS CodeArtifact repository with another AWS account?","By creating a resource policy that grants access to the other account","By enabling cross-region replication","By creating a public endpoint for the repository","By exporting the repository to a S3 bucket","You can share packages by creating a resource policy on the domain or repository that grants access to the other AWS account."
"What is the difference between a CodeArtifact domain and a CodeArtifact repository?","A domain is a logical grouping of repositories, while a repository stores the packages","A domain stores the packages, while a repository manages the IAM policies","A domain is for public packages, while a repository is for private packages","A domain is for AWS services, while a repository is for customer code","A domain is a logical container that groups multiple repositories together. A repository, within a domain, is where the actual software packages are stored."
"What is the significance of the 'aws codeartifact get-authorization-token' command?","It retrieves a temporary authentication token for accessing CodeArtifact repositories","It generates a KMS encryption key","It creates a new IAM role","It configures network access control lists","The `aws codeartifact get-authorization-token` command retrieves a temporary authentication token, which is necessary for authenticating with CodeArtifact repositories when using package management clients."
"What is the recommended approach for automating the creation of AWS CodeArtifact repositories and domains?","Using AWS CloudFormation or AWS CDK","Manually creating them through the AWS Management Console","Using a shell script and the AWS CLI","Using AWS Config rules","AWS CloudFormation or AWS CDK allows you to define your infrastructure as code, including CodeArtifact resources, enabling automated and repeatable deployments."
"When should you consider using AWS CodeArtifact's 'repository policies'?","When you need fine-grained control over access to specific packages","When you want to configure network access","When you need to monitor repository usage","When you want to automate backups","Repository policies are used to control access to the repository and the packages it contains, allowing for fine-grained access control."
"Which of the following is NOT a supported package format in AWS CodeArtifact?","Debian (.deb)","Maven","npm","PyPI","CodeArtifact supports Maven, npm, PyPI, NuGet and generic packages but not Debian packages (.deb)."
"How can you ensure high availability for your AWS CodeArtifact infrastructure?","CodeArtifact is a fully managed service and is inherently highly available","By creating read replicas of the repositories","By deploying CodeArtifact across multiple AWS regions","By configuring a load balancer in front of CodeArtifact","CodeArtifact is a fully managed service, meaning AWS handles the underlying infrastructure and ensures high availability."
"You want to give a developer temporary access to publish packages to your AWS CodeArtifact repository. How can you best achieve this?","Grant temporary credentials using AWS STS and IAM roles","Add the developer's IAM user to the repository's resource policy","Share the repository's access key with the developer","Create a long-lived IAM user for the developer with publish permissions","Using AWS STS (Security Token Service) and IAM roles, you can provide temporary credentials to a user, giving them temporary access to publish packages."
"What happens if you try to publish a package version that already exists in your AWS CodeArtifact repository (with the same package name and version)?","The publish operation will fail","CodeArtifact will automatically overwrite the existing package","CodeArtifact will create a new version with a different timestamp","CodeArtifact will create a snapshot of the existing package","CodeArtifact prevents you from publishing a package version that already exists to maintain immutability and prevent accidental overwrites."
"How does AWS CodeArtifact help in securing your software supply chain?","By providing a centralised, controlled repository for software packages","By automatically scanning code for vulnerabilities","By encrypting network traffic","By implementing multi-factor authentication","CodeArtifact helps secure the software supply chain by providing a central, controlled repository, ensuring that all developers and build processes use the same, approved dependencies."
"Which of the following is NOT a benefit of using AWS CodeArtifact?","Simplified package sharing and management","Reduced risk of using compromised packages","Automated vulnerability scanning of packages","Improved build reproducibility","Automated vulnerability scanning of packages is not a built-in feature of CodeArtifact. While CodeArtifact helps manage and secure your packages, it doesn't automatically scan for vulnerabilities."
"What is the purpose of the 'domain-owner' in an AWS CodeArtifact domain?","It specifies the AWS account that owns and manages the domain","It defines the team responsible for the domain","It identifies the region where the domain is located","It specifies the administrator of the domain","The 'domain-owner' specifies the AWS account that owns and manages the domain. This account has full control over the domain and its repositories."
"How can you monitor the storage usage of your AWS CodeArtifact repositories?","Using Amazon CloudWatch metrics","Using AWS CloudTrail logs","Using AWS Config rules","Using AWS Trusted Advisor","Amazon CloudWatch metrics provide insights into the storage usage of your CodeArtifact repositories, allowing you to monitor and manage capacity."
"What is the command to install a package from AWS CodeArtifact using the pip CLI?","pip install --index-url <repository_endpoint> <package_name>","pip get <package_name> --repository <repository_endpoint>","pip fetch <package_name> --repository <repository_endpoint>","pip download <package_name> --repository <repository_endpoint>","The command to install a package with pip from a specific repository is `pip install --index-url <repository_endpoint> <package_name>`."
"In AWS CodeArtifact, what does 'package origin' refer to?","The source of the package (e.g., an upstream repository or a direct publish)","The size of the package","The version of the package","The author of the package","Package origin refers to the source of the package, indicating whether it was directly published to the repository or retrieved from an upstream repository or external connection."
"What is the purpose of the AWS CodeArtifact 'repository-external-connections' parameter?","To allow CodeArtifact to retrieve packages from public package managers","To limit the external access from a given repository","To provide version control","To enable cross-account access","The `repository-external-connections` parameter allows the CodeArtifact repository to be configured to be able to retrieve artifacts from an upstream public repository. For example, npmjs, pypi or Maven central."
"Which of the following is a valid AWS CodeArtifact domain name?","my-domain","MyDomain","my_domain","My Domain","CodeArtifact domain names must start with a lowercase letter and can only contain lowercase letters, numbers, hyphens (-), and periods (.)."
"What is the typical lifecycle of a package in AWS CodeArtifact?","Published, consumed, archived","Created, tested, deployed","Requested, downloaded, installed","Compiled, linked, executed","The typical lifecycle involves the package being published to the repository, consumed by builds and deployments, and potentially archived or deprecated over time."
"How can you ensure that your AWS CodeArtifact setup is compliant with your organisation's security policies?","By using AWS Config to monitor and enforce compliance rules","By creating IAM policies","By scanning for known vulnerabilities","By encrypting artifacts","AWS Config allows you to define and enforce compliance rules for your AWS resources, including CodeArtifact, ensuring they adhere to your organisation's security policies."
"What is the effect of setting an AWS CodeArtifact repository to read-only?","Users can consume packages but cannot publish new packages","Users cannot access the repository","The repository can only be accessed from within a VPC","The repository can only be accessed by the account owner","Setting a repository to read-only means that users can still download and consume packages from it, but they are prevented from publishing new packages or updating existing ones."
"You need to migrate existing packages from a different package repository to AWS CodeArtifact. What is the recommended approach?","Use the AWS CLI to upload packages to CodeArtifact","Use AWS DataSync","Use AWS Transfer Family","Create a direct S3 connection","The AWS CLI can be used to upload packages to a CodeArtifact repository, allowing you to migrate from other repositories."
"What is the purpose of the AWS CodeArtifact '--dry-run' parameter in the AWS CLI?","To preview the changes that will be made without actually executing the command","To test network connectivity","To check IAM permissions","To debug errors in the command","The `--dry-run` parameter allows you to preview the changes that will be made by a command without actually executing it. This is useful for testing and validating your configuration."
"Which of the following actions CANNOT be performed via AWS CodeArtifact's console?","Uploading a single package","Creating a repository","Deleting a domain","Configuring upstream repositories","Uploading single packages cannot be done directly through the AWS Management Console, but only via the AWS CLI and package management client integrations."
"Which action will reduce the cost of storing packages with multiple versions in CodeArtifact?","Setting a storage policy with artifact retention rules","Switching to a cheaper storage class","Moving older versions of artifacts to S3","Deleting the entire repository","Storage policies with artifact retention rules can be defined so that older versions are automatically deleted."
"What is an advantage of using AWS CodeArtifact with AWS CodePipeline?","Seamless integration and automated package versioning during CI/CD","Cost optimisation of AWS CodePipeline pipelines","Centralised location for code repository backups","Automated scanning for viruses and other security issues","AWS CodeArtifact integrates seamlessly with CodePipeline, ensuring that the correct versions of artifacts are available during different CI/CD stages."
"How can you control the IP addresses or CIDR blocks that can access your AWS CodeArtifact repositories?","By attaching a resource policy with a 'Condition' block that restricts access based on 'aws:SourceIp'","By enabling VPC endpoints for AWS CodeArtifact","By implementing a firewall rule","By enabling AWS Shield","By using a resource policy with a `Condition` block that restricts access based on `aws:SourceIp`."
"What is the command used to update a AWS CodeArtifact repository?","aws codeartifact update-repository","aws codeartifact modify-repository","aws codeartifact change-repository","aws codeartifact edit-repository","`aws codeartifact update-repository` is the correct AWS CLI command used to update existing AWS CodeArtifact repositories."
"What is one of the limits for CodeArtifact?","A maximum of 100 repositories per domain","A maximum of 10 domains per AWS account","A maximum of 1GB per artifact","A maximum of 5 IAM users per domain","CodeArtifact has a limit of 100 repositories per domain."
"How does AWS CodeArtifact enable developers to work more efficiently?","By providing a single source of truth for all software packages","By managing the infrastructure for builds and deployments","By automating code reviews","By providing real-time application monitoring","CodeArtifact acts as a single source of truth for all software packages, allowing developers to easily discover, share, and reuse components, leading to increased efficiency."
"A new version of an artifact has been deployed and you want to notify all the stakeholders. What functionality would help you with this?","AWS CodeArtifact events to Amazon EventBridge","AWS CodeArtifact metrics to Amazon CloudWatch","AWS CodeArtifact data to AWS CloudTrail","AWS CodeArtifact audit logs to Amazon S3","Events can be configured to be sent when an artifact is published and sent to EventBridge which can then be used to notify stakeholders using, for example, SNS."
"Which of the following are supported in CodeArtifact package version semantics?","Major, minor, patch, and pre-release versions","Major, minor, and fix versions only","Only major versions are required","Only numbered versions","CodeArtifact supports major, minor, patch, and pre-release versions, following semantic versioning principles."
"How can I determine the origin of a specific artifact in CodeArtifact?","View the package metadata in the CodeArtifact console or using the AWS CLI","Examine the CloudTrail logs","Inspect the build logs","Run an AWS Config rule","The package metadata in CodeArtifact will show the artifact's origin (e.g. published to CodeArtifact or pulled from an external connection)."
