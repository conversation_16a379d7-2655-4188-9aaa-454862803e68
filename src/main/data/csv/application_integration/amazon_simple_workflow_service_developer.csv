"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon SWF, what is a 'workflow'?","A set of activities performed in a specific order to achieve a business goal","A single, atomic task within a larger process","A storage container for workflow data","A user interface for managing workflows","A workflow in SWF represents the overall business process, coordinating multiple activities to achieve a specific goal."
"What is the main role of a 'decider' in Amazon SWF?","To schedule activities and respond to events","To execute individual tasks in the workflow","To store the workflow execution history","To provide a user interface for monitoring workflows","Deciders are responsible for coordinating the workflow execution by scheduling activities based on the workflow logic and responding to events."
"What is an 'activity worker' in Amazon SWF responsible for?","Executing the individual tasks within a workflow","Managing the overall workflow execution flow","Storing the workflow execution history","Providing a user interface for starting workflows","Activity workers perform the actual business logic, executing the specific tasks defined in the workflow."
"In Amazon SWF, what does the term 'domain' refer to?","A logical grouping of workflows, activity types, and other resources","A region where the SWF service is hosted","A type of activity that can be performed","A set of users with permissions to access the SWF service","A domain provides a namespace for organizing and managing SWF components."
"What is the purpose of the 'ActivityTaskTimeout' in Amazon SWF?","To define the maximum time an activity worker has to complete a task","To define the maximum time a workflow can run","To define the interval between activity retries","To define the maximum time a decider has to respond to an event","The `ActivityTaskTimeout` specifies the maximum duration an activity worker is allowed to take to complete a given task."
"Which of the following is a benefit of using Amazon SWF for workflow management?","Fault tolerance and durability","Automatic scaling of compute resources","Integrated monitoring of all AWS services","Simplified database management","Amazon SWF provides fault tolerance and durability by automatically retrying failed tasks and ensuring the workflow execution state is preserved."
"In Amazon SWF, what happens if an activity worker fails to complete a task within the 'ScheduleToCloseTimeout'?","The task is automatically retried by SWF","The workflow execution is terminated","An exception is thrown, but the workflow continues","A new activity worker is automatically launched","If an activity worker fails to complete a task within the `ScheduleToCloseTimeout`, the task is automatically retried by SWF."
"What is the purpose of the 'SignalWorkflowExecution' action in Amazon SWF?","To send an external event to a running workflow","To terminate a running workflow","To start a new workflow execution","To cancel a scheduled activity","`SignalWorkflowExecution` allows you to send external events to a running workflow, enabling external systems to influence the workflow's execution flow."
"In Amazon SWF, what is the difference between 'TaskList' and 'ActivityList'?","`TaskList` is used for deciders, `ActivityList` is not a concept in SWF.","`TaskList` is used for activity workers, `ActivityList` is used for deciders.","`TaskList` lists all domains and `ActivityList` lists all workflows.","They are synonyms.","`TaskList` is used by deciders to poll for decision tasks and by activity workers to poll for activity tasks. There is no `ActivityList`."
"Which of the following is a typical use case for Amazon SWF?","Order processing and fulfilment","Static website hosting","Real-time data analytics","Image recognition","Amazon SWF is well-suited for order processing and fulfilment scenarios, where complex workflows involving multiple steps and systems are required."
"What type of tasks do deciders poll for using the Amazon SWF 'PollForDecisionTask' API?","Tasks that require decisions about the next steps in the workflow","Tasks that need to be executed by activity workers","Tasks that contain the workflow execution history","Tasks that need to be validated before completion","Deciders poll for decision tasks, which contain information about the current state of the workflow execution and require the decider to make decisions about the next steps."
"What does the 'ScheduleToStartTimeout' parameter in Amazon SWF define?","The maximum time an activity task can wait in the task list before an activity worker picks it up","The maximum time a workflow execution can run","The maximum time a decider can take to respond to a decision task","The time between retries of a failed activity","The `ScheduleToStartTimeout` specifies the maximum amount of time an activity task can wait in the task list before an activity worker picks it up."
"What is the significance of the 'WorkflowExecutionTimeout' parameter in Amazon SWF?","It specifies the maximum duration for a workflow execution","It specifies the maximum time a decider can take to respond to an event","It specifies the maximum time an activity worker can take to complete a task","It specifies the time between retries of a failed activity","The `WorkflowExecutionTimeout` sets a limit on the total duration of a workflow execution. If the workflow doesn't complete within this time, it's terminated."
"In Amazon SWF, what is the purpose of 'ContinueAsNewWorkflowExecution'?","To restart a workflow execution from the beginning with potentially updated input","To terminate a workflow execution","To pause a workflow execution","To resume a workflow execution after a failure","`ContinueAsNewWorkflowExecution` allows you to restart a workflow execution from the beginning with potentially updated input, enabling long-running workflows to avoid hitting execution limits."
"What happens in Amazon SWF when an activity worker fails to respond within the 'HeartbeatTimeout'?","The activity task is considered failed and retried or handled according to the workflow's error handling logic.","The workflow execution is terminated.","A new activity worker is automatically launched.","The task is automatically reassigned to a different activity worker.","When an activity worker fails to send a heartbeat within the specified `HeartbeatTimeout`, SWF considers the activity task as failed and takes appropriate action based on the workflow's configuration."
"Which of the following is NOT a component of Amazon SWF?","Orchestrator","Decider","Activity Worker","Domain","The core components are Decider, Activity Worker and Domain. Orchestrator is not one of the core components."
"What is the primary role of the SWF workflow starter?","To initiate a new workflow execution","To monitor the status of running workflows","To execute individual activities in the workflow","To manage the task lists for activity workers","The workflow starter is responsible for starting new workflow executions by sending the initial request to SWF."
"In Amazon SWF, what is the purpose of a 'marker'?","To record specific events in the workflow execution history","To indicate the start of a new activity","To define a branch in the workflow logic","To store temporary data during the workflow execution","Markers are used to record specific events in the workflow execution history, providing a way to track progress and diagnose issues."
"What is the benefit of using Amazon SWF over implementing a custom workflow solution?","SWF provides built-in features for fault tolerance, scalability, and coordination","Custom solutions are always more cost-effective","Custom solutions offer better integration with third-party systems","SWF allows direct access to the underlying AWS infrastructure","Amazon SWF provides built-in features for fault tolerance, scalability, and coordination, reducing the effort required to build and maintain a custom workflow solution."
"In Amazon SWF, what is the purpose of the 'RecordMarker' API action?","To add a custom marker event to the workflow execution history","To retrieve the history of a workflow execution","To signal a workflow execution","To schedule an activity task","The `RecordMarker` API action allows you to add a custom marker event to the workflow execution history, enabling you to track specific events or milestones in the workflow."
"What is the purpose of the 'GetWorkflowExecutionHistory' action in Amazon SWF?","To retrieve the history of events for a specific workflow execution","To start a new workflow execution","To signal a running workflow execution","To terminate a workflow execution","The `GetWorkflowExecutionHistory` action is used to retrieve the history of events that occurred during a specific workflow execution, providing a detailed audit trail of the workflow's progress."
"In Amazon SWF, what is the difference between a 'child workflow' and a 'sub-activity'?","A child workflow is a separate workflow execution invoked by the parent workflow, while a sub-activity is a task within the parent workflow","They are the same thing.","A child workflow manages several activities while the sub-activity manages domains.","There is no such thing as a sub-activity","A child workflow is a separate, self-contained workflow execution that can be invoked and managed by a parent workflow.  A sub-activity is not a concept in SWF."
"Which of the following is a limitation of Amazon SWF compared to other workflow services?","It requires developers to manage the execution state and coordination logic","It only supports Java programming language","It cannot integrate with other AWS services","It is limited to simple workflows","SWF requires developers to manage the execution state and coordination logic, providing more control but also requiring more effort."
"What is the purpose of the 'StartChildWorkflowExecution' action in Amazon SWF?","To initiate a new child workflow execution from within a parent workflow","To terminate a running child workflow execution","To signal a child workflow execution","To query the status of a child workflow execution","The `StartChildWorkflowExecution` action allows a parent workflow to start a new child workflow execution, enabling complex workflows to be broken down into smaller, manageable components."
"In Amazon SWF, what is the purpose of the 'CancelWorkflowExecution' action?","To stop a running workflow execution","To pause a workflow execution","To restart a workflow execution","To resume a workflow execution after a failure","The `CancelWorkflowExecution` action is used to stop a running workflow execution, typically in response to an error or a user request."
"What is the main advantage of using a 'versioned' activity in Amazon SWF?","Allows you to update the activity logic without affecting running workflows","It allows parallel processing of activities","It enables automatic scaling of activity workers","It increases the security of activity execution","Versioning allows you to update the activity logic without affecting running workflows, providing backward compatibility and enabling seamless updates."
"In Amazon SWF, what is the purpose of the 'DescribeWorkflowExecution' action?","To retrieve information about a specific workflow execution","To start a new workflow execution","To signal a running workflow execution","To terminate a workflow execution","The `DescribeWorkflowExecution` action allows you to retrieve information about a specific workflow execution, such as its status, start time, and input."
"What is the primary use case for the Amazon SWF 'TerminateWorkflowExecution' API?","To forcefully stop a workflow execution immediately","To gracefully cancel a workflow execution","To retry a failed workflow execution","To archive a completed workflow execution","The `TerminateWorkflowExecution` API is used to forcefully stop a workflow execution immediately, often used when a workflow is in an unrecoverable state or needs to be stopped urgently."
"In Amazon SWF, what is the purpose of the 'RequestCancelWorkflowExecution' action?","To request the cancellation of a workflow execution","To terminate a workflow execution","To signal a workflow execution","To query the status of a workflow execution","The `RequestCancelWorkflowExecution` action is used to request the cancellation of a workflow execution, allowing the workflow to gracefully shut down."
"Which of the following is NOT a valid 'state' of a workflow execution in Amazon SWF?","Running","Completed","Failed","Suspended","Valid states are: Running, Completed, Failed, Cancelled, TimedOut and ContinuedAsNew. There is no Suspended State."
"In Amazon SWF, what is the 'retry' mechanism designed to handle?","Transient failures in activity tasks","Permanent errors in workflow logic","Security breaches in the SWF service","Resource exhaustion on the decider","The retry mechanism in SWF is designed to handle transient failures in activity tasks, such as network connectivity issues or temporary resource unavailability."
"What does the 'defaultTaskList' parameter in the SWF domain configuration specify?","The task list used for deciders and activity workers by default","The list of all available activity types in the domain","The list of users authorized to access the domain","The default timeout settings for activities in the domain","The `defaultTaskList` parameter specifies the task list that deciders and activity workers will use by default when polling for tasks in the domain."
"Which of the following actions triggers a 'decision task' in Amazon SWF?","An activity task completes or fails","An activity worker starts executing a task","A user accesses the SWF console","A new workflow execution is initiated","Decision tasks are triggered when events occur in the workflow, such as the completion or failure of an activity task, requiring the decider to make decisions about the next steps."
"What is the main purpose of the Amazon SWF Flow Framework?","To simplify the development of SWF-based applications using annotations and programming conventions","To provide a graphical user interface for managing SWF workflows","To automate the deployment of SWF activity workers","To provide a command-line interface for interacting with the SWF service","The SWF Flow Framework simplifies the development of SWF-based applications by providing annotations and programming conventions, making it easier to define and manage workflows."
"What is the key benefit of using the 'exponential backoff' strategy for activity retries in Amazon SWF?","It reduces the load on activity workers and prevents overwhelming downstream systems","It guarantees that all activities will eventually succeed","It simplifies the configuration of activity retries","It prioritises critical activities over less important ones","Exponential backoff reduces the load on activity workers and prevents overwhelming downstream systems by gradually increasing the delay between retry attempts."
"In Amazon SWF, what is the purpose of a 'Timer'?","To schedule events to occur after a specified delay","To monitor the execution time of activities","To track the overall duration of a workflow","To trigger alerts when certain events occur","Timers are used to schedule events to occur after a specified delay, enabling workflows to implement time-based logic."
"What does the term 'event-driven' mean in the context of Amazon SWF?","The workflow execution progresses based on events such as activity completion and timer expirations","The workflow is triggered by external events such as user actions or system alerts","The workflow logic is defined using event-based programming paradigms","The workflow execution is optimised for handling a large number of concurrent events","In SWF, workflow execution progresses based on events such as activity completion, timer expirations, and external signals, which trigger decision tasks and drive the workflow forward."
"Which Amazon service is commonly used with Amazon SWF for storing and retrieving data related to workflow executions?","Amazon S3","Amazon CloudWatch","Amazon SNS","Amazon CloudFront","Amazon S3 is commonly used with SWF to store and retrieve data related to workflow executions, such as input and output data for activities."
"In Amazon SWF, what is the significance of the 'executionContext' attribute?","It allows activity workers to pass data to the decider for decision-making purposes","It is used to store the execution history of a workflow","It specifies the AWS region where the workflow is executed","It defines the security credentials used to access SWF","The `executionContext` attribute allows activity workers to pass data to the decider for decision-making purposes, enabling activities to provide context to the workflow execution."
"What is the purpose of 'ActivityType' registration in Amazon SWF?","To define the interface and configuration for an activity that can be performed in a workflow","To register a new activity worker with the SWF service","To specify the task list for an activity","To define the security permissions for an activity","ActivityType registration defines the interface and configuration for an activity that can be performed in a workflow, including its name, version, and default settings."
"Which of the following is NOT a recommended best practice for designing workflows in Amazon SWF?","Making activity tasks as granular as possible","Using long-running activity tasks to minimize communication overhead","Implementing idempotent activities to handle retries","Handling exceptions and errors gracefully","Using long-running activity tasks is NOT recommended because it can make it harder to manage and debug the workflow."
"In Amazon SWF, what is the purpose of 'WorkflowType' registration?","To define the structure and behaviour of a specific type of workflow","To register a new decider with the SWF service","To specify the task list for a workflow","To define the security permissions for a workflow","WorkflowType registration defines the structure and behaviour of a specific type of workflow, including its name, version, and default settings."
"What does the 'input' parameter in the Amazon SWF 'StartWorkflowExecution' API specify?","The data passed to the initial activity of the workflow","The name of the workflow type to be executed","The task list used for the workflow execution","The timeout settings for the workflow execution","The `input` parameter specifies the data that is passed to the initial activity of the workflow when it is started."
"In Amazon SWF, how can you ensure that a critical activity is executed even if the activity worker fails?","Configure a retry policy with a sufficient number of retries and an appropriate backoff strategy","Increase the heartbeat timeout to allow the activity worker more time to respond","Use a larger EC2 instance for the activity worker","Enable multi-AZ deployment for the SWF service","Configuring a retry policy with a sufficient number of retries and an appropriate backoff strategy ensures that a critical activity is executed even if the activity worker fails temporarily."
"What is the difference between a 'decision task' and an 'activity task' in Amazon SWF?","A decision task is executed by the decider to determine the next steps in the workflow, while an activity task is executed by an activity worker to perform a specific business logic operation","A decision task is executed by the activity worker, while an activity task is executed by the decider.","A decision task is initiated by a user while an activity task is started by a decider.","An activity task is executed when a decision task fails.","A decision task is executed by the decider to determine the next steps in the workflow, while an activity task is executed by an activity worker to perform a specific business logic operation."
"What is the primary advantage of using task lists in Amazon SWF?","To decouple deciders and activity workers, allowing them to scale independently","To define the priority of tasks in the workflow","To group activities based on their functionality","To monitor the progress of tasks in the workflow","Task lists decouple deciders and activity workers, allowing them to scale independently by enabling them to poll for tasks from different lists based on their capacity and capabilities."
"In Amazon SWF, what is the purpose of the 'CloseStatusFilter' when using 'ListWorkflowExecutions'?","To filter the results based on the final status of the workflow executions (e.g., COMPLETED, FAILED, CANCELED)","To filter the results based on the current state of the workflow executions (e.g., RUNNING, OPEN)","To filter the results based on the start time of the workflow executions","To filter the results based on the workflow type","The `CloseStatusFilter` is used to filter the results of the `ListWorkflowExecutions` API call based on the final status of the workflow executions, allowing you to retrieve workflows that have completed with a specific status."
"In Amazon SWF, what does a 'Domain' represent?","A logical grouping of related workflows, activities, and other SWF components","A physical server where SWF components are hosted","A specific type of activity worker","A queue for pending workflow tasks","A domain provides a namespace for your workflows, activities, and other SWF components, isolating them from other applications."
"What is the main role of a 'Decider' in Amazon SWF?","To schedule activity tasks and provide control flow for a workflow","To execute activity tasks","To monitor workflow progress and send notifications","To store the state of a workflow execution","The decider is responsible for inspecting the current state of a workflow execution and making decisions about which activity tasks to schedule next, thus controlling the flow of the workflow."
"What is an 'Activity Worker' in Amazon SWF responsible for?","Executing the actual business logic of a workflow","Scheduling tasks for the decider","Managing the workflow state","Routing events to the appropriate decider","Activity workers are responsible for performing the individual tasks that make up a workflow's business logic. They execute activity tasks assigned by the decider."
"In Amazon SWF, what is a 'Workflow Execution'?","A single instance of a workflow","The definition of a workflow","A collection of activity workers","The process of registering a domain","A workflow execution represents a specific run of a workflow, from start to finish."
"What is the purpose of 'Activity Task Timeouts' in Amazon SWF?","To prevent activity workers from getting stuck on long-running tasks","To limit the number of activity workers that can run concurrently","To set the maximum time for a workflow execution","To specify the time zone for workflow events","Activity task timeouts ensure that activity workers don't get stuck indefinitely on tasks, allowing the decider to reschedule the task or handle the error."
"What does 'Decider Timeout' in Amazon SWF refer to?","The maximum time a decider has to make a decision about the workflow","The maximum time an activity worker has to complete a task","The time it takes to register a domain","The time before a workflow execution is automatically cancelled","Decider timeout defines the maximum amount of time that a decider has to respond to a task before SWF times out and retries the task, or fails the workflow."
"Which of the following is a benefit of using Amazon SWF over simpler queueing systems?","Complex workflow orchestration and coordination","Automatic scaling of activity workers","Lower cost for simple tasks","Built-in support for message encryption","Amazon SWF is designed for complex workflows involving multiple steps and coordination between different actors. It provides more advanced features than simple queueing systems."
"What is the role of the 'SWF Flow Framework'?","A programming model and set of tools for building SWF applications","A tool for monitoring workflow executions in real-time","A service for managing activity workers","A framework for deploying SWF applications to AWS","The SWF Flow Framework provides a programming model and tools that simplify the development of SWF applications, making it easier to define workflows and activities."
"What is the function of the 'RespondActivityTaskCompleted' action in Amazon SWF?","To signal that an activity worker has finished processing a task","To cancel a running activity task","To request a new activity task","To start a new workflow execution","The RespondActivityTaskCompleted action is used by an activity worker to notify SWF that it has successfully completed an assigned activity task."
"What is the purpose of the 'ContinueAsNewWorkflowExecution' action in Amazon SWF?","To restart a workflow execution with a new input and potentially a new version of the workflow definition","To pause a workflow execution","To cancel a workflow execution","To resume a completed workflow execution","ContinueAsNewWorkflowExecution is used to restart a workflow execution with potentially updated code, data, or configuration. This allows for long-running workflows to adapt to changes without losing their history."
"What is the 'TaskList' in Amazon SWF used for?","To route tasks to specific deciders or activity workers","To store the history of a workflow execution","To define the schema of activity tasks","To manage user permissions for SWF resources","A TaskList is used to route tasks to specific deciders or activity workers. It acts as a logical queue for tasks of a particular type."
"How does Amazon SWF ensure durability of workflow state?","By storing workflow history in a durable storage system","By replicating the workflow state across multiple availability zones","By caching the workflow state in memory","By using a distributed consensus algorithm","Amazon SWF stores workflow history in a durable storage system, ensuring that the workflow state is preserved even in the event of failures."
"Which of the following is a valid way to monitor the progress of a workflow execution in Amazon SWF?","Using the SWF console or AWS CLI to inspect the workflow history","By setting up CloudWatch alarms based on workflow metrics","By subscribing to SNS notifications about workflow events","All of the above","You can monitor workflow progress using the SWF console/CLI, CloudWatch alarms, and SNS notifications."
"What is the purpose of the 'SignalWorkflowExecution' action in Amazon SWF?","To send an external event to a running workflow execution","To start a new workflow execution","To cancel a running workflow execution","To terminate a workflow execution","The SignalWorkflowExecution action is used to send an external event to a running workflow execution, allowing external systems to influence the workflow's behaviour."
"In Amazon SWF, what happens when an activity worker fails to heartbeat within the specified 'heartbeat timeout'?","The task is considered failed and is rescheduled or handled according to the workflow logic","The workflow execution is automatically cancelled","The activity worker is automatically restarted","The activity worker is put into a suspended state","If an activity worker fails to heartbeat within the heartbeat timeout, SWF considers the task failed and will reschedule it or handle it according to the workflow's defined logic."
"What is the purpose of registering an activity type or workflow type in Amazon SWF?","To define the interface and configuration for the activity or workflow","To deploy the code for the activity or workflow","To authorize access to the activity or workflow","To create a backup of the activity or workflow","Registering an activity type or workflow type defines its interface, default parameters, and configuration options, such as timeouts and task lists."
"Which AWS service is commonly used for storing and retrieving data accessed by activity workers in Amazon SWF?","Amazon S3","Amazon CloudFront","Amazon Glacier","Amazon SES","Amazon S3 is a common choice for storing and retrieving data used by activity workers, providing scalable and durable storage."
"How does Amazon SWF handle versioning of workflows and activities?","By allowing multiple versions of workflows and activities to be registered and used concurrently","By automatically upgrading all running workflows and activities to the latest version","By requiring manual migration of workflows and activities to new versions","By preventing versioning altogether","Amazon SWF allows multiple versions of workflows and activities to coexist, enabling seamless upgrades and changes without disrupting running executions."
"What is the purpose of the 'TerminateWorkflowExecution' action in Amazon SWF?","To forcefully stop a running workflow execution","To pause a workflow execution","To mark a workflow execution as completed","To restart a workflow execution","TerminateWorkflowExecution is used to immediately stop a running workflow execution, typically in response to an error or an external event."
"When should you use Amazon SWF instead of AWS Step Functions?","When you need fine-grained control over task scheduling and state management","When you need a visual workflow designer","When you need automatic retries of failed tasks","When you need to integrate with AWS Lambda functions","Amazon SWF provides more flexibility and control over task scheduling and state management, while Step Functions offers a simpler, more visual approach for coordinating AWS services."
"What is the purpose of the 'RecordMarker' event in Amazon SWF history?","To add a custom event to the workflow history for tracking purposes","To mark a specific point in the workflow execution","To indicate an error in the workflow execution","To signal the start of a new activity task","RecordMarker events allow you to add custom information to the workflow history, which can be useful for tracking progress, debugging, or auditing."
"What is the relationship between a Decider and a WorkflowExecution in Amazon SWF?","A Decider makes decisions to guide the WorkflowExecution through the steps in the workflow definition","A Decider contains the business logic of the WorkflowExecution","A Decider stores the state of the WorkflowExecution","A Decider executes tasks defined by the WorkflowExecution","The decider examines the history of a `WorkflowExecution` and decides what activities need to be scheduled next, thus it guides the flow of the workflow."
"What feature of Amazon SWF aids in ensuring that a Decider does not have to keep polling for new task to do?","Activity Task Polling","Decision Task Polling","Long Polling","Short Polling","SWF uses long-polling to give `Deciders` access to tasks. Long-polling is the only correct answer."
"What event is recorded in the Workflow Execution History when an activity is initiated?","ActivityTaskScheduled","ActivityTaskStarted","ActivityTaskCompleted","WorkflowExecutionStarted","The `ActivityTaskScheduled` event is recorded when an activity is initiated."
"When are activity tasks added to a task list in Amazon SWF?","When a decider schedules the activity task","When an activity worker polls for tasks","When the workflow execution starts","When an activity worker completes a task","Activity tasks are added to a task list when a decider schedules them as part of the workflow's logic."
"How does Amazon SWF handle failures in activity workers?","It automatically reschedules the task to another worker or allows the decider to handle the failure","It immediately terminates the workflow execution","It pauses the workflow execution until the worker recovers","It ignores the failure and continues with the next task","SWF will reschedule the task or allow the decider to handle the failure."
"What is the maximum duration for a workflow execution in Amazon SWF?","1 year","30 days","72 hours","Unlimited","The maximum duration for a workflow execution in Amazon SWF is 1 year."
"Which of the following is a characteristic of Amazon SWF Domains?","They isolate workflows, activities, and other components within a specific application or organization","They define the geographical region where workflows are executed","They provide a pricing model for SWF usage","They automatically scale activity workers based on demand","Domains are used for isolating resources."
"What is the primary advantage of using activity types in Amazon SWF?","They abstract the implementation details of activity tasks","They automatically scale activity workers","They provide built-in error handling for activity tasks","They reduce the cost of activity execution","Activity types help abstract the implementation details."
"What is the main purpose of the AWS Flow Framework for Java in Amazon SWF?","To simplify the development and testing of SWF applications using Java","To provide a visual designer for creating workflows","To automate the deployment of SWF applications","To monitor the performance of SWF applications","The AWS Flow Framework for Java simplifies development and testing."
"Which of the following is a key difference between Amazon SWF and AWS SQS?","SWF is designed for complex workflows, while SQS is a simple queuing service","SWF automatically scales activity workers, while SQS requires manual scaling","SWF supports long-running tasks, while SQS is limited to short-lived tasks","SWF provides built-in monitoring and logging, while SQS does not","SQS is a simple queueing service, while SWF is for complex workflows."
"What is the purpose of the 'StartWorkflowExecution' action in Amazon SWF?","To initiate a new instance of a workflow","To resume a previously paused workflow","To cancel a running workflow","To update the definition of a workflow","The StartWorkflowExecution action is used to create new instances of a workflow."
"In Amazon SWF, what does the term 'Decision Task' refer to?","A task assigned to a decider to make decisions about the workflow's next steps","A task assigned to an activity worker to perform a specific business logic operation","A task used to monitor the progress of a workflow execution","A task used to register a new activity type","Decision Tasks are sent to deciders for decision making."
"How can you ensure that activity tasks in Amazon SWF are executed in a specific order?","By using a decider to schedule the tasks in the desired sequence","By assigning priorities to activity tasks","By using task lists to group related tasks","By setting dependencies between activity tasks","The decider needs to explicitly schedule activities in the correct sequence."
"What is the role of the 'WorkflowStarter' in the AWS Flow Framework for Java?","To initiate workflow executions","To execute activity tasks","To manage activity workers","To monitor workflow progress","The `WorkflowStarter` is used to intitiate workflow executions in the AWS Flow Framework for Java."
"Which of the following is a valid use case for Amazon SWF?","Orchestrating a multi-step order processing system","Distributing web traffic across multiple servers","Storing and retrieving large files","Monitoring the health of EC2 instances","SWF is suited to ordering systems and workflows."
"What is the significance of the 'ExecutionContext' in Amazon SWF?","It provides a way to pass data between activity tasks and deciders","It defines the permissions for accessing SWF resources","It specifies the geographical region for workflow execution","It manages the concurrency of activity workers","It is used to pass data between activity tasks and deciders."
"How does Amazon SWF support fault tolerance?","By automatically retrying failed activity tasks and rescheduling them on other workers","By automatically backing up workflow state to multiple availability zones","By automatically scaling activity workers to handle increased load","By automatically encrypting all data in transit","SWF allows for automatic retrying."
"Which of the following is a responsibility of the 'Decider' in Amazon SWF?","Polling for Decision Tasks","Executing Activity Tasks","Polling for Activity Tasks","Registering Workflow Types","Polling for Decision Tasks is the Deciders responsibility."
"What type of consistency does Amazon SWF provide for workflow executions?","Eventually consistent","Strongly consistent","Read-after-write consistent","Session consistent","SWF provides eventual consistency for executions."
"Which of the following is NOT a benefit of using Amazon SWF?","Tight integration with AWS Lambda","Complex workflow management","Coordination across distributed components","High scalability and reliability","SWF is NOT tightly integrated with Lambda."
"How can you pass data between tasks in Amazon SWF?","Through activity input and output","Using shared memory","By passing environment variables","Using HTTP requests","The activity input and output are used to share data between tasks."
"What is the purpose of the 'WorkflowIdReusePolicy' in Amazon SWF?","To control whether a workflow ID can be reused after a workflow execution has completed","To define the maximum number of workflow executions with the same ID","To specify the expiration time for workflow IDs","To automatically generate unique workflow IDs","The WorkflowIdReusePolicy determines whether to allow workflows to reuse IDs."
"In Amazon SWF, what is the function of the 'GetWorkflowExecutionHistory' API?","To retrieve the history of events that occurred during a workflow execution","To list all running workflow executions","To start a new workflow execution","To terminate a workflow execution","It allows you to obtain the history of the execution."
"What is the purpose of a 'Domain Retention Period' in Amazon SWF?","To define how long workflow execution history is retained","To specify the duration for which activity workers are active","To set the maximum lifetime of a domain","To control the frequency of workflow backups","Defines how long the workflow history will be retained."
"What is the purpose of the Lambda Task in the AWS Step Functions?","To invoke an AWS Lambda function","To define a state in the state machine","To execute a task on an EC2 instance","To integrate with Amazon SQS","AWS Step Functions integrates tightly with Lambda, SWF doesn't offer as much integration with Lambda."
"Which of the following is a valid use case for Step Functions, but not ideal for SWF?","Orchestrating serverless workflows with visual design","Managing complex workflows with fine-grained control","Integrating with legacy applications","Handling long-running tasks with external dependencies","SWF doesn't come with the same visual design features as Step Functions."
