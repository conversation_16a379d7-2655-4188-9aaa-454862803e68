"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of Amazon MQ?","To provide a managed message broker service.","To provide a managed compute service.","To provide a managed database service.","To provide a managed container service.","Amazon MQ simplifies setting up and managing message brokers in the cloud, reducing operational overhead."
"Which message broker does Amazon MQ for Active MQ support?","Active MQ","RabbitMQ","Kafka","Redis","Amazon MQ for Active MQ provides a managed service for the open-source Active MQ message broker."
"Which message broker does Amazon MQ for Rabbit MQ support?","Rabbit MQ","Kafka","Active MQ","Redis","Amazon MQ for Rabbit MQ provides a managed service for the open-source Rabbit MQ message broker."
"What is the advantage of using Amazon MQ over managing your own message broker on EC2?","Reduced operational overhead.","Lower network latency.","Greater customisation options.","Direct access to the underlying hardware.","Amazon MQ handles patching, maintenance, and scaling, reducing the management burden."
"Which transport protocol is commonly used with Active MQ in Amazon MQ?","AMQP","HTTP","SMTP","FTP","AMQP (Advanced Message Queuing Protocol) is a standard protocol for message queuing and is commonly used with Active MQ."
"What are Broker instance types in Amazon MQ?","Different sizes of virtual machines to host the broker.","A type of message queue.","A specific protocol used for message transfer.","A set of user permissions.","Broker instance types define the compute and memory resources available to the message broker."
"Which deployment mode offers high availability in Amazon MQ?","Active/Standby","Single-Instance","Multi-Region","Disaster Recovery","The Active/Standby deployment mode provides automatic failover in case of an instance failure."
"What does the term 'failover' refer to in the context of Amazon MQ Active/Standby deployment?","Automatic switch to the standby broker.","Manual restart of the active broker.","Message deletion.","Network disconnection.","Failover is the automatic process of switching from the active to the standby broker when the active broker becomes unavailable."
"How can you secure access to your Amazon MQ broker?","Using AWS Identity and Access Management (IAM) roles and policies.","Using a firewall rule","Using client-side certificates only","Using physical keys","IAM roles and policies control who can access and manage the Amazon MQ broker."
"What is the purpose of the Amazon MQ console?","To manage and monitor your message brokers.","To configure your EC2 instances.","To manage your VPC settings.","To write code for your applications.","The Amazon MQ console provides a graphical interface for managing brokers, queues, and users."
"How can you monitor the performance of your Amazon MQ broker?","Using Amazon CloudWatch metrics.","Using third-party monitoring tools only.","Using system logs on the broker instance.","Using the AWS Config service","Amazon CloudWatch collects and tracks metrics like CPU utilisation, memory usage, and queue depth."
"Which type of message broker is best suited for applications requiring strict ordering and transactional support in Amazon MQ?","Active MQ","Rabbit MQ","Kafka","Redis","Active MQ supports JMS which provides built-in support for ordering and transactions."
"What is the purpose of Virtual Destinations in Amazon MQ?","To provide a mechanism for publish-subscribe messaging using queues.","To provide a mechanism for load balancing messages across multiple consumers.","To provide a mechanism for filtering messages based on content.","To provide a mechanism for encrypting messages at rest.","Virtual Destinations in Active MQ allows you to combine the benefits of both queue and topic messaging."
"What does the term 'message durability' mean in the context of Amazon MQ?","Ensuring that messages are persisted to disk.","Ensuring messages are delivered within a certain time frame.","Ensuring messages are encrypted in transit.","Ensuring messages are delivered only once.","Message durability ensures that messages are not lost in case of broker failure."
"What is the purpose of the Amazon MQ configuration?","To define broker settings like authentication, authorisation and logging.","To define the VPC settings for the broker.","To define the instance type for the broker.","To define the security group rules for the broker.","The Amazon MQ configuration defines broker settings like authentication, authorisation and logging."
"How do you integrate Amazon MQ with other AWS services?","By using AWS SDKs and APIs.","By manually copying data between services.","By using command-line tools only.","By using a third-party integration service","AWS SDKs and APIs provide a programmatic way to interact with Amazon MQ from other AWS services."
"What is the role of AWS CloudTrail in relation to Amazon MQ?","To log API calls made to Amazon MQ.","To monitor the performance of Amazon MQ brokers.","To manage the security groups for Amazon MQ.","To back up the data stored in Amazon MQ.","CloudTrail logs API calls, allowing you to audit who made what changes to your Amazon MQ resources."
"What is the difference between queues and topics in Amazon MQ?","Queues are for point-to-point messaging; topics are for publish-subscribe.","Queues are for publish-subscribe messaging; topics are for point-to-point.","Queues are only for durable messages; topics are only for non-durable messages.","Queues are only for synchronous communication; topics are only for asynchronous communication.","Queues are point to point while topics are publish subscribe."
"How does Amazon MQ handle security updates and patching?","Amazon MQ automatically applies security updates and patches.","Users are responsible for manually applying security updates and patches.","Amazon MQ notifies users of available security updates and patches, but does not automatically apply them.","Amazon MQ does not provide security updates or patches.","Amazon MQ manages security updates and patches automatically to ensure the broker is secure and up-to-date."
"What is the purpose of the network of brokers feature in Amazon MQ?","To create a distributed message broker environment.","To create a backup of your message broker.","To create a test environment for your message broker.","To create a single, larger message broker instance.","Network of brokers in Active MQ allows you to create a distributed message broker environment."
"When should you choose Amazon MQ over Amazon SQS or SNS?","When you need a managed message broker with standard protocols like AMQP or JMS.","When you need a simple, fully managed queue service.","When you need a fully managed publish/subscribe service.","When you need a key-value store.","Amazon MQ provides a fully managed message broker, ideal when migrating from on-premises message brokers or using standard protocols."
"How do you scale an Amazon MQ broker?","By changing the broker instance type.","By adding more queues.","By increasing the storage size.","By increasing the number of consumers.","You can scale an Amazon MQ broker by changing the broker instance type to a larger size."
"What is the purpose of the 'Replication' setting when creating an Amazon MQ broker?","To enable high availability and fault tolerance.","To improve the performance of the broker.","To reduce the cost of the broker.","To encrypt the data stored in the broker.","Replication (using Active/Standby) provides high availability by replicating data to a standby broker."
"How can you connect to an Amazon MQ broker from an application running outside of AWS?","By configuring the broker's security groups to allow inbound traffic from the application's IP address.","By creating a VPN connection between the application's network and the AWS VPC.","By using AWS Direct Connect.","By configuring the broker to be publicly accessible.","Security groups and VPNs ensure secure connections to the Amazon MQ broker from outside of AWS."
"What is the purpose of the Amazon MQ 'Maintenance Window'?","To schedule maintenance tasks like patching and upgrades.","To schedule backups of the broker.","To schedule restarts of the broker.","To schedule scaling operations for the broker.","The maintenance window is the time period when Amazon MQ performs maintenance tasks."
"Which of the following is NOT a supported authentication mechanism for Amazon MQ?","AWS IAM","LDAP","Username and password","Multi-Factor Authentication","Amazon MQ doesn't support Multi-Factor Authentication."
"What is the purpose of the 'dead-letter queue' in Amazon MQ?","To store messages that could not be delivered to their intended destination.","To store messages that have been successfully processed.","To store messages that are waiting to be processed.","To store messages that have expired.","Dead-letter queues store undeliverable messages for later analysis or reprocessing."
"What is the primary benefit of using Amazon MQ for RabbitMQ over self-managing RabbitMQ?","Simplified management and maintenance.","Lower latency.","Greater control over the underlying infrastructure.","Lower cost.","Amazon MQ handles the operational aspects of running a RabbitMQ cluster."
"Which of the following protocols is NOT directly supported by Amazon MQ?","MQTT","STOMP","AMQP","HTTPS","HTTPS is not a protocol used by Amazon MQ for message transfer."
"How can you enforce encryption at rest for messages stored in Amazon MQ?","By using AWS Key Management Service (KMS).","By enabling encryption in transit.","By configuring the broker to use SSL/TLS.","By using client-side encryption.","KMS allows you to encrypt data at rest using AWS managed keys."
"What is the purpose of the Amazon MQ REST API?","To manage and monitor your message brokers programmatically.","To send messages to your message brokers.","To receive messages from your message brokers.","To configure your VPC settings.","The REST API provides a programmatic way to manage brokers."
"How do you monitor the health of the underlying EC2 instances used by Amazon MQ?","Through Amazon CloudWatch metrics.","Through the Amazon MQ console.","Through the EC2 console.","Through AWS Trusted Advisor.","Amazon CloudWatch monitors the underlying EC2 instances."
"What type of network access does Amazon MQ provide?","Private access within your VPC.","Public access over the internet.","Both private and public access, configurable by the user.","Access through a dedicated VPN connection only.","Amazon MQ is only accessible within your VPC via a private IP address."
"Which service can you use to automate the deployment and management of Amazon MQ brokers?","AWS CloudFormation.","AWS Config.","AWS Lambda.","AWS CodeDeploy.","CloudFormation allows you to define and provision Amazon MQ resources using infrastructure as code."
"What is the benefit of using Amazon MQ's automatic backups?","Ensures data recovery in case of broker failure.","Improves the performance of the broker.","Reduces the cost of the broker.","Encrypts the data stored in the broker.","Automatic backups provide a recovery point in case of a disaster or accidental data loss."
"What is a 'broker endpoint' in Amazon MQ?","The URL used to connect to the message broker.","The name of the message queue.","The AWS region where the broker is located.","The IP address of the broker instance.","The broker endpoint is the address that client applications use to connect to the broker."
"What is the purpose of the 'Active MQ Web Console' in Amazon MQ?","To manage and monitor the Active MQ broker through a web browser.","To configure the security groups for the broker.","To configure the VPC settings for the broker.","To write code for your applications.","The Active MQ Web Console provides a web-based interface for managing and monitoring the broker."
"How can you configure Amazon MQ to use a custom domain name?","By using Route 53 and a load balancer.","By configuring the broker's security groups.","By configuring the VPC settings for the broker.","By configuring the broker's DNS settings directly.","You cannot directly configure a custom domain name for Amazon MQ endpoints. The endpoints are provided by AWS within their domain."
"Which of the following is NOT a recommended best practice for securing Amazon MQ?","Using strong passwords for user accounts.","Enabling encryption in transit.","Allowing public access to the broker.","Restricting access to the broker using security groups and IAM.","Allowing public access is generally not recommended."
"How can you monitor the message throughput of your Amazon MQ broker?","Using Amazon CloudWatch metrics like 'MessagesSentToQueue' and 'MessagesReceivedFromQueue'.","Using the Amazon MQ console.","Using the AWS Trusted Advisor.","Using the EC2 console.","These CloudWatch metrics provide insight into the message flow through the broker."
"Which type of message broker offers the possibility of using JMS?","Active MQ","Rabbit MQ","Kafka","Redis","Java Message Service (JMS) is a Java API that allows applications to create, send, receive, and read messages in Active MQ."
"You need to migrate an existing Active MQ broker running on premises to AWS. Which service simplifies this process?","Amazon MQ for Active MQ.","Amazon SQS.","Amazon SNS.","AWS Lambda.","Amazon MQ for Active MQ allows a direct migration of your active mq instance."
"Which of the following is a valid use case for Amazon MQ?","Migrating existing applications that use JMS or AMQP.","Building a serverless application.","Storing large binary files.","Running complex analytical queries.","Amazon MQ simplifies the migration of existing applications that rely on standard messaging protocols."
"What happens to messages in Amazon MQ during a failover event in an Active/Standby setup?","Messages are automatically failed over to the standby broker.","Messages are lost.","Messages are queued on the failed broker until it recovers.","Messages are moved to a dead-letter queue.","In an Active/Standby setup, messages are automatically failed over to the standby broker to minimise downtime."
"Which action is NOT typically performed through the Amazon MQ console?","Viewing broker metrics.","Creating and configuring queues.","Deploying application code.","Managing user access.","The MQ console is for broker administration not for deploying code."
"What is the maximum message size supported by Amazon MQ?","Varies depending on the broker and protocol.","256 KB","1 MB","10 MB","The maximum message size is protocol dependent but generally smaller than SQS."
"When using Amazon MQ, how does the choice of message acknowledgement mode affect message delivery?","It determines whether a message is removed from the queue immediately after being sent or after confirmation of receipt.","It determines the message TTL.","It determines the encryption level of the message.","It determines the order in which messages are processed.","Acknowledgement modes control when messages are removed from the queue, impacting delivery guarantees."
"What is the primary purpose of Amazon MQ?","To provide a managed message broker service","To manage container orchestration","To provide a serverless compute environment","To manage relational databases","Amazon MQ is a managed message broker service that makes it easier to set up and operate message brokers in the cloud."
"Which of the following message brokers does Amazon MQ support?","ActiveMQ and RabbitMQ","Kafka and Kinesis","SQS and SNS","DynamoDB and Cassandra","Amazon MQ currently supports ActiveMQ and RabbitMQ message brokers."
"What is the benefit of using Amazon MQ over self-managing a message broker?","Reduced operational overhead","Increased control over the underlying infrastructure","Lower cost","Higher performance","Amazon MQ reduces the operational overhead of managing a message broker by handling tasks such as patching, upgrades, and monitoring."
"In Amazon MQ, what does a 'broker' represent?","A message broker instance","A queue destination","A message producer","A network interface","A broker in Amazon MQ represents a single message broker instance, either ActiveMQ or RabbitMQ."
"What is the purpose of the Amazon MQ console?","To manage and monitor message brokers","To write code for message processing","To deploy applications","To manage user access to AWS resources","The Amazon MQ console provides a web-based interface for managing and monitoring message brokers, including creating, configuring, and deleting brokers."
"What is the significance of a 'network of brokers' in Amazon MQ?","Provides high availability and scalability","Limits the number of concurrent connections","Simplifies security configuration","Reduces message throughput","A network of brokers in Amazon MQ provides high availability and scalability by distributing messages across multiple brokers."
"Which protocol can be used to communicate with an Amazon MQ ActiveMQ broker?","AMQP","HTTP","SMTP","FTP","AMQP (Advanced Message Queuing Protocol) is a standard protocol used to communicate with ActiveMQ brokers in Amazon MQ."
"Which protocol can be used to communicate with an Amazon MQ RabbitMQ broker?","AMQP","HTTP","SMTP","FTP","AMQP (Advanced Message Queuing Protocol) is a standard protocol used to communicate with RabbitMQ brokers in Amazon MQ."
"What type of encryption does Amazon MQ use to protect data at rest?","AWS Key Management Service (KMS) encryption","SSL/TLS encryption","Password-based encryption","AES-256 encryption managed by Amazon","Amazon MQ uses AWS Key Management Service (KMS) encryption to protect data at rest."
"What type of encryption does Amazon MQ use to protect data in transit?","SSL/TLS encryption","AWS Key Management Service (KMS) encryption","Password-based encryption","AES-256 encryption managed by Amazon","Amazon MQ uses SSL/TLS encryption to protect data in transit."
"Which AWS service can be used to monitor Amazon MQ brokers?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch can be used to monitor various metrics for Amazon MQ brokers, such as CPU utilization, memory usage, and queue depth."
"How does Amazon MQ handle scaling?","Vertical scaling by increasing broker instance size","Horizontal scaling by adding more brokers to a network","Automatic scaling based on message volume","No scaling supported","Amazon MQ supports scaling vertically by increasing the broker instance size, and horizontally by adding more brokers to a network."
"What is the purpose of 'maintenance windows' in Amazon MQ?","To perform broker upgrades and patching","To restrict access to the broker","To back up broker data","To optimize broker performance","Maintenance windows in Amazon MQ are used to perform broker upgrades and patching to ensure the security and stability of the service."
"What is a 'message selector' in Amazon MQ?","A filter to consume specific messages from a queue","A tool to prioritize messages","A component to route messages to different queues","A mechanism to encrypt messages","A message selector in Amazon MQ is a filter that allows consumers to selectively consume messages from a queue based on message properties."
"How does Amazon MQ support high availability?","Through multi-AZ deployments","Through single-AZ deployments only","Through manual failover","High availability is not supported","Amazon MQ supports high availability through multi-AZ deployments, which provide automatic failover to a standby broker in case of an outage."
"What is the role of a 'connection factory' in Amazon MQ?","To create connections to the message broker","To define message formats","To manage user authentication","To handle message routing","A connection factory in Amazon MQ is used to create connections to the message broker, enabling applications to send and receive messages."
"What is the purpose of the Amazon MQ 'dead-letter queue'?","To store messages that could not be delivered","To store messages that were successfully delivered","To store message metadata","To store undelivered acknowledgements","The dead-letter queue in Amazon MQ is used to store messages that could not be delivered to their intended recipients after a certain number of retries."
"Which type of instances can be used with Amazon MQ brokers?","Standard AWS EC2 instances","Dedicated instances only","Spot instances only","Reserved instances only","Amazon MQ brokers can be deployed on standard AWS EC2 instances."
"How can you integrate Amazon MQ with other AWS services?","Using IAM roles and policies","Using VPC peering","Using AWS Direct Connect","Using AWS VPN","IAM roles and policies are used to control access and permissions, enabling seamless integration between Amazon MQ and other AWS services."
"Which of the following is a key difference between Amazon SQS and Amazon MQ?","Amazon MQ supports standard messaging protocols like AMQP, while SQS uses its own proprietary protocol","SQS supports standard messaging protocols like AMQP, while MQ uses its own proprietary protocol","SQS is more expensive than MQ","MQ is serverless, while SQS requires server management","Amazon MQ supports standard messaging protocols like AMQP and JMS, whereas Amazon SQS uses its own proprietary protocol."
"What is the purpose of 'broker logs' in Amazon MQ?","To troubleshoot issues and monitor broker activity","To store message payloads","To manage user authentication","To configure broker settings","Broker logs in Amazon MQ are used to troubleshoot issues and monitor broker activity, providing valuable insights into broker performance and errors."
"What is the maximum message size supported by Amazon MQ?","Limited by the underlying broker engine (e.g., ActiveMQ, RabbitMQ)","256 KB","1 MB","10 MB","The maximum message size supported by Amazon MQ depends on the underlying broker engine (e.g., ActiveMQ, RabbitMQ)."
"How can you authenticate users connecting to an Amazon MQ broker?","Using AWS IAM or broker-specific authentication","Using AWS STS","Using AWS Cognito","Using AWS Directory Service","You can authenticate users connecting to an Amazon MQ broker using AWS IAM or broker-specific authentication mechanisms."
"What is the purpose of using 'virtual destinations' in Amazon MQ ActiveMQ?","To provide a publish-subscribe messaging pattern on top of queues","To improve message encryption","To manage message routing","To enable message compression","Virtual destinations in Amazon MQ ActiveMQ provide a publish-subscribe messaging pattern on top of queues, allowing multiple consumers to receive copies of the same message."
"When should you choose Amazon MQ over Amazon SQS?","When you need compatibility with existing applications that use standard messaging protocols such as AMQP or JMS.","When you need a serverless, fully managed queueing service with virtually unlimited scale.","When you are deploying a new application from scratch.","When you are mainly dealing with media files.","Choose Amazon MQ when you need compatibility with existing applications that use standard messaging protocols such as AMQP or JMS. SQS is preferred for fully managed queueing without protocol dependencies."
"What is the main benefit of using a RabbitMQ broker in Amazon MQ?","Support for more complex routing scenarios","Lower latency","Better integration with AWS Lambda","Easier setup of a network of brokers","RabbitMQ brokers in Amazon MQ support more complex routing scenarios using exchanges and bindings."
"How do you manage access control for Amazon MQ brokers?","Using IAM roles and policies","Using security groups","Using network ACLs","Using VPC endpoints","Access control for Amazon MQ brokers is managed using IAM roles and policies."
"What is the recommended best practice for managing broker configuration in Amazon MQ?","Using infrastructure as code (IaC) tools such as CloudFormation or Terraform","Using the Amazon MQ console directly","Manually configuring the broker instance","Using AWS Config","Using infrastructure as code (IaC) tools such as CloudFormation or Terraform is the recommended best practice for managing broker configuration in Amazon MQ, enabling repeatable and consistent deployments."
"How do you enable audit logging for Amazon MQ brokers?","By enabling CloudTrail data events for Amazon MQ","By enabling CloudWatch Logs","By enabling VPC Flow Logs","By enabling AWS Config Rules","You can enable audit logging for Amazon MQ brokers by enabling CloudTrail data events for Amazon MQ."
"What is the purpose of the 'auto-delete' queue attribute in Amazon MQ ActiveMQ?","To automatically delete the queue when the last consumer disconnects","To automatically delete messages after a certain time","To automatically delete inactive queues","To prevent queues from being deleted","The 'auto-delete' queue attribute in Amazon MQ ActiveMQ automatically deletes the queue when the last consumer disconnects."
"What is the benefit of using 'message groups' in Amazon MQ?","To ensure that messages are processed in a specific order by a single consumer","To improve message encryption","To manage message routing","To enable message compression","Message groups in Amazon MQ are used to ensure that messages are processed in a specific order by a single consumer."
"Which of the following is a valid use case for a network of brokers in Amazon MQ?","Implementing disaster recovery","Reducing latency for local consumers","Simplifying security configuration","Reducing the cost of messaging","A network of brokers in Amazon MQ can be used to implement disaster recovery by replicating messages across multiple brokers in different availability zones or regions."
"How does Amazon MQ support monitoring of message throughput?","Through Amazon CloudWatch metrics","Through broker logs","Through AWS X-Ray","Through AWS Trusted Advisor","Amazon MQ supports monitoring of message throughput through Amazon CloudWatch metrics, which provide insights into the number of messages sent and received."
"What is the purpose of a 'composite queue' in Amazon MQ ActiveMQ?","To combine multiple physical queues into a single logical queue","To encrypt messages across multiple queues","To manage permissions across multiple queues","To compress messages across multiple queues","A composite queue in Amazon MQ ActiveMQ combines multiple physical queues into a single logical queue, allowing consumers to receive messages from multiple queues simultaneously."
"Which feature of Amazon MQ helps prevent message loss in case of broker failure?","Multi-AZ deployment","Single-AZ deployment","Message duplication","Message compression","Multi-AZ deployment in Amazon MQ helps prevent message loss in case of broker failure by providing automatic failover to a standby broker."
"What is the role of the 'destination policy' in Amazon MQ ActiveMQ?","To configure access control, message retention, and other queue-specific settings","To configure network settings for the broker","To manage user authentication","To define message formats","The destination policy in Amazon MQ ActiveMQ is used to configure access control, message retention, and other queue-specific settings for individual queues and topics."
"How does Amazon MQ integrate with VPCs?","Amazon MQ brokers are deployed within a VPC","Amazon MQ brokers can only be accessed through the public internet","Amazon MQ brokers require a dedicated VPC","Amazon MQ brokers use VPC peering","Amazon MQ brokers are deployed within a VPC, providing network isolation and security."
"What is the difference between a queue and a topic in Amazon MQ?","Queues provide point-to-point messaging, while topics provide publish-subscribe messaging","Queues are used for small messages, while topics are used for large messages","Queues are more secure than topics","Topics are more reliable than queues","Queues provide point-to-point messaging, where each message is delivered to one consumer, while topics provide publish-subscribe messaging, where each message is delivered to all subscribers."
"What is the purpose of the 'prefetch size' configuration in Amazon MQ?","To control the number of messages a consumer can receive before acknowledging them","To control the maximum message size","To control the number of messages stored in a queue","To control the number of consumers that can connect to a queue","The 'prefetch size' configuration in Amazon MQ controls the number of messages a consumer can receive before acknowledging them, allowing for more efficient message processing."
"How can you ensure that messages are processed in the order they are sent in Amazon MQ?","Using message groups and a single consumer","Using multiple consumers","Using message prioritization","Using message selectors","You can ensure that messages are processed in the order they are sent in Amazon MQ by using message groups and a single consumer."
"What is the purpose of the 'exclusive consumer' pattern in Amazon MQ?","To ensure that only one consumer receives messages from a queue","To improve message encryption","To manage message routing","To enable message compression","The 'exclusive consumer' pattern in Amazon MQ ensures that only one consumer receives messages from a queue, preventing concurrent processing of the same message."
"How can you scale the performance of an Amazon MQ broker?","By increasing the instance size or adding brokers to a network","By enabling auto-scaling","By increasing the message size limit","By enabling message compression","You can scale the performance of an Amazon MQ broker by increasing the instance size (vertical scaling) or adding brokers to a network (horizontal scaling)."
"Which Amazon MQ feature is helpful for debugging message flow issues?","Message tracing","Message compression","Message encryption","Message prioritization","Message tracing is helpful for debugging message flow issues by tracking the path of messages through the broker."
"What is the impact of using persistent messages in Amazon MQ?","Messages are stored on disk, ensuring they are not lost in case of broker failure","Messages are only stored in memory, resulting in faster delivery","Messages are encrypted at rest","Messages are automatically deleted after a certain time","Using persistent messages in Amazon MQ ensures that messages are stored on disk, so they are not lost in case of broker failure."
"What is the recommended approach for automating the creation and configuration of Amazon MQ brokers?","Using Infrastructure as Code (IaC) tools like CloudFormation or Terraform","Manually configuring brokers through the AWS Management Console","Using shell scripts to automate broker creation","Using AWS CLI commands directly","Using Infrastructure as Code (IaC) tools like CloudFormation or Terraform is the recommended approach for automating the creation and configuration of Amazon MQ brokers, providing repeatable and consistent deployments."
"How can you improve the security of Amazon MQ brokers within your VPC?","By using security groups to control inbound and outbound traffic","By enabling VPC Flow Logs","By enabling AWS Shield","By enabling AWS WAF","You can improve the security of Amazon MQ brokers within your VPC by using security groups to control inbound and outbound traffic, limiting access to authorized resources."
"What should you do if you need to migrate from a self-managed message broker to Amazon MQ with minimal downtime?","Use a bridge to forward messages between the existing broker and Amazon MQ","Shut down the existing broker and migrate all messages at once","Create a snapshot of the existing broker and restore it in Amazon MQ","Recreate the entire application to use Amazon MQ directly","Using a bridge to forward messages between the existing broker and Amazon MQ allows for a gradual migration with minimal downtime."
"What is the benefit of using the JMS (Java Message Service) API with Amazon MQ?","Provides a standard interface for interacting with message brokers","Offers native integration with AWS Lambda","Provides enhanced security features","Increases message throughput","Using the JMS (Java Message Service) API with Amazon MQ provides a standard interface for interacting with message brokers, simplifying application development and portability."
"How does Amazon MQ handle the delivery of messages to multiple consumers subscribing to a topic?","Each consumer receives a copy of the message","Messages are distributed randomly among consumers","One consumer is selected to receive the message","Messages are delivered to consumers based on priority","In a publish-subscribe model, each consumer subscribing to a topic receives a copy of the message."
"What is the role of the AWS Identity and Access Management (IAM) service in relation to Amazon MQ?","IAM controls access to Amazon MQ resources and brokers","IAM manages the networking configuration of Amazon MQ","IAM is responsible for encrypting data in Amazon MQ","IAM handles the automatic scaling of Amazon MQ brokers","AWS IAM controls access to Amazon MQ resources and brokers, allowing you to manage permissions and authentication for users and applications."
