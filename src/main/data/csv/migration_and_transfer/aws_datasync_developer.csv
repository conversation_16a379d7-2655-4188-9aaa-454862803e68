"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary use case for AWS DataSync?","Automating and accelerating data transfers between on-premises storage and AWS storage services","Managing AWS Identity and Access Management (IAM) roles","Monitoring AWS CloudTrail logs","Managing EC2 instances","DataSync is designed for efficient and automated data transfer between on-premises systems and AWS."
"Which AWS service is commonly used as a destination for data transferred by AWS DataSync?","Amazon S3","Amazon EC2","Amazon CloudWatch","Amazon Route 53","DataSync can transfer data to S3 buckets for object storage."
"Which network protocol does AWS DataSync use for data transfer?","NFS and SMB","HTTP","FTP","SMTP","DataSync uses standard NFS and SMB protocols to connect to on-premises storage."
"What security measure is implemented by AWS DataSync during data transfer?","In-transit encryption","Static encryption","Auditing","Multi-factor authentication","DataSync encrypts data in transit using TLS."
"What is an AWS DataSync agent?","A virtual machine deployed on-premises to connect to storage","A web application for managing AWS resources","A command-line tool for interacting with AWS services","A serverless function for data transformation","The DataSync agent is a VM that connects your on-premises storage to AWS."
"When configuring an AWS DataSync task, what determines the source location?","The on-premises or cloud storage system where the data resides","The AWS region where the DataSync agent is located","The IAM role used by the DataSync service","The encryption key used for data transfer","The source location is where the data originates before being transferred."
"How does AWS DataSync handle data integrity during transfer?","Verifies data integrity at each end of the transfer","Assumes data integrity","Relies on the underlying network protocol","Encrypts data but does not verify it","DataSync verifies the integrity of the data at both the source and destination."
"What is the purpose of task scheduling in AWS DataSync?","To automate data transfers on a recurring basis","To manually trigger data transfers","To monitor data transfer performance","To configure encryption settings","Task scheduling allows you to automate data transfers based on a defined schedule."
"Which AWS service can be used to monitor the progress and performance of AWS DataSync tasks?","Amazon CloudWatch","Amazon CloudTrail","Amazon Config","Amazon Inspector","CloudWatch provides metrics and logs for monitoring DataSync tasks."
"What is the role of the AWS DataSync discovery job?","Identify files and directories in a source location","Optimise data transfer speeds","Enforce data encryption","Manage user access","The discovery job is designed to help identify files and folders that can be migrated."
"How does AWS DataSync optimise data transfer speeds?","Using a custom transfer protocol and parallel data streams","Using standard NFS and SMB protocols only","Compressing data before transfer","Limiting bandwidth usage","DataSync uses a purpose-built protocol and parallel data streams to optimise transfer speeds."
"What type of storage systems can act as a source for AWS DataSync?","Network File System (NFS) and Server Message Block (SMB)","Amazon S3 only","Amazon EBS volumes only","Amazon Glacier only","DataSync supports NFS and SMB protocols for accessing on-premises storage."
"When would you use AWS DataSync instead of AWS Storage Gateway?","For automated and accelerated data transfer between on-premises storage and AWS","For presenting on-premises storage as a virtual disk on EC2 instances","For creating a local cache of data stored in S3","For providing a file share interface to S3","DataSync is focused on data transfer, while Storage Gateway provides different storage integration options."
"What is a 'Task Execution' in AWS DataSync?","A single run of a DataSync task","The definition of a DataSync task","The DataSync agent software","The configuration of the source location","A Task Execution represents a single instance of a DataSync task being run."
"How does AWS DataSync handle file permissions and metadata during data transfer?","Preserves file permissions and metadata","Removes file permissions and metadata","Converts file permissions to a standard format","Ignores file permissions and metadata","DataSync preserves file permissions and metadata during the transfer process."
"What is the cost model for AWS DataSync?","Pay-as-you-go based on the amount of data copied","Fixed monthly fee","Hourly rate for the DataSync agent","Free of charge","DataSync charges you based on the amount of data you copy."
"Which of the following can AWS DataSync transfer data to?","Amazon EFS","Amazon EC2","Amazon VPC","AWS Lambda","AWS DataSync can transfer data to Amazon EFS, Elastic File System."
"Which of the following is NOT a supported feature of AWS DataSync?","Real-time data replication","Data filtering","Data compression","Encryption during transit","Real-time data replication is NOT a feature of DataSync, it is batch oriented."
"What is the maximum size of a single file that can be transferred by AWS DataSync?","Limited by the target storage service","1 TB","10 TB","Unlimited","The maximum file size that can be transferred is determined by the destination storage service, like S3 or EFS."
"What is the primary benefit of using AWS DataSync for migrating data to AWS?","Reduced migration time and cost","Increased security of data","Simplified application deployment","Automated server patching","AWS DataSync primarily reduces migration time and cost by optimising data transfer."
"What is the role of the AWS DataSync console?","To manage and monitor DataSync tasks and agents","To configure EC2 instances","To monitor network traffic","To manage IAM users","The DataSync console provides a central interface for managing tasks and agents."
"Which of the following can be configured for an AWS DataSync task?","Data transfer schedule","EC2 instance size","S3 bucket lifecycle policy","IAM user permissions","A DataSync task can be configured with a schedule to automate data transfers."
"What is the purpose of the AWS DataSync location?","To define the source or destination of the data transfer","To specify the AWS region","To configure network settings","To define the encryption key","A DataSync location defines either the source or destination endpoint for the data transfer."
"What type of compression is used by AWS DataSync during data transfer?","DataSync does not perform compression","GZIP","ZIP","LZ4","DataSync does not perform compression. Compression should be configured independently at the source if required."
"Which of the following AWS services integrates with DataSync to provide monitoring and alerting capabilities?","Amazon CloudWatch","Amazon SQS","Amazon SNS","Amazon SES","DataSync integrates with Amazon CloudWatch to enable monitoring and alerting based on metrics and logs."
"What happens to the source data after a successful AWS DataSync transfer?","The source data remains unchanged","The source data is automatically deleted","The source data is archived","The source data is compressed","After a successful transfer, the source data remains unchanged by default."
"What type of connection does an AWS DataSync Agent require to the AWS Cloud?","Outbound HTTPS connection to AWS public endpoints","Inbound SSH connection from AWS","Direct Connect link","VPN connection","The DataSync Agent needs an outbound HTTPS connection to AWS public endpoints to communicate with the DataSync service."
"Which of the following is a limitation of AWS DataSync?","Cannot transfer data to S3 Glacier","Requires a static IP address for the agent","Only supports NFS protocol","Cannot transfer data over the internet","AWS DataSync supports transfer to S3 Glacier. It does not have a limitation on transfering data over the internet."
"How does AWS DataSync handle the transfer of symbolic links?","Transfers symbolic links as files","Skips symbolic links","Converts symbolic links to hard links","Fails the transfer if symbolic links are present","DataSync transfers symbolic links as files, effectively copying the link target as a file."
"What is the best practice for securing the AWS DataSync agent?","Limit network access to the agent","Disable encryption on the agent","Use a public IP address for the agent","Store the agent's private key in S3","Limiting network access to the agent enhances its security."
"What is the effect of enabling 'verify only' in an AWS DataSync task configuration?","Data is verified without being transferred","Data is transferred without verification","Only small files are transferred and verified","Only encrypted files are transferred and verified","Enabling 'verify only' causes DataSync to verify the integrity of data at the destination without transferring any new data."
"How does AWS DataSync handle sparse files?","Preserves sparse files","Converts sparse files to regular files","Skips sparse files","Compresses sparse files","DataSync preserves sparse files. This is important because some file systems use sparse files to efficiently represent large files."
"What is the purpose of configuring a private endpoint for DataSync in your VPC?","To improve security and reduce exposure to the public internet","To increase data transfer speeds","To simplify the configuration of DataSync","To reduce DataSync costs","Configuring a private endpoint for DataSync improves security and reduces exposure to the public internet by keeping traffic within your VPC."
"Which IAM permission is required for the DataSync agent to access your S3 bucket?","s3:ListBucket and s3:GetObject","ec2:RunInstances","cloudwatch:PutMetricData","iam:CreateRole","The DataSync agent requires 's3:ListBucket' and 's3:GetObject' permissions to access the S3 bucket when you wish to transfer to S3 as destination."
"What is the purpose of DataSync's data filtering feature?","To selectively transfer files based on criteria such as file size or modification time","To encrypt data before transfer","To compress data during transfer","To improve the speed of data transfer","Data filtering allows you to selectively transfer files based on criteria such as file size or modification time."
"How can you ensure high availability of the DataSync Agent?","Deploy multiple DataSync Agents in different Availability Zones","Configure automatic failover in the DataSync console","Use AWS Auto Scaling to manage the agent","High availability is not supported for DataSync agents","Multiple DataSync Agents should be deployed in different Availability Zones in order to enable high availability."
"What is the recommended way to update an AWS DataSync agent?","The agent automatically updates itself","Download and install the latest agent version from the AWS console","Use AWS Systems Manager to patch the agent","Re-register the agent","The AWS DataSync agent can only be manually updated by downloading and installing the latest version from the AWS console."
"What is the role of IAM when setting up AWS DataSync?","To control access to DataSync resources and data locations","To manage network connectivity","To configure data encryption","To set up data transfer schedules","IAM is used to control access to DataSync resources (tasks, agents, locations) and the underlying data locations (S3 buckets, NFS shares)."
"Which AWS service can be used to notify you upon the completion of a DataSync task?","Amazon SNS","Amazon SQS","Amazon SES","Amazon CloudWatch Events","Amazon SNS can be used with CloudWatch Events to send notifications when a DataSync task completes."
"How does AWS DataSync ensure data consistency between the source and destination?","By verifying data integrity at the source and destination","By automatically retrying failed transfers","By using checksums","By using RAID arrays","DataSync ensures data consistency by verifying data integrity (checksums) at both the source and the destination."
"What is the impact of changing the IAM role associated with an AWS DataSync task?","The task may lose access to the source or destination location","The task will automatically restart","The task's data transfer schedule will be reset","The task's data filtering settings will be erased","Changing the IAM role can affect the task's ability to access the source or destination location if the new role does not have the necessary permissions."
"How can you determine the optimal number of parallel operations for an AWS DataSync task?","Experiment by monitoring the task's performance in CloudWatch","The number of parallel operations is automatically determined by DataSync","Consult the DataSync documentation for recommended values","The number of parallel operations is fixed and cannot be changed","The best approach is to experiment and monitor the task's performance in CloudWatch to determine the optimal number of parallel operations for your specific workload and network conditions."
"What is the purpose of pre-copying data with AWS DataSync before an initial cutover migration?","To reduce the downtime during the final cutover","To improve the speed of the cutover process","To test the DataSync configuration","To encrypt the data","Pre-copying data minimizes downtime during the cutover by transferring the bulk of the data beforehand. Then, during the cutover window, only the changes (deltas) need to be copied."
"What is the advantage of using AWS DataSync over rsync for data transfer?","DataSync provides automated data transfer and data integrity verification","Rsync supports data integrity verification","Rsync has a simplified configuration","DataSync does not support incremental backups","DataSync automates and accelerates data transfer while also verifying data integrity."
"How does AWS DataSync handle data deduplication?","DataSync does not perform data deduplication","DataSync automatically deduplicates data before transfer","DataSync can be configured to deduplicate data","DataSync relies on destination to perform data deduplication","DataSync does not perform data deduplication. Deduplication would need to be handled by the storage solution."
"What happens if the AWS DataSync agent loses network connectivity during a data transfer?","The task will pause and automatically resume when connectivity is restored","The task will fail and need to be restarted manually","The task will continue using cached data","The task will switch to a different agent","The task will pause and automatically resume when connectivity is restored. DataSync is designed to be resilient to temporary network interruptions."
"What is the benefit of using AWS DataSync with AWS Direct Connect?","Increased data transfer speeds and reduced latency","Automatic data encryption","Simplified task configuration","Reduced DataSync costs","Using AWS Direct Connect provides a dedicated network connection, which can result in increased data transfer speeds and reduced latency compared to transferring data over the public internet."
"Which statement best describes the AWS DataSync agent?","Software that connects to your storage and communicates with AWS","EC2 instance launched on your behalf","AWS managed service","A load balancer","The DataSync agent is a virtual machine or software appliance that you deploy on-premises or in the cloud to connect your storage to AWS."
"What is a common use case for AWS DataSync to move data into Amazon FSx?","Migrating on-premises file shares to a fully managed file system in AWS","Backing up EC2 instances to FSx","Monitoring FSx file system performance","Encrypting data stored in FSx","DataSync helps migrate on-premises file shares to FSx for a fully managed file system experience in AWS."
"What is the primary function of AWS DataSync?","Automated data transfer between on-premises storage and AWS services","Real-time video transcoding","Managing serverless applications","Hosting static websites","DataSync automates and accelerates data transfer between on-premises storage and AWS, simplifying migration and replication tasks."
"Which AWS service is commonly used as a destination for data transferred by AWS DataSync?","Amazon S3","Amazon CloudWatch","Amazon Lex","Amazon Pinpoint","DataSync is often used to move data to Amazon S3 for storage, analytics, or other processing."
"What type of data transfer does AWS DataSync support?","Block-level incremental data transfer","Full volume snapshots only","Object-level full data transfer only","Metadata-only transfer","DataSync supports block-level incremental data transfer, transferring only the changed portions of files, which improves efficiency and speed."
"How does AWS DataSync ensure data integrity during transfer?","By performing checksum verification","By using encryption at rest","By limiting transfer speeds","By compressing the data","DataSync performs checksum verification to ensure that the data transferred is identical to the source data, guaranteeing data integrity."
"Which protocol does AWS DataSync use to communicate with on-premises storage systems?","NFS or SMB","HTTP","FTP","SMTP","DataSync uses NFS (Network File System) or SMB (Server Message Block) to connect to on-premises file systems, allowing it to access and transfer data."
"What is an AWS DataSync agent?","A virtual machine deployed on-premises","A serverless function","A container image","An AWS managed service","The DataSync agent is a virtual machine that is deployed on-premises to access the source storage system and transfer data to AWS."
"What is a DataSync task in AWS DataSync?","A configuration that defines the source, destination, and schedule for data transfer","A CloudWatch alarm","An IAM role","An EC2 instance","A DataSync task defines the source, destination, and schedule for data transfer, encapsulating the configuration for a specific data transfer job."
"What is the purpose of the AWS DataSync discovery jobs?","Discovering storage configuration to reduce migration project risk","Monitoring network traffic","Analysing security vulnerabilities","Deploying the DataSync agent","DataSync Discovery helps reduce risk and accelerate migrations by discovering storage configuration, capacity, and performance metrics for on-premises file servers."
"When configuring an AWS DataSync task, what can you specify to control the network bandwidth usage?","Bandwidth throttling","Region selection","Security group settings","Storage class","DataSync allows you to control network bandwidth usage by specifying bandwidth throttling settings, preventing it from consuming all available bandwidth."
"What is the cost model for AWS DataSync?","Pay-as-you-go based on the amount of data transferred","Fixed monthly fee","Free for AWS customers","Hourly rate","DataSync charges are based on a pay-as-you-go model, with costs determined by the amount of data transferred from the source to the destination."
"What type of encryption does AWS DataSync use for data in transit?","TLS","AES-256","RSA","DES","DataSync uses TLS (Transport Layer Security) encryption for data in transit, securing the data as it is being transferred over the network."
"What IAM permissions are required for the AWS DataSync agent to access an S3 bucket?","Permissions to read and write objects to the S3 bucket","Permissions to manage IAM roles","Permissions to launch EC2 instances","Permissions to create VPCs","The DataSync agent needs permissions to read and write objects to the S3 bucket to be able to transfer data to it."
"What is the benefit of using AWS DataSync for migrating large amounts of data to AWS?","It accelerates the migration process","It provides real-time data analytics","It simplifies application deployment","It automates infrastructure provisioning","DataSync is designed to accelerate the migration of large amounts of data to AWS, significantly reducing the time required for migration."
"Can AWS DataSync be used to transfer data between two S3 buckets in different AWS Regions?","No, DataSync cannot be used to transfer between S3 buckets.","Yes, DataSync can be used to transfer between S3 buckets in different Regions.","Only if the S3 buckets are in the same AWS account","Only if cross-region replication is enabled","DataSync supports data transfer between S3 buckets in different AWS Regions, providing a way to replicate data across geographical locations."
"What type of storage system can be used as a source for data migration with AWS DataSync?","NFS file servers","CloudWatch Logs","DynamoDB tables","Lambda functions","DataSync supports NFS (Network File System) file servers as a source for data migration, allowing you to migrate data from on-premises NFS file systems to AWS."
"Which AWS service can be used to monitor the progress and performance of AWS DataSync tasks?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch can be used to monitor the progress and performance of DataSync tasks, providing metrics and logs for tracking the data transfer."
"How can you ensure that data is transferred securely between your on-premises environment and AWS using DataSync?","By using an AWS DataSync agent deployed in a private subnet with limited internet access","By disabling encryption","By using public IP addresses for all resources","By storing the AWS access keys on the on-premises server","Deploying the DataSync agent in a private subnet with limited internet access enhances security by restricting network access and minimising exposure to external threats."
"What is the purpose of creating a task in AWS DataSync?","To define the source and destination of the data transfer","To create an EC2 instance","To configure a load balancer","To create a VPC","A task defines the source and destination of the data transfer, along with other settings such as bandwidth limits and scheduling."
"What happens if the AWS DataSync agent loses connectivity during a data transfer task?","The transfer will pause and resume automatically when connectivity is restored","The transfer will be cancelled and data lost","The transfer will continue from a different agent","The agent will be automatically redeployed","DataSync is designed to handle network interruptions gracefully. The transfer will pause and resume automatically when connectivity is restored, ensuring that data is not lost."
"When using AWS DataSync, how does it handle file permissions and ownership?","It can preserve file permissions and ownership during transfer","It automatically sets default permissions","It removes all permissions","It converts all permissions to read-only","DataSync can preserve file permissions and ownership during transfer, ensuring that the data retains its original access controls when moved to AWS."
"How does AWS DataSync handle deleting files on the destination that no longer exist on the source?","It can be configured to delete files on the destination","It always deletes files on the destination","It never deletes files on the destination","It moves the files to a separate archive","DataSync can be configured to delete files on the destination that no longer exist on the source, allowing you to keep the source and destination synchronised."
"What is the main advantage of using AWS DataSync over using rsync or other traditional file transfer tools?","DataSync is optimised for AWS and provides better performance and security","rsync is cheaper","rsync is easier to configure","DataSync supports more file systems","DataSync is optimised for AWS and provides better performance and security compared to rsync, it integrates seamlessly with AWS services and provides features like encryption and data integrity checks."
"What is the first step when setting up AWS DataSync to transfer data from an on-premises file server to an AWS S3 bucket?","Deploy an AWS DataSync agent on-premises","Create an S3 bucket","Configure IAM roles","Configure security groups","The first step is to deploy an AWS DataSync agent on-premises, allowing DataSync to access the on-premises file server and transfer data to AWS."
"What is the purpose of the ‘verifyMode’ parameter in an AWS DataSync task configuration?","To specify how DataSync verifies data integrity after the transfer","To set the level of encryption","To configure network throttling","To define the schedule for the transfer","The `verifyMode` parameter specifies how DataSync verifies data integrity after the transfer, ensuring that the data transferred is identical to the source data."
"If you need to replicate data from an on-premises NAS to AWS S3 for disaster recovery, what service should you use?","AWS DataSync","AWS Storage Gateway","AWS Backup","AWS Direct Connect","DataSync is ideal for replicating data from on-premises storage to AWS for disaster recovery as it automates the transfer and ensures data integrity."
"Which AWS DataSync feature helps to minimise the impact on on-premises network resources during data transfer?","Bandwidth throttling","Data compression","Encryption","Deduplication","Bandwidth throttling allows you to limit the amount of network bandwidth used by DataSync, minimising the impact on on-premises network resources."
"What can you do if you encounter performance bottlenecks when using AWS DataSync?","Increase the bandwidth limit","Deploy multiple DataSync agents","Decrease the MTU size","Reduce the number of files transferred","Deploying multiple DataSync agents can improve transfer performance by distributing the workload across multiple agents."
"What AWS service is used to store the DataSync agent's activation key?","AWS Secrets Manager","AWS KMS","AWS IAM","AWS CloudHSM","The DataSync agent's activation key is securely stored in AWS Secrets Manager, providing a secure way to manage and retrieve the activation key when needed."
"What is the advantage of using the 'preserveDeletedFiles' option in AWS DataSync?","It prevents DataSync from deleting files on the destination","It enables data compression","It enables encryption in transit","It allows you to monitor the transfer progress","The `preserveDeletedFiles` option prevents DataSync from deleting files on the destination that have been deleted from the source, useful for maintaining a complete copy of the data."
"How does AWS DataSync handle metadata associated with files?","It can preserve metadata such as timestamps and permissions","It strips all metadata from the files","It converts the metadata to a different format","It stores metadata in a separate database","DataSync can preserve metadata such as timestamps and permissions during the transfer, ensuring that the data retains its original attributes."
"What is the benefit of using AWS DataSync for periodic data synchronisation?","Automated and scheduled transfers","Real-time data analytics","Simplified application deployment","Automated infrastructure provisioning","DataSync automates and schedules data transfers, making it ideal for periodic data synchronisation between on-premises storage and AWS."
"What is the maximum file size that AWS DataSync can transfer?","There is no practical limit","1 TB","5 TB","10 TB","DataSync has no practical limit on the file size that it can transfer, making it suitable for transferring large files and datasets."
"Which AWS service can be used to receive notifications about the status of AWS DataSync tasks?","Amazon EventBridge","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon EventBridge can be used to receive notifications about the status of DataSync tasks, allowing you to monitor and respond to events such as successful transfers or errors."
"How does AWS DataSync handle symbolic links?","It can preserve symbolic links","It converts symbolic links to regular files","It skips symbolic links","It deletes symbolic links","DataSync can preserve symbolic links during the transfer, ensuring that the symbolic links are maintained in the destination."
"If you want to transfer data from an on-premises Windows file server to AWS, which protocol should you configure AWS DataSync to use?","SMB","NFS","HTTP","FTP","For Windows file servers, DataSync should be configured to use the SMB (Server Message Block) protocol to access and transfer data."
"What type of encryption does AWS DataSync use for data at rest in Amazon S3?","Server-side encryption","Client-side encryption","No encryption","Volume encryption","DataSync leverages S3's server-side encryption for data at rest, securing data within the S3 bucket."
"Can you use AWS DataSync to migrate data from one AWS Region to another?","Yes, DataSync supports cross-region data transfer","No, DataSync only supports on-premises to AWS transfers","Only if the regions are in the same AWS account","Only if cross-region replication is enabled","DataSync can be used to migrate data between AWS Regions, providing flexibility in replicating data across different geographical locations."
"When configuring AWS DataSync, what is the purpose of the 'exclude' filter?","To exclude specific files or directories from the transfer","To exclude specific users from accessing the data","To exclude specific network ranges from the transfer","To exclude specific S3 bucket policies","The 'exclude' filter allows you to specify files or directories that should be excluded from the data transfer, providing granular control over what data is transferred."
"Which of the following storage types is NOT supported as a source for AWS DataSync?","HDFS","NFS","SMB","Object Storage","HDFS (Hadoop Distributed File System) is not directly supported as a source for DataSync. DataSync primarily supports NFS and SMB file systems."
"What is a 'location' in the context of AWS DataSync?","A network file system or S3 bucket used as a data source or destination","A geographical region where the DataSync agent is deployed","A specific directory on an EC2 instance","A container image","A 'location' in DataSync refers to a network file system or S3 bucket that is used as a source or destination for data transfer."
"Which AWS DataSync feature provides the ability to copy only the data that has changed since the last transfer?","Incremental transfer","Full transfer","Differential transfer","Snapshot transfer","DataSync uses incremental transfer to copy only the data that has changed since the last transfer, improving efficiency and speed."
"What is the primary reason to choose AWS DataSync over other file transfer methods for large-scale data migration?","Optimised for performance, security, and integration with AWS services","It is free of charge","It has a user-friendly GUI","It offers unlimited storage","DataSync is optimised for performance, security, and integration with AWS services, making it a robust and efficient solution for large-scale data migration."
"In AWS DataSync, what does the term 'agent activation' refer to?","Linking an on-premises agent to your AWS account","Starting an EC2 instance","Configuring an IAM role","Creating an S3 bucket","Agent activation is the process of linking an on-premises DataSync agent to your AWS account, enabling it to securely transfer data to AWS."
"What should you configure in your firewall to allow the DataSync agent to communicate with AWS?","Allow outbound traffic to AWS service endpoints on ports 80 and 443","Disable the firewall completely","Allow inbound traffic from AWS","Restrict all outbound traffic","To allow the DataSync agent to communicate with AWS, you need to allow outbound traffic to AWS service endpoints on ports 80 and 443."
"How does AWS DataSync handle errors during data transfer?","It retries failed transfers automatically","It immediately stops the entire transfer","It skips the failed files","It deletes the files that caused the error","DataSync retries failed transfers automatically, ensuring that data is eventually transferred successfully even if errors occur during the process."
"Which AWS service is used for the initial deployment and management of the AWS DataSync agent?","AWS Management Console","AWS CLI","AWS CloudFormation","AWS Systems Manager","The AWS Management Console is often used for the initial deployment and management of the DataSync agent, providing a user-friendly interface for configuring and monitoring the agent."
"Which of the following is a key factor in determining the overall transfer speed achievable with AWS DataSync?","Network bandwidth","CPU usage","Storage capacity","Number of IAM roles","Network bandwidth is a key factor in determining the transfer speed, as it limits the amount of data that can be transferred over the network in a given time."
"If your AWS DataSync task is consistently failing, what is the first thing you should check?","The IAM permissions of the DataSync agent","The S3 bucket versioning settings","The EC2 instance type","The CloudWatch Logs settings","The first thing to check is the IAM permissions of the DataSync agent to ensure that it has the necessary permissions to access the source and destination storage."
"Which DataSync setting directly impacts the network throughput consumed by the data transfer process?","Bandwidth limit","Encryption level","Storage class","File system type","The bandwidth limit setting directly impacts the network throughput consumed by the data transfer process, allowing you to control how much bandwidth DataSync uses."
"What is the primary function of AWS DataSync's reporting features?","Providing visibility into data transfer performance and status","Generating compliance reports","Automatically creating backups","Monitoring security vulnerabilities","The reporting features provide visibility into data transfer performance and status, helping you monitor the progress and identify any potential issues."
"What configuration setting is most critical for ensuring data consistency when transferring data with AWS DataSync?","Verify mode","Bandwidth limit","Encryption type","Schedule","The verify mode configuration setting is most critical for ensuring data consistency as it dictates how DataSync verifies the integrity of the transferred data."
"What is the primary use case for AWS DataSync?","Automating and accelerating data transfer between on-premises storage and AWS.","Real-time video transcoding.","Managing serverless application deployments.","Analysing large datasets using SQL.","DataSync is designed to move large amounts of data quickly and securely between on-premises storage and AWS storage services."
"Which AWS service can be used as a destination for data transferred by AWS DataSync?","Amazon S3","Amazon EC2","AWS Lambda","Amazon CloudFront","DataSync directly integrates with S3 as a destination for transferring data to AWS."
"What protocol does AWS DataSync use to transfer data over the network?","NFS or SMB","FTP","HTTP","SMTP","DataSync uses NFS or SMB protocols to connect to your on-premises file system and transfer the data."
"When configuring an AWS DataSync task, what is an 'agent'?","A software appliance deployed in your on-premises environment to access your storage.","A managed service that monitors data transfer performance.","A security group that controls access to the DataSync service.","A virtual private cloud (VPC) endpoint for DataSync.","The DataSync agent is a virtual machine that you deploy on-premises to connect to your storage system and facilitate data transfer."
"What is a benefit of using AWS DataSync over transferring data manually?","DataSync automates and optimises the transfer process, reducing transfer time and management overhead.","DataSync is always free to use.","DataSync automatically encrypts data at rest.","DataSync can transfer data directly to Glacier.","DataSync automates the process, handles encryption in transit, verifies data integrity, and optimises transfer speeds."
"What type of encryption does AWS DataSync use to encrypt data in transit?","TLS","AES-256","RSA","MD5","DataSync uses TLS (Transport Layer Security) to encrypt data as it is transferred between your on-premises storage and AWS."
"Which of the following AWS services can be integrated with AWS DataSync for monitoring and logging data transfer activities?","Amazon CloudWatch","AWS Config","AWS IAM","Amazon Route 53","Amazon CloudWatch can be used to monitor metrics related to DataSync tasks, such as bytes transferred, files copied, and errors encountered."
"What is the main advantage of using AWS DataSync's built-in data validation feature?","Ensuring data integrity during transfer and preventing data corruption.","Reducing the cost of data transfer.","Increasing the speed of data transfer.","Automatically converting data formats.","Data validation in DataSync verifies that the data transferred is identical to the source data, ensuring data integrity."
"What can AWS DataSync be used for in a hybrid cloud architecture?","Migrating data from on-premises file servers to Amazon S3 for cloud storage.","Deploying applications to on-premises servers from AWS.","Managing network configurations across hybrid environments.","Monitoring the health of on-premises hardware.","DataSync is commonly used for migrating and synchronising data between on-premises data centres and AWS in a hybrid cloud setup."
"What is the purpose of scheduling tasks in AWS DataSync?","To automate data transfers at regular intervals.","To manually start data transfers.","To configure network settings.","To define storage policies.","Scheduling tasks in DataSync allows you to automate data transfers on a recurring basis, such as daily or weekly backups."
"What type of data transfer does AWS DataSync *not* support?","Offline data transfer using physical storage devices.","Online data transfer over the network.","Incremental data transfer.","Scheduled data transfer.","DataSync is designed for online data transfer over the network and does not offer any support for offline data transfer using physical devices."
"When configuring an AWS DataSync task, what is the purpose of bandwidth throttling?","To limit the amount of bandwidth DataSync uses during data transfer.","To increase the speed of data transfer.","To compress the data before transfer.","To encrypt the data during transfer.","Bandwidth throttling helps to avoid saturating the network and allows other applications to continue using the network without being significantly impacted."
"What security measure is automatically applied to data transferred by AWS DataSync during transit?","TLS encryption","Server-Side Encryption (SSE)","Client-Side Encryption (CSE)","No encryption is applied by default","DataSync encrypts all data in transit using Transport Layer Security (TLS), ensuring secure transfer over the network."
"If a file already exists in the destination during an AWS DataSync task, what is the default behaviour of DataSync?","DataSync overwrites the file if it has changed.","DataSync skips the file.","DataSync creates a new version of the file.","DataSync stops the entire task with an error.","By default, DataSync checks if the file has changed since the last transfer. If so, it overwrites the file in the destination. Otherwise it skips the copy."
"With AWS DataSync, what resource is required to access an NFS file server in your on-premises environment?","A DataSync Agent","An IAM Role","A VPC Endpoint","A Storage Gateway","A DataSync Agent needs to be deployed and configured within the same network as the NFS server to enable DataSync to access and transfer data from it."
"What is the maximum file size supported by AWS DataSync?","No limit","5 TB","10 TB","20 TB","DataSync does not impose a limit on the size of individual files that can be transferred."
"In AWS DataSync, what does 'verifyMode' in task configuration control?","Whether DataSync verifies data integrity after the transfer","Whether DataSync encrypts the data at rest","Whether DataSync compresses data during the transfer","Whether DataSync skips directories","The `verifyMode` setting in DataSync determines whether DataSync will perform a data integrity verification after the transfer to ensure that data was copied correctly."
"Which of the following protocols is supported by AWS DataSync for accessing data on network file systems?","NFS and SMB","HTTP and HTTPS","FTP and SFTP","SMTP and POP3","DataSync supports NFS (Network File System) and SMB (Server Message Block) protocols for accessing data on network file systems."
"Which AWS service integrates with AWS DataSync to provide a fully managed NFS file server in the cloud?","Amazon EFS","Amazon S3","Amazon EBS","Amazon FSx","Amazon EFS (Elastic File System) is a fully managed NFS file system that can be used as a destination for data transferred by DataSync."
"What type of storage location cannot be used as a source location for AWS DataSync?","Object Storage","SMB file share","NFS file share","AWS Snowball Edge","AWS Snowball Edge cannot be a source location. DataSync can transfer data to Snowball Edge but not from it."
"In AWS DataSync, what is the function of Task Scheduling?","To automatically run data transfers on a recurring basis","To manually trigger data transfers","To optimise network bandwidth usage","To monitor the status of data transfers","Task Scheduling in DataSync allows you to automate data transfers at specified intervals, such as daily, weekly, or monthly."
"Which AWS service can you use to monitor the progress and performance of AWS DataSync tasks?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and logs related to DataSync tasks, allowing you to monitor transfer progress, performance, and any errors."
"When using AWS DataSync to transfer data to Amazon S3, which storage class offers the lowest storage cost but may incur retrieval fees?","S3 Glacier Deep Archive","S3 Standard","S3 Intelligent-Tiering","S3 Standard Infrequent Access","S3 Glacier Deep Archive offers the lowest storage cost for archiving data, but it has the highest retrieval costs and longest retrieval times, making it suitable for rarely accessed data."
"What is the benefit of using AWS DataSync over rsync for data transfer?","DataSync is fully managed, supports encryption in transit, and optimises network usage","Rsync offers better security features","Rsync allows for more granular control over file selection","Rsync is always faster than DataSync","DataSync is a fully managed service that automates and optimises data transfer, includes encryption in transit, handles network optimisation, and is purpose-built for AWS services."
"If an AWS DataSync task fails repeatedly, what is the first step you should take to troubleshoot the issue?","Check the DataSync agent's logs and CloudWatch metrics for errors","Increase the allocated bandwidth for the DataSync task","Recreate the DataSync task","Contact AWS Support","Examining the DataSync agent's logs and CloudWatch metrics provides valuable information about the cause of the failures, such as network connectivity issues, permission errors, or storage access problems."
"Which network protocol is required for AWS DataSync to connect to an on-premises Network File System (NFS) export?","NFS v3","SMB","iSCSI","SFTP","DataSync requires NFS v3 to connect to and transfer data from on-premises NFS exports."