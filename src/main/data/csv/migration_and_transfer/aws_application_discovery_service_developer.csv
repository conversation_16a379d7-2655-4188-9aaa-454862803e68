"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Application Discovery Service, what is the primary function of the agentless discovery method?","Discovering server metadata and performance data without installing agents","Migrating physical servers to AWS","Creating a detailed application dependency map automatically","Generating cost estimates for cloud migration","Agentless discovery uses service APIs to gather information, avoiding the need to install software on the servers being discovered."
"What type of data does the AWS Application Discovery Service agent collect?","Detailed system performance metrics and application dependencies","Network bandwidth usage and packet loss","User login activity and security vulnerabilities","Personally identifiable information (PII) and sensitive data","The agent collects a broad range of system performance metrics, running processes and details of network connections which are used to build application dependency maps."
"Which AWS service is commonly used to visualise and analyse the data collected by AWS Application Discovery Service?","AWS Migration Hub","Amazon CloudWatch","AWS Config","Amazon Inspector","AWS Migration Hub integrates with Application Discovery Service to present the discovered inventory and dependency information, helping with migration planning."
"What is the purpose of the 'discovery connector' in AWS Application Discovery Service when using agentless discovery?","To securely communicate with and retrieve data from the discovered servers","To install software agents on the discovered servers remotely","To encrypt all network traffic between AWS and the discovered environment","To provide a central repository for all discovered data","The discovery connector acts as a secure conduit to communicate with your on-premises environment via APIs, collecting data without agents."
"Which of the following is a key benefit of using AWS Application Discovery Service for cloud migration planning?","It provides a comprehensive inventory of your existing infrastructure.","It automatically migrates your applications to AWS.","It replaces the need for any manual assessment of your environment.","It eliminates all security vulnerabilities during migration.","Application Discovery Service builds a detailed inventory and dependency map of your environment, crucial for effective cloud migration planning."
"What information does AWS Application Discovery Service typically provide about application dependencies?","The communication patterns between different applications and servers","The amount of CPU usage of each application","The geographical location of each server","The names of the developers responsible for each application","Application Discovery Service identifies how applications communicate with each other and other servers, providing insight into application dependencies."
"Which AWS service can be used to group and track applications discovered by AWS Application Discovery Service for migration purposes?","AWS Migration Hub","AWS Systems Manager","AWS CloudTrail","Amazon Route 53","AWS Migration Hub provides a central location to group applications and track their migration progress, integrating with Application Discovery Service for inventory and dependency information."
"Which discovery method in AWS Application Discovery Service is suitable when you require in-depth application dependency mapping, including process-level details?","Agent-based discovery","Agentless discovery","Manual inventory collection","CloudFormation template deployment","Agent-based discovery provides the most granular level of detail including process-level information and application dependencies."
"What is a potential limitation of using agentless discovery with AWS Application Discovery Service?","It may not capture detailed process-level information.","It requires installation of software on every server.","It can only discover AWS resources.","It is significantly more expensive than agent-based discovery.","Agentless discovery relies on APIs, so it might not capture the same level of detail, particularly process-level information, as agent-based discovery."
"When performing agent-based discovery using AWS Application Discovery Service, what type of permission is required on the target servers?","The agent requires administrative or root privileges","The agent requires no permissions on the server","The agent requires read-only access to the network configuration","The agent requires access to the AWS Management Console","The agent needs administrative or root privileges to access and collect detailed system information on the target server."
"Which security best practice should be followed when deploying AWS Application Discovery Service agents on your servers?","Use the principle of least privilege when configuring agent access rights.","Grant the agent full administrative access to the entire network.","Disable encryption of data transmitted by the agent.","Share the agent's access keys with all users.","Following the principle of least privilege ensures that the agent has only the necessary permissions, minimising the security risk."
"What is the primary purpose of the AWS Application Discovery Service Migration Hub import functionality?","To manually upload discovered inventory data from external sources","To automatically migrate applications to AWS","To create custom dashboards for monitoring migration progress","To schedule automated discovery scans","Migration Hub import allows you to manually upload data from other discovery tools or spreadsheets, integrating external inventory data into Migration Hub."
"Which of the following is NOT a supported output format for AWS Application Discovery Service data exports?","CSV","JSON","XML","PDF","AWS Application Discovery Service allows you to export data in CSV and JSON formats, useful for integration and analysis."
"How does AWS Application Discovery Service help to reduce the risk of migrating applications to the cloud?","By providing a detailed understanding of application dependencies and infrastructure","By automatically fixing any errors found in the applications","By guaranteeing zero downtime during migration","By eliminating the need for any testing after migration","Understanding dependencies allows for a more planned and controlled migration, reducing the risks associated with unforeseen issues or outages."
"What can you use AWS Application Discovery Service to identify in your on-premises environment?","Servers, applications, and their dependencies","Security vulnerabilities and compliance issues","User accounts and passwords","Network firewall configurations","Application Discovery Service helps identify servers, applications and dependencies between them."
"What is the relationship between AWS Application Discovery Service and AWS Migration Evaluator?","AWS Migration Evaluator provides cost recommendations based on data from Application Discovery Service.","AWS Migration Evaluator automatically migrates servers identified by Application Discovery Service.","AWS Migration Evaluator is a replacement for Application Discovery Service.","AWS Migration Evaluator is used to secure data collected by Application Discovery Service.","AWS Migration Evaluator leverages the data gathered by ADS to provide insights and cost recommendations for migration projects."
"What is the first step you should take before starting an AWS Application Discovery Service discovery?","Determine the scope of the discovery and define your goals.","Immediately install agents on all servers.","Configure the AWS Migration Hub.","Set up billing alerts in AWS CloudWatch.","Defining the scope and goals allows you to focus the discovery efforts and ensures you gather the information you actually need."
"What is the purpose of creating application groups in AWS Migration Hub using data from AWS Application Discovery Service?","To organise servers and applications for easier tracking during migration","To automatically load balance applications after migration","To encrypt the data collected by Application Discovery Service","To set up disaster recovery for migrated applications","Application groups allow you to organise and track the progress of related servers and applications as they are migrated."
"How does AWS Application Discovery Service support compliance efforts during cloud migration?","By identifying which applications are running on non-compliant systems","By automatically patching vulnerabilities in the discovered systems","By generating compliance reports for migrated applications","By enforcing security policies on the discovered systems","Identifying applications on non-compliant systems is valuable for planning and prioritising migration tasks to meet regulatory requirements."
"Which of the following is a common use case for AWS Application Discovery Service, besides cloud migration?","IT portfolio management and optimisation","Threat detection and security incident response","Automated server provisioning","Database performance tuning","Application Discovery Service helps with IT portfolio management by providing a comprehensive view of your IT assets and their relationships."
"In AWS Application Discovery Service, what is the role of the AWS Agentless Discovery Connector?","To collect data from VMware vCenter without installing agents on individual VMs","To manage AWS Identity and Access Management (IAM) roles","To automatically update the Application Discovery Service agents","To create backups of the discovered data","The AWS Agentless Discovery Connector is designed specifically for collecting data from VMware vCenter environments without needing agents on each VM."
"If you need to perform a quick assessment of your server infrastructure without installing agents, which AWS Application Discovery Service method is most appropriate?","Agentless discovery","Agent-based discovery","Manual inventory import","AWS CloudTrail analysis","Agentless discovery is the fastest way to gather basic inventory information without the overhead of agent installation."
"What is a key advantage of using the agent-based discovery method over the agentless method in AWS Application Discovery Service?","More detailed application dependency mapping","Lower cost of implementation","Faster discovery speed","No need for administrative privileges","Agent-based discovery provides deeper insights into application dependencies, including process-level communication."
"Which AWS service can be used to monitor the health and performance of the AWS Application Discovery Service connectors and agents?","Amazon CloudWatch","AWS CloudTrail","AWS Config","Amazon Inspector","CloudWatch is the primary service for monitoring the health and performance of AWS resources and services, including ADS connectors and agents."
"What data security measure is important to implement when using the AWS Application Discovery Service agent?","Encrypt data in transit and at rest.","Disable all network communication.","Store the discovered data locally on the agent.","Share access keys publicly.","Encrypting data in transit and at rest helps protect sensitive information during the discovery process."
"Which of the following is NOT directly supported by AWS Application Discovery Service?","Automated application refactoring","Dependency mapping","Inventory collection","Migration planning support","Application Discovery Service focuses on discovery and planning, not the actual refactoring or migration of applications."
"If you have a heterogeneous environment with both physical and virtual servers, which AWS Application Discovery Service approach provides the most complete picture?","Combining both agent-based and agentless discovery","Relying solely on agentless discovery","Using only manual inventory import","Focusing exclusively on cloud-native services","Combining both approaches provides the best coverage, with agent-based discovery for detailed data and agentless discovery for wider coverage and easier setup."
"Which of the following is a benefit of using AWS Application Discovery Service in a large enterprise environment?","Scalability to handle a large number of servers and applications","Automatic migration of all applications to AWS","Elimination of all manual inventory management tasks","Real-time monitoring of application performance in AWS","ADS is designed to scale and discover a vast array of assets and applications so that the entire organisation has a good overview of the infrastructure they plan to migrate."
"What is the primary purpose of the 'tags' feature in AWS Application Discovery Service?","To categorise and organise discovered resources","To automatically migrate tagged resources","To control access to discovered data","To encrypt tagged data","Tags allow you to categorise and organise your discovered resources, making it easier to filter and manage them within Migration Hub."
"When using AWS Application Discovery Service, how do you ensure that sensitive data is not collected by the agents?","Configure the agents to exclude specific directories or processes.","Disable the agents completely.","Grant the agents only read-only access.","Store all data locally.","By properly configuring the agent, you can prevent sensitive information from being scanned or collected."
"What is a common reason for using AWS Application Discovery Service to analyse your existing IT environment before a merger or acquisition?","To understand the IT assets and dependencies of the combined organisations.","To automatically integrate the IT systems of the merging companies.","To determine the market value of the combined IT infrastructure.","To eliminate duplicate IT systems before the merger.","Understanding the combined IT landscape is critical for planning the integration of the organisations after a merger."
"You want to use AWS Application Discovery Service to identify applications that are good candidates for modernisation. What data would be most helpful?","Application dependencies and resource utilisation.","User access patterns.","Network bandwidth consumption.","Storage capacity utilisation.","Dependencies help you find the systems which are the hardest to move and resource utilisation helps to identify systems which may benefit from upgrades or a lift-and-shift."
"What is the main difference between the 'Agent' and 'Agentless' deployment of the AWS Discovery Service?","The Agent deployment requires installing software, while the Agentless does not.","The Agent deployment collects network data, while the Agentless collects hardware data.","The Agent deployment only works in the AWS Cloud, while the Agentless works on-premises.","The Agent deployment analyses cost, while the Agentless analyses compliance.","The Agent deployment requires installing software on the server, while the Agentless deployment uses VMware vCenter to collect server data."
"Which is NOT a valid benefit of running the AWS Application Discovery Service?","Helps to identify ideal lift-and-shift and replatforming candidates.","Assists with TCO calculations and ROI projections.","Eliminates the need to understand on-premises infrastructure before migrating.","Identifies dependencies between physical or virtual servers and applications.","Application Discovery Service creates a deeper understanding of your existing infrastructure, and does not eliminate that need."
"Which AWS Service provides a central location to track the progress of application migrations discovered using the AWS Application Discovery Service?","AWS Migration Hub","AWS Systems Manager","AWS CloudTrail","Amazon Inspector","AWS Migration Hub pulls in the data from AWS Discovery Service and provides a way to group applications and track migration progress."
"Which action can you perform to ensure only needed information is captured by the ADS?","Configure the discovery agents to exclude directories or processes that contain sensitive data.","Do not use any discovery agents.","Disable encryption of data transmitted by the agents.","Grant full administrative access to all resources.","By properly configuring the agent, you can prevent sensitive information from being scanned or collected."
"How does AWS Application Discovery Service support cloud migration projects?","By automatically migrating applications to AWS.","By generating detailed inventory reports and dependency maps.","By providing cost estimates for cloud resources.","By automating the configuration of cloud infrastructure.","Application Discovery Service provides inventory reports and dependency mapping to plan migration."
"What is a key capability of AWS Application Discovery Service that helps in assessing the readiness of applications for migration?","Identifying application dependencies","Automated code refactoring","Performance testing of migrated applications","Automated deployment of applications","Identifying dependencies between applications is critical for assessing their migration readiness."
"Which of the following is a consideration when choosing between agent-based and agentless discovery with AWS Application Discovery Service?","The level of detail required for application dependency mapping.","The number of users accessing the applications.","The geographic location of the servers.","The cost of the servers.","The choice between agent-based and agentless depends on the level of detail needed for dependency mapping."
"What type of data does AWS Application Discovery Service collect to map application dependencies?","Network connection information and process execution details","User login credentials","Security vulnerability scan results","Cloud resource usage metrics","Application Discovery Service collects data to allow a dependency map to be created and dependencies to be evaluated."
"Which AWS service helps visualise the servers, applications, and databases discovered by AWS Application Discovery Service?","AWS Migration Hub","AWS Config","Amazon CloudWatch","AWS CloudTrail","AWS Migration Hub provides tools to visualise the data discovered."
"What is a common use case for AWS Application Discovery Service in the context of application rationalisation?","Identifying redundant or underutilised applications","Automatically migrating applications to AWS","Generating cost reports for application usage","Enforcing security policies on applications","Application Discovery Service assists with application rationalisation by helping to identify redundant or underutilised apps."
"What is a key benefit of using AWS Application Discovery Service in a cloud migration project?","Reduced risk of unexpected application downtime","Automated migration of all applications","Lower cloud infrastructure costs","Elimination of all security vulnerabilities","Application Discovery Service reduces the risk of unexpected downtime by providing a better understanding of application dependencies."
"What data point would be LEAST useful if trying to identify 'easy to move' workloads?","Dependence on legacy platforms.","Geographic location of the servers.","Lack of dependence on other applications.","Low Resource Utilisation.","Geographic Location is not important when deciding which workloads are 'easy to move'."
"When would you consider using agentless discovery with AWS Application Discovery Service?","When you require in-depth process-level dependency mapping","When you have a limited budget and require a quick assessment","When migrating all servers to AWS simultaneously","When you need to capture real-time user activity","Agentless discovery allows for a fast 'high-level' view of assets for limited budgets."
"After migrating servers discovered by the AWS Application Discovery Service, what can you do?","You can retire the ADS agents.","You can use the agents to monitor security vulnerabilities in real time.","You can use the agents for system patching.","You can use the agents for performance tuning.","Once servers are migrated, you can retire the agents as the discovery has served its primary purpose."
"How does the Agent of the AWS Application Discovery Service discover more about the services, applications and processes than the Agentless alternative?","By reading configuration files.","By accessing the hypervisor.","By querying the server using the operating system API.","By using an Internet search.","The agent works in conjunction with the operating system API and reads configuration files."
"What is the primary function of the AWS Application Discovery Service?","To discover and gather information about on-premises servers and applications","To manage AWS Identity and Access Management (IAM) roles","To automate the deployment of EC2 instances","To monitor the performance of AWS Lambda functions","The Application Discovery Service helps you plan migration projects by gathering information about your existing on-premises environment."
"Which of the following is a deployment option for the AWS Application Discovery Service?","Agentless Discovery","AWS CloudTrail","Amazon Inspector","AWS Config","Agentless Discovery collects server information without installing an agent on the server."
"What type of data does the AWS Application Discovery Service agent collect?","System performance data and application dependencies","User credentials and access keys","Network traffic logs","Email content and file shares","The AWS Application Discovery agent collects detailed system performance data and discovers application dependencies."
"What AWS service can be used to store the data collected by the AWS Application Discovery Service?","AWS Migration Hub","Amazon S3","Amazon RDS","Amazon CloudWatch","AWS Migration Hub is the central location for tracking your migration progress and includes Application Discovery data."
"What information does the AWS Application Discovery Service *not* collect?","Personally identifiable information (PII)","Operating system details","Installed software","CPU utilisation","The Application Discovery Service is designed to avoid collecting PII for privacy and security reasons."
"If you are using the agent-based method with AWS Application Discovery Service, what must you ensure on each target server?","That the Application Discovery agent is installed and running","That AWS CloudTrail is enabled","That the AWS CLI is configured","That the server has a public IP address","The agent must be installed and running on each server you want to discover using the agent-based method."
"What is the main benefit of using AWS Application Discovery Service for migration planning?","It helps to identify dependencies and reduce the risk of migration failures.","It automatically migrates your applications to AWS.","It provides free AWS credits for migration.","It creates automated documentation of your application architecture.","Understanding application dependencies is crucial for a successful migration, and Application Discovery Service aids in this process."
"Which AWS Application Discovery Service component provides a consolidated view of your discovered resources and migration progress?","AWS Migration Hub","AWS Systems Manager","AWS CloudFormation","AWS Config","Migration Hub provides a single location to track your migration, including data gathered by Application Discovery Service."
"What does the term 'application dependency' mean in the context of the AWS Application Discovery Service?","The relationship between different software components and services running on your servers","The cost of running an application on AWS","The security vulnerabilities associated with an application","The programming language used to develop an application","Application dependencies refer to the connections and interactions between different parts of your software stack."
"How can you control the scope of discovery when using AWS Application Discovery Service?","By configuring discovery connectors and filtering collected data","By limiting the number of agents installed","By setting budget limits","By defining resource groups in AWS Resource Groups","Discovery connectors allow you to specify the networks, IP address ranges, and other parameters to control the scope of discovery."
"What is the first step in the process of using AWS Application Discovery Service?","Deploying the discovery agent or connector","Creating an IAM role","Configuring AWS CloudTrail","Setting up an Amazon S3 bucket","The first step is usually deploying the agent or connector to start the discovery process."
"Which pricing model applies to the AWS Application Discovery Service?","It is offered at no charge","Pay-per-use based on the number of discovered servers","A monthly subscription fee","A one-time setup fee","AWS Application Discovery Service is offered at no charge, but other AWS services used with it might incur costs."
"Which security best practice should you follow when deploying the AWS Application Discovery agent?","Use IAM roles with the least privileges required","Share the agent credentials with the entire team","Disable encryption","Run the agent as the root user","Granting the agent only the necessary permissions is a key security principle."
"What is an advantage of using the agentless discovery method over the agent-based method in AWS Application Discovery Service?","No software installation required on target servers","More detailed performance data collection","Automatic application dependency mapping","Lower network bandwidth consumption","Agentless discovery avoids the need to install and manage agents on each server."
"What kind of reports can be generated from the data collected by AWS Application Discovery Service?","Migration readiness reports and dependency analysis reports","Financial reports and compliance reports","Security vulnerability reports and performance reports","Customer satisfaction reports and marketing reports","Discovery Service generates reports that help you understand your existing environment and plan your migration."
"How does AWS Application Discovery Service help with application refactoring?","By identifying application dependencies and architectural patterns","By automatically rewriting application code","By providing code analysis tools","By suggesting new programming languages","Understanding application dependencies is a crucial step in application refactoring."
"What is the significance of 'tags' in the context of the AWS Application Discovery Service?","They help to categorize and organize discovered resources","They define security permissions","They determine the instance type to use","They specify the region for deployment","Tags can be used to group and filter discovered resources based on your specific needs."
"Which AWS service is tightly integrated with AWS Application Discovery Service to facilitate server migration?","AWS Server Migration Service (SMS)","Amazon EC2 Auto Scaling","Amazon CloudFront","Amazon SQS","SMS helps migrate discovered servers to AWS after the discovery process."
"What is the maximum retention period for the data collected by the AWS Application Discovery Service?","24 months","7 days","30 days","1 month","The default data retention is for 24 months."
"In the context of AWS Application Discovery Service, what does the term 'connector' refer to?","A virtual appliance that discovers servers in your VMware environment","A plugin for your web browser","A physical network cable","A database management tool","The connector is used to discover servers in your VMware environment without installing agents."
"Which AWS service allows you to automate the process of creating inventories of your existing IT resources using AWS Application Discovery Service?","AWS Systems Manager","AWS CloudTrail","Amazon Inspector","AWS Config","AWS Systems Manager provides automation capabilities for various tasks, including inventory management."
"How can you ensure that sensitive data is not collected by the AWS Application Discovery agent?","Exclude specific directories and files from the agent's scan scope.","Disable the agent.","Encrypt all collected data.","Use a dedicated AWS account.","Excluding specific directories and files prevents the agent from scanning those areas."
"You are tasked with assessing the migration readiness of an application using AWS Application Discovery Service. What key information should you gather?","Application dependencies, resource utilisation, and configuration details","User feedback, marketing data, and sales figures","Employee satisfaction, training records, and performance reviews","Customer demographics, market trends, and competitor analysis","Understanding application dependencies, resource utilisation, and configuration details is crucial for migration readiness."
"What is the purpose of the AWS Application Discovery Service CLI?","To manage the discovery process from the command line","To monitor the performance of EC2 instances","To deploy applications to AWS Lambda","To create and manage IAM roles","The CLI provides a way to manage the discovery process programmatically."
"What is the relationship between AWS Application Discovery Service and AWS CloudWatch?","Application Discovery Service does not directly integrate with CloudWatch","Application Discovery Service sends metrics to CloudWatch","CloudWatch triggers Application Discovery scans","CloudWatch is used to deploy the Application Discovery agent","While Discovery Service itself doesn't directly push metrics to CloudWatch, discovered resources that are migrated may then be monitored using CloudWatch."
"How can you determine which servers are most suitable for migration to AWS using AWS Application Discovery Service?","By analysing resource utilisation and dependency data","By randomly selecting servers","By prioritizing servers with the highest uptime","By migrating all servers at once","Resource utilization and dependency data helps identify servers that are good candidates for migration based on their workload and dependencies."
"What role does the AWS Application Discovery Service play in a cloud migration strategy?","It provides a comprehensive inventory of your existing IT infrastructure","It automates the process of migrating data to AWS","It optimizes the performance of applications running on AWS","It provides security assessments of your AWS environment","Discovery Service offers vital visibility into an organization's IT environment, which is essential for effective cloud migration planning."
"What is a common use case for the data collected by the AWS Application Discovery Service?","Cost optimisation","Fraud detection","Social media analysis","Weather forecasting","Data collected is used to inform cost optimisation strategies by understanding existing resource utilization."
"Which of the following actions can be performed directly within the AWS Application Discovery Service console?","View discovered servers and their dependencies","Manage IAM users and roles","Launch EC2 instances","Configure VPC settings","The primary function of the console is to view and manage discovered assets and their dependencies."
"What is the impact of incorrect application dependency mapping when migrating to AWS?","Application failures and performance issues","Reduced migration costs","Faster migration times","Improved security posture","Incorrect dependency mapping leads to application failures and performance issues due to misconfigured connections and dependencies."
"How does the AWS Application Discovery Service support the AWS Well-Architected Framework?","By providing insights into the Reliability pillar","By managing IAM permissions","By automating cost optimisation","By enforcing security best practices","The Application Discovery Service supports the Reliability pillar of the Well-Architected Framework by helping to understand dependencies and potential points of failure in the existing IT environment."
"What is the role of AWS Migration Evaluator (formerly TSO Logic) in relation to AWS Application Discovery Service?","It provides detailed cost estimates for migrating discovered resources to AWS.","It automatically migrates servers to AWS.","It manages user access to the AWS Management Console.","It performs security vulnerability assessments.","Migration Evaluator provides cost estimates based on the data collected by Application Discovery Service."
"What is the purpose of the 'export to CSV' feature in the AWS Application Discovery Service?","To download the discovered data for offline analysis and reporting","To import data from other discovery tools","To automatically back up the discovered data","To encrypt the discovered data","Exporting to CSV allows you to analyse and report on the data using other tools and platforms."
"How can you use AWS Application Discovery Service to identify underutilized servers?","By analysing resource utilisation metrics collected by the discovery agent","By manually inspecting server logs","By running performance tests on each server","By comparing the number of users accessing each server","Analyzing resource utilisation metrics helps identify servers that are not being used to their full capacity."
"What is the relationship between AWS Application Discovery Service and AWS Config?","AWS Config records configuration changes of AWS resources, while Application Discovery Service focuses on on-premises resources.","They both monitor the same resources","AWS Config replaces Application Discovery Service","Application Discovery Service uses AWS Config to store data","AWS Config is used for configuration management within AWS, while Application Discovery Service focuses on discovering on-premises resources."
"What is the benefit of integrating AWS Application Discovery Service with AWS CloudEndure Migration?","CloudEndure Migration can use the data collected by Application Discovery Service to automate the migration process.","CloudEndure Migration replaces AWS Application Discovery Service","They are completely unrelated","CloudEndure Migration is only for database migrations","CloudEndure Migration can leverage the data from Discovery Service to improve the migration workflow."
"How can you use AWS Application Discovery Service to optimise your application portfolio?","By identifying redundant and outdated applications","By automatically upgrading applications","By purchasing new software licenses","By training your employees","Identifying outdated and redundant applications helps optimise the portfolio and reduce unnecessary costs."
"What should you do if the AWS Application Discovery agent is not collecting data as expected?","Check the agent logs for errors and ensure that the required ports are open.","Reinstall the operating system","Contact AWS support immediately","Ignore the issue and hope it resolves itself","Checking agent logs helps identify any errors or configuration issues that might be preventing data collection."
"How does AWS Application Discovery Service handle dynamic IP addresses?","It can discover servers with dynamic IP addresses, but the data might not be accurate over time.","It only supports static IP addresses","It converts dynamic IP addresses to static IP addresses","It ignores servers with dynamic IP addresses","While it can discover servers with dynamic IPs, the data might be less reliable due to frequent changes."
"What is the purpose of 'agentless discovery' in VMware environments?","To discover virtual machines without installing agents on the guest operating system","To discover physical servers","To discover cloud resources","To discover databases","Agentless discovery avoids the need to install agents on each virtual machine."
"You want to understand the network traffic patterns of your on-premises applications. Can AWS Application Discovery Service provide this information?","Yes, it can collect detailed network traffic data using the agent-based method","No, it only collects basic server information","Yes, but only with the agentless method","It depends on the operating system","The agent collects network traffic data to map application dependencies."
"Which migration strategy is best supported by the insights gained from AWS Application Discovery Service?","Rehosting (lift and shift)","Refactoring","Repurchasing","Retiring","Knowing the details of the current infrastructure provides the most benefit when rehosting."
"You are planning a large-scale migration to AWS and need to prioritise which applications to migrate first. How can AWS Application Discovery Service help?","By providing data on application dependencies, business criticality, and resource utilisation","By automatically migrating the least critical applications","By providing AWS credits for specific applications","By randomly selecting applications for migration","Dependency, criticality, and resource data is vital when deciding the order in which to migrate different apps."
"What is the advantage of using AWS Application Discovery Service over manual inventory methods?","It provides automated and continuous discovery, reducing manual effort and improving accuracy","It is always free","It guarantees 100% accuracy","It requires no network access","Automated discovery is more efficient and less prone to errors than manual methods."
"What type of operating systems can be discovered using the AWS Application Discovery agent?","Windows and Linux","Only Windows","Only Linux","Only Unix","The agent supports both Windows and Linux operating systems."
"When should you consider using agent-based discovery over agentless discovery with AWS Application Discovery Service?","When you need detailed application dependency mapping and resource utilisation data","When you want to minimise the effort of installing agents","When you are only interested in basic server information","When you have limited network bandwidth","Agent-based discovery provides more detailed data."
"You have discovered a set of applications using AWS Application Discovery Service. How can you group these applications into logical business units?","By using tags and creating custom reports","By manually copying the data to a spreadsheet","By deleting the unnecessary applications","By changing the application names","Tags and custom reports provide flexible ways to group and analyse your discovered applications."
"Which of the following is a limitation of the AWS Application Discovery Service?","It requires internet access for the agents to communicate with AWS.","It cannot discover servers running on bare metal.","It only supports a limited number of operating systems.","It cannot discover databases.","The agents need the internet to communicate with AWS."
"What is the primary purpose of AWS Application Discovery Service?","To help plan migrations to AWS by discovering on-premises infrastructure.","To monitor application performance in AWS.","To automate deployment of applications on AWS.","To manage user access to AWS resources.","Application Discovery Service helps you gather information about your on-premises servers to plan and execute migration projects more efficiently."
"Which AWS Application Discovery Service agent-based discovery tool provides the most detailed inventory data?","AWS Discovery Agent","AWS Migration Hub","AWS CloudWatch Agent","AWS Systems Manager Agent","The AWS Discovery Agent provides in-depth system configuration, system performance, and details of running processes and network connections."
"What type of data can AWS Application Discovery Service collect using agentless discovery?","Server specifications and basic configuration.","Detailed performance metrics of running applications.","Network traffic analysis.","User login information.","Agentless discovery gathers data about server specifications, configuration, and utilisation, but lacks the detailed information offered by the agent-based approach."
"Which service integrates with AWS Application Discovery Service to provide a central hub for tracking migration progress?","AWS Migration Hub","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Migration Hub uses the data gathered by Application Discovery Service to provide a unified view of your migration projects."
"In what format can AWS Application Discovery Service export the discovered inventory data?","CSV or JSON","XML or YAML","PDF or DOCX","HTML or Markdown","Application Discovery Service allows you to export the discovered data in CSV or JSON formats for further analysis and planning."
"Which AWS Application Discovery Service discovery method is best for environments where installing agents is not possible due to security concerns?","Agentless Discovery","Agent-based Discovery","Manual inventory collection","CloudWatch Logs","Agentless discovery is ideal for environments where you cannot install agents, as it leverages existing network infrastructure to gather information."
"When using AWS Application Discovery Service, what does the term 'assessment' typically refer to?","The process of analysing discovered inventory data to determine migration readiness.","The act of installing discovery agents on servers.","The configuration of AWS Migration Hub.","The initial scan performed by agentless discovery.","An assessment involves analysing the discovered data to identify dependencies and estimate the effort required for migration."
"What is a key advantage of using AWS Application Discovery Service for migration planning?","Reduced risk and complexity of migration projects.","Automatic application deployment to AWS.","Real-time monitoring of on-premises server performance.","Simplified AWS billing management.","Application Discovery Service allows you to reduce the uncertainty in your migration process by helping you understand what you have and what depends on it."
"Which AWS Application Discovery Service component is responsible for collecting detailed performance data on running processes?","AWS Discovery Agent","AWS Discovery Connector","AWS Migration Hub","AWS Systems Manager","The AWS Discovery Agent, when installed on servers, gathers detailed information about running processes, including CPU and memory usage."
"What is the primary purpose of the AWS Discovery Connector?","To collect data about VMware vCenter environments.","To deploy discovery agents to on-premises servers.","To visualise discovered data in AWS Migration Hub.","To encrypt data in transit to AWS.","The AWS Discovery Connector is deployed in VMware vCenter environments to discover server inventory and configuration data without agents."
"How does AWS Application Discovery Service help with dependency mapping?","It automatically identifies dependencies between applications and servers.","It allows you to manually define dependencies between resources.","It generates network traffic reports for dependency analysis.","It visualises dependencies in AWS CloudWatch.","Application Discovery Service identifies dependencies by analysing network connections and running processes, providing a map of application relationships."
"What is the recommended approach for securing the data collected by AWS Application Discovery Service agents?","Encrypt data in transit and at rest.","Use a dedicated AWS account for discovery.","Monitor agent activity with AWS CloudTrail.","Disable agent logging to prevent data breaches.","Encrypting data both in transit and at rest ensures the confidentiality and integrity of the discovered information."
"Which AWS service can be used to visualise the data collected by AWS Application Discovery Service?","AWS Migration Hub","AWS CloudWatch","AWS Config","AWS CloudTrail","AWS Migration Hub provides a central view of your migration projects, including visualisations of the data discovered by Application Discovery Service."
"What is the typical use case for exporting data from AWS Application Discovery Service?","Performing in-depth analysis of inventory data.","Automating application deployment.","Configuring AWS CloudWatch alarms.","Managing user access permissions.","Exporting data enables you to use third-party tools for more advanced analysis of your infrastructure and application dependencies."
"When planning a migration using AWS Application Discovery Service, what does 'right-sizing' refer to?","Optimising instance sizes in AWS based on discovered utilisation data.","Reducing the number of servers to be migrated.","Simplifying the application architecture before migration.","Standardising the operating systems of on-premises servers.","Right-sizing involves selecting the appropriate instance sizes in AWS based on the actual resource consumption of your on-premises servers."
"Which AWS service allows you to create migration plans based on the data collected by AWS Application Discovery Service?","AWS Migration Hub","AWS CloudFormation","AWS Systems Manager","AWS CodeDeploy","AWS Migration Hub helps you create migration plans based on the discovered inventory and dependency data."
"What is the role of the AWS Discovery Agent in capturing application dependencies?","It monitors network connections and running processes on each server.","It analyses log files to identify application dependencies.","It scans application code for dependency declarations.","It relies on manual input to define application dependencies.","The AWS Discovery Agent actively monitors network connections and process activity to automatically identify dependencies between applications and servers."
"How can AWS Application Discovery Service help reduce the cost of cloud migration?","By identifying unused or underutilised servers.","By automating the process of application deployment.","By optimising AWS billing and cost management.","By simplifying the configuration of AWS resources.","Identifying underutilised servers allows you to avoid migrating unnecessary resources to the cloud, reducing costs."
"Which AWS Application Discovery Service component requires an AWS account to function?","AWS Discovery Agent","AWS Discovery Connector","Both AWS Discovery Agent and AWS Discovery Connector","Neither AWS Discovery Agent nor AWS Discovery Connector","Both the Discovery Agent and Connector need to communicate with the AWS Cloud."
"What is the relationship between AWS Application Discovery Service and AWS CloudTrail?","AWS CloudTrail logs API calls made by AWS Application Discovery Service.","AWS Application Discovery Service uses AWS CloudTrail to monitor server activity.","AWS CloudTrail stores the data collected by AWS Application Discovery Service.","AWS Application Discovery Service is not directly related to AWS CloudTrail.","CloudTrail records API calls to manage Application Discovery Service, which is useful for auditing and security purposes."
"Which security best practice should be followed when using AWS Application Discovery Service?","Restrict access to the discovered data to authorised personnel.","Disable encryption to improve performance.","Share discovery agent credentials with all team members.","Store discovered data on public S3 buckets.","Limiting access to the discovered data ensures that sensitive information about your infrastructure is not exposed."
"What information does AWS Application Discovery Service collect about installed software on discovered servers?","Software name, version, and installation date.","Software license keys and activation codes.","Software usage patterns and user activity.","Software vulnerability scan results.","The service provides name, version and installation date."
"How can you ensure that AWS Application Discovery Service data is up-to-date?","Schedule regular discovery runs.","Manually refresh the discovered data.","Rely on real-time data updates.","Disable data caching.","Scheduling regular runs ensures that any changes to your on-premises environment are reflected in the discovered data."
"What is the purpose of the 'tags' feature in AWS Application Discovery Service?","To categorise and organise discovered resources.","To automate the process of discovery.","To monitor server performance in real-time.","To encrypt data in transit to AWS.","Tags allow you to organise and categorise the resources that Application Discovery Service discovers, making it easier to manage and analyse the data."
"What type of environments is AWS Application Discovery Service suited for?","Hybrid environments with both on-premises and cloud infrastructure.","Fully cloud-native environments.","Static environments with minimal infrastructure changes.","Small businesses with limited IT infrastructure.","It is specifically designed to help discover and assess on-premises infrastructure for migration to AWS, thus supporting hybrid environments."
"What is the default data retention period for data collected by AWS Application Discovery Service?","The data is retained indefinitely until manually deleted.","The data is retained for 30 days.","The data is retained for 90 days.","The data is retained for 1 year.","Data is stored indefinitely, until manually deleted."
"Which is the less resource intensive method of discovering servers using AWS Discovery Service?","Agentless","Agent-based","Manual Import","CloudWatch Metrics","Agentless discovery is less resource intensive as it gathers the data from external sources rather than installing an agent on the servers."
"What is the maximum number of servers that a single AWS Discovery Connector can discover?","500","100","5000","1000","The documentation states that the Connector can discover up to 500 servers."
"What is the advantage of using AWS Application Discovery Service with AWS Migration Hub?","Centralised migration tracking","Automated application deployment","Real-time server monitoring","Enhanced security compliance","The advantage is centralised migration tracking."
"Which of the following is a supported operating system for the AWS Discovery Agent?","Windows Server and Linux","macOS only","Only Windows Server","Only Linux","The AWS Discovery Agent is available for both Windows Server and Linux."
"How does AWS Application Discovery Service identify dependencies between servers?","By analysing network connections and process execution","By reading application configuration files","By monitoring user login activity","By scanning system logs","AWS Application Discovery Service analyses network connections and running processes."
"What is the first step in using AWS Application Discovery Service?","Deploy the Discovery Agent or Connector","Creating a migration plan","Optimising server configurations","Analysing discovered data","The first step is always to deploy the discovery methods."
"What happens to discovered data when an AWS Discovery Agent is uninstalled?","The data remains in AWS Application Discovery Service until manually deleted","The data is automatically deleted after 7 days","The data is automatically deleted after 30 days","The data is immediately deleted","The data will remain even if the Agent is uninstalled."
"Which pricing model applies to AWS Application Discovery Service?","It is a free service","Pay-as-you-go","Subscription-based","Reserved Instance pricing","The service is provided free of charge."
"Which service is commonly used to store the exported data from AWS Application Discovery Service?","Amazon S3","Amazon RDS","Amazon DynamoDB","Amazon EBS","Amazon S3 is a common way to store large export files."
"What type of security credentials are required to deploy an AWS Discovery Agent?","IAM user credentials with appropriate permissions","Root account credentials","No credentials are required","Temporary Security Credentials","IAM user credentials with appropriate permissions are required to allow the agent to upload data."
"Which metric is NOT collected by the AWS Discovery Agent?","CPU utilisation","Memory utilisation","Disk I/O","User login activity","User login activity is NOT collected by the AWS Discovery Agent."
"Which feature of AWS Application Discovery Service allows you to group discovered resources?","Tags","Filters","Reports","Rules","Tags allow you to logically group resources."
"What is the benefit of using agentless discovery for VMware environments?","No software installation on VMs","More detailed performance data","Real-time application monitoring","Automated vulnerability scanning","No software installation on VMs is a benefit when security is a concern."
"How can you troubleshoot issues with the AWS Discovery Agent?","Check the agent logs","Use AWS CloudWatch","Contact AWS Support","Reinstall the agent","Check the local agent logs to diagnose problems."
"Which AWS service provides recommendations for migrating databases based on the data collected by AWS Application Discovery Service?","AWS Database Migration Service (DMS)","AWS Schema Conversion Tool (SCT)","AWS RDS","AWS DynamoDB","AWS Database Migration Service can provide recommendations."
"What is the maximum amount of time data can be retained in Application Discovery Service?","Indefinitely","1 year","6 months","3 months","It can be retained indefinitely until you choose to delete it."
"Which tool would you use to discover dependencies between applications running on physical servers?","AWS Discovery Agent","AWS Trusted Advisor","AWS CloudTrail","AWS Inspector","The AWS Discovery Agent analyses network connections and running processes on each server."
"Which option would be ideal if you have a large environment with hundreds of servers and limited network bandwidth?","Use the AWS Discovery Agent and schedule data uploads during off-peak hours","Use the AWS Discovery Connector for agentless discovery only","Use AWS CloudTrail to monitor server activity","Manually collect and import server inventory data","Scheduling data uploads during off-peak hours helps minimize network congestion."
"Which AWS service can be used to automate the deployment of AWS Discovery Agents across a large number of servers?","AWS Systems Manager","AWS CloudFormation","AWS CodeDeploy","AWS OpsWorks","AWS Systems Manager is useful to deploy the agent at scale."
"What is the benefit of integrating AWS Application Discovery Service with AWS Cost Explorer?","To identify potential cost savings by optimising instance sizes during migration","To track the cost of running AWS Discovery Agents","To generate cost allocation reports based on discovered application dependencies","To forecast future AWS costs based on on-premises infrastructure usage","This facilitates right sizing and therefore cost savings."
"You need to provide temporary access to a third-party auditor to the data collected by AWS Application Discovery Service. How can you achieve this securely?","Create an IAM role with limited permissions and provide temporary credentials","Share your root account credentials","Export the data to an unencrypted S3 bucket","Grant the auditor direct access to the Application Discovery Service console","Temporary IAM credentials for a restricted role should be given to auditors."
"What is the maximum number of application groupings that can be created within AWS Migration Hub using data discovered by AWS Application Discovery Service?","There is no limit","10","50","100","There is no imposed limit to how many groupings you can create."
"What is the primary function of the AWS Application Discovery Service (ADS)?","To collect information about your on-premises servers and applications to plan cloud migrations","To monitor the performance of your AWS cloud resources","To manage user access to AWS services","To automate the deployment of applications on AWS","The primary function of AWS Application Discovery Service is to help you gather information about your existing on-premises servers and applications, aiding in planning migrations to the AWS cloud."
"Which two discovery methods are offered by AWS Application Discovery Service?","Agentless Discovery and Agent-based Discovery","API Discovery and CLI Discovery","Automated Discovery and Manual Discovery","Scheduled Discovery and Continuous Discovery","AWS Application Discovery Service offers both Agentless and Agent-based discovery methods for gathering information about your environment."
"What type of information does the AWS Application Discovery Service Agent collect?","System configuration, system performance, running processes, and network connections","User login activity, security vulnerabilities, and compliance status","Billing details, cost optimisation recommendations, and resource utilisation","Geographic location, weather conditions, and traffic patterns","The ADS Agent collects detailed information including system configuration, system performance, running processes, and network dependencies, providing a comprehensive view of your environment."
"What data does Agentless Discovery for AWS Application Discovery Service (ADS) collect?","Server utilisation data and configuration information via direct API calls","Operating system patches, application configurations and installed software","User activity, network traffic patterns and log files","Data from IoT sensors, mobile devices and social media feeds","Agentless Discovery gathers server utilisation data and configuration information by making direct API calls to the hypervisor or other management interfaces of your servers."
"What AWS service is commonly used to store and analyse data gathered by the AWS Application Discovery Service (ADS)?","Amazon Athena","Amazon S3 Glacier","Amazon CloudWatch Logs","Amazon Route 53","Amazon Athena is commonly used to analyse the data collected by AWS Application Discovery Service to gain insights into your infrastructure."
"What is a key advantage of using the AWS Application Discovery Service (ADS) Agent over Agentless Discovery?","The Agent provides more detailed information about installed applications and dependencies","The Agent is less resource-intensive on the discovered servers","The Agent can be deployed without requiring administrative privileges","The Agent discovers and analyses all applications regardless of network visibility","The Agent gathers more granular information about installed applications and dependencies, which is not available with Agentless Discovery alone."
"Which file format is used for exporting data from the AWS Application Discovery Service (ADS)?","CSV (Comma Separated Values)","JSON (JavaScript Object Notation)","XML (Extensible Markup Language)","YAML (YAML Ain't Markup Language)","Data exported from AWS Application Discovery Service is typically in CSV format, making it easily importable into various analysis tools."
"What is the recommended AWS service for visualising data collected by AWS Application Discovery Service (ADS)?","AWS Migration Hub","AWS Systems Manager","AWS CloudTrail","AWS Config","Although it can be visualised through many services, AWS Migration Hub is often used to visualise and track migration progress, using the data collected by ADS."
"When should you consider using Agentless Discovery with AWS Application Discovery Service (ADS)?","When you need to quickly assess server utilisation without installing software on each server","When you need detailed information about every application running on each server","When you need to discover servers in a highly secure and isolated environment","When you need to collect real-time monitoring data from servers","Agentless Discovery is useful for rapidly gathering basic server utilisation data without the need to install software on each server, making it a quicker initial assessment option."
"Which AWS service does Application Discovery Service (ADS) integrate with to help you plan your migration?","AWS Migration Hub","AWS CloudFormation","AWS Lambda","AWS IAM","AWS Application Discovery Service integrates with AWS Migration Hub, allowing you to consolidate and visualise data about your existing infrastructure and track your migration progress."
"What is the cost model for using the AWS Application Discovery Service (ADS)?","ADS is free to use, but there are costs associated with the AWS resources it uses","You pay based on the number of servers discovered","You pay based on the amount of data scanned","You pay a fixed monthly fee","AWS Application Discovery Service is free to use. However, you will incur costs related to the AWS resources it uses, such as storage in Amazon S3."
"Which security best practice should you follow when deploying the AWS Application Discovery Service (ADS) Agent?","Ensure the agent has least privilege access to the resources it needs to access","Grant the agent full administrative access to all resources for seamless operation","Store the agent's credentials directly in the application code for easy access","Disable all security measures on the agent to minimise performance impact","It's crucial to follow the principle of least privilege when deploying the ADS Agent, granting it only the necessary permissions to perform its discovery tasks."
"How does AWS Application Discovery Service (ADS) help in identifying server dependencies?","By analysing network traffic and process execution data","By relying solely on manual input from system administrators","By scanning the server's hardware components","By analysing user access logs and authentication patterns","AWS Application Discovery Service helps identify server dependencies by analysing network traffic and process execution data, providing insights into how different servers and applications interact."
"What is the purpose of the AWS Application Discovery Service (ADS) Agent Collector?","To package the AWS Discovery Agent installation files","To schedule agent updates and security patches","To manage discovered inventory","To remotely administer and manage all AWS services","The Agent Collector packages the AWS Discovery Agent installation files, simplifying the deployment and management of the agent across your infrastructure."
"What is the maximum number of servers that can be discovered by AWS Application Discovery Service (ADS) per AWS account?","There is no limit to the number of servers that can be discovered","1000","5000","10000","AWS Application Discovery Service (ADS) does not impose a hard limit on the number of servers that can be discovered per AWS account, allowing you to scale your discovery efforts as needed."
"You need to collect software inventory data from your Windows servers for migration planning. Which AWS Application Discovery Service (ADS) discovery method should you use?","Agent-based Discovery","Agentless Discovery","Manual Discovery","CloudWatch Logs Discovery","For comprehensive software inventory data, Agent-based Discovery is recommended, as it gathers detailed information about installed applications on your Windows servers."
"What type of data does the AWS Application Discovery Service (ADS) Agent NOT collect?","Personally Identifiable Information (PII)","CPU utilization","Running processes","Network connections","The AWS Application Discovery Service Agent does not collect Personally Identifiable Information (PII) in order to comply with data privacy regulations."
"Which AWS IAM role is required to run the AWS Application Discovery Service (ADS) Agent?","AWSAgentlessDiscovery","AWSAgentDiscovery","AWSApplicationDiscoveryAgent","AWSMigrationHubRole","The AWSApplicationDiscoveryAgent role is required to run the AWS Application Discovery Service Agent. It grants the necessary permissions for the agent to collect information about your servers and applications."
"What is a key consideration when choosing between Agent-based and Agentless Discovery with AWS Application Discovery Service (ADS)?","The level of detail required and the security policies of your environment","The cost of AWS services and the availability of technical support","The number of servers you have and the complexity of your applications","The size of your organisation and the number of IT staff","The key consideration is the level of detail required for your discovery and the security policies of your environment, as Agent-based Discovery provides more granular information but requires installing agents on your servers."
"You want to ensure that the data collected by the AWS Application Discovery Service (ADS) Agent is encrypted. What should you do?","The data collected by the Agent is always encrypted by default","Configure the agent to use HTTPS","Implement client-side encryption before data is sent","Use a VPN connection between the agent and the AWS cloud","The data collected by the Agent is always encrypted in transit by default, ensuring the security of your data during transmission."
"What is the purpose of the AWS Application Discovery Service (ADS) Migration Hub Connector?","To manage and monitor migration tasks","To connect your on-premises environment to the AWS cloud","To automatically migrate applications to AWS","To translate data formats during migration","The Migration Hub Connector simplifies the process of connecting your on-premises environment to AWS Migration Hub, allowing you to visualise and track your migration progress."
"What is the command-line interface (CLI) command to download the AWS Application Discovery Service (ADS) Agent?","aws application-discovery download-agent","aws discovery download-agent","aws ads get-agent","aws migrationhub get-agent","The command-line interface (CLI) command to download the AWS Application Discovery Service (ADS) Agent is 'aws application-discovery download-agent'."
"How does AWS Application Discovery Service (ADS) help with application rationalisation?","By identifying redundant or underutilised applications","By automatically updating applications to the latest versions","By optimising application performance and resource utilisation","By managing application licensing and compliance","AWS Application Discovery Service (ADS) assists in application rationalisation by providing insights into application usage, helping you identify redundant or underutilised applications that can be decommissioned or consolidated."
"What is the significance of 'Server Utilization' data collected by AWS Application Discovery Service (ADS)?","It helps determine the right size of AWS instances for migrated workloads","It provides information about security vulnerabilities on the discovered servers","It tracks user activity and application usage patterns","It monitors network performance and bandwidth utilisation","'Server Utilization' data collected by AWS Application Discovery Service (ADS) is crucial for determining the appropriate size of AWS instances for migrated workloads, ensuring optimal performance and cost efficiency."
"Which feature of AWS Application Discovery Service (ADS) allows you to track the progress of your migration project?","Integration with AWS Migration Hub","Integration with AWS CloudTrail","Integration with AWS Config","Integration with AWS Systems Manager","AWS Application Discovery Service integrates with AWS Migration Hub, which offers features to track the progress of your migration project, providing a centralised view of your migration activities."
"You need to generate a report of all installed software on your servers using data from AWS Application Discovery Service (ADS). What steps would you take?","Export the data in CSV format and use a reporting tool to analyse it","Use the AWS Management Console to generate a pre-defined software inventory report","Integrate ADS with AWS Cost Explorer to generate a software cost report","Use AWS Lambda to automatically generate and email a weekly software report","To generate a report of all installed software, you would typically export the data from AWS Application Discovery Service (ADS) in CSV format and then use a reporting tool or scripting language to analyse the data and create the desired report."
"What is the impact of running the AWS Application Discovery Service (ADS) Agent on server performance?","Minimal impact, as the Agent is designed to be lightweight","Significant impact, potentially slowing down the server","Unpredictable impact, varying depending on the server's workload","No impact, as the Agent runs in a separate virtual machine","The AWS Application Discovery Service (ADS) Agent is designed to be lightweight and have a minimal impact on server performance, ensuring that the discovery process doesn't significantly affect the server's operation."
"What AWS service can be used to automate the deployment and configuration of the AWS Application Discovery Service (ADS) Agent?","AWS Systems Manager","AWS CloudFormation","AWS Lambda","AWS Config","AWS Systems Manager can be used to automate the deployment and configuration of the AWS Application Discovery Service (ADS) Agent, streamlining the agent installation process and ensuring consistent configurations across your environment."
"How does AWS Application Discovery Service (ADS) assist in identifying potential security risks during migration?","By providing visibility into network connections and dependencies","By automatically patching security vulnerabilities on discovered servers","By scanning for malware and viruses on discovered servers","By enforcing compliance policies on discovered servers","AWS Application Discovery Service (ADS) aids in identifying potential security risks by providing visibility into network connections and dependencies, allowing you to assess the security posture of your applications and infrastructure before migration."
"You want to discover servers located in a private network without internet access. Which AWS Application Discovery Service (ADS) discovery method is appropriate?","Agent-based Discovery","Agentless Discovery","CloudWatch Logs Discovery","Manual Discovery","Agent-based Discovery is appropriate for discovering servers located in a private network without internet access because the agent can collect data locally and securely transmit it to AWS."
"What type of AWS account is required to run the AWS Application Discovery Service (ADS) Agent?","An AWS account with appropriate IAM permissions","A root AWS account","A dedicated AWS account for security auditing","A limited AWS account for testing purposes","To run the AWS Application Discovery Service (ADS) Agent, you need an AWS account with appropriate IAM permissions that allow the agent to access and send data to AWS services."
"What happens if the AWS Application Discovery Service (ADS) Agent loses connectivity to the AWS cloud?","The Agent will continue to collect data locally and upload it when connectivity is restored","The Agent will immediately stop collecting data","The Agent will automatically restart the server to re-establish the connection","The Agent will automatically switch to Agentless Discovery mode","If the AWS Application Discovery Service (ADS) Agent loses connectivity to the AWS cloud, it will typically continue to collect data locally and upload it when connectivity is restored, ensuring minimal data loss."
"How can you ensure that only authorised users have access to the data collected by AWS Application Discovery Service (ADS)?","By using AWS IAM roles and policies to control access","By storing the data in an encrypted Amazon S3 bucket","By implementing multi-factor authentication (MFA) for all users","By regularly rotating access keys and passwords","You can ensure that only authorised users have access to the data collected by AWS Application Discovery Service (ADS) by using AWS IAM roles and policies to control access, granting permissions only to those who need it."
"What is the benefit of integrating AWS Application Discovery Service (ADS) with AWS CloudTrail?","To audit all API calls made to ADS and track user activities","To monitor the performance of ADS and identify bottlenecks","To automatically create backups of the data collected by ADS","To generate cost reports for ADS usage","Integrating AWS Application Discovery Service (ADS) with AWS CloudTrail provides an audit trail of all API calls made to ADS, allowing you to track user activities and ensure compliance with security and governance policies."
"You need to migrate a large number of servers with complex dependencies to AWS. How can AWS Application Discovery Service (ADS) help?","By providing a detailed inventory of servers, applications, and dependencies","By automatically migrating the servers to AWS","By optimising the performance of the migrated servers","By managing the cost of the migration process","AWS Application Discovery Service (ADS) can help with complex migrations by providing a detailed inventory of servers, applications, and dependencies, giving you a clear understanding of your environment and facilitating migration planning."
"Which of the following is NOT a benefit of using AWS Application Discovery Service (ADS)?","Automated application deployment","Detailed server inventory","Dependency mapping","Migration planning","Automated application deployment is NOT a direct benefit of using AWS Application Discovery Service (ADS). While ADS provides information that can be used for deployment planning, it does not automate the deployment process itself."
"When using AWS Application Discovery Service (ADS) Agent, what is the minimum operating system requirement for Windows servers?","Windows Server 2008 R2","Windows Server 2003","Windows Server 2000","Windows Server NT","The minimum operating system requirement for Windows servers when using the AWS Application Discovery Service (ADS) Agent is Windows Server 2008 R2."
"Which AWS service is commonly used to orchestrate and automate the migration of applications to AWS, using the data collected by ADS?","AWS Migration Hub","AWS CloudFormation","AWS Systems Manager","AWS Data Pipeline","AWS Migration Hub is commonly used to orchestrate and automate the migration of applications to AWS, leveraging the data collected by AWS Application Discovery Service (ADS) to streamline the migration process."
"How can you troubleshoot issues with the AWS Application Discovery Service (ADS) Agent?","Check the Agent logs for error messages and connectivity issues","Disable the Agent and restart the server","Contact AWS Support immediately","Reinstall the Agent on the server","To troubleshoot issues with the AWS Application Discovery Service (ADS) Agent, you should first check the Agent logs for error messages and connectivity issues. These logs can provide valuable insights into the cause of the problem."
"What security measures should you take when storing the AWS Application Discovery Service (ADS) Agent Collector in Amazon S3?","Enable server-side encryption and restrict access using IAM policies","Disable public access to the S3 bucket","Use an S3 lifecycle policy to automatically delete the Collector after a certain period","Enable versioning on the S3 bucket","When storing the AWS Application Discovery Service (ADS) Agent Collector in Amazon S3, you should enable server-side encryption and restrict access using IAM policies to ensure the confidentiality and integrity of the Collector package."
"Which AWS service can be used to create a visual representation of the dependencies discovered by AWS Application Discovery Service (ADS)?","AWS Migration Hub","Amazon CloudWatch","AWS CloudTrail","AWS Config","Although several services can display the data. the data collected by AWS Application Discovery Service (ADS) is primarily visualised using AWS Migration Hub, where you can see server and application dependencies."
"How does AWS Application Discovery Service (ADS) assist with cost optimisation during cloud migration?","By providing data to right-size AWS instances based on utilisation","By automatically reducing AWS resource consumption","By negotiating discounts with AWS on your behalf","By recommending cheaper AWS services","AWS Application Discovery Service (ADS) assists with cost optimisation during cloud migration by providing data that enables you to right-size AWS instances based on utilisation, ensuring that you are not over-provisioning resources in the cloud."
"What is the role of the AWS Application Discovery Service (ADS) connector?","To establish a secure connection between your on-premises environment and AWS","To translate data between different formats during migration","To optimise the performance of migrated applications","To manage user access to AWS resources","The AWS Application Discovery Service (ADS) connector, such as the Migration Hub Connector, establishes a secure connection between your on-premises environment and AWS, allowing you to transfer data and manage your migration process."
"How can you ensure that the AWS Application Discovery Service (ADS) Agent is always running on your servers?","Use AWS Systems Manager State Manager to automatically install and configure the Agent","Manually install and configure the Agent on each server","Use AWS CloudFormation to create a template that deploys the Agent","Use AWS Lambda to periodically check and restart the Agent","You can use AWS Systems Manager State Manager to automatically install and configure the AWS Application Discovery Service (ADS) Agent, ensuring that the Agent is always running on your servers and that configurations are consistent."
"Which of the following is a key benefit of using AWS Application Discovery Service (ADS) for regulatory compliance?","Providing an audit trail of all application dependencies and configurations","Automatically enforcing compliance policies on discovered servers","Generating compliance reports for your entire infrastructure","Encrypting all data in transit and at rest","AWS Application Discovery Service (ADS) can help with regulatory compliance by providing an audit trail of all application dependencies and configurations, allowing you to demonstrate that you have a clear understanding of your environment and can meet regulatory requirements."
"You need to update the AWS Application Discovery Service (ADS) Agent on a large number of servers. What is the recommended approach?","Use AWS Systems Manager to automate the update process","Manually update the Agent on each server","Reinstall the Agent on all servers","Disable the Agent and wait for AWS to automatically update it","The recommended approach for updating the AWS Application Discovery Service (ADS) Agent on a large number of servers is to use AWS Systems Manager to automate the update process, ensuring consistency and efficiency."
"What is the purpose of the 'relationship' data collected by AWS Application Discovery Service (ADS)?","To identify dependencies between servers and applications","To track user access to applications","To monitor the performance of applications","To manage the licensing of applications","The 'relationship' data collected by AWS Application Discovery Service (ADS) is used to identify dependencies between servers and applications, allowing you to understand how different components of your environment interact and plan your migration accordingly."
"What is the primary function of AWS Application Discovery Service?","Automated discovery of on-premises server and application configurations and dependencies.","Provisioning and management of virtual machines in the cloud.","Monitoring the performance of cloud-based applications.","Managing user access and permissions for AWS resources.","Application Discovery Service helps you plan migration projects by gathering information about your existing environment."
"Which agent can be used with AWS Application Discovery Service to collect detailed system and application performance data?","AWS Discovery Agent","AWS Systems Manager Agent","CloudWatch Agent","AWS Config Agent","The AWS Discovery Agent is installed on on-premises servers and collects detailed configuration and performance data."
"What type of information can be collected by AWS Application Discovery Service's agentless discovery?","Server hardware specifications and basic network configurations.","Detailed application dependencies and performance metrics.","User login information and access logs.","Application code and database schema.","Agentless discovery provides basic server information without requiring software installation on the servers."
"Which AWS service integrates with AWS Application Discovery Service to visualise application dependencies and migration readiness?","AWS Migration Hub","AWS CloudWatch","AWS Config","AWS CloudTrail","AWS Migration Hub uses the data gathered by Application Discovery Service to provide a centralised view of your migration progress and dependencies."
"Which of the following is a benefit of using AWS Application Discovery Service before migrating to the cloud?","Reduced migration planning time and effort.","Automated deployment of applications to AWS.","Automatic scaling of resources in AWS.","Improved application security in AWS.","Application Discovery Service reduces the complexity of migration planning by providing insights into your existing infrastructure."
"Which AWS service allows you to export the data collected by AWS Application Discovery Service for further analysis?","AWS Glue","AWS Athena","AWS Redshift","AWS SQS","AWS Glue can be used to catalogue and transform the data collected by Application Discovery Service before loading it into a data warehouse for analysis."
"Which of the following is NOT a data point collected by the AWS Application Discovery Agent?","Number of CPUs and memory allocated.","IP addresses, MAC addresses, and DNS servers.","Running processes and open ports.","Physical location of the server.","The Discovery agent doesn't directly collect the physical location of a server. This is typically derived from other sources."
"What is the key difference between agent-based and agentless discovery in AWS Application Discovery Service?","Agent-based discovery provides more detailed information about applications and their dependencies.","Agentless discovery is more secure than agent-based discovery.","Agent-based discovery is always cheaper than agentless discovery.","Agentless discovery requires more network bandwidth than agent-based discovery.","Agent-based discovery provides more granular details about application dependencies as it runs inside the server."
"For AWS Application Discovery Service, what is the main purpose of the Migration Evaluator?","To estimate the Total Cost of Ownership (TCO) of running your infrastructure on AWS.","To automatically migrate your applications to AWS.","To automatically scale your applications in AWS.","To monitor the performance of your applications in AWS.","The Migration Evaluator is a tool within ADS that analyses discovered data to provide an estimate of the cost of running the discovered workloads on AWS."
"When using AWS Application Discovery Service, how is the data encrypted?","Data is encrypted in transit and at rest using AWS KMS.","Data is not encrypted.","Data is only encrypted in transit using TLS.","Data is only encrypted at rest using AES-256.","AWS Application Discovery Service uses AWS KMS for encryption both in transit and at rest."
"What is the primary function of AWS Application Discovery Service?","To collect server and application information for migration planning","To monitor real-time application performance","To automate the deployment of applications","To manage AWS Identity and Access Management (IAM) roles","The AWS Application Discovery Service is primarily used to gather information about on-premises servers and applications, aiding in migration planning."
"Which of the following discovery methods is agentless in AWS Application Discovery Service?","Agentless Discovery Connector","AWS CLI","AWS SDK","Application Discovery Agent","The Agentless Discovery Connector enables discovery of on-premises servers without installing any agents on them."
"What type of data is collected by the AWS Application Discovery Agent?","Detailed system performance metrics and application dependencies","Only basic hardware specifications","Only network traffic data","Only user login information","The Application Discovery Agent collects detailed information about servers, including system performance metrics, installed applications, network dependencies, and process details."
"When using AWS Application Discovery Service, what is a 'Configuration Item'?","A representation of a discovered resource (e.g., server, application)","A set of IAM permissions for discovery","A schedule for running discovery tasks","A report detailing migration costs","A Configuration Item represents a discovered resource like a server, application, or database, along with its attributes and relationships."
"Which AWS service is commonly used to visualise and analyse the data collected by AWS Application Discovery Service?","AWS Migration Hub","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Migration Hub is designed to work with Application Discovery Service to visualise migration readiness and track migration progress."
"What is the role of the AWS Application Discovery Service Connector?","To collect configuration and utilisation data from VMware vCenter","To enable secure access to AWS resources","To provide real-time monitoring of application performance","To automate the creation of AWS CloudFormation templates","The Discovery Connector is used to gather information from VMware vCenter environments, providing agentless discovery capabilities."
"How does AWS Application Discovery Service aid in cost estimation for migrations?","By providing detailed server specifications and utilisation data","By predicting future cloud costs based on historical spending","By automatically negotiating discounts with AWS","By suggesting optimal instance types without analysing current usage","Application Discovery Service provides detailed server specifications and utilisation data, which helps in right-sizing instances and estimating migration costs."
"What is a benefit of using AWS Application Discovery Service for application modernisation?","It helps identify dependencies and plan refactoring efforts","It automatically converts applications to microservices","It provides a fully managed containerisation platform","It automatically patches outdated software versions","Application Discovery Service assists in application modernisation by providing insights into application dependencies, enabling better planning for refactoring and modernisation efforts."
"In AWS Application Discovery Service, what is a key advantage of using agents over agentless discovery?","More detailed information about applications and processes can be collected","Agentless discovery is more expensive","Agentless discovery can identify all installed software","Agents are easier to deploy and manage","Using agents allows for more detailed information about applications, processes, and dependencies to be collected compared to agentless discovery."
"What is the first step in configuring Application Discovery Service?","Configure access to the on-premises environment or install agents","Define migration waves","Create an AWS organisation","Schedule a discovery run","The first step is to configure access to the on-premises environment (for agentless discovery) or install agents on the servers that need to be discovered."
"What is the primary purpose of AWS Application Discovery Service?","To help plan migration projects to AWS by gathering information about on-premises environments","To automatically deploy applications to AWS","To monitor the performance of applications running on AWS","To manage AWS infrastructure costs","Application Discovery Service helps customers gather information about their on-premises servers and applications to plan and execute migration projects."
"Which two discovery methods does AWS Application Discovery Service offer for gathering information about on-premises servers?","Agentless discovery and Agent-based discovery","CloudWatch metrics and CloudTrail logs","AWS Config rules and AWS IAM roles","Trusted Advisor checks and Cost Explorer reports","Application Discovery Service uses both agentless discovery (using the AWS Discovery Connector) and agent-based discovery (using the AWS Discovery Agent) to gather information."
"When using AWS Application Discovery Service, what type of data does the AWS Discovery Agent collect that the AWS Discovery Connector does not?","Detailed application dependency information","Server IP addresses","Operating system details","CPU utilisation metrics","The AWS Discovery Agent collects detailed application dependency information, including processes, network connections, and performance metrics, which is not collected by the agentless Discovery Connector."
"Which AWS service is commonly used to visualise the data collected by AWS Application Discovery Service?","AWS CloudWatch","AWS Systems Manager","AWS Migration Hub","Amazon Inspector","AWS Migration Hub integrates with Application Discovery Service to provide a central location for visualising migration progress and planning migrations."
"Which of the following is a benefit of using AWS Application Discovery Service for migration planning?","Reduced migration time and effort","Automated code refactoring","Automated database schema conversion","Automated application deployment","Application Discovery Service helps reduce migration time and effort by providing detailed information about the on-premises environment, allowing for better planning and execution."
"Which of the following is a prerequisite for using AWS Application Discovery Service agentless discovery?","VMware vCenter Server access","AWS CloudTrail enabled","AWS Config enabled","EC2 instance profile configured","Agentless discovery requires access to VMware vCenter Server to discover virtual machines."
"What is the main advantage of using the AWS Application Discovery Agent over the agentless Discovery Connector?","More granular application dependency mapping","Lower overhead on discovered servers","Faster discovery process","No installation required","The agent provides more granular application dependency mapping, identifying processes and network connections."
"When using AWS Application Discovery Service, where is the discovered data stored?","AWS Migration Hub","Amazon S3","Amazon RDS","AWS Glue Data Catalog","Discovered data is stored within AWS Migration Hub, providing a central repository for migration-related information."
"You need to assess the compatibility of your on-premises applications with AWS before migrating. Can AWS Application Discovery Service directly provide this assessment?","No, but it provides data that can be used for compatibility assessment.","Yes, it automatically flags incompatible applications.","Yes, but it requires custom scripts to be uploaded.","No, this assessment must be done manually.","Application Discovery Service itself doesn't provide direct compatibility assessments, but the data it gathers is invaluable for performing such assessments."
"What is the purpose of the 'export to CSV' feature in AWS Application Discovery Service?","To share the discovered data with third-party migration tools","To automatically migrate the discovered servers to AWS","To visualise the discovered data in a graphical format","To delete the discovered data from AWS","The 'export to CSV' feature allows you to share the discovered data with third-party migration tools or perform custom analysis outside of the AWS console."
"What is the primary function of AWS Application Discovery Service?","To automatically discover and collect information about your on-premises servers and applications.","To provide a managed service for running containerised applications.","To act as a serverless compute service for running code without provisioning servers.","To provide a managed database service.","AWS Application Discovery Service helps you understand your existing IT infrastructure by discovering servers, their specifications, performance data, and dependencies."
"Which of the following discovery methods offers the most comprehensive data collection in AWS Application Discovery Service?","Agent-based discovery","Agentless discovery","Manual inventory","CloudWatch Logs","Agent-based discovery provides a more in-depth analysis by installing an agent on each server to gather detailed configuration, system performance, and dependency information."
"What type of data does AWS Application Discovery Service NOT collect by default?","Server utilisation metrics","Installed software and application dependencies","Server names and IP addresses","User login credentials","AWS Application Discovery Service is not designed to collect sensitive user credentials. It focuses on infrastructure and application metadata."
"What is the name of the AWS Application Discovery Service agent?","AWS Discovery Agent","AWS Server Agent","AWS Migration Agent","AWS Collector Agent","The agent installed on on-premises servers to collect data for AWS Application Discovery Service is called the AWS Discovery Agent."
"In AWS Application Discovery Service, which service is used to store the discovered data?","AWS Migration Hub","Amazon CloudWatch","Amazon S3","AWS Config","AWS Migration Hub serves as the central repository for storing the discovered data from AWS Application Discovery Service, facilitating migration planning."
"Which AWS service can leverage data collected by AWS Application Discovery Service to plan and execute migrations?","AWS Migration Hub","AWS Config","AWS Systems Manager","Amazon Inspector","AWS Migration Hub integrates with AWS Application Discovery Service to provide a centralised location for planning, tracking, and executing server migrations."
"Which of the following is a benefit of using AWS Application Discovery Service for migration planning?","Reduced migration risks","Automated application deployment","Real-time monitoring of application performance","Automatic security patching","AWS Application Discovery Service helps reduce migration risks by providing a clear understanding of dependencies and server specifications, enabling informed decision-making."
"What is the primary output of AWS Application Discovery Service that aids in migration planning?","Application dependency maps","Cost estimates for cloud resources","Security vulnerability reports","Compliance audit logs","Application dependency maps visualise the relationships between servers and applications, crucial for planning a smooth and effective migration."
"How does AWS Application Discovery Service help with optimising resource allocation in the cloud after a migration?","By providing utilisation metrics and performance data","By automatically scaling resources based on demand","By providing a list of recommended instance types","By predicting future resource requirements","AWS Application Discovery Service provides insights into server utilisation and performance data, enabling you to choose the right-sized cloud resources after migration."
"Which pricing model is used for AWS Application Discovery Service?","It is offered at no charge","Pay-per-server discovered","Subscription-based pricing","Pay-per-API call","AWS Application Discovery Service is offered at no charge, making it cost-effective for assessing your on-premises environment."