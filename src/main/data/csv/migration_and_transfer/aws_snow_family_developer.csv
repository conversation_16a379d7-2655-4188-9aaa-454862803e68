"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary use case for AWS Snowcone?","Data migration from edge locations with limited network connectivity","Large-scale data migration to AWS","Running compute-intensive workloads in the cloud","Disaster recovery in multiple AWS Regions","Snowcone is designed for edge computing and data migration from locations with limited or no network connectivity."
"Which AWS Snow Family device offers the largest storage capacity?","Snowmobile","Snowball Edge Compute Optimized","Snowball Edge Storage Optimized","Snowcone","Snowmobile is a truck-sized device that offers the largest storage capacity among the Snow Family devices."
"What is the primary advantage of using AWS Snowball Edge over transferring data over the internet?","Faster data transfer for large datasets","Lower cost for small datasets","Simplified data encryption","Automated data compression","Snowball Edge provides a faster and more secure way to transfer large amounts of data compared to transferring over the internet, especially when network bandwidth is limited."
"What type of encryption is used to protect data at rest on AWS Snow Family devices?","AES-256","RSA-2048","DES","MD5","AWS Snow Family devices use AES-256 encryption to protect data at rest, ensuring data confidentiality and integrity."
"Which AWS service is typically used to manage and track the status of your AWS Snow Family data transfer jobs?","AWS Snow Family Management Console","AWS Transfer Family","AWS Storage Gateway","AWS Data Pipeline","The AWS Snow Family Management Console provides a central location to manage and track the status of data transfer jobs using Snow Family devices."
"Which of the following AWS Snow Family devices is best suited for machine learning inference at the edge?","Snowball Edge Compute Optimized","Snowball Edge Storage Optimized","Snowcone","Snowmobile","Snowball Edge Compute Optimized is specifically designed with GPUs to support machine learning inference and other compute-intensive workloads at the edge."
"What is the main purpose of the 'AWS OpsHub' application in the context of AWS Snow Family devices?","To manage and operate Snow Family devices in disconnected environments","To monitor AWS CloudWatch metrics for Snow Family","To configure AWS IAM roles for Snow Family","To automate the transfer of data from Snow Family to AWS","AWS OpsHub is a software application that simplifies the management and operation of Snow Family devices, especially in environments with limited or no network connectivity."
"Which AWS Snow Family device is ideal for rugged environments and can withstand extreme temperatures?","Snowcone","Snowball Edge","Snowmobile","All Snow Family Devices","Snowcone is designed to be rugged and can operate in a wide range of environmental conditions, making it suitable for edge locations."
"When using AWS Snowball Edge, what is the 'S3 Adapter' used for?","Providing an S3-compatible endpoint on the device","Encrypting data before transferring to S3","Compressing data before transferring to S3","Monitoring data transfer progress to S3","The S3 Adapter provides an S3-compatible endpoint on Snowball Edge, allowing you to use existing S3 tools and applications to interact with the device's storage."
"What is the primary cost component associated with using AWS Snow Family devices?","Data transfer fees and device usage fees","Compute instance usage fees","Storage capacity on S3","Network bandwidth charges","The primary cost components are the data transfer fees (per job) and the device usage fees (per day/week) while the device is in your possession."
"What is the maximum amount of storage available on a single AWS Snowball Edge Storage Optimized device?","80 TB","42 TB","100 TB","200 TB","Snowball Edge Storage Optimized devices come in various sizes; the typical maximum usable storage is around 80 TB per device."
"What is the primary reason to use AWS Snowmobile instead of AWS Snowball Edge?","When transferring extremely large datasets (exabytes)","When needing on-site compute capabilities","When migrating data from a single server","When transferring data over a high-bandwidth network","Snowmobile is designed for exabyte-scale data transfers, while Snowball Edge is more suitable for smaller datasets (terabytes to petabytes)."
"Which AWS service is used to ensure data integrity during data transfer with AWS Snow Family devices?","AWS DataSync","AWS KMS","AWS IAM","AWS Snow Family client","The AWS Snow Family client performs data integrity checks during the transfer process to ensure that data is copied correctly and without corruption."
"What is the purpose of AWS Snow Family's 'clustering' feature when using Snowball Edge devices?","To combine multiple devices into a single logical storage pool","To enable high availability for compute instances","To distribute data across multiple AWS Regions","To automatically backup data to S3","Clustering allows you to combine multiple Snowball Edge devices into a single logical storage pool, increasing the overall storage capacity and enabling features like data replication."
"What is the typical workflow for migrating data using AWS Snow Family devices?","Request device, transfer data, ship device back to AWS","Create S3 bucket, transfer data over internet, delete S3 bucket","Upload data to AWS Glacier, restore data to Snowball Edge, ship device back to AWS","Create EC2 instance, attach EBS volume, transfer data to Snowball Edge, ship device back to AWS","The typical workflow involves requesting a device, transferring data onto the device, and then shipping the device back to AWS for data import into S3 or Glacier."
"Which AWS Snow Family device supports running AWS Lambda functions at the edge?","Snowball Edge Compute Optimized","Snowball Edge Storage Optimized","Snowcone","Snowmobile","Snowball Edge Compute Optimized has the compute resources necessary to support the execution of AWS Lambda functions at the edge."
"What is the primary advantage of using AWS Snowcone over AWS Snowball Edge for edge computing?","Smaller size and ruggedized design for harsh environments","Higher compute capacity","Greater storage capacity","Lower data transfer costs","Snowcone is smaller, lighter, and more ruggedized than Snowball Edge, making it more suitable for deployments in space-constrained and harsh environments."
"Which of the following is a key consideration when deciding between AWS Snowball Edge and AWS Storage Gateway for hybrid cloud storage?","Network bandwidth and latency","EC2 instance type","IAM role configuration","AWS Region availability","Network bandwidth and latency are key factors. Snowball Edge is better for situations with limited bandwidth, while Storage Gateway requires a stable network connection."
"What is the recommended method for securely erasing data from an AWS Snowball Edge device before returning it to AWS?","AWS performs a NIST 800-88 compliant media sanitisation after receiving the device","Manually deleting the data","Formatting the device","Physically destroying the device","AWS automatically performs a NIST 800-88 compliant media sanitisation after receiving the Snowball Edge device back, ensuring that all data is securely erased."
"You are migrating data from an on-premises data centre to AWS using Snowball Edge. What is the first step you need to take?","Create a job in the AWS Snow Family Management Console","Order a Snowball Edge device from the AWS Marketplace","Install the AWS CLI","Configure an S3 bucket","The first step is to create a job in the AWS Snow Family Management Console to specify the data source, destination, and other job parameters."
"Which Snow Family device supports both block and object storage?","Snowball Edge","Snowcone","Snowmobile","Both Snowcone and Snowball Edge","Snowball Edge can act as both a block storage device (using EBS-compatible volumes) and an object storage device (using the S3 Adapter)."
"What is the advantage of using AWS Snow Family over AWS Direct Connect for data migration?","Snow Family does not require a dedicated network connection.","Direct Connect is less secure.","Snow Family is always faster.","Direct Connect is more expensive.","Snow Family does not require a dedicated network connection, making it suitable for environments with limited or intermittent connectivity, unlike Direct Connect."
"What is the purpose of the AWS Snow Family client?","To encrypt and transfer data to the Snow Family device","To monitor Snow Family device performance","To configure the network settings on the Snow Family device","To manage IAM roles on the Snow Family device","The AWS Snow Family client is used to encrypt and securely transfer data to the Snow Family device from your on-premises environment."
"Which AWS service provides a hardware root of trust for AWS Snow Family devices?","AWS KMS","AWS CloudHSM","AWS Certificate Manager","AWS Secrets Manager","AWS KMS (Key Management Service) provides the hardware root of trust for AWS Snow Family devices, ensuring the security and integrity of encryption keys."
"What is the maximum job life cycle for an AWS Snow Family data migration job?","360 days","90 days","14 days","7 days","The maximum job lifecycle for an AWS Snow Family data migration job is typically 360 days, allowing ample time to complete the data transfer process."
"You have a large amount of data in a remote location with very limited internet connectivity. Which AWS Snow Family device would be most suitable for transferring this data to AWS?","Snowcone","Snowball Edge Storage Optimized","Snowball Edge Compute Optimized","Snowmobile","Snowball Edge Storage Optimized is ideal for transferring large amounts of data from remote locations with limited internet connectivity."
"What type of instances can be launched on a Snowball Edge Compute Optimized device?","EC2 instances","RDS instances","Lambda functions","ECS containers","EC2 instances can be launched on Snowball Edge Compute Optimized devices, allowing you to run compute-intensive workloads at the edge."
"What is the primary use case for AWS Snow Family in the Media & Entertainment industry?","Transferring large video files from film sets to the cloud","Hosting streaming video content","Transcoding video files","Creating visual effects","AWS Snow Family is often used to transfer large video files from film sets and other remote locations to the cloud for editing, processing, and archiving."
"What is the purpose of the Snowball Edge’s local endpoint?","To provide S3-compatible access to the data stored on the device","To provide console access to EC2 instances running on the device","To provide a network file share","To provide access to CloudWatch metrics","The local endpoint on Snowball Edge provides S3-compatible access to the data stored on the device, allowing you to use S3 SDKs and tools to interact with the data."
"Which AWS Snow Family device is suitable for moving data to AWS and also includes built-in compute capabilities?","Snowball Edge","Snowcone","Snowmobile","Storage Gateway","The Snowball Edge device offers both storage capacity for data migration and built-in compute capabilities, allowing you to run applications and process data locally."
"You are tasked with migrating a large database to AWS using Snowball Edge. Which of the following considerations is most important?","Ensuring the database software is compatible with the EC2 instance type on Snowball Edge","Selecting the correct IAM role for the Snowball Edge device","Determining the cost of the Snowball Edge data transfer job","The network bandwidth at the on-premises data centre","Ensuring the database software is compatible with the EC2 instance type on Snowball Edge is critical for a successful migration."
"What is the maximum storage capacity for AWS Snowcone?","8 TB","80 TB","100 TB","42 TB","The maximum storage capacity of AWS Snowcone is 8 TB."
"You need to perform data processing on a Snowball Edge device before transferring the data to S3. Which AWS service can you use to automate this processing?","AWS Lambda","AWS Glue","AWS Step Functions","AWS Batch","AWS Lambda functions can be deployed to Snowball Edge to automate data processing tasks before transferring the data to S3."
"Which AWS Snow Family device is often used in tactical edge environments, such as military operations?","Snowcone","Snowball Edge","Snowmobile","Storage Gateway","Snowcone, due to its small size, ruggedness, and battery power, is often used in tactical edge environments."
"What is the purpose of the AWS Snow Family’s 'data sanitisation' process?","To securely erase all data from the device after the data is transferred to AWS","To compress the data before transferring it to AWS","To encrypt the data while it is in transit to AWS","To validate the integrity of the data before transferring it to AWS","The data sanitisation process is used to securely erase all data from the device after the data is transferred to AWS, ensuring data privacy and security."
"Which AWS service is used to manage the lifecycle of data stored on Snowball Edge?","S3 Lifecycle policies","Glacier Vault Lock","AWS Storage Gateway","AWS Data Lifecycle Manager","S3 Lifecycle policies are used to manage the lifecycle of data stored on Snowball Edge once it is transferred to S3."
"You need to transfer data from multiple remote sites to a central AWS Region. Which AWS Snow Family device would be most cost-effective?","Snowball Edge","Snowcone","Snowmobile","Storage Gateway","Snowball Edge is typically more cost-effective for transferring data from multiple remote sites due to its larger storage capacity compared to Snowcone."
"What is a typical use case for AWS Snow Family in the genomics research field?","Transferring large sequencing datasets to the cloud","Running bioinformatics analysis","Managing research data","Visualizing genomic data","Transferring large sequencing datasets from research labs to the cloud is a common use case for AWS Snow Family in the genomics research field."
"Which of the following security measures is implemented on AWS Snow Family devices to protect data in transit?","Encryption","Firewall","Intrusion Detection System","Multi-Factor Authentication","Encryption is used to protect data in transit on AWS Snow Family devices, ensuring confidentiality during the transfer process."
"You are using Snowball Edge to perform data processing at the edge. How can you monitor the performance of the EC2 instances running on the device?","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS CloudWatch can be used to monitor the performance of the EC2 instances running on the Snowball Edge device."
"What is the recommended method for shipping an AWS Snow Family device back to AWS?","Using the pre-paid shipping label provided by AWS","Using a third-party shipping company","Hand-delivering the device","Dropping it off at a local post office","Using the pre-paid shipping label provided by AWS ensures that the device is shipped securely and tracked properly."
"Which of the following is a valid storage option for a Snowball Edge device?","HDD and SSD","Only SSD","Only HDD","NVMe","Snowball Edge devices support both HDD and SSD storage options, providing flexibility for different workloads."
"What is the advantage of using AWS Snowball Edge for disaster recovery?","Rapid data restoration in case of a disaster","Real-time data replication","Automated failover","Continuous data backup","Snowball Edge enables rapid data restoration in case of a disaster by providing a local copy of your data that can be quickly shipped back to AWS."
"Which AWS Snow Family device requires a climate-controlled environment during data transfer?","Snowmobile","Snowball Edge","Snowcone","All AWS Snow Family Devices","Snowmobile is a truck-sized device and requires a climate-controlled environment to ensure the integrity of the hardware and data."
"You are using AWS Snowball Edge to collect sensor data from a remote industrial site. Which feature would be most useful for processing this data at the edge?","AWS IoT Greengrass","AWS Lambda","Amazon Kinesis","Amazon SQS","AWS IoT Greengrass allows you to securely run local compute, messaging, data caching, sync, and ML inference capabilities on Snowball Edge."
"Which AWS service can be used to orchestrate data transfer jobs with AWS Snow Family devices?","AWS DataSync","AWS Step Functions","AWS Glue","AWS CodePipeline","AWS Step Functions can be used to orchestrate data transfer jobs with AWS Snow Family devices, allowing you to create complex workflows and manage the entire data migration process."
"Which of the following AWS Snow Family devices is ideal for transferring data from a cruise ship with limited connectivity?","Snowcone","Snowball Edge","Snowmobile","Storage Gateway","Snowball Edge is ideal for the task as it can store significant amount of data and doesn't need an active network connection."
"Which of the following is the MOST important factor in selecting the correct AWS Snow Family device for a data migration project?","The amount of data to be migrated","The network bandwidth available","The desired security posture","The available budget","While all answers are important, the amount of data to be migrated will generally dictate which device will be the best one."
"What is the primary use case for AWS Snowcone?","Edge computing and data transport in space-constrained environments","Large-scale data migration to AWS","Running machine learning models in the cloud","Storing infrequently accessed data","Snowcone is designed for edge computing use cases where space is limited, such as mobile command centres or vehicles, allowing you to collect, process, and move data to AWS."
"Which AWS Snow Family device is most suitable for migrating petabytes of data to AWS in a data centre environment?","AWS Snowball Edge Storage Optimised","AWS Snowcone","AWS Snowmobile","AWS Outposts","Snowball Edge Storage Optimised is designed for large-scale data migrations and has higher storage capacities than Snowcone, suitable for data centres."
"What is the purpose of the AWS Snowball Edge Compute Optimised device?","To provide local compute and storage capabilities at the edge","To primarily migrate data to AWS","To host fully functional AWS Outposts","To provide archival storage","Snowball Edge Compute Optimised provides significant compute resources for edge workloads, enabling local processing and analysis."
"Which data transfer interface is NOT supported on the AWS Snowball Edge device?","InfiniBand","10GBase-T","SFP+","QSFP+","Snowball Edge devices do not support InfiniBand. They use standard networking interfaces like 10GBase-T and SFP+."
"What is the key benefit of using AWS Snow Family devices for data migration compared to using the internet?","Faster data transfer for large datasets in environments with limited network connectivity","Lower cost for small data sets","Simplified security configuration","Real-time data replication","Snow Family devices can provide significantly faster data transfer compared to the internet, particularly in environments with limited or unreliable network connectivity, for large data sets."
"How does AWS protect data stored on Snow Family devices during transit?","Data is encrypted at rest and in transit using KMS managed keys","Data is sent unencrypted for faster transfer speeds","Data is protected by physical security measures only","Data is only encrypted after it has been transferred to AWS","AWS Snow Family devices encrypt data at rest and in transit to protect confidentiality and integrity during transport using KMS managed keys or other acceptable means."
"Which AWS service is used to manage and track the status of your Snow Family device jobs?","AWS Snow Family Management Console within the AWS Console","Amazon S3","AWS Lambda","Amazon EC2","The AWS Snow Family Management Console allows you to create jobs, track the shipment and status of your devices, and manage other aspects of your data migration."
"What type of data transfer is BEST suited for the AWS Snowmobile device?","Exabyte-scale data migration","Terabyte-scale data migration","Real-time streaming data ingestion","Infrequent data archiving","Snowmobile is designed for exabyte-scale data migration, where transferring data over the internet is impractical due to bandwidth limitations."
"How can you use AWS Snowcone to perform edge computing tasks?","By deploying AWS Lambda functions and EC2 AMIs directly on the device","By only using it for data ingestion and transfer","By connecting it to an on-premises server","By mirroring data from a cloud-based virtual machine","Snowcone allows you to deploy AWS Lambda functions and EC2 AMIs directly on the device, enabling local processing and analysis of data at the edge."
"What is the advantage of using AWS Snowball Edge for data processing at the edge?","It reduces latency and bandwidth costs by processing data locally","It eliminates the need for encryption","It allows for unlimited storage capacity","It provides direct access to AWS services without needing internet","Snowball Edge reduces latency and bandwidth costs by processing data locally, as the data doesn't need to be constantly transferred to the cloud for processing."
"What role does Amazon S3 play in the AWS Snow Family data transfer process?","S3 is the primary destination for data migrated using Snow Family devices","S3 is used to manage the physical security of Snow Family devices","S3 is used to encrypt data before it is loaded onto the device","S3 is used to track the location of Snow Family devices","S3 is the typical destination for data migrated using Snow Family devices, as it provides scalable and durable storage in the cloud."
"Which AWS Snow Family device offers onboard NFS and SMB file interfaces?","AWS Snowball Edge","AWS Snowcone","AWS Snowmobile","AWS Outposts","AWS Snowball Edge provides onboard NFS and SMB file interfaces to easily integrate the device into existing on-premises environments."
"When should you consider using AWS Snowmobile instead of AWS Snowball Edge for data migration?","When migrating multiple petabytes or exabytes of data","When migrating a few terabytes of data","When processing data at the edge","When needing real-time data replication","Snowmobile is designed for extremely large-scale data migrations (petabytes or exabytes), where using multiple Snowball Edge devices is not feasible."
"What is a key difference between AWS Snowball Edge Storage Optimised and Compute Optimised?","Compute Optimised offers more CPU and memory resources for edge processing","Storage Optimised is designed for edge computing","Compute Optimised has lower storage capacity than Storage Optimised","Storage Optimised provides more networking options","The Compute Optimised device provides more CPU and memory resources for demanding edge processing workloads, while Storage Optimised focuses on high storage capacity."
"What is the purpose of the E Ink display on the AWS Snowball Edge device?","To show the device's shipping label and status information","To allow users to interact with the device locally","To display real-time sensor data","To serve as an emergency power source","The E Ink display on the Snowball Edge shows the shipping label, device status, and other relevant information for easy identification and tracking."
"How does AWS handle physical security for AWS Snow Family devices during transit?","Devices are tracked using GPS, and tamper-evident enclosures are used","Devices are shipped without any tracking or security measures","Devices are monitored by video surveillance only","Devices are escorted by armed guards only","AWS uses various physical security measures, including GPS tracking, tamper-evident enclosures, and secure transportation, to protect Snow Family devices during transit."
"Which of the following is a valid use case for using AWS Snowcone in a remote monitoring scenario?","Collecting and processing sensor data from industrial equipment","Hosting a website","Running a large database","Operating a call centre","Snowcone can be deployed in remote locations to collect and process sensor data from industrial equipment, enabling real-time monitoring and analysis."
"What is the main advantage of using AWS DataSync with AWS Snow Family devices?","DataSync can be used to efficiently copy data between on-premises storage and Snow Family devices over the network","DataSync is required to transfer data to Snow Family devices","DataSync is only used to monitor the status of Snow Family devices","DataSync provides encryption for data on Snow Family devices","AWS DataSync can be used to efficiently and securely copy data between on-premises storage and Snow Family devices over a network connection, simplifying the data transfer process."
"Which of the following actions CANNOT be performed on a AWS Snow Family device while it is disconnected from the network?","Running EC2 instances for compute-intensive tasks","Transferring data from Amazon S3","Importing new AMIs","Updating the operating system","Data cannot be transferred from S3 without network connectivity to authenticate to AWS, and access the S3 data. Although computing and data transfer between local data sets can be performed."
"How does AWS Snow Family handle compliance requirements for data stored on devices?","Data is encrypted, and devices are physically secured to meet various compliance standards","Data is not encrypted, as encryption can slow down the transfer process","Compliance is the sole responsibility of the customer","AWS does not support any compliance standards for Snow Family devices","AWS Snow Family helps customers meet compliance requirements by encrypting data and providing physical security measures that align with various industry standards."
"Which AWS Identity and Access Management (IAM) role is typically required for managing AWS Snow Family devices?","A role with permissions to create and manage Snowball jobs, access S3 buckets, and configure KMS keys","A role with full administrator access to the AWS account","A role with read-only access to all AWS services","A role with permissions to only manage EC2 instances","You need an IAM role with sufficient permissions to create and manage Snowball jobs, access S3 buckets for data transfer, and configure KMS keys for encryption."
"What is the primary cost component associated with using AWS Snow Family devices?","Data transfer fees and device usage fees","Network bandwidth costs","Software licensing fees","Power consumption costs","The primary cost components are the data transfer fees (related to the amount of data transferred) and the device usage fees (based on the duration of device rental)."
"Which AWS service provides a command-line interface (CLI) for interacting with AWS Snow Family devices?","AWS CLI","Amazon EC2 CLI","AWS CloudShell","AWS Systems Manager CLI","The AWS CLI provides a command-line interface for managing and interacting with various AWS services, including Snow Family devices."
"What is the maximum storage capacity offered by a single AWS Snowball Edge Storage Optimised device (as of the current generation)?","Around 80 TB","Around 10 TB","Around 1 PB","Around 1 GB","The Snowball Edge Storage Optimised device offers a storage capacity of approximately 80 TB of usable storage."
"Which of the following use cases is NOT well-suited for AWS Snow Family devices?","Real-time data streaming with low latency requirements","Large-scale data migration","Edge computing with limited connectivity","Data collection in remote locations","Real-time data streaming with low latency is not ideal for Snow Family devices, as they involve physical shipment and are not designed for continuous, real-time data transfer."
"How can you ensure that data transferred to an AWS Snow Family device is validated for integrity?","Use checksums and integrity checks during data transfer","Rely solely on the device's internal error correction mechanisms","Manually verify each file after transfer","AWS does not provide any data validation mechanisms","It's important to use checksums and integrity checks during the data transfer process to ensure that data is transferred without errors or corruption."
"Which AWS Snow Family device is designed to operate in extreme environmental conditions?","AWS Snowcone","AWS Snowball Edge","AWS Snowmobile","AWS Outposts","AWS Snowcone is designed to withstand harsh environmental conditions, making it suitable for deployments in remote or challenging locations."
"What is the purpose of the AWS OpsHub for Snow Family?","A GUI that allows you to manage and monitor your Snow Family devices","A command-line tool for data transfer","A service for encrypting data on Snow Family devices","A network acceleration service for Snow Family","OpsHub provides a GUI that allows you to manage and monitor your Snow Family devices, simplifying administration tasks."
"What happens to the data stored on an AWS Snow Family device after it is returned to AWS?","The data is securely erased in accordance with NIST standards","The data is automatically migrated to Amazon S3","The data is returned to the customer","The data is stored indefinitely by AWS","AWS securely erases the data on the device in accordance with National Institute of Standards and Technology (NIST) standards, ensuring data privacy and security."
"How does AWS Snow Family integrate with AWS Key Management Service (KMS)?","KMS is used to encrypt data on Snow Family devices during transit and at rest","KMS is used to physically track the location of Snow Family devices","KMS is used to manage the network configuration of Snow Family devices","KMS is not integrated with Snow Family","AWS KMS is integrated with Snow Family to provide encryption of data at rest and in transit, allowing you to control the encryption keys used to protect your data."
"You need to deploy a machine learning model to perform inference on data collected in a remote location with limited network connectivity. Which AWS Snow Family device is most suitable?","AWS Snowball Edge Compute Optimised","AWS Snowball Edge Storage Optimised","AWS Snowcone","AWS Snowmobile","Snowball Edge Compute Optimised is designed for compute-intensive workloads, making it ideal for deploying and running machine learning models at the edge."
"What is the main advantage of using AWS Snow Family for migrating data from an on-premises tape library to AWS?","Faster and more efficient data transfer compared to traditional methods","Simplified data governance and compliance","Reduced storage costs","Automated data archiving","Snow Family provides a faster and more efficient way to migrate data from on-premises tape libraries to AWS, avoiding the limitations of traditional network-based transfers."
"How does AWS Snow Family support data residency requirements?","By allowing you to transfer data to AWS regions that meet your specific residency needs","By storing data only within the customer's on-premises environment","By automatically replicating data across multiple regions","By using only open-source software on the devices","Snow Family supports data residency requirements by allowing you to choose the AWS region where your data will be stored after the transfer."
"Which AWS Snow Family device offers built-in GPU capabilities for accelerated computing at the edge?","AWS Snowball Edge Compute Optimised","AWS Snowball Edge Storage Optimised","AWS Snowcone","AWS Snowmobile","Snowball Edge Compute Optimised is equipped with a GPU to accelerate computationally intensive workloads, such as machine learning inference and video processing, at the edge."
"What is the purpose of the Trusted Platform Module (TPM) on AWS Snow Family devices?","To securely store encryption keys and protect against tampering","To monitor the temperature of the device","To manage network connectivity","To provide a secure boot environment","The TPM provides a secure hardware environment for storing encryption keys and protecting against tampering, enhancing the security of the device and the data it contains."
"What is a Snowball Edge client?","Software that you install on a local machine to manage the Snowball Edge device","A web browser used to access the AWS Management Console","An AWS Lambda function","A virtual machine image","The Snowball Edge client is a software application that you install on your local workstation to manage the Snowball Edge device, transfer data, and monitor the device's status."
"Which of the following is an important consideration when planning the physical placement of an AWS Snowball Edge device in an on-premises environment?","Adequate power and network connectivity","Proximity to the cloud","Minimal security requirements","Easy accessibility for the public","Adequate power and network connectivity are critical for the proper operation of a Snowball Edge device. You should ensure that the device has access to a stable power source and a network connection with sufficient bandwidth."
"You want to track the chain of custody for an AWS Snow Family device throughout its journey. Which AWS service can help with this?","AWS Control Tower","AWS Supply Chain","AWS Artifact","AWS Audit Manager","While no single service is dedicated to chain of custody for Snow devices, AWS uses internal processes (e.g., tracking, logging) and physical security measures to maintain chain of custody. AWS Artifact might provide some documentation related to their security processes."
"Which of the following data transfer tools is NOT compatible with AWS Snow Family devices?","rsync","Robocopy","AWS DataSync","Azure Data Box","The Snow Family devices are designed to work with the AWS ecosystem for data transfer and compute, so Azure Data Box is not a compatible option."
"What is the purpose of the AWS Snowball Edge unlock key?","To unlock the device and access the data stored on it","To encrypt the data transferred to the device","To manage the device's power settings","To track the device's location","The unlock key is used to unlock the Snowball Edge device and access the data stored on it. It provides a secure authentication mechanism."
"What is the minimum amount of time you can rent an AWS Snow Family device for?","Typically a few days","1 hour","One year","One month","The minimum rental period is typically a few days, but it depends on the device and the specifics of the job."
"What is a main use case for AWS Snowcone SSD?","Edge locations with limited power","High performance computing","Hosting a large database","Archival purposes","Snowcone SSDs are designed for edge locations where power is limited as they offer a low power consumption, and they need to be lightweight."
"Which AWS service is used to create and manage virtual tape libraries?","AWS Storage Gateway","AWS Backup","AWS Snow Family","AWS Direct Connect","AWS Storage Gateway is used to create and manage virtual tape libraries. It's not part of the Snow Family but a related storage service."
"What is the maximum supported file size for AWS Snowball Edge?","There is technically no file size limit, as it depends on the underlying file system used.","1 GB","1 TB","1 MB","Technically, there isn't a hard file size limit. Instead, the effective limits are dependent upon the file system in use."
"You are using a Snowball Edge device and experience a network outage. What happens to any running EC2 instances?","EC2 instances continue to run locally, even without network connectivity","EC2 instances automatically shut down","EC2 instances are migrated to another available Snowball Edge device","EC2 instances pause, but lose all unsaved data","EC2 instances are able to keep running locally, although you will be unable to communicate with the outside environment during the outage."
"How can you monitor the performance and health of your AWS Snowball Edge devices?","Using the AWS OpsHub for Snow Family and the AWS CloudWatch console","Using a third party monitoring tool","Manually logging into the device and running diagnostic commands","AWS does not provide monitoring tools for Snowball Edge devices","AWS provides multiple different tools to monitor the performance and health of your AWS Snowball Edge devices."
"Can you use AWS Snow Family to migrate data from one AWS region to another?","Yes, by transferring data to a Snow Family device and shipping it to the destination region","No, Snow Family devices can only be used for on-premises to AWS migrations","Yes, by setting up a VPN connection between the two regions and using Snow Family as an intermediary","No, you need to use AWS Direct Connect to move data between regions","By shipping data to the correct region, you can migrate data from region to region."
"What type of encryption does AWS Snow Family offer for data at rest?","AES-256 encryption","No encryption","DES encryption","MD5 hashing","AWS uses AES-256 encryption, a strong and widely used encryption algorithm, to protect data at rest on Snow Family devices."
"Which AWS Snow Family device would be best for transferring daily backups from an office in a remote location with limited internet access?","AWS Snowcone","AWS Snowball Edge Storage Optimized","AWS Snowmobile","AWS Outposts","AWS Snowcone is best because it is small, ruggedized and designed for these edge locations."
"What happens to an AWS Snowball Edge device if the tamper-evident enclosure is breached during transit?","The device is automatically wiped, and AWS is notified","The device continues to function normally, but the customer is responsible for any data loss","The device is returned to the customer for inspection","AWS will automatically send a replacement device","If the tamper-evident enclosure is breached, the device is automatically wiped, and AWS is notified, ensuring that the data is protected."
"For AWS Snow Family devices, what is the primary use case for AWS Snowball Edge Compute Optimised?","Performing edge computing workloads with machine learning inference","Archiving infrequently accessed data","Migrating very large datasets to AWS","Running basic file server tasks","Snowball Edge Compute Optimised is designed for edge computing workloads that require significant processing power, especially machine learning inference."
"What type of data transfer is the AWS Snowcone primarily designed for?","Data transfers from edge locations with limited space and power","Migrating petabytes of data to AWS in a data centre","Running large-scale simulations on remote sites","Providing local storage for desktops","AWS Snowcone is designed for data transfers in edge locations with extremely limited space and power, enabling data collection and processing in these environments."
"Which AWS service should you use to track and manage the movement of AWS Snow Family devices?","AWS Snowball Edge Client","AWS OpsHub","AWS Snowball Management Console","AWS Trusted Advisor","The AWS Snowball Management Console provides a central location to track and manage all your Snow Family devices."
"Which AWS Snow Family device supports both block and object storage?","AWS Snowball Edge Storage Optimised","AWS Snowcone","AWS Snowmobile","AWS Storage Gateway","The AWS Snowball Edge Storage Optimised device supports both block and object storage, making it versatile for different types of data."
"When using AWS Snow Family devices for data migration, what security measures are implemented to protect the data in transit?","Data is encrypted using AWS KMS managed keys","Data is transferred without encryption for faster speeds","Physical security is not a concern, as the devices are always tracked","Data is encrypted using third-party encryption tools","AWS Snow Family devices encrypt data in transit using AWS KMS managed keys to protect it from unauthorised access."
"What is the main advantage of using AWS Snowmobile for data transfer?","Transferring Exabytes of data quickly and securely","Providing local compute at the edge","Replacing traditional data centres","Offering cost-effective data archival","AWS Snowmobile is designed for transferring exabytes of data, offering a fast and secure way to move very large datasets to AWS."
"Which of the following is a typical use case for AWS Snowball Edge Storage Optimised?","Migrating large datasets to AWS and local storage","Running lightweight edge computing tasks","Hosting static websites","Analysing real-time video streams","AWS Snowball Edge Storage Optimised is typically used for migrating large datasets to AWS and for local storage needs, offering significant storage capacity."
"Which feature on AWS Snowball Edge allows you to cluster multiple devices together?","AWS OpsHub","AWS Snowball Edge Clustering","AWS Snowball Edge Networking","AWS Snowball Edge Storage","AWS Snowball Edge Clustering allows you to group multiple Snowball Edge devices to increase compute and storage capacity."
"What type of connectivity is required for initial configuration of an AWS Snowcone device?","Wireless connection only","A wired network connection for activation and management","No network connection is required","Satellite internet connection","A wired network connection is required for initial activation and management of an AWS Snowcone device."
"What is the primary purpose of the AWS OpsHub application in the context of AWS Snow Family devices?","To manage and monitor AWS Snow Family devices at edge locations","To encrypt data before it is transferred to AWS","To create machine learning models on the edge","To provision EC2 instances in the AWS cloud","AWS OpsHub is a tool designed to manage and monitor AWS Snow Family devices at edge locations, simplifying device configuration and operation."
"What is the primary use case for the AWS Snowcone device within the AWS Snow Family?","Edge computing and data transfer in space-constrained environments","Large-scale data migration to AWS","High-performance computing in remote locations","Long-term archival storage","Snowcone is designed for edge computing and data transfer in environments with limited space and power, such as IoT and tactical edge scenarios."
"Which AWS Snow Family device is most suitable for migrating petabytes to exabytes of data to AWS?","AWS Snowball Edge","AWS Snowmobile","AWS Snowcone","AWS Outposts","Snowmobile is a truck-sized device specifically designed for migrating extremely large datasets (petabytes to exabytes) to AWS."
"Which of the following AWS Snow Family devices offers built-in compute capabilities for processing data at the edge?","AWS Snowball Edge","AWS Snowcone","AWS Snowmobile","AWS Storage Gateway","Snowball Edge devices offer built-in compute capabilities, allowing you to process data locally before transferring it to AWS."
"What type of storage is available on the AWS Snowball Edge Storage Optimised device?","Object storage and block storage","Only block storage","Only object storage","Tape storage","The Snowball Edge Storage Optimised device provides both object storage (S3-compatible) and block storage options."
"What is the function of the AWS Snow Family device's E Ink display?","To display the shipping label and device status","To provide a user interface for data transfer","To display video content","To allow users to write notes","The E Ink display on Snow Family devices shows the shipping label, device status, and instructions for handling the device."
"Which of the following security features is available on AWS Snow Family devices?","Data encryption and tamper-evident enclosure","Multi-factor authentication only","Biometric authentication only","Network firewalls only","Snow Family devices provide security features such as data encryption and a tamper-evident enclosure to protect data during transit."
"For what type of use case is the 'AWS Snowball Edge Compute Optimised' device best suited?","Machine learning inference at the edge","Long-term archival storage","Large-scale media rendering","Storing backups of virtual machines","The Compute Optimised Snowball Edge is designed for compute-intensive workloads like machine learning inference at the edge."
"Which of the following AWS services can you use to track the status of your AWS Snow Family data transfer job?","AWS Snowball Management Console","AWS CloudTrail","Amazon CloudWatch","AWS Trusted Advisor","The AWS Snowball Management Console is used to manage and track the status of Snow Family data transfer jobs."
"When shipping an AWS Snow Family device back to AWS, what is the recommended method for physically securing the device?","Using the provided tamper-evident seal","Using a padlock","Using bubble wrap","Leaving it unsecured","AWS Snow Family devices come with a tamper-evident seal that should be used when shipping the device back to AWS."
"What is the primary advantage of using the AWS Snow Family for data migration compared to transferring data over the internet?","Faster transfer speeds for large datasets","Lower cost for small datasets","Simplified network configuration","Increased data durability","The Snow Family provides significantly faster transfer speeds for large datasets compared to transferring data over the internet, especially in areas with limited bandwidth."
"Which AWS Snow Family device is best suited for edge computing in environments with limited space and power?","Snowcone","Snowball Edge Compute Optimized","Snowball Edge Storage Optimized","Snowmobile","Snowcone is the smallest and most rugged device in the Snow Family, designed for edge computing in space-constrained environments."
"What is the primary use case for AWS Snowmobile?","Transferring exabytes of data to AWS","Running local compute and storage workloads","Edge data collection and processing","Shipping small amounts of data quickly","Snowmobile is designed for transferring extremely large datasets (exabytes) to AWS. The Snowball Edge devices handle smaller amounts."
"Which Snowball Edge option is most suitable for running machine learning workloads on-premises?","Compute Optimized","Storage Optimized","Snowcone","Snowmobile","The Compute Optimized Snowball Edge has powerful compute resources and GPUs, making it suitable for machine learning workloads."
"With AWS Snow Family devices, what type of encryption is used to protect the data at rest?","AWS Key Management Service (KMS)","Client-Side Encryption","Server-Side Encryption","Hardware Security Module (HSM)","AWS KMS is used to encrypt the data at rest on AWS Snow Family devices, using keys managed by AWS."
"Which networking interface is typically used for data transfer on a Snowball Edge device?","40/100 GbE QSFP+","1 GbE RJ45","Wi-Fi","Bluetooth","Snowball Edge devices commonly use 40/100 GbE QSFP+ interfaces for high-speed data transfer."
"What is the typical workflow for using a Snowball Edge device to import data into AWS?","Order the device, load data, ship it back to AWS, data is imported to S3.","Directly upload data to S3 via the device.","Connect the device to your AWS account and directly transfer the data.","Use AWS Storage Gateway to copy data to the device.","The process involves ordering the device, loading data onto it, shipping it back to AWS, where the data is then imported into S3."
"Which AWS service is commonly used to manage and monitor the status of AWS Snow Family device jobs?","AWS Snow Family Management Console","AWS CloudWatch","AWS Systems Manager","AWS Config","The AWS Snow Family Management Console provides a central place to manage and monitor the status of Snow Family device jobs."
"What is a key benefit of using AWS Snow Family devices for data migration?","Reduced network bandwidth requirements","Real-time data synchronization","Automated data transformation","Simplified serverless application deployment","AWS Snow Family devices reduce the reliance on network bandwidth for transferring large datasets, particularly over slow or unreliable networks."
"In the context of AWS Snow Family, what does the term 'data residency' refer to?","Ensuring data stays within a specific geographic region","Encrypting data at rest","Compressing data before transfer","Replicating data across multiple AWS regions","Data residency ensures data stays within a specific geographic region, often required for compliance or regulatory reasons. AWS Snow Family devices allow you to meet these requirements by collecting the data within the required region and then shipping the device to AWS."
"Which security feature helps protect data on AWS Snow Family devices during transit?","Tamper-evident enclosure","Multi-factor authentication","Virtual Private Cloud (VPC)","AWS Shield","The tamper-evident enclosure on Snow Family devices helps protect the data during transit by providing physical evidence of any unauthorized access attempts."