"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"Which protocol is supported by AWS Transfer Family for transferring files?","SFTP","HTTP","SMTP","POP3","SFTP (SSH File Transfer Protocol) is one of the secure file transfer protocols supported by AWS Transfer Family."
"What is the primary purpose of AWS Transfer Family?","Securely transferring files into and out of AWS storage services","Managing AWS IAM roles and policies","Monitoring network traffic","Deploying containerised applications","AWS Transfer Family provides a fully managed service that enables the secure transfer of files over SFTP, FTPS, and FTP directly into and out of Amazon S3 or Amazon EFS."
"Which AWS storage services can AWS Transfer Family integrate with?","Amazon S3 and Amazon EFS","Amazon Glacier and Amazon EBS","Amazon RDS and Amazon DynamoDB","Amazon Redshift and Amazon MQ","AWS Transfer Family integrates directly with Amazon S3 for object storage and Amazon EFS for file storage, enabling seamless file transfers to these services."
"Which identity provider can AWS Transfer Family use to authenticate users?","AWS Managed Identities","Azure Active Directory","Google Identity","Okta","AWS Transfer Family uses AWS Managed Identities, API Gateway custom authorizers, or third-party identity providers via custom authentication to authenticate users."
"In AWS Transfer Family, what is an 'endpoint'?","The network address used to connect to the server","A user's home directory in S3","An IAM role associated with the server","A step in a file processing workflow","An endpoint in AWS Transfer Family represents the network address (either a public IP or a VPC endpoint) through which clients connect to the server for file transfers."
"Which pricing model is associated with AWS Transfer Family?","Pay-as-you-go based on server usage and data transfer","Fixed monthly fee","Tiered pricing based on storage capacity","Free tier for small businesses","AWS Transfer Family uses a pay-as-you-go pricing model, where you're charged based on the hours your server is active and the amount of data transferred."
"When setting up AWS Transfer Family, what does the 'service managed' option for user authentication mean?","AWS Transfer Family manages user credentials and SSH keys","User credentials are stored in AWS IAM","User authentication is handled by a third-party IdP","Authentication is disabled","When using 'service managed' authentication, AWS Transfer Family handles the storage and management of user credentials, including SSH keys, simplifying user management."
"What type of data encryption does AWS Transfer Family provide during file transfer?","Encryption in transit","Encryption at rest","Both encryption in transit and at rest","No encryption","AWS Transfer Family supports encryption in transit through protocols like SFTP, FTPS, and FTPs. It also supports encryption at rest with S3 or EFS encryption features."
"What is the function of the 'Pre-processing Workflow' option in AWS Transfer Family?","To perform actions on the file before it's stored in S3 or EFS","To monitor the server's health and performance","To compress the file during transfer","To trigger notifications upon successful file transfer","Pre-processing workflows enable you to perform actions on files before they are stored, such as virus scanning, content inspection, or format conversion using AWS Step Functions."
"Which file transfer protocol provides end-to-end encryption and is widely considered the most secure option for AWS Transfer Family?","SFTP","FTP","FTPS","HTTP","SFTP (SSH File Transfer Protocol) offers end-to-end encryption using SSH, providing a highly secure method for transferring files."
"What is the advantage of using AWS Transfer Family over setting up your own file transfer server on EC2?","Fully managed service reduces operational overhead","Lower cost due to no instance management","Faster transfer speeds","Unlimited storage capacity","AWS Transfer Family is a fully managed service, meaning AWS handles server maintenance, patching, and scaling, reducing operational burden compared to managing your own EC2-based server."
"How can you control access to files transferred via AWS Transfer Family?","IAM roles and policies","Network ACLs","Security Groups","AWS Shield","Access control for files transferred via AWS Transfer Family is primarily managed through IAM roles and policies, which define what users and services can access within S3 or EFS."
"Which AWS service is commonly used to implement custom authentication for AWS Transfer Family?","AWS Lambda","Amazon SQS","Amazon SNS","Amazon CloudWatch","AWS Lambda is often used to implement custom authentication logic for AWS Transfer Family, allowing integration with various identity providers or custom authentication schemes."
"Which AWS service can be used to monitor file transfer activity in AWS Transfer Family?","Amazon CloudWatch Logs","AWS CloudTrail","Amazon VPC Flow Logs","AWS Config","Amazon CloudWatch Logs can be used to monitor file transfer activity and troubleshoot issues in AWS Transfer Family by logging events and errors related to file transfers."
"What is the role of an 'IAM role' in the context of AWS Transfer Family?","To grant the Transfer Family service permission to access S3 or EFS","To authenticate users connecting to the Transfer Family server","To define the encryption key used for file transfers","To limit the amount of data users can transfer","An IAM role is used to grant the Transfer Family service permission to access resources like S3 buckets or EFS file systems, allowing it to read and write files on behalf of users."
"What is a potential use case for using an EFS file system with AWS Transfer Family?","Providing shared storage for multiple users","Storing database backups","Hosting static website content","Serving container images","Using EFS with AWS Transfer Family is suited for cases where multiple users need to access and share files through the service, providing a collaborative file storage environment."
"What does the 'chroot' setting in AWS Transfer Family do?","Limits the user's access to a specific directory","Encrypts the transferred files","Sets the server's timezone","Configures the server's hostname","The 'chroot' setting restricts the user's access to a designated directory within S3 or EFS, preventing them from navigating outside of that directory."
"How can you ensure high availability for your AWS Transfer Family server?","By using multiple Availability Zones","By enabling server-side encryption","By using a content delivery network (CDN)","By implementing load balancing","AWS Transfer Family inherently provides high availability by operating across multiple Availability Zones (AZs). The infrastructure is fully managed and designed for fault tolerance."
"What is the purpose of the 'Post-import Workflow' option in AWS Transfer Family?","To process files after they have been successfully transferred to S3 or EFS","To authenticate users after a successful login","To configure server settings","To back up the Transfer Family server configuration","The 'Post-import Workflow' allows you to trigger actions after a file has been successfully transferred, such as running data validation checks or initiating further data processing steps using AWS Step Functions."
"What type of endpoint is created when you configure a server with Internet access in AWS Transfer Family?","Public endpoint","VPC endpoint","Private endpoint","Internal endpoint","When you create a server with Internet access in AWS Transfer Family, a public endpoint is created, which allows clients to connect to the server from anywhere on the internet."
"Which of the following is not a supported protocol by AWS Transfer Family?","HTTPS","SFTP","FTPS","FTP","HTTPS is not a supported protocol by AWS Transfer Family. It supports SFTP, FTPS and FTP."
"Which AWS service can be integrated with AWS Transfer Family to implement custom authentication logic using API Gateway?","AWS Lambda","Amazon SQS","Amazon SNS","Amazon CloudWatch","AWS Lambda is commonly used for custom authentication logic and can be invoked through an API Gateway authorizer."
"What type of encryption method is used by FTPS in AWS Transfer Family?","TLS","AES-256","RSA","SHA-256","FTPS uses TLS to encrypt data during transfer."
"Which event triggers the Pre-Processing Workflow in AWS Transfer Family?","Before a file is transferred to the S3 bucket or EFS file system","After a file is transferred to the S3 bucket or EFS file system","When a user logs into the Transfer Family server","When the Transfer Family server is created","The Pre-Processing Workflow is triggered before the file is transferred to the destination."
"What is the primary benefit of using AWS Transfer Family with Amazon S3?","Provides secure and scalable storage for transferred files","Enables real-time data analytics","Offers low-latency database access","Automates infrastructure management","Amazon S3 provides a scalable, durable and cost-effective storage solution for files transferred using AWS Transfer Family."
"How does AWS Transfer Family handle user authentication when using service-managed identities?","AWS Transfer Family stores and manages user credentials, including SSH keys","Users authenticate with their existing AWS IAM credentials","Authentication is handled through a third-party identity provider","Authentication is completely disabled","AWS Transfer Family stores and manages user credentials, including SSH keys, when using service-managed identities."
"What is the key benefit of using AWS Transfer Family for secure file transfer?","It simplifies compliance with security and regulatory requirements","It reduces the cost of data storage","It accelerates data processing workflows","It enhances data visualisation capabilities","AWS Transfer Family simplifies compliance with security and regulatory requirements like HIPAA, PCI DSS, and SOC."
"Which AWS Transfer Family feature helps in enforcing security policies by restricting user access to specific directories?","Chroot","IAM Roles","Security Groups","Network ACLs","The chroot feature restricts a user's access to a specific directory, enhancing security."
"What AWS service is commonly used to trigger automated processing of files after they are uploaded to S3 via AWS Transfer Family?","AWS Lambda","Amazon EC2","Amazon RDS","Amazon DynamoDB","AWS Lambda is commonly used to trigger automated processing of files uploaded to S3."
"What is one of the main cost drivers when using AWS Transfer Family?","The duration the server is active and the amount of data transferred","The number of users connected to the server","The amount of storage used in S3 or EFS","The number of API calls made to the Transfer Family service","The cost is mainly driven by the time the server is active and the amount of data transferred."
"Which feature allows you to restore an AWS Transfer Family server to a previous state?","AWS Backup integration","Point-in-time recovery","Automated snapshots","Server cloning","AWS Backup integration enables you to backup and restore Transfer Family servers to a previous state."
"What is the purpose of specifying a 'home directory' when configuring a user in AWS Transfer Family?","To define the initial directory the user sees upon login","To encrypt the user's data","To limit the user's access to specific files","To track the user's activity","The home directory defines the initial directory the user sees upon login, providing a controlled environment."
"What is the advantage of using a VPC endpoint for AWS Transfer Family?","It allows clients to connect to the server without exposing it to the internet","It encrypts data in transit","It accelerates data transfer speeds","It provides centralised logging","A VPC endpoint allows clients to connect to the server without exposing it to the internet, enhancing security."
"Which type of AWS Transfer Family endpoint requires an Elastic IP address?","Public Endpoint","VPC Endpoint","Private Endpoint","Internal Endpoint","Public Endpoint requires an Elastic IP address."
"What does the 'Always Apply Home Directory' option do in AWS Transfer Family?","Ensures users are always restricted to their home directory, regardless of configured permissions","Automatically backs up the home directory","Encrypts the home directory","Shares the home directory with all users","The 'Always Apply Home Directory' option ensures that users are always restricted to their home directory, irrespective of any other permissions configured."
"How can you enable logging for AWS Transfer Family servers?","By enabling CloudWatch Logs","By enabling CloudTrail","By enabling VPC Flow Logs","By enabling S3 server access logging","You can enable logging by configuring CloudWatch Logs for the server, allowing you to monitor file transfer activities."
"What is the maximum file size supported by AWS Transfer Family?","Varies based on the storage service (S3 or EFS)","1 GB","10 GB","50 GB","The maximum file size supported is dependent on the underlying storage service (S3 or EFS) and its limits."
"Which AWS service is used to invoke a workflow after a file transfer is completed in AWS Transfer Family?","AWS Step Functions","AWS Lambda","Amazon SQS","Amazon SNS","AWS Step Functions is used to define and orchestrate workflows after a file transfer is completed."
"What does the 'Server Alias' setting in AWS Transfer Family allow you to do?","Assign a custom domain name to your server","Configure server-side encryption","Specify the server's IP address","Define the server's security group","The 'Server Alias' setting allows you to assign a custom domain name to your server, making it easier for clients to connect using a familiar name."
"What is the purpose of using wildcard characters in the scope down policy in AWS Transfer Family?","To allow you to map a directory","To set transfer limits","To restrict users based on IP","To create user groups","The Scope-Down policy uses wildcard characters to map directories."
"What happens to your AWS Transfer Family server if you don't upload your SSH Public Key?","You won't be able to authenticate to the server","Your files will be encrypted","Your data will be deleted","Your data will be exposed","If you do not upload your SSH public key, you won't be able to authenticate with the server."
"Where is the data you send/receive to AWS Transfer Family ultimately stored?","In Amazon S3 or Amazon EFS","In Amazon RDS","In Amazon DynamoDB","In Amazon Glacier","The data sent to AWS Transfer Family are stored in Amazon S3 or Amazon EFS."
"With AWS Transfer Family and AWS Managed Identities, can I restrict SSH access by IP address?","Yes, using IAM Condition elements in policies","No, all IP addresses are allowed with IAM","Yes, using CloudTrail to audit IP access","No, IP restrictions can only be done with third-party IdP","With IAM Condition elements, it's possible to restrict SSH access based on the source IP address for extra security."
"With AWS Transfer Family, which protocol is primarily used for secure file transfer and supports features like data integrity and encryption?","SFTP","FTP","HTTP","FTPS","SFTP (SSH File Transfer Protocol) provides secure file transfer capabilities, including data integrity and encryption, using SSH."
"What is the main purpose of using AWS Transfer Family?","Securely transfer files into and out of AWS storage services","Directly host a website on AWS","Monitor network traffic","Manage IAM users","AWS Transfer Family is a fully managed service designed to securely transfer files into and out of AWS storage services like S3 and EFS."
"Which AWS service is most commonly integrated with AWS Transfer Family to store the transferred files?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","AWS Transfer Family often integrates with Amazon S3 for storing transferred files, offering scalability and cost-effectiveness."
"Which authentication method is supported by AWS Transfer Family to verify the identity of the user accessing the service?","SSH keys","Password","MFA token","IP address","AWS Transfer Family supports SSH key-based authentication, a more secure method compared to passwords, for verifying user identities."
"When using AWS Transfer Family, what is the primary advantage of using a custom hostname?","Provides a branded endpoint for file transfers","Increases transfer speed","Reduces storage costs","Improves data encryption","Using a custom hostname with AWS Transfer Family provides a branded endpoint, making it easier for customers to remember and access the service."
"In AWS Transfer Family, what is a 'server' in the context of the service referring to?","A logical entity that enables file transfer over a specific protocol","A physical EC2 instance","A storage volume","A database instance","In AWS Transfer Family, a 'server' represents a logical entity configured to enable file transfer using protocols like SFTP, FTPS, or FTP."
"Which of these features allows you to automate actions based on file transfers in AWS Transfer Family?","Post-upload Lambda function invocation","Automatic server scaling","Real-time user monitoring","Database backups","AWS Transfer Family can trigger a Lambda function after a file is uploaded, enabling automated processing or other actions."
"Which compliance standards are often met by AWS Transfer Family, making it suitable for regulated industries?","HIPAA, PCI DSS, SOC","GDPR, CCPA, LGPD","ISO 27001, ISO 9001, ISO 20000","FERPA, COPPA, VPAT","AWS Transfer Family is compliant with standards like HIPAA, PCI DSS, and SOC, making it suitable for regulated industries that require secure file transfer."
"What pricing model does AWS Transfer Family utilise?","Pay-as-you-go based on the time the server is enabled and the data transferred","Fixed monthly fee","Per-user license","Free tier usage","AWS Transfer Family uses a pay-as-you-go model, where you're charged based on the duration the server is enabled and the amount of data transferred."
"When configuring AWS Transfer Family, what is the role of an IAM role associated with the service?","Grants the service permission to access other AWS resources","Defines user authentication policies","Controls network access","Manages encryption keys","The IAM role associated with AWS Transfer Family allows the service to access other AWS resources, such as S3 buckets, on your behalf."
"With AWS Transfer Family, what does the 'User' object represent?","An individual or application authorised to transfer files","A storage location","A security group","A network interface","In AWS Transfer Family, a 'User' represents an individual or application that is authorized to access the file transfer service."
"Which protocol supported by AWS Transfer Family does NOT inherently encrypt data in transit?","FTP","SFTP","FTPS","HTTPS","FTP does not inherently encrypt data in transit, making it less secure than SFTP or FTPS."
"How can you monitor the file transfer activity within AWS Transfer Family?","Amazon CloudWatch logs and metrics","AWS CloudTrail events","Amazon VPC Flow Logs","AWS Config rules","File transfer activity in AWS Transfer Family can be monitored using Amazon CloudWatch logs and metrics, and AWS CloudTrail events."
"What is the benefit of integrating AWS Transfer Family with AWS Managed Active Directory?","Centralised user management and authentication","Automated server scaling","Data encryption at rest","Cost optimisation","Integrating AWS Transfer Family with AWS Managed Active Directory allows for centralised user management and authentication using existing directory services."
"Which action can you take to enhance the security of your AWS Transfer Family server?","Enable VPC endpoints","Disable public access","Enable encryption at rest","Increase storage capacity","Enabling VPC endpoints for your AWS Transfer Family server restricts network access to only your VPC, improving security."
"What is a key benefit of using AWS Transfer Family over self-managing an SFTP server on EC2?","Reduced operational overhead and maintenance","Increased customisation options","Lower costs for small workloads","Faster transfer speeds","AWS Transfer Family reduces operational overhead and maintenance compared to self-managing an SFTP server on EC2."
"In AWS Transfer Family, what is a common use case for custom identity providers?","Integrating with existing identity management systems","Enforcing multi-factor authentication","Implementing encryption at rest","Automating server provisioning","Custom identity providers allow AWS Transfer Family to integrate with existing identity management systems, providing flexible authentication options."
"When migrating an existing FTP server to AWS Transfer Family, what is a crucial step to ensure minimal disruption?","Migrating user accounts and data securely","Changing IP addresses","Updating DNS records","Disabling old servers simultaneously","Migrating user accounts and data securely is crucial for ensuring minimal disruption when migrating an existing FTP server to AWS Transfer Family."
"What is the purpose of the pre-signed URLs feature in AWS Transfer Family when used with S3?","Granting temporary access to upload or download specific files","Enabling server-side encryption","Automating file processing","Controlling network access","Pre-signed URLs in AWS Transfer Family, when used with S3, grant temporary access to upload or download specific files without requiring permanent credentials."
"How can you automate the deployment and management of AWS Transfer Family servers?","AWS CloudFormation or AWS CDK","AWS Systems Manager","AWS Lambda","AWS Config","AWS CloudFormation or AWS CDK can be used to automate the deployment and management of AWS Transfer Family servers."
"What should you consider when choosing the storage location (S3 bucket or EFS) for your AWS Transfer Family server?","Data access patterns and performance requirements","Storage costs alone","Encryption methods available","Compliance regulations","Data access patterns and performance requirements should be considered when choosing between S3 and EFS for your AWS Transfer Family server."
"Which of the following is NOT a supported protocol by AWS Transfer Family?","SMTP","SFTP","FTPS","FTP","SMTP (Simple Mail Transfer Protocol) is not a supported protocol by AWS Transfer Family; it is used for email."
"When troubleshooting file transfer issues in AWS Transfer Family, where can you find detailed logs of transfer activity?","Amazon CloudWatch Logs","AWS CloudTrail","Amazon S3 access logs","AWS Config history","Detailed logs of transfer activity can be found in Amazon CloudWatch Logs."
"What is the main benefit of using AWS Transfer Family for secure file transfer compared to building a custom solution?","Simplified management and reduced operational overhead","Lower initial setup costs","Increased control over underlying infrastructure","Faster transfer speeds","AWS Transfer Family simplifies management and reduces operational overhead compared to building a custom solution."
"Which setting determines how long AWS Transfer Family retains logs in CloudWatch?","CloudWatch log retention policy","S3 bucket lifecycle policy","Transfer Family server configuration","IAM policy","The CloudWatch log retention policy determines how long AWS Transfer Family retains logs in CloudWatch."
"What is the significance of specifying a virtual private cloud (VPC) for an AWS Transfer Family server?","Isolating the server within a private network","Increasing transfer speeds","Reducing storage costs","Simplifying user authentication","Specifying a VPC for an AWS Transfer Family server isolates the server within a private network, enhancing security."
"How does AWS Transfer Family handle data encryption in transit?","Via TLS or SSH protocols depending on the protocol used","By default, no encryption is provided","Using S3 server-side encryption","Through client-side encryption only","AWS Transfer Family handles data encryption in transit via TLS or SSH protocols, depending on the chosen protocol (FTPS, SFTP)."
"What is a common use case for using AWS Transfer Family with AWS Lambda?","Performing post-processing on uploaded files","Automating server scaling","Enabling multi-factor authentication","Managing user permissions","AWS Transfer Family is often used with AWS Lambda to perform post-processing on uploaded files, such as virus scanning or data transformation."
"How can you control access to specific files or directories within an S3 bucket used by AWS Transfer Family?","IAM policies and S3 bucket policies","VPC network ACLs","AWS Config rules","AWS Firewall Manager","Access to specific files or directories within an S3 bucket can be controlled using IAM policies and S3 bucket policies."
"What feature allows you to customise the greeting banner displayed to users when they connect to an AWS Transfer Family server?","Custom banner configuration","Message of the day (MOTD) setting","Login message customization","Server welcome message","AWS Transfer Family allows you to customize the greeting banner displayed to users when they connect."
"Which AWS service can be used to track user activity and API calls made to AWS Transfer Family?","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail tracks user activity and API calls made to AWS Transfer Family, providing an audit trail."
"What is a benefit of using AWS Transfer Family with Amazon EFS (Elastic File System)?","Shared file system access for multiple users","Cost-effective storage for large files","Automated data backup","Simplified data encryption","Using AWS Transfer Family with Amazon EFS provides a shared file system access for multiple users."
"Which of the following is NOT a valid endpoint type for an AWS Transfer Family server?","HTTP endpoint","VPC endpoint","Public endpoint","Private endpoint","AWS Transfer Family does not support HTTP endpoints. It uses VPC, Public or Private endpoints for secure file transfers."
"What type of data transformation can be performed by integrating AWS Transfer Family with AWS Lambda?","File format conversion, data validation, and metadata extraction","Data compression only","Data encryption only","User authentication only","Integrating AWS Transfer Family with AWS Lambda allows for file format conversion, data validation, and metadata extraction."
"How can you ensure high availability for your AWS Transfer Family server?","Multi-AZ deployment","Load balancing","Auto Scaling","Read replicas","While not directly supported, you can ensure high availability by using multiple servers behind a load balancer and replicating data across multiple S3 buckets."
"What is the purpose of a 'session policy' in AWS Transfer Family?","To limit the permissions a user can assume during a transfer session","To control network access","To define storage quotas","To manage encryption keys","A session policy limits the permissions a user can assume during a transfer session, providing fine-grained access control."
"Which of the following is a common requirement for using a custom hostname with AWS Transfer Family?","Valid SSL/TLS certificate","Static IP address","Public DNS record","Registered domain name","Using a custom hostname with AWS Transfer Family requires a valid SSL/TLS certificate to ensure secure connections."
"What is the main advantage of using AWS Transfer Family compared to using a traditional FTP server hosted on-premises?","Scalability and availability","Lower storage costs","Increased control over the file system","Simplified user management","AWS Transfer Family provides better scalability and availability compared to a traditional FTP server hosted on-premises."
"When should you consider using AWS Transfer Family instead of simply uploading files directly to S3 using the AWS CLI or SDK?","When you need to support standard file transfer protocols like SFTP or FTPS","When you need to upload very large files","When you need to automate data processing","When you need to encrypt data at rest","AWS Transfer Family is preferred when you need to support standard file transfer protocols like SFTP or FTPS, which are not directly supported by the AWS CLI or SDK for S3."
"What action should you take to minimise data transfer costs when using AWS Transfer Family?","Transfer data within the same AWS region","Compress files before transferring","Use accelerated transfers","Limit the number of concurrent transfers","Transferring data within the same AWS region minimizes data transfer costs."
"How can you automate user onboarding for AWS Transfer Family?","Using AWS Lambda to create users and configure permissions","Manually creating users in the AWS console","Using AWS Systems Manager Automation","Using AWS IAM Role","Automating user onboarding can be achieved by using AWS Lambda to create users and configure permissions."
"What type of encryption is supported for data at rest when using Amazon S3 with AWS Transfer Family?","Server-Side Encryption (SSE) and Client-Side Encryption (CSE)","Client-Side Encryption (CSE) only","No Encryption","Server-Side Encryption (SSE) only","Amazon S3 supports Server-Side Encryption (SSE) and Client-Side Encryption (CSE) for data at rest."
"How does AWS Transfer Family handle the transfer of metadata associated with files?","Preserves and transfers metadata along with the file","Discards metadata during the transfer","Automatically converts metadata to a different format","Requires manual configuration to transfer metadata","AWS Transfer Family preserves and transfers metadata along with the file, maintaining file context."
"You need to ensure that users of your AWS Transfer Family server can only access a specific directory within an S3 bucket. How do you achieve this?","Configure a 'chroot' directory for each user","Configure an S3 bucket policy","Configure a network ACL","Configure a custom hostname","A 'chroot' directory isolates users to a specific directory within the S3 bucket, restricting their access to other parts of the bucket."
"Which security best practice should you implement when configuring user access in AWS Transfer Family?","Apply the principle of least privilege","Grant full S3 access to all users","Use shared SSH keys","Disable CloudTrail logging","Apply the principle of least privilege, granting users only the necessary permissions to access specific resources."
"Which of the following is NOT a feature of AWS Transfer Family?","Content delivery network (CDN) integration","Secure file transfer protocols (SFTP, FTPS, FTP)","Integration with AWS storage services (S3, EFS)","Automated post-processing using AWS Lambda","AWS Transfer Family does not offer direct content delivery network (CDN) integration."
"What is a key advantage of using VPC endpoints with AWS Transfer Family?","They provide private connectivity to AWS services without using public IPs","They increase transfer speeds","They reduce storage costs","They simplify user authentication","VPC endpoints provide private connectivity to AWS services without using public IPs, enhancing security and compliance."
"When configuring AWS Transfer Family, what is the purpose of a 'Service Role'?","Grants the service permissions to manage AWS resources on your behalf","Defines user authentication policies","Controls network access","Manages encryption keys","A 'Service Role' grants the AWS Transfer Family service permissions to manage AWS resources on your behalf, such as accessing S3 buckets."
"What is the primary function of AWS Transfer Family?","To provide secure file transfer services into and out of Amazon S3 or Amazon EFS","To manage AWS IAM roles and policies","To monitor network traffic between AWS resources","To encrypt data at rest in Amazon S3","AWS Transfer Family provides fully managed, secure file transfer services over SFTP, FTPS, and FTP directly into and out of Amazon S3 or Amazon EFS."
"Which protocol is NOT supported natively by AWS Transfer Family?","HTTP","SFTP","FTPS","FTP","AWS Transfer Family supports SFTP, FTPS, and FTP natively. HTTP is not a file transfer protocol supported by the service."
"When setting up an AWS Transfer Family server, which identity provider option is NOT supported?","AWS Cognito User Pool","AWS Managed Active Directory","Service Managed","IAM","AWS Transfer Family supports AWS Managed Active Directory, Service Managed and IAM for authenticating users. AWS Cognito User Pool is not directly supported."
"What is the use of the 'Post-authentication' workflow in AWS Transfer Family?","To execute custom logic after a user successfully authenticates","To prevent users from accessing specific directories","To encrypt data in transit","To authenticate users before they connect to the server","The 'Post-authentication' workflow allows you to execute custom logic, such as auditing or additional authorisation checks, after a user successfully authenticates to the Transfer Family server."
"Which storage service is typically used as the backend for AWS Transfer Family?","Amazon S3","Amazon EBS","Amazon Glacier","Amazon RDS","AWS Transfer Family is most commonly used with Amazon S3 as the backend storage service for storing and retrieving files."
"Which of the following AWS Transfer Family authentication methods requires no external user database?","Service Managed","AWS Directory Service for Microsoft Active Directory","Custom Identity Provider","IAM","When using Service Managed, AWS Transfer Family manages the user identities. No external user database such as Active Directory or custom identity provider is required."
"What is the purpose of the 'Home directory' setting in AWS Transfer Family user configuration?","To define the default directory that users are placed in after login","To specify the network interface used for file transfers","To configure encryption settings for the server","To set up logging for file transfer activity","The 'Home directory' setting defines the directory a user is placed in immediately after logging in to the AWS Transfer Family server."
"In AWS Transfer Family, what feature can be used to restrict user access to specific directories within an S3 bucket?","Scoped-down identity","MFA","VPC endpoints","CloudTrail","Scoped-down identity policies can be used to restrict user access to specific directories within an S3 bucket, ensuring users can only access the files they are authorised to see."
"Which AWS service can be integrated with AWS Transfer Family to provide monitoring and logging of file transfer activities?","AWS CloudWatch","AWS Config","AWS IAM","AWS Trusted Advisor","AWS CloudWatch can be integrated with AWS Transfer Family to provide detailed monitoring and logging of file transfer activities, enabling you to track usage and identify potential issues."
"You need to ensure that data transferred using AWS Transfer Family is encrypted both in transit and at rest. Which configuration options should you consider?","Enable encryption at rest for S3 and use SFTP or FTPS for encryption in transit.","Disable public access to S3 and use FTP","Use HTTP and encryption at rest for S3","Enable encryption at rest for EBS and use SFTP","To ensure both encryption in transit and at rest, enable encryption at rest for S3 (which is enabled by default) and use SFTP or FTPS protocols, as they provide encryption in transit."
"What is the primary function of the AWS Transfer Family service?","To provide secure file transfer capabilities directly into and out of Amazon S3 or Amazon EFS.","To manage AWS Identity and Access Management (IAM) roles.","To monitor network traffic in AWS.","To provide a fully managed database service.","AWS Transfer Family provides scalable, secure file transfer solutions for transferring files into and out of Amazon S3 or Amazon EFS using protocols like SFTP, FTPS, and FTP."
"Which of the following protocols is supported by AWS Transfer Family?","SFTP","SMTP","RDP","HTTP","SFTP (SSH File Transfer Protocol) is a secure file transfer protocol supported by AWS Transfer Family."
"With AWS Transfer Family, where can the files be directly stored?","Amazon S3 or Amazon EFS","Amazon EC2 instance store","Amazon EBS volumes","AWS Snowball Edge","AWS Transfer Family is designed to transfer files directly into Amazon S3 for object storage or Amazon EFS for file system storage."
"How does AWS Transfer Family enable secure access to your storage?","By integrating with AWS Identity and Access Management (IAM) and using access roles.","By using hardware security keys.","By disabling public access to storage.","By relying on the default VPC security groups only.","AWS Transfer Family relies on IAM roles and policies to grant secure access to the underlying storage. You can define granular permissions to control which users can access which data."
"What is a key benefit of using AWS Transfer Family over managing your own file transfer servers?","It eliminates the operational overhead of managing servers, patching, and scaling.","It offers unlimited storage capacity.","It provides automatic database backups.","It guarantees 100% uptime for your applications.","AWS Transfer Family is a fully managed service, which eliminates the operational burden of managing file transfer servers, including patching, scaling, and monitoring."
"Which of the following authentication methods can be used with AWS Transfer Family?","Service managed, AWS Directory Service, or custom identity provider.","AWS Certificate Manager (ACM).","Amazon Cognito user pools.","Hardware Multi-Factor Authentication (MFA).","AWS Transfer Family supports using a service managed identity provider, integrating with AWS Directory Service (such as Active Directory), or using a custom identity provider for user authentication."
"What is a primary use case for employing an AWS Transfer Family custom identity provider?","Integrating with an existing on-premises identity management system.","Enabling encryption at rest.","Improving network performance.","Automating the creation of IAM roles.","A custom identity provider is often used to integrate AWS Transfer Family with an existing on-premises or third-party identity management system, allowing you to leverage your existing user accounts and authentication mechanisms."
"In AWS Transfer Family, what does the term 'server' refer to?","A logical grouping of endpoints, authentication settings, and security policies for file transfer.","A physical server dedicated to file transfer operations.","An Amazon EC2 instance running a file transfer application.","An AWS Lambda function processing file transfers.","In AWS Transfer Family, a 'server' represents a logical grouping that encompasses the endpoints, authentication settings, and security policies required for facilitating file transfers."
"When setting up an AWS Transfer Family server, what is the purpose of configuring an endpoint?","To specify the network address where clients can connect to initiate file transfers.","To define the maximum transfer speed.","To configure logging settings.","To enable server-side encryption.","Configuring an endpoint in AWS Transfer Family involves specifying the network address (VPC endpoint, public IP address) that clients use to connect and initiate file transfers."
"If you need to move files securely between AWS S3 and an on-premises SFTP server, which AWS service is the most suitable?","AWS Transfer Family","AWS Storage Gateway","AWS DataSync","AWS CloudFront","AWS Transfer Family is specifically designed for securely transferring files directly into and out of Amazon S3 using protocols like SFTP."
"What is the primary purpose of the AWS Transfer Family service?","To provide secure file transfer capabilities to and from AWS storage services","To manage AWS Identity and Access Management (IAM) roles and permissions","To monitor network traffic within an AWS Virtual Private Cloud (VPC)","To automate the deployment of applications on AWS EC2 instances","AWS Transfer Family is designed specifically to enable secure file transfers into and out of AWS storage services like S3 and EFS."
"Which of the following protocols is supported by AWS Transfer Family for secure file transfers?","SFTP (SSH File Transfer Protocol)","HTTP (Hypertext Transfer Protocol)","SMTP (Simple Mail Transfer Protocol)","POP3 (Post Office Protocol version 3)","SFTP is a secure file transfer protocol supported by AWS Transfer Family."
"Which AWS service is commonly integrated with AWS Transfer Family to store the transferred files?","Amazon S3 (Simple Storage Service)","Amazon EC2 (Elastic Compute Cloud)","Amazon RDS (Relational Database Service)","Amazon ECS (Elastic Container Service)","AWS Transfer Family is frequently used to transfer files directly into and out of Amazon S3 buckets."
"What authentication methods are supported by AWS Transfer Family?","Service managed, Password-based, Public Key Authentication, AWS Managed Active Directory","AWS IAM user/role, Service-linked roles, Temporary Security Credentials, AWS STS","Multi-Factor Authentication (MFA), Windows Authentication, Kerberos, SAML","Cognito User Pools, Social Media Logins, Third-Party Identity Providers, OIDC","AWS Transfer Family supports service managed authentication, password-based authentication, Public Key Authentication and AWS Managed Active Directory."
"You need to ensure that only authorised users can access specific directories within your AWS Transfer Family endpoint. How can you achieve this?","By configuring logical directories and associating them with IAM roles and policies","By creating separate AWS accounts for each user","By implementing Network Access Control Lists (NACLs) on the VPC subnet","By using AWS CloudTrail to monitor file access","Logical directories combined with IAM roles and policies enable granular control over user access to specific file locations within Transfer Family."
"What is the benefit of using AWS Transfer Family over managing your own SFTP server on an EC2 instance?","AWS Transfer Family simplifies management, reduces operational overhead, and provides built-in scalability and security.","Managing your own SFTP server gives you more granular control over the underlying operating system.","Running your own SFTP server is always cheaper than using AWS Transfer Family.","AWS Transfer Family only supports S3 storage.","AWS Transfer Family is a fully managed service, which reduces operational overhead and simplifies management compared to managing your own server."
"Which feature of AWS Transfer Family allows you to automate pre- or post-processing of files transferred to S3?","Workflow execution","Automatic Scaling","File encryption","Virtual private cloud","AWS Transfer Family has the capability to trigger post-upload processing based on workflows."
"Your company wants to audit all file transfer activity performed through AWS Transfer Family. Which AWS service can be used to achieve this?","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail tracks API calls made to AWS services, including AWS Transfer Family, and logs the activity for auditing purposes."
"What is the purpose of Endpoint configuration when setting up AWS Transfer Family?","Defining the network access point for your transfer server, such as VPC or public internet.","Specifying the storage location for the transferred files.","Configuring the authentication methods for users.","Setting up monitoring and logging for the transfer server.","The endpoint determines how your transfer server is accessed, either through a VPC or publicly."
"A customer needs to migrate an existing on-premises SFTP server to AWS with minimal changes to their existing client applications. How can AWS Transfer Family help achieve this?","It supports standard SFTP clients and allows for integration with existing authentication systems.","It automatically converts files to different formats during the transfer.","It automatically replicates files across multiple AWS regions for disaster recovery.","It encrypts files in transit and at rest.","AWS Transfer Family supports standard SFTP clients, minimising disruption and the need for changes to client applications during migration."
"Which AWS Transfer Family protocol supports both user authentication using AWS IAM and access to S3 buckets?","SFTP","FTP","FTPS","AS2","SFTP (SSH File Transfer Protocol) supports both IAM authentication for users and direct access to S3 buckets, enabling secure and managed file transfers."
"In AWS Transfer Family, what is the primary function of a Server endpoint?","To define the network access point for file transfers","To encrypt data at rest","To control user permissions","To monitor file transfer activity","A Server endpoint in AWS Transfer Family defines the network access point (VPC endpoint) that clients will use to connect to your file transfer server."
"Which feature of AWS Transfer Family allows you to invoke custom workflows upon successful file upload?","Post-import processing workflows","Pre-import processing workflows","Real-time analytics dashboards","Server-side encryption","AWS Transfer Family supports post-import processing workflows, which can be triggered upon successful file uploads, allowing you to automate tasks like virus scanning or data transformation."
"Which identity provider options can be configured with AWS Transfer Family to authenticate users?","AWS Managed or Custom","Only AWS Managed","Only Custom","Only Active Directory","AWS Transfer Family allows you to choose between AWS Managed and Custom Identity Providers to authenticate users. The custom option allows you to connect to external identity services."
"What type of encryption does AWS Transfer Family use to protect data in transit?","TLS","SSL","AES-256","RSA","AWS Transfer Family uses TLS (Transport Layer Security) to encrypt data in transit, ensuring secure communication between clients and the transfer server."
"For AWS Transfer Family AS2 endpoint, what is the primary function of a trading partner profile?","To store the trading partner's certificate and endpoint information","To configure user permissions","To define file naming conventions","To track transfer statistics","The trading partner profile in AS2 endpoints is used to store the trading partner's certificate, AS2 identifiers, and endpoint information, enabling secure and trusted data exchange."
"Which AWS service is commonly integrated with AWS Transfer Family for data storage?","Amazon S3","Amazon EBS","Amazon Glacier","Amazon RDS","Amazon S3 is the most common storage service integrated with AWS Transfer Family, providing a scalable and durable object storage repository for transferred files."
"What is the key benefit of using AWS Transfer Family's 'Service Managed' option compared to a custom implementation?","Reduced operational overhead and simplified management","Unlimited customisation options","Higher performance throughput","Lower cost","The 'Service Managed' option in AWS Transfer Family reduces operational overhead and simplifies management by handling the underlying infrastructure and scaling for you."
"What is the purpose of the AWS Transfer Family pre-signed URL?","To grant temporary access to upload or download files from S3","To configure the server's hostname","To define the IP address range","To encrypt data at rest","Pre-signed URLs in AWS Transfer Family provide temporary access to upload or download files directly from S3, without requiring permanent access credentials, which provides a more secure solution."
"Which of the following AWS Transfer Family protocols is best suited for secure, reliable transfers that require confirmation of receipt?","AS2","SFTP","FTP","FTPS","AS2 (Applicability Statement 2) is designed for secure and reliable transfers that require confirmation of receipt, making it ideal for EDI (Electronic Data Interchange) and other business-critical applications."