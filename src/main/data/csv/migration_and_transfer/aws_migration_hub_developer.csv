"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS Migration Hub?","To track the progress of application migrations to AWS.","To manage AWS IAM users and roles.","To configure AWS VPC settings.","To provision EC2 instances.","Migration Hub provides a central location to track the progress of application migrations, regardless of the tools used."
"Which AWS service does Migration Hub integrate with to perform server migrations?","AWS Application Migration Service (MGN)","AWS Lambda","Amazon S3","Amazon CloudWatch","AWS Application Migration Service (MGN) is a service used to perform server migrations, and it integrates with Migration Hub for tracking."
"What is the purpose of the Migration Hub Discovery Connector?","To collect server inventory and performance data from on-premises environments.","To create VPC peering connections.","To manage AWS budgets.","To monitor EC2 instance CPU usage.","The Discovery Connector collects information about your on-premises servers to help you plan your migration."
"In AWS Migration Hub, what is an 'Application'?","A logical grouping of resources that represent a business application being migrated.","A container for AWS Lambda functions.","A tool for managing AWS CloudFormation stacks.","A service for load balancing traffic to EC2 instances.","Migration Hub uses the concept of an 'Application' to group the resources that make up a business application being migrated."
"Which of the following is a key benefit of using AWS Migration Hub?","Provides a single pane of glass for migration tracking.","Automatically optimises EC2 instance sizes.","Manages AWS security groups.","Creates AWS CloudTrail logs.","Migration Hub centralises migration tracking, providing a clear overview of the entire process."
"What type of data can the AWS Migration Hub Discovery Connector collect?","Server configuration, performance data and network dependencies.","AWS account credentials.","Employee contact information.","Competitor pricing data.","The Discovery Connector gathers information about server configuration, performance metrics, and network dependencies."
"How can you start using AWS Migration Hub?","By enabling the service in the AWS Management Console.","By downloading and installing a client application.","By purchasing a specific AWS support plan.","By creating an AWS Glue ETL job.","Migration Hub can be enabled directly from the AWS Management Console."
"What is the role of AWS Partner Network (APN) partners in relation to AWS Migration Hub?","APN partners can provide migration tools and services that integrate with Migration Hub.","APN partners manage AWS billing.","APN partners provide AWS training.","APN partners handle AWS compliance certifications.","APN partners offer various migration tools and services that integrate with Migration Hub, expanding the migration capabilities available."
"When using AWS Migration Hub, how do you associate migrated resources with a specific application?","By using the AWS Migration Hub console or API to tag resources.","By creating IAM roles with specific permissions.","By configuring AWS CloudWatch alarms.","By setting up VPC endpoints.","Resources are associated with an Application within Migration Hub by tagging them through the console or API."
"Which of the following is NOT a feature of AWS Migration Hub?","Automated code refactoring.","Migration status tracking.","Discovery of on-premises servers.","Integration with migration tools.","Migration Hub doesn't provide automated code refactoring; it focuses on discovery and tracking."
"What level of support does AWS Migration Hub require?","Migration Hub is a free service, but the tools it integrates with might have costs.","Requires a paid AWS Support plan.","Requires a custom support agreement.","Requires an Enterprise support plan.","Migration Hub itself is free, but the services and tools it integrates with, like MGN, might have associated costs."
"You want to visualise the dependencies between servers in your on-premises environment before migrating. Which AWS Migration Hub tool/feature can help you with this?","Discovery Connector's dependency mapping.","Migration Evaluator.","Application Discovery Service (ADS).","Cloud Adoption Readiness Tool (CART).","The Discovery Connector can map dependencies between servers, aiding in migration planning."
"What is the primary benefit of using AWS Migration Hub Strategy Recommendations?","Provides suggested migration strategies based on discovered application characteristics.","Automates the entire migration process.","Automatically generates CloudFormation templates.","Offers free AWS training courses.","Strategy Recommendations analyses discovered application characteristics to suggest appropriate migration strategies (rehost, replatform, etc.)."
"Which AWS service provides a detailed cost assessment for your migration project, integrating with AWS Migration Hub?","Migration Evaluator","AWS Cost Explorer","AWS Budgets","AWS Pricing Calculator","Migration Evaluator helps estimate the costs associated with migrating to AWS."
"How does AWS Migration Hub help with migration governance?","By providing a central view of migration status and progress.","By automatically enforcing security policies.","By managing AWS resource costs.","By providing automated compliance reports.","Migration Hub aids in governance by providing a centralised view of the migration, making it easier to track and manage the process."
"What is the significance of 'Migration Task' within AWS Migration Hub?","Represents a specific unit of work being performed during a migration (e.g., server replication).","Represents a security audit finding.","Represents a billable item in your AWS account.","Represents an Amazon S3 bucket policy.","A 'Migration Task' tracks a specific unit of work within a migration, such as server replication."
"Which AWS service can be used with AWS Migration Hub to perform agentless discovery of servers?","AWS Application Discovery Service (ADS)","Amazon Inspector","AWS Config","AWS Systems Manager","AWS Application Discovery Service (ADS) can perform agentless discovery and integrate with Migration Hub."
"How can you provide custom information about your migration process within AWS Migration Hub?","By using the API to update the status of Migration Tasks.","By creating AWS CloudWatch dashboards.","By configuring AWS Lambda functions.","By customising IAM policies.","The API allows you to update Migration Task statuses and add custom information relevant to your migration."
"Which of the following is a common migration strategy supported by AWS Migration Hub's Strategy Recommendations?","Rehost (Lift and Shift)","Reinvent","Retain","Replace","Rehost (Lift and Shift) is a common migration strategy and is often recommended by Migration Hub."
"You need to track the migration of a database to Amazon RDS using AWS Migration Hub. Which service will you likely integrate with?","AWS Database Migration Service (DMS)","Amazon SQS","Amazon SNS","AWS Glue","AWS DMS is used for database migrations and integrates with Migration Hub for tracking progress."
"What is the purpose of the 'Home Region' setting in AWS Migration Hub?","To specify the AWS region where migration tracking data is stored.","To specify the region where EC2 instances are launched.","To specify the region for AWS billing.","To specify the region for AWS support.","The 'Home Region' determines where the migration tracking data is stored in Migration Hub."
"When migrating applications using AWS Migration Hub, how can you track the cutover process?","By updating the status of Migration Tasks to reflect cutover progress.","By creating AWS CloudWatch events.","By configuring AWS Config rules.","By setting up VPC flow logs.","Updating Migration Task statuses allows you to track the cutover phase within Migration Hub."
"What is the relationship between AWS Migration Hub and AWS CloudEndure Migration?","CloudEndure Migration replicates servers to AWS, and Migration Hub tracks the progress.","CloudEndure Migration manages AWS IAM roles.","CloudEndure Migration provides AWS cost optimisation.","CloudEndure Migration automates AWS security audits.","CloudEndure Migration is a server replication tool that integrates with Migration Hub for tracking."
"How does AWS Migration Hub help reduce the risk of migration projects?","By providing visibility into the migration progress and identifying potential issues.","By automatically resolving security vulnerabilities.","By managing AWS compliance certifications.","By automating AWS cost optimisation.","Visibility into migration progress helps identify and address issues early on, reducing overall risk."
"What is the primary use case for AWS Migration Hub's 'Discover' functionality?","To identify and assess the applications running in your on-premises environment.","To discover new AWS services and features.","To discover AWS IAM users and roles.","To discover AWS CloudFormation templates.","The 'Discover' functionality helps you identify and assess the applications running in your on-premises environment before migrating."
"Which of the following can be integrated with AWS Migration Hub to automate the migration of virtual machines (VMs)?","AWS Application Migration Service (MGN)","Amazon ECS","Amazon EKS","AWS CodeDeploy","AWS Application Migration Service (MGN) helps automate the migration of virtual machines and integrates with Migration Hub for central monitoring and reporting."
"What is the purpose of 'Server Insights' in AWS Migration Hub?","To provide detailed performance metrics and utilisation data for discovered servers.","To provide security recommendations for servers.","To manage server patching and updates.","To automate server scaling.","Server Insights in Migration Hub provide detailed performance and utilisation data for discovered servers, aiding in resource planning."
"Which AWS service is most commonly used to migrate databases in conjunction with AWS Migration Hub for tracking purposes?","AWS Database Migration Service (DMS)","Amazon Redshift","Amazon DynamoDB","AWS Glue","AWS Database Migration Service (DMS) is most commonly used for database migrations and can be tracked via AWS Migration Hub."
"When using AWS Migration Hub, how can you determine the dependencies between applications during the migration process?","By analysing the dependency mappings created by the Discovery Connector.","By reviewing AWS CloudTrail logs.","By examining AWS Config rules.","By checking VPC flow logs.","Dependency mappings created by the Discovery Connector can be analysed to understand application dependencies during migration."
"What is a key advantage of using AWS Migration Hub over manually tracking your migration?","Provides a centralised and automated view of the entire migration process.","Automatically configures AWS networking.","Automatically manages AWS security groups.","Automatically optimises EC2 instance sizes.","Migration Hub provides a centralized and automated view, reducing the manual effort involved in tracking migrations."
"What is the purpose of creating 'Migration Tasks' within AWS Migration Hub?","To represent and track specific migration activities, such as server replication or database migration.","To define AWS IAM roles and permissions.","To configure AWS CloudWatch alarms.","To create VPC endpoints.","Migration Tasks represent and track specific migration activities."
"Which of the following is a characteristic of AWS Migration Hub?","It is a central place to track application migration progress.","It replaces existing migration tools.","It automatically migrates your applications.","It requires a dedicated migration team.","Migration Hub acts as a central hub for tracking progress, integrating with existing migration tools."
"When considering a migration project, which AWS Migration Hub feature helps assess the readiness of your organisation for cloud adoption?","Cloud Adoption Readiness Tool (CART)","Migration Evaluator","AWS Trusted Advisor","AWS Well-Architected Framework","The Cloud Adoption Readiness Tool (CART) helps evaluate your organisation's readiness for cloud adoption."
"What is the difference between using Migration Hub and managing the migrations yourself?","Migration Hub provides a central location to manage and monitor progress, whereas managing migrations yourself can be fragmented and difficult to track.","Migration Hub requires a paid subscription.","Migration Hub provides faster migration speeds.","Migration Hub automatically handles all migration complexities.","Migration Hub offers a centralised view for managing and monitoring migrations, improving control and visibility, unlike fragmented self-managed approaches."
"What role does application discovery play in the AWS Migration Hub process?","It identifies the resources needed for migration.","It manages AWS IAM permissions.","It monitors the performance of EC2 instances.","It creates AWS CloudTrail logs.","Application discovery identifies the resources needed for migration planning and execution."
"Which of the following is NOT a way AWS Migration Hub aids in migration planning?","Providing estimated costs.","Offering migration strategy recommendations.","Automatically generating documentation.","Mapping application dependencies.","While Migration Hub offers cost estimates, strategy recommendations, and dependency mapping, it doesn't automatically generate documentation."
"What is the significance of the 'Home Region' within the AWS Migration Hub context?","It is where the migration data is stored and processed.","It is where the applications will be migrated.","It is the region that contains the AWS support team.","It is the region that handles billing.","The Home Region dictates where the migration data is stored and processed within AWS Migration Hub."
"When using AWS Migration Hub, what kind of status updates can be provided for Migration Tasks?","In progress, completed, failed.","Pending, approved, rejected.","Active, inactive, archived.","Healthy, unhealthy, unknown.","The available status updates for Migration Tasks are typically 'in progress', 'completed', and 'failed'."
"Which of the following is a common use case for the AWS Migration Hub Discovery Connector?","Discovering on-premises servers and their configurations.","Discovering AWS IAM users and roles.","Discovering AWS CloudFormation templates.","Discovering AWS pricing information.","The Discovery Connector's primary use case is to discover on-premises servers and their configurations for migration planning."
"What type of information is typically gathered by the AWS Application Discovery Service (ADS) for integration with AWS Migration Hub?","Server inventory, application dependencies, and performance data.","AWS account credentials.","Employee contact information.","Competitor pricing data.","AWS Application Discovery Service collects comprehensive data including server inventory, application dependencies, and performance metrics."
"Which of the following is a key factor in successfully using AWS Migration Hub effectively?","Properly tagging resources with Migration Hub metadata.","Having extensive AWS certification.","Using only AWS native migration tools.","Having a large dedicated migration team.","Properly tagging resources is crucial for associating them with applications and tracking their migration status within Migration Hub."
"How does AWS Migration Hub assist in visualising the migration process?","By providing a centralised dashboard showing the status of each migration task.","By automatically generating architectural diagrams.","By automatically creating network topologies.","By automatically creating AWS CloudFormation templates.","Migration Hub provides a dashboard that centralizes the status of each migration task, enhancing visibility into the migration process."
"Which AWS service, when integrated with AWS Migration Hub, is most suited for migrating physical servers?","AWS Application Migration Service (MGN)","AWS CodeDeploy","Amazon SQS","AWS Lambda","AWS Application Migration Service (MGN) is designed to migrate physical servers and integrates well with Migration Hub."
"What is the primary role of the Migration Hub in a large-scale migration project?","Providing a central view of progress and status.","Automating security compliance.","Optimising network bandwidth.","Managing AWS support cases.","The primary role of Migration Hub is to provide a centralized view of progress and status, ensuring stakeholders have a comprehensive understanding of the migration."
"Which of the following describes the relationship between AWS Migration Hub and migration tools like AWS Server Migration Service (SMS)?","Migration Hub tracks the progress of migrations performed by tools like SMS.","Migration Hub replaces tools like SMS.","Migration Hub competes with tools like SMS.","Migration Hub has no connection to tools like SMS.","Migration Hub complements migration tools by providing a central point for tracking and reporting on migration tasks."
"How does AWS Migration Hub support a phased migration approach?","By allowing you to track migration tasks and progress for each phase of the migration.","By automatically deploying applications in phases.","By automatically configuring network settings for each phase.","By automatically scaling EC2 instances based on the current phase.","Migration Hub allows you to track migration tasks and progress for each phase, providing visibility and control over phased migrations."
"What is the primary function of AWS Migration Hub?","To provide a central location to track the progress of application migrations to AWS.","To manage AWS Identity and Access Management (IAM) roles.","To monitor the health of EC2 instances.","To manage AWS CloudTrail logs.","AWS Migration Hub provides a single location to track the progress of application migrations, regardless of the tools used."
"In AWS Migration Hub, what does a 'Discovery Connector' do?","Collects server inventory and performance data from on-premises environments.","Connects to AWS CloudTrail for security analysis.","Optimises database queries on RDS instances.","Integrates with AWS Lambda functions for event-driven architectures.","The Discovery Connector collects inventory and performance data, which is then used to plan and execute migrations."
"When using AWS Migration Hub, what type of data is typically collected by the AWS Discovery Service?","Server utilisation and application dependencies.","Website traffic and user behaviour.","Real-time stock market data.","Geographic location of mobile devices.","AWS Discovery Service collects data about the servers, their configuration, and the applications running on them, including server utilisation and application dependencies."
"Within AWS Migration Hub, how are applications typically represented?","As a collection of related resources or servers.","As a single EC2 instance.","As a set of S3 buckets.","As a CloudFormation stack.","Applications in Migration Hub are viewed as groups of related servers or resources, allowing for a holistic migration strategy."
"Which AWS service integrates with AWS Migration Hub to provide automated server migration capabilities?","AWS Application Migration Service (MGN)","AWS CloudFormation","Amazon CloudWatch","AWS Config","AWS Application Migration Service (MGN) integrates with Migration Hub to provide a streamlined approach for server migration."
"What is the main benefit of using AWS Migration Hub's 'Migration Tracking' feature?","Provides a centralised view of migration progress and status.","Automatically scales EC2 instances.","Encrypts data in transit between on-premises and AWS.","Performs automatic code refactoring.","Migration tracking in Migration Hub gives visibility into the progress and status of each component of the migration."
"How does AWS Migration Hub assist in right-sizing your migrated resources?","By providing performance data and recommendations for optimal instance types.","By automatically reducing the size of EBS volumes.","By optimizing database query performance.","By suggesting cost-effective pricing models.","Migration Hub leverages discovery tools to analyse on-premises workloads and provide recommendations for optimal AWS instance types, ensuring resources are right-sized."
"Which of the following is NOT a key feature of AWS Migration Hub?","Automated patching of operating systems.","Migration Tracking.","Discovery.","Integration with Migration Tools.","Migration Hub focuses on discovery, tracking and integration with other services and tools. It does not provide OS patching."
"What role does AWS Migration Evaluator play within an AWS migration project tracked by AWS Migration Hub?","Provides total cost of ownership (TCO) analysis and migration recommendations.","Automatically migrates data to S3 buckets.","Manages IAM roles and permissions for migrated applications.","Performs vulnerability scanning of migrated servers.","AWS Migration Evaluator focuses on providing TCO analysis and specific recommendations for migrating to AWS."
"What is the purpose of tagging resources in AWS Migration Hub?","To logically group and track resources related to a specific migration.","To automatically encrypt data stored in S3.","To enforce security policies on EC2 instances.","To automatically scale RDS database instances.","Tagging allows you to group resources and monitor progress based on specific migration projects or applications."
"Regarding AWS Migration Hub, which of the following is true about its cost?","AWS Migration Hub itself is provided at no additional charge.","There is a per-server monthly fee for using AWS Migration Hub.","Costs depend on the number of applications being migrated.","You pay for the amount of data stored within AWS Migration Hub.","Migration Hub does not have an associated charge, but you will still be charged for the migrated infrastructure on AWS."
"Which of the following is a common use case for AWS Migration Hub's 'Dependency Mapping' feature?","Understanding application interdependencies before migrating to AWS.","Automatically creating CloudFormation templates.","Optimising network latency between on-premises and AWS.","Generating compliance reports for migrated applications.","Dependency mapping is key to understanding how applications communicate and relate to each other, essential for planning a successful migration."
"How does AWS Migration Hub help in consolidating migration efforts?","By providing a unified view of all migration activities across different AWS services.","By automatically merging multiple AWS accounts.","By automatically creating a disaster recovery plan.","By replacing existing on-premises infrastructure with AWS services.","Migration Hub helps consolidate migration efforts by providing a single pane of glass for tracking and managing migrations across different AWS services and tools."
"When using AWS Migration Hub, how do you track the status of individual servers being migrated?","Using the 'Migration Tracking' dashboard.","Using AWS CloudTrail logs.","Using Amazon CloudWatch metrics.","Using AWS Config rules.","The Migration Tracking dashboard provides a central location to monitor the status of each server or resource being migrated."
"How can AWS Migration Hub assist with compliance requirements during a migration project?","By providing audit trails of all migration activities.","By automatically encrypting all data in transit.","By generating compliance reports for migrated applications.","By automatically patching security vulnerabilities.","Migration Hub, when used in conjunction with services like AWS CloudTrail, provides audit trails that can be used for compliance purposes."
"Which AWS service would you typically use in conjunction with AWS Migration Hub to replicate on-premises servers to AWS?","AWS Application Migration Service (MGN).","Amazon S3.","AWS Lambda.","Amazon CloudFront.","AWS Application Migration Service (MGN) is specifically designed for server replication during migration projects and integrates seamlessly with AWS Migration Hub."
"What is a 'Migration Task' in the context of AWS Migration Hub?","A representation of a specific migration activity, such as server replication.","An automated script for migrating databases.","A virtual appliance for collecting server inventory data.","A security policy for controlling access to migrated resources.","A Migration Task represents a specific activity or step in the migration process, such as server replication or database migration."
"What type of information can be discovered using AWS Discovery Service and reported to AWS Migration Hub?","Hardware specifications of on-premises servers.","Customer billing information.","Employee social security numbers.","Competitor pricing data.","AWS Discovery Service focuses on collecting information about the hardware and software configuration of on-premises servers."
"How does AWS Migration Hub help ensure data integrity during a migration?","By providing tools to validate data replication and verify data consistency.","By automatically encrypting all data in transit.","By automatically backing up all data before the migration starts.","By automatically compressing all data to reduce transfer time.","Migration Hub does not directly ensure data integrity, but it integrates with tools that can validate data replication and verify data consistency."
"Which of the following is a key benefit of using AWS Migration Hub for a large-scale migration project?","It provides a single pane of glass for tracking and managing migration activities.","It automatically migrates all on-premises applications to AWS.","It eliminates the need for any manual migration efforts.","It completely automates the entire migration process.","The main benefit of Migration Hub is its ability to centralise tracking and management, not complete automation."
"What is the relationship between AWS Migration Hub and AWS CloudEndure Migration (now AWS Application Migration Service)?","AWS Migration Hub integrates with AWS Application Migration Service to track migration progress.","AWS Migration Hub replaces AWS CloudEndure Migration.","AWS Migration Hub is a prerequisite for using AWS CloudEndure Migration.","AWS Migration Hub and AWS CloudEndure Migration are completely independent services.","AWS Migration Hub can track the progress of migrations performed by AWS Application Migration Service, providing a unified view."
"In the context of AWS Migration Hub, what does 'Application Discovery' refer to?","Identifying and documenting the applications running in your on-premises environment.","Automatically converting applications to serverless architectures.","Finding and installing new applications from the AWS Marketplace.","Identifying and removing unused applications from your AWS environment.","Application discovery involves identifying and documenting the applications, their dependencies, and their configurations in the on-premises environment."
"What is the main purpose of the AWS Migration Hub console?","To provide a centralised interface for managing and tracking migrations.","To provide command-line access to AWS migration services.","To provide a repository for storing migration scripts.","To provide a real-time monitoring dashboard for AWS resources.","The AWS Migration Hub console offers a single point of access for managing and tracking migration activities."
"How can AWS Migration Hub help reduce the risk of migration failures?","By providing visibility into dependencies and potential issues before migration.","By automatically rolling back failed migrations.","By automatically fixing any errors that occur during migration.","By automatically testing applications after migration.","Migration Hub helps reduce risk by providing insights into dependencies and potential problems before the actual migration occurs."
"What role does 'AWS Partner Network (APN)' play in the context of AWS Migration Hub?","APN partners can provide migration services and tools that integrate with AWS Migration Hub.","APN partners are responsible for managing the AWS Migration Hub service.","APN partners are the primary users of AWS Migration Hub.","APN partners provide training on how to use AWS Migration Hub.","APN partners often offer services and tools that integrate with AWS Migration Hub, helping organisations with their migration efforts."
"What is the purpose of defining 'Migration Strategies' in AWS Migration Hub?","To outline the approach and methodology for migrating specific applications or workloads.","To define the security policies for migrated resources.","To define the backup and recovery procedures for migrated data.","To define the pricing models for migrated services.","Migration strategies help define how to approach the migration of different applications or workloads, considering factors like complexity and dependencies."
"How can you use AWS Migration Hub to assess the readiness of your applications for migration?","By analysing application dependencies and compatibility with AWS services.","By automatically performing penetration testing on your applications.","By automatically optimising your applications for cloud performance.","By automatically generating documentation for your applications.","Migration Hub helps assess readiness by analysing dependencies and compatibility, giving insights into potential migration challenges."
"Which of the following is a valid strategy for migrating databases using AWS Migration Hub and related services?","Rehosting, replatforming, or refactoring the database.","Deleting the database and recreating it from scratch in AWS.","Migrating the database to a competitor's cloud platform.","Keeping the database on-premises and accessing it from AWS.","Rehosting (lift and shift), replatforming (modifying the database), and refactoring (completely redesigning) are all valid database migration strategies."
"What is the benefit of using AWS Migration Hub with multiple migration tools?","It provides a centralised view of progress regardless of the tools used.","It automatically selects the best migration tool for each application.","It eliminates the need to use multiple migration tools.","It automatically converts data between different migration tool formats.","Migration Hub provides a central view of progress even if you're using multiple tools for different parts of the migration."
"How does AWS Migration Hub support iterative migration approaches?","By allowing you to migrate applications in phases and track progress at each stage.","By automatically migrating all applications at once.","By requiring you to migrate all applications simultaneously.","By preventing you from migrating applications until all dependencies are resolved.","Migration Hub allows for phased migrations, providing visibility at each stage."
"When planning a migration with AWS Migration Hub, what does 'TCO analysis' help you determine?","The total cost of ownership of running your workloads on AWS.","The total cost of the migration project itself.","The total cost of your on-premises infrastructure.","The total cost of AWS support services.","TCO analysis helps you understand the overall cost of running your workloads on AWS, factoring in infrastructure, operational expenses, and other costs."
"How does AWS Migration Hub help in managing the complexity of a hybrid cloud environment during migration?","By providing a centralised view of both on-premises and AWS resources.","By automatically managing security policies across both environments.","By automatically optimising network performance between both environments.","By automatically syncing data between both environments.","Migration Hub provides a unified view of both on-premises and AWS resources, making it easier to manage the complexity of a hybrid environment."
"What type of reports can you generate using AWS Migration Hub?","Migration progress reports, dependency analysis reports, and readiness assessment reports.","Financial statements, marketing reports, and sales forecasts.","Customer satisfaction surveys, employee performance reviews, and market research reports.","Security audit reports, compliance reports, and penetration testing results.","Migration Hub provides reports focused on tracking progress, understanding dependencies, and assessing readiness for migration."
"In AWS Migration Hub, what is the significance of 'Migration Events'?","They represent specific actions or milestones during the migration process.","They represent security breaches or incidents during the migration process.","They represent scheduled maintenance windows for migrated resources.","They represent user login attempts to migrated applications.","Migration events are key actions and milestones that occur during the migration, providing a timeline of the process."
"How can AWS Migration Hub help with post-migration validation?","By providing tools to verify that applications are functioning correctly in AWS.","By automatically rolling back to the on-premises environment if issues are detected.","By automatically optimising application performance after migration.","By automatically updating application documentation after migration.","Migration Hub, in conjunction with other services, can help validate that applications are functioning correctly after migration."
"What is the relationship between AWS Migration Hub and AWS Server Migration Service (SMS) (now deprecated)?","AWS Migration Hub used to integrate with AWS Server Migration Service (SMS) but SMS is now deprecated.","AWS Migration Hub replaced AWS Server Migration Service (SMS).","AWS Migration Hub is a prerequisite for using AWS Server Migration Service (SMS).","AWS Migration Hub and AWS Server Migration Service (SMS) are completely independent services.","AWS Migration Hub used to integrate with the older AWS Server Migration Service (SMS) which has since been deprecated. AWS Application Migration Service (MGN) is the generally preferred option now."
"How does AWS Migration Hub facilitate collaboration between different teams involved in a migration project?","By providing a centralised platform for tracking progress and sharing information.","By automatically assigning tasks to team members based on their skill sets.","By automatically resolving conflicts between different teams.","By automatically generating communication plans for the migration project.","Migration Hub's centralised platform promotes collaboration by providing a single source of truth for tracking progress and sharing relevant information."
"What is the primary benefit of using AWS Migration Hub for heterogeneous migrations (e.g., migrating from different on-premises environments)?","It provides a consistent view and tracking mechanism regardless of the source environment.","It automatically converts data between different source environments.","It eliminates the need for any specialised migration tools for each environment.","It automatically optimises application performance for each environment.","Migration Hub's consistent tracking mechanism is particularly valuable when dealing with the complexities of heterogeneous migrations."
"When using AWS Migration Hub, how can you customise the migration dashboard to focus on specific applications or workloads?","By using tags and filters to create custom views of the migration progress.","By creating separate dashboards for each application or workload.","By customising the underlying data model of AWS Migration Hub.","By manually editing the configuration files of AWS Migration Hub.","Tags and filters allow you to create custom views and focus on the specific areas of the migration that are most important to you."
"How can AWS Migration Hub help you identify and address potential bottlenecks in your migration process?","By providing performance metrics and insights into resource utilisation.","By automatically optimising network bandwidth during migration.","By automatically scaling AWS resources to handle peak migration loads.","By automatically rescheduling migration tasks to avoid contention.","Migration Hub, by integrating with discovery and monitoring tools, can help identify performance bottlenecks and resource utilisation issues."
"What is a common use case for integrating AWS Migration Hub with third-party migration tools?","To centralise tracking and reporting across different migration solutions.","To automatically convert data between different migration tool formats.","To eliminate the need for any manual configuration of third-party tools.","To automatically optimise the performance of third-party tools.","Integration with third-party tools allows you to maintain a single, unified view of your migration progress, regardless of the tools you are using."
"How does AWS Migration Hub help you ensure consistency across multiple migrations?","By providing a standard framework and methodology for all migration projects.","By automatically synchronising data between different environments.","By automatically enforcing security policies across all migrated resources.","By automatically testing applications after each migration.","Migration Hub promotes consistency by offering a standard framework and methodology for all migration efforts, leading to a more predictable and reliable process."
"What type of data visualisation is available within AWS Migration Hub to represent migration progress?","Graphical dashboards, charts, and reports.","3D models of the on-premises infrastructure.","Real-time video feeds of the migration process.","Interactive simulations of the migrated environment.","Migration Hub provides graphical dashboards, charts, and reports to visualise migration progress and key metrics."
"When using AWS Migration Hub, what considerations are important for data residency and compliance requirements?","Ensuring that data is migrated to AWS regions that meet your specific regulatory requirements.","Encrypting all data in transit regardless of regulatory requirements.","Ignoring data residency and compliance requirements during the migration process.","Storing all data in a single AWS region for simplicity.","Data residency and compliance are critical considerations, and you must ensure that your data is migrated to regions that meet your specific requirements."
"In AWS Migration Hub, what is the primary purpose of a 'discovery tool'?","To collect information about your on-premises environment.","To automatically migrate applications.","To manage AWS costs.","To configure network settings.","Discovery tools gather detailed information about your existing servers, applications, and dependencies, forming the basis for migration planning."
"What type of strategy does AWS Migration Hub assist with when planning a cloud migration?","Rehost, Replatform, Refactor, Repurchase, Retain, Retire","Only Rehost","Only Replatform","Only Refactor","Migration Hub is built to assist with using all 6 of the R's, not just one."
"Which AWS service is commonly used alongside AWS Migration Hub to perform server migrations?","AWS Application Migration Service (MGN)","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Application Migration Service (MGN) is a managed service for lift-and-shift migrations that integrates with Migration Hub for tracking progress."
"What is the main advantage of using AWS Migration Hub for tracking migration progress?","Provides a central view of your migration status.","Automatically optimizes your AWS costs.","Secures your network connections.","Recommends instance types.","Migration Hub provides a single place to monitor the progress of your migrations, regardless of the tools you are using."
"Within AWS Migration Hub, what does the term 'application' typically represent?","A logical grouping of servers and other resources.","A single EC2 instance.","A database connection string.","A security group configuration.","In Migration Hub, an 'application' represents a collection of related servers, databases, and other resources that work together."
"Which of these is NOT a key function of AWS Migration Hub?","Automated application refactoring","Migration tracking and reporting","Centralised view of migration progress","Integration with various migration tools","Migration Hub provides migration tracking and not automated application refactoring."
"When using AWS Migration Hub, which AWS service provides detailed dependency mapping information to aid in migration planning?","AWS Application Discovery Service","Amazon Inspector","AWS Trusted Advisor","AWS IAM","AWS Application Discovery Service provides detailed dependency mapping that is consumed by Migration Hub to build the migration plan."
"What is a key benefit of using AWS Migration Hub Strategy Recommendations?","It suggests optimal migration strategies for your applications.","It automatically applies security patches to migrated servers.","It encrypts data in transit.","It manages DNS records.","Strategy Recommendations helps you determine the best approach (rehost, replatform, etc.) for each application based on its characteristics."
"How does AWS Migration Hub help manage dependencies during a migration project?","By visualising application dependencies.","By automatically resolving dependency conflicts.","By forcing a complete rebuild of all application components.","By ignoring dependencies to speed up the migration.","Migration Hub helps you understand and manage application dependencies, which is essential for a successful migration."
"What is the purpose of creating an 'Application' within AWS Migration Hub?","To group servers and track their migration status.","To define security policies for migrated resources.","To create a backup of on-premises data.","To provision new AWS resources.","Creating an application in Migration Hub allows you to group related servers and track their migration as a unit."
"When using AWS Migration Hub, how can you track the progress of a server migration?","By monitoring the status displayed in the Migration Hub console.","By checking the AWS CloudTrail logs.","By manually inspecting the migrated server.","By using AWS Config rules.","The Migration Hub console provides a central view of the status of each server migration, showing progress and any issues."
"Which AWS service, when integrated with AWS Migration Hub, allows for agentless discovery of on-premises servers?","AWS Application Discovery Service","AWS Systems Manager","AWS CloudWatch Logs","Amazon Inspector","AWS Application Discovery Service can perform agentless discovery to gather information about your on-premises environment."
"What is the main benefit of using AWS Migration Hub for application discovery?","It provides detailed application dependency mapping.","It automatically optimises application performance.","It prevents security breaches during migration.","It eliminates the need for migration planning.","Application discovery with Migration Hub helps you understand application dependencies and plan your migration more effectively."
"What does AWS Migration Hub use to identify servers being migrated?","Agent installed on the server","Server IP address","Server hostname","MAC address","Migration Hub uses an agent installed on the server to identify and track its migration status."
"What information does AWS Migration Hub typically collect about on-premises servers?","Operating system, installed applications, and resource utilisation.","User account passwords","Firewall rules","Network topology diagrams.","Migration Hub collects information about the operating system, installed applications, resource utilization, and other relevant details."
"Which migration strategy is best supported by AWS Application Migration Service (MGN) and tracked within AWS Migration Hub?","Rehost (Lift and Shift)","Replatform (Lift, Tweak, and Shift)","Refactor (Re-architect)","Repurchase (Drop and Shop)","Application Migration Service (MGN) is specifically designed for rehosting (lift and shift) migrations."
"What reporting capabilities does AWS Migration Hub provide?","Migration status reports.","Network bandwidth utilisation reports.","Security vulnerability reports.","Cost optimisation reports.","Migration Hub offers reporting on the status of your migrations, helping you track progress and identify potential issues."
"When integrating a third-party migration tool with AWS Migration Hub, what is required for the integration to function?","The tool must support the AWS Migration Hub API.","The tool must be open source.","The tool must be certified by AWS.","The tool must be installed on an EC2 instance.","For a third-party tool to integrate with Migration Hub, it needs to be able to communicate with the Migration Hub API to report its progress."
"How can AWS Migration Hub assist with cost management during a migration?","By identifying underutilised on-premises servers.","By automatically reducing AWS resource usage.","By providing real-time cost estimates.","By negotiating discounts with AWS.","Migration Hub can help identify underutilised servers in your on-premises environment, which can lead to cost savings when migrating to AWS."
"What is the first step in using AWS Migration Hub to manage a migration project?","Discover your on-premises environment.","Provision new AWS resources.","Define security policies.","Install the AWS CLI.","The first step is to understand your existing environment by discovering your servers and applications."
"Within AWS Migration Hub, what are 'Migration Tasks'?","Individual steps in the migration process for a server or application.","Automated scripts for migrating data.","Virtual machines used for testing.","Network configurations for migrated resources.","Migration Tasks represent the individual steps involved in migrating a server or application, such as data replication and cutover."
"What is the role of AWS Migration Hub in a hybrid cloud environment?","To manage the migration of workloads to AWS.","To manage on-premises infrastructure.","To provide a unified view of all IT resources.","To automate software deployments.","Migration Hub primarily focuses on managing and tracking the migration of workloads from on-premises environments to AWS."
"Which AWS service can be used to assess the readiness of your applications for migration, and is integrated with AWS Migration Hub?","AWS Migration Evaluator","AWS Trusted Advisor","AWS Cloud Conformity","Amazon Inspector","AWS Migration Evaluator is designed to assess application readiness and is integrated with Migration Hub for a seamless experience."
"How does AWS Migration Hub help in reducing the risks associated with migration projects?","By providing a centralised view of migration progress.","By automatically resolving application compatibility issues.","By guaranteeing zero downtime during migration.","By eliminating the need for testing.","By providing a central view, it makes progress transparent."
"What type of data can AWS Migration Hub collect about your on-premises infrastructure?","Server configuration, application inventory, and performance metrics.","User credentials","Customer data","Financial records.","Migration Hub focuses on server configuration, application inventory, performance metrics, and other technical details."
"Which of the following AWS services can be integrated with AWS Migration Hub for migration tracking?","AWS Application Migration Service (MGN) and AWS Database Migration Service (DMS).","Amazon S3","Amazon CloudFront","AWS Lambda.","AWS Application Migration Service (MGN) and AWS Database Migration Service (DMS) integrate with Migration Hub to provide migration tracking."
"In the context of AWS Migration Hub, what does 'Cutover' refer to?","The final switch from on-premises to the AWS environment.","The initial discovery phase of the migration.","The cost estimation process.","The security auditing process.","Cutover is the final switch from the on-premises environment to the AWS environment, making the migrated application live."
"What is the main purpose of the AWS Application Migration Service (MGN) when used in conjunction with AWS Migration Hub?","To replicate servers to AWS in a highly automated way.","To monitor server performance on AWS.","To manage user access to AWS resources.","To create backups of on-premises data.","AWS Application Migration Service (MGN) is used to automatically replicate servers to AWS, making the lift-and-shift process much easier."
"Which of the following is a key consideration when using AWS Migration Hub for database migrations?","Ensuring compatibility between the source and target database engines.","Encrypting data in transit between servers.","Configuring network security groups.","Setting up load balancing.","Database migrations require careful consideration of compatibility between the source and target database engines to avoid data loss or application errors."
"How does AWS Migration Hub support hybrid migrations?","By tracking the status of workloads as they move between on-premises and AWS environments.","By providing a single sign-on solution for both environments.","By automatically synchronising data between on-premises and AWS.","By creating a virtual private network (VPN) connection.","Migration Hub tracks the migration status, providing transparency during hybrid migrations where some workloads may reside on-premises while others are in AWS."
"What is the primary benefit of using AWS Migration Hub for a large-scale migration project involving multiple teams?","It provides a unified view of progress and reduces coordination overhead.","It automatically assigns tasks to team members.","It eliminates the need for project managers.","It reduces AWS costs by optimising resource utilisation.","For large-scale projects, a single view is very important for project management purposes."
"How can you ensure that your migration projects are well-organised and easily tracked using AWS Migration Hub?","By creating and using 'Applications' to group related resources.","By manually updating a spreadsheet with migration progress.","By ignoring dependencies between servers.","By disabling logging to reduce storage costs.","Applications allow you to group related servers and track their migration as a single unit, improving organisation."
"When planning a migration using AWS Migration Hub, why is it important to accurately identify application dependencies?","To ensure that all required components are migrated together.","To minimise the number of servers being migrated.","To simplify security configurations.","To reduce the overall cost of the migration.","Accurately identifying dependencies ensures that all components are migrated together."
"What type of application workloads are best suited for migration using AWS Application Migration Service (MGN) and AWS Migration Hub?","Simple, stateless applications.","Complex, stateful applications.","Containerised applications.","Serverless applications.","AWS Application Migration Service (MGN) and AWS Migration Hub are well-suited for simple, stateless applications."
"Which AWS service can be used in conjunction with AWS Migration Hub to migrate databases?","AWS Database Migration Service (DMS)","AWS Storage Gateway","AWS Direct Connect","AWS Glue","AWS Database Migration Service (DMS) is specifically designed for migrating databases and integrates well with Migration Hub."
"In AWS Migration Hub, what does 'Home Region' signify?","The AWS region where the migration tracking data is stored.","The on-premises data centre location.","The region where the migrated applications will run.","The region with the lowest AWS costs.","The Home Region is the AWS region where Migration Hub stores and manages the migration tracking data."
"How does AWS Migration Hub contribute to reducing the overall migration time?","By providing a centralised view of migration status, helping to identify and resolve issues quickly.","By automatically optimising network bandwidth.","By eliminating the need for testing.","By bypassing security checks.","Migration Hub's centralised view helps to quickly identify and resolve issues."
"What role does AWS CloudTrail play when using AWS Migration Hub?","It logs API calls made to AWS Migration Hub.","It monitors server performance on AWS.","It manages user access to AWS resources.","It provides real-time cost estimates.","AWS CloudTrail logs API calls made to AWS Migration Hub, providing auditability and security monitoring."
"What is the key consideration when using AWS Migration Hub to manage a migration that involves both servers and databases?","Ensuring that server and database dependencies are properly mapped.","Encrypting all data in transit.","Choosing the correct instance types.","Optimising storage costs.","Ensuring that server and database dependencies are properly mapped helps."
"When using AWS Migration Hub, how can you determine which applications are ready to be migrated to AWS?","By reviewing the dependency mappings and assessment results.","By checking the server CPU utilisation.","By manually inspecting each application.","By running a security scan.","By reviewing the dependency mappings and assessment results helps you determine which applications are ready to be migrated."
"Which of the following is a common challenge addressed by AWS Migration Hub in a migration project?","Lack of visibility into migration progress.","Insufficient network bandwidth.","Incompatible operating systems.","Inadequate security measures.","Lack of visibility into migration progress is one of the challenges addressed by Migration Hub."
"How does AWS Migration Hub help in streamlining the migration process for enterprises?","By providing a single pane of glass for managing migrations across multiple tools.","By automatically resolving application compatibility issues.","By eliminating the need for migration planning.","By reducing AWS costs.","Migration Hub streamlines the process by consolidating the management across multiple tools into a single location."
"What is a primary advantage of using AWS Migration Hub's 'Strategy Recommendations' feature?","It helps identify the most suitable migration approach (e.g., rehost, replatform) for each application.","It automatically migrates applications to AWS.","It monitors the performance of migrated applications.","It manages the security of migrated applications.","Strategy Recommendations helps identify the best migration approach based on the application."
"In AWS Migration Hub, how are 'Assessments' used in the migration process?","To evaluate the readiness and suitability of applications for migration.","To automatically migrate data.","To define security policies for migrated resources.","To create a backup of on-premises data.","Assessments are used to determine the readiness and suitability of applications for migration."
"What is the purpose of the AWS Migration Hub Orchestrator?","Automating and simplifying complex migration workflows.","Monitoring application performance after migration.","Managing security configurations for migrated servers.","Reducing costs associated with data transfer during migration.","The Migration Hub Orchestrator is designed to automate and simplify complex migration workflows, making the process more efficient."
"What is the primary function of AWS Migration Hub?","To provide a central location to track the progress of application migrations to AWS.","To automatically migrate all applications to AWS without any configuration.","To provide cost estimates for running applications in AWS.","To manage AWS IAM roles and permissions.","Migration Hub offers a unified view of your migration projects, tracking progress and status across multiple tools."
"Which AWS service does Migration Hub integrate with to perform server migrations?","AWS Application Migration Service (MGN)","Amazon S3","AWS Lambda","Amazon EC2 Auto Scaling.","Migration Hub leverages services like MGN to facilitate the actual migration of servers and applications."
"What type of discovery can be performed within AWS Migration Hub to help with migration planning?","Agentless discovery using AWS Discovery Collector.","Forced discovery using root credentials.","Simulated discovery using synthetic data.","Rehydrated discovery using historical data.","Migration Hub can perform agentless discovery to identify servers, applications, and dependencies without installing agents on each server."
"In AWS Migration Hub, what does the term 'Application' refer to?","A logical grouping of resources being migrated together.","A single EC2 instance.","A specific AWS service, like S3 or RDS.","A database schema.","In Migration Hub, an 'Application' represents a collection of resources (servers, databases, etc.) that are being migrated as a unit."
"What is the benefit of using AWS Migration Hub's 'Migration Tracking' feature?","Provides a single pane of glass for monitoring migration progress.","Automatically optimises the performance of migrated applications.","Encrypts data during the migration process.","Automatically creates backups of migrated data.","Migration Tracking provides a centralised view to monitor the status and progress of your migration projects, improving visibility."
"What is the purpose of defining 'Migration Strategies' within AWS Migration Hub?","To recommend the best migration approach for each application.","To automatically generate CloudFormation templates.","To manage user access to migration tools.","To schedule migration windows.","Migration Strategies help you determine the optimal migration path for your applications, such as rehosting, replatforming, or refactoring."
"If you want to migrate a large number of virtual machines to AWS, which AWS Migration Hub integrated service is best suited for this task?","AWS Application Migration Service (MGN)","AWS Database Migration Service (DMS)","AWS Snowball Edge","AWS Storage Gateway","MGN is designed for efficiently migrating a large number of physical, virtual, or cloud servers to AWS."
"Which AWS service can be used in conjunction with AWS Migration Hub to migrate databases to AWS?","AWS Database Migration Service (DMS)","Amazon SQS","AWS Glue","Amazon Kinesis.","DMS is a database migration service that can be integrated with Migration Hub to migrate databases to AWS."
"What is a 'Migration Workflow' in the context of AWS Migration Hub?","A predefined sequence of steps for migrating a specific type of application.","A CloudWatch dashboard for monitoring migration metrics.","An IAM role with permissions to perform migrations.","A network configuration for connecting to on-premises environments.","A Migration Workflow is a structured set of tasks and steps defined for migrating a particular application or workload."
"What is the purpose of the 'AWS Discovery Collector' tool used with AWS Migration Hub?","To gather information about on-premises servers and applications.","To automatically migrate applications to AWS.","To monitor the performance of migrated applications.","To manage AWS IAM roles.","The AWS Discovery Collector is used to collect information about on-premises servers, their configurations, and dependencies."
"When using AWS Migration Hub, how are applications typically identified and grouped?","By assigning them a common Application ID.","By their IP address ranges.","By their operating system types.","By their network subnet.","Applications in Migration Hub are logically grouped and identified using an Application ID, making it easier to manage and track related resources."
"What information can you gather about your applications and infrastructure using AWS Migration Hub's discovery features?","Server specifications, installed software, and network dependencies.","User login history and password policies.","Detailed cost breakdown of on-premises infrastructure.","Compliance reports for regulatory requirements.","Migration Hub helps you discover server specs, installed software, and network dependencies."
"Which migration strategy involves moving an application to AWS without making significant changes to its architecture?","Rehosting (Lift and Shift)","Replatforming","Refactoring","Repurchasing.","Rehosting, also known as 'Lift and Shift,' involves moving an application to AWS without major architectural changes."
"What is a key benefit of using AWS Migration Hub for database migrations with AWS DMS?","Centralised tracking of the database migration progress.","Automated schema conversion.","Automatic scaling of the database instances.","Real-time replication of data.","Migration Hub provides a single place to track the progress of database migrations alongside other application components."
"How does AWS Migration Hub help with cost estimation for migrations?","By integrating with AWS Cost Explorer to forecast migration costs.","By automatically reducing AWS service costs during the migration.","By providing a fixed-price migration package.","By generating a bill of materials for on-premises hardware.","Migration Hub, through integration with other AWS services and analysis of discovered assets, aids in estimating migration costs."
"What is the role of the AWS Systems Manager Agent (SSM Agent) in the context of AWS Migration Hub?","To collect system information for discovery purposes.","To automatically install software on migrated servers.","To manage security patches on EC2 instances.","To configure network settings.","The SSM Agent, when installed on servers, facilitates the collection of system information necessary for discovery in Migration Hub."
"Which AWS service can be used to create a detailed migration plan based on the information gathered by AWS Migration Hub?","AWS Migration Evaluator","AWS CloudTrail","AWS Trusted Advisor","AWS Config.","AWS Migration Evaluator analyses discovered data to create a detailed migration plan."
"How does AWS Migration Hub support heterogeneous database migrations?","By integrating with AWS Schema Conversion Tool (SCT).","By automatically converting database code to a different programming language.","By providing a universal database driver.","By bypassing database compatibility issues.","Migration Hub works with SCT to aid heterogeneous database migrations by converting database schemas."
"What are some of the key metrics tracked by AWS Migration Hub during a server migration?","Migration status, data transfer progress, and cutover time.","CPU utilisation and memory usage.","Network latency and packet loss.","Disk I/O and storage capacity.","Migration Hub tracks migration status, data transfer, and cutover time."
"What type of reporting is available in AWS Migration Hub to track migration progress?","Customisable dashboards and reports.","Automated incident reports.","Daily vulnerability scans.","Real-time stock market data.","Migration Hub allows generating customisable dashboards and reports to monitor the migration."
"What is the relationship between AWS Migration Hub and AWS CloudEndure Migration?","CloudEndure Migration is now AWS Application Migration Service (MGN) and integrates with Migration Hub.","CloudEndure Migration replaces AWS Migration Hub.","CloudEndure Migration is used to migrate only databases, while Migration Hub handles everything else.","CloudEndure Migration is a separate product with no integration with Migration Hub.","AWS Application Migration Service (MGN), formerly CloudEndure Migration, is now the preferred migration tool and integrates with Migration Hub."
"How can AWS Migration Hub help reduce the risk of migration failures?","By providing visibility into the migration process and enabling proactive issue resolution.","By automatically rolling back migrations if errors occur.","By providing a 100% guarantee of successful migration.","By automatically encrypting all data during migration.","Migration Hub provides improved visibility."
"When using AWS Application Migration Service (MGN) with AWS Migration Hub, what is a 'replication server'?","A temporary EC2 instance that replicates data from the source server to AWS.","A server that stores application code.","A server that load balances traffic to migrated applications.","A server that monitors the health of migrated servers.","The replication server is a temporary instance created by MGN to replicate data."
"What is the function of AWS Migration Hub Orchestrator?","Automates and simplifies complex migration workflows.","Automatically upgrades operating systems on migrated servers.","Automatically manages AWS IAM roles during the migration.","Automatically generates documentation for migrated applications.","Orchestrator automates and simplifies the workflow."
"What level of access is required to use AWS Migration Hub?","IAM permissions to access and manage AWS resources.","Root account access.","No access required.","A dedicated VPN connection.","IAM permissions."
"What is the purpose of 'Migration Events' in AWS Migration Hub?","To provide real-time notifications about migration progress and issues.","To automatically trigger automated remediation actions.","To automatically encrypt data during migration.","To schedule downtime windows for migrations.","Migration Events are real-time."
"What is the main advantage of using AWS Migration Hub for tracking the progress of your migration efforts?","Centralised visibility across multiple migration tools and services.","Automated performance optimisation of migrated applications.","Automatic cost reduction for AWS services.","Real-time stock market data.","Centralised visibility."
"What kind of applications is AWS Migration Hub suitable for?","All types of applications, from simple to complex.","Only web applications.","Only database applications.","Only applications running on Linux.","All types of applications."
"When integrating AWS Migration Hub with AWS Application Discovery Service (ADS), what information does ADS typically provide?","Details about on-premises servers, their configurations, and dependencies.","User login history and password policies.","Detailed cost breakdown of on-premises infrastructure.","Compliance reports for regulatory requirements.","Server details, configuration and dependencies."
"How does AWS Migration Hub help improve collaboration among migration teams?","By providing a central platform for communication and coordination.","By automatically assigning tasks to team members.","By providing a real-time chat interface.","By generating automatic meeting minutes.","It provides a central platform."
"Which of the following is NOT a key component of the AWS Migration Hub workflow?","Performing detailed business analysis","Discovering your environment","Planning your migration","Tracking your progress","Performing business analysis is not a direct component of the Migration Hub."
"Which AWS service is commonly used with AWS Migration Hub to migrate file shares to AWS?","AWS Storage Gateway","Amazon SQS","AWS Glue","Amazon Kinesis","AWS Storage Gateway is commonly used to migrate file shares."
"How can AWS Migration Hub assist with identifying potential migration blockers?","By providing alerts and recommendations based on discovered data.","By automatically fixing any errors that are found.","By automatically rolling back migrations if errors occur.","By providing a 100% guarantee of successful migration.","By providing alerts and recommendations."
"What is the primary purpose of the AWS Migration Hub console?","To provide a centralised dashboard for managing and monitoring migrations.","To automatically migrate applications to AWS.","To manage AWS IAM roles.","To monitor AWS resource utilisation.","It provides a centralised dashboard."
"When planning a migration using AWS Migration Hub, what should you consider regarding application dependencies?","Document application dependencies to ensure they are migrated correctly.","Ignore application dependencies as they will be automatically resolved.","Migrate all applications in a single, large batch.","Use a standardised server naming convention","Dependencies need to be understood and documented."
"How can AWS Migration Hub help with tracking the cost of your migration project?","By integrating with AWS Cost Explorer to provide cost visibility.","By automatically reducing AWS service costs during the migration.","By providing a fixed-price migration package.","By generating a bill of materials for on-premises hardware.","Integration with cost explorer."
"What is the relationship between AWS Migration Hub and AWS Server Migration Service (SMS)?","AWS SMS is now deprecated and replaced by AWS Application Migration Service (MGN).","AWS SMS remains the primary migration tool while Migration Hub only provides tracking.","AWS SMS is used to migrate only databases, while Migration Hub handles everything else.","AWS SMS is a separate product with no integration with Migration Hub.","AWS SMS is deprecated."
"Which activity is not typically part of the Application Discovery process within AWS Migration Hub?","Collecting hardware inventory data","Performing automated code reviews","Identifying installed software","Mapping application dependencies","Code reviews are not part of app discovery."
"How does AWS Migration Hub address compliance requirements during a migration?","By providing tools for assessing and managing compliance risks.","By automatically encrypting all data during migration.","By ensuring that all data is stored in a specific geographic region.","By providing a 100% guarantee of compliance.","Migration Hub offers tools for assessing and managing risks."
"What is a 'Wave' in the context of AWS Migration Hub?","A group of applications or servers migrated together in a planned sequence.","A sudden spike in migration traffic.","A type of network protocol used for migration.","A unit of measurement for migration progress.","A wave is a group migrated together."
"What are some of the challenges that AWS Migration Hub helps address during a migration project?","Visibility, complexity, and risk.","Cost, performance, and security.","Availability, scalability, and reliability.","Storage, compute, and networking.","Migration Hub addresses visibility, complexity and risk."
"You are using AWS Migration Hub to track your ongoing migration. The status for one of your applications is showing as 'In Progress'. What does this typically indicate?","The application migration is currently underway and data is being transferred.","The application migration has completed successfully.","The application migration has failed.","The application migration is scheduled to begin.","Migration in underway."
"How does AWS Migration Hub work with services like AWS CloudTrail to provide security and auditing capabilities during a migration?","It leverages CloudTrail logs to track all migration-related activities and provide an audit trail.","It automatically encrypts all data during the migration.","It manages AWS IAM roles during the migration.","It monitors AWS resource utilization during the migration.","Leverages CloudTrail for security."
"What kind of integration does AWS Migration Hub offer with AWS Partner Network (APN) partners?","It provides a platform for partners to offer migration services and integrate their tools.","It automatically assigns tasks to partner team members.","It provides a real-time chat interface for partners.","It generates automatic meeting minutes for partner meetings.","Migration Services"
"Which AWS service is integrated with AWS Migration Hub to provide agentless discovery of on-premises resources?","AWS Discovery Collector","Amazon SQS","AWS Glue","Amazon Kinesis","AWS Discovery Collector for agentless discovery."
"Which best describes how AWS Migration Hub simplifies the overall cloud migration process?","It provides a central location to track migration progress, identify issues, and improve collaboration.","It automates all aspects of the migration process without any manual intervention.","It provides a 100% guarantee of a successful migration.","It automatically optimizes the performance of migrated applications.","Central Location."
"What happens to the data collected by AWS Discovery Collector after the discovery process?","The data is stored in AWS Migration Hub for migration planning and tracking.","The data is automatically deleted after the discovery process.","The data is stored in a local file on the discovery server.","The data is sent directly to the target AWS environment.","Data stored in AWS Migration Hub"
"Which statement accurately describes the cost associated with using AWS Migration Hub?","AWS Migration Hub is a free service; you only pay for the AWS services used during the migration.","AWS Migration Hub requires a monthly subscription fee based on the number of applications being migrated.","AWS Migration Hub charges a fee based on the amount of data transferred during the migration.","AWS Migration Hub requires a one-time setup fee for initial configuration.","Its free."
"What is the primary purpose of AWS Migration Hub?","To provide a central location to track the progress of application migrations to AWS.","To perform automated code refactoring for application migrations.","To automatically provision AWS resources for migrated applications.","To manage and optimise the costs of running applications on AWS.","AWS Migration Hub provides a central console to track the progress of application migrations, providing visibility across various migration tools."
"Which AWS service integrates with AWS Migration Hub to perform server migration?","AWS Application Migration Service (MGN)","AWS Database Migration Service (DMS)","AWS DataSync","AWS Snowball","AWS Application Migration Service (MGN) is specifically designed for server migration and integrates with Migration Hub to provide a central tracking and management point."
"In AWS Migration Hub, what is an 'application'?","A logical grouping of resources representing a business application being migrated.","A single virtual machine instance being migrated to AWS.","A specific database being migrated using DMS.","A collection of AWS accounts used for migration.","In Migration Hub, an 'application' represents a logical grouping of resources that make up a business application being migrated, allowing for consolidated tracking and reporting."
"What is the key benefit of using AWS Migration Hub's 'Discovery' feature?","It provides a centralised inventory of your on-premises servers and applications.","It automatically migrates databases to AWS.","It optimises the cost of your AWS infrastructure.","It automatically patches operating systems on your servers.","The Discovery feature in Migration Hub helps to build a comprehensive inventory of your on-premises environment, providing crucial information for planning your migration."
"What type of migration strategy can AWS Migration Hub assist with?","Supports various strategies like rehosting, replatforming, and refactoring.","Only supports rehosting (lift and shift) migrations.","Only supports replatforming migrations.","Only supports refactoring migrations.","Migration Hub is designed to work with a variety of migration strategies, providing a central view regardless of the chosen approach, including rehosting, replatforming and refactoring."
"Which of the following is NOT a function provided directly by AWS Migration Hub?","Automated code conversion.","Tracking the status of ongoing migrations.","Visualising migration progress.","Discovering servers in your on-premises environment.","Automated code conversion is not a function directly provided by AWS Migration Hub. It focuses on discovery, tracking, and visualisation of the migration process."
"What information does AWS Migration Hub use to track migration progress?","Migration Hub relies on status updates provided by migration tools.","It automatically detects changes in the on-premises environment.","It uses network traffic analysis to determine migration status.","It requires manual input of migration progress.","Migration Hub relies on updates from migration tools like MGN and DMS to track the progress of each migration task."
"How can AWS Migration Hub help in managing multiple migration waves?","By providing a centralised view of all migration projects and their status.","By automatically scheduling migration waves.","By automatically optimizing resource allocation across migration waves.","By providing automated testing for all migrated applications.","Migration Hub provides a centralized view of all migration projects and their statuses, allowing users to track migration progress across multiple waves."
"What role does AWS Migration Hub play in database migrations using AWS DMS?","It tracks the progress of DMS tasks and provides a consolidated view.","It automates the schema conversion process for DMS.","It automatically configures the DMS replication instance.","It optimizes the performance of the target database in DMS.","AWS Migration Hub integrates with AWS DMS to track the progress of database migration tasks, offering a unified dashboard for managing the entire migration process."
"How does AWS Migration Hub provide value after the initial migration phase?","It continues to provide a central view for managing migrated applications and troubleshooting issues.","It automatically decommissions on-premises servers.","It automatically optimizes the performance of migrated applications.","It automatically generates documentation for migrated applications.","Migration Hub provides a central view for managing migrated applications even after the initial migration phase, helping to troubleshoot and manage the applications."
"What is the primary function of AWS Migration Hub?","To provide a central location to track the progress of application migrations to AWS.","To automatically migrate databases to AWS.","To provide a cost estimation tool for AWS services.","To manage AWS Identity and Access Management (IAM) roles.","AWS Migration Hub serves as a single location to track the progress of application migrations, regardless of the tools used."
"Which AWS Migration Hub feature helps discover servers running in your on-premises environment?","AWS Discovery Collector","AWS Application Discovery Service Agentless Collector","AWS Migration Evaluator","AWS Database Migration Service (DMS)","The AWS Discovery Collector is a tool that you install in your on-premises environment to collect server inventory and utilisation data, sending it to the Application Discovery Service, which then integrates with Migration Hub."
"Within AWS Migration Hub, what does the term 'application' refer to?","A logical grouping of resources that make up a business application.","A single virtual machine instance running in AWS.","A database server instance.","A collection of AWS IAM users and roles.","In Migration Hub, an 'application' is a logical grouping of resources, such as servers, databases, and storage, that constitute a business application. This allows you to track the migration of all components of a specific application together."
"What is the purpose of defining an 'application' in AWS Migration Hub before starting a migration?","To group and track the progress of all resources related to a specific application.","To automatically optimise the application's architecture during migration.","To reduce the cost of the migration process.","To provide enhanced security for the application after migration.","Defining an application in Migration Hub lets you group related resources and monitor their migration status collectively, simplifying the tracking process."
"Which of the following AWS services is directly integrated with AWS Migration Hub to perform server migration?","AWS Application Migration Service (MGN)","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS Application Migration Service (MGN) is directly integrated with Migration Hub to facilitate the actual migration of servers from on-premises or other cloud environments to AWS. Migration Hub tracks the progress of migrations performed by MGN."
"You are using AWS Migration Hub to track your migration project. What type of information can you view?","Migration status, performance data, and estimated completion time.","Network bandwidth usage and latency.","Security compliance reports and audit logs.","Real-time stock market data.","Migration Hub provides information on migration status, performance data, and estimated completion time to help you monitor and manage your migration project effectively."
"Which AWS service can be used to collect data about your on-premises servers and import it into AWS Migration Hub, even without installing agents on the servers?","AWS Application Discovery Service (Agentless Collector)","AWS Systems Manager Inventory","AWS Config","Amazon Inspector","The AWS Application Discovery Service Agentless Collector is designed to discover servers in your on-premises environment without requiring the installation of agents on those servers. This data can then be imported into Migration Hub to support your migration efforts."
"Your company is migrating several applications to AWS. How can AWS Migration Hub help ensure consistent application naming across different migration tools?","By providing a centralised repository for application metadata.","By automatically renaming applications based on AWS best practices.","By enforcing strict naming conventions during the migration process.","By generating random names for each migrated application.","AWS Migration Hub offers a central location to store and manage application metadata, which helps ensure consistent application naming conventions are used across different migration tools and teams during the migration process."
"What kind of migration strategies can be tracked using AWS Migration Hub?","Rehost, Replatform, Repurchase, Refactor, and Retain.","Only lift-and-shift (rehost) migrations.","Only database migrations.","Only application refactoring projects.","AWS Migration Hub is capable of tracking various migration strategies, including Rehost, Replatform, Repurchase, Refactor, and Retain, providing a comprehensive view of your migration project regardless of the approaches used."
"How does AWS Migration Hub facilitate collaboration among different teams working on a migration project?","By providing a centralised dashboard with a unified view of the migration progress.","By automatically assigning tasks to team members based on their skills.","By replacing existing project management tools.","By generating automated documentation for the migrated applications.","AWS Migration Hub provides a centralised dashboard where all teams involved in the migration project can see a unified view of the migration progress, facilitating better collaboration and coordination."
"Which AWS service does AWS Migration Hub integrate with to track the progress of application migrations?","AWS Application Discovery Service","AWS IAM","Amazon CloudWatch","AWS CloudTrail","AWS Application Discovery Service integrates with Migration Hub to discover on-premises servers and applications, which are then tracked within Migration Hub during the migration process."
"What is the primary function of AWS Migration Hub?","To provide a central location to track the progress of application migrations to AWS.","To provide a database migration tool.","To provide serverless application deployment.","To provide a tool to manage AWS costs.","AWS Migration Hub provides a single location to track the progress of application migrations to AWS, regardless of the migration tools used."
"Which of the following migration strategies is supported by AWS Migration Hub for tracking progress?","Rehosting (Lift and Shift)","Database Migration Service (DMS) only","Serverless deployments only","Containerisation only","Migration Hub supports tracking progress for various migration strategies, including Rehosting, Replatforming, and Refactoring. It is not limited to only DMS migrations."
"In AWS Migration Hub, what does the term 'Application' refer to?","A collection of resources that deliver business functionality.","An individual EC2 instance.","A single S3 bucket.","A set of AWS IAM roles.","In Migration Hub, an 'Application' represents a collection of resources (e.g., servers, databases) that together deliver a specific business function. This allows for tracking the migration of the entire application as a unit."
"What type of information does AWS Migration Hub collect about your on-premises environment before a migration?","Inventory and utilisation data of servers and applications.","Network configuration settings only.","Security group configurations only.","User account details only.","Migration Hub integrates with AWS Discovery tools to collect detailed inventory and utilisation data of your on-premises servers and applications, which is essential for planning and executing a successful migration."
"What is the benefit of using AWS Migration Hub's 'Strategy Recommendations' feature?","It suggests optimal migration strategies for each application.","It automatically migrates your applications.","It automatically provisions AWS resources.","It provides cost estimations for cloud resources only.","Migration Hub's Strategy Recommendations analyses your application portfolio and recommends the most suitable migration strategy (e.g., rehost, replatform, refactor) for each application, saving time and effort in the planning phase."
"What is a key capability of AWS Migration Hub in terms of migration planning?","Providing a centralised view of your migration portfolio.","Automatically migrating databases.","Managing AWS billing.","Optimising EC2 instance sizes only.","Migration Hub provides a centralised view of your entire migration portfolio, allowing you to track progress, identify dependencies, and manage the overall migration process effectively."
"Which AWS service is commonly used with AWS Migration Hub to discover and assess on-premises servers and applications?","AWS Application Discovery Service (ADS)","AWS Config","AWS CloudWatch","AWS Systems Manager","AWS Application Discovery Service (ADS) is commonly used alongside Migration Hub to discover and assess your on-premises environment, providing valuable insights for migration planning."
"How does AWS Migration Hub help with tracking the costs associated with a migration?","It does not directly track migration costs.","It automatically optimises AWS resource costs.","It provides detailed cost breakdowns for each migrated application.","It predicts future AWS costs with machine learning.","AWS Migration Hub primarily focuses on tracking the progress of migrations and does not directly manage or report on the associated costs. However, it helps in planning the migration which in turn influences the costs."
"Which of the following is NOT a key feature of AWS Migration Hub?","Application Dependency Discovery","Migration Progress Tracking","Cost Optimisation Recommendations","Migration Strategy Recommendations","While Migration Hub provides application discovery, progress tracking, and migration strategy recommendations, it does not directly offer cost optimisation recommendations. Other AWS services are better suited for cost management."
"What is the primary function of AWS Migration Hub?","To provide a central location to track the progress of application migrations.","To automatically migrate applications to AWS without any configuration.","To provide detailed cost analysis of running applications on-premises.","To manage AWS Identity and Access Management (IAM) roles for migration projects.","AWS Migration Hub provides a central location to track the progress of application migrations into AWS, grouping applications and tracking their migration status."
"Which AWS service is integrated with AWS Migration Hub to perform agentless discovery of servers?","AWS Application Discovery Service (ADS)","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS Application Discovery Service is the service integrated with Migration Hub to perform agentless discovery of servers, providing details on server specification, utilisation and software."
"Within AWS Migration Hub, what is an 'application'?","A collection of resources being migrated together.","A single EC2 instance.","A specific AWS account.","A database schema.","In AWS Migration Hub, an 'application' represents a collection of resources, often representing a logical application, that are being migrated together."
"Which of the following is NOT a key capability offered by AWS Migration Hub?","Automated code refactoring.","Migration progress tracking.","Application discovery.","Integration with migration tools.","Automated code refactoring is not a capability offered directly by AWS Migration Hub. It provides tools and integrations to manage and track migrations but doesn't refactor code."
"What type of information does AWS Migration Hub primarily help you consolidate and track during a migration?","The status of each server being migrated.","The current monthly bill for your AWS services.","Security group configurations.","The names of the engineers working on the migration.","Migration Hub centralises and tracks the status of each server and application as it's migrated, providing visibility into the overall migration progress."
"What is a benefit of using AWS Migration Hub for application migrations?","Reduced migration complexity through centralised tracking.","Automatic cost optimisation of migrated resources.","Guaranteed 100% application uptime during migration.","Automatic generation of CloudFormation templates.","AWS Migration Hub helps reduce migration complexity by providing a central location to view the progress of your migration, regardless of the tools being used."
"How does AWS Migration Hub assist with migration governance?","By providing a single view of all migration activities.","By automatically approving all migration requests.","By enforcing strict naming conventions for migrated resources.","By automatically updating security policies for migrated applications.","AWS Migration Hub helps with migration governance by providing a single, unified view of all migration activities, allowing for better tracking and control."
"Which of the following services can be used to migrate databases using AWS Migration Hub?","AWS Database Migration Service (DMS)","AWS Storage Gateway","AWS Transfer Family","AWS Snowball","AWS Database Migration Service (DMS) is a service that migrates databases to AWS. Migration Hub integrates with DMS to track the progress of database migrations."
"If you're using AWS Migration Hub to track your migration, how would you typically update the status of a server as it moves through the migration process?","Via the migration tool you're using, which integrates with Migration Hub.","By manually updating a spreadsheet in Migration Hub.","By directly updating the server's metadata in AWS.","By submitting a support ticket to AWS.","The status is usually updated through the migration tool you're using, provided it's integrated with Migration Hub. This provides automatic progress updates."
"What is the purpose of the 'Home Region' setting in AWS Migration Hub?","To designate the region where migration tracking data is stored.","To specify the AWS region where all migrated resources will reside.","To define the geographic location of your on-premises data centre.","To configure the primary region for your AWS account.","The Home Region in AWS Migration Hub designates the AWS region where migration tracking data is stored and managed. This region serves as the central hub for your migration information."