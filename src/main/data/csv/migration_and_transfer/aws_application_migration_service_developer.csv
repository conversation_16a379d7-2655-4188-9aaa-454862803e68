"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Application Migration Service, what is the purpose of the Replication Settings?","To configure the instance type and EBS volumes for the converted instance","To configure the network settings for the converted instance","To configure the AMI ID to be used during conversion","To configure the IAM role used by the source server","Replication Settings are where you define the target instance type, EBS volume configuration and other replication settings during the conversion process."
"Which AWS service does AWS Application Migration Service primarily utilise for the actual migration process?","AWS Elastic Disaster Recovery","AWS Server Migration Service","AWS CloudEndure Migration","AWS DataSync","AWS Application Migration Service is the next generation of CloudEndure Migration, so it utilises the CloudEndure agent and infrastructure underneath."
"What is the purpose of the Test instance launched by AWS Application Migration Service?","To validate the migrated server before final cutover","To replicate data from the source server","To monitor network performance during replication","To configure security groups","The Test instance allows you to perform user acceptance testing (UAT) and validate the migrated server before final cutover."
"What is the function of the AWS Application Migration Service Agent?","To replicate data from the source server to the staging area","To manage IAM roles for the converted instance","To configure network settings on the target server","To automate the deployment of applications","The AWS Application Migration Service Agent is installed on the source server to replicate data to the staging area."
"What is the purpose of defining a 'Cutover window' in AWS Application Migration Service?","To schedule the final migration cutover","To define the replication start time","To set the acceptable downtime during migration","To specify the region for the migrated instance","The 'Cutover window' allows you to schedule the final migration cutover to minimise disruption."
"In AWS Application Migration Service, what does 'Launch into Target' signify?","Launching a converted instance in the target AWS account","Starting the initial replication process","Creating a snapshot of the source server","Stopping the replication process","'Launch into Target' initiates the creation of a converted instance in your target AWS account using the replicated data."
"What is the significance of the Staging Area subnet in AWS Application Migration Service?","It's where the replicated data is stored temporarily.","It's where the source server resides.","It's where the final migrated instance runs.","It contains the network configuration for the final migrated instance","The Staging Area subnet hosts the replication servers and is where the replicated data is temporarily stored before launching a test or cutover instance."
"Which AWS account needs the AWS Application Migration Service agent installed?","The source server's account","The target AWS account","The AWS account that manages the Application Migration Service console","The AWS support account","The AWS Application Migration Service agent needs to be installed on the source server that you are migrating."
"In AWS Application Migration Service, what is the purpose of the 'Readiness' status?","To indicate whether the source server is ready for conversion","To indicate the progress of the replication process","To indicate the health of the target instance","To indicate the completion of the cutover process","The 'Readiness' status provides an indication of whether the source server is successfully replicating data and is ready for a test or cutover."
"Which of the following is a prerequisite for using AWS Application Migration Service?","An AWS account with appropriate IAM permissions","A VPN connection between the source and target environments","A configured AWS Site-to-Site VPN","A dedicated network interface in the source environment","You need an AWS account and properly configured IAM roles with permissions to access both the source servers and the target AWS environment."
"In AWS Application Migration Service, what is the purpose of the 'Data replication settings'?","To configure the replication server instance type and EBS volumes","To configure the network bandwidth for replication","To configure the IAM role used by the agent","To configure the target region for migration","The 'Data replication settings' lets you define the replication server instance type, the EBS volume type (e.g., gp2, gp3, io1), and encryption settings."
"Which type of migration is AWS Application Migration Service best suited for?","Lift-and-shift migrations","Database migrations","Application refactoring","Serverless application deployment","AWS Application Migration Service is primarily designed for lift-and-shift migrations of servers and virtual machines from on-premises or other cloud environments to AWS."
"What is the role of the 'Replication Server' in AWS Application Migration Service?","To replicate data from the source server to EBS volumes in the staging area","To manage the conversion of the source server's operating system","To monitor the health of the source server","To manage network traffic during migration","The Replication Server acts as an intermediary, replicating data from the source server to EBS volumes in the staging area in your AWS account."
"In AWS Application Migration Service, what is the purpose of the 'Launch Settings'?","To configure the instance type and network settings for test and cutover instances","To configure the IAM role used by the Replication Server","To configure the data replication schedule","To configure the logging level for the migration process","The 'Launch Settings' define the instance type, network settings (subnet, security groups), and other parameters for the test and cutover instances that will be launched in the target AWS environment."
"What does the term 'Cutover' refer to in the context of AWS Application Migration Service?","The final migration event where the migrated server replaces the source server","The initial synchronisation of data between the source and target environments","The process of testing the migrated server","The process of installing the Application Migration Service agent","'Cutover' is the final event where you switch from the source server to the migrated instance in the target AWS environment."
"What is the benefit of using AWS Application Migration Service over manual migration methods?","Reduced downtime during migration","Automatic application refactoring","Simplified database migration","Automated scaling of migrated applications","AWS Application Migration Service offers reduced downtime during migration compared to manual methods because it uses continuous data replication."
"What is the purpose of the 'Staging Area' in AWS Application Migration Service?","A temporary storage location for replicated data before it is converted into a usable instance","A secure vault for storing migration credentials","A centralized dashboard for monitoring migration progress","A tool for refactoring applications during migration","The 'Staging Area' is a temporary location in your AWS account where replicated data is stored and managed before being used to launch test or cutover instances."
"Which operating systems are commonly supported by AWS Application Migration Service?","Windows and Linux","Only Linux","Only Windows","MacOS and Solaris","AWS Application Migration Service supports migration of both Windows and Linux operating systems."
"In AWS Application Migration Service, what does the 'Data Resync' option do?","Resynchronises the data between the source server and the staging area.","Restarts the Application Migration Service agent on the source server","Reconfigures the network settings on the target instance","Re-encrypts the data in the staging area","'Data Resync' forces a resynchronisation of the data between the source server and the staging area, ensuring that the target instance has the latest data."
"What is the key difference between AWS Application Migration Service and AWS Server Migration Service (SMS)?","AWS Application Migration Service offers continuous replication, while SMS uses periodic snapshots","AWS Application Migration Service only supports Linux, while SMS supports both Linux and Windows","AWS Application Migration Service is free, while SMS is a paid service","AWS Application Migration Service is used for database migrations, while SMS is used for server migrations","The main difference is that AWS Application Migration Service uses continuous replication, minimising downtime, while SMS uses periodic snapshots, which can result in longer downtime during migration."
"Which AWS service is required to set up the replication settings for AWS Application Migration Service?","AWS Elastic Compute Cloud (EC2)","AWS Simple Storage Service (S3)","AWS Identity and Access Management (IAM)","AWS Virtual Private Cloud (VPC)","The Replication Settings in AWS Application Migration Service require configurations for instance type, EBS volumes and network settings which are all part of the AWS EC2 service."
"What security benefit does AWS Application Migration Service provide during the migration process?","Encryption of data in transit and at rest","Automated security vulnerability scanning","Automated patching of the operating system","Compliance reporting for migrated servers","AWS Application Migration Service provides security by encrypting data both in transit and at rest during the replication process, ensuring data confidentiality."
"What is the first step in using AWS Application Migration Service to migrate a server?","Install the Application Migration Service agent on the source server","Create a new VPC in the target AWS account","Configure the Replication Settings","Create a new IAM role for the migration","The first step is to install the Application Migration Service agent on the source server so that it can begin replicating data."
"How does AWS Application Migration Service handle application dependencies during migration?","It migrates all dependencies along with the server","It automatically installs missing dependencies on the target server","It relies on manual configuration of dependencies on the target server","It ignores application dependencies","AWS Application Migration Service performs a lift-and-shift migration and will transfer all dependencies along with the server, however they may require manual configuration on the target server"
"What is the advantage of using the test launch feature in AWS Application Migration Service?","It allows for verification of the migrated server without impacting the source server","It reduces the overall migration time","It automatically configures the migrated server","It optimises the performance of the migrated application","The test launch feature allows you to verify that the migrated server is working as expected in the target environment without affecting the source server."
"Which AWS service is typically used to manage and monitor the progress of migrations with AWS Application Migration Service?","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS CloudWatch is typically used to monitor the progress of migrations using metrics and logs provided by AWS Application Migration Service."
"What is the maximum amount of time you can set for the data retention policy when using AWS Application Migration Service?","There is no limit","7 days","30 days","90 days","AWS Application Migration Service provides configuration options, but ultimately relies on CloudEndure's engine and therefore has no limit on data retention policy."
"How does AWS Application Migration Service minimize downtime during the final cutover?","By continuously replicating data until the cutover","By using parallel processing for data transfer","By automatically optimising the target instance","By using a dedicated network connection for migration","AWS Application Migration Service minimizes downtime by continuously replicating data up until the cutover, ensuring that only minimal changes need to be synchronised during the final cutover event."
"When using AWS Application Migration Service, what action do you take after completing testing on the test instance?","Perform the Cutover process","Delete the source server","Deactivate the Application Migration Service agent","Optimise the EBS volume performance","After testing is complete and you're satisfied with the results, you proceed with the 'Cutover' process to migrate the server to the production environment."
"What is the main advantage of using AWS Application Migration Service for migrating servers to AWS?","Reduced migration costs","Automated application refactoring","Improved security posture","Simplified management of the migration process","AWS Application Migration Service simplifies the management of the migration process by providing a centralised console for managing, tracking, and automating server migrations."
"What is the default encryption method used by AWS Application Migration Service?","AES-256","RSA-2048","Triple DES","MD5","AWS Application Migration Service uses AES-256 encryption by default for data in transit and at rest, ensuring data confidentiality and security."
"What is the purpose of the 'Post-launch actions' in AWS Application Migration Service?","To automate tasks after the migrated instance is launched","To schedule migration windows","To configure network settings","To test the migrated instance","'Post-launch actions' allow you to automate tasks that need to be performed after the migrated instance is launched, such as installing software, configuring the operating system, or joining the instance to a domain."
"Which of the following is a key consideration when planning a migration using AWS Application Migration Service?","Network bandwidth between the source and target environments","The location of the staging area","The type of operating system","The number of applications on the server","Network bandwidth between the source and target environments is a crucial factor as it affects the speed and reliability of data replication."
"How does AWS Application Migration Service handle licensing requirements for migrated software?","It automatically transfers the licenses","It requires manual license management","It provides free licenses for migrated software","It disables licensed software on the migrated server","AWS Application Migration Service does not automatically transfer licenses, and you are responsible for managing the licensing requirements for any software running on the migrated server."
"What is the purpose of the 'Source Server Settings' in AWS Application Migration Service?","To configure the details of the server being migrated","To define the target instance type","To configure the replication schedule","To manage user permissions","The 'Source Server Settings' are used to specify the details of the source server that you are migrating, such as its IP address, operating system, and other relevant information."
"What role does IAM play in AWS Application Migration Service?","To control access to the Application Migration Service console and resources","To manage network settings","To configure the replication schedule","To monitor migration progress","IAM is used to control access to the Application Migration Service console and resources, ensuring that only authorized users can perform migration tasks."
"What is the advantage of using the continuous data replication feature in AWS Application Migration Service?","Minimised data loss during cutover","Automated application refactoring","Simplified database migration","Improved security posture","Continuous data replication minimizes data loss during cutover by ensuring that the target environment is always up-to-date with the latest changes from the source environment."
"What type of storage does AWS Application Migration Service use for the Staging Area?","EBS volumes","S3 buckets","Glacier storage","Instance store","AWS Application Migration Service uses EBS volumes for the Staging Area, providing persistent storage for replicated data."
"What is the purpose of the 'Launch Template' option in AWS Application Migration Service?","To customise the launch configuration of the migrated instance","To manage network security groups","To configure IAM roles","To schedule migration windows","The 'Launch Template' option allows you to customize the launch configuration of the migrated instance, including the instance type, network settings, and other parameters."
"Which AWS region is AWS Application Migration Service available in?","Most AWS regions","Only US East (N. Virginia)","Only EU West (Ireland)","Only Asia Pacific (Tokyo)","AWS Application Migration Service is available in most AWS regions, providing flexibility in choosing the target region for your migration."
"In AWS Application Migration Service, what happens to the source server after the cutover?","It remains running until you manually shut it down","It is automatically shut down","It is automatically disconnected from the network","It is automatically upgraded","After the cutover, the source server remains running until you manually shut it down."
"What is the purpose of defining a 'Target instance type' in AWS Application Migration Service?","To specify the EC2 instance type for the migrated server","To configure network security groups","To define the operating system for the migrated server","To manage user permissions","The 'Target instance type' specifies the EC2 instance type that will be used for the migrated server in the target AWS environment."
"How does AWS Application Migration Service handle migrating databases running on the source server?","It doesn't migrate databases","It migrates the database along with the operating system","It can only migrate applications","Databases should be migrated seperately","Databases require a seperate migration strategy."
"What is a key benefit of using AWS Application Migration Service for compliance?","Centralised migration management","Automated application refactoring","Database migration support","Integrated compliance checks","Centralised migration management allows for control and audibility."
"What is the maximum size of each volume supported by AWS Application Migration Service?","16 TiB","1 TiB","4 TiB","32 TiB","AWS Application Migration Service replicates and migrates EBS volumes up to 16 TiB."
"You want to automate the testing and cutover process in AWS Application Migration Service. Which AWS service can you integrate with?","AWS Step Functions","AWS Lambda","AWS Glue","AWS X-Ray","You can use AWS Step Functions to orchestrate and automate the testing and cutover process in AWS Application Migration Service."
"How does AWS Application Migration Service ensure data consistency during replication?","By using incremental replication","By performing full replication every time","By optimising the instance for speed","By verifying the data","AWS Application Migration Service ensures data consistency through incremental replication. "
"How does AWS Application Migration Service handle dynamic IP addresses on the source server?","By requiring static IP addresses","By automatically updating the IP address on the target instance","By requiring network administrators to manually do it","By disabling network access","If the source server has a dynamic IP, AWS Application Migration Service tracks and automatically updates the IP address on the target instance."
"Which of the following factors significantly influence the duration of the initial sync phase in AWS Application Migration Service?","Network bandwidth and data change rate","CPU utilisation","Application complexity","The server location","The initial sync duration is most significantly influenced by the available network bandwidth and how quickly the data on the source server is changing (data change rate)."
"What is the primary purpose of AWS Application Migration Service (MGN)?","To migrate on-premises servers to AWS.","To manage application deployments on EC2.","To monitor application performance on AWS.","To automate infrastructure provisioning on AWS.","AWS Application Migration Service is designed to simplify and accelerate the migration of on-premises servers and virtual machines to AWS."
"In AWS Application Migration Service (MGN), what is a Replication Server responsible for?","Transferring data from the source server to AWS.","Managing user authentication.","Orchestrating the entire migration process.","Providing a user interface for migration.","The Replication Server is responsible for continuously transferring data from the source server to AWS, ensuring data is kept up-to-date."
"When using AWS Application Migration Service (MGN), what type of agent needs to be installed on the source server?","AWS Replication Agent","AWS CloudWatch Agent","AWS Systems Manager Agent","AWS Config Agent","The AWS Replication Agent is required on the source server to replicate data to AWS."
"With AWS Application Migration Service (MGN), what is the purpose of a test instance?","To validate the migrated server's functionality before cutover.","To perform load testing on the migrated environment.","To monitor the health of the replication process.","To store the replicated data.","Test instances are launched to verify the migrated server's functionality and ensure everything works as expected before the final cutover."
"Which AWS service does Application Migration Service (MGN) primarily integrate with to launch migrated instances?","Amazon EC2","AWS Lambda","Amazon ECS","Amazon S3","Application Migration Service uses Amazon EC2 to launch the migrated instances."
"What is the recommended initial cutover type in AWS Application Migration Service (MGN) to minimise risk?","Test cutover","Permanent cutover","Delta cutover","Failback cutover","A test cutover allows you to validate the migrated instance before committing to a permanent migration."
"What type of workloads are best suited for AWS Application Migration Service (MGN)?","Server-based workloads","Container-based workloads","Serverless workloads","Static website workloads","MGN is designed for migrating server-based workloads from on-premises environments to AWS."
"In AWS Application Migration Service (MGN), what does the term 'cutover' refer to?","The final switch from the source server to the migrated instance in AWS.","The initial replication of data from the source server to AWS.","The process of installing the AWS Replication Agent.","The testing phase of the migrated instance.","Cutover signifies the final switch from the on-premises server to the migrated instance running in AWS."
"Which of the following is a key benefit of using AWS Application Migration Service (MGN)?","Reduced migration downtime","Automated code refactoring","Simplified database migration","Improved server hardware utilisation","Application Migration Service minimizes downtime during the migration process."
"What is the initial Sync in AWS Application Migration Service (MGN) process?","The initial replication of the entire source server disk volume to AWS.","The synchronization of user accounts and permissions.","The verification of network connectivity between the source server and AWS.","The testing of the migrated application.","The Initial Sync refers to the replication of the entire disk volume from the source server to AWS."
"What is the recommended approach when migrating databases using AWS Application Migration Service (MGN)?","Use a dedicated database migration tool such as AWS DMS","Replicate the database server and its data using MGN","Migrate the application and database together using MGN","Migrate the database manually after the application migration","For databases, it is recommended to use AWS Database Migration Service (DMS) rather than relying on MGN replication."
"When using AWS Application Migration Service (MGN), what is the purpose of defining launch templates?","To configure the EC2 instance settings for the migrated instances.","To automate the installation of software on the source server.","To specify the network configuration for the replication servers.","To manage user access to the MGN console.","Launch templates define the instance type, security groups, and other settings for the EC2 instances created during cutover."
"Which AWS IAM permission is essential for a user to manage and perform migrations using AWS Application Migration Service (MGN)?","mgn:ManageReplicationConfiguration","ec2:DescribeInstances","s3:GetObject","cloudwatch:GetMetricData","The `mgn:ManageReplicationConfiguration` permission is required to manage replication settings and initiate migrations."
"How does AWS Application Migration Service (MGN) handle incremental data replication?","It replicates only the changed blocks of data.","It replicates the entire disk volume on each interval.","It uses snapshots to replicate data.","It relies on application-level replication.","MGN continuously replicates only the changed blocks of data to minimize bandwidth usage and replication time."
"You are using AWS Application Migration Service (MGN) to migrate a server with sensitive data. How can you ensure the data is encrypted during replication?","Configure encryption at rest using KMS keys.","Configure encryption at the application level.","Disable replication to avoid data transfer.","Encrypt the data manually after the migration.","MGN supports encryption at rest using KMS keys, ensuring that data is encrypted both during replication and in storage."
"What is the purpose of the 'Readiness' status in AWS Application Migration Service (MGN)?","To indicate that the server is ready for a test or cutover.","To indicate that the replication agent is installed correctly.","To indicate that the initial data synchronization is complete.","To indicate that the server is powered on.","The 'Readiness' status confirms that the server is ready for a test or cutover operation."
"Which AWS region is NOT a supported region for using Application Migration Service (MGN)?","AWS China (Beijing)","US East (N. Virginia)","EU (Ireland)","Asia Pacific (Tokyo)","AWS China (Beijing) is not currently a supported region for Application Migration Service (MGN)."
"What is the maximum disk size supported for replication on a single server when using AWS Application Migration Service (MGN)?","8 TiB","2 TiB","16 TiB","4 TiB","AWS Application Migration Service (MGN) supports disks up to 8 TiB per server."
"What is the name of the group of settings related to networking, security groups, and launch templates in AWS Application Migration Service (MGN)?","Launch settings","Replication settings","Network settings","Cutover settings","Launch settings control the networking, security groups, and launch templates."
"Which of the following actions is irreversible when migrating a server using AWS Application Migration Service (MGN)?","The permanent cutover","The test cutover","The agent installation","The initial sync","The permanent cutover is irreversible, as it commits the server to being migrated."
"With AWS Application Migration Service (MGN), how can you customize the EC2 instance type for a migrated server?","By modifying the launch template settings","By changing the replication settings","By updating the source server's configuration","By adjusting the migration settings","The EC2 instance type is customized through the launch template settings."
"What is the benefit of using the agentless discovery feature in AWS Application Migration Service (MGN)?","It discovers servers without installing agents.","It automates the installation of replication agents.","It improves replication performance.","It simplifies network configuration.","The agentless discovery feature allows for the discovery of servers without requiring agent installation."
"What is the maximum number of servers that can be migrated simultaneously using AWS Application Migration Service (MGN)?","There is no practical limit","50 servers","100 servers","200 servers","There is no practical limit on the number of servers that can be migrated simultaneously."
"What does the term 'Staging Area' refer to in the context of AWS Application Migration Service (MGN)?","The AWS account where the replicated data is stored","The on-premises data center where the source servers reside","The network configuration used for replication","The monitoring dashboard in the MGN console","The staging area refers to the AWS account where the replicated data is stored during the migration process."
"Which AWS service can be used to automate the deployment of applications after migrating servers with AWS Application Migration Service (MGN)?","AWS CodeDeploy","AWS Systems Manager","AWS CloudFormation","AWS Lambda","AWS CodeDeploy is often used to automate application deployment after servers have been migrated."
"What is the purpose of the EC2 Image Builder in the context of AWS Application Migration Service (MGN)?","To customize the base image for the migrated servers","To automate the creation of replication servers","To manage user access to the MGN console","To monitor the health of the replication process","EC2 Image Builder can be used to customize the base image for migrated servers."
"When using AWS Application Migration Service (MGN), how do you handle licensing requirements for migrated software?","Manage licenses according to the software vendor's terms","Transfer existing licenses automatically","Bypass licensing requirements during migration","Use a default AWS license","Licensing requirements must be managed according to the software vendor's terms."
"You want to migrate a large number of servers with minimal disruption. What is a best practice to achieve this using AWS Application Migration Service (MGN)?","Use phased migration approach","Migrate all servers at once during a maintenance window","Rely on manual migration processes","Ignore testing to speed up migration","A phased migration approach minimizes disruption by migrating servers in batches."
"Which of the following is NOT a supported operating system for source servers when using AWS Application Migration Service (MGN)?","macOS","Windows Server","Red Hat Enterprise Linux","Ubuntu Linux","macOS is not a supported operating system for source servers when using AWS Application Migration Service (MGN)."
"When using AWS Application Migration Service (MGN), how can you monitor the progress of the replication process?","Using the AWS MGN console dashboard","Using AWS CloudTrail logs","Using Amazon S3 event notifications","Using AWS Config rules","The AWS MGN console dashboard provides a visual overview of the replication process."
"What is the main advantage of using agent-based migration over other migration methods with AWS Application Migration Service (MGN)?","Provides continuous data replication","Eliminates the need for networking configuration","Allows for offline migration","Requires no AWS account","Agent-based migration allows for continuous data replication, ensuring minimal downtime."
"With AWS Application Migration Service (MGN), what is the purpose of configuring tags?","To organize and manage migrated resources.","To automate the installation of replication agents.","To simplify network configuration.","To manage user access to the MGN console.","Tags help organize and manage migrated resources in AWS."
"What is the name for the resource in AWS MGN representing the server being replicated?","Source Server","Replication Instance","Migration Host","Target Instance","The Source Server represents the server being replicated from on-premises to AWS."
"In AWS Application Migration Service (MGN), what happens to the source server after a successful permanent cutover?","The source server remains active until you decommission it","The source server is automatically shut down","The source server is deleted","The source server continues to replicate","The source server remains active until you choose to decommission it."
"Which AWS service can be used to create alerts based on the replication status of servers in AWS Application Migration Service (MGN)?","Amazon CloudWatch","AWS CloudTrail","Amazon SNS","AWS Config","Amazon CloudWatch can be used to create alerts based on the replication status of servers."
"When migrating servers using AWS Application Migration Service (MGN), how do you ensure that the migrated instances have the correct time zone?","Configure the time zone in the launch template","Configure the time zone on the replication server","Set the time zone on the source server before migration","Automatically uses the AWS Region's time zone","Configure the time zone in the launch template to ensure migrated instances have the correct time zone."
"What is the purpose of the 'Data Routing and Throttling' feature in AWS Application Migration Service (MGN)?","To control the bandwidth used during replication","To route data through a specific network interface","To prioritize certain types of data during replication","To encrypt data in transit","The 'Data Routing and Throttling' feature allows you to control the bandwidth used during replication."
"Which of the following components is not required to successfully use AWS Application Migration Service (MGN)?","A VPN connection between on-premises and AWS","An AWS account","The AWS Replication Agent","An IAM role with appropriate permissions","A VPN connection between on-premises and AWS may be desired but is not strictly required for MGN."
"In AWS Application Migration Service (MGN), what is the purpose of the 'Security Group' setting in the Launch Template?","To control network traffic to the migrated instance","To encrypt data at rest","To manage user access to the instance","To monitor the instance's performance","The Security Group setting in the Launch Template controls the network traffic to the migrated instance."
"What is the effect of increasing the 'Staging Disk Type' on AWS Application Migration Service (MGN)?","It can improve replication performance.","It reduces storage costs.","It increases network bandwidth.","It reduces the number of servers that can be migrated.","Increasing the Staging Disk Type, for example to SSD from HDD can improve replication performance."
"When using AWS Application Migration Service (MGN), how can you automate the installation of applications on the migrated instances?","By integrating with AWS Systems Manager","By using the MGN console directly","By manually installing the applications after cutover","By using AWS CloudFormation","Integrating with AWS Systems Manager allows you to automate the installation of applications on migrated instances."
"You need to migrate a server that requires a specific hostname after migration. How can you ensure this using AWS Application Migration Service (MGN)?","Configure the hostname in the launch template.","Configure the hostname on the replication server.","Configure the hostname on the source server.","Change the hostname manually after cutover.","You can configure the hostname in the launch template to ensure the migrated server has the desired hostname."
"What is the key difference between AWS SMS and AWS MGN?","SMS migrates VMs as AMIs while MGN uses continuous replication.","SMS is agentless while MGN requires an agent.","SMS is free while MGN is paid.","SMS supports more operating systems than MGN.","SMS (Server Migration Service) migrates virtual machines as AMIs, while MGN (Application Migration Service) uses continuous replication for a faster and less disruptive migration."
"With AWS Application Migration Service (MGN), what is the purpose of the 'Licensing' tab?","To configure your AWS licence for the migrated server.","To check if your licence is transferrable.","To acknowledge that you are responsible for your own licence during migration.","To automatically migrate your licences.","The Licensing tab provides info about the best practise of acknowledging that you are responsible for your own licence during migration."
"What is the relationship between AWS CloudEndure Migration and AWS Application Migration Service (MGN)?","MGN is the next generation of CloudEndure Migration.","CloudEndure Migration is the next generation of MGN.","They are separate products with different purposes.","They are the same product under different names.","AWS Application Migration Service (MGN) is the next generation of CloudEndure Migration, offering enhanced features and integration with AWS services."
"Can AWS Application Migration Service (MGN) be used to migrate workloads from AWS to on-premises environments?","No, it is only for migrating to AWS","Yes, with the same ease as migrating to AWS.","Yes, with some additional configuration steps.","Only with third-party tools.","Application Migration Service (MGN) is designed for migrating workloads to AWS, not from AWS to on-premises environments."
"When using AWS Application Migration Service (MGN), how do you ensure data consistency during the cutover process?","By performing a final sync before cutover.","By using application-level replication.","By relying on database replication features.","By stopping all processes on the source server before cutover.","A final sync before cutover minimizes data loss and ensures data consistency."
"When would you choose Application Migration Service (MGN) over CloudEndure Disaster Recovery (CDR)?","When you need a cost effective migration solution","When you need continuous data protection","When you need near zero downtime Disaster Recovery","When you require point in time restore capabilities","MGN is the service of choice when you need a cost effective migration solution."
"When using AWS Application Migration Service (MGN), what are the main factors affecting the time it takes to perform the initial data synchronisation?","Available network bandwidth and size of data to be replicated","CPU utilisation on the source server and Instance type","Disk performance of staging area and network latency","IAM role permissions and availability zone","The initial data synchronisation time is significantly influenced by the available network bandwidth and the total size of data that needs to be replicated from the source server to AWS."
"What is the primary function of AWS Application Migration Service (MGN)?","Migrating on-premises servers and virtual machines to AWS","Managing AWS IAM roles","Monitoring application performance","Deploying containerized applications","AWS Application Migration Service is designed to simplify and accelerate the migration of on-premises servers and virtual machines to AWS."
"Which replication setting in AWS Application Migration Service controls the amount of data replicated at any one time?","Throttle","Bandwidth Limitation","Transfer Window","Replication Rate","The Throttle setting in AWS Application Migration Service allows you to control the amount of data replicated at any given time, effectively limiting bandwidth usage."
"When using AWS Application Migration Service (MGN), what type of agent is installed on the source server?","Replication Agent","Monitoring Agent","Security Agent","Container Agent","A replication agent is installed on the source server to facilitate the continuous replication of data to AWS."
"In AWS Application Migration Service (MGN), what is the purpose of a cutover?","To switch the source server from on-premises to the AWS Cloud","To perform a test migration","To install the replication agent","To configure replication settings","A cutover is the final step in the migration process, where the source server is switched from on-premises to running as an EC2 instance in AWS."
"What is the Recovery Instance in AWS Application Migration Service (MGN) used for?","To test the migrated server before cutover","To store the replicated data","To manage the replication process","To configure security settings","The recovery instance is created from the replicated data and used to test the migrated server's functionality before the final cutover."
"Which AWS service is tightly integrated with Application Migration Service (MGN) to provide compute resources for migrated servers?","Amazon EC2","Amazon S3","Amazon RDS","Amazon Lambda","Application Migration Service relies on Amazon EC2 to provide the compute resources for the migrated servers once they are cutover to AWS."
"What is the main benefit of using the continuous data replication feature in AWS Application Migration Service (MGN)?","Minimising downtime during migration","Simplifying security configurations","Reducing the cost of storage","Improving application performance","Continuous data replication helps to minimise downtime during the migration process by keeping the AWS environment up-to-date with changes on the source server."
"In AWS Application Migration Service (MGN), what is the purpose of the Launch Template?","To define the EC2 instance configuration for the migrated server","To configure network settings","To schedule cutover windows","To manage replication agents","The launch template defines the EC2 instance configuration, including instance type, AMI, and security groups, for the migrated server when it is launched in AWS."
"How does AWS Application Migration Service (MGN) help with testing migrations?","By allowing you to launch test instances of your migrated servers","By automatically generating test scripts","By providing performance testing tools","By simulating network conditions","AWS Application Migration Service allows you to launch test instances of your migrated servers to verify their functionality before the final cutover."
"What is the significance of 'Right-Sizing' when migrating with AWS Application Migration Service (MGN)?","Optimizing the compute resources allocated to the migrated server","Ensuring data security during migration","Simplifying the replication process","Automating application deployment","'Right-Sizing' refers to the practice of optimizing the compute resources allocated to the migrated server to ensure efficient performance and cost-effectiveness."
"What is the role of the AWS Replication Server in the Application Migration Service (MGN) architecture?","Receiving and processing the replicated data from source servers","Managing IAM roles","Monitoring Application performance","Scheduling Server cutovers","The AWS Replication Server receives and processes the replicated data from the source servers, preparing it for launching the recovery instances."
"In AWS Application Migration Service (MGN), what is the function of the Staging Area?","To temporarily store replicated data before it's converted to EC2 instances","To manage user access","To store final AWS instances","To monitor network performance","The Staging Area provides temporary storage for replicated data before it is converted into EC2 instances."
"When using AWS Application Migration Service (MGN), what is the primary benefit of the non-disruptive testing feature?","Minimizing impact on production environments during testing","Automating the migration process","Reducing the cost of migration","Simplifying security configurations","The non-disruptive testing feature allows you to test the migrated servers without impacting the production environment on the source servers."
"Which pricing model applies to the use of the AWS Application Migration Service (MGN) service itself?","MGN is generally free to use, you only pay for the AWS resources consumed during the migration process.","Pay-as-you-go based on the number of migrated servers","Subscription based on the number of users","Upfront payment for a fixed number of migrations","AWS Application Migration Service (MGN) is generally free to use, and you only pay for the AWS resources consumed during the migration process, such as EC2 instances and storage."
"What action should you take in Application Migration Service (MGN) after verifying that the test instance is working as expected?","Proceed with the cutover to finalise the migration","Delete the test instance","Update the replication settings","Restart the replication agent","After verifying that the test instance is working as expected, you should proceed with the cutover to finalise the migration."
"What is the best practice for dealing with databases when migrating with AWS Application Migration Service (MGN)?","Migrate databases separately using a database migration tool such as AWS DMS","Include the database server as part of the server migration","Replicate the database data directly to the application servers","Ignore the database during the migration process","It is generally best practice to migrate databases separately using a dedicated database migration tool like AWS DMS to ensure data integrity and minimise downtime."
"What role does the AWS Systems Manager Agent (SSM Agent) play in the context of AWS Application Migration Service (MGN)?","The SSM Agent can be installed on the migrated instances to enable management and automation tasks","The SSM Agent is used to replicate data from source servers","The SSM Agent is used to configure MGN replication settings","The SSM Agent is used to perform the server cutovers","The AWS Systems Manager Agent (SSM Agent) can be installed on the migrated instances to enable management and automation tasks after the migration is complete."
"What is the purpose of setting a 'Target Instance Type' in the AWS Application Migration Service (MGN) launch template?","To specify the EC2 instance type to be used for the migrated server","To configure the security groups for the migrated server","To schedule the server cutover","To monitor application performance","The 'Target Instance Type' in the launch template specifies the EC2 instance type that will be used for the migrated server when it is launched in AWS."
"Which of the following network configurations must be in place to successfully use AWS Application Migration Service (MGN)?","A secure connection between the source server and the AWS Replication Server","A public IP address assigned to each source server","A VPN connection to the AWS management console","Direct Connect connection between AWS and all source servers","A secure connection between the source server and the AWS Replication Server is essential for data replication."
"What is the benefit of using AWS CloudEndure Migration (predecessor to MGN) or MGN over a 'lift and shift' approach using traditional VM import/export?","Reduced downtime and simplified migration process","Lower migration costs","Improved security during migration","Automated application testing","AWS MGN offers reduced downtime due to continuous replication and simplifies the migration process compared to traditional VM import/export."
"When using AWS Application Migration Service (MGN), what is the recommended approach for migrating applications with multiple servers?","Migrate the servers in waves, starting with the least critical applications","Migrate all servers simultaneously","Migrate the most critical server first","Migrate the database server last","Migrate the servers in waves, starting with the least critical applications, is a common and recommended approach for managing complex migrations."
"What is the significance of the 'Data Routing and Throttling' settings in AWS Application Migration Service (MGN)?","Controlling bandwidth usage and minimising impact on source server performance","Encrypting data during replication","Automatically scheduling server cutovers","Configuring security groups for the migrated instances","The 'Data Routing and Throttling' settings are crucial for controlling bandwidth usage and minimising the impact on source server performance during replication."
"How does AWS Application Migration Service (MGN) handle application licensing during migration?","It does not handle application licensing. You are responsible for ensuring licenses are properly migrated","It automatically transfers licenses from the source server","It provides temporary licenses for the migrated instances","It generates new licenses for the migrated instances","AWS Application Migration Service (MGN) does not handle application licensing. You are responsible for ensuring that licenses are properly migrated and compliant with the licensing terms."
"Which of the following is a key requirement for source servers to be compatible with AWS Application Migration Service (MGN)?","The server must have a supported operating system.","The server must have a public IP address.","The server must be virtualized.","The server must be running in a VMware environment.","The source server must have a supported operating system to be compatible with AWS Application Migration Service (MGN)."
"What is the purpose of the 'Cutover Readiness' check in AWS Application Migration Service (MGN)?","To verify that all dependencies are met before initiating the cutover","To test the performance of the migrated instance","To configure the security groups","To schedule a cutover window","The 'Cutover Readiness' check verifies that all dependencies are met and the instance is ready for cutover."
"You are experiencing slow data replication with AWS Application Migration Service (MGN). What should you check first?","Network bandwidth and throttling settings","Firewall configuration","IAM roles","EC2 instance size","Network bandwidth and throttling settings are the most common causes of slow data replication."
"How does AWS Application Migration Service (MGN) assist with migrating servers with different operating systems?","It supports migration of servers with a wide range of operating systems by using the replication agent.","It automatically converts the operating system to a standard Linux distribution.","It requires all servers to have the same operating system.","It migrates only Windows servers","AWS Application Migration Service (MGN) supports the migration of servers with a wide range of operating systems by using the replication agent. It does not require all servers to have the same operating system or automatically convert operating systems."
"What is the role of IAM permissions when using AWS Application Migration Service (MGN)?","To grant MGN access to the source servers and AWS resources","To configure EC2 instance sizes","To monitor application performance","To schedule the cutover","IAM permissions are necessary to grant AWS Application Migration Service (MGN) the necessary access to the source servers and AWS resources during the migration process."
"What is the purpose of the 'Post-Launch Actions' feature in AWS Application Migration Service (MGN)?","To automate tasks after the migrated instance is launched","To configure security groups before launch","To monitor replication progress","To schedule a cutover","'Post-Launch Actions' allows you to automate tasks, such as installing software or running scripts, after the migrated instance is launched in AWS."
"What is the recommended first step when troubleshooting a failed cutover in AWS Application Migration Service (MGN)?","Review the MGN event logs and CloudWatch logs","Increase the replication bandwidth","Reinstall the replication agent","Contact AWS support","The recommended first step is to review the MGN event logs and CloudWatch logs to identify the cause of the failure."
"What is a key advantage of using AWS Application Migration Service (MGN) for application modernisation?","It provides a flexible and automated migration path to AWS, enabling further modernisation efforts.","It automatically containerises applications during migration","It replaces legacy code with modern programming languages","It optimizes application performance","AWS Application Migration Service (MGN) provides a flexible and automated migration path to AWS, which can then facilitate application modernisation efforts, such as refactoring or containerisation."
"How does AWS Application Migration Service (MGN) help ensure data integrity during migration?","By using continuous data replication and checksum verification","By automatically encrypting data at rest","By performing regular backups","By using a dedicated network connection","Continuous data replication with checksum verification ensures that data is transferred accurately and completely."
"What type of applications are typically good candidates for migration using AWS Application Migration Service (MGN)?","Applications that are difficult to refactor or re-platform","Applications that are already cloud-native","Applications that require significant code changes","Applications that have no dependencies","Applications that are difficult to refactor or re-platform are often good candidates for lift-and-shift migrations using MGN."
"What is the purpose of the 'Conversion Server' in the AWS Application Migration Service (MGN) architecture?","To convert the replicated data into a format suitable for EC2 instances","To manage user access","To monitor server performance","To schedule cutovers","The Conversion Server converts the replicated data into a format compatible with EC2 instances, preparing it for launch."
"When using AWS Application Migration Service (MGN), how can you minimize costs associated with storage during the replication process?","By optimizing data transfer settings and using appropriate storage tiers","By using smaller EC2 instance types","By reducing the number of replication agents","By disabling data encryption","Optimising data transfer settings and using appropriate storage tiers for replicated data can help minimize storage costs during the replication process."
"What is a key benefit of using AWS Application Migration Service (MGN) in conjunction with AWS Cloud Adoption Framework (CAF)?","Aligning the migration strategy with business objectives and best practices","Automating security configurations","Simplifying application testing","Reducing the number of migrated servers","Using AWS Application Migration Service (MGN) in conjunction with AWS Cloud Adoption Framework (CAF) ensures alignment with business objectives and best practices for a successful cloud migration."
"How does AWS Application Migration Service (MGN) help with disaster recovery (DR)?","By replicating on-premises servers to AWS for quick recovery in case of a disaster","By automating backups of EC2 instances","By providing real-time monitoring of application performance","By simplifying security configurations","AWS Application Migration Service (MGN) can be used for disaster recovery by replicating on-premises servers to AWS, enabling quick recovery in case of a disaster."
"What is the relationship between AWS Application Migration Service (MGN) and AWS Server Migration Service (SMS)?","MGN is the recommended service for migrating servers to AWS, while SMS is being phased out.","MGN and SMS are interchangeable services with the same features.","SMS is used for migrating virtual machines, while MGN is used for migrating physical servers.","SMS is a more advanced version of MGN with additional features.","MGN is the recommended service for migrating servers to AWS, and AWS Server Migration Service (SMS) is being phased out."
"What is the purpose of 'Replication Settings' in AWS Application Migration Service (MGN)?","To define the data replication parameters, such as bandwidth throttling and security settings","To configure the EC2 instance type for the migrated server","To schedule the server cutover","To monitor application performance","'Replication Settings' in AWS Application Migration Service (MGN) are used to define the data replication parameters, such as bandwidth throttling and security settings."
"When using AWS Application Migration Service (MGN), how can you ensure that sensitive data is protected during replication?","By enabling data encryption in transit and at rest","By using smaller EC2 instance types","By reducing the number of replication agents","By disabling data compression","Enabling data encryption in transit and at rest helps to protect sensitive data during the replication process."
"What is the relationship between AWS Application Migration Service (MGN) and AWS Elastic Disaster Recovery?","Both services use similar technology to provide continuous data replication, but MGN focuses on migration while DRS focuses on disaster recovery.","Both services offer the same functionalities and are interchangeable.","MGN is a more advanced version of DRS with additional features.","MGN focuses on disaster recovery while DRS focuses on migration.","Both services use similar technology to provide continuous data replication, but MGN focuses on migration while DRS focuses on disaster recovery."
"You need to migrate servers to AWS with minimal downtime using AWS Application Migration Service (MGN). What should you prioritize?","Implementing continuous data replication and thorough testing","Using smaller EC2 instance types","Disabling data encryption","Reducing the number of replicated servers","Prioritising continuous data replication and thorough testing ensures minimal downtime during the migration process."
"What are some of the limitations of using AWS Application Migration Service (MGN)?","MGN has limitations on the supported operating systems and database types.","MGN can only migrate servers to a single AWS region.","MGN does not support automated testing.","MGN cannot migrate servers with dependencies on other services.","MGN has limitations on the supported operating systems and database types, so it is important to verify compatibility before starting the migration process."
"What is the purpose of a 'Licence Manager Override' in AWS Application Migration Service (MGN) in the post launch template?","To specify licence details for the OS and third party software post-migration","To enforce existing software licensing policies","To perform licence key rotation","To schedule automated licence audits","Licence Manager Override allows you to specify licence details for the OS and third party software post-migration."
"Which of the following resources is required to setup Application Migration Service (MGN)?","A configured Replication Server","An existing AWS CloudTrail","A VPC with access to the internet or a VPC Endpoint for Application Migration Service","A private Hosted Zone on Route53","A VPC with access to the internet or a VPC Endpoint for Application Migration Service is a hard requirement."
"When you cutover from a physical server to the MGN environment, what change will need to be performed on the original, physical server?","Shut down the physical server to prevent IP address conflicts","Reinstall the operating system","Remove all applications","Defragment the hard drive","Shut down the physical server to prevent IP address conflicts in the network."
"The servers you want to migrate using MGN are in a VPC with no Internet access, how can you migrate them?","Create a VPC Endpoint for Application Migration Service","Configure a NAT Gateway","Use AWS DataSync","Use AWS Transfer Family","A VPC Endpoint for Application Migration Service is needed to allow MGN traffic to reach the service without going over the Internet."
"In AWS Application Migration Service (MGN), what does the term 'Agent' refer to?","Software installed on the source server to replicate data.","A tool for automating application deployments.","A security service for protecting migrated servers.","A service for monitoring application performance.","The Agent is installed on the source server to replicate data to AWS."
"What is the primary purpose of AWS Application Migration Service (MGN)?","Migrating on-premises servers to AWS.","Load balancing web applications across multiple AWS regions.","Managing container deployments on AWS.","Monitoring the health of EC2 instances.","AWS MGN is specifically designed for migrating servers from on-premises or other cloud environments to AWS."
"During an AWS Application Migration Service (MGN) migration, what does the 'Cutover' process involve?","Switching the source server to run on AWS.","Creating a backup of the source server.","Deleting the source server.","Optimising the performance of the migrated server.","The cutover process switches the source server to run on AWS, effectively completing the migration."
"Which AWS service does Application Migration Service (MGN) primarily integrate with to provide its migration capabilities?","AWS Elastic Disaster Recovery (DRS)","AWS CloudTrail","AWS Config","AWS Systems Manager","MGN's capabilities overlap significantly with DRS, but MGN is the current, preferred service for server migrations."
"In AWS Application Migration Service (MGN), what is a 'Replication Server'?","A temporary EC2 instance used to replicate data.","A server that hosts the MGN console.","A server used for application testing.","A server dedicated to database replication.","Replication servers are temporary EC2 instances that handle the replication of data from the source server to AWS."
"What is a key benefit of using AWS Application Migration Service (MGN) for server migration?","Minimising downtime during migration.","Automatically refactoring application code.","Providing built-in security auditing.","Simplifying database schema conversions.","MGN minimises downtime by continuously replicating data in the background."
"When using AWS Application Migration Service (MGN), what type of replication is typically employed?","Continuous, block-level replication","Snapshot-based replication","File-based replication","Scheduled replication","MGN uses continuous, block-level replication to ensure minimal data loss during migration."
"Which of the following tasks does AWS Application Migration Service (MGN) *not* automate?","Application code refactoring","Server replication","EC2 instance creation","Automated conversion","MGN automates server replication and EC2 instance creation but does not refactor application code."
"What AWS IAM permission is essential for a user to manage migrations using AWS Application Migration Service (MGN)?","MGN Service Role","EC2 Full Access","S3 Read Only Access","CloudWatch Full Access","The MGN Service Role is required to give permissions to the service."
"What is the role of the 'Launch Template' in AWS Application Migration Service (MGN)?","Defines the EC2 instance configuration for migrated servers.","Specifies the network settings for the migrated servers.","Configures the application deployment process.","Manages the replication settings for the source server.","The Launch Template defines the configuration of the EC2 instance that will be created for the migrated server, including instance type, security groups, and IAM role."
"What is the purpose of 'Test Mode' in AWS Application Migration Service (MGN)?","To validate the migrated server configuration before cutover.","To simulate network latency during migration.","To test the performance of the source server.","To perform a security audit of the source environment.","Test Mode allows you to validate the migrated server in a non-production environment before performing the final cutover."
"Which of the following is a typical prerequisite before using AWS Application Migration Service (MGN)?","Installing the MGN agent on the source server.","Configuring a VPN connection to AWS.","Setting up a dedicated migration network.","Creating a detailed application dependency map.","Installing the MGN agent on the source server is a mandatory step to enable data replication."
"If the AWS Application Migration Service (MGN) Agent fails to install on a source server, what is a common troubleshooting step?","Verifying network connectivity to AWS.","Checking the source server's CPU utilisation.","Restarting the source server.","Disabling the source server's firewall.","Verifying network connectivity to AWS is a key troubleshooting step."
"In AWS Application Migration Service (MGN), what happens to the source server after a successful cutover?","The source server remains running until manually decommissioned.","The source server is automatically shut down.","The source server is automatically converted to an AMI.","The source server is automatically backed up.","After a successful cutover, the source server typically remains running until it is manually decommissioned, allowing for a rollback if needed."
"Which of the following is *not* a supported operating system for source servers when using AWS Application Migration Service (MGN)?","Mainframe z/OS","Windows Server","Linux","CentOS","Mainframe is not a supported OS by Application Migration Service"
"What type of data replication is used for database migration when using AWS Application Migration Service (MGN)?","Block-level","File-level","Snapshot-based","Application-level","Application Migration Service uses block-level data replication to minimize data loss."
"You are migrating servers using AWS Application Migration Service (MGN). How can you monitor the progress of the replication process?","Using the MGN console","Using AWS CloudTrail","Using AWS Config","Using AWS Trusted Advisor","The MGN console provides real-time information on the replication progress of each server."
"What is the best practice regarding security groups during migration using AWS Application Migration Service (MGN)?","Use security groups that mirror the source environment.","Use security groups that allow all traffic.","Use security groups that block all traffic.","Use a single security group for all migrated servers.","Mirroring the security groups of the source environment ensures that existing security policies are maintained."
"What happens to the source server's IP address after the cutover to AWS using AWS Application Migration Service (MGN)?","The IP address can be retained on the EC2 instance.","The IP address is automatically released.","The IP address is assigned to a new server.","The IP address is reserved for future use.","The IP address can be retained on the EC2 instance during cutover."
"What is the role of the 'Conversion Server' within AWS Application Migration Service (MGN)?","To convert the replicated data into an EC2 instance.","To manage the replication process.","To monitor the health of the source servers.","To perform security scanning.","The conversion server transforms the replicated data into an EC2 instance format."
"You need to migrate a large number of servers to AWS using Application Migration Service (MGN). What is the recommended approach?","Use the MGN console to migrate servers in batches.","Use the AWS CLI or API to automate the migration process.","Migrate all servers simultaneously.","Migrate servers one at a time.","Automating the migration process via the AWS CLI or API is more efficient for large-scale migrations."
"Which AWS service can be used to orchestrate and automate post-migration tasks after the cutover with AWS Application Migration Service (MGN)?","AWS Systems Manager","AWS CloudFormation","AWS CloudTrail","AWS Config","AWS Systems Manager can be used to automate post-migration tasks."
"What is the maximum number of servers that can be migrated concurrently using AWS Application Migration Service (MGN)?","There is no limit","100","500","1000","There is effectively no limit, the architecture of the product has been designed to handle large numbers of concurrent migrations"
"What is the best approach to address application compatibility issues during a migration using AWS Application Migration Service (MGN)?","Conduct thorough testing in the test environment before cutover.","Ignore compatibility issues until after cutover.","Automatically refactor the application code.","Replace the application with a cloud-native alternative.","Thorough testing in the test environment allows you to identify and address application compatibility issues before the final cutover."
"How does AWS Application Migration Service (MGN) handle data encryption during the replication process?","Data is encrypted in transit and at rest.","Data is encrypted only in transit.","Data is encrypted only at rest.","Data encryption is not supported.","Data is encrypted both in transit and at rest for enhanced security."
"What happens if the connection between the source server and AWS is interrupted during replication using AWS Application Migration Service (MGN)?","Replication resumes automatically when the connection is restored.","Replication must be restarted manually.","The migration process is automatically cancelled.","The source server is automatically shut down.","Replication is designed to resume automatically, ensuring data consistency."
"You are migrating a server with a large amount of data using AWS Application Migration Service (MGN). How can you optimise the replication process?","Increase the bandwidth of the network connection.","Reduce the size of the data being replicated.","Increase the CPU of the replication server.","Disable data encryption.","Increasing the bandwidth of the network connection will speed up the replication process."
"What is the primary difference between AWS Application Migration Service (MGN) and AWS Server Migration Service (SMS)?","MGN supports continuous replication, while SMS uses periodic snapshots.","MGN supports only Windows servers, while SMS supports both Windows and Linux.","MGN is a free service, while SMS is a paid service.","MGN requires manual configuration, while SMS is fully automated.","MGN uses continuous replication, whereas SMS uses snapshots"
"What is the advantage of using AWS Application Migration Service (MGN) compared to manually migrating servers?","Reduced downtime and automated processes.","Lower cost.","Increased security.","Simplified network configuration.","MGN minimises downtime with it's continuous replication"
"How can you minimise the cost of using AWS Application Migration Service (MGN)?","Optimising the EC2 instance type for the replication server.","Reducing the replication frequency.","Disabling data encryption.","Using a different migration tool.","Optimising the EC2 instance type for the replication server can help reduce costs."
"Which AWS service can be used to create a hybrid cloud environment in conjunction with AWS Application Migration Service (MGN)?","AWS Direct Connect","AWS CloudFront","AWS IAM","AWS Lambda","AWS Direct Connect provides a dedicated network connection."
"What is the purpose of the 'Staging Area' in AWS Application Migration Service (MGN)?","A temporary storage location for replicated data.","A virtual network for testing migrated servers.","A region for deploying migrated applications.","A security zone for migrated servers.","The staging area is a temporary location for the replicated data before it is converted into an EC2 instance."
"What is the recommended method for migrating databases using AWS Application Migration Service (MGN)?","Use AWS Database Migration Service (DMS).","Use the MGN agent to replicate the database files.","Manually export and import the database.","Use AWS Storage Gateway to replicate the database.","Databases are best migrated using AWS Database Migration Service (DMS)"
"What is the role of the 'Source Server Settings' in AWS Application Migration Service (MGN)?","To configure the replication settings for the source server.","To configure the network settings for the source server.","To configure the security settings for the source server.","To configure the application settings for the source server.","These settings allow you to control how the service replicates data from the source server."
"How can you ensure that the migrated servers in AWS are properly sized after migration using AWS Application Migration Service (MGN)?","Monitor performance metrics and adjust instance sizes accordingly.","Use the same instance sizes as the source servers.","Use the smallest possible instance sizes.","Use the largest possible instance sizes.","Monitoring performance metrics after migration allows you to optimise instance sizing for cost and performance."
"What is the impact of using AWS Application Migration Service (MGN) on the source server's performance during migration?","Minimal impact due to continuous replication.","Significant performance degradation.","The source server is temporarily unavailable.","The source server is automatically optimised.","MGN is designed to have minimal impact."
"What is the purpose of the 'Post-Launch Actions' in AWS Application Migration Service (MGN)?","To automate tasks after the cutover is complete.","To configure the replication settings.","To configure the network settings.","To configure the security settings.","Post-Launch Actions allow you to automate tasks such as installing software or configuring settings on the migrated server."
"How does AWS Application Migration Service (MGN) support compliance requirements during migration?","By ensuring data is encrypted in transit and at rest.","By automatically generating compliance reports.","By providing built-in security auditing.","By automating compliance checks.","MGN encrypts data both in transit and at rest."
"What is the recommended approach to handle licensing issues when migrating servers using AWS Application Migration Service (MGN)?","Use AWS License Manager to manage licenses.","Ignore licensing issues until after cutover.","Automatically transfer licenses to AWS.","Use a different migration tool.","AWS License Manager helps you manage and track software licenses in AWS and on-premises environments."
"How can you validate that the data has been migrated successfully using AWS Application Migration Service (MGN)?","Perform thorough testing in the test environment.","Compare the file sizes on the source and target servers.","Compare the number of files on the source and target servers.","Use a data checksum tool.","Testing in the test environment is the most comprehensive way to ensure data integrity."
"What is the role of 'AWS Marketplace' in the context of using AWS Application Migration Service (MGN)?","To find and deploy pre-configured AMIs for migrated servers.","To purchase licenses for the MGN agent.","To find and deploy pre-configured network settings for migrated servers.","To find and deploy pre-configured security settings for migrated servers.","AWS Marketplace offers a wide range of AMIs that can be used as a base for migrated servers, providing pre-configured software and operating systems."
"What is the difference between 'Agentless' and 'Agent-based' migration solutions, and how does AWS Application Migration Service (MGN) fit into these categories?","MGN is Agent-based, requiring software installation on the source server for replication.","MGN is Agentless, using hypervisor-level APIs for replication.","MGN supports both Agentless and Agent-based migrations.","MGN uses a combination of both Agentless and Agent-based methods.","MGN is Agent-based, requiring software installation on the source server."
"You are migrating servers using AWS Application Migration Service (MGN), and the network bandwidth between the source and destination is limited. What strategy can you implement to optimise replication?","Prioritise migrating the most critical servers first.","Increase the number of replication servers.","Reduce the replication frequency.","Disable data encryption.","By prioritising critical servers, you can ensure that the most important applications are migrated quickly and efficiently, even with limited bandwidth."
"When using AWS Application Migration Service (MGN), you encounter issues with application dependencies during the cutover process. What is the recommended approach to resolve this?","Thoroughly document application dependencies and create a detailed migration plan.","Ignore application dependencies until after cutover.","Automatically resolve application dependencies during cutover.","Use a different migration tool.","A detailed migration plan which takes into account all application dependencies will help the cutover process happen smoothly."
"How can you ensure that the migrated servers are protected by the same security policies as the source servers using AWS Application Migration Service (MGN)?","Replicate security groups and network configurations from the source environment.","Disable security policies during migration.","Use a single security policy for all migrated servers.","Rely on AWS default security policies.","Ensuring your security groups are the same will make the transfer to the migrated service easier."
"You want to automate the entire migration process using AWS Application Migration Service (MGN), including server replication, testing, and cutover. Which AWS service or tool can help achieve this?","AWS CloudFormation","AWS Systems Manager","AWS CodePipeline","AWS Lambda","You can use AWS Cloudformation to automate an entire migration"
"After migrating servers to AWS using Application Migration Service (MGN), you need to decommission the source servers in a secure and compliant manner. What are some best practices to follow?","Erase data, destroy hardware, and document the decommissioning process.","Leave the source servers running indefinitely.","Transfer the source servers to another department.","Sell the source servers without wiping the data.","Erasing data, destroying hardware, and documenting the decommissioning process are critical steps."
"You are migrating servers using AWS Application Migration Service (MGN) and need to maintain the same Active Directory integration on the migrated servers. How can you achieve this?","Join the migrated servers to the existing Active Directory domain.","Create a new Active Directory domain in AWS.","Migrate the Active Directory domain to AWS.","Disable Active Directory integration on the migrated servers.","To achieve the desired outcome, simply join your new EC2 resources (migrated servers) into the existing Active Directory Domain."
"What is the primary benefit of using 'right-sizing' during a migration project with Application Migration Service (MGN)?","Matching cloud resources to actual workload needs for cost optimisation.","Increasing the diskspace to the maximum available.","Doubling the compute power for safety.","Using the same configuration from the source environment.","Using right-sizing helps you match your cloud environment to your current needs for cost optimisation and future growth."
"What does the term 'rehosting' refer to in the context of AWS Application Migration Service (MGN)?","Migrating an application without making significant changes to its architecture.","Refactoring an application to use cloud-native services.","Repurchasing an application from a new vendor.","Replacing an application with a cloud-native alternative.","Rehosting also referred to as lift-and-shift, is a migration strategy of moving an application with no architectural changes."
"What is the primary purpose of AWS Application Migration Service (MGN)?","To rehost on-premises servers to AWS.","To containerise on-premises applications.","To refactor on-premises databases.","To modernise on-premises applications using serverless technologies.","AWS Application Migration Service (MGN) is designed to simplify and accelerate the process of rehosting on-premises servers to AWS."
"In AWS Application Migration Service (MGN), what is the purpose of the 'Staging Area'?","A temporary location in AWS where replicated data is stored before final cutover.","A section of the AWS console for managing migrations.","A development environment for testing migrated applications.","A persistent storage location for migrated data after cutover.","The Staging Area in MGN is a temporary location in AWS where replicated data is stored before the final cutover to ensure minimal downtime."
"Which replication setting in AWS Application Migration Service (MGN) determines how frequently data is synchronised from the source server?","Data replication frequency.","Bandwidth throttling.","Cutover schedule.","Agent version.","The 'Data replication frequency' setting determines how often the data is synchronised from the source server to the staging area in AWS."
"Which of the following is a prerequisite for using AWS Application Migration Service (MGN)?","An AWS IAM role with necessary permissions.","An on-premises vCenter server.","A public IP address assigned to each source server.","A dedicated VPN connection to AWS.","Using AWS Application Migration Service requires an AWS IAM role with the necessary permissions to allow MGN to access and manage resources in your AWS account."
"What is the purpose of the 'Test cutover' option in AWS Application Migration Service (MGN)?","To validate the migration process before the final cutover.","To automatically rollback the migration if issues are found.","To perform a cost analysis of the migration.","To deploy the application to a production environment immediately.","The 'Test cutover' option allows you to validate the migration process and test the migrated server in AWS before performing the final cutover to minimise downtime and potential issues."
"How does AWS Application Migration Service (MGN) minimise downtime during migration?","By performing continuous data replication until the cutover.","By automatically rewriting application code.","By using serverless functions for migration.","By migrating applications to containers before cutover.","AWS Application Migration Service (MGN) minimises downtime by continuously replicating data in the background until the final cutover, ensuring minimal disruption to the application."
"What type of agent is required on the source servers when using AWS Application Migration Service (MGN)?","Replication Agent.","CloudWatch Agent.","Systems Manager Agent.","CodeDeploy Agent.","A Replication Agent is required on the source servers to facilitate the data replication to the staging area."
"What happens to the data replicated to the staging area after the cutover in AWS Application Migration Service (MGN)?","It is used to create an AMI that is launched as an EC2 instance.","It is automatically deleted.","It is archived for long-term storage.","It remains in the staging area indefinitely.","After cutover, the data in the staging area is used to create an AMI, which is then launched as an EC2 instance."
"When using AWS Application Migration Service (MGN), what is a Launch Template used for?","To configure the EC2 instance settings after the cutover.","To define the replication settings.","To schedule the cutover window.","To monitor the migration progress.","Launch Templates are used to configure the settings of the EC2 instance that will be launched after the cutover, such as instance type, security groups, and networking."
"What is the function of the 'licensing' setting in AWS Application Migration Service (MGN) after the cutover of a server?","It allows you to apply existing server licences to the migrated instance.","It is used to manage the cost optimisation of migrated servers.","It provides automated patch management.","It is used to configure network security rules.","The licensing setting in MGN allows you to apply existing server licences to the migrated instance on AWS, helping ensure licensing compliance."
"What is the primary purpose of AWS Application Migration Service (MGN)?","Rehosting servers to AWS","Monitoring application performance","Managing AWS infrastructure costs","Creating serverless applications","AWS Application Migration Service (MGN) is designed to simplify and expedite the process of rehosting on-premises or cloud-based servers to AWS."
"Which replication setting in AWS Application Migration Service (MGN) helps minimise the impact on the source server's performance during initial replication?","Throttling the replication bandwidth","Disabling change tracking","Encrypting the replicated data","Setting up scheduled replication","Throttling the replication bandwidth will reduce the amount of resources MGN uses on the source server."
"In AWS Application Migration Service (MGN), what is the purpose of a launch template?","To define the EC2 instance configuration for the migrated server","To specify the replication settings","To manage user permissions for migration","To monitor migration progress","A launch template defines the EC2 instance type, security groups, networking, and other configurations for the migrated server when it is launched in AWS."
"What type of agent must be installed on the source server when using AWS Application Migration Service (MGN)?","Replication agent","Monitoring agent","Security agent","Inventory agent","The Replication Agent captures block-level data from the source server's disks and replicates it to AWS."
"What is the function of the 'Cutover' process in AWS Application Migration Service (MGN)?","To switch the migrated server from replication to running in AWS","To initiate the initial data replication","To test the replication process","To delete the source server","The Cutover process involves shutting down the source server and launching the replicated instance in AWS, making the migrated server live."
"What is a key benefit of using AWS Application Migration Service (MGN) over a manual migration process?","Automated server conversion and minimal downtime","Manual server conversion and zero downtime","Increased server cost and zero downtime","Manual server conversion and minimal downtime","MGN automates the conversion of servers and helps minimize downtime during the migration process."
"When using AWS Application Migration Service (MGN), what type of data replication is used?","Block-level replication","File-level replication","Object-level replication","Database replication","AWS Application Migration Service (MGN) utilises block-level replication to efficiently copy data from the source server to AWS."
"Which AWS service does AWS Application Migration Service (MGN) use to launch the migrated servers in AWS?","Amazon EC2","AWS Lambda","Amazon ECS","AWS S3","AWS Application Migration Service (MGN) launches the migrated servers as EC2 instances."
"How does AWS Application Migration Service (MGN) handle updating the replicated data after the initial synchronisation?","Continuous data replication","Periodic snapshot replication","Manual data synchronization","Scheduled data backups","MGN uses continuous data replication to ensure that changes made on the source server are replicated to AWS in near real-time."
"With AWS Application Migration Service (MGN), what does the term 'Target instance' refer to?","The EC2 instance created in AWS after migration","The source server being migrated","The replication server used for staging","The MGN agent installed on the source server","The target instance is the EC2 instance created in AWS where the migrated server will run."
"When using AWS Application Migration Service (MGN), what is the primary purpose of the 'Launch Settings' configuration for a migrated server?","To define the EC2 instance type, security groups, and other launch-related parameters.","To configure the initial sync settings for data replication.","To specify the target AWS Region for the migration.","To set up monitoring and logging for the migrated server.","The 'Launch Settings' in MGN allows you to configure the details of the EC2 instance that will be launched as part of the migration process, including instance type and security groups."
"Which of the following best describes the role of the 'Replication Agent' in AWS Application Migration Service (MGN)?","It replicates data from the source server to the staging area in AWS.","It configures the security groups for the migrated server.","It manages the launch settings for the target EC2 instance.","It monitors the health of the migrated application.","The Replication Agent's main function is to replicate data from the source server to the staging area in AWS, ensuring data synchronisation during the migration process."
"During an AWS Application Migration Service (MGN) migration, what is the purpose of the 'Cutover' process?","To switch over from the source server to the migrated server in AWS.","To configure the initial data replication settings.","To test the migrated server in a non-production environment.","To create a backup of the source server before migration.","The 'Cutover' process is the final step where the migrated server in AWS takes over from the original source server, effectively completing the migration."
"With AWS Application Migration Service (MGN), where is the replicated data initially stored during the migration process?","In a staging area within your AWS account.","Directly in the target EC2 instance.","In an Amazon S3 bucket.","In Amazon Glacier.","MGN uses a staging area within your AWS account to store the replicated data before the cutover process. This allows for testing and validation before the final migration."
"When using AWS Application Migration Service (MGN), what is the recommended first step before initiating a large-scale migration?","Perform a test cutover to validate the migration process.","Configure all the launch settings for every server.","Disable the source servers' firewalls.","Increase the bandwidth of your internet connection.","Performing a test cutover helps identify potential issues and validate the migration process before migrating all servers, reducing risks and ensuring a smoother transition."
"What AWS service does Application Migration Service (MGN) utilise for its data replication process?","AWS Replication Agent","AWS DataSync","AWS Storage Gateway","AWS Transfer Family","AWS Application Migration Service (MGN) utilises the AWS Replication Agent for its data replication process. This agent is installed on the source server and replicates data to the AWS environment."
"What type of migration is AWS Application Migration Service (MGN) best suited for?","Rehosting (Lift and Shift)","Refactoring","Replatforming","Repurchasing","Application Migration Service (MGN) is primarily designed for rehosting (lift and shift) migrations, where applications are moved to AWS with minimal changes."
"If you need to migrate a large number of servers using AWS Application Migration Service (MGN), what feature can help automate and orchestrate the migration process?","Migration Wave","Server Group","Replication Setting","Agent Auto-Update","Migration Waves allow you to group servers for migration, apply consistent settings, and manage the migration process in a structured and automated way."
"When troubleshooting replication issues with AWS Application Migration Service (MGN), what is the first place you should check?","The Replication Agent logs on the source server.","The AWS CloudTrail logs.","The target EC2 instance's system logs.","The Application Migration Service (MGN) console health dashboard.","Checking the Replication Agent logs on the source server is the first step to troubleshoot replication issues, as they provide detailed information about the data transfer process."
"Which security best practice should be implemented when configuring the AWS Replication Agent for use with AWS Application Migration Service (MGN)?","Use IAM roles to restrict the agent's permissions to only what it needs.","Allow the agent unrestricted access to all resources.","Use the root AWS account credentials on the agent.","Disable encryption of data in transit.","Using IAM roles to restrict the agent's permissions ensures that the agent only has access to the resources it needs, minimising the potential security impact of a compromised agent."
"When using AWS Application Migration Service (MGN), what type of agent is installed on the source server?","Replication Agent","Monitoring Agent","Security Agent","Backup Agent","The Replication Agent is installed on the source server to continuously replicate data to AWS."
"What is the primary purpose of the AWS Application Migration Service (MGN)?","To rehost servers to AWS with minimal downtime","To refactor applications for cloud-native architectures","To build serverless applications","To manage container deployments","Application Migration Service simplifies and accelerates the rehosting of servers to AWS, reducing the time and cost involved."
"Which replication setting in AWS Application Migration Service (MGN) determines how much data is transferred at a time?","Bandwidth throttling","Cutover window","Security group assignment","Launch Template","Bandwidth throttling allows you to control the rate at which data is transferred, which can be helpful in situations with limited network bandwidth."
"With AWS Application Migration Service (MGN), what is a 'Cutover'?","The final migration step when the application is launched on AWS","A temporary pause in the replication process","The initial installation of the replication agent","A method of data compression","A Cutover is the final step in the migration process, where the replicated server is launched on AWS and the application starts running in the cloud."
"What is the purpose of test instances when migrating with AWS Application Migration Service (MGN)?","To validate that the migrated server functions correctly on AWS","To test the network connectivity to the source server","To measure the performance of the replication agent","To verify the security configuration of the source server","Test instances allow you to perform testing and validation of the migrated server to ensure it functions as expected before the final cutover."
"What kind of data does AWS Application Migration Service (MGN) encrypt during replication?","Data in transit","Data at rest in the source server","Data at rest in the target EBS volume","Data in memory","Application Migration Service encrypts data in transit between the source server and AWS to protect the data during replication."
"When migrating with AWS Application Migration Service (MGN), which AWS service is primarily used for creating the migrated instances?","EC2","Lambda","S3","DynamoDB","EC2 instances are launched in AWS to host the migrated servers when using Application Migration Service."
"After migrating a server using AWS Application Migration Service (MGN), what action should you take on the source server?","Deactivate the replication agent and decommission the server","Immediately remove the replication agent","Re-purpose the source server to be a backup server","Leave the source server running indefinitely","After successful migration, the replication agent should be deactivated and the source server can be decommissioned to avoid unnecessary costs and potential security risks."
"With AWS Application Migration Service (MGN), how does data replication from the source server to AWS occur?","Continuous, block-level replication","Snapshot-based replication","File-based synchronisation","Database replication","Application Migration Service uses continuous, block-level replication to ensure that data is constantly synchronised from the source server to AWS, minimising downtime."
"Which of the following best describes the overall migration strategy supported by AWS Application Migration Service (MGN)?","Rehosting","Refactoring","Replatforming","Repurchasing","AWS Application Migration Service primarily supports the rehosting (lift and shift) migration strategy."