"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Database Migration Service (DMS)?","To migrate databases to AWS quickly and securely","To optimise database performance","To monitor database health","To backup databases","DMS's main function is to migrate databases between different platforms, whether on-premises or in the cloud."
"What type of migration is supported by AWS DMS?","Homogeneous and Heterogeneous migrations","Homogeneous migrations only","Heterogeneous migrations only","Cloud-only migrations","DMS supports both homogeneous migrations (e.g., Oracle to Oracle) and heterogeneous migrations (e.g., Oracle to PostgreSQL)."
"Which component of AWS DMS performs the actual data replication?","Replication Instance","Endpoint","Task","Schema Conversion Tool (SCT)","The Replication Instance is the engine that moves data between the source and target databases."
"In AWS DMS, what does an Endpoint represent?","A connection to a database source or target","A definition of a migration task","A tool for schema conversion","A CloudWatch dashboard","Endpoints define the connection information (e.g., server name, port, credentials) for the source and target databases."
"What is the purpose of the AWS DMS Schema Conversion Tool (SCT)?","To convert database schema from one engine to another","To monitor database performance","To optimise database queries","To encrypt database data","SCT helps convert the source database schema to a format compatible with the target database, especially in heterogeneous migrations."
"Which AWS service can be used in conjunction with DMS to assess schema compatibility before migration?","AWS Schema Conversion Tool (SCT)","AWS Config","AWS CloudTrail","AWS CloudWatch","AWS SCT can assess schema compatibility, highlighting potential issues before data migration."
"During a full load migration in AWS DMS, what is the initial phase of the migration process?","Copying all the data from the source to the target","Applying ongoing changes","Validating data","Performing schema conversion","A full load migration starts by copying all the data from the source database to the target database."
"What is the term for the process of capturing ongoing changes after the full load migration in AWS DMS?","Change Data Capture (CDC)","Data Transformation","Data Validation","Data Replication","Change Data Capture (CDC) refers to the process of capturing and applying incremental changes after the initial full load."
"Which of the following migration strategies minimizes downtime when using AWS DMS?","Using Change Data Capture (CDC)","Performing a full load migration","Migrating during peak hours","Ignoring schema differences","Change Data Capture (CDC) allows near real-time replication, minimising downtime during the cutover."
"What type of endpoint does AWS DMS require when migrating data to an Amazon S3 bucket?","Target endpoint","Source endpoint","Both Source and Target endpoint","Neither source nor target endpoint","When migrating data to S3, DMS only needs a target endpoint representing the S3 bucket."
"Which task setting in AWS DMS controls how data is transformed during migration?","Transformation Rules","Validation Settings","Logging Settings","Security Settings","Transformation Rules allow you to modify data as it is migrated, such as renaming tables or columns."
"How can you ensure data integrity during and after a migration using AWS DMS?","Using data validation tasks","Disabling logging","Skipping data transformation","Ignoring schema differences","Data validation tasks can compare data between the source and target to verify the migration was successful."
"What is the purpose of a Replication Instance in AWS DMS?","To perform data transformations","To store the migration metadata","To execute the migration tasks","To configure the source database","The Replication Instance is responsible for running the migration tasks and transferring data between the source and target."
"Which database engines are commonly used as source endpoints in AWS DMS?","Oracle, SQL Server, MySQL","S3, DynamoDB, Redshift","CloudWatch, CloudTrail, Config","Lambda, EC2, ECS","Oracle, SQL Server and MySQL are commonly used as source databases for migrations with DMS."
"Which database engines are commonly used as target endpoints in AWS DMS?","PostgreSQL, Aurora, Redshift","CloudWatch, CloudTrail, Config","Lambda, EC2, ECS","Oracle, SQL Server, MySQL","PostgreSQL, Aurora and Redshift are popular target databases in AWS DMS migrations."
"What is the function of the 'LOB' setting within a DMS task?","To handle large object data types","To limit the amount of data migrated","To log all migration activities","To load balance the migration process","'LOB' settings control how DMS handles large objects (LOBs) like BLOBs and CLOBs during migration."
"Which AWS service integrates with DMS to monitor the migration process?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS IAM","Amazon CloudWatch allows you to monitor DMS metrics and set up alarms for various migration events."
"Which of the following best describes a homogeneous migration in AWS DMS?","Migrating from SQL Server to SQL Server","Migrating from Oracle to PostgreSQL","Migrating from MySQL to MongoDB","Migrating from on-premises to S3","A homogeneous migration involves moving data between the same database engines."
"Which of the following best describes a heterogeneous migration in AWS DMS?","Migrating from MySQL to PostgreSQL","Migrating from SQL Server to SQL Server","Migrating from Oracle to Oracle","Migrating from a single AZ to a Multi AZ RDS instance","A heterogeneous migration involves moving data between different database engines."
"What is the benefit of using AWS DMS for migrating databases to AWS?","Reduced downtime and simplified migration","Automatic performance optimisation","Automatic data encryption","Automated security hardening","DMS helps reduce downtime during migration and simplifies the overall process."
"Which of the following is NOT a typical phase in a database migration project using AWS DMS?","Schema Conversion","Data Validation","Performance Tuning","Full Load and CDC","Performance Tuning is typically done *after* the data migration is complete."
"What is the significance of pre-validation tests in AWS DMS before starting a migration task?","To identify and resolve potential issues before migration","To optimise database performance","To encrypt sensitive data","To create database backups","Pre-validation tests help detect problems like incompatible data types or missing dependencies before the migration begins."
"What is a potential problem that AWS DMS pre-validation tests can help identify?","Incompatible data types between source and target","High CPU utilisation on the target instance","Network latency issues","Insufficient IAM permissions","Incompatible data types are a common issue detected by pre-validation tests."
"When migrating a database to AWS using DMS, what is the impact of insufficient network bandwidth?","Slower data transfer rates","Complete migration failure","Data corruption","Automatic scaling of network resources","Insufficient bandwidth leads to slower data transfer and increases the migration time."
"How can you improve the performance of an AWS DMS migration task?","Increase the replication instance size","Reduce the replication instance size","Disable logging","Reduce transformation rules","Increasing the replication instance size provides more resources for the migration."
"What is the purpose of using multi-AZ for the AWS DMS replication instance?","To improve fault tolerance and availability","To increase migration speed","To reduce migration costs","To simplify the migration process","Multi-AZ provides high availability for the replication instance."
"Which type of log provides detailed information about errors and events during an AWS DMS migration task?","Task logs","Instance logs","SCT logs","CloudTrail logs","Task logs contain detailed information about the task's progress and any errors that occur."
"What does the AWS DMS status 'Table statistics not found' usually indicate?","DMS is unable to collect table statistics from the source database","The task has completed successfully","The target table does not exist","The replication instance is overloaded","This status indicates that DMS cannot collect statistics, which can affect task performance."
"What is the recommended approach for handling primary key constraints when migrating data to an Amazon Redshift target using AWS DMS?","Create the primary key constraint after the data load","Create the primary key constraint before the data load","Drop the primary key constraint during the migration","Ignore the primary key constraint","Redshift does not enforce primary key constraints, so it's generally recommended to create them *after* the data load."
"How does AWS DMS handle data type conversions during heterogeneous database migrations?","It automatically converts data types based on predefined rules","It requires manual data type mapping configurations","It only supports migrations between databases with identical data types","It skips columns with incompatible data types","DMS can automatically convert data types, but you can also manually configure data type mappings for more control."
"What is the impact of data transformation rules on migration performance in AWS DMS?","They can increase the processing overhead","They can improve the processing overhead","They have no impact on performance","They are only applied during full load","Transformation rules add processing overhead, as DMS needs to apply the rules to each record being migrated."
"Which AWS service can be used to store the AWS DMS replication instance logs?","Amazon CloudWatch Logs","Amazon S3","Amazon RDS","Amazon EC2","Amazon CloudWatch Logs is commonly used to store and monitor logs from DMS replication instances."
"When should you consider using AWS DMS for a database migration project?","When migrating large databases with minimal downtime","When performing a simple database backup","When optimising database query performance","When building a new database application","DMS is best suited for migrating large databases while minimising downtime."
"How does AWS DMS ensure data consistency during a migration with Change Data Capture (CDC)?","By applying transactions in the same order as the source database","By validating checksums of the data","By automatically retrying failed transactions","By using parallel processing","DMS applies transactions in the same order they occurred on the source database."
"What is the role of the AWS DMS Control Table?","Stores metadata about the migration process","Stores the actual data being migrated","Stores the database schema","Stores user credentials","The control table stores metadata about the migration process, such as task status and progress."
"Which of the following is a valid task setting in AWS DMS for handling errors?","Stop After Error","Ignore Errors","Skip Table","Retry Immediately","'Stop After Error' is a valid task setting that tells DMS to stop the task if an error occurs."
"What is the purpose of the 'Target table preparation mode' setting in AWS DMS?","To control how DMS creates or prepares target tables","To control how data is transformed","To define the logging level","To configure security settings","This setting determines if DMS creates, truncates, or does nothing with the target table before loading data."
"What is the function of the AWS DMS 'Validate migration' task setting?","To compare the data between source and target after the migration","To check if the target database exists","To verify the source database connection","To optimise the replication instance","This setting runs a validation process to compare data between the source and target after the migration to ensure accuracy."
"Which AWS DMS task setting controls the level of detail included in the task logs?","Logging level","Debug level","Verbosity level","Detail level","The logging level setting determines how much detail is included in the DMS task logs."
"Which AWS DMS task setting is used to specify the rules for transforming data during migration?","Table mapping","Transformation rules","Data conversion","Schema conversion","Transformation rules allow you to modify the data during the migration process."
"In AWS DMS, what does a 'full load plus CDC' replication strategy involve?","Performing a full load followed by capturing ongoing changes","Only capturing ongoing changes","Only performing a full load","Backing up and restoring the database","Full load plus CDC involves an initial full data copy followed by the ongoing capture of changes."
"What is the significance of 'Commit rate' setting in an AWS DMS task?","Controls the frequency at which transactions are committed to the target database","Controls the speed of data replication","Controls the encryption level","Controls the size of the replication instance","The commit rate controls how frequently transactions are committed, affecting performance and recovery."
"What does the AWS DMS error 'Resource limit exceeded' typically indicate?","The replication instance has reached its capacity limits","The source database is unavailable","The target database is full","The IAM role lacks necessary permissions","This error suggests the replication instance needs to be scaled up to handle the workload."
"What is the purpose of the 'Parallel load' setting in AWS DMS?","To improve the speed of full load migration by using multiple threads","To encrypt the data in parallel","To validate data in parallel","To perform schema conversion in parallel","Parallel load increases migration speed by using multiple threads to load data simultaneously."
"How can you monitor the progress of an AWS DMS migration task in real-time?","Using Amazon CloudWatch metrics","Using AWS Config rules","Using AWS CloudTrail logs","Using Amazon Inspector","Amazon CloudWatch provides various metrics to track the progress and performance of DMS tasks."
"When migrating a database using AWS DMS, how does the migration impact the source database?","Minimal impact with Change Data Capture (CDC)","Significant performance impact during full load","Requires a restart of the source database","Requires schema changes on the source database","With CDC, the impact is minimised as only changes are replicated, but there can be a bigger load during full load."
"Which AWS service is used to manage access control for AWS DMS resources?","AWS IAM","AWS CloudTrail","AWS Config","AWS Certificate Manager","IAM is used to control access to AWS services, including DMS."
"When migrating to Amazon Aurora using AWS DMS, which feature can further reduce downtime?","Using Aurora Global Database","Using Aurora Serverless","Using Aurora Auto Scaling","Using Aurora Backtrack","Aurora Global Database is primarily for DR, not minimising downtime during a *migration*."
"In AWS DMS, what is the purpose of a Replication Instance?","To host the DMS task and perform the data migration","To store the source database credentials","To store the target database schema","To act as a load balancer for database connections","The Replication Instance is responsible for performing the actual data migration, including reading from the source, transforming the data, and writing to the target."
"Which migration strategy is typically associated with AWS DMS when migrating heterogeneous databases?","Schema conversion using AWS SCT followed by data migration with DMS","Direct data replication without schema changes","Snapshot migration with minimal downtime","Using native database tools for migration","For heterogeneous database migrations, AWS Schema Conversion Tool (SCT) is typically used to convert the source schema to a compatible target schema, and then DMS migrates the data."
"What type of data validation can AWS DMS perform after migrating data?","Data validation ensures data consistency between source and target","It validates network connectivity","Validates the size of the data migrated","Validates the security groups associated with the DMS instance","AWS DMS can perform data validation to compare data in the source and target databases, helping to ensure data consistency and accuracy after migration."
"Which AWS service is commonly used in conjunction with AWS DMS to assess and convert database schemas?","AWS Schema Conversion Tool (SCT)","AWS Glue","AWS Data Pipeline","AWS CloudTrail","AWS Schema Conversion Tool (SCT) is used to assess and convert database schemas from one engine to another, making it a natural partner with DMS for heterogeneous migrations."
"What is the purpose of a 'Table Mapping' in an AWS DMS Task?","To specify which tables to migrate and how they should be transformed","To define the network routing for data transfer","To configure the security settings for the migration","To set the storage location for the replicated data","Table mappings define the rules for selecting and transforming tables during the migration process. They specify which tables to include, and what transformations (if any) to apply."
"What is the difference between 'Full Load' and 'Change Data Capture (CDC)' in AWS DMS?","Full Load migrates existing data, CDC replicates ongoing changes","Full Load migrates schema, CDC migrates data","Full Load is for homogeneous migrations, CDC is for heterogeneous","Full Load is faster, CDC is slower","Full Load migrates all the existing data from the source to the target, while CDC captures and applies ongoing changes to keep the target database synchronized with the source."
"What is the purpose of using endpoints in AWS DMS?","To define the source and target databases for the migration","To define the transformation rules for the data","To specify the AWS region for the migration","To monitor the progress of the migration","Endpoints in AWS DMS are used to define the source and target databases for the data migration. They specify the connection details and database type."
"Which AWS service is often used as a target endpoint for AWS DMS when migrating data to a data warehouse?","Amazon Redshift","Amazon S3","Amazon DynamoDB","Amazon EC2","Amazon Redshift is a popular target endpoint for DMS when migrating data to a data warehouse for analytical purposes."
"Which of the following is NOT a supported source database engine for AWS DMS?","Microsoft Access","MySQL","Oracle","SQL Server","Microsoft Access is not a supported source database engine for AWS DMS. The supported engines are typically enterprise-grade databases."
"When should you consider using heterogeneous database migration with AWS DMS?","When migrating between different database engines (e.g., Oracle to PostgreSQL)","When migrating between the same database engine across different AWS regions","When migrating a small database with minimal downtime","When migrating a database with a simple schema","Heterogeneous database migration is appropriate when you need to migrate data between different database engines. It often involves schema conversion as well."
"What happens to ongoing transactions on the source database during a full load migration in AWS DMS?","They are allowed to complete before the migration starts","They are rolled back automatically","They are suspended until the migration completes","They are replicated to the target database","Transactions ongoing during a full load migration may lead to inconsistencies if not managed carefully. It is recommended to stop write operations to the source during the full load phase or use CDC."
"Which of the following is a key benefit of using AWS DMS for database migration?","Minimised downtime during migration","Automatic schema optimisation","Built-in data encryption at rest","Free database backups","AWS DMS aims to minimise downtime by migrating data while the source database remains online, especially when using CDC."
"In AWS DMS, what is the role of the 'AWS DMS Replication Agent'?","To capture changes from the source database and send them to the replication instance","To create backups of the target database","To monitor the performance of the target database","To manage the security groups for the DMS instance","The DMS Replication Agent is used in certain scenarios to capture changes from the source database and transmit them to the DMS replication instance, particularly when direct access to the source is restricted."
"When performing a homogeneous migration with AWS DMS, what schema migration approach is generally recommended?","Migrate the schema using native database tools","Use AWS SCT to automatically convert the schema","Manually recreate the schema on the target database","Use AWS DMS to automatically generate the schema","For homogeneous migrations, using native database tools is often recommended to migrate the schema since the underlying database engine remains the same."
"What is a 'Transformation Rule' in AWS DMS?","A rule that modifies data during migration (e.g., changing data types or filtering columns)","A rule that defines the network connectivity between source and target","A rule that specifies the security settings for the migration","A rule that determines the storage location for the replicated data","Transformation rules allow you to modify the data as it's being migrated. This can include things like changing data types, filtering columns, or applying calculations."
"What is the purpose of the 'Test Endpoint Connection' functionality in AWS DMS?","To verify connectivity between the replication instance and the source/target databases","To test the performance of the migration task","To test the network bandwidth between the source and target","To test the security configurations of the endpoints","The 'Test Endpoint Connection' functionality in AWS DMS is used to verify that the replication instance can successfully connect to both the source and target databases."
"Which of the following is a typical use case for AWS DMS?","Migrating a production database to a new AWS region","Performing real-time analytics on database data","Managing access control to database instances","Creating backups of database data","A typical use case for AWS DMS is migrating a production database to a new AWS region to improve performance or disaster recovery capabilities."
"How does AWS DMS handle large objects (LOBs) during migration?","It supports different LOB migration methods (e.g., limited LOB mode, full LOB mode)","It automatically compresses LOBs before migration","It excludes LOBs from the migration by default","It converts LOBs to text data","AWS DMS provides different LOB migration modes to handle large objects, allowing you to choose the best approach based on your requirements and the size of the LOBs."
"Which of the following is a limitation of AWS DMS?","It cannot migrate data between different database engines without schema conversion","It cannot migrate encrypted databases","It cannot migrate data from on-premises databases","It can only migrate to Amazon RDS","AWS DMS can migrate data between different database engines (with the help of SCT), and it supports migrating data from on-premises databases. It can migrate to various AWS services besides RDS."
"What is the relationship between AWS DMS and AWS DataSync?","AWS DMS is for database migration, AWS DataSync is for file system replication","AWS DMS is for file system replication, AWS DataSync is for database migration","They both perform the same function","They are completely unrelated services","AWS DMS is specifically designed for database migration, while AWS DataSync is designed for replicating file systems and object storage."
"In AWS DMS, what does the 'Target Table Preparation Mode' setting control?","How the target tables are created or prepared before the migration starts","The compression level used for the migrated data","The encryption settings for the target database","The network routing for the replicated data","The 'Target Table Preparation Mode' setting controls how the target tables are created or prepared before the migration starts. Options may include creating the tables, dropping and recreating them, or truncating them."
"Which of the following is NOT a typical task involved in setting up an AWS DMS migration?","Configuring the AWS Lambda function","Creating the replication instance","Defining the source and target endpoints","Creating the migration task","Configuring an AWS Lambda function is not a standard part of setting up a typical DMS migration task."
"What is the purpose of using multiple AWS DMS replication instances?","To increase the migration throughput and improve performance","To provide high availability for the migration task","To reduce the cost of the migration","To simplify the migration process","Using multiple replication instances can increase migration throughput and improve overall performance, especially for large databases."
"How does AWS DMS handle errors encountered during data migration?","It logs errors and continues the migration, skipping problematic records","It stops the migration task immediately","It automatically attempts to correct the errors","It sends an email notification to the administrator","AWS DMS logs errors and can continue the migration, skipping problematic records. It's important to monitor the logs and address any errors that occur."
"When would you choose to use AWS DMS over native database migration tools?","When you need to minimise downtime during migration","When you need to perform a simple backup and restore","When you need to perform schema conversion","When you need to create a database replica","AWS DMS is particularly useful when you need to minimise downtime during the migration process, especially when combined with Change Data Capture (CDC)."
"What is the role of the 'Control Table' in AWS DMS when using Change Data Capture (CDC)?","To track the progress of the CDC replication process","To store the schema information for the source and target databases","To manage the network connections between the source and target","To store the security credentials for the endpoints","The Control Table helps track the progress of the CDC replication process and ensures data consistency during ongoing changes."
"Which AWS service can be used to monitor the performance and health of AWS DMS replication instances?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch can be used to monitor various metrics related to the performance and health of AWS DMS replication instances, helping you identify and resolve any issues."
"What is the purpose of the 'LOB support' option in AWS DMS task settings?","To specify how large objects (LOBs) should be migrated","To enable data compression during migration","To encrypt data in transit","To control the network bandwidth used for migration","The 'LOB support' option in AWS DMS task settings allows you to specify how large objects (LOBs) should be migrated, with options like limited LOB mode or full LOB mode."
"Which database engines can AWS DMS migrate data to and from?","Homogenous and heterogenous databases, including Oracle, MySQL, SQL Server, PostgreSQL, MariaDB, and Amazon Aurora","Only from on-premise servers to AWS RDS","Only from AWS RDS to Amazon S3","Only homogenous databases","AWS DMS supports homogenous and heterogenous databases, including Oracle, MySQL, SQL Server, PostgreSQL, MariaDB, and Amazon Aurora."
"Which of the following is a valid use case for AWS DMS?","Migrating a database from an on-premises environment to Amazon RDS","Backing up a database to Amazon S3","Creating a disaster recovery plan for a database","Monitoring the performance of a database","Migrating a database from an on-premises environment to Amazon RDS is a typical and valid use case for AWS DMS."
"How can you ensure data security during a migration using AWS DMS?","By encrypting data at rest and in transit","By using a dedicated network connection","By limiting access to the replication instance","By using AWS CloudTrail to monitor activity","Data security can be ensured by encrypting data at rest and in transit using SSL connections, IAM roles, and KMS encryption."
"What is a 'replication task' in AWS DMS?","A set of instructions defining the data migration process","A virtual server used to host the database","A network connection between the source and target databases","A backup of the source database","A replication task in AWS DMS defines the data migration process, including the source and target endpoints, table mappings, and transformation rules."
"You need to migrate a large database with minimal downtime. Which DMS feature is most crucial?","Change Data Capture (CDC)","Full Load migration","Schema conversion","Data validation","Change Data Capture (CDC) is the most crucial feature for minimizing downtime during the migration of large databases, as it replicates ongoing changes."
"What does 'Resource optimisation' refer to in the context of AWS DMS migration planning?","Efficiently allocating and managing resources like replication instances","Ensuring data is encrypted at rest and in transit","Optimising the database schema for the target environment","Reducing the cost of data transfer","Resource optimisation in DMS migration planning refers to efficiently allocating and managing resources such as replication instances to ensure optimal performance."
"What is the primary benefit of using AWS DMS for ongoing replication?","Keeping the target database synchronised with the source database","Creating a backup of the source database","Creating a disaster recovery environment","Improving database performance","The primary benefit of using AWS DMS for ongoing replication is keeping the target database synchronised with the source database, enabling scenarios like disaster recovery or data warehousing."
"What is the 'migration type' setting in AWS DMS used for?","Specifying the type of migration, such as one-time migration or ongoing replication","Defining the database engine for the source and target","Specifying the network connectivity for the migration","Configuring the security settings for the migration","The 'migration type' setting in AWS DMS is used to specify the type of migration, such as a one-time migration (full load) or ongoing replication (CDC)."
"What is the significance of a 'schema only' migration in AWS DMS?","It migrates only the database schema without the data","It migrates only the data without the schema","It migrates both the schema and the data","It migrates only specific tables","A 'schema only' migration in AWS DMS migrates only the database schema (table definitions, indexes, etc.) without migrating the data itself."
"Which of the following is a recommended best practice for using AWS DMS?","Start with a small subset of data for initial testing","Always migrate all tables in a database at once","Use a single replication instance for all migrations","Disable data validation to improve performance","It's a best practice to start with a small subset of data for initial testing to validate the migration setup and configuration before migrating the entire database."
"How does AWS DMS help in disaster recovery scenarios?","By continuously replicating data to a secondary database in a different region","By providing automated database backups","By automatically failing over to a secondary database","By providing tools for schema conversion","AWS DMS helps in disaster recovery scenarios by continuously replicating data to a secondary database, ensuring minimal data loss in case of a primary database failure."
"Which endpoint type would you choose in AWS DMS for migrating data to an on-premises SQL Server database?","Microsoft SQL Server","Amazon S3","PostgreSQL","Oracle","You would choose the 'Microsoft SQL Server' endpoint type in AWS DMS when migrating data to an on-premises SQL Server database."
"What is the use of 'Selection rules' in AWS DMS Task settings?","To define which tables or schemas should be included in the migration","To define which data types should be transformed","To define which users should have access to the target database","To define the order in which tables should be migrated","'Selection rules' are used to define which tables or schemas should be included in the migration. You can use wildcard characters to specify patterns."
"How does AWS DMS handle data type conversion during heterogeneous migration?","It uses AWS Schema Conversion Tool (SCT) to convert data types","It automatically converts data types based on default mappings","It requires manual data type mapping","It does not support data type conversion","AWS DMS relies on AWS Schema Conversion Tool (SCT) to handle data type conversion during heterogeneous migrations, allowing for schema transformation and data type mapping."
"When setting up an AWS DMS task, what does the 'Error handling' section allow you to configure?","The behaviour of the task when it encounters errors, such as skipping records or stopping the task","The network configuration for error logging","The email notifications for error alerts","The automatic retry attempts for failed tasks","The 'Error handling' section allows you to configure the behaviour of the task when it encounters errors, such as skipping records or stopping the task altogether."
"Which of the following AWS DMS features ensures data integrity during and after migration?","Data validation","Data encryption","Data compression","Data partitioning","Data validation is a feature that ensures data integrity by comparing data on source and target."
"What does the 'commit rate' setting in AWS DMS control?","The frequency at which transactions are committed to the target database","The compression rate of the data being migrated","The encryption rate of the data being migrated","The data transfer rate","The 'commit rate' setting in AWS DMS controls the frequency at which transactions are committed to the target database. Increasing the commit rate can improve performance, but also increase the risk of data loss in case of a failure."
"What type of migration strategy is best suited for databases with minimal downtime requirements using AWS DMS?","Online migration using Change Data Capture (CDC)","Offline migration using full load only","Big bang migration with cutover window","Snapshot migration with schema conversion","Online migration using Change Data Capture (CDC) is best suited for databases with minimal downtime requirements, continuously replicating changes to the target."
"Why is it important to assess the network bandwidth when planning an AWS DMS migration?","To ensure sufficient bandwidth for transferring data between source and target","To optimise the cost of the migration","To ensure data security during the migration","To ensure compliance with data residency regulations","Assessing the network bandwidth is important to ensure sufficient bandwidth for transferring data between the source and target, avoiding bottlenecks and delays."
"In AWS DMS, what is the primary purpose of a replication instance?","To host the replication tasks and perform the data migration","To store the target database","To provide the source database","To manage user access to the databases","The replication instance is responsible for hosting the replication tasks and performing the actual data migration process."
"What type of data migration does AWS DMS support?","Homogeneous and Heterogeneous migrations","Homogeneous migrations only","Heterogeneous migrations only","Only database upgrades","DMS supports both homogeneous migrations (e.g., Oracle to Oracle) and heterogeneous migrations (e.g., Oracle to PostgreSQL)."
"What is the role of endpoints in AWS DMS?","They define the source and target databases","They define the replication instance","They define the migration tasks","They define the network configuration","Endpoints specify the source and target database connections, including database engine, server name, port, username, and password."
"Which of the following is a supported source database for AWS DMS?","Oracle","Microsoft Excel","Apache Hadoop","Adobe PDF","Oracle is a supported source database engine for DMS. DMS supports many different database engines."
"Which of the following is a supported target database for AWS DMS?","Amazon Redshift","Microsoft Powerpoint","Adobe Photoshop","XML Files","Amazon Redshift is a supported target data warehouse for DMS, allowing you to migrate data into Redshift."
"What is the purpose of AWS DMS Schema Conversion Tool (SCT)?","To convert database schema from one engine to another","To manage replication instances","To monitor replication tasks","To optimize database performance","SCT assists in converting database schema objects, stored procedures, and database code from one database engine to another, making heterogeneous migrations easier."
"In AWS DMS, what does the term 'task' refer to?","A configuration defining what data to migrate and how","A physical server running the replication","A user account with permissions to access databases","A network firewall rule","A DMS task defines what data to migrate (tables, views, etc.) and how it should be migrated (full load, CDC, etc.)."
"What is Change Data Capture (CDC) in the context of AWS DMS?","A method for capturing ongoing changes to the source database","A method for backing up data","A method for restoring data","A method for encrypting data","CDC captures changes made to the source database after the initial full load, replicating these changes to the target database in near real-time."
"Which of the following is a common use case for AWS DMS?","Migrating databases to AWS cloud","Running analytics queries","Developing web applications","Managing user identities","DMS is primarily used for migrating databases to the AWS cloud, whether it's homogeneous or heterogeneous migration."
"What type of endpoint does AWS DMS require to connect to an on-premises database?","A database instance and connectivity to the database","A VPN connection only","An SSH tunnel only","A firewall rule only","AWS DMS needs connectivity to the database, this is commonly achieved via a VPN or Direct Connect."
"What is the purpose of using transformation rules in an AWS DMS task?","To modify data during migration","To speed up the migration process","To compress the data","To encrypt the data","Transformation rules allow you to modify data during migration, such as changing data types, filtering data, or applying calculations."
"Which of the following is a limitation of using AWS DMS for migrating data?","DMS cannot migrate stored procedures in some cases","DMS cannot migrate data larger than 1TB","DMS can only migrate homogeneous databases","DMS can only migrate to RDS","DMS has limitations with complex stored procedures and custom functions during heterogeneous migrations, sometimes requiring manual conversion."
"What type of migration strategy does AWS DMS primarily use?","Online migration with minimal downtime","Offline migration with extended downtime","Snapshot-based migration","Log shipping migration","DMS primarily uses an online migration strategy, minimising downtime by capturing changes and replicating them to the target database while the source database remains operational."
"What is the purpose of the 'Full Load' phase in an AWS DMS task?","To copy all the existing data from the source to the target","To capture changes made to the source database","To validate data integrity","To optimise database performance","The Full Load phase is the initial phase where all the existing data from the source database is copied to the target database."
"How can you monitor the progress of an AWS DMS task?","Using the AWS Management Console and CloudWatch","Using the AWS CLI only","Using CloudTrail only","Using the AWS SDK only","You can monitor DMS tasks through the AWS Management Console and CloudWatch, which provides metrics and logs for replication tasks."
"Which of the following factors can affect the performance of an AWS DMS task?","Network bandwidth and latency","CPU speed of the replication instance","Size of the target database","Location of the source database","Network bandwidth and latency between the source, replication instance, and target database significantly impact DMS performance."
"What does the 'Replication Status' of an AWS DMS task indicate?","The current state of the data migration process","The amount of data migrated","The number of errors encountered","The duration of the migration","The Replication Status indicates the current state of the migration process, such as 'Running,' 'Stopped,' 'Failed,' etc."
"When should you use the AWS DMS Schema Conversion Tool (SCT)?","When migrating from a different database engine","When migrating to the same database engine","When creating database backups","When optimizing database performance","SCT is primarily used during heterogeneous migrations (e.g., Oracle to PostgreSQL) to convert database schemas."
"What is the benefit of using AWS DMS for migrating databases instead of manual methods?","Reduced downtime and simplified migration process","Increased security","Improved database performance","Lower storage costs","DMS offers reduced downtime and a simplified migration process compared to manual methods."
"What type of data consistency does AWS DMS provide during migration?","Eventual consistency","Strong consistency","Immediate consistency","No consistency","DMS provides eventual consistency during ongoing replication, meaning that changes will eventually be reflected in the target database."
"What is the purpose of using multiple AWS DMS tasks?","To parallelize the migration process","To migrate different regions","To migrate different tables","To test database performance","Using multiple DMS tasks allows you to parallelize the migration process, which can speed up the overall migration time."
"In AWS DMS, what is the purpose of a 'table mapping'?","To specify which tables to migrate and how","To specify database performance","To specify the data validation rules","To specify data encryption","Table mappings define which tables to migrate from the source to the target database and how the data should be transformed, if needed."
"What is the advantage of using AWS DMS for migrating databases to Amazon RDS?","Simplified setup and management of the target database","Lower CPU utilisation","Higher memory utilisation","Increased complexity","DMS simplifies the setup and management of the target database when migrating to Amazon RDS, as RDS provides managed database services."
"Which of the following is a best practice for optimizing AWS DMS performance?","Use a replication instance in the same region as the source and target","Use a small replication instance","Use a low network bandwidth","Use a VPN to access the database","Placing the replication instance in the same region as the source and target databases minimizes latency and improves migration performance."
"What type of database migration does AWS DMS 'full load and CDC' support?","A migration process where the entire data is copied and after which, only the ongoing data changes are replicated.","A migration process where only the database structure is copied.","A migration process where only the most recent data is copied.","A migration process where only the logs are migrated.","'Full load and CDC' is a migration approach where the initial data is bulk copied and then ongoing changes are replicated."
"In AWS DMS, what is the purpose of the 'LOB' (Large Object) settings?","To handle large data types like BLOBs and CLOBs","To manage the size of database tables","To compress data during migration","To encrypt data during migration","LOB settings control how DMS handles large data types like BLOBs (Binary Large Objects) and CLOBs (Character Large Objects) during migration."
"What happens if the AWS DMS replication instance runs out of storage?","The replication task will pause or fail","The source database will become unavailable","The target database will become unavailable","DMS will automatically allocate more storage","If the replication instance runs out of storage, the DMS task will pause or fail, as it needs storage space to buffer changes and perform data transformations."
"What is the purpose of using AWS CloudWatch with AWS DMS?","To monitor replication task performance and troubleshoot issues","To manage database backups","To manage user access","To optimise database performance","CloudWatch provides metrics and logs for monitoring DMS task performance and troubleshooting issues."
"What is the maximum size of an individual table that can be migrated using AWS DMS?","There is no hard limit on the size of the table","1TB","10TB","100GB","There is no hard limit on the size of individual tables which can be migrated, although large tables may require more resources and time."
"How does AWS DMS handle data type conversions during heterogeneous migrations?","AWS DMS automatically converts data types","AWS DMS requires manual data type conversion","AWS DMS cannot migrate databases with different data types","AWS DMS uses a fixed mapping of data types","DMS attempts to automatically convert data types, but manual intervention is often required for complex or unsupported data type conversions, sometimes using SCT."
"What is the role of the AWS DMS task settings 'Target table preparation mode'?","To control how the target tables are created or handled","To improve database performance","To encrypt data during migration","To validate data integrity","The 'Target table preparation mode' setting controls how DMS prepares the target tables, such as creating them, truncating them, or doing nothing."
"Which of the following actions should you take to secure an AWS DMS replication instance?","Use security groups to control network access","Encrypt the replication instance storage","Enable multi-factor authentication","Use IAM roles","Using security groups to control network access to the replication instance is a key security measure."
"What is the purpose of a Test Endpoint in AWS DMS?","To verify connectivity to the source and target databases","To run performance tests","To manage user access","To optimise database performance","Testing endpoints verifies connectivity to the source and target databases to ensure that DMS can access them before starting a migration."
"What does it mean when an AWS DMS task is in the 'Stopped' state?","The replication task has been manually stopped","The replication task has completed successfully","The replication task has encountered an error","The replication task is paused","A 'Stopped' state indicates that the replication task has been manually stopped by the user."
"Which AWS service is used to store the logs generated by AWS DMS replication tasks?","Amazon CloudWatch Logs","Amazon S3","Amazon Glacier","Amazon EBS","AWS DMS integration with CloudWatch means that logs are automatically stored in CloudWatch Logs."
"What is the purpose of using Amazon Virtual Private Cloud (VPC) with AWS DMS?","To isolate the replication instance in a private network","To improve database performance","To encrypt data during migration","To manage user access","Using a VPC allows you to isolate the replication instance in a private network, enhancing security and control over network traffic."
"When configuring an AWS DMS task, what is the 'Commit rate'?","The number of transactions committed per second","The rate at which data is encrypted","The speed of the network connection","The frequency of database backups","The commit rate controls the number of transactions that DMS commits to the target database per second, which can impact performance."
"Which of the following is a valid migration type when configuring an AWS DMS task?","Migrate existing data only","Migrate all data","Migrate recent data","Migrate historical data","DMS supports migrating existing data only (full load), migrating ongoing changes (CDC), or a combination of both."
"Which of the following methods is most suitable to migrate a 50TB on-premises database to AWS with minimal downtime?","AWS DMS","AWS Snowball Edge","AWS S3","Direct SQL import","AWS DMS is most suitable because it supports online migration, meaning the database can be migrated with minimal downtime."
"You are migrating a database using AWS DMS and encounter an error related to data type mismatch. What should you do?","Use AWS Schema Conversion Tool (SCT) to convert the schema","Restart the replication instance","Increase the allocated storage","Contact AWS Support","SCT can help convert the schema and data types from one database to another before the migration, solving this error."
"When using AWS DMS, which security measure is most effective in protecting data in transit between the source and target databases?","Enabling SSL encryption for the endpoints","Using IAM roles","Enabling multi-factor authentication","Applying network ACLs","Enabling SSL encryption for the endpoints ensures that the data is encrypted while in transit between the source and target databases."
"What is the purpose of the 'Error handling' settings in an AWS DMS task configuration?","To define how DMS should respond to errors during migration","To encrypt data during migration","To optimise database performance","To validate data integrity","'Error handling' settings allow you to define how DMS should respond to errors during the migration process, such as stopping the task or retrying."
"You are migrating a large database using AWS DMS and notice the replication instance is experiencing high CPU utilisation. What action should you take?","Increase the size of the replication instance","Increase the allocated storage","Reduce the commit rate","Change network routing","Increasing the size of the replication instance provides more CPU resources, which can improve performance when the instance is CPU-bound."
"Which of the following tasks can you perform using AWS DMS?","Database migration","Operating System patching","Network monitoring","Application deployment","AWS DMS's purpose is database migration."
"When performing a heterogeneous database migration with AWS DMS, what's the first step you should typically take?","Assess and convert the source database schema","Start the full load","Configure the target database","Choose target database engine","Schema conversion is the very first step."
"During an AWS DMS migration, you notice high latency between the replication instance and the source database. What could be the cause?","Geographical distance between the instance and database","Insufficient storage on the replication instance","Incorrect IAM role configuration","An oversized replication instance","Network latency is directly impacted by the geographical distance"
"What happens to the source database after AWS DMS has completed a full load and is in the CDC phase?","It remains operational and continues to receive updates","It is automatically shut down","It is converted to a read-only replica","It is automatically backed up","The source database remains operational and active for the application."
"If an AWS DMS task fails repeatedly, what is a common troubleshooting step?","Review the CloudWatch logs for error messages","Restart the source database","Increase the allocated storage on the source database","Switch to a different AWS region","DMS logs contain diagnostic information, looking for error messages is a great first step."
"What is a benefit of using AWS DMS over other data migration methods?","Support for continuous data replication","Automated schema conversion","Higher performance","Reduced data security","One of the core benefits of DMS is supporting continuous data replication."
"When would you consider using AWS DMS with AWS DataSync?","When migrating large files or objects to AWS S3","When migrating database schemas","When performing complex data transformations","When optimizing network performance","DataSync helps migrate large file volumes to S3, which could be complementary to migrating a database via DMS."
"After configuring an AWS DMS task, how can you validate the data integrity after the migration?","Use data validation tools and techniques","Rely solely on DMS's internal checks","Manually compare data between databases","Check the CloudWatch metrics","Data validation is a standard step to check if there is data corruption during the migration."
"In AWS DMS, what does the term 'Heartbeat' refer to?","A mechanism for monitoring replication latency","An automated task restart","A data encryption method","A cost optimization technique","Heartbeat is a mechanism to check on the replication lag between source and target databases."
"What is a primary consideration when choosing the size of an AWS DMS replication instance?","The amount of data to be migrated","The number of users accessing the data","The frequency of database backups","The cost of the instance","The amount of data to be migrated impacts the compute requirements, and therefore the replication instance size."
"In AWS DMS, what is the purpose of a replication instance?","To host the DMS software and perform data migration tasks","To store backup copies of the source database","To provide a read-only replica of the target database","To serve as a load balancer for database traffic","The replication instance hosts the DMS software that moves data between the source and target databases."
"What type of migration does AWS DMS support?","Homogeneous and heterogeneous migrations","Only homogeneous migrations","Only heterogeneous migrations","Only migrations to NoSQL databases","DMS supports both homogeneous migrations (e.g., SQL Server to SQL Server) and heterogeneous migrations (e.g., Oracle to MySQL)."
"What is the purpose of using schema conversion tool (SCT) in AWS DMS?","To convert the source database schema to a compatible schema for the target database","To encrypt data during the migration process","To monitor the replication process","To perform data validation after the migration","The SCT is used to automatically convert database schemas from one database engine to another."
"Which of the following is a change processing tuning setting available within DMS?","Commit rate","Task concurrency","Schema refresh interval","Replication instance size","Commit rate controls the number of changes committed to the target database in a single batch, affecting performance."
"What is the purpose of the AWS DMS task settings 'Error handling' configuration?","To define how DMS handles errors encountered during the migration process","To specify the level of logging for the migration task","To set up automatic retries for failed tasks","To configure alerts for critical errors","The error handling configuration defines how DMS should respond to errors, such as suspending the task, skipping the record, or logging the error."
"What is the first step when setting up a homogeneous database migration using AWS DMS?","Creating the replication instance","Creating endpoints for the source and target databases","Creating the migration task","Configuring security groups","First, you need to create endpoints to connect your source and target database."
"What is the purpose of a AWS DMS replication task?","To define the source and target databases, tables to migrate, and migration type","To manage user access to the source and target databases","To monitor the CPU utilisation of the replication instance","To create backups of the source database","The replication task defines the specifics of the migration, including the source and target endpoints, table selection, and migration type (full load, CDC, or both)."
"Which of the following migration types does AWS DMS support?","Full load, Change Data Capture (CDC), and full load + CDC","Full load only","Change Data Capture (CDC) only","Schema conversion only","DMS supports performing a full data load, capturing ongoing changes (CDC), or a combination of both."
"Which of the following is a key consideration when choosing an AWS DMS replication instance size?","The amount of data to be migrated and the change rate","The network bandwidth between the source and target databases","The CPU utilisation of the source database","The storage capacity of the target database","The replication instance size should be determined based on the volume of data to be migrated and the expected change rate."
"When migrating from Oracle to PostgreSQL using AWS DMS, what role does the Schema Conversion Tool (SCT) play?","Converting Oracle PL/SQL code to PostgreSQL compatible code","Migrating data between the databases","Managing the replication instance","Monitoring the migration process","SCT can help migrate Oracle’s PL/SQL to PostgreSQL's PL/pgSQL."
"In AWS DMS, what does the 'Target table preparation mode' setting control?","How DMS prepares the target tables before loading data","The compression algorithm used for data transfer","The encryption key used for data security","The network protocol used for communication","The 'Target table preparation mode' setting defines actions like 'Do nothing', 'Drop tables', or 'Truncate tables' on the target database."
"Which AWS service can you use to monitor the progress and performance of your AWS DMS tasks?","CloudWatch","CloudTrail","Config","Trusted Advisor","CloudWatch provides metrics and logs for monitoring the performance and progress of DMS tasks."
"What is the primary benefit of using AWS DMS for database migration?","Minimising downtime during the migration process","Automatically optimising database queries","Providing real-time data analytics","Creating a disaster recovery solution","DMS minimises downtime by continuously replicating changes from the source to the target database."
"Which of the following is NOT a supported source database for AWS DMS?","Amazon Aurora","Microsoft Access","Oracle","MySQL","Microsoft Access is not a supported source database for AWS DMS"
"Which of the following is a supported target database for AWS DMS?","Amazon Aurora","Microsoft Access","IBM DB2","Informix","Amazon Aurora is a supported target database for AWS DMS"
"What is the purpose of the 'Validation' feature in AWS DMS?","To verify that the data migrated to the target database matches the data in the source database","To check the compatibility of the source and target database schemas","To monitor the performance of the replication instance","To encrypt the data during the migration process","The validation feature verifies that the data on the target database is an accurate representation of the data from the source database"
"When using AWS DMS for a homogeneous migration, what type of task is generally recommended initially?","A full load task, followed by a CDC task","A CDC task only","A schema conversion task","A validation task only","For a homogeneous migration, it's typically recommended to start with a full load task to migrate the existing data, then follow with a CDC task to capture ongoing changes."
"What security measures should you consider when configuring an AWS DMS replication instance?","Security Groups to control network access, IAM roles for service permissions, and encryption for data at rest and in transit","Enabling multi-factor authentication for the AWS account","Configuring intrusion detection systems on the source database","Implementing strict password policies for database users","Consider security groups for network access, IAM roles for service permissions, and encryption for data at rest and in transit when configuring DMS."
"What is the significance of 'LOB support' in AWS DMS?","Handling large objects (LOBs) such as documents and images during migration","Managing logical object backup","Supporting logical operators in database queries","Load balancing across multiple replication instances","LOB support is important for migrating databases that contain large binary or character data, such as documents and images."
"Which of the following tasks can NOT be achieved directly with AWS DMS?","Modifying data during migration","Migrating data between different database engines","Performing a one-time data load","Capturing ongoing changes from a source database","DMS focuses on data replication, not data transformation. While transformations can be applied, they are generally more basic and not intended for complex data manipulation."
"Which of the following statements is true regarding AWS DMS endpoints?","Endpoints define the connection details for the source and target databases","Endpoints automatically create backups of the source database","Endpoints manage user access to the databases","Endpoints are used to monitor the performance of the replication instance","Endpoints contain the necessary connection information (server name, port, username, password) for DMS to access the source and target databases."
"What is the purpose of the 'Data transformations' feature within an AWS DMS task?","To modify data as it is being migrated from the source to the target","To encrypt data before it is migrated","To compress data during the migration process","To validate data after the migration is complete","The 'Data transformations' feature allows you to modify data as it is being migrated, such as changing data types, filtering rows, or replacing values."
"When should you consider using a larger AWS DMS replication instance?","When migrating large databases with high change rates","When migrating small databases with low change rates","When performing schema conversions","When monitoring database performance","A larger replication instance is needed when dealing with large databases that have a high rate of change, as it requires more resources to process the data."
"What is the primary advantage of using AWS DMS over creating custom ETL scripts for database migration?","Reduced development effort and simplified management","Faster data transfer rates","More granular control over the migration process","Lower cost","AWS DMS reduces the development effort associated with writing and managing custom ETL scripts for database migration."
"Which of the following is a common use case for AWS DMS Change Data Capture (CDC)?","Keeping a data warehouse synchronised with operational databases","Backing up a database","Restoring a database to a previous point in time","Creating a disaster recovery environment","CDC in DMS is commonly used to keep a data warehouse synchronised with changes made to operational databases."
"What type of network connectivity is required for an AWS DMS replication instance to access source and target databases within a VPC?","The replication instance must be in the same VPC as the databases, or have VPC peering or VPN connectivity","The replication instance must have a public IP address","The databases must be publicly accessible","The replication instance must be in a different region from the databases","The replication instance must be in the same VPC as the databases, or have VPC peering or VPN connectivity"
"Which AWS service can be integrated with AWS DMS to capture changes from an on-premises database and replicate them to the AWS Cloud?","AWS Direct Connect or AWS VPN","AWS Storage Gateway","AWS CloudFront","AWS Snowball","AWS Direct Connect or AWS VPN provides secure and reliable connectivity between an on-premises environment and the AWS Cloud, enabling DMS to capture changes from on-premises databases."
"What is the benefit of using the AWS DMS 'Start task' option with 'Start from checkpoint'?","Resumes the migration from the last known consistent point","Restarts the migration from the beginning","Skips the data validation step","Only migrates new data","The 'Start from checkpoint' option allows you to resume the migration from the last known consistent point, which is useful if the task was interrupted."
"What should you consider when migrating a database with sensitive data using AWS DMS?","Encrypting data at rest and in transit, and using appropriate IAM roles","Using a public IP address for the replication instance","Disabling data validation","Skipping schema conversion","When migrating sensitive data, ensure that data is encrypted both at rest and in transit, and use appropriate IAM roles to control access to the data."
"Which AWS DMS task setting can you use to control the number of tables migrated in parallel?","ParallelLoadThreads","MaxFullLoadSubTasks","NumberOfTables","TableLoadingTasks","The 'ParallelLoadThreads' setting controls the number of tables that are loaded in parallel during a full load migration."
"Which of the following actions would improve AWS DMS migration task performance?","Use a replication instance in the same region as the source and target databases","Increase the allocated storage for the target database","Reduce the number of indexes on the target tables during the initial load","Enable multi-AZ for the replication instance","Using a replication instance in the same region as the source and target databases minimises latency and improves performance."
"What does the AWS DMS task setting 'Full LOB mode' signify?","Migrate all LOBs from start to end","Migrate LOB's inline if under a certain size","Do not migrate LOBs at all","Migrate partial LOBs","'Full LOB mode' ensures that all large objects are migrated from start to finish, regardless of their size."
"Which AWS service can you use to automate the provisioning and configuration of AWS DMS replication instances?","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS Auto Scaling","AWS CloudFormation allows you to define and provision infrastructure as code, including AWS DMS replication instances."
"What does the AWS DMS status 'Replication running' indicate?","The task is successfully capturing and applying changes","The task is being configured","The task has completed its initial load","The task has encountered an error","The 'Replication running' status indicates that the DMS task is actively capturing changes from the source and applying them to the target database."
"Which AWS service can provide alerting when AWS DMS tasks fail or encounter errors?","Amazon CloudWatch Alarms","AWS Trusted Advisor","AWS CloudTrail","Amazon Inspector","CloudWatch Alarms can be configured to trigger notifications (e.g., via SNS) when specific metrics, such as errors in a DMS task, exceed predefined thresholds."
"You need to migrate a very large database (terabytes) to AWS using DMS. Which strategy would minimise the overall migration time?","Use multiple DMS tasks with partitioned data and enable parallel load","Use a single DMS task with a large replication instance","Use AWS Snowball to migrate data first then use DMS for CDC","Reduce the size of the replication instance to save on cost","For large databases, using multiple DMS tasks with partitioned data and enabling parallel load can significantly reduce the migration time."
"In AWS DMS, what is the purpose of the 'Migration type' setting being set to 'Migrate existing data and replicate ongoing changes'?","To perform a full load migration followed by a CDC migration","To migrate schema only","To perform a full load only","To replicate only existing data","'Migrate existing data and replicate ongoing changes' performs a full load migration and then transitions to change data capture (CDC) to continuously replicate ongoing changes."
"What does the AWS DMS setting 'Stop after applying changes' do?","The task will finish replication when it has caught up with the source database.","The task will stop the replication instance when the target database is online.","The task will ignore all subsequent data validation checks.","The task will automatically switch over to the target database","'Stop after applying changes' will make the task to finish replication when it has caught up with the source database."
"Which AWS service would you typically integrate with AWS DMS to provide a secure tunnel for data replication between an on-premises database and AWS?","AWS Site-to-Site VPN","AWS CloudFront","AWS Storage Gateway","AWS IAM","AWS Site-to-Site VPN provides a secure, encrypted tunnel for network traffic between your on-premises environment and your AWS VPC, which is essential for securely replicating data using DMS."
"How can you ensure high availability for your AWS DMS replication instance?","By enabling Multi-AZ deployment for the replication instance","By creating a read replica of the replication instance","By using AWS Auto Scaling for the replication instance","By creating a backup of the replication instance","Enabling Multi-AZ deployment provides redundancy by automatically provisioning a standby replication instance in a different Availability Zone."
"What is the purpose of the AWS DMS setting 'Control table settings'?","To specify the tables DMS uses to store metadata and progress information","To control the access permissions for the source and target databases","To configure data validation rules","To manage the replication instance size","The 'Control table settings' in AWS DMS specify the tables that DMS uses to store metadata and progress information during the migration."
"What is a common issue that can cause the DMS replication task to fail or slow down significantly?","Network latency between the replication instance and the source or target databases","Low CPU utilisation on the replication instance","Insufficient storage space on the source database","An overly complex data transformation","Network latency between the replication instance and the source or target databases is a common bottleneck that can significantly impact DMS performance."
"Which of the following database types is a good candidate for using AWS DMS to migrate to Amazon Aurora?","PostgreSQL","dBase","Paradox","Microsoft Access","PostgreSQL is one of the database types that can be migrated to Amazon Aurora."
"During a full load migration with AWS DMS, what action can you take to minimise the impact on the source database?","Reduce the number of parallel load threads","Increase the replication instance size","Increase the source database's memory","Increase the source database's CPU","Reducing the number of parallel load threads during the full load migration can minimise the impact on the source database."
"When configuring an AWS DMS task, what is the purpose of the 'Selection rules'?","To specify which tables and schemas to include or exclude from the migration","To define the data transformation rules","To configure the error handling policies","To set the replication instance size","Selection rules define which tables and schemas will be included or excluded from the migration task."
"What is the primary purpose of AWS Database Migration Service (DMS)?","To migrate databases to AWS quickly and securely","To optimise database queries","To monitor database performance","To create database backups","AWS DMS is designed to help you migrate databases to AWS easily and securely. It supports various database engines and migration scenarios."
"Which of the following migration types does AWS DMS support?","Homogeneous and Heterogeneous","Online only","Schema only","Application code only","AWS DMS supports both homogeneous migrations (same database engine) and heterogeneous migrations (different database engines)."
"Which of the following is NOT a required component for setting up an AWS DMS replication instance?","An Amazon EC2 instance","Source database","Target database","Replication subnet group","An Amazon EC2 instance is not a necessary component for setting up an AWS DMS replication instance."
"In AWS DMS, what is an endpoint?","A source or target database connection","A security group","A replication task","A log file","An endpoint in AWS DMS represents a source or target database connection that DMS can use to migrate data."
"What type of migration strategy in AWS DMS minimises downtime during the migration process?","Online migration","Offline migration","Schema migration","Data validation","An online migration strategy in AWS DMS minimises downtime by replicating data while the source database remains operational."
"Which task does an AWS DMS replication instance perform?","Reads data from the source, transforms it, and loads it to the target","Optimises SQL queries on the source database","Encrypts data at rest in the target database","Monitors the CPU utilisation of the source database","The replication instance is responsible for reading data from the source database, transforming it as needed, and then loading it into the target database."
"What is the purpose of AWS DMS Schema Conversion Tool (SCT)?","Convert database schemas for heterogeneous migrations","Optimise network bandwidth during migrations","Automate the deployment of database instances","Monitor database performance after migration","The Schema Conversion Tool helps convert database schemas from one database engine to another, which is crucial for heterogeneous migrations."
"Which type of database can be used as both source and target in an AWS DMS migration?","MySQL","Amazon S3","Amazon Redshift","Microsoft Excel","MySQL can be used as both a source and target database in an AWS DMS migration."
"Which of the following is a benefit of using AWS Database Migration Service (DMS) for database migrations?","Reduced migration time and cost","Automatic database backups","Real-time database monitoring","Simplified application deployment","AWS DMS helps reduce the time and cost associated with database migrations by automating many of the manual tasks involved."
"Which of the following tasks can be achieved using AWS DMS Replication Task settings?","Full Load and CDC (Change Data Capture)","Defining IAM Roles","Setting up VPC","Defining the retention policy","The Full Load and CDC (Change Data Capture) tasks define the scope of migration and are defined using Replication Task settings."
"What is the primary purpose of the AWS Database Migration Service (DMS)?","To migrate databases to AWS quickly and securely","To create database backups","To monitor database performance","To manage database security","AWS DMS's primary purpose is to enable you to migrate databases to AWS quickly and securely."
"Which of the following is a valid source database engine for an AWS DMS migration task?","Oracle","Amazon S3","Amazon SQS","AWS Lambda","Oracle is a supported source database engine in AWS DMS."
"What type of migration does AWS DMS support?","Homogeneous and heterogeneous migrations","Only homogeneous migrations","Only heterogeneous migrations","Only migrations to NoSQL databases","AWS DMS supports both homogeneous (same database engine) and heterogeneous (different database engine) migrations."
"Which component of AWS DMS is responsible for reading data from the source database, transforming it, and loading it into the target database?","Replication Instance","Endpoint","Schema Conversion Tool","Migration Task","The Replication Instance is the core component that performs the data extraction, transformation, and loading in DMS."
"What is the purpose of the AWS Schema Conversion Tool (SCT) in the context of database migration?","To convert database schema from one engine to another","To encrypt data during migration","To monitor the progress of the migration","To automatically scale the database instance","The Schema Conversion Tool assists in converting database schema from one database engine to another, simplifying heterogeneous migrations."
"Which of the following strategies is used by AWS DMS for migrating large tables with minimal downtime?","Change Data Capture (CDC)","Full Load","Data Compression","Automated Backups","Change Data Capture (CDC) allows DMS to capture changes to the source database and apply them to the target database in real-time, minimising downtime."
"What is an 'Endpoint' in the context of AWS DMS?","A connection to a source or target database","A security group","A set of IAM permissions","A CloudWatch alarm","An Endpoint in AWS DMS represents a connection to either the source or target database."
"During an AWS DMS migration, what does the term 'Task Settings' define?","How DMS migrates and replicates data","The size of the replication instance","The type of database being migrated","The geographical region for the migration","Task Settings in DMS define the rules and parameters for how DMS migrates and replicates data between the source and target databases."
"You are using AWS DMS to migrate a database and need to minimise the impact on your source database. Which setting should you configure?","Limit the number of tables migrated concurrently","Increase the size of the replication instance","Disable validation tasks","Enable logging to CloudWatch","Limiting the number of tables migrated concurrently will reduce the load on the source database during the migration process."
"What does AWS DMS use to track changes during a Change Data Capture (CDC) replication?","Database transaction logs","CloudWatch metrics","IAM roles","VPC settings","During CDC replication, DMS uses the database transaction logs to track changes made to the source database."
"What is the primary function of AWS Database Migration Service (DMS)?","Migrating databases to AWS","Monitoring database performance","Backing up databases","Managing database users","AWS DMS is primarily used to migrate databases from on-premises or other cloud environments to AWS."
"Which of the following migration scenarios is supported by AWS DMS?","Homogeneous migrations (e.g., Oracle to Oracle)","Application code migration","Operating system migration","Server hardware migration","AWS DMS supports homogeneous migrations, where the source and target databases are of the same type."
"What is an AWS DMS replication instance?","A compute instance that performs the data migration","A database instance used for storing metadata","A storage volume for temporary data","A network interface for connecting to the source database","The replication instance is a compute instance that performs the actual data migration from the source to the target."
"Which of the following is a task setting in AWS DMS?","Specifies the tables to be migrated","Defines the VPC security groups","Configures the IAM role","Sets the CloudWatch alarms","AWS DMS tasks define the details of the migration, including the tables to be migrated and the type of migration."
"What is the purpose of the AWS DMS Schema Conversion Tool (SCT)?","To convert database schema from one engine to another","To encrypt data in transit","To monitor database performance","To manage database users","The Schema Conversion Tool helps convert database schema from one database engine to another, which is essential for heterogeneous migrations."
"Which type of endpoint does AWS DMS use to connect to a database?","Source and target endpoints","Read-only endpoint","Write-only endpoint","Metadata endpoint","AWS DMS requires both source and target endpoints to establish a connection to the source and target databases."
"Which of the following AWS services can be used to monitor the progress and performance of AWS DMS tasks?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and logs to monitor the progress and performance of DMS tasks."
"What type of data migration can AWS DMS perform?","Full load and Change Data Capture (CDC)","Incremental backup","Full operating system migration","Virtual machine migration","AWS DMS can perform a full load migration to copy all data initially, followed by CDC to replicate ongoing changes."
"Which of the following is a benefit of using AWS DMS for database migration?","Minimises downtime during migration","Automatically optimises database queries","Provides automatic scaling for databases","Replaces the need for database administrators","AWS DMS minimises downtime by performing continuous replication during the migration process."
"In AWS DMS, what does a migration task status of 'Table completed' signify?","The table has been fully migrated and replication has started","The table has been skipped","The table has encountered an error","The table is ready for use in the source database","'Table completed' in AWS DMS signifies that the full load migration for a particular table is complete and replication of ongoing changes has begun."
"What is the primary purpose of AWS Database Migration Service (DMS)?","To migrate databases to AWS quickly and securely","To perform real-time analytics on database data","To manage database security policies","To monitor database performance in AWS","AWS DMS is specifically designed to migrate databases from on-premises or other cloud providers to AWS, with minimal downtime."
"Which of the following migration scenarios is supported by AWS Database Migration Service (DMS)?","Homogeneous database migrations (e.g., Oracle to Oracle)","Operating system upgrades","Application code refactoring","Network infrastructure upgrades","DMS excels at homogeneous migrations where the source and target database engines are the same, simplifying the migration process."
"In AWS DMS, what is an 'endpoint'?","A connection to a source or target database","A virtual machine hosting the migration process","A set of security credentials","A log file containing migration events","An endpoint in DMS represents a connection to either the source database from which data is being migrated or the target database to which data is being migrated."
"What is the purpose of a replication instance in AWS Database Migration Service (DMS)?","To host the DMS replication tasks and perform the data conversion","To store backup copies of the databases being migrated","To provide a web-based interface for monitoring the migration","To act as a firewall between source and target databases","The replication instance hosts the DMS replication tasks, performing the actual data conversion and movement between source and target endpoints."
"Which AWS service does AWS Database Migration Service (DMS) primarily integrate with for schema conversion when migrating to a different database engine?","AWS Schema Conversion Tool (SCT)","AWS CloudFormation","AWS Glue","AWS Data Pipeline","AWS SCT is specifically designed to help convert database schemas from one database engine to another, making it a crucial tool when performing heterogeneous database migrations with DMS."
"What is a change data capture (CDC) in the context of AWS Database Migration Service (DMS)?","A process of capturing and applying changes to the target database after the initial data load","A method of encrypting data in transit during migration","A tool for monitoring the progress of the migration","A technique for compressing data before migration","CDC in DMS allows for near real-time replication by capturing changes made to the source database and applying them to the target database after the initial data load has been completed."
"When using AWS Database Migration Service (DMS), what happens to the source database during the migration process?","The source database remains operational","The source database is shut down temporarily","The source database is automatically upgraded","The source database is converted to a read-only replica","DMS is designed to minimise downtime, allowing the source database to remain operational during the migration process, reducing impact on applications."
"What type of validation can be performed by AWS Database Migration Service (DMS) to ensure data integrity after migration?","Data validation tasks to compare data between source and target","Database schema validation","Network connectivity validation","Operating system compatibility validation","DMS offers data validation tasks that allow you to compare data between the source and target databases after migration, ensuring data integrity."
"Which of the following is a benefit of using AWS Database Migration Service (DMS) for database migrations?","Reduced downtime during migration","Automated application code updates","Simplified network configuration","Automated server provisioning","AWS DMS minimises downtime during database migrations, allowing applications to remain available throughout the process."
"Which is a strategy AWS DMS can use for migrating large tables?","Using parallel load to speed up the initial data transfer","Migrating the smallest tables first","Migrating the schema only","Skipping tables larger than 1 TB","AWS DMS can utilise a parallel load strategy to improve migration speed for large tables by using multiple threads to transfer the data simultaneously."