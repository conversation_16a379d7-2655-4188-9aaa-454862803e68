"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
What is the primary purpose of Amazon WorkSpaces Core?,To provide a simplified way to deploy and manage virtual desktops.,To provide a managed database service.,To provide a serverless compute platform.,To provide a content delivery network.,"WorkSpaces Core focuses on simplifying the deployment and management of virtual desktops by offering a flexible, customisable solution."
Which AWS service provides the foundational infrastructure for Amazon WorkSpaces Core?,Amazon EC2,Amazon S3,Amazon RDS,Amazon Lambda,"WorkSpaces Core leverages Amazon EC2 instances to run the virtual desktops, providing the necessary compute resources."
"With Amazon WorkSpaces Core, what level of control do you have over the image creation process?",Full control over the image build and customisation.,"Limited control, using pre-built AWS images only.","No control, AWS manages the images entirely.",Control only over the application installation within the image.,"WorkSpaces Core offers full control over the image creation process, allowing users to build custom images tailored to their specific needs."
Which of the following is a key benefit of using Amazon WorkSpaces Core over the standard Amazon WorkSpaces?,Greater customisation and control over the environment.,Lower cost for basic desktop configurations.,Built-in security features for compliance.,Simplified management of user profiles.,"WorkSpaces Core provides a higher degree of customisation and control, allowing for tailored solutions that fit specific organisational requirements."
How does Amazon WorkSpaces Core handle operating system licensing?,You are responsible for providing your own OS licenses.,AWS provides the OS licenses.,The licensing is included in the hourly usage fee.,Licensing is handled through a third-party provider.,"With WorkSpaces Core, you are responsible for providing and managing your own operating system licenses, offering flexibility in license management."
"When deploying Amazon WorkSpaces Core, what is the role of AWS Identity and Access Management (IAM)?",To control access to WorkSpaces Core resources and manage user permissions.,To manage the operating system licenses for the WorkSpaces.,To monitor the performance of the WorkSpaces.,To automatically scale the WorkSpaces infrastructure.,"IAM is crucial for controlling access to WorkSpaces Core resources and managing user permissions, ensuring secure and controlled access."
What type of storage is typically used for the root volume of an Amazon WorkSpaces Core instance?,Amazon EBS,Amazon S3,Amazon EFS,Instance Store,Amazon EBS (Elastic Block Storage) is commonly used for the root volume of WorkSpaces Core instances due to its persistence and performance characteristics.
What is a recommended approach for patching and updating Amazon WorkSpaces Core images?,Automate patching and updates using tools like AWS Systems Manager.,Manually patch and update each image individually.,Rely on AWS to automatically patch and update the images.,Disable automatic updates to maintain a stable environment.,Automating patching and updates using tools like AWS Systems Manager ensures consistency and reduces the manual effort involved in maintaining the images.
Which networking service is typically used to provide network connectivity for Amazon WorkSpaces Core?,Amazon VPC,Amazon CloudFront,Amazon Route 53,Amazon API Gateway,"Amazon VPC (Virtual Private Cloud) provides the necessary network infrastructure for WorkSpaces Core, allowing you to define and control your network environment."
"In Amazon WorkSpaces Core, what is the purpose of a 'golden image'?",A template image used to create multiple consistent WorkSpaces.,A backup image used for disaster recovery.,A performance benchmark image used for testing.,A security-hardened image used for compliance.,"A 'golden image' serves as a template, ensuring consistency and standardisation across multiple WorkSpaces deployments."
How can you optimise costs when using Amazon WorkSpaces Core?,By carefully managing image sizes and instance types.,By using reserved instances for WorkSpaces.,By disabling automatic backups.,"By using a single, large instance for all users.",Optimising image sizes and selecting appropriate instance types is crucial for cost-effectiveness in WorkSpaces Core.
Which of the following is a common use case for Amazon WorkSpaces Core?,Custom virtual desktops for developers and engineers.,Web hosting for static websites.,Big data analytics processing.,Real-time gaming servers.,WorkSpaces Core is often used for providing custom virtual desktops tailored to the specific needs of developers and engineers.
What tool can be used to manage and automate the deployment of applications within Amazon WorkSpaces Core?,AWS Systems Manager,Amazon CloudWatch,AWS CloudTrail,Amazon S3,AWS Systems Manager provides capabilities for automating application deployment and management within WorkSpaces Core environments.
How do you ensure data security within Amazon WorkSpaces Core?,By implementing encryption at rest and in transit.,By relying solely on AWS's default security measures.,By disabling network access to the WorkSpaces.,By using only open-source software on the WorkSpaces.,"Implementing encryption at rest (e.g., using EBS encryption) and in transit (e.g., using HTTPS) is essential for data security within WorkSpaces Core."
What is the role of directory services (like Active Directory) in Amazon WorkSpaces Core?,To manage user authentication and authorisation.,To manage the operating system licenses.,To manage the storage volumes.,To manage the network configuration.,"Directory services like Active Directory are used to manage user authentication and authorisation, providing a centralised way to control access to the WorkSpaces."
How can you monitor the performance of Amazon WorkSpaces Core instances?,Using Amazon CloudWatch metrics and dashboards.,Using Amazon Inspector vulnerability scans.,Using AWS Trusted Advisor cost optimisation checks.,Using Amazon Macie data discovery tools.,"Amazon CloudWatch provides metrics and dashboards for monitoring the performance of WorkSpaces Core instances, allowing you to identify and address performance issues."
What is the difference between persistent and non-persistent Amazon WorkSpaces Core deployments?,"Persistent WorkSpaces retain user data between sessions, while non-persistent WorkSpaces do not.",Persistent WorkSpaces are more expensive than non-persistent WorkSpaces.,Persistent WorkSpaces are faster than non-persistent WorkSpaces.,Persistent WorkSpaces are easier to manage than non-persistent WorkSpaces.,"Persistent WorkSpaces retain user data and settings between sessions, while non-persistent WorkSpaces reset to a clean state each time."
Which of the following is a key consideration when choosing an instance type for Amazon WorkSpaces Core?,The workload requirements of the applications being used.,The cost of the operating system license.,The availability of the instance type in different regions.,The network bandwidth available to the instance type.,"Selecting an appropriate instance type should be based on the workload requirements of the applications, ensuring sufficient resources are available."
How does Amazon WorkSpaces Core integrate with other AWS services?,"It integrates with services like Systems Manager, CloudWatch, and IAM.",It does not integrate with other AWS services.,It only integrates with services related to storage.,It only integrates with services related to networking.,"WorkSpaces Core integrates with a variety of AWS services, including Systems Manager for automation, CloudWatch for monitoring, and IAM for access control."
What type of virtualisation technology is used by Amazon WorkSpaces Core?,It uses a custom virtualisation solution based on Amazon EC2.,VMware vSphere,Microsoft Hyper-V,Citrix XenServer,"Amazon WorkSpaces Core uses a custom virtualisation solution built on top of Amazon EC2, providing a flexible and scalable platform."
Which of the following is a recommended practice for securing Amazon WorkSpaces Core?,Implement multi-factor authentication (MFA).,Disable all network access to the WorkSpaces.,Use only the default AWS security settings.,Allow all users to have administrative privileges.,"Implementing MFA adds an extra layer of security, protecting against unauthorised access to the WorkSpaces."
How can you automate the creation of Amazon WorkSpaces Core instances?,Using AWS CloudFormation or similar infrastructure-as-code tools.,Manually creating each instance through the AWS Management Console.,Using a third-party desktop virtualisation solution.,Using a script that runs directly on Amazon EC2.,"AWS CloudFormation (or similar IaC tools) allows you to automate the creation and configuration of WorkSpaces Core instances, ensuring consistency and repeatability."
What is the role of security groups in Amazon WorkSpaces Core?,To control network traffic to and from the WorkSpaces instances.,To manage user authentication and authorisation.,To encrypt data at rest on the WorkSpaces instances.,To monitor the performance of the WorkSpaces instances.,"Security groups act as virtual firewalls, controlling network traffic to and from the WorkSpaces instances."
How can you ensure high availability for Amazon WorkSpaces Core?,By deploying WorkSpaces across multiple Availability Zones.,"By using a single, large WorkSpaces instance.",By disabling automatic backups.,By manually restarting failed WorkSpaces instances.,Deploying WorkSpaces across multiple Availability Zones ensures that your virtual desktops remain available even if one Availability Zone experiences an outage.
Which of the following is a benefit of using Amazon WorkSpaces Core for remote workers?,Provides secure access to corporate resources from anywhere.,Requires workers to be physically located in the office.,Increases the risk of data breaches.,Reduces the flexibility of the work environment.,WorkSpaces Core provides a secure and flexible way for remote workers to access corporate resources from any location.
What is the impact of using Amazon WorkSpaces Core on endpoint device management?,It simplifies endpoint management by centralising the desktop environment.,It increases the complexity of endpoint management.,It has no impact on endpoint management.,It requires users to manage their own endpoint devices.,"WorkSpaces Core simplifies endpoint management by centralising the desktop environment, reducing the need to manage individual devices."
How can you control the software installed on Amazon WorkSpaces Core instances?,By using a custom golden image with pre-installed software.,By allowing users to install any software they choose.,By relying on AWS to automatically install software.,By disabling software installation entirely.,Creating a custom golden image with pre-installed software gives you control over the applications available on the WorkSpaces instances.
What is the relationship between Amazon WorkSpaces Core and virtual desktop infrastructure (VDI)?,WorkSpaces Core provides a flexible and customisable VDI solution.,WorkSpaces Core is not related to VDI.,WorkSpaces Core is a competitor to VDI solutions.,WorkSpaces Core is a deprecated version of VDI.,"WorkSpaces Core offers a flexible and customisable VDI (Virtual Desktop Infrastructure) solution, allowing you to build virtual desktops tailored to your needs."
How does Amazon WorkSpaces Core handle scalability?,By allowing you to easily provision and deprovision WorkSpaces instances as needed.,By automatically scaling the storage capacity of the WorkSpaces instances.,By requiring you to manually scale the WorkSpaces infrastructure.,By limiting the number of WorkSpaces instances that can be deployed.,"WorkSpaces Core allows you to easily provision and deprovision WorkSpaces instances, making it simple to scale your virtual desktop environment."
What is a best practice for managing user profiles in Amazon WorkSpaces Core?,Use a profile management solution to roam user profiles between sessions.,Store user profiles locally on the WorkSpaces instances.,Disable user profiles entirely.,Require users to manually back up their profiles.,"Using a profile management solution ensures that user data and settings are preserved between sessions, providing a consistent user experience."
Which of the following is a common integration for Amazon WorkSpaces Core?,Integration with Microsoft Teams for collaboration.,Integration with Amazon Alexa for voice control.,Integration with AWS IoT for device management.,Integration with Amazon Rekognition for image analysis.,WorkSpaces Core often integrates with Microsoft Teams (or similar collaboration tools) for improved communication and collaboration among users.
How can you monitor the security posture of Amazon WorkSpaces Core?,Using AWS Security Hub and Amazon GuardDuty.,Using Amazon CloudWatch metrics and dashboards.,Using AWS Trusted Advisor cost optimisation checks.,Using Amazon Macie data discovery tools.,"AWS Security Hub and Amazon GuardDuty can be used to monitor the security posture of WorkSpaces Core environments, detecting potential threats and vulnerabilities."
What type of applications are best suited for Amazon WorkSpaces Core?,Applications with high performance requirements and specific customisation needs.,Applications that are only available as web applications.,Applications that require minimal resources and can run in a browser.,Applications that are designed for mobile devices.,"WorkSpaces Core is well-suited for applications with high performance demands and specific customisation needs, providing a flexible and powerful virtual desktop environment."
Which of the following is a potential drawback of using Amazon WorkSpaces Core?,Requires more technical expertise to set up and manage compared to standard WorkSpaces.,Offers less customisation options than standard WorkSpaces.,Is more expensive than standard WorkSpaces for basic configurations.,Is less secure than standard WorkSpaces.,WorkSpaces Core requires more technical expertise to set up and manage compared to standard WorkSpaces due to the increased level of customisation.
How can you optimise the performance of Amazon WorkSpaces Core instances?,By using SSD storage and optimising the operating system.,"By using a single, large instance for all users.",By disabling automatic backups.,By using a slow network connection.,"Using SSD storage and optimising the operating system (e.g., disabling unnecessary services) can improve the performance of WorkSpaces Core instances."
What is the role of the WorkSpaces Application Manager (WAM) in the context of WorkSpaces Core?,WAM is NOT supported in WorkSpaces Core.,To manage the deployment and licensing of applications.,To monitor the performance of applications.,To provide a centralised repository for applications.,"WAM is NOT supported in WorkSpaces Core, as WorkSpaces Core allows for greater customisation which requires a different approach to application management."
"When deploying Amazon WorkSpaces Core, how do you manage access to specific applications?",You manage application access through the custom image and Active Directory group policies.,You manage application access through AWS IAM roles.,You manage application access through Amazon S3 bucket policies.,You manage application access directly on the individual WorkSpaces instances.,"Application access control is typically managed through the custom image creation process and Active Directory group policies, allowing you to define which users have access to specific applications."
What is the recommended way to handle persistent data in a non-persistent Amazon WorkSpaces Core environment?,Redirect user profiles and data to a network share or cloud storage.,Store data locally on the WorkSpaces instance.,Disable data persistence entirely.,Require users to manually back up their data.,Redirecting user profiles and data to a network share or cloud storage ensures that data is preserved even in a non-persistent environment.
"In the context of Amazon WorkSpaces Core, what is the advantage of using a Windows Server operating system over a Linux operating system?",Windows Server allows for easier integration with Active Directory and Windows applications.,Linux offers better performance for virtual desktop workloads.,Linux is less expensive than Windows Server.,Windows Server requires less management overhead.,"Windows Server often provides easier integration with Active Directory and compatibility with Windows-based applications, which is a common requirement for many organisations."
Which of the following methods can be used to deploy software to Amazon WorkSpaces Core instances after they are created?,"Using AWS Systems Manager Automation, Group Policy Objects (GPOs), or scripting.",Using the Amazon WorkSpaces client application.,Using the AWS CloudFormation console.,Using Amazon S3 event triggers.,"Software can be deployed post-creation using AWS Systems Manager Automation, Group Policy Objects (GPOs) (for Windows environments), or custom scripting solutions."
What is the purpose of using snapshots with Amazon WorkSpaces Core?,To create backups of the golden image and individual WorkSpaces instances.,To monitor the performance of the WorkSpaces instances.,To manage user authentication and authorisation.,To optimise the cost of the WorkSpaces instances.,"Snapshots allow you to create backups of the golden image and individual WorkSpaces instances, providing a mechanism for disaster recovery and rollback."
How can you ensure that your Amazon WorkSpaces Core deployment is compliant with industry regulations?,By implementing security controls and monitoring the environment using AWS services.,By relying solely on AWS's default compliance certifications.,By disabling all network access to the WorkSpaces.,By using only open-source software on the WorkSpaces.,"Implementing specific security controls (e.g., encryption, access controls, logging) and monitoring the environment with AWS services like Security Hub and CloudTrail are crucial for meeting compliance requirements."
What is the best way to provide a consistent user experience across different Amazon WorkSpaces Core instances?,Using a centralised profile management solution and consistent golden images.,Allowing users to customise their WorkSpaces independently.,Disabling user customisation entirely.,Requiring users to use the same physical endpoint device.,"A centralised profile management solution ensures that user settings and data are consistent across different WorkSpaces instances, while consistent golden images provide a standardised base environment."
Which AWS service is commonly used for logging and auditing activities within Amazon WorkSpaces Core?,AWS CloudTrail,Amazon CloudWatch,AWS Config,Amazon Inspector,"AWS CloudTrail records API calls made to AWS services, providing a comprehensive audit trail of activities within your WorkSpaces Core environment."
You are experiencing slow network performance on your Amazon WorkSpaces Core instances. What is the first thing you should check?,The network configuration of your VPC and the network interfaces of the WorkSpaces instances.,The CPU utilisation of the WorkSpaces instances.,The storage capacity of the WorkSpaces instances.,The user's internet connection.,"Checking the VPC configuration, security group rules, and network interfaces of the WorkSpaces instances is essential to diagnose network performance issues."
How does Amazon WorkSpaces Core help in a Bring Your Own Device (BYOD) environment?,It allows users to securely access corporate resources from their personal devices without compromising security.,It requires users to use company-provided devices.,It prohibits users from accessing corporate resources from personal devices.,It has no impact on the BYOD environment.,"WorkSpaces Core enables secure access to corporate resources from personal devices, without exposing sensitive data directly on the device itself."
"In Amazon WorkSpaces Core, how can you control which regions users can access their virtual desktops from?",Using IAM policies and network access controls.,Using AWS Organizations service control policies (SCPs).,Using Amazon Route 53 geoproximity routing.,Using Amazon CloudFront geo restrictions.,IAM policies and network access controls (such as security groups and network ACLs) can be used to restrict access to WorkSpaces Core resources based on the user's location.
What is the maximum storage capacity for the root volume (C: drive) in Amazon WorkSpaces Core?,"It depends on the chosen Amazon EBS volume size, which can be up to 16 TB.",50 GB,100 GB,200 GB,"The maximum storage capacity for the root volume in WorkSpaces Core depends on the size of the EBS volume selected, which can be up to 16 TB."
How do you ensure that your Amazon WorkSpaces Core instances are automatically restarted in case of a failure?,Configure auto-recovery using Amazon CloudWatch alarms.,Rely on AWS to automatically restart the instances.,Manually restart the instances after a failure.,Disable automatic restarts to prevent data loss.,Auto-recovery can be configured using Amazon CloudWatch alarms to automatically restart WorkSpaces Core instances in case of a failure.
"When using Amazon WorkSpaces Core, how can you implement a least privilege access model for administrators?",By using granular IAM permissions and restricting access to specific resources.,By giving all administrators full access to the WorkSpaces environment.,By using a single administrator account for all tasks.,By disabling administrative access entirely.,"Granular IAM permissions allow you to define specific roles and permissions for administrators, ensuring that they only have access to the resources they need to perform their tasks."
What is the primary purpose of Amazon WorkSpaces Core?,"To provide a simplified, API-first approach to managing virtual desktops.",To manage on-premises physical desktops.,To provide a fully managed virtual desktop service with pre-configured bundles.,To manage mobile devices.,WorkSpaces Core allows developers to integrate virtual desktop infrastructure (VDI) capabilities into their own custom solutions through APIs.
Which AWS service is commonly used to handle user authentication and authorisation when using Amazon WorkSpaces Core?,AWS Identity and Access Management (IAM),Amazon Simple Queue Service (SQS),Amazon CloudWatch,Amazon DynamoDB,"IAM is used to manage access to AWS resources, including WorkSpaces Core, by defining users, groups, and roles."
"With Amazon WorkSpaces Core, which operating systems can be used for desktops?",Windows and Linux,macOS only,ChromeOS only,Android only,"WorkSpaces Core supports Windows and Linux operating systems, giving organisations the flexibility to choose the best option for their needs."
"When using Amazon WorkSpaces Core, who is responsible for managing the underlying infrastructure and virtual machine instances?",The customer is responsible.,AWS automatically manages the infrastructure.,A third-party vendor is responsible.,The operating system vendor is responsible.,The customer is responsible for provisioning and managing the virtual machine instances used with WorkSpaces Core.
Which network type is compatible with Amazon Workspaces Core?,Virtual Private Cloud (VPC),Content Delivery Network (CDN),Global Accelerator,Storage Area Network (SAN),"WorkSpaces Core resources are deployed within a VPC, providing a secure and isolated network environment."
What is a key advantage of using Amazon WorkSpaces Core over traditional on-premises VDI solutions?,Reduced operational overhead due to AWS managing the underlying infrastructure.,Lower initial capital expenditure (CAPEX).,Unlimited scalability for desktop resources.,Faster deployment of virtual desktops.,"By leveraging AWS, WorkSpaces Core significantly reduces the operational burden of managing and maintaining the underlying infrastructure."
Which billing model applies to Amazon WorkSpaces Core?,Pay-as-you-go based on resource consumption.,Fixed monthly fee per user.,Upfront payment for a set number of desktops.,Free of charge as part of AWS Free Tier.,"WorkSpaces Core uses a pay-as-you-go model, allowing customers to only pay for the resources they consume."
What type of security infrastructure is typically used alongside Amazon WorkSpaces Core to secure data in transit and at rest?,Encryption and secure network configurations.,Physical security measures.,Multi-factor authentication on physical devices,Voice recognition,"Encryption, both in transit and at rest, along with secure network configurations are standard practice for securing data within a WorkSpaces Core environment."
Which of the following is NOT a use case for Amazon WorkSpaces Core?,Managed desktop-as-a-service,Custom virtual application streaming.,Integration with existing VDI environments.,Delivering secure remote access to applications.,Amazon WorkSpaces Core is NOT a managed desktop-as-a-service. That would be Amazon WorkSpaces.
Which API service does Amazon Workspaces Core make extensive use of?,Compute and Networking APIs,Database and Storage APIs,Security and Identity APIs,IoT and Mobile APIs,Amazon Workspaces Core gives customers access to underlying Compute and Networking APIs.
What is the primary use case for Amazon WorkSpaces Core?,Providing a customisable and flexible virtual desktop solution.,Replacing traditional on-premises desktops entirely.,Providing a fully managed desktop-as-a-service solution.,Creating a secure environment for web browsing only.,WorkSpaces Core is designed to offer a more customisable and flexible virtual desktop solution compared to the fully managed service of regular WorkSpaces.
Which of the following best describes the infrastructure management responsibilities when using Amazon WorkSpaces Core?,"Customers manage the desktop images, application deployment, and infrastructure.",AWS manages all aspects of the infrastructure.,"AWS manages the underlying hardware, while customers manage the operating system.",Customers are responsible for the network configuration only.,"With WorkSpaces Core, the customer assumes responsibility for managing desktop images, application deployment, and underlying infrastructure components."
What does BYOL stand for in the context of Amazon WorkSpaces Core licensing?,Bring Your Own Licence,Build Your Own Linux,Buy Your Own Laptop,Back Your Own Load,BYOL means customers can utilise their existing Windows Server licences with WorkSpaces Core.
What is the main difference between Amazon WorkSpaces and Amazon WorkSpaces Core regarding image management?,"WorkSpaces Core requires customers to fully manage their own images, whereas WorkSpaces provides pre-built options.","WorkSpaces Core doesn't support custom images, while WorkSpaces does.","WorkSpaces allows only Linux-based images, while WorkSpaces Core allows only Windows-based images.",There is no difference in image management between the two.,"WorkSpaces provides pre-built images and managed image updates, while WorkSpaces Core requires customers to manage their own images."
Which component is mandatory to manage network access when deploying Amazon WorkSpaces Core?,Virtual Private Cloud (VPC),Simple AD,AWS Directory Service,Amazon Route 53,A VPC is mandatory for deploying WorkSpaces Core since you need a virtual private cloud to deploy WorkSpaces in.
Which of the following is a supported operating system for custom images used with Amazon WorkSpaces Core?,Windows Server,macOS,ChromeOS,Android,WorkSpaces Core currently supports only Windows Server for creating custom images.
What is a key benefit of using Amazon WorkSpaces Core over traditional on-premises desktop infrastructure?,Reduced operational overhead through AWS managed services.,Lower initial hardware costs.,Elimination of all security patching requirements.,Guaranteed higher performance than physical desktops.,"WorkSpaces Core reduces operational overhead by offloading infrastructure management to AWS, whilst still offering flexibility in customisation."
How does licensing work when using 'Bring Your Own Licence' (BYOL) with Amazon WorkSpaces Core?,You utilise existing Microsoft Windows Server licences.,You receive free Windows Server licences from AWS.,You use licences from your existing Linux distributions.,You must purchase new licences through the AWS Marketplace.,"With the BYOL model, you utilise existing Windows Server licences that you already own, helping to reduce costs."
"In Amazon WorkSpaces Core, what tool/service would you use to deploy and manage applications on your WorkSpaces?",AWS Systems Manager,Amazon AppStream 2.0,AWS CodeDeploy,AWS Config,AWS Systems Manager can be used to automate application deployment and configuration on your WorkSpaces Core instances.
Which of the following is a primary factor when deciding between Amazon WorkSpaces and Amazon WorkSpaces Core?,The level of customisation and control required.,The cost of storage only.,The preferred geographic region.,The number of users only.,The decision between WorkSpaces and WorkSpaces Core hinges largely on the level of customisation and control desired over the environment.