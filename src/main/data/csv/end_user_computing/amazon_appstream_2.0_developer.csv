question,correct_answer,wrong_answer1,wrong_answer2,wrong_answer3,rationale
What is the primary function of Amazon AppStream 2.0?,Streaming desktop applications to users' devices,Hosting static websites,Managing container deployments,Storing large data files,"AppStream 2.0 allows you to stream desktop applications from AWS to any device with a supported web browser, providing a consistent user experience."
Which AWS service is commonly used to manage user authentication and authorisation for AppStream 2.0?,AWS IAM Identity Center (Successor to AWS Single Sign-On),Amazon Cognito,AWS Directory Service,AWS Shield,IAM Identity Center is often used to provide centralised access management and single sign-on (SSO) for users accessing AppStream 2.0 applications.
What is an AppStream 2.0 Image Builder primarily used for?,Creating and configuring application images,Monitoring user activity,Managing user profiles,Deploying applications directly to user devices,The Image Builder is used to create custom application images that are then streamed to users via AppStream 2.0.
What is the purpose of an AppStream 2.0 Fleet?,To manage the streaming instances that run the applications,To define user permissions,To store application data,To configure network settings,An AppStream 2.0 Fleet consists of the streaming instances that run the applications and are responsible for providing the streaming experience to users.
What are AppStream 2.0 stacks used for?,"To group and manage fleets, images, and user access policies",To store user data,To define application dependencies,To configure security groups,"AppStream 2.0 stacks are used to organise and manage the different components of an AppStream 2.0 environment, including fleets, images, and user access policies."
Which instance types are best suited for graphics-intensive applications in AppStream 2.0?,"Graphics optimised instances (e.g., g4dn, g5)","Memory optimised instances (e.g., r5)","Compute optimised instances (e.g., c5)","General purpose instances (e.g., m5)","Graphics-optimised instances, such as g4dn and g5, are designed for applications that require high-performance graphics processing, making them ideal for AppStream 2.0 workloads that involve such applications."
What is the purpose of Elastic fleets in AppStream 2.0?,To automatically scale the number of streaming instances based on demand,To provide static IP addresses to streaming instances,To enable offline access to applications,To encrypt data at rest,"Elastic fleets in AppStream 2.0 automatically adjust the number of streaming instances based on user demand, ensuring optimal performance and cost efficiency."
Which of the following client types can be used to access applications streamed through AppStream 2.0?,Web browser,Thick client application,Virtual machine,Mobile application,"AppStream 2.0 is designed to be accessed through a web browser, eliminating the need for client-side installations."
Which AWS service can be integrated with AppStream 2.0 to provide persistent storage for user data?,Amazon S3,Amazon EBS,Amazon Glacier,Amazon EC2 Instance Store,"Amazon S3 can be used with AppStream 2.0 to provide persistent storage for user data, ensuring that user files are preserved between streaming sessions."
What is the role of the AppStream 2.0 User Pool?,To manage user accounts and authentication within AppStream 2.0,To store application binaries,To configure network settings,To monitor fleet health,"The AppStream 2.0 User Pool is responsible for managing user accounts and authentication, providing a secure and controlled access environment for users."
What is the function of the 'Application Settings Persistence' feature in AppStream 2.0?,Saving and restoring application settings between user sessions,Backing up application data to S3,Automatically updating applications,Encrypting application data at rest,"Application Settings Persistence allows you to save and restore application settings for each user, providing a personalised experience across different streaming sessions."
"When setting up an AppStream 2.0 environment, what is the significance of choosing the correct VPC?",It defines the network environment for the streaming instances,It determines the geographic region for the service,It manages user authentication,It stores application images,"The VPC defines the network environment where the AppStream 2.0 streaming instances will be launched, including subnets, security groups, and network access control lists (ACLs)."
Which of the following is NOT a supported authentication method for AppStream 2.0 user access?,Certificate-based authentication,Active Directory,SAML 2.0,AppStream 2.0 Managed User,"AppStream 2.0 supports Active Directory, SAML 2.0 federation, and AppStream 2.0 Managed User pools, but not certificate-based authentication directly."
What is the purpose of the 'Disconnect Timeout' setting in AppStream 2.0?,To specify the amount of time a streaming session can remain disconnected before it's terminated,To set the maximum streaming session duration,To configure the idle timeout for the session,To define the time after which a user is automatically logged out,"The 'Disconnect Timeout' setting specifies how long a streaming session can remain disconnected before it's automatically terminated, freeing up resources."
How does AppStream 2.0 contribute to improved security compared to traditional desktop application deployments?,"Applications are streamed, not installed locally, reducing the attack surface",It automatically encrypts all data at rest,It replaces the need for antivirus software,It eliminates the need for operating system patching,"Since applications are streamed and not installed locally, the attack surface on user devices is reduced, improving security."
What is the purpose of the AppStream 2.0 'Idle Disconnect Timeout' setting?,To automatically disconnect idle streaming sessions after a specified time,To limit the overall duration of a streaming session,To disconnect sessions when network latency is high,To prevent users from launching multiple streaming sessions,"The 'Idle Disconnect Timeout' automatically disconnects streaming sessions that have been idle for a specified period, releasing resources and preventing unnecessary costs."
Which AWS service can be used to create and manage Active Directory for AppStream 2.0?,AWS Directory Service,AWS IAM Identity Center (Successor to AWS Single Sign-On),AWS Cloud Directory,Amazon Cognito,AWS Directory Service provides managed Active Directory services that can be integrated with AppStream 2.0 for user authentication and authorisation.
"When using AppStream 2.0, where are the applications actually executed?",On AWS streaming instances,On the user's local device,In a container on the user's device,On a separate virtual machine managed by the user,"Applications are executed on the AWS streaming instances within the AppStream 2.0 environment, and only the visual output is streamed to the user's device."
What is the maximum streaming session duration for AppStream 2.0?,"Configurable, up to a maximum duration",1 hour,8 hours,24 hours,The maximum streaming session duration for AppStream 2.0 is configurable and can be adjusted based on your needs.
Which of the following is a key benefit of using AppStream 2.0 over traditional application deployments?,Centralised application management and reduced IT overhead,Offline access to applications,Increased local processing power,Automatic data backups to user devices,"AppStream 2.0 provides centralised application management, reduces IT overhead, and simplifies application deployment and maintenance."
What is the recommended method for providing users access to their network file shares when using AppStream 2.0?,Integrating with Active Directory and using network file shares,Copying files directly to the streaming instances,Requiring users to upload files to Amazon S3,Using a third-party file sharing service,Integrating with Active Directory and using network file shares allows users to access their existing file shares seamlessly within the AppStream 2.0 environment.
What is the purpose of the 'Always On' fleet type in AppStream 2.0?,To keep streaming instances running even when no users are connected,To automatically update applications,To provide offline access,To automatically scale down to zero instances when not in use,"'Always On' fleets keep streaming instances running continuously, ensuring that applications are immediately available to users without any startup delay."
How does AppStream 2.0 help to protect sensitive data?,Data remains on the AWS streaming instances and is not stored on user devices,It automatically encrypts all data on user devices,It prevents users from taking screenshots,It requires multi-factor authentication for all users,"Since data remains on the AWS streaming instances and is not stored on user devices, AppStream 2.0 helps to protect sensitive data from loss or theft."
What is the AppStream 2.0 'Persistent Storage' feature used for?,Saving user data and application settings between sessions,Backing up application images,Storing application installation files,Configuring network settings,"The 'Persistent Storage' feature allows users to save their data and application settings, ensuring that they are available in future streaming sessions."
Which of the following is a cost optimisation strategy for AppStream 2.0?,Using Elastic fleets to scale instances based on demand,Using 'Always On' fleets for all users,Over-provisioning streaming instances,Disabling application settings persistence,Using Elastic fleets to automatically scale the number of instances based on user demand can significantly reduce costs compared to 'Always On' fleets.
What type of applications are best suited for streaming via AppStream 2.0?,Desktop applications with high graphics requirements,Mobile applications,Web applications,Server-side applications,"AppStream 2.0 is particularly well-suited for streaming desktop applications, especially those with high graphics requirements."
Which protocol is primarily used for streaming applications in AppStream 2.0?,Proprietary streaming protocol,RDP,VNC,HTTP,AppStream 2.0 uses a proprietary streaming protocol optimized for low latency and high performance.
What is the purpose of creating a new 'Image' from an Image Builder instance in AppStream 2.0?,To capture the installed applications and configurations for deployment to fleets,To create a backup of the Image Builder instance,To automatically update applications,To change the operating system of the Image Builder instance,"Creating a new 'Image' from an Image Builder captures the installed applications and configurations, allowing you to deploy them to your AppStream 2.0 fleets."
You are experiencing slow application performance within AppStream 2.0. What should you check first?,The CloudWatch metrics for the instance fleet,The user's internet connection speed,The application's resource usage,The security group rules,"Checking the CloudWatch metrics for the instance fleet can help identify resource constraints, such as CPU or memory bottlenecks, that may be impacting performance."
Which of the following is a key consideration when selecting an instance type for your AppStream 2.0 fleet?,"The application's resource requirements (CPU, memory, GPU)",The size of the user's home directory,The number of users accessing the fleet,The geographic region of the users,Selecting an instance type that meets the application's resource requirements is crucial for ensuring good performance and a positive user experience.
How can you ensure that users always have the latest version of an application in AppStream 2.0?,By updating the application in the Image Builder and creating a new image,By automatically updating the application on each streaming instance,By pushing updates to the user's local device,By requiring users to manually update the application,The recommended approach is to update the application in the Image Builder and then create a new image that is deployed to your AppStream 2.0 fleets.
What is the role of the 'Home Folders' feature in AppStream 2.0?,To provide persistent storage for user documents and data,To store application installation files,To manage user profiles,To configure network settings,"The 'Home Folders' feature provides persistent storage for user documents and data, ensuring that users can access their files across different streaming sessions."
Which of the following is a valid way to integrate AppStream 2.0 with an existing Active Directory domain?,Using AWS Directory Service to create a trust relationship with your existing domain,Creating a new Active Directory domain within AppStream 2.0,Manually adding users to the AppStream 2.0 user pool,Using a VPN connection to connect to the Active Directory domain,"AWS Directory Service allows you to create a trust relationship between your existing Active Directory domain and the AppStream 2.0 environment, enabling seamless user authentication and authorisation."
What is the purpose of the 'Session Context Persistence' feature in AppStream 2.0?,To save user sessions and resume them later,To back up application settings,To encrypt data at rest,To monitor user activity,"'Session Context Persistence' saves the state of user sessions, allowing users to resume their work where they left off, even after a disconnect."
When should you consider using a 'Dedicated' fleet type in AppStream 2.0?,When you need guaranteed instance capacity and consistent performance,When you want to minimise costs,When you need to support a large number of concurrent users,When you want to use the latest instance types,"'Dedicated' fleets provide guaranteed instance capacity and consistent performance, making them suitable for applications with stringent performance requirements."
How can you monitor the performance and health of your AppStream 2.0 environment?,Using Amazon CloudWatch metrics and alarms,Using AWS CloudTrail logs,Using the AppStream 2.0 console,Using a third-party monitoring tool,"Amazon CloudWatch provides detailed metrics and alarms for AppStream 2.0, allowing you to monitor the performance and health of your environment."
Which AppStream 2.0 feature allows you to control the applications that users can access?,Application entitlements,Fleet policies,User permissions,Security groups,"Application entitlements allow you to specify which applications are available to specific users or groups, providing granular control over application access."
What is the recommended way to handle application licensing when using AppStream 2.0?,Using a network license server or cloud-based licensing solution,Embedding licenses directly into the application image,Distributing licenses to users manually,Ignoring licensing requirements,Using a network license server or cloud-based licensing solution allows you to manage application licenses centrally and ensure compliance.
"When configuring an AppStream 2.0 stack, what is the purpose of the 'Storage Connectors'?",To allow users to access their files stored in cloud storage services like OneDrive or Google Drive,To configure network settings,To manage user profiles,To store application images,"Storage Connectors enable users to access their files stored in cloud storage services, providing a seamless experience for accessing their data."
What is a benefit of using the 'Non-Persistent' user setting with AppStream 2.0?,"It ensures that no user data is saved, maintaining a clean environment for each session",It automatically backs up user data,It allows users to access their data offline,It provides faster streaming performance,"The 'Non-Persistent' user setting ensures that no user data is saved between sessions, creating a clean and consistent environment for each user."
What is the difference between an 'Elastic' fleet and an 'Always-On' fleet in AppStream 2.0?,"Elastic fleets scale up and down based on demand, while Always-On fleets maintain a fixed number of instances",Elastic fleets are cheaper than Always-On fleets,Elastic fleets provide better performance than Always-On fleets,Elastic fleets are only suitable for small deployments,"Elastic fleets automatically scale based on demand, optimising costs, while Always-On fleets maintain a fixed number of instances, providing consistent availability."
Which of the following is a valid use case for AppStream 2.0?,Providing access to CAD/CAM software to remote engineers,Hosting a public-facing website,Running batch processing jobs,Storing large datasets,AppStream 2.0 is well-suited for providing remote access to graphics-intensive applications like CAD/CAM software.
Which feature in AppStream 2.0 allows you to control the USB redirection?,Streaming Protocol policies,Image builder settings,Fleet configuration,Stack settings,"Streaming Protocol policies allow you to allow or deny USB redirection, allowing a better control of USB devices connected to client machines."
How do you control where an AppStream 2.0 user can cut/copy and paste between the streaming app and their local device?,Clipboard policies,Stack settings,Fleet settings,Image builder settings,Clipboard policies configure the direction of copy and paste allowed from local to streaming and vice versa.
What is the benefit of integrating Amazon FSx with AppStream 2.0?,Providing a persistent file system for users,Reducing costs,Increasing the security,Providing an authentication mechanism,"Amazon FSx provides a fully managed, highly performant, and scalable file system that can be used as a persistent shared file system for AppStream 2.0 users."
Which parameter in AppStream 2.0 helps to mitigate the 'Blast Radius' of an attack or problem?,IsolateSession,Fleet type,Image Version,User Type,The IsolateSession setting in a fleet provides a stronger level of user isolation and prevents users from being able to circumvent operating system file permissions. This feature can help mitigate the \blast radius\' when one user session is impacted by an attack or problem.'
What is the primary function of Amazon AppStream 2.0?,To stream desktop applications to users' devices,To host static websites,To manage container deployments,To provide serverless computing resources,"AppStream 2.0 streams desktop applications from AWS to users on any device, providing access to applications without needing local installation."
Which AWS service is commonly used to manage user identities and access to Amazon AppStream 2.0?,AWS IAM Identity Center (Successor to AWS Single Sign-On),Amazon Cognito,AWS Directory Service,AWS Organizations,AWS IAM Identity Center is the recommended service to manage user identities and secure access to AppStream 2.0.
What is an image in the context of Amazon AppStream 2.0?,"A template containing the operating system, applications, and settings for streaming",A snapshot of a user's session,A file containing user data,A configuration file for the AppStream 2.0 environment,"An image in AppStream 2.0 is a template that defines the OS, applications, and settings for streaming instances."
"In Amazon AppStream 2.0, what does a 'stack' represent?",A collection of resources needed to provision and manage application streaming,A single application available for streaming,A user's profile and settings,A security group for controlling network access,A stack is a collection of AWS resources required to provision and manage the streaming of applications to users.
What is the purpose of Elastic fleets in Amazon AppStream 2.0?,To automatically scale the number of streaming instances based on demand,To provide a static number of streaming instances,To manage user profiles,To define network configurations,"Elastic fleets in AppStream 2.0 automatically scale the number of streaming instances to match user demand, optimising cost and performance."
Which client types can access applications streamed through Amazon AppStream 2.0?,HTML5 web browsers and the AppStream 2.0 client,Only the AppStream 2.0 client,Only desktop applications,Only mobile applications,"Users can access AppStream 2.0 applications through either a HTML5 web browser or the AppStream 2.0 client, offering flexibility in accessing streamed applications."
What is the purpose of User Settings Persistence in Amazon AppStream 2.0?,To save user-specific application settings and data between streaming sessions,To enforce user access policies,To track user activity,To optimise application performance,"User Settings Persistence saves user application settings and data between streaming sessions, providing a consistent user experience."
Which storage options can be integrated with Amazon AppStream 2.0 to allow users to access their files?,"Amazon S3, Google Drive, OneDrive",Amazon EBS,Amazon EFS,Amazon Glacier,"AppStream 2.0 can be integrated with Amazon S3, Google Drive, and OneDrive to allow users to access their files directly within the streaming environment."
What is the benefit of using the Image Builder in Amazon AppStream 2.0?,To create and customise images with the desired applications and settings,To manage user access policies,To monitor application performance,To automate the deployment of streaming instances,"The Image Builder allows administrators to create custom images that contain the applications and settings needed for streaming, ensuring a consistent and controlled environment."
Which protocol is primarily used for streaming applications in Amazon AppStream 2.0?,NICE DCV,RDP,VNC,ICA,"AppStream 2.0 uses the NICE DCV protocol, which is designed for high-performance remote visualisation of 3D applications and desktops."
How can you control which applications are available to users in Amazon AppStream 2.0?,By assigning applications to specific stacks and using user assignment policies,By managing network access control lists,By modifying the user's operating system,By controlling user permissions in IAM,You control which applications are available to users by assigning applications to stacks and using user assignment policies to associate users with specific stacks.
What is the purpose of Application settings persistence for Amazon AppStream 2.0?,To allow users to retain their application settings between sessions,To automatically update applications,To monitor application usage,To manage user profiles,"Application settings persistence ensures users retain their application settings between streaming sessions, providing a consistent user experience."
How does Amazon AppStream 2.0 handle application updates?,"Administrators can update the image, and new sessions use the updated image",Users update applications individually in their sessions,Applications are automatically updated from the internet,Application updates are not supported,Administrators need to update the image used by AppStream 2.0.  New sessions will then use the updated image.
What security measures can you implement with Amazon AppStream 2.0 to protect your streamed applications?,"Network isolation, data encryption, and access control policies","Data compression, caching, and load balancing","Application sandboxing, vulnerability scanning, and intrusion detection","Operating system hardening, patching, and configuration management","Security measures for AppStream 2.0 include network isolation, data encryption, and access control policies to ensure the safety and integrity of streamed applications and data."
What is a disconnected application in the context of Amazon AppStream 2.0?,An application that can be used when the streaming instance loses connection,An application that is not compatible with AppStream 2.0,An application that requires an internet connection,An application that is automatically removed from the image,AppStream 2.0 provides support for applications that can be used even when the streaming instance loses network connectivity.
How can you monitor the performance and usage of Amazon AppStream 2.0?,Using Amazon CloudWatch metrics and logs,Using AWS CloudTrail logs,Using Amazon Inspector scans,Using AWS Config rules,"Amazon CloudWatch metrics and logs allow you to monitor the performance and usage of AppStream 2.0, providing insights into application and infrastructure behaviour."
What is the purpose of the Amazon AppStream 2.0 API?,To programmatically manage and automate AppStream 2.0 resources,To manage user access,To monitor network traffic,To optimise application performance,"The Amazon AppStream 2.0 API allows you to programmatically manage and automate AppStream 2.0 resources, facilitating integration with other systems and workflows."
Which AWS service can you use to store user profiles for Amazon AppStream 2.0 users?,Amazon S3,Amazon EBS,Amazon EC2,Amazon RDS,"User profiles can be stored in Amazon S3, providing a centralised and durable storage solution for user settings and data."
How does Amazon AppStream 2.0 ensure data security during application streaming?,Data is encrypted in transit and at rest,Data is compressed and cached,Data is replicated across multiple regions,Data is anonymized,AppStream 2.0 encrypts data in transit and at rest to protect sensitive information during application streaming.
What is the purpose of the 'Fleet Advisor' in Amazon AppStream 2.0?,To analyse application usage and recommend optimal fleet configurations,To manage user access,To monitor network performance,To automate application updates,The Fleet Advisor analyses application usage patterns and provides recommendations for optimising fleet configurations to reduce costs and improve performance.
What is the role of a Directory Config object within Amazon AppStream 2.0?,It's used to integrate with Active Directory for user authentication and authorisation,It configures network security groups,It defines the applications available in the streaming image,It manages user profiles,"A Directory Config object in Amazon AppStream 2.0 is essential for integrating with Active Directory, enabling user authentication and authorisation within the streaming environment."
What is the significance of 'Stream View' in Amazon AppStream 2.0?,It defines the appearance and user interface settings during streaming,It manages application updates,It monitors network traffic,It controls user access policies,"Stream View in Amazon AppStream 2.0 customises the visual appearance and user interface settings during the streaming process, providing a tailored user experience."
What does the term 'App Block' refer to within Amazon AppStream 2.0?,A way to install and manage individual applications within an image,A way to block certain users from accessing specific applications,A method for monitoring application performance,A way to restrict network access for specific applications,"An App Block is a self-contained module that packages an application and its dependencies, simplifying the process of installing and managing individual applications within AppStream 2.0 images."
How do you determine the appropriate instance type for an Amazon AppStream 2.0 fleet?,By considering the resource requirements of the applications being streamed,By selecting the lowest-cost instance type,By choosing an instance type with the highest available memory,By selecting an instance type with the fastest network speed,The appropriate instance type for an AppStream 2.0 fleet is determined by considering the resource requirements of the applications being streamed.
What is the maximum streaming duration supported by Amazon AppStream 2.0 per user session?,12 hours,4 hours,8 hours,24 hours,Amazon AppStream 2.0 supports a maximum streaming duration of 12 hours per user session.
How can you provide users with access to locally installed printers within their Amazon AppStream 2.0 sessions?,By enabling printer redirection,By installing printer drivers on the streaming instances,By configuring a virtual print server,By using a third-party printing solution,You can provide users with access to locally installed printers within their AppStream 2.0 sessions by enabling printer redirection.
What is the purpose of 'Dynamic App Framework' (DAF) in Amazon AppStream 2.0?,To simplify the integration and management of applications in AppStream 2.0,To manage user profiles,To monitor network traffic,To automate application updates,Dynamic App Framework (DAF) simplifies the integration and management of applications in AppStream 2.0 by allowing for dynamic installation and configuration of applications.
Which of the following actions will optimise Amazon AppStream 2.0's performance?,Ensure the application image is updated and optimised,Increasing the number of users in a stack,Reducing the size of the stack,Enabling always-on sessions,Ensuring the application image is updated and optimised will improve start times and overall performance.
What is the function of the 'Usage Reporting' feature in Amazon AppStream 2.0?,To track application usage and user activity,To manage user access,To monitor network traffic,To automate application updates,"The Usage Reporting feature tracks application usage and user activity in AppStream 2.0, providing insights into how users interact with streamed applications."
How does Amazon AppStream 2.0 integrate with Microsoft Active Directory?,For user authentication and authorisation,For network traffic monitoring,For application management,For automated software updates,"Amazon AppStream 2.0 integrates with Microsoft Active Directory for user authentication and authorisation, simplifying user management and providing a seamless user experience."
Which factor is most important when selecting the appropriate region for your Amazon AppStream 2.0 implementation?,Proximity to your users,Cost of the service,Available instance types,Network bandwidth,"Proximity to your users is a key factor when selecting the region for your AppStream 2.0 implementation, minimising latency and improving the user experience."
How can you reduce the cost of running Amazon AppStream 2.0?,By optimising fleet utilisation and using cost-optimised instance types,By increasing the number of users per instance,By reducing the image size,By using a different streaming protocol,Optimising fleet utilisation and using cost-optimised instance types are effective ways to reduce the cost of running AppStream 2.0.
What is the purpose of the 'Idle disconnect timeout' setting in Amazon AppStream 2.0?,To automatically disconnect inactive user sessions to conserve resources,To manage user access,To monitor network traffic,To automate application updates,"The idle disconnect timeout setting automatically disconnects inactive user sessions in AppStream 2.0, conserving resources and reducing costs."
How can you provide users with temporary elevated permissions within their Amazon AppStream 2.0 sessions?,Using Group Policy Objects (GPOs) in the base image,By granting permanent administrator access,By using a custom script to elevate permissions temporarily,By using the User Assignment Policy,"GPOs in the base image will not persist, and admin should never be granted permanently, so the proper method is using a custom script to elevate permissions when needed."
What is the maximum number of applications that can be assigned to a single Amazon AppStream 2.0 stack?,"There's no specific limit, but it's recommended to keep the number manageable for performance reasons",5,10,20,"While there's no hard limit, the number of applications assigned to a stack should be manageable to ensure optimal performance and a good user experience."
What is the purpose of the 'AppStream 2.0 Agent' that is installed on the image?,To manage the streaming session and communication with the AppStream 2.0 service,To monitor network traffic,To manage user access,To automate application updates,"The AppStream 2.0 Agent is essential for managing the streaming session and communication with the AppStream 2.0 service, enabling the delivery of applications to users."
How do you configure application licensing when using Amazon AppStream 2.0?,By integrating with a license server or using a licensing solution within the image,By managing licenses through the AWS Marketplace,By using the User Assignment Policy,By setting a default license for all users,"Application licensing in AppStream 2.0 is typically configured by integrating with a license server or using a licensing solution within the image, ensuring compliance with software licensing agreements."
What is the role of 'Stack Scaling Policies' in Amazon AppStream 2.0?,To automatically adjust the number of streaming instances based on demand,To manage user access,To monitor network traffic,To automate application updates,"Stack scaling policies automatically adjust the number of streaming instances based on demand, optimising resource utilisation and ensuring a responsive user experience."
Which AWS service is typically used to provide a fully managed Active Directory for Amazon AppStream 2.0?,AWS Managed Microsoft AD,AWS IAM Identity Center (Successor to AWS Single Sign-On),Amazon Cognito,AWS Directory Service Simple AD,"AWS Managed Microsoft AD provides a fully managed Active Directory service for Amazon AppStream 2.0, simplifying user management and integration."
How can you ensure that users have the latest version of an application when using Amazon AppStream 2.0?,By regularly updating the image and deploying it to the fleet,By enabling automatic application updates,By using a configuration management tool,By instructing users to update the application themselves,The most reliable way to ensure users have the latest version of an application in AppStream 2.0 is by regularly updating the image and deploying it to the fleet.
What is the purpose of the 'Application Catalogue' within the Amazon AppStream 2.0 console?,To manage and organise the applications available for streaming,To monitor network traffic,To manage user access,To automate application updates,The Application Catalogue provides a centralised location to manage and organise the applications available for streaming in AppStream 2.0.
How can you enable clipboard redirection in Amazon AppStream 2.0?,By enabling the appropriate setting in the stack configuration,By configuring the network security group,By modifying the user's operating system,By installing a third-party application,"Clipboard redirection is enabled by configuring the appropriate setting in the stack configuration, allowing users to copy and paste content between their local device and the streaming session."
"When deploying Amazon AppStream 2.0, how would you handle applications that require GPU acceleration?",By selecting GPU-enabled instance types for the fleet,By using a software-based GPU emulator,By offloading the GPU processing to the user's device,By configuring a virtual GPU,"For applications requiring GPU acceleration, you must select GPU-enabled instance types for the fleet, providing the necessary hardware resources for optimal performance."
What does the term 'Session Context' refer to in Amazon AppStream 2.0?,The user's environment and application settings within a streaming session,The network configuration settings for the streaming session,The security policy applied to the streaming session,The monitoring data collected during the streaming session,"Session Context refers to the user's environment and application settings within a streaming session, including preferences, data, and other customisations."
What are some benefits of using Amazon AppStream 2.0 compared to traditional desktop application deployment?,"Centralised application management, improved security, and reduced administrative overhead","Increased application compatibility, faster application performance, and lower infrastructure costs","Enhanced user customisation, greater application version control, and improved data backup","Simplified application packaging, streamlined application licensing, and improved disaster recovery","Amazon AppStream 2.0 offers centralised application management, improved security, and reduced administrative overhead compared to traditional desktop application deployment."
What is a key difference between 'Always-On' and 'On-Demand' fleets in Amazon AppStream 2.0?,"Always-On fleets have instances running at all times, while On-Demand fleets start instances when users connect",Always-On fleets offer higher performance than On-Demand fleets,Always-On fleets support a wider range of applications than On-Demand fleets,Always-On fleets are cheaper than On-Demand fleets,"Always-On fleets have instances running continuously, ensuring immediate availability for users, while On-Demand fleets start instances only when users connect, potentially saving costs during periods of low usage."
What is the purpose of 'Application Auto Scaling' in Amazon AppStream 2.0?,To automatically scale the compute capacity of the streaming instances,To automatically update the applications within the image,To automatically manage user access to the applications,To automatically optimise the network configuration for streaming,"Application Auto Scaling in AppStream 2.0 automatically adjusts the compute capacity of the streaming instances based on demand, optimising performance and cost."
"In the context of Amazon AppStream 2.0, what does a 'Network ACL' (Access Control List) control?",Inbound and outbound network traffic at the subnet level,User access to applications,Application updates,Authentication and authorisation for users,"A Network ACL controls inbound and outbound network traffic at the subnet level, providing a security layer for AppStream 2.0 resources."
How can you monitor the health and availability of your Amazon AppStream 2.0 fleets?,Using Amazon CloudWatch metrics and alarms,Using AWS CloudTrail logs,Using Amazon Inspector scans,Using AWS Config rules,"Amazon CloudWatch metrics and alarms provide real-time monitoring of the health and availability of your AppStream 2.0 fleets, allowing you to identify and respond to issues quickly."
What is the primary purpose of Amazon AppStream 2.0?,To stream desktop applications to users' devices,To store large amounts of data,To manage AWS infrastructure costs,To monitor application performance,AppStream 2.0 allows you to stream desktop applications to users on any device with a supported browser.
Which AWS service is most commonly used to manage user access and authentication for Amazon AppStream 2.0?,AWS IAM,Amazon S3,Amazon EC2,Amazon CloudFront,"AWS IAM (Identity and Access Management) is used to control access to AWS resources, including AppStream 2.0."
What is an AppStream 2.0 'stack'?,A collection of applications and settings for streaming,A virtual server used for application development,A physical server hosting the streaming infrastructure,A monitoring tool for application performance,An AppStream 2.0 stack is a collection of application and settings for streaming applications to end users. It defines the environment for the streaming experience.
Which AppStream 2.0 instance type is generally the most cost-effective for light productivity workloads?,Graphics.g4dn.xlarge,Compute.large,Memory.4xlarge,GraphicsPro.4gn.xlarge,"Compute.large instance types are designed for light productivity workloads, offering a balance of compute and memory resources at a cost-effective price point."
What is the purpose of the AppStream 2.0 Image Builder?,To create custom application images for streaming,To manage user profiles,To monitor streaming performance,To automate infrastructure deployment,The Image Builder is used to create custom application images that contain the applications you want to stream to your users.
Which feature of AppStream 2.0 allows you to persist user settings and data between streaming sessions?,Application settings persistence,Elastic fleets,Directory config,Fleet auto scaling,"Application settings persistence allows user settings and data to be saved between streaming sessions, providing a consistent user experience."
What is the purpose of an AppStream 2.0 'fleet'?,To manage the pool of streaming instances,To manage user authentication,To define application images,To configure network settings,"An AppStream 2.0 fleet is a pool of streaming instances that are used to deliver applications to users. It defines the instance type, scaling parameters, and other configuration settings."
Which of the following is a valid scaling policy option for an AppStream 2.0 fleet?,Target utilisation,Fixed capacity,Manual Scaling,Scheduled Scaling,Target utilisation automatically scales the fleet size based on the CPU usage to ensure an efficient use of resources
What network configuration is required for AppStream 2.0 to access applications and data within a private VPC?,VPC Endpoints or VPC Peering,Public IP Addresses on all instances,Direct Connect,Internet Gateway,AppStream 2.0 requires VPC Endpoints or VPC Peering to connect to resources in a private VPC without exposing traffic to the public internet.
Which of the following authentication methods is supported by Amazon AppStream 2.0?,SAML 2.0,LDAP,RADIUS,Kerberos,"SAML 2.0 is a supported authentication method, allowing integration with identity providers for single sign-on."
How can you reduce the cost of running AppStream 2.0 environments when they are not in use?,Stop the fleet,Delete the Image Builder,Remove the Application,Detach the directory config,"Stopping the fleet when it is not in use is an effective way to reduce costs, as you are not charged for instance hours when the fleet is stopped."
Which type of storage is best suited for storing user home directories in AppStream 2.0?,Amazon S3,Amazon EBS,Amazon EFS,Amazon Glacier,Amazon S3 is suited for durable storage and is appropriate to store user home directories in AppStream 2.0
What is the primary benefit of using Elastic fleets in Amazon AppStream 2.0?,Automatic scaling based on user demand,Faster application loading times,Enhanced security features,Reduced instance costs,"Elastic fleets automatically scale the number of streaming instances based on user demand, ensuring that resources are available when needed and reducing costs when demand is low."
Which feature of AppStream 2.0 enables users to copy and paste content between their local device and the streaming session?,Local device integration,Clipboard redirection,Application streaming,Desktop synchronisation,Clipboard redirection enables users to seamlessly copy and paste content between their local device and the streaming session.
What is the purpose of the AppStream 2.0 'directory config'?,To connect AppStream 2.0 to an Active Directory domain,To configure network settings,To manage application settings,To monitor user activity,"The directory config is used to connect AppStream 2.0 to an Active Directory domain, enabling user authentication and authorisation."
What is the maximum session duration allowed in Amazon AppStream 2.0?,12 hours,24 hours,48 hours,72 hours,The maximum session duration allowed in Amazon AppStream 2.0 is 12 hours.
Which of the following protocols is used for streaming applications in Amazon AppStream 2.0?,NICE DCV,RDP,VNC,ICA,"AppStream 2.0 uses NICE DCV (Desktop Cloud Visualisation) for streaming applications, providing a high-performance and low-latency experience."
Which AWS service can be integrated with AppStream 2.0 to provide single sign-on (SSO) capabilities?,AWS IAM Identity Center (Successor to AWS Single Sign-On),Amazon Cognito,AWS Directory Service,AWS KMS,AWS IAM Identity Center (Successor to AWS Single Sign-On) provides single sign-on capabilities and can be integrated with AppStream 2.0 for streamlined user authentication.
What is the purpose of the 'Always-On' fleet type in AppStream 2.0?,To provide dedicated streaming instances that are always running,To automatically scale instances based on demand,To optimise application loading times,To reduce instance costs during off-peak hours,"The 'Always-On' fleet type provides dedicated streaming instances that are always running, ensuring that users can access applications immediately without waiting for instances to start."
Which AppStream 2.0 feature allows you to control the applications that users can access?,Application entitlements,Fleet configuration,User settings persistence,Directory configuration,Application entitlements allow you to control which applications specific users or groups can access within AppStream 2.0.
What is the primary purpose of the 'Capacity' tab within the AppStream 2.0 Fleet details?,To configure scaling policies,To manage user access,To define application images,To monitor instance performance,"The 'Capacity' tab within the AppStream 2.0 Fleet details is used to configure scaling policies, including minimum capacity, maximum capacity, and desired capacity."
Which of the following actions will NOT incur charges when using Amazon AppStream 2.0?,Creating an image builder,Stopping a fleet,Running a streaming session,Storing application images,"Stopping a fleet will not incur charges, as you are not charged for instance hours when the fleet is stopped. However, there might be charges associated with associated volumes."
Which of the following is a key advantage of using AppStream 2.0 over traditional on-premises application deployments?,Centralised application management,Increased hardware costs,Reduced security,Limited scalability,"AppStream 2.0 offers centralised application management, allowing you to manage and update applications from a single location."
What is the recommended method for updating applications in an AppStream 2.0 image?,Use the Image Builder to create a new image,Manually update the application on each streaming instance,Use AWS Systems Manager to patch the instances,Remotely access each streaming instance via RDP,"The recommended method is to use the Image Builder to create a new image with the updated applications, ensuring consistency across all streaming instances."
Which AppStream 2.0 feature allows you to provide users with persistent storage that is automatically connected to their streaming sessions?,Home Folders,Application settings persistence,Elastic Volumes,User Profiles,Home Folders (backed by S3) provide users with persistent storage that is automatically connected to their streaming sessions.
Which of the following is a key consideration when choosing an AppStream 2.0 instance type?,Application requirements,Number of users,Network bandwidth,Storage capacity,"The choice of instance type should be based on the application requirements, such as CPU, memory, and graphics processing needs."
What is the purpose of the AppStream 2.0 streaming URL?,To provide users with access to their streaming sessions,To configure application settings,To manage user authentication,To monitor streaming performance,"The streaming URL is used to provide users with access to their streaming sessions, allowing them to launch and use applications."
Which AWS service can be used to monitor the performance and health of AppStream 2.0 fleets?,Amazon CloudWatch,AWS CloudTrail,AWS Config,AWS Trusted Advisor,"Amazon CloudWatch can be used to monitor the performance and health of AppStream 2.0 fleets, providing insights into instance utilisation, network traffic, and other metrics."
What is the recommended way to integrate a multi-factor authentication (MFA) with AppStream 2.0?,Integrate with your SAML 2.0 identity provider,Use AWS IAM roles,Configure AppStream 2.0 directly,Disable password authentication,"The recommended way to integrate MFA is to use your SAML 2.0 identity provider, which can enforce MFA policies during the authentication process."
Which of the following is a valid option for managing application licensing in AppStream 2.0?,Network licensing server,Per-user licensing,Concurrent licensing,Device-based licensing,"Network licensing server is a valid way to manage application licensing, allowing you to track and control license usage across your AppStream 2.0 environment."
Which AppStream 2.0 feature enables you to upload and manage applications from a central location?,Application Catalog,Image Builder,Fleet Configuration,Directory Config,"Application Catalog allows you to upload and manage applications from a central location, making it easier to create and maintain application images."
What is the purpose of the AppStream 2.0 Usage Reports?,To track user session activity and resource consumption,To configure scaling policies,To manage user access,To define application images,"Usage Reports are used to track user session activity and resource consumption, providing insights into how AppStream 2.0 is being used."
Which of the following is a key benefit of using non-persistent instances in AppStream 2.0?,Improved security,Faster application loading times,Reduced storage costs,Persistent user settings,"Non-persistent instances provide improved security, as they are reset after each user session, preventing data from being stored on the instances."
What type of security groups should be associated with AppStream 2.0 instances?,Security groups that allow inbound traffic from the VPC CIDR,Security groups that allow outbound internet access,Security groups that allow inbound RDP access,Security groups that allow inbound SSH access,Security groups should allow inbound traffic from the VPC CIDR range to ensure that AppStream 2.0 instances can communicate with other resources in the VPC.
Which AppStream 2.0 setting allows you to configure the idle disconnect timeout for user sessions?,Idle disconnect timeout,Session duration,Maximum session length,Disconnect delay,The idle disconnect timeout setting allows you to configure the amount of time that a user session can be idle before it is automatically disconnected.
Which feature in AppStream 2.0 enables you to configure the applications that are automatically launched when a user starts a streaming session?,Session Scripts,Start Applications,Application configuration,Fleet configuration,Session Scripts allow you to configure the applications that are automatically launched when a user starts a streaming session.
Which of the following is a key factor to consider when designing the network architecture for AppStream 2.0?,Bandwidth,User location,Storage capacity,Application compatibility,"Network bandwidth is a key factor, as it can impact the performance of the streaming experience."
What is the primary purpose of using tags in AppStream 2.0?,To organise and manage resources,To configure scaling policies,To manage user access,To define application images,"Tags are used to organise and manage resources, making it easier to track and control costs and apply policies."
What level of administrative access does AppStream 2.0 grant to end users by default?,Limited,Full,Root,None,AppStream 2.0 provides limited administrative access to end users to enhance security
You want to make sure that any data that users store during a session with an app streamed from AppStream 2.0 is automatically removed after a user disconnects. How can you achieve this?,Set up non-persistent AppStream 2.0 instances.,Configure persistent AppStream 2.0 instances.,Set up S3 buckets for persistent storage.,Disable user access to the applications.,"By setting up non-persistent AppStream 2.0 instances, any data created or stored during the session will be automatically removed when the user disconnects, enhancing security and data privacy."
Your company's security policy requires that all data transmitted between the client device and the Amazon AppStream 2.0 streaming instance must be encrypted. Which of the following methods ensures that AppStream 2.0 meets this requirement?,AppStream 2.0 encrypts all streaming traffic by default.,Configure AWS CloudHSM for data encryption.,Enable S3 server-side encryption.,Implement client-side encryption.,"Amazon AppStream 2.0 is designed to encrypt all streaming traffic by default, ensuring that data transmitted between the client device and the AppStream 2.0 instance remains secure."
What type of AWS account access is recommended for a contractor needing access to an AppStream 2.0 deployment to make changes?,Create IAM user with appropriate permissions,Share the root account credentials,Use the AWS Organizations master account,Create a temporary AWS account,The best practice is to create an IAM user for the contractor and grant that user the least privilege necessary to perform the required tasks.
You need to integrate Amazon AppStream 2.0 with an identity provider that supports SAML 2.0 to enable single sign-on (SSO) for your users. What is the first step in this process?,Configure a trust relationship between AWS and the identity provider.,Create a new AppStream 2.0 stack.,Enable multi-factor authentication (MFA) for all users.,Upload the IdP metadata to AWS IAM.,"Before integrating with SAML, you need to set up a trust relationship to establish a secure connection between your identify provider and AWS."
"When configuring Amazon AppStream 2.0, which of the following factors has the MOST impact on the streaming performance experienced by end users?",Network latency between the user and the AWS Region,Number of applications installed on the image,Storage capacity of the streaming instance,User's local device specifications,Network latency is the MOST important factor. Low latency is key to a smooth streaming experience.
"You are deploying a new application in an Amazon AppStream 2.0 environment, and users are reporting that the application is not visible after they log in. Which of the following is the MOST likely cause of this issue?",The application is not assigned to the users' entitlements.,The application is not compatible with the AppStream 2.0 instance type.,The application is not installed correctly on the base image.,The users' accounts are not properly configured in Active Directory.,Entitlements control access to applications. If a user does not have the correct entitlement they will not see it.
A user reports that their application settings are not being saved between Amazon AppStream 2.0 sessions. Which of the following could be the reason?,Application settings persistence is not enabled for the stack.,The user is using a temporary profile.,The home folder feature is disabled.,The user is using a different device each session.,Application settings persistence is the feature that saves the user's settings between sessions.
You want to allow users to access their local printers from within their Amazon AppStream 2.0 sessions. What feature should you enable?,Local device redirection,Home Folders,Clipboard redirection,File transfer,Local device redirection is used to allow access to printers and other devices.
Your company wants to restrict users from copying and pasting sensitive data from the AppStream 2.0 session to their local device. How can you configure AppStream 2.0 to meet this requirement?,Disable clipboard redirection.,Disable file transfer.,Configure network restrictions.,Enforce MFA.,"Clipboard redirection allows copying and pasting, disabling this will stop the users from performing this action."
Which of the following AppStream 2.0 features allows administrators to pre-install and configure applications in a consistent manner across all streaming instances?,Image Builder,Application Catalog,Fleet Configuration,Directory Config,The Image Builder allows you to create a custom image with all the pre-installed applications and settings.
What is the primary function of Amazon AppStream 2.0?,To stream desktop applications to users on any device,To provide a serverless compute environment,To manage and deploy containerised applications,To store and retrieve data objects,"AppStream 2.0 allows you to stream desktop applications securely to any device, providing users with access to the applications they need without requiring them to be installed locally."
Which AWS service does Amazon AppStream 2.0 utilise for user authentication?,AWS Identity and Access Management (IAM),Amazon Cognito,AWS Directory Service,AWS Single Sign-On (SSO),AppStream 2.0 can be integrated with various identity providers but it uses AWS Directory Service to manage users and authenticate them.
Which of the following streaming protocols does Amazon AppStream 2.0 use to deliver applications?,NICE DCV,RDP,Citrix ICA,VMware Blast Extreme,"AppStream 2.0 uses the NICE DCV (Desktop Cloud Visualisation) protocol for streaming applications, providing a high-performance and secure streaming experience."
What is an 'Image' in the context of Amazon AppStream 2.0?,"A template containing the operating system, applications, and settings",A snapshot of a user's streaming session,A collection of AWS Lambda functions,A virtual network configuration,"An Image in AppStream 2.0 serves as a template containing the operating system, applications, and settings that are used to create streaming instances."
What is a 'Stack' in Amazon AppStream 2.0?,A collection of resources needed to provision and manage streaming applications,A single instance of a streaming application,A user's profile and settings,A pricing model for AppStream 2.0,"A Stack in AppStream 2.0 is a collection of resources, including Images, fleets, and user settings, that are needed to provision and manage streaming applications."
What is a 'Fleet' in Amazon AppStream 2.0?,A group of streaming instances that deliver applications to users,A collection of user profiles and settings,A network configuration for streaming traffic,A set of security policies for AppStream 2.0,A Fleet in AppStream 2.0 is a group of streaming instances (virtual machines) that deliver applications to users.
Which of the following instance types are generally suitable for graphics-intensive applications in Amazon AppStream 2.0?,Graphics Pro,General Purpose,Memory Optimised,Compute Optimised,"Graphics Pro instance types are designed for applications that require high-performance graphics capabilities, making them suitable for graphics-intensive applications in AppStream 2.0."
Which of the following is NOT a supported method for user access to Amazon AppStream 2.0?,Through a web browser,Through a native client application,Through a VPN connection,Through an SSH tunnel,Users typically access AppStream 2.0 applications either through a web browser or a native client application.  VPN or SSH are not direct access methods.
What is the purpose of 'Application Settings Persistence' in Amazon AppStream 2.0?,To save user application settings between streaming sessions,To optimise application performance,To manage application licensing,To control user access to applications,"Application Settings Persistence allows users to save their application settings (e.g., preferences, customisations) between streaming sessions, providing a consistent user experience."
Which of the following storage options can be integrated with Amazon AppStream 2.0 for user data?,Amazon S3,Amazon EBS,Amazon EFS,Amazon Glacier,"AppStream 2.0 can be integrated with Amazon S3, allowing users to store and access their data in a centralised and scalable location."
What is the purpose of the 'Image Builder' in Amazon AppStream 2.0?,To create and manage custom images for streaming applications,To monitor application performance,To manage user accounts,To configure network settings,"The Image Builder is used to create and manage custom images, allowing you to install and configure the applications and settings that you want to stream to your users."
Which of the following is a key benefit of using Amazon AppStream 2.0 for application delivery?,Improved security by centralising applications and data,Reduced application licensing costs,Simplified network management,Increased server capacity,"AppStream 2.0 improves security by centralising applications and data in the cloud, reducing the risk of data breaches and protecting sensitive information."
Which Amazon AppStream 2.0 feature allows you to configure idle disconnect timeouts?,Session Management,Scaling Policies,User Settings Persistence,Image Builder,"Session Management features in AppStream 2.0 let you configure how long a session can be idle before it automatically disconnects, helping to manage costs and resources."
What is the role of an Active Directory (AD) Connector in Amazon AppStream 2.0?,To allow AppStream 2.0 to use your existing AD for user authentication,To provide secure network connectivity,To manage application licensing,To monitor application performance,"An AD Connector allows AppStream 2.0 to integrate with your existing Active Directory, enabling you to use your existing AD users and groups for authentication and authorisation."
How does Amazon AppStream 2.0 support printing from streamed applications?,Through integration with local printers or network printers,By redirecting print jobs to AWS Lambda,By converting print jobs to PDF and storing them in S3,By disabling printing entirely for security reasons,"AppStream 2.0 allows users to print from streamed applications by integrating with local printers or network printers, providing a seamless printing experience."
What is the purpose of 'Dynamic App Updates' in Amazon AppStream 2.0?,To update applications without rebuilding the image,To scale resources automatically based on demand,To manage user access to applications,To monitor application performance in real-time,"Dynamic App Updates allow you to update applications without rebuilding the image, reducing the need for frequent image updates and minimising downtime."
Which of the following is a common use case for Amazon AppStream 2.0?,Delivering CAD/CAM software to remote engineers,Hosting a website,Storing large datasets,Running batch processing jobs,"AppStream 2.0 is often used to deliver resource-intensive applications such as CAD/CAM software to remote engineers, enabling them to access these applications from any device with a reliable internet connection."
Which security best practice should be followed when configuring Amazon AppStream 2.0?,Use strong authentication methods and restrict access to resources,Allow open access to all resources for ease of use,Share administrator credentials with all users,Disable logging to reduce storage costs,"Using strong authentication methods (e.g., multi-factor authentication) and restricting access to resources based on the principle of least privilege are essential security best practices for AppStream 2.0."
What type of scaling policy is best suited for ensuring that AppStream 2.0 has enough capacity to handle peak demand?,Target capacity scaling,Manual scaling,Scheduled scaling,Step scaling,Target Capacity scaling is most suitable for ensuring that AppStream 2.0 has enough capacity to handle peak demand. It keeps a specific number of instances available.
How does Amazon AppStream 2.0 handle application licensing?,"It supports various licensing models, including concurrent and named user licensing",It only supports concurrent licensing,It only supports named user licensing,It requires all applications to be open-source,"AppStream 2.0 supports various licensing models, including concurrent and named user licensing, giving you flexibility in how you manage your application licenses."
Which AWS service is commonly used to manage user identities and access to Amazon AppStream 2.0 applications?,AWS IAM Identity Center (Successor to AWS SSO),Amazon CloudWatch,AWS CloudTrail,Amazon SQS,"AWS IAM Identity Center (Successor to AWS SSO) is often used to manage user identities and access to AppStream 2.0 applications, simplifying user management and access control."
What is the role of 'Application Catalog' within Amazon AppStream 2.0?,To manage and organise applications for streaming,To store application binaries,To manage user profiles,To monitor application performance,"The Application Catalog is used to manage and organise the applications that you want to stream to your users, providing a central place to manage your application portfolio."
Which of the following actions can help optimise the performance of Amazon AppStream 2.0?,"Optimising image size, configuring application settings persistence and selecting appropriate instance types",Increasing the complexity of network configurations,Disabling application settings persistence,Using the cheapest possible instance types,"Optimising image size, configuring application settings persistence, and selecting appropriate instance types are all actions that can help optimise the performance of AppStream 2.0."
What is the recommended approach for patching and updating applications within Amazon AppStream 2.0?,By regularly rebuilding the image with the latest updates,By manually updating applications on individual instances,By disabling automatic updates for security reasons,By using a third-party patching tool without proper testing,"The recommended approach for patching and updating applications within AppStream 2.0 is to regularly rebuild the image with the latest updates, ensuring that all instances are running the most secure and up-to-date versions of your applications."
How does Amazon AppStream 2.0 contribute to meeting compliance requirements?,"By centralising applications and data, reducing the risk of data breaches",By providing automatic encryption of data at rest,By automating security audits,By automatically backing up all data,"AppStream 2.0 contributes to meeting compliance requirements by centralising applications and data, reducing the risk of data breaches and making it easier to control access to sensitive information."
What is the purpose of 'Home Folders' in Amazon AppStream 2.0?,To provide users with persistent storage for their files,To store application settings,To manage user profiles,To store application binaries,"Home Folders provide users with persistent storage for their files, allowing them to access their data from any streaming session."
Which AWS service is used to monitor the health and performance of Amazon AppStream 2.0 instances?,Amazon CloudWatch,Amazon CloudTrail,AWS Config,AWS Trusted Advisor,"Amazon CloudWatch is used to monitor the health and performance of AppStream 2.0 instances, providing insights into CPU usage, memory utilisation, and other key metrics."
When should you consider using Elastic Fleets in Amazon AppStream 2.0?,When your application needs to scale quickly and handle unpredictable workloads,When you have a small number of users and stable workloads,When you need to minimise costs at all costs,When you need to disable all scaling capabilities,"Elastic Fleets are designed for applications that need to scale quickly and handle unpredictable workloads, allowing you to automatically adjust the number of instances based on demand."
What is a key advantage of using Amazon AppStream 2.0 over traditional virtual desktop infrastructure (VDI)?,Simplified management and reduced infrastructure costs,Increased application compatibility,Improved network performance,Enhanced security features,"AppStream 2.0 simplifies management and reduces infrastructure costs compared to traditional VDI solutions, as you don't need to manage the underlying infrastructure or operating systems."
How can you control the network traffic for Amazon AppStream 2.0 instances?,By configuring security groups and network ACLs,By disabling all network access,By using a third-party firewall,By routing all traffic through a VPN,"You can control the network traffic for AppStream 2.0 instances by configuring security groups and network ACLs, allowing you to restrict access to specific ports and protocols."
What is the benefit of using 'Application Lifecycle Management' tools with Amazon AppStream 2.0?,To automate application deployment and updates,To monitor application performance,To manage user access to applications,To optimise application licensing,"Application Lifecycle Management tools can be used to automate application deployment and updates, streamlining the application management process and reducing the risk of errors."
Which Amazon AppStream 2.0 feature enables you to control the maximum concurrent sessions for a user?,Entitlements,Scaling policies,Image Builder,Fleet types,"Entitlements allow you to control which users can access which applications within your AppStream 2.0 environment, including limiting the maximum concurrent sessions for a user."
What is the purpose of the 'Session Recording' feature in Amazon AppStream 2.0?,To record user sessions for auditing and compliance purposes,To monitor application performance,To manage user access to applications,To optimise network bandwidth,"The Session Recording feature allows you to record user sessions for auditing and compliance purposes, providing a record of user activity and helping to identify potential security threats."
Which statement is true about Amazon AppStream 2.0 regional availability?,It is available in many AWS regions globally,It is available in only one AWS region,It is only available in US regions,It is only available in European regions,"AppStream 2.0 is available in many AWS regions globally, allowing you to deploy your applications closer to your users and reduce latency."
How can you integrate Amazon AppStream 2.0 with your existing CI/CD pipeline?,By using the Image Builder API to automate image creation and updates,By manually deploying applications to AppStream instances,By disabling all integration with external tools,By using a third-party deployment tool without proper security review,"You can integrate AppStream 2.0 with your existing CI/CD pipeline by using the Image Builder API to automate image creation and updates, streamlining the application deployment process."
What type of Amazon AppStream 2.0 scaling policy is triggered by CloudWatch alarms?,Step Scaling,Manual Scaling,Scheduled Scaling,Target Capacity Scaling,"Step Scaling policies are triggered by CloudWatch alarms, allowing you to automatically adjust the number of instances based on specific metrics, such as CPU usage or memory utilisation."
How can you ensure that users have a consistent experience when using Amazon AppStream 2.0 across different devices?,By configuring application settings persistence and using Home Folders,By disabling all customisation options,By requiring users to use the same type of device,By using a third-party synchronisation tool,"Configuring application settings persistence and using Home Folders ensures that users have a consistent experience across different devices, as their application settings and files are preserved between streaming sessions."
Which feature of Amazon AppStream 2.0 lets you give certain users access to specific apps?,Entitlements,Fleet scaling,Image builder,Session Management,"Entitlements lets you give certain users access to specific apps, letting you manage the apps each user has."
Which of these should you consider when selecting the right instance type for AppStream 2.0?,The graphic requirements of your apps and the number of users,The default security settings,The AMI used,The instance type used by your other AWS services,"When choosing an instance type, the graphics requirements of your apps and the number of users each instance will support are important considerations."
What is the purpose of 'Application Isolation' in Amazon AppStream 2.0?,To ensure that applications run in a secure and isolated environment,To improve application performance,To simplify application management,To optimise application licensing costs,"Application Isolation ensures that applications run in a secure and isolated environment, preventing them from interfering with each other and improving overall system stability."
"When using Amazon AppStream 2.0, what does 'Application Streaming Mode' refer to?",The method used to stream the application to the user's device,The type of application being streamed,The programming language used to develop the application,The geographic location of the streaming server,"Application Streaming Mode refers to the method used to stream the application to the user's device, typically either using a web browser or a native client."
What's a key way to reduce costs using Amazon AppStream 2.0?,Use Elastic Fleets and configure appropriate scaling policies,Allocate maximum resources at all times,Disable all monitoring and logging,Ignore licensing costs,Using Elastic Fleets and configuring appropriate scaling policies are key ways to reduce costs with AppStream 2.0 because you only pay for the resources you consume.
What can you use to configure a custom sign-in page for users accessing Amazon AppStream 2.0?,AWS IAM Identity Center (Successor to AWS SSO),Amazon Cognito,AWS Directory Service,Amazon CloudFront,"AWS IAM Identity Center (Successor to AWS SSO) can be used to create a custom sign-in page for users accessing AppStream 2.0, providing a branded and personalised login experience."
"In Amazon AppStream 2.0, what does 'Persistent Storage' mean for a user's streamed applications?",The user's data and settings are saved between sessions,"The application is always available, even when the user is offline",The application is installed locally on the user's device,The application runs faster due to local caching,"Persistent Storage means the user's data and settings are saved between sessions, so their streamed applications feel like they're always there."
Which of the following is an important factor in determining the end-user experience with Amazon AppStream 2.0?,Network latency between the user and the AWS region,The number of users sharing an instance,The brand of device used to access the application,The operating system of the user's device,"Network latency between the user and the AWS region is a critical factor in determining the end-user experience with AppStream 2.0, as high latency can lead to lag and a poor user experience."
You have a global user base and want to deploy Amazon AppStream 2.0 for them. What should you consider?,Deploying stacks in multiple AWS regions closer to users,Using a single stack in the lowest-cost AWS region,Using a content delivery network (CDN),Disabling all network optimisation features,"For a global user base, you should deploy stacks in multiple AWS regions closer to your users to minimise latency and provide a better user experience."
What's one way Amazon AppStream 2.0 helps with security?,Centralising applications and data in the cloud,Allowing applications to run directly on user devices,Providing unlimited storage for user data,Removing the need for user authentication,"AppStream 2.0 enhances security by centralising applications and data in the cloud, reducing the risk of data breaches and unauthorised access."
"For Amazon AppStream 2.0, what should you do to make sure your streaming instances can access resources in your VPC?",Configure VPC peering or use AWS PrivateLink,Disable all network security measures,Use public IP addresses for all instances,Share your AWS credentials with all users,"To allow your AppStream 2.0 streaming instances to access resources in your VPC, you need to configure VPC peering or use AWS PrivateLink to establish a secure connection."
What is the use-case for managing AppStream 2.0 by using the AWS Command Line Interface (CLI) or SDKs?,Automating tasks such as image creation and fleet management,Improving user experience,Lowering the streaming costs,Improving Application compatibility,"Using the AWS CLI or SDKs lets you automate tasks like creating images and managing fleets, so you can deploy and manage AppStream 2.0 programmatically."