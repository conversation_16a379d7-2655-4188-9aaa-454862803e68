question,correct_answer,wrong_answer1,wrong_answer2,wrong_answer3,rationale
What is the primary purpose of Amazon WorkSpaces Web?,"Provide secure, browser-based access to internal web applications",Host and manage virtual desktops,Run serverless web applications,Store and manage static website content,WorkSpaces Web enables secure access to internal web applications without needing a fully managed virtual desktop.
Which AWS service does Amazon WorkSpaces Web rely on for identity and access management?,AWS IAM Identity Center (successor to AWS Single Sign-On),AWS Directory Service,Amazon Cognito,AWS Secrets Manager,WorkSpaces Web integrates with AWS IAM Identity Center (successor to AWS Single Sign-On) for user authentication and authorisation.
What is the benefit of using Amazon WorkSpaces Web over a traditional VPN for accessing internal web applications?,Improved security and reduced attack surface,Lower cost for large deployments,Higher performance for streaming media,Greater compatibility with legacy browsers,WorkSpaces Web provides enhanced security by isolating browsing activity and reducing the attack surface compared to a traditional VPN.
Which of the following network configurations is required for Amazon WorkSpaces Web?,A VPC with internet connectivity,A direct connection to AWS,A peered connection to the customer's on-premises network,A NAT gateway in a public subnet,WorkSpaces Web requires a VPC with internet connectivity to function correctly.
What is the purpose of the browser policies within Amazon WorkSpaces Web?,To control user access to specific websites and features,To manage the underlying operating system,To monitor user activity,To configure network settings,Browser policies allow administrators to control which websites and features users can access within the WorkSpaces Web browser.
How does Amazon WorkSpaces Web ensure data security and prevent data leakage?,By isolating browsing sessions and preventing local file access,By encrypting all network traffic,By providing detailed audit logs,By implementing multi-factor authentication,"WorkSpaces Web isolates browsing sessions and prevents users from copying data to their local devices, reducing the risk of data leakage."
What type of applications are best suited for access via Amazon WorkSpaces Web?,Internal web applications and SaaS applications,Desktop applications requiring high performance,Applications requiring direct access to local hardware,Games with rich 3D graphics,WorkSpaces Web is ideal for accessing internal web applications and SaaS applications that are accessed via a web browser.
What is the main advantage of using Amazon WorkSpaces Web from a management perspective?,Simplified deployment and management of secure browsing environments,Full control over the underlying operating system,Centralised management of all desktop applications,Automated patching of user devices,WorkSpaces Web simplifies management by providing a centrally managed secure browsing environment.
Which authentication method is supported by Amazon WorkSpaces Web for user login?,SAML 2.0 federation via AWS IAM Identity Center (successor to AWS Single Sign-On),Multi-Factor Authentication using SMS,Username and password stored in WorkSpaces Web,Kerberos authentication,WorkSpaces Web supports SAML 2.0 federation via AWS IAM Identity Center (successor to AWS Single Sign-On) for user authentication.
What is the impact on end-user devices when using Amazon WorkSpaces Web?,No software needs to be installed on user devices,User devices must be enrolled in a device management system,User devices must have the latest anti-virus software installed,User devices require a specific operating system version,"WorkSpaces Web requires no software installation on end-user devices, simplifying the user experience."
How does Amazon WorkSpaces Web handle browser updates and patching?,Amazon manages browser updates and patching automatically,Administrators must manually apply browser updates,Users are responsible for updating their own browsers,Browser updates are handled by the underlying operating system,"Amazon WorkSpaces Web manages browser updates and patching automatically, reducing the administrative burden."
What is the key difference between Amazon WorkSpaces and Amazon WorkSpaces Web?,"WorkSpaces Web provides access to web applications, while WorkSpaces provides access to full virtual desktops",WorkSpaces Web is cheaper than WorkSpaces,WorkSpaces Web provides higher performance than WorkSpaces,WorkSpaces Web is only available in certain regions,"WorkSpaces Web provides secure browser access to web applications, whereas WorkSpaces provides full virtual desktops."
Which AWS service does Amazon WorkSpaces Web use to store configuration data?,Amazon S3,Amazon DynamoDB,Amazon RDS,Amazon EBS,Amazon WorkSpaces Web utilizes Amazon S3 for storing its configuration data.
What is the role of the 'portal' in Amazon WorkSpaces Web architecture?,The portal is the entry point for users to access their web applications,The portal is a virtual machine running the browser,The portal is a network firewall,The portal is a database server,The portal serves as the entry point for users to securely access their web applications within WorkSpaces Web.
How does Amazon WorkSpaces Web prevent users from downloading sensitive data to their local machines?,By disabling file downloads and clipboard access within the browser session,By encrypting all downloaded files,By scanning downloaded files for malware,By requiring users to use a secure file transfer protocol,WorkSpaces Web prevents data leakage by disabling file downloads and clipboard access within the isolated browser session.
What is the pricing model for Amazon WorkSpaces Web?,Pay-as-you-go based on usage,Fixed monthly fee per user,Hourly fee per instance,Free for AWS customers,"Amazon WorkSpaces Web uses a pay-as-you-go pricing model, where you only pay for what you use."
What is the advantage of using Amazon WorkSpaces Web for employees working from unmanaged devices?,It provides a secure way to access internal resources without compromising security,It allows employees to bypass company security policies,It increases the performance of web applications,It allows employees to install software on their personal devices,"WorkSpaces Web provides a secure access method for employees using unmanaged devices, ensuring compliance with security policies."
Which type of cloud architecture is Amazon WorkSpaces Web built on?,Virtualised infrastructure,Containerised infrastructure,Serverless infrastructure,Bare metal servers,Amazon WorkSpaces Web is built on virtualised infrastructure.
How can you monitor the usage of Amazon WorkSpaces Web?,Using AWS CloudWatch metrics and logs,Using the WorkSpaces Web console,Using AWS Config rules,Using Amazon Inspector,AWS CloudWatch metrics and logs can be used to monitor the usage of WorkSpaces Web.
Which of the following is NOT a component of the Amazon WorkSpaces Web architecture?,Application Load Balancer,EC2 Instance,Virtual Private Cloud,S3 Bucket,An EC2 Instance is NOT a component of the Amazon WorkSpaces Web architecture.
How does Amazon WorkSpaces Web integrate with existing web applications?,It requires minimal or no changes to existing web applications,It requires extensive code modifications to web applications,It requires web applications to be rewritten in a specific programming language,It requires web applications to be hosted on Amazon EC2,Amazon WorkSpaces Web is designed to integrate with existing web applications with minimal or no modifications.
What is the maximum session duration for a user using Amazon WorkSpaces Web?,"Configurable, up to 24 hours",1 hour,4 hours,8 hours,"The maximum session duration for a user on WorkSpaces Web is configurable, up to 24 hours."
How can you control which web applications users can access through Amazon WorkSpaces Web?,By configuring browser policies and access rules,By modifying the web application code,By using AWS WAF rules,By configuring the user's IAM permissions,You can control access to web applications through WorkSpaces Web by configuring browser policies and access rules.
What is the benefit of using Amazon WorkSpaces Web for contractors and temporary workers?,Providing secure access to internal applications without granting full network access,Allowing contractors to use their own preferred browsers,Providing contractors with access to sensitive data,Allowing contractors to install software on company devices,WorkSpaces Web provides a secure and controlled way for contractors to access internal applications without needing full network access.
Which of the following is a best practice for securing Amazon WorkSpaces Web?,Implement multi-factor authentication,Disable logging,Allow all network traffic,Use default browser settings,Implementing multi-factor authentication enhances the security of WorkSpaces Web.
What type of network traffic is encrypted by Amazon WorkSpaces Web?,All traffic between the user's browser and the AWS infrastructure,Only traffic containing sensitive data,Only traffic within the VPC,No network traffic is encrypted,WorkSpaces Web encrypts all traffic between the user's browser and the AWS infrastructure.
What is the purpose of the 'Idle disconnect timeout' setting in Amazon WorkSpaces Web?,To automatically disconnect inactive sessions after a specified period,To prevent users from accessing certain websites,To automatically update the browser version,To limit the amount of data a user can download,The 'Idle disconnect timeout' automatically disconnects inactive sessions to conserve resources and enhance security.
Can Amazon WorkSpaces Web be used to access applications that require client certificates?,"Yes, with appropriate configuration","No, client certificates are not supported",Only with a third-party browser extension,Only for applications hosted on AWS,"Yes, Amazon WorkSpaces Web supports applications requiring client certificates but requires appropriate configuration."
How does Amazon WorkSpaces Web protect against malware and other security threats?,By isolating browsing sessions and preventing execution of untrusted code,By scanning all web traffic for malicious content,By requiring users to install anti-virus software,By blocking access to all external websites,WorkSpaces Web isolates browsing sessions to prevent the execution of untrusted code and protect against malware.
What is the purpose of using tags in Amazon WorkSpaces Web?,To organise and manage resources,To control user access,To encrypt data,To automate patching,Tags can be used to organise and manage WorkSpaces Web resources.
How does Amazon WorkSpaces Web handle printing from the browser session?,Printing is disabled by default to prevent data leakage,Users can print directly to their local printers,Users can only print to network printers,Users can only print to PDF files,"Printing is disabled by default in WorkSpaces Web to prevent data leakage, providing an additional layer of security."
What is the maximum upload file size allowed within Amazon WorkSpaces Web?,This can be controlled by administrators,1 MB,10MB,50MB,The maximum upload file size allowed within Amazon Workspaces Web can be configured by the administrators.
What feature of AWS IAM Identity Center (successor to AWS Single Sign-On) is most directly leveraged by Amazon WorkSpaces Web?,SAML 2.0 federation,Multi-Factor Authentication,Role-Based Access Control,Password Policies,Amazon WorkSpaces Web directly leverages SAML 2.0 federation in AWS IAM Identity Center (successor to AWS Single Sign-On) for user authentication and access.
What is a typical use case for integrating Amazon WorkSpaces Web with a Security Information and Event Management (SIEM) system?,To monitor user activity and detect security threats,To improve network performance,To manage user identities,To automate security patching,Integrating WorkSpaces Web with a SIEM system allows organisations to monitor user activity and detect potential security threats.
What type of data is NOT typically logged by Amazon WorkSpaces Web?,Content of web pages visited,User login attempts,Session duration,Network traffic patterns,WorkSpaces Web typically does not log the content of web pages visited for privacy reasons.
Which AWS service can be used to create a custom dashboard to visualise Amazon WorkSpaces Web usage metrics?,Amazon CloudWatch,AWS CloudTrail,Amazon Athena,Amazon QuickSight,Amazon CloudWatch can be used to create custom dashboards to visualise WorkSpaces Web usage metrics.
What is the purpose of configuring an 'Endpoint' within the Amazon WorkSpaces Web configuration?,To define the URL of the web application being accessed,To specify the geographic region where the service is deployed,To configure the network settings,To set up authentication parameters,An 'Endpoint' in WorkSpaces Web defines the URL of the web application that users will access through the service.
When should you consider using Amazon WorkSpaces Web instead of a full Amazon WorkSpaces virtual desktop?,When users only need access to web-based applications and not a full desktop environment,When users require high-performance computing capabilities,When users need to install custom software,When users need to access local hardware devices,WorkSpaces Web is suitable when users primarily need access to web applications and a full desktop environment is not necessary.
How does Amazon WorkSpaces Web interact with client-side certificates required by some web applications?,It can be configured to pass through client-side certificates for authentication,It automatically strips client-side certificates for security reasons,It requires the client-side certificate to be installed on the user's local machine,It only supports web applications without client-side certificate requirements,WorkSpaces Web can be configured to securely pass through client-side certificates for authentication with web applications.
What is the primary reason for isolating the browser session in Amazon WorkSpaces Web?,To protect internal networks and resources from potential threats,To improve browser performance,To reduce bandwidth consumption,To simplify browser management,Isolating the browser session in WorkSpaces Web protects internal networks and resources from potential threats originating from the web.
How do you ensure that users are only accessing approved web applications through Amazon WorkSpaces Web?,By configuring access rules and browser policies,By monitoring user activity logs,By implementing network firewalls,By encrypting all web traffic,You ensure access to only approved web applications by configuring access rules and browser policies within WorkSpaces Web.
Which Amazon WorkSpaces Web component handles the actual rendering of the web application?,The Streaming Instance,The Portal,The Identity Provider,The Load Balancer,The Streaming Instance is responsible for rendering the web application in Amazon WorkSpaces Web.
What is the purpose of configuring a 'Trust Store' in Amazon WorkSpaces Web?,To manage and validate SSL/TLS certificates for secure connections,To store user credentials,To manage browser policies,To configure network settings,A 'Trust Store' in WorkSpaces Web manages and validates SSL/TLS certificates to ensure secure connections to web applications.
You are troubleshooting an issue where users are unable to access a specific web application through Amazon WorkSpaces Web. What is the first step you should take?,Verify the access rules and browser policies are correctly configured,Check the user's network connectivity,Reboot the streaming instance,Update the browser version,The first step is to verify that the access rules and browser policies within WorkSpaces Web are correctly configured to allow access to the application.
What security benefit does Amazon WorkSpaces Web provide for organisations using SaaS applications?,"It centralises access and control over SaaS applications, reducing the risk of shadow IT",It automates user provisioning for SaaS applications,It eliminates the need for user passwords for SaaS applications,It encrypts all data stored in SaaS applications,"WorkSpaces Web provides centralised access and control over SaaS applications, reducing the risk of shadow IT by ensuring access is managed and secured through a central point."
What is the primary function of Amazon WorkSpaces Web?,Provides secure web browser access to internal applications,Provides virtual desktop infrastructure,Provides cloud-based storage,Provides a code development environment,WorkSpaces Web allows users to securely access internal web applications without requiring a full virtual desktop.
"In Amazon WorkSpaces Web, what is a 'portal'?",A secure access point for internal web applications,A user's home directory,A virtual machine,A physical server,A portal in WorkSpaces Web is the secure gateway through which users access their designated internal web applications.
Which AWS service does Amazon WorkSpaces Web primarily integrate with for identity management?,AWS Identity and Access Management (IAM),Amazon CloudWatch,Amazon S3,Amazon EC2,WorkSpaces Web relies on IAM (or an IAM integrated external identity provider) for user authentication and authorisation.
What is the benefit of using Amazon WorkSpaces Web over traditional VPN solutions for accessing web applications?,Improved security posture by isolating browser activity,Higher network bandwidth,Lower storage costs,Greater computational power,"WorkSpaces Web isolates browsing activity within a secure container, reducing the attack surface compared to VPNs that grant broader network access."
Which of the following is a key security feature offered by Amazon WorkSpaces Web?,Browser isolation,Antivirus software,Data encryption at rest,Physical security,"WorkSpaces Web's browser isolation feature ensures that all web browsing activity is contained within a secure, isolated environment, preventing potential threats from reaching the user's device or the internal network."
What type of applications are best suited for Amazon WorkSpaces Web?,Internal web applications,Desktop applications,Mobile applications,Gaming applications,WorkSpaces Web is designed primarily for providing secure access to internal web applications.
How does Amazon WorkSpaces Web pricing typically work?,Pay-as-you-go based on usage,Flat monthly fee per user,One-time license purchase,Free of charge,"WorkSpaces Web uses a pay-as-you-go pricing model based on actual usage, making it cost-effective for many organisations."
Which AWS Region is NOT a supported region for Amazon WorkSpaces Web at initial launch?,Africa (Cape Town),US East (N. Virginia),Europe (Ireland),Asia Pacific (Sydney),"At initial launch, the Africa (Cape Town) region was not supported, but this may change."
"When configuring Amazon WorkSpaces Web, what is the purpose of setting up a 'browser policy'?",To define allowed and blocked websites,To define screen resolution,To define user access levels,To define the user's password complexity,"A browser policy in WorkSpaces Web lets you control which websites users can access, improving security and compliance."
What level of administrative access is granted to the user within the WorkSpaces Web browser?,No administrative access,Full administrative access,Limited administrative access,Root access,"Users have no administrative access within the WorkSpaces Web browser instance, further enhancing security."
How does Amazon WorkSpaces Web handle user data generated during a browsing session?,Data is automatically deleted after the session ends,Data is stored permanently,Data is stored for 24 hours,Data is backed up to Amazon S3,"User data generated during a WorkSpaces Web browsing session is typically deleted when the session ends, ensuring no residual data remains."
Which of the following is a benefit of using Amazon WorkSpaces Web for BYOD (Bring Your Own Device) scenarios?,Enhanced security for corporate resources,Faster application performance,Increased storage capacity,Simplified software deployment,"WorkSpaces Web provides a secure way for employees to access corporate resources from their personal devices, without compromising security."
What type of web browser is used in Amazon WorkSpaces Web?,"A secure, isolated browser provided by AWS",Google Chrome,Mozilla Firefox,Microsoft Edge,"WorkSpaces Web uses a secure, isolated browser environment managed by AWS to ensure security and prevent malware."
How can you monitor the usage of Amazon WorkSpaces Web?,Using Amazon CloudWatch,Using Amazon S3,Using AWS Config,Using Amazon Inspector,"Amazon CloudWatch can be used to monitor the usage and performance of WorkSpaces Web, including session duration and resource consumption."
What is one of the key advantages of Amazon WorkSpaces Web for organisations with remote workers?,Secure and controlled access to internal resources,Offline access to applications,Reduced hardware costs,Improved printing capabilities,WorkSpaces Web allows remote workers to securely access internal web applications from any device with a web browser.
Which component is mandatory for Amazon WorkSpaces Web setup?,An Identity Provider (IdP),An Amazon EC2 Instance,An Amazon S3 Bucket,An Amazon RDS Database,An Identity Provider (IdP) is essential for user authentication and authorisation within WorkSpaces Web.
What is the maximum session duration limit for Amazon WorkSpaces Web?,8 hours,2 hours,12 hours,24 hours,The maximum session duration limit for Amazon WorkSpaces Web is 8 hours to help balance usability and security.
What is the purpose of 'network settings' within the Amazon WorkSpaces Web configuration?,To control network access for the WorkSpaces Web instances,To configure user permissions,To set up multi-factor authentication,To define browser policies,"Network settings are used to define the networking configuration for the WorkSpaces Web environment, enabling secure and controlled access to internal resources."
How does Amazon WorkSpaces Web contribute to compliance efforts?,By providing a secure and isolated browsing environment,By managing software licenses,By providing data encryption at rest,By automating patching,WorkSpaces Web helps organisations meet compliance requirements by providing a secure and isolated browsing environment for accessing sensitive data and applications.
What is the role of the 'portal endpoint' in Amazon WorkSpaces Web?,The URL users access to launch their secure web browser sessions,The URL for the WorkSpaces Web admin console,The URL for downloading the WorkSpaces Web client,The URL for accessing the AWS Marketplace,The portal endpoint is the URL that users use to initiate their secure WorkSpaces Web browsing sessions.
Which of these is not a standard feature of Amazon WorkSpaces Web?,File storage within the browser session,Browser isolation,Access control policies,Session management,WorkSpaces Web does not include persistent file storage within the browser session. Data is typically cleared after the session ends.
What happens when a user attempts to access a website blocked by the WorkSpaces Web browser policy?,The user sees a blocked page,The browser crashes,The user is redirected to the homepage,The website opens in a new tab,"When a user attempts to access a blocked website, WorkSpaces Web will display a blocked page, preventing access."
How can you ensure users connect to Amazon WorkSpaces Web via a specific network?,Using Network Access Control Lists (NACLs) or Security Groups,Using IAM policies,Using Route 53,Using VPC endpoints,You can control network access to WorkSpaces Web using NACLs or Security Groups to restrict traffic based on IP addresses or network ranges.
What kind of user authentication does Amazon WorkSpaces Web support?,SAML 2.0-based federation,Password-based authentication only,Biometric authentication,Multi-factor authentication only,WorkSpaces Web supports SAML 2.0-based federation allowing you to integrate with existing identity providers.
Which of the following AWS services can you use to centrally manage and deploy Amazon WorkSpaces Web policies?,AWS IAM Identity Center (successor to AWS Single Sign-On),AWS Systems Manager,AWS Config,AWS CloudTrail,"AWS IAM Identity Center (successor to AWS Single Sign-On) is commonly used for central user and access management, including WorkSpaces Web policies."
What is the main purpose of the 'trust policy' when configuring Amazon WorkSpaces Web?,To define the trust relationship between WorkSpaces Web and your identity provider,To define the maximum session duration,To define the regions where WorkSpaces Web can be deployed,To define which websites are allowed,The trust policy establishes the secure connection and trust relationship between WorkSpaces Web and your identity provider (IdP).
Which of the following scenarios is well-suited for Amazon WorkSpaces Web?,Providing secure access to web-based training materials for contractors,Providing access to full desktop applications,Hosting a public-facing website,Providing access to high-performance computing applications,"WorkSpaces Web is ideal for providing secure access to web-based training materials, especially for contractors who need controlled access to internal resources."
What is one way to minimise the cost of running Amazon WorkSpaces Web?,Optimise session duration,Increase browser storage,Disable browser isolation,Use a less secure browser,Optimising session duration and limiting usage to essential tasks can help minimise costs associated with WorkSpaces Web.
What is the purpose of setting up a custom domain within Amazon WorkSpaces Web?,To allow users to access the WorkSpaces Web portal using a branded URL,To improve network performance,To encrypt data in transit,To restrict user access based on location,"Setting up a custom domain allows you to provide a branded URL for your WorkSpaces Web portal, enhancing the user experience and improving brand recognition."
Which of the following is NOT a typical use case for Amazon WorkSpaces Web?,Providing access to legacy applications,Providing access to virtual machines,Providing access to internal web applications,Providing secure browsing from unmanaged devices,"Providing access to virtual machines is not a standard use case for Amazon WorkSpaces Web, which focuses on providing secure access to web applications."
What is the key difference between Amazon WorkSpaces and Amazon WorkSpaces Web?,"WorkSpaces provides full virtual desktops, while WorkSpaces Web provides secure web browser access",WorkSpaces is more expensive than WorkSpaces Web,"WorkSpaces is only for Windows, while WorkSpaces Web is for all operating systems",WorkSpaces is less secure than WorkSpaces Web,"Amazon WorkSpaces provides full virtual desktops, while WorkSpaces Web provides secure web browser access to internal web applications."
What happens to browser extensions installed within an Amazon WorkSpaces Web session?,They are typically not persistent and are removed after the session ends,They are automatically installed on the user's local machine,They are stored permanently in Amazon S3,They are available across all user sessions,Browser extensions installed within a WorkSpaces Web session are typically non-persistent and are removed when the session ends.
What is the impact of enabling multi-factor authentication (MFA) for Amazon WorkSpaces Web?,Increases the security of user logins,Increases network bandwidth usage,Decreases user productivity,Decreases storage costs,"Enabling MFA enhances the security of user logins by requiring a second factor of authentication, protecting against password compromise."
How can you integrate Amazon WorkSpaces Web with an existing on-premises Active Directory?,Using AWS Directory Service AD Connector,Using AWS IAM,Using AWS CloudHSM,Using AWS KMS,"AWS Directory Service AD Connector allows you to connect your on-premises Active Directory to AWS, enabling seamless integration with WorkSpaces Web for user authentication."
Which of the following statements is true about Amazon WorkSpaces Web?,It provides a secure and isolated browsing environment,It requires a complex VPN setup,It allows users to install any software,It provides direct access to the underlying operating system,"Amazon WorkSpaces Web provides a secure and isolated browsing environment, protecting users and corporate resources from potential threats."
What is the recommended way to update the browser software within Amazon WorkSpaces Web?,AWS automatically manages browser updates,Administrators must manually update the browser software,Users can update the browser software themselves,The browser software cannot be updated,"AWS automatically manages browser updates within WorkSpaces Web, ensuring that users have access to the latest security patches and features."
You have a team of developers who need secure access to internal documentation and web-based tools. Which AWS service is best suited for this?,Amazon WorkSpaces Web,Amazon EC2,Amazon S3,Amazon Lambda,Amazon WorkSpaces Web is the best choice for providing secure and controlled access to internal documentation and web-based tools for developers.
What type of network traffic does Amazon WorkSpaces Web typically handle?,HTTPS (web) traffic,SMTP (email) traffic,FTP (file transfer) traffic,SSH (secure shell) traffic,Amazon WorkSpaces Web primarily handles HTTPS traffic for accessing web applications.
You need to grant temporary access to an external vendor for a specific internal web application. How can you achieve this using Amazon WorkSpaces Web?,By configuring a temporary IAM role for the vendor,By creating a new user account in your Active Directory,By sharing your AWS account credentials,By disabling security policies,"By configuring a temporary IAM role that grants the vendor access to the specific WorkSpaces Web portal, providing secure and controlled access."
What is a limitation of Amazon WorkSpaces Web compared to a traditional desktop environment?,Limited functionality compared to desktop applications,Inability to access files on the local machine,Lack of internet access,No support for multi-factor authentication,WorkSpaces Web focuses on web-based applications and has limited functionality compared to full desktop applications; it is not designed to run native desktop applications.
Which component of Amazon WorkSpaces Web is responsible for managing user sessions and access policies?,WorkSpaces Web Portal,AWS Identity and Access Management (IAM),Amazon EC2,Amazon S3,"The WorkSpaces Web Portal is responsible for managing user sessions, defining access policies, and controlling which internal web applications users can access."
What is the advantage of using Amazon WorkSpaces Web for providing access to legacy web applications?,It allows access to legacy applications without requiring modifications,It automatically modernises the legacy applications,It provides offline access to legacy applications,It removes all security vulnerabilities from the legacy applications,"WorkSpaces Web allows access to legacy web applications without requiring modifications, providing a secure way to use outdated applications."
How does Amazon WorkSpaces Web protect against data exfiltration?,By isolating browser sessions and preventing data copying,By encrypting all data at rest,By requiring multi-factor authentication,By providing real-time monitoring of user activity,WorkSpaces Web protects against data exfiltration by isolating browser sessions and preventing users from copying data or downloading files to their local devices.
Which of the following is NOT a typical use case for Amazon WorkSpaces Web?,Securely accessing internal web applications,Providing a sandbox environment for testing websites,Remotely accessing full desktop applications,Allowing secure access from unmanaged devices,"Remotely accessing full desktop applications is not a typical use case, as WorkSpaces Web focuses on providing secure access to web applications."
What should you consider when choosing the appropriate network settings for Amazon WorkSpaces Web?,The network access requirements of the internal web applications,The number of users accessing the applications,The size of the web applications,The location of the users,"You should consider the network access requirements of the internal web applications, including the necessary ports and protocols, when configuring the network settings for WorkSpaces Web."
How can you track user activity within Amazon WorkSpaces Web?,By integrating with Amazon CloudTrail,By integrating with Amazon S3,By integrating with Amazon EC2,By integrating with Amazon RDS,"You can track user activity within WorkSpaces Web by integrating with Amazon CloudTrail, which captures API calls and user actions for auditing and security purposes."
What is the primary reason for using a 'federated identity' with Amazon WorkSpaces Web?,To integrate with existing identity providers for seamless user authentication,To encrypt data at rest,To improve network performance,To reduce the cost of WorkSpaces Web,"The primary reason is to integrate with existing identity providers, such as Active Directory or Okta, for seamless user authentication and simplified user management."
Which of the following features is most effective in preventing malware from infecting a user's device while using Amazon WorkSpaces Web?,Browser Isolation,Multi-Factor Authentication,Data Encryption at Rest,Network Access Control Lists,Browser isolation creates a secure container that prevents malware within the browsing session from affecting the user's device or the organisation's network.
What is the primary use case for Amazon WorkSpaces Web?,"Providing secure, browser-based access to internal web applications",Hosting static websites,Running virtual machines,Providing desktop-as-a-service,WorkSpaces Web focuses on secure access to internal web apps without the complexity of full virtual desktops.
Which AWS service does Amazon WorkSpaces Web integrate with for identity management?,AWS Identity and Access Management (IAM),Amazon Cognito,Amazon Connect,AWS Directory Service,WorkSpaces Web integrates with IAM for managing user identities and access permissions.
What type of web browsers are supported by Amazon WorkSpaces Web?,Modern HTML5 compatible browsers,Internet Explorer 11 only,Text-based browsers only,Only the Amazon Silk browser,WorkSpaces Web is designed to work with modern web browsers that support HTML5.
What is the purpose of a portal in Amazon WorkSpaces Web?,To define the web applications accessible to users,To manage user profiles,To configure network settings,To create virtual desktops,"Portals define the web applications users can access, how they're authenticated, and security policies applied."
Which authentication method can be configured for Amazon WorkSpaces Web portals?,SAML 2.0,Kerberos,LDAP,NTLM,"WorkSpaces Web supports SAML 2.0 for federated authentication, allowing users to log in using their existing credentials."
How does Amazon WorkSpaces Web help prevent data leakage from internal web applications?,By preventing users from copying and pasting data,By automatically encrypting all data at rest,By limiting the number of concurrent users,By blocking all internet access,"WorkSpaces Web enables policies that restrict actions such as copying, pasting, printing, and downloading from the browser session."
Which of the following is a key benefit of using Amazon WorkSpaces Web over traditional VPNs?,Simplified management and reduced operational overhead,Lower bandwidth requirements,Direct access to the EC2 instance,Improved offline access,WorkSpaces Web streamlines management by eliminating the need for VPN infrastructure and client software.
What is the role of the Amazon WorkSpaces Web Gateway?,To provide a secure connection between users and internal web applications,To manage user authentication,To store user data,To provide load balancing,The WorkSpaces Web Gateway is the central point for secure connectivity between users and web apps.
Which of the following policies can be configured within an Amazon WorkSpaces Web portal?,Data Loss Prevention (DLP) policies,Operating System update policies,Anti-virus software policies,Hardware firewall policies,WorkSpaces Web portals allow for DLP policies to control user actions and prevent data exfiltration.
How does Amazon WorkSpaces Web impact the security posture of an organisation's internal web applications?,It enhances security by isolating web applications from the user's local device,It weakens security by exposing web applications to the internet,It has no impact on security,It increases the cost of security,WorkSpaces Web enhances security by isolating the browser session and enforcing security policies.
What is a common use case for Amazon WorkSpaces Web in regulated industries?,Providing secure access to sensitive data for remote workers,Allowing developers to debug applications,Hosting public websites,Running machine learning models,Regulated industries use WorkSpaces Web to secure access to sensitive data and ensure compliance.
How does Amazon WorkSpaces Web contribute to a zero-trust security model?,By isolating access to web applications and enforcing granular policies,By granting full access to all internal resources,By relying on perimeter-based security,By disabling all security features,WorkSpaces Web supports zero-trust by isolating access and enforcing granular policies based on user identity and context.
What is the pricing model for Amazon WorkSpaces Web?,"Pay-as-you-go, based on usage",Fixed monthly fee per user,One-time license fee,Free for all AWS customers,"WorkSpaces Web charges based on actual usage, allowing for cost optimisation."
Which of the following actions can be controlled using policies within Amazon WorkSpaces Web?,"Copying and pasting data, printing, and downloading files",Controlling the operating system of the client device,Limiting the amount of memory available to the browser,Installing software on the user's local device,"WorkSpaces Web policies allow control over actions like copying, pasting, printing, and downloading data."
What is the relationship between Amazon WorkSpaces Web and Amazon WorkSpaces?,"WorkSpaces Web provides browser-based access, while WorkSpaces provides full virtual desktops",WorkSpaces Web is a replacement for Amazon WorkSpaces,Amazon WorkSpaces Web is a feature of Amazon WorkSpaces,They are unrelated services,WorkSpaces Web offers browser-based access as a lighter-weight alternative to the full virtual desktops provided by WorkSpaces.
What user information is typically required to access an Amazon WorkSpaces Web portal?,Username and password,IP address,MAC address,Serial number,"Users need a username and password, often integrated with SAML, to access WorkSpaces Web portals."
What benefit does Amazon WorkSpaces Web offer in terms of client device management?,Reduced device management overhead,Increased device management complexity,Required installation of specific software,Dependence on a specific operating system,"WorkSpaces Web reduces device management by providing access through a browser, eliminating the need to manage client software."
How does Amazon WorkSpaces Web protect against browser-based threats?,By isolating the browser session in a secure environment,By installing anti-virus software on the client device,By relying on the user's browser security settings,By blocking all external websites,"WorkSpaces Web isolates the browser session, mitigating the impact of browser-based threats."
What is the typical setup process for Amazon WorkSpaces Web?,"Create a portal, configure identity provider, define web application access","Install software on the user's device, configure network settings, launch the browser","Create an EC2 instance, install a web server, configure DNS records","Upload web application code, configure a database, launch the application","The typical setup involves creating a portal, configuring identity, and defining web application access rules."
What network configuration is typically required for Amazon WorkSpaces Web to access internal web applications?,"Private network connectivity, such as AWS PrivateLink",Public internet access,VPN connection,Direct Connect,"WorkSpaces Web uses private network connectivity, like PrivateLink, to securely access internal applications without exposing them to the internet."
What is the primary benefit of using session persistence in Amazon WorkSpaces Web?,Maintaining user sessions across multiple browser tabs or windows,Improving network performance,Reducing storage costs,Enhancing security,"Session persistence allows users to maintain their sessions across different browser tabs and windows, enhancing the user experience."
Which AWS service can be used to monitor the performance and usage of Amazon WorkSpaces Web?,Amazon CloudWatch,AWS CloudTrail,AWS Config,Amazon Inspector,CloudWatch provides metrics and logs for monitoring WorkSpaces Web performance and usage.
How does Amazon WorkSpaces Web support compliance requirements like HIPAA or PCI DSS?,By providing a secure and controlled environment for accessing sensitive data,By automatically encrypting all data,By managing user accounts,By preventing all internet access,WorkSpaces Web can help organisations meet compliance requirements by securing access to sensitive data and enforcing policies.
What is the maximum session duration that can be configured in Amazon WorkSpaces Web?,Up to 24 hours,Up to 1 hour,Up to 8 hours,Unlimited,WorkSpaces Web allows you to configure session durations up to 24 hours.
How can you provide users with access to specific websites using Amazon WorkSpaces Web?,By configuring web application access rules in the portal,By modifying the user's browser settings,By installing browser extensions,By creating a custom operating system image,Web application access rules within the portal define which websites users can access.
What is the impact of using Amazon WorkSpaces Web on an organisation's security perimeter?,It shrinks the security perimeter by isolating browser sessions,It expands the security perimeter by exposing internal applications,It has no impact on the security perimeter,It makes the security perimeter more complex,WorkSpaces Web shrinks the security perimeter by isolating browser sessions and controlling access.
What is the role of the browser isolation technology in Amazon WorkSpaces Web?,To prevent malware and other threats from reaching the user's device,To improve browser performance,To reduce network bandwidth usage,To provide offline access,Browser isolation prevents malware and threats from infecting the user's local device by running the browser session in a secure environment.
How can you automate the deployment and configuration of Amazon WorkSpaces Web?,Using AWS CloudFormation or Infrastructure as Code (IaC) tools,Using the AWS Management Console only,Using a manual configuration process,Using a third-party configuration management tool,CloudFormation and other IaC tools allow you to automate the deployment and configuration of WorkSpaces Web.
What is the purpose of configuring network settings in Amazon WorkSpaces Web?,To ensure secure and reliable connectivity to internal web applications,To improve browser performance,To reduce network bandwidth usage,To prevent access to external websites,"Network settings ensure secure connectivity to internal web applications, often through PrivateLink or other private networking options."
How does Amazon WorkSpaces Web support multi-factor authentication (MFA)?,By integrating with identity providers that support MFA,By providing its own built-in MFA solution,By requiring users to install a separate MFA application,By disabling MFA,"WorkSpaces Web relies on identity providers, like those using SAML 2.0, to handle MFA."
What is the benefit of using Amazon WorkSpaces Web for Bring Your Own Device (BYOD) environments?,It allows users to access internal web applications securely without installing software on their personal devices,It requires users to install specific software on their personal devices,It prevents users from accessing internal web applications on their personal devices,It reduces the security of the organisation's network,WorkSpaces Web allows secure access on personal devices without requiring software installation.
How does Amazon WorkSpaces Web help improve end-user productivity?,By providing a consistent and secure browsing experience,By providing faster internet speeds,By removing security measures,By simplifying device management,WorkSpaces Web improves productivity with a consistent and secure browsing experience across different devices.
What is the benefit of using Amazon WorkSpaces Web with existing virtual desktop infrastructure (VDI)?,It provides a lightweight alternative for accessing web applications,It replaces the VDI environment,It integrates directly with the VDI environment,It increases the cost of the VDI environment,"WorkSpaces Web provides a lighter-weight option for accessing web applications, complementing existing VDI solutions."
What type of logging is available in Amazon WorkSpaces Web?,Audit logging of user activity and policy enforcement,Operating system event logging,Network traffic logging,Application performance logging,WorkSpaces Web provides audit logs of user activity and policy enforcement actions.
How can you customise the user experience in Amazon WorkSpaces Web?,By customising the browser appearance and behaviour,By customising the user's operating system,By installing browser extensions,By changing the user's hardware,Customising the browser appearance and behaviour within the isolated session is possible through some configuration.
What is the purpose of the clipboard redirection feature in Amazon WorkSpaces Web?,To control the flow of data between the browser session and the user's local device,To improve browser performance,To reduce network bandwidth usage,To prevent users from accessing the clipboard,"Clipboard redirection controls how data can be copied and pasted between the browser session and the local device, preventing data leakage."
How does Amazon WorkSpaces Web integrate with security information and event management (SIEM) systems?,By providing logs that can be ingested into SIEM systems,By providing direct access to user data,By preventing all access to security logs,By replacing the SIEM system,WorkSpaces Web integrates with SIEM systems by providing logs for security analysis and monitoring.
What is the impact of Amazon WorkSpaces Web on application compatibility?,It improves application compatibility by providing a consistent browser environment,It reduces application compatibility by limiting browser features,It has no impact on application compatibility,It requires applications to be rewritten,"WorkSpaces Web aims to provide a consistent environment, potentially improving compatibility for some web applications."
How does Amazon WorkSpaces Web handle browser updates?,Browser updates are managed automatically by Amazon,Users are responsible for updating their browsers,The browser cannot be updated,The operating system must be updated,Amazon manages browser updates within the WorkSpaces Web environment.
What is the role of the Domain Name System (DNS) in Amazon WorkSpaces Web?,To resolve the addresses of internal web applications,To encrypt network traffic,To manage user authentication,To provide load balancing,DNS is used to resolve the addresses of the internal web applications that users need to access.
How can you ensure high availability for Amazon WorkSpaces Web?,By deploying the service in multiple AWS Availability Zones,By installing the service on multiple servers,By configuring a backup service,High availability is managed by AWS,Amazon handles the high availability of WorkSpaces Web behind the scenes.
What is the primary purpose of using the PrivateLink service in conjunction with Amazon WorkSpaces Web?,To secure traffic between WorkSpaces Web and internal resources,To improve browser performance,To reduce network bandwidth usage,To provide offline access,PrivateLink allows secure communication to internal resources without exposing traffic to the public internet.
How does Amazon WorkSpaces Web help with application modernisation efforts?,By providing a secure and controlled environment for accessing legacy web applications,By automatically rewriting legacy applications,By replacing legacy applications with modern alternatives,By preventing access to legacy applications,"WorkSpaces Web allows secure access to legacy applications, enabling a smoother transition during modernisation efforts."
What is the relationship between Amazon WorkSpaces Web and serverless computing?,WorkSpaces Web leverages serverless technologies to manage the browser sessions,WorkSpaces Web is a serverless compute service,Serverless computing is not related to WorkSpaces Web,WorkSpaces Web hosts serverless applications,WorkSpaces Web leverages serverless technologies for its infrastructure and session management.
What is the difference between Amazon WorkSpaces Web and Amazon AppStream 2.0?,"WorkSpaces Web provides access to web applications, while AppStream 2.0 provides access to full desktop applications",WorkSpaces Web is a replacement for Amazon AppStream 2.0,Amazon WorkSpaces Web is a feature of Amazon AppStream 2.0,They are unrelated services,"WorkSpaces Web is intended for browser based access to web applications, while AppStream provides streamed access to desktop applications."
What is a typical use case for implementing content restrictions with Amazon Workspaces Web?,Preventing access to websites deemed malicious or inappropriate,Limiting access to online gaming sites,Improving network performance,Increasing user productivity,Content restriction policies ensure that users only access web resources aligned with business policy and security posture.
How can you retrieve logs from an Amazon Workspaces Web environment for security monitoring?,Amazon CloudWatch,Amazon S3,AWS CloudTrail,AWS Config,Logs are stored in CloudWatch so you can monitor for malicious activity.
Which configuration settings allows you to create a customized message which is displayed to users before they use Amazon Workspaces Web?,Custom Branding,Custom Authentication,Custom Permissions,Custom VPC,A custom brand can improve trust and provides context to users about the platform they are using.
"You need to provide a secure way for contractors to access a client's web-based applications, without giving them full access to the corporate network. Which Amazon service is the MOST suitable for this scenario?",Amazon WorkSpaces Web,Amazon Connect,Amazon Workspaces,Amazon EC2,WorkSpaces Web is the best solution here as it will provide secure access to the applications without exposing the corporate network.
What is the primary purpose of Amazon WorkSpaces Web?,To provide secure access to internal websites and web applications from any device.,To host and manage virtual desktops.,To provide a platform for developing web applications.,To store and manage user files in the cloud.,Amazon WorkSpaces Web's main goal is to provide secure web access to internal resources without the complexity of a full VDI.
"In Amazon WorkSpaces Web, what is a 'portal'?",A collection of browser settings and access policies.,A virtual desktop environment.,A dedicated physical server.,A networking component.,A portal in WorkSpaces Web defines the user experience and access controls for web resources.
Which AWS service is commonly used to manage user authentication and authorisation for Amazon WorkSpaces Web?,AWS Identity and Access Management (IAM),Amazon Cloud Directory,Amazon Cognito,AWS Directory Service,"IAM is typically used to define user roles and permissions, controlling access to WorkSpaces Web resources."
What type of web browsers are supported by Amazon WorkSpaces Web?,Modern browsers supporting WebRTC.,Only Internet Explorer.,Only Chrome.,Any browser.,WorkSpaces Web requires browsers that support WebRTC for streaming the web session.
How does Amazon WorkSpaces Web enhance security when accessing internal web applications?,By isolating web sessions in a secure container.,By providing encrypted storage.,By managing network traffic.,By filtering all user data.,"WorkSpaces Web isolates web sessions, preventing direct access to the user's device and network, enhancing security."
What is a key benefit of using Amazon WorkSpaces Web over traditional VPN solutions for accessing internal websites?,Improved security and simplified management.,Faster network speeds.,Lower storage costs.,Easier application deployment.,"WorkSpaces Web simplifies management and enhances security by isolating web access, unlike traditional VPNs."
Which of the following is a component of the Amazon WorkSpaces Web architecture?,Streaming instances.,Virtual machines.,Physical servers.,Containers.,Streaming instances deliver the web browser session to the user.
What is the role of 'network settings' in Amazon WorkSpaces Web?,To define network access policies for the web applications.,To configure internet access for users.,To manage firewall rules.,To set up VPN connections.,"Network settings define how WorkSpaces Web accesses your internal web applications, including VPC and subnet configuration."
How can you control which websites are accessible through Amazon WorkSpaces Web?,By configuring URL filtering rules.,By modifying DNS settings.,By using browser extensions.,By blocking IP addresses.,URL filtering rules in WorkSpaces Web allow administrators to specify which websites users can access.
What is the purpose of the Amazon WorkSpaces Web administrative console?,"To manage portals, users, and security settings.",To deploy web applications.,To monitor network traffic.,To configure virtual machines.,"The administrative console is used to configure and manage WorkSpaces Web resources, including portals, users, and security."
What is the benefit of streaming web applications through Amazon WorkSpaces Web?,It reduces the risk of data leakage to personal devices.,It speeds up application loading times.,It increases storage capacity.,It enables offline access to web applications.,"Streaming web apps prevents sensitive data from being stored on users' devices, reducing data leakage."
Which of the following is a typical use case for Amazon WorkSpaces Web?,Providing secure access to internal CRM systems.,Hosting public-facing websites.,Running compute-intensive applications.,Storing large datasets.,Securely providing access to internal CRM systems is a common use case for WorkSpaces Web.
What type of logging and auditing capabilities are available with Amazon WorkSpaces Web?,Logging of user activity and access events.,Full packet capture.,Real-time video recording.,System performance monitoring.,WorkSpaces Web provides logging of user activity and access events for auditing and compliance.
Which AWS region is required when deploying Amazon WorkSpaces Web?,Any AWS region that supports it.,Only the US East (N. Virginia) region.,Only the EU (Ireland) region.,Only the Asia Pacific (Tokyo) region.,WorkSpaces Web can be deployed in any AWS region where it is supported.
How does Amazon WorkSpaces Web integrate with existing identity providers (IdPs)?,Through SAML 2.0 or OpenID Connect.,Through proprietary protocols.,It does not integrate with IdPs.,Through direct database connections.,WorkSpaces Web integrates with IdPs using standard protocols like SAML 2.0 or OpenID Connect.
What is the purpose of the 'User Settings' within an Amazon WorkSpaces Web portal?,To configure user-specific browser settings and preferences.,To manage user accounts.,To control network access for users.,To define storage quotas for users.,"User Settings define the browser experience for users in terms of cookies, history and other personal settings."
What security benefits does Amazon WorkSpaces Web offer against zero-day exploits?,"It isolates the browser environment, limiting the impact of exploits.",It automatically patches vulnerabilities.,It uses advanced antivirus software.,It blocks all network traffic.,The isolated browser environment in WorkSpaces Web reduces the risk posed by zero-day exploits.
How can you scale Amazon WorkSpaces Web to support a growing number of users?,By adjusting the number of streaming instances.,By increasing storage capacity.,By upgrading network bandwidth.,By adding more portals.,WorkSpaces Web scales by automatically adjusting the number of streaming instances based on user demand.
What is the purpose of 'Trust Stores' in Amazon WorkSpaces Web?,To manage trusted certificates for web applications.,To store user credentials.,To store network configuration settings.,To store application code.,"Trust Stores in WorkSpaces Web allow you to manage trusted certificates, ensuring secure communication with web applications."
What is the typical pricing model for Amazon WorkSpaces Web?,Pay-as-you-go based on usage.,Fixed monthly fee per user.,One-time license fee.,Free of charge.,WorkSpaces Web uses a pay-as-you-go pricing model based on usage.
"When troubleshooting connectivity issues with Amazon WorkSpaces Web, what should you check first?",Network configuration and security group settings.,User credentials.,Browser version.,Operating system compatibility.,Network configuration and security group settings are crucial for ensuring connectivity between WorkSpaces Web and internal resources.
What level of administrative access is needed to configure an Amazon WorkSpaces Web portal?,Sufficient IAM permissions to manage WorkSpaces Web resources.,Root access to the AWS account.,Physical access to the AWS data centre.,No administrative access is required.,You need IAM permissions to manage WorkSpaces Web resources to configure a portal.
How does Amazon WorkSpaces Web help comply with data residency requirements?,By ensuring that data remains within a specific AWS region.,By encrypting all data in transit.,By providing detailed audit logs.,By deleting data automatically after a set period.,WorkSpaces Web helps comply with data residency requirements by ensuring data remains within the configured AWS region.
What type of resource in Amazon WorkSpaces Web defines how a browser instance interacts with web applications?,Browser Policy,Firewall Rule,Security Group,Network ACL,A Browser Policy determines how the streaming browser instance handles specific websites and applications.
How do you restrict access to certain websites based on the user's group membership in Amazon WorkSpaces Web?,Configure URL filtering rules based on IAM roles.,Modify the DNS settings.,Use browser extensions.,Block IP addresses.,IAM roles attached to the user are used to define URL filtering rules.
Which of the following is a key benefit of using Amazon WorkSpaces Web for contractors or temporary workers?,Provides secure access to internal resources without providing full network access.,Faster deployment of web applications.,Lower storage costs.,Easy access to public websites.,Providing secure access to internal resources without full network access is a major benefit when using WorkSpaces Web for contractors.
What is the impact on end-user devices when accessing web applications through Amazon WorkSpaces Web?,No applications or data are stored on the end-user device.,Applications are installed locally.,Data is cached locally.,Applications run on the end-user's hardware.,"No apps or data are stored, as they are streamed from AWS."
What is the role of VPC endpoints in Amazon WorkSpaces Web?,To provide private connectivity to AWS services.,To provide public internet access.,To encrypt network traffic.,To manage firewall rules.,VPC endpoints enable WorkSpaces Web to securely connect to other AWS services within your VPC.
How can you monitor the performance and health of your Amazon WorkSpaces Web deployment?,Using Amazon CloudWatch metrics and logs.,Using the WorkSpaces Web console.,Using third-party monitoring tools.,There is no monitoring available.,CloudWatch metrics and logs provide insights into the performance and health of WorkSpaces Web deployments.
Which type of certificate is typically used to secure the communication between the user's browser and Amazon WorkSpaces Web?,SSL/TLS certificate.,Self-signed certificate.,X.509 certificate.,Kerberos ticket.,SSL/TLS certificates are used to encrypt communication between the user's browser and WorkSpaces Web.
What is the recommended method for deploying Amazon WorkSpaces Web at scale?,Using AWS CloudFormation templates.,Manually configuring each portal.,Using a custom script.,Using the AWS Management Console.,CloudFormation templates enable automated and consistent deployment of WorkSpaces Web at scale.
How can you ensure that users are using the latest version of the browser in Amazon WorkSpaces Web?,WorkSpaces Web automatically updates the browser.,Users must manually update the browser.,Administrators must manually update the browser.,Browser updates are not supported.,WorkSpaces Web automatically manages browser updates to ensure users have the latest version.
What is the purpose of 'Session persistence' in Amazon WorkSpaces Web?,To maintain user's browser sessions across multiple connections.,To store user credentials.,To record user activity.,To improve network performance.,"Session persistence allows user's browser sessions to be maintained across multiple connections, allowing users to resume where they left off."
How does Amazon WorkSpaces Web support multi-factor authentication (MFA)?,By integrating with existing IAM and identity provider MFA configurations.,By requiring users to create a separate MFA profile.,It does not support MFA.,By using biometric authentication.,WorkSpaces Web integrates with existing IAM and identity provider MFA configurations.
When should you consider using Amazon WorkSpaces Web over Amazon WorkSpaces?,When you need secure access to internal web applications only.,When you need a full virtual desktop environment.,When you need to run compute-intensive applications.,When you need offline access to applications.,WorkSpaces Web is better suited for secure access to internal web applications when a full VDI is not needed.
What type of data can be protected by using Amazon WorkSpaces Web?,Sensitive data displayed in web applications.,All data stored on user devices.,Network traffic.,Physical access to servers.,WorkSpaces Web helps protect sensitive data displayed in web applications by preventing it from being stored on user devices.
What is the role of 'Tags' in Amazon WorkSpaces Web?,To organise and manage WorkSpaces Web resources.,To encrypt data.,To define network policies.,To control user access.,Tags enable you to organise and manage WorkSpaces Web resources by providing metadata.
What is a key difference between Amazon WorkSpaces Web and Amazon AppStream 2.0?,"WorkSpaces Web is for web apps, while AppStream 2.0 is for streaming desktop applications.",WorkSpaces Web is cheaper than AppStream 2.0.,WorkSpaces Web is more secure than AppStream 2.0.,WorkSpaces Web requires more configuration than AppStream 2.0.,"WorkSpaces Web specialises in streaming web applications, while AppStream 2.0 streams full desktop applications."
How does Amazon WorkSpaces Web handle browser cookies?,Cookies can be configured to be cleared at the end of each session or persisted based on policy.,Cookies are always cleared at the end of each session.,Cookies are always persisted.,Cookies are not supported.,"The ability to persist or clear cookies can be configured in the web access portals, to suit compliance or user requirements."
What is the recommended method for configuring Amazon WorkSpaces Web to access resources in a private subnet?,Using VPC endpoints and private subnets.,Using a public subnet with an internet gateway.,Using a VPN connection.,Using a direct connect connection.,Using VPC endpoints within private subnets is the recommended and most secure approach.
Which of the following security best practices should be followed when configuring Amazon WorkSpaces Web?,Implement the principle of least privilege with IAM roles.,Disable logging.,Allow all network traffic.,Store credentials in plain text.,Following the principle of least privilege is a key security best practice.
What is the purpose of the 'Browser Isolation' feature in Amazon WorkSpaces Web?,To isolate web sessions from the user's device and network.,To isolate web sessions from each other.,To isolate different web applications.,To isolate the browser from the internet.,The 'Browser Isolation' feature ensures that web sessions are isolated from the user's device and network for enhanced security.
What is a key advantage of using Amazon WorkSpaces Web for Bring Your Own Device (BYOD) environments?,It allows users to securely access internal web applications without installing software on their personal devices.,It improves device performance.,It allows IT to manage personal devices.,It reduces network bandwidth consumption.,Workspaces Web helps BYOD environments by giving the user easy access to the tools they need without the risks of installing software on personal devices.
How can you integrate Amazon WorkSpaces Web with an existing Active Directory (AD) environment?,Using AWS Directory Service.,Using IAM.,Using Amazon Cognito.,Using Amazon Cloud Directory.,AWS Directory Service is used to integrate WorkSpaces Web with an existing Active Directory environment.
Which of the following AWS services can be used to store temporary data for Amazon WorkSpaces Web sessions?,Amazon S3,Amazon EBS,Amazon EFS,Amazon Glacier,"Amazon S3 can be used to store temporary session data, like downloads."
"When using Amazon WorkSpaces Web, which aspect requires the most consideration for data egress control?",Configuring restrictive URL filtering policies.,Disabling copy-paste functionality.,Disabling printing.,Enabling watermarks.,The main consideration is to put a restrictive URL filtering policy in place to minimize the risk of unwanted downloads.
What is the best way to control access to specific web applications based on user roles when using Amazon WorkSpaces Web?,Integrate with an Identity Provider (IdP) and use IAM policies.,Configure individual user accounts.,Use local user accounts within WorkSpaces Web.,Assign static IP addresses to users.,Integrating with an IdP and using IAM policies ensures that user access is correctly managed and dynamically provisioned.
You need to ensure that all connections to your internal web applications via Amazon WorkSpaces Web are encrypted. What configuration should you verify?,That the web applications are using HTTPS.,That the client browser is up to date.,That the network firewall is correctly configured.,That all users have strong passwords.,Verifying that web applications are configured to use HTTPS ensures all data transmissions are encrypted during transit.
How does Amazon WorkSpaces Web handle local printer redirection?,Local printer redirection is not supported.,It uses a generic printer driver for all printers.,It installs printer drivers on the streaming instance.,It mirrors the local printer settings.,WorkSpaces Web doesn't support local printer redirection to maintain security and prevent data egress.