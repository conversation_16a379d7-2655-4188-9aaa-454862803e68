"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In FreeRTOS, what is a task?","A C function that executes independently.","A global variable.","A hardware interrupt.","A compiler directive.","A task in FreeRTOS is essentially a C function that can execute independently of other tasks, managed by the RTOS scheduler."
"What is the purpose of the FreeRTOS scheduler?","To determine which task should be running at any given time.","To manage memory allocation.","To handle interrupt requests.","To compile the source code.","The FreeRTOS scheduler is responsible for deciding which task gets to run based on priority and scheduling algorithm."
"What is the difference between a task and an interrupt service routine (ISR) in FreeRTOS?","Tasks run in thread mode, ISRs run in interrupt mode.","Tasks have higher priority than ISRs.","ISRs can block, tasks cannot.","There is no difference.","Tasks run in the context of the FreeRTOS scheduler, whereas ISRs run in interrupt context with higher priority to respond to hardware events."
"Which FreeRTOS API function is used to create a new task?","xTaskCreate()","vTaskStartScheduler()","prvTaskExitError()","xQueueCreate()","The xTaskCreate() function is the primary API for creating and adding a new task to the FreeRTOS scheduler."
"What is the purpose of a task priority in FreeRTOS?","To determine the order in which tasks are executed by the scheduler.","To allocate memory to a task.","To define the task's stack size.","To specify the task's name.","Task priority determines which task will be given CPU time when multiple tasks are ready to run; higher priority tasks preempt lower priority tasks."
"In FreeRTOS, what is a critical section?","A section of code where interrupts are disabled.","A section of code that is executed by the scheduler.","A section of code that allocates memory.","A section of code that creates a task.","A critical section is a section of code where interrupts are disabled to prevent data corruption from interrupt handlers accessing shared resources."
"What is a mutex in FreeRTOS?","A synchronisation object used to protect shared resources.","A data structure used to store messages.","A function used to create a task.","A hardware timer.","A mutex (mutual exclusion) is a synchronisation primitive that protects shared resources by ensuring only one task can access the resource at a time."
"Which FreeRTOS API function is used to take a mutex?","xSemaphoreTake()","xSemaphoreGive()","xTaskDelay()","xQueueReceive()","xSemaphoreTake() is used to acquire (take) a mutex semaphore, blocking the task if the mutex is already held by another task."
"What is a binary semaphore in FreeRTOS typically used for?","Signalling the occurrence of an event.","Protecting shared resources like a mutex.","Dynamic memory allocation.","Task creation.","Binary semaphores are often used for signaling between tasks or between an ISR and a task, indicating that an event has occurred."
"What is the difference between a mutex and a binary semaphore in FreeRTOS?","A mutex has ownership, a binary semaphore does not.","Binary Semaphores are recursive.","There is no difference.","Mutexes can be used inside ISRs, binary semaphores cannot.","A mutex has the concept of ownership (only the task that took the mutex can give it back), while a binary semaphore does not have this restriction."
"What is a queue in FreeRTOS?","A data structure used for inter-task communication.","A function used to create a task.","A hardware timer.","A synchronisation object used to protect shared resources.","A queue is a data structure used for passing messages and data between tasks in FreeRTOS."
"Which FreeRTOS API function is used to send data to a queue?","xQueueSend()","xQueueReceive()","xTaskNotifyGive()","xSemaphoreGive()","xQueueSend() is used to send data to a FreeRTOS queue, which can be used by another task or ISR to receive data."
"Which FreeRTOS API function is used to receive data from a queue?","xQueueReceive()","xQueueSend()","xTaskCreate()","xTimerCreate()","xQueueReceive() is used to retrieve data from a FreeRTOS queue, potentially blocking the task if the queue is empty."
"What is the purpose of the configTICK_RATE_HZ configuration parameter in FreeRTOS?","Defines the frequency of the RTOS tick interrupt.","Defines the stack size for tasks.","Defines the priority of the idle task.","Defines the maximum number of tasks.","configTICK_RATE_HZ specifies the frequency of the RTOS tick interrupt, which is used for time-slicing and timing services."
"What is the purpose of the vTaskDelay() function in FreeRTOS?","To put a task into the blocked state for a specified number of ticks.","To terminate a task.","To change the priority of a task.","To create a new task.","vTaskDelay() puts a task into the blocked state for a specified number of RTOS ticks, allowing other tasks to run."
"What is a task notification in FreeRTOS?","A mechanism for direct task-to-task communication.","A way to create a new task.","A function used to allocate memory.","A way to disable interrupts.","Task notifications provide a lightweight and efficient mechanism for tasks to signal each other, often used for simple synchronisation or event notification."
"Which FreeRTOS API function is used to send a task notification?","xTaskNotifyGive()","xQueueSend()","xSemaphoreGive()","vTaskDelay()","xTaskNotifyGive() is used to send a notification to a task, which can be received using xTaskNotifyWait()."
"What is a software timer in FreeRTOS?","A mechanism for executing a function after a specified delay or at regular intervals.","A hardware timer peripheral.","A function used to create a task.","A data structure used to store messages.","Software timers in FreeRTOS allow you to execute a callback function after a specified delay or at regular intervals, managed by the RTOS timer service."
"Which FreeRTOS API function is used to create a software timer?","xTimerCreate()","xTaskCreate()","xQueueCreate()","xSemaphoreCreateBinary()","xTimerCreate() is used to create a software timer in FreeRTOS, specifying the timer name, period, auto-reload, and callback function."
"What is the purpose of the Idle Task in FreeRTOS?","To execute when no other task is ready to run.","To initialise the RTOS.","To manage memory allocation.","To handle interrupt requests.","The Idle Task is a low-priority task that runs when no other task is ready to execute, providing a place to put low-priority background processing or power-saving code."
"What is the purpose of the configASSERT() macro in FreeRTOS?","To check for errors during development.","To configure the RTOS.","To disable interrupts.","To allocate memory.","configASSERT() is a macro used for debugging and error checking during development, allowing you to define custom assertion behaviour."
"What is the purpose of the pxCurrentTCB variable in FreeRTOS?","Points to the Task Control Block of the currently executing task.","Defines the tick rate.","Specifies the stack size.","Determines the priority of tasks.","pxCurrentTCB points to the Task Control Block (TCB) of the task that is currently running, providing access to the task's state and context."
"What is the purpose of a Task Control Block (TCB) in FreeRTOS?","To store information about a task.","To define the RTOS tick rate.","To allocate memory.","To handle interrupts.","The Task Control Block (TCB) is a data structure that stores all the information about a task, such as its stack pointer, priority, and state."
"In FreeRTOS, what does context switching refer to?","The process of saving the state of one task and restoring the state of another.","The process of creating a new task.","The process of allocating memory.","The process of handling interrupt requests.","Context switching is the process of saving the state of the currently running task (registers, stack pointer, etc.) and restoring the state of another task, allowing the scheduler to switch between tasks."
"Which scheduler policy does FreeRTOS implement by default?","Preemptive priority-based scheduling.","First-Come, First-Served (FCFS).","Round Robin.","Shortest Job First (SJF).","FreeRTOS by default uses a preemptive priority-based scheduler, meaning that higher priority tasks can interrupt lower priority tasks."
"What does it mean for a FreeRTOS task to be in the 'blocked' state?","The task is waiting for an event or resource.","The task is currently executing.","The task has been terminated.","The task is ready to execute but is preempted.","A task in the blocked state is waiting for an event to occur (e.g., queue data, semaphore) or for a delay to expire before it can become ready to run."
"What is stack overflow in FreeRTOS?","When a task writes beyond the allocated memory for its stack.","When there are too many tasks.","When memory allocation fails.","When an interrupt occurs.","Stack overflow occurs when a task writes data beyond the bounds of its allocated stack memory, potentially corrupting other data or causing a crash."
"How can you prevent stack overflow in FreeRTOS?","By allocating sufficient stack size for each task.","By disabling interrupts.","By using dynamic memory allocation.","By increasing the tick rate.","Stack overflow can be prevented by ensuring that each task is allocated a stack size large enough to accommodate its local variables, function calls, and interrupt handling."
"What is the purpose of configUSE_IDLE_HOOK in FreeRTOS?","To enable a user-defined function to run in the Idle Task.","To configure the RTOS tick rate.","To enable stack overflow checking.","To define the priority of the Idle Task.","configUSE_IDLE_HOOK enables a user-defined function (the Idle Hook) to be called from the Idle Task, allowing you to perform background processing or power-saving actions when no other tasks are running."
"What is the purpose of configUSE_TICK_HOOK in FreeRTOS?","To enable a user-defined function to run at each RTOS tick.","To configure the RTOS tick rate.","To enable stack overflow checking.","To define the priority of the Idle Task.","configUSE_TICK_HOOK enables a user-defined function (the Tick Hook) to be called from the RTOS tick interrupt, allowing you to perform periodic tasks or monitoring at each tick."
"What is the purpose of the FreeRTOS 'portable' layer?","To provide a hardware abstraction layer for different architectures.","To manage memory allocation.","To handle interrupt requests.","To create tasks.","The FreeRTOS portable layer contains the architecture-specific code, allowing the core RTOS code to be independent of the underlying hardware platform."
"What type of real-time operating system is FreeRTOS?","Hard real-time operating system.","Batch processing operating system.","General purpose operating system.","Time-sharing operating system.","FreeRTOS is typically used as a soft real-time operating system, meaning that while it strives to meet deadlines, it does not guarantee them with absolute certainty."
"What is the purpose of using queues in FreeRTOS?","To enable inter-task communication and synchronisation.","To manage memory allocation.","To handle interrupts.","To create tasks.","Queues in FreeRTOS are a mechanism for tasks to send and receive data, facilitating communication and synchronisation between different parts of an application."
"What is the role of the FreeRTOS configuration file (FreeRTOSConfig.h)?","To configure the RTOS kernel.","To define task functions.","To specify hardware initialisation.","To define interrupt handlers.","FreeRTOSConfig.h is a header file that allows users to configure various aspects of the FreeRTOS kernel, such as tick rate, stack sizes, and features."
"Which of the following is NOT a valid task state in FreeRTOS?","Waiting","Ready","Running","Sleeping","Sleeping is not a task state recognised by FreeRTOS. It is 'Blocked' state instead, when waiting for a timer, event or resource."
"What is a message buffer in FreeRTOS?","A data structure for sending larger data chunks between tasks or ISRs.","A way to handle interrupt requests.","A function used to allocate memory.","A type of semaphore.","Message buffers provide a stream-like interface for sending larger blocks of data between tasks or between an ISR and a task."
"In FreeRTOS, what does the term 're-entrant' mean?","A function that can be safely called from multiple tasks or ISRs simultaneously.","A function that creates a new task.","A function that allocates memory.","A function that disables interrupts.","A re-entrant function is one that can be safely called from multiple contexts (tasks or ISRs) concurrently without causing data corruption or unexpected behaviour."
"What is the purpose of the configSUPPORT_STATIC_ALLOCATION configuration parameter in FreeRTOS?","To enable static memory allocation for RTOS objects.","To enable dynamic memory allocation.","To disable interrupts.","To configure the RTOS tick rate.","configSUPPORT_STATIC_ALLOCATION enables the use of static memory allocation for creating RTOS objects like tasks, queues, and semaphores, which can improve determinism and reduce memory fragmentation."
"What is the purpose of using event groups in FreeRTOS?","To allow tasks to synchronise based on multiple event flags.","To create a new task.","To manage memory allocation.","To handle interrupt requests.","Event groups provide a mechanism for tasks to wait for a combination of multiple events to occur before proceeding, allowing for more complex synchronisation scenarios."
"What is the purpose of the configMAX_PRIORITIES configuration parameter in FreeRTOS?","To define the maximum number of priority levels available.","To define the maximum number of tasks.","To define the stack size for tasks.","To configure the RTOS tick rate.","configMAX_PRIORITIES specifies the maximum number of priority levels that can be used in the FreeRTOS system, affecting the granularity of task scheduling."
"What is the significance of the configMINIMAL_STACK_SIZE parameter in FreeRTOS?","Specifies the minimum stack size allocated to the idle task.","Specifies the maximum stack size allocated to any task.","Configures the RTOS tick rate.","Enables stack overflow detection.","configMINIMAL_STACK_SIZE defines the minimum allowed stack size that is allocated to the Idle Task. Other tasks may have larger stack sizes."
"What is the purpose of the INCLUDE_vTaskDelayUntil macro in FreeRTOS?","To determine the exact time a task wakes after a delay.","To create a new task.","To manage memory allocation.","To handle interrupt requests.","The `INCLUDE_vTaskDelayUntil` macro determines whether to include the `vTaskDelayUntil` function, which allows a task to delay itself until a specified absolute time."
"How do you handle a situation where two tasks need to access the same hardware peripheral in FreeRTOS?","Use a mutex to protect access to the peripheral.","Disable interrupts while accessing the peripheral.","Create a new task for each access.","Use a queue to send data to the peripheral directly.","Using a mutex ensures that only one task can access the hardware peripheral at a time, preventing conflicts and data corruption."
"When might you choose to use a message buffer instead of a queue in FreeRTOS?","When sending large amounts of data between tasks.","When sending a single integer between tasks.","When creating a new task.","When managing memory allocation.","Message buffers are designed for sending larger blocks of data, providing a stream-like interface, whereas queues are more suitable for discrete messages."
"Which FreeRTOS feature can help you reduce power consumption?","Tickless Idle mode.","Stack overflow detection.","Task notifications.","Queue management.","Tickless Idle mode allows the microcontroller to enter a low-power state when no tasks are ready to run, reducing power consumption."
"What is the purpose of the configUSE_RECURSIVE_MUTEXES configuration option in FreeRTOS?","Enables mutexes to be taken multiple times by the same task.","Disables the use of mutexes.","Enables the use of queues.","Disables interrupt handling.","configUSE_RECURSIVE_MUTEXES enables the use of recursive mutexes, which can be taken multiple times by the same task without blocking, useful for recursive functions."
"What is a potential drawback of using dynamic memory allocation in FreeRTOS?","Memory fragmentation.","Increased determinism.","Reduced code size.","Improved interrupt handling.","Dynamic memory allocation can lead to memory fragmentation over time, making it harder to allocate contiguous blocks of memory."
"Which of the following is a key benefit of using an RTOS like FreeRTOS in embedded systems?","Improved task management and scheduling.","Directly reduces power consumption.","Simplifies the hardware design.","Eliminates the need for interrupts.","An RTOS like FreeRTOS provides a structured way to manage tasks, allowing for efficient scheduling and resource allocation in embedded systems."
"In FreeRTOS, how can you ensure a specific task always gets the CPU time it needs, even when lower-priority tasks are running?","Use a real-time scheduling algorithm.","Set the task to the lowest priority.","Disable all interrupts.","Allocate more memory to the task.","Using real-time scheduling algorithm will help ensure a specific task always gets the CPU time it needs. FreeRTOS priority-based scheduling provides the mechanism to achieve that."
"What is the main advantage of using static memory allocation over dynamic allocation in FreeRTOS applications?","It avoids memory fragmentation and provides deterministic memory usage.","It reduces the application's memory footprint.","It allows more flexibility in memory allocation sizes.","It simplifies memory management.","Static memory allocation avoids runtime memory allocation, which can lead to memory fragmentation and non-deterministic behaviour. This makes static allocation more predictable and suitable for real-time systems where timing is critical."
"In FreeRTOS, what is the primary function of a task?","To execute a specific part of the application code independently.","To manage memory allocation for the system.","To handle interrupt service routines.","To initialise hardware peripherals.","A task in FreeRTOS is an independent thread of execution that performs a specific function within the application."
"Which FreeRTOS API function is used to create a new task?","xTaskCreate()","vTaskStartScheduler()","xQueueCreate()","xSemaphoreCreateBinary()","xTaskCreate() is the FreeRTOS API function specifically designed to create and start a new task."
"What is the role of the FreeRTOS scheduler?","To determine which task should be running at any given time.","To allocate memory to tasks.","To handle interrupt requests.","To manage file system operations.","The FreeRTOS scheduler is responsible for selecting the highest-priority ready task to execute."
"What is a mutex in FreeRTOS used for?","To protect shared resources from concurrent access.","To send data between tasks.","To signal the occurrence of an event.","To create a periodic timer.","A mutex is a synchronisation primitive used to provide exclusive access to a shared resource, preventing data corruption and race conditions."
"Which FreeRTOS API function is used to send data to a queue?","xQueueSend()","xTaskNotify()","xStreamBufferSend()","xSemaphoreGive()","xQueueSend() is the standard FreeRTOS API function for sending data to a queue."
"What is the purpose of a binary semaphore in FreeRTOS?","To signal availability of a resource or event.","To store multiple data items.","To manage dynamic memory allocation.","To perform floating point operations.","A binary semaphore is commonly used to signal the availability of a resource or the occurrence of an event, acting as a flag."
"What is the difference between a task and an interrupt service routine (ISR) in FreeRTOS?","ISRs must be short and non-blocking, while tasks can be longer and blocking.","Tasks have higher priority than ISRs.","ISRs can access all FreeRTOS APIs, while tasks cannot.","Tasks are triggered by hardware events, while ISRs are triggered by software.","ISRs need to be short and non-blocking to avoid delaying other interrupts and the main program execution. Tasks can be longer running and can block while waiting for resources."
"Which FreeRTOS configuration setting determines the tick rate?","configTICK_RATE_HZ","configCPU_CLOCK_HZ","configTOTAL_HEAP_SIZE","configMAX_PRIORITIES","configTICK_RATE_HZ sets the frequency of the RTOS tick interrupt, which is the basis for time-slicing and time-outs."
"What is a queue in FreeRTOS used for?","Inter-task communication and data transfer.","Dynamic memory allocation.","Interrupt handling.","Hardware initialisation.","Queues are a fundamental mechanism for tasks to exchange data in a safe and synchronised manner."
"What is task starvation in FreeRTOS?","A high-priority task preventing lower-priority tasks from running.","A task running out of memory.","A task being terminated unexpectedly.","A task exceeding its allocated stack size.","Task starvation occurs when higher-priority tasks continuously prevent lower-priority tasks from getting CPU time."
"What is a critical section in FreeRTOS?","A section of code where interrupts are disabled to ensure atomicity.","A section of code that requires high CPU utilisation.","A section of code that handles hardware interrupts.","A section of code that manages dynamic memory.","A critical section disables interrupts to prevent context switching and ensure that a sequence of operations is executed atomically."
"How do you define a task's priority in FreeRTOS?","Using the `uxPriority` parameter in `xTaskCreate()`.","Using the `configMAX_PRIORITIES` setting.","By setting a global variable.","Through the interrupt vector table.","The `uxPriority` parameter in `xTaskCreate()` allows you to assign a priority level to each task, influencing the scheduler's decision on which task to run."
"What is the purpose of `vTaskDelay()` in FreeRTOS?","To suspend a task for a specified number of RTOS ticks.","To delete a task from memory.","To change a task's priority.","To send data to a queue.","`vTaskDelay()` is used to put a task into the blocked state for a specified duration, allowing other tasks to run."
"What is the role of the idle task in FreeRTOS?","To consume CPU time when no other tasks are ready to run.","To initialise the RTOS kernel.","To handle interrupt service routines.","To manage memory allocation.","The idle task ensures that the CPU is always executing something, even when no other tasks are ready, and can be used for low-priority tasks like power management."
"What is the purpose of the FreeRTOS heap?","To provide memory for dynamic allocation by tasks.","To store the kernel's internal data structures.","To hold the stack of each task.","To store interrupt vector tables.","The heap is a region of memory used by FreeRTOS for dynamic allocation, allowing tasks to allocate memory as needed using functions like `pvPortMalloc()`."
"Which FreeRTOS feature allows tasks to signal events to each other without using queues?","Task Notifications.","Mutexes.","Semaphores.","Software Timers.","Task notifications provide a lightweight mechanism for tasks to signal events to each other, offering an alternative to queues and semaphores for simple signalling."
"In FreeRTOS, what is meant by 'context switching'?","The process of saving and restoring the state of a task.","The process of allocating memory to a task.","The process of handling an interrupt request.","The process of creating a new task.","Context switching involves saving the state of the current running task (registers, stack pointer, etc.) and restoring the state of another task, allowing the scheduler to switch between tasks."
"Which FreeRTOS data structure is best suited for passing complex data structures between tasks when minimizing copying?","Message Buffer.","Queue.","Binary Semaphore.","Mutex.","Message buffers are better for larger or more complex data structures, as they often involve passing pointers rather than copying the entire data."
"What is a software timer in FreeRTOS?","A task that executes a function after a specified delay or at a periodic interval.","A hardware peripheral used for generating interrupts.","A data structure used for managing memory.","A mechanism for synchronising tasks.","Software timers allow you to execute a function after a delay or repeatedly at an interval, without tying up a dedicated task."
"What is the purpose of the `configASSERT()` macro in FreeRTOS?","To provide a debugging mechanism for detecting errors.","To optimise code for speed.","To disable interrupts.","To enable memory protection.","`configASSERT()` is a macro that allows you to check conditions and trigger an error handler if the condition is false, aiding in debugging and error detection."
"What is the function of the FreeRTOS `vTaskStartScheduler()` function?","To start the FreeRTOS scheduler and begin executing tasks.","To create a new task.","To suspend a task.","To delete a task.","`vTaskStartScheduler()` initialises the RTOS kernel and starts the scheduler, initiating the execution of tasks."
"What is the purpose of the FreeRTOS tick hook?","To execute a user-defined function at each RTOS tick.","To handle hardware interrupts.","To manage memory allocation.","To initialise hardware peripherals.","The tick hook allows you to execute custom code at each RTOS tick, providing a way to perform periodic tasks or collect system statistics."
"What is the potential problem with using a long, blocking function within an interrupt service routine (ISR) in FreeRTOS?","It can delay the execution of other interrupts and tasks.","It can lead to memory corruption.","It can cause the RTOS kernel to crash.","It can improve system performance.","Long, blocking functions in ISRs can significantly delay other interrupts and tasks, potentially leading to missed deadlines and system instability."
"How does FreeRTOS handle stack overflow protection?","By using stack overflow hooks and memory protection units (MPUs).","By automatically increasing stack size.","By ignoring stack overflows.","By preventing task creation.","FreeRTOS can use stack overflow hooks or MPUs to detect stack overflows, allowing you to take corrective action."
"Which FreeRTOS API function can be used to dynamically allocate memory?","pvPortMalloc()","xTaskCreate()","xQueueSend()","vTaskDelay()","`pvPortMalloc()` is the FreeRTOS API function for allocating memory from the heap."
"What is a counting semaphore in FreeRTOS used for?","To control access to a limited number of resources.","To signal the occurrence of a single event.","To protect shared resources from concurrent access.","To send data between tasks.","A counting semaphore can be used to track the availability of a limited number of resources, allowing tasks to acquire and release these resources."
"What is the purpose of the FreeRTOS configuration file, `FreeRTOSConfig.h`?","To configure the FreeRTOS kernel parameters.","To define the task code.","To specify the hardware peripherals.","To manage the file system.","`FreeRTOSConfig.h` is used to configure various aspects of the FreeRTOS kernel, such as tick rate, heap size, and task priorities."
"What is the difference between `xQueueSend()` and `xQueueSendToFront()` in FreeRTOS?","`xQueueSendToFront()` places the item at the head of the queue, while `xQueueSend()` places it at the end.","`xQueueSend()` is used for sending data, while `xQueueSendToFront()` is used for sending signals.","`xQueueSendToFront()` is non-blocking, while `xQueueSend()` is blocking.","There is no difference.","`xQueueSendToFront()` places the item at the beginning of the queue, ensuring it is the next item to be received, while `xQueueSend()` places it at the end."
"Which FreeRTOS API function is used to delete a task?","vTaskDelete()","xTaskEnd()","xTaskRemove()","vTaskSuspend()","`vTaskDelete()` is the FreeRTOS API function for removing a task from the system."
"What is the purpose of the FreeRTOS event groups?","To allow tasks to synchronise and communicate based on multiple events.","To manage dynamic memory allocation.","To handle hardware interrupts.","To create periodic timers.","Event groups allow tasks to wait for combinations of events to occur, enabling more complex synchronisation patterns."
"In FreeRTOS, what is the difference between suspending and deleting a task?","A suspended task can be resumed, while a deleted task cannot.","A suspended task releases its memory, while a deleted task keeps its memory.","A suspended task continues to consume CPU time, while a deleted task does not.","A suspended task can still be notified, while a deleted task cannot.","Suspending a task pauses its execution but retains its state, allowing it to be resumed later. Deleting a task removes it from the system, freeing its resources."
"Which FreeRTOS feature is useful for implementing a producer-consumer pattern with varying data sizes?","Stream Buffers","Queues","Mutexes","Binary Semaphores","Stream buffers are particularly well-suited for handling streams of data where the size of each chunk may vary, making them ideal for producer-consumer scenarios where the data size isn't fixed."
"What is the purpose of the `configUSE_PREEMPTION` setting in `FreeRTOSConfig.h`?","To enable or disable preemptive scheduling.","To set the tick rate.","To configure the heap size.","To define the number of tasks.","`configUSE_PREEMPTION` enables or disables preemptive scheduling. When enabled, higher-priority tasks can interrupt lower-priority tasks that are currently running."
"What is the difference between a queue and a stream buffer in FreeRTOS?","Queues are used for fixed-size data, while stream buffers are used for variable-size data.","Queues are non-blocking, while stream buffers are blocking.","Queues are more efficient than stream buffers.","Stream buffers can only hold one item.","Queues are typically used for fixed-size data, while stream buffers are better suited for streams of variable-size data."
"How can you measure the CPU usage of a task in FreeRTOS?","Using the run time statistics feature.","By analysing the memory usage.","By monitoring the interrupt frequency.","By counting the number of context switches.","FreeRTOS provides a run time statistics feature that can measure the amount of CPU time each task consumes."
"What is the purpose of the `configMAX_SYSCALL_INTERRUPT_PRIORITY` setting in FreeRTOS?","To define the highest interrupt priority from which FreeRTOS API functions can be safely called.","To set the priority of the idle task.","To limit the number of tasks that can be created.","To disable interrupts during system calls.","`configMAX_SYSCALL_INTERRUPT_PRIORITY` specifies the maximum interrupt priority level from which FreeRTOS API functions can be safely called, preventing priority inversion issues."
"Which FreeRTOS data structure would be most suitable for implementing a LIFO (Last-In, First-Out) buffer?","A stream buffer using `xStreamBufferReceive()` with an appropriate size.","A standard queue using `xQueueSendToFront()` and `xQueueReceive()`.","A counting semaphore.","A mutex.","Stream buffers can implement a LIFO by writing to the buffer and reading the most recently written data by adjusting the read pointer within the stream buffer functions."
"In FreeRTOS, what does the term 'priority inversion' refer to?","A lower-priority task blocking a higher-priority task.","A task exceeding its allocated stack size.","A task running out of memory.","A task being deleted unexpectedly.","Priority inversion occurs when a lower-priority task holds a resource that a higher-priority task needs, effectively blocking the higher-priority task."
"What is the role of the FreeRTOS MPU (Memory Protection Unit)?","To protect tasks from accessing memory outside their allocated regions.","To manage dynamic memory allocation.","To handle hardware interrupts.","To optimise code for speed.","The MPU enforces memory protection, preventing tasks from accessing memory regions outside of their allocated spaces, enhancing system stability and security."
"Which function provides a time delay that does NOT place the calling task into the blocked state in FreeRTOS?","vTaskDelayUntil()","vTaskDelay()","taskYIELD()","xSemaphoreTake()","vTaskDelayUntil() is used to delay until a specified absolute time, relative to when the scheduler was started, and does not place the task into the blocked state."
"When should you use a mutex instead of a binary semaphore in FreeRTOS?","When you need to protect a shared resource from concurrent access and avoid priority inversion.","When you need to signal the occurrence of an event.","When you need to send data between tasks.","When you need to create a periodic timer.","Mutexes are specifically designed to protect shared resources and prevent priority inversion, making them the preferred choice over binary semaphores in such scenarios."
"What is the purpose of the `configUSE_IDLE_HOOK` setting in `FreeRTOSConfig.h`?","To enable or disable the idle hook function.","To set the tick rate.","To configure the heap size.","To define the number of tasks.","`configUSE_IDLE_HOOK` enables or disables the idle hook function, which is called by the idle task when no other tasks are ready to run."
"In FreeRTOS, what is a task hook?","A function that is called when a specific task event occurs (e.g., task creation, deletion).","A hardware interrupt handler.","A data structure used for managing memory.","A mechanism for synchronising tasks.","Task hooks are user-defined functions that are called at specific points in a task's lifecycle, such as creation or deletion, allowing you to perform custom actions."
"What is a benefit of using statically allocated RTOS objects (e.g., queues, semaphores) in FreeRTOS compared to dynamically allocated ones?","Increased determinism and reduced risk of memory fragmentation.","Faster creation and deletion.","Automatic garbage collection.","More flexible memory usage.","Statically allocated objects are allocated at compile time, eliminating the overhead and potential issues associated with dynamic memory allocation, like fragmentation and non-deterministic allocation times."
"What is a stream buffer in FreeRTOS primarily used for?","Passing streams of bytes between tasks or ISRs, typically for UART or other serial communication.","Protecting shared resources.","Implementing message queues.","Creating periodic timers.","Stream buffers are designed for passing streams of data, making them ideal for UART or other serial communication applications where data is received or transmitted as a continuous flow of bytes."
"What is the purpose of setting `configSUPPORT_STATIC_ALLOCATION` to 1 in FreeRTOS?","To enable the use of statically allocated RTOS objects.","To disable dynamic memory allocation.","To enable preemptive scheduling.","To configure the heap size.","Setting `configSUPPORT_STATIC_ALLOCATION` to 1 enables the use of statically allocated RTOS objects like tasks, queues, and semaphores."
"What is the function of the `xTaskGetTickCount()` FreeRTOS API?","To return the number of ticks since the scheduler started.","To set the tick rate.","To suspend a task for a specified number of ticks.","To delete a task.","`xTaskGetTickCount()` returns the current RTOS tick count, providing a measure of elapsed time since the scheduler was started."
"Which configuration option in FreeRTOS should be enabled to include basic stack overflow checking?","configCHECK_FOR_STACK_OVERFLOW","configUSE_MALLOC_FAILED_HOOK","configASSERT","configUSE_TRACE_FACILITY","The `configCHECK_FOR_STACK_OVERFLOW` option enables basic stack overflow checking in FreeRTOS, which can help identify potential memory corruption issues."
"How can you trigger a context switch from within a FreeRTOS task?","By calling taskYIELD().","By calling vTaskDelay().","By calling xQueueSend().","By calling xSemaphoreGive().","`taskYIELD()` forces a context switch, allowing other ready tasks of the same priority to run."
