"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS IoT SiteWise, what is the primary function of an asset?","To represent a physical entity (e.g., a pump, a motor, or a wind turbine)","To store historical data from industrial equipment.","To define alarms and notifications.","To manage user access control.","Assets in SiteWise represent physical entities such as machines, equipment, or processes in an industrial environment."
"What type of data is typically ingested into AWS IoT SiteWise?","Time-series data from industrial equipment","Social media feeds","Customer relationship management (CRM) data","Geospatial data","SiteWise is specifically designed for handling time-series data generated by sensors and industrial equipment."
"Which AWS service is commonly used to collect data from industrial devices and send it to AWS IoT SiteWise?","AWS IoT Greengrass","Amazon S3","AWS Lambda","Amazon EC2","AWS IoT Greengrass allows you to securely connect and manage devices at the edge, enabling them to collect and transmit data to SiteWise."
"What is the purpose of an asset model in AWS IoT SiteWise?","To define the structure and properties of assets","To encrypt data at rest","To monitor network traffic","To create user interfaces","Asset models define the structure, attributes, and measurements associated with a type of asset, providing a template for creating individual assets."
"Which data type is NOT supported for asset properties in AWS IoT SiteWise?","STRING","INTEGER","BOOLEAN","JSON","JSON is not a supported primitive data type. Other primitive types are supported."
"In AWS IoT SiteWise, what is the purpose of a transform?","To perform calculations on data streams in real-time","To convert data into different file formats","To compress data for storage","To encrypt data for security","Transforms allow you to define mathematical or logical operations on data streams to derive new measurements or insights."
"Which AWS service can be used to visualise data from AWS IoT SiteWise?","Amazon QuickSight","Amazon CloudWatch","AWS CloudTrail","AWS Config","Amazon QuickSight is a business intelligence service that can connect to SiteWise and create visualisations from the collected data."
"What is the benefit of using AWS IoT SiteWise Edge?","Allows data processing and storage on-premises","Provides unlimited cloud storage capacity","Automatically backs up data to Amazon S3","Encrypts all data in transit","SiteWise Edge allows you to process and store data locally before sending it to the cloud, reducing latency and bandwidth usage."
"What is the purpose of the AWS IoT SiteWise Monitor feature?","To create and manage web applications for visualising and interacting with SiteWise data.","To monitor the health of AWS IoT Greengrass devices.","To manage user authentication and authorisation.","To perform security audits.","SiteWise Monitor provides a way to create web applications for visualising data and interacting with assets."
"Which AWS service can be integrated with AWS IoT SiteWise to trigger actions based on data changes?","AWS IoT Events","Amazon SNS","Amazon SQS","AWS Step Functions","AWS IoT Events can be integrated with SiteWise to detect and respond to events based on data changes in SiteWise assets."
"What is the function of the `iotsitewise:DescribeAsset` IAM permission in AWS IoT SiteWise?","Allows a user to retrieve information about a specific asset","Allows a user to create new assets","Allows a user to delete existing assets","Allows a user to update asset properties","The `iotsitewise:DescribeAsset` permission grants the ability to retrieve detailed information about a specific asset in SiteWise."
"How does AWS IoT SiteWise help in reducing operational costs?","By providing real-time data insights for predictive maintenance and optimisation","By automatically scaling compute resources","By reducing the need for on-premises data storage","By automating security compliance","Real-time data insights enable predictive maintenance and operational optimisation, leading to reduced downtime and improved efficiency."
"What is the purpose of the 'quality' property in AWS IoT SiteWise data streams?","To indicate the reliability and accuracy of the data","To specify the data storage duration","To define data encryption method","To assign access control policies","The 'quality' property indicates the reliability and accuracy of the data being ingested into SiteWise."
"Which type of AWS IoT SiteWise property is used to represent a static, unchanging characteristic of an asset?","Attribute","Measurement","Transform","Metric","Attributes represent static characteristics of an asset, such as its model number or serial number."
"In AWS IoT SiteWise, what does a 'time in seconds' value represent in a time-series data stream?","The number of seconds since the Unix epoch","The duration for which the data is valid","The timestamp of the data in the local timezone","The frequency at which the data is updated","The 'time in seconds' value is the standard Unix epoch timestamp, representing the number of seconds since January 1, 1970, 00:00:00 UTC."
"What is the primary purpose of AWS IoT SiteWise bulk import?","To ingest historical data from CSV files","To export data to external databases","To configure asset models","To manage user permissions","Bulk import allows you to efficiently ingest historical data from CSV files into SiteWise for analysis and reporting."
"Which data storage option is used by AWS IoT SiteWise for storing ingested time-series data?","Time series database","Object storage","Relational database","Graph database","SiteWise utilises a time series database optimised for storing and querying time-series data from industrial equipment."
"What is the purpose of AWS IoT SiteWise asset alias?","To provide a human-readable identifier for an asset property","To encrypt asset data","To manage user access to assets","To compress asset data","Asset aliases provide a human-readable identifier for an asset property that can be used in queries and applications."
"What is the purpose of the AWS IoT SiteWise Gateway?","To collect data from industrial devices and send it to SiteWise","To manage user authentication and authorization","To encrypt data at rest","To visualise data from SiteWise","The gateway is responsible for collecting data from industrial devices and sending it to SiteWise for processing and storage."
"Which AWS service can be used to authenticate devices connecting to AWS IoT SiteWise?","AWS IoT Core","Amazon Cognito","AWS IAM","AWS Directory Service","AWS IoT Core provides the device management and authentication infrastructure needed to connect devices to SiteWise."
"What is the function of `iotsitewise:UpdateAsset` IAM permission in AWS IoT SiteWise?","Allows a user to modify the properties of an existing asset","Allows a user to create new assets","Allows a user to delete existing assets","Allows a user to describe assets","The `iotsitewise:UpdateAsset` permission grants the ability to modify the properties of an existing asset."
"Which type of visualisation is best suited for displaying historical trends of sensor readings in AWS IoT SiteWise Monitor?","Time series chart","Pie chart","Bar chart","Scatter plot","A time series chart is specifically designed for displaying data over time, making it ideal for visualising sensor readings."
"What is the key benefit of using AWS IoT SiteWise for predictive maintenance?","Early detection of equipment failures","Automated data backups","Improved data encryption","Reduced network latency","Predictive maintenance uses data analysis to identify potential equipment failures before they occur, allowing for proactive maintenance."
"Which of the following is NOT a valid use case for AWS IoT SiteWise?","Monitoring and optimising industrial processes","Predicting equipment failures","Analysing customer sentiment","Visualising real-time data from industrial equipment","Analysing customer sentiment is not a typical use case for AWS IoT SiteWise, which is designed for industrial data."
"What does 'warm tier' storage typically refer to in the context of AWS IoT SiteWise?","Data that is accessed less frequently than hot tier data but still requires fast retrieval","Data that is accessed most frequently","Data that is archived for long-term storage","Data that is in transit","Warm tier storage is used for data that is accessed less frequently than hot tier data but still requires relatively fast retrieval."
"Which AWS IoT SiteWise API operation is used to retrieve historical values of an asset property?","GetAssetPropertyValueHistory","GetAssetPropertyValue","ListAssetProperties","DescribeAssetProperty","`GetAssetPropertyValueHistory` retrieves the historical values of an asset property over a specified time range."
"In AWS IoT SiteWise, what is the purpose of defining a 'window' in a transform?","To specify the duration over which a calculation is performed","To specify the time zone for data storage","To define the data encryption method","To filter data based on a time range","A window in a transform specifies the duration over which a calculation is performed, allowing for calculations on rolling time periods."
"Which of the following is a valid use for AWS IoT SiteWise Edge?","Processing data locally to reduce latency and bandwidth usage","Storing all data in the cloud without any on-premises processing","Replacing AWS IoT SiteWise entirely","Managing user access to AWS IoT SiteWise","Processing data locally reduces latency and bandwidth usage, which is particularly useful in environments with limited connectivity."
"What is the purpose of the 'offset' property in the timestamp of an AWS IoT SiteWise data stream?","To specify the time zone offset from UTC","To indicate the data quality","To define data encryption method","To specify the data storage duration","The 'offset' property specifies the time zone offset from UTC, ensuring accurate time representation regardless of the data source's location."
"Which of the following is NOT a supported data source for AWS IoT SiteWise?","OPC-UA servers","Modbus TCP devices","MQTT brokers","Amazon S3","While data can be stored on S3, it's not a direct source of live data ingestion in the same way as the industrial protocols."
"In AWS IoT SiteWise Monitor, what are 'dashboards' used for?","Visualising and interacting with data from AWS IoT SiteWise assets","Managing user access permissions","Configuring data ingestion rules","Creating asset models","Dashboards are used to display visualisations and provide interactive interfaces for exploring data from SiteWise assets."
"What is the purpose of the 'iotsitewise:BatchPutAssetPropertyValue' IAM permission in AWS IoT SiteWise?","Allows a user to ingest multiple data points for one or more asset properties in a single request","Allows a user to retrieve the history of property values","Allows a user to create new assets","Allows a user to delete existing assets","`iotsitewise:BatchPutAssetPropertyValue` allows a user to efficiently ingest multiple data points for various asset properties in a single request."
"What is the primary benefit of using AWS IoT SiteWise for root cause analysis in industrial operations?","Centralised view of industrial data and real-time data analytics","Automated security compliance","Lower storage costs","Automated user authentication","AWS IoT SiteWise provides a centralised view of industrial data and real-time analytics that helps in identifying the root causes of operational issues."
"In AWS IoT SiteWise, what does the term 'property notification' refer to?","A notification triggered when an asset property value changes","A notification sent when a new asset is created","A notification sent when a user logs into the system","A notification sent when data storage exceeds a limit","Property notifications are triggered when an asset property value changes, allowing for real-time monitoring and alerting."
"Which AWS service is commonly used with AWS IoT SiteWise to perform advanced analytics and machine learning on industrial data?","Amazon SageMaker","Amazon Redshift","Amazon S3","AWS Lambda","Amazon SageMaker can be used to build, train, and deploy machine learning models that analyse data from SiteWise."
"What is the function of the `iotsitewise:CreateAssetModel` IAM permission in AWS IoT SiteWise?","Allows a user to define the structure and properties of an asset type","Allows a user to create individual assets","Allows a user to delete asset models","Allows a user to update existing asset models","`iotsitewise:CreateAssetModel` allows a user to define the structure and properties of a type of asset."
"What is a common use case for calculated metrics in AWS IoT SiteWise?","To calculate performance indicators based on multiple data streams","To manage user access control","To monitor network traffic","To compress data for storage","Calculated metrics allow you to derive new measurements or insights by performing calculations on multiple data streams in real-time."
"Which feature of AWS IoT SiteWise helps to ensure data consistency across different devices and systems?","Data synchronisation","Data encryption","Data compression","Data partitioning","SiteWise provides features for data synchronisation to ensure that data from different devices and systems is consistent and reliable."
"What is the benefit of using AWS IoT SiteWise for operational equipment effectiveness (OEE) calculations?","Real-time visibility into OEE metrics and factors affecting performance","Automated security audits","Improved data encryption","Reduced data storage costs","SiteWise provides real-time visibility into OEE metrics and the factors that affect equipment performance, helping to improve overall efficiency."
"Which of the following is NOT a typical component of an AWS IoT SiteWise asset model?","Calculated Metrics","Attributes","Measurements","EC2 Instances","EC2 Instances are unrelated to an Asset Model."
"In AWS IoT SiteWise, what is the purpose of the 'cold tier' storage option?","To store data that is rarely accessed","To store data that requires fast retrieval","To store data that is encrypted","To store data that is frequently updated","Cold tier storage is used for data that is rarely accessed and has minimal storage costs."
"Which AWS IoT SiteWise API operation is used to create a new asset model?","CreateAssetModel","CreateAsset","UpdateAssetModel","DescribeAssetModel","The `CreateAssetModel` operation is used to define the structure and properties of a new asset type."
"What is the function of the 'iotsitewise:ListAssets' IAM permission in AWS IoT SiteWise?","Allows a user to retrieve a list of assets in a specific AWS account and region","Allows a user to create new assets","Allows a user to delete existing assets","Allows a user to update asset properties","`iotsitewise:ListAssets` grants the ability to retrieve a list of assets in a specified AWS account and region."
"In AWS IoT SiteWise Monitor, what is the purpose of 'alarms'?","To notify users of critical events or deviations from expected behaviour","To manage user access permissions","To configure data ingestion rules","To create asset models","Alarms are used to notify users of critical events or deviations from expected behaviour, enabling quick response to potential issues."
"Which AWS service is commonly used with AWS IoT SiteWise to build custom applications and integrations?","AWS Lambda","Amazon S3","Amazon EC2","Amazon DynamoDB","AWS Lambda allows you to run code in response to events triggered by SiteWise, enabling custom applications and integrations."
"What is the primary purpose of AWS IoT SiteWise in the context of smart manufacturing?","To collect, organise, and analyse data from manufacturing equipment","To manage user access control","To monitor network traffic","To compress data for storage","SiteWise helps smart manufacturing initiatives by providing a platform to collect, organise, and analyse data from manufacturing equipment."
"In AWS IoT SiteWise, what is the purpose of the 'time ordering' setting when configuring historical data ingestion?","To ensure data is processed in chronological order","To encrypt the data","To compress the data","To limit the amount of data that is ingested","The 'time ordering' setting ensures that data is processed in chronological order, which is crucial for accurate time-series analysis."
"Which AWS IoT SiteWise API operation is used to retrieve the current value of an asset property?","GetAssetPropertyValue","GetAssetPropertyValueHistory","ListAssetProperties","DescribeAssetProperty","`GetAssetPropertyValue` retrieves the most recent value of an asset property."
"What is the maximum number of properties allowed in a single AWS IoT SiteWise Asset Model?","200","50","10","1000","An asset model can have a maximum of 200 properties."
"In AWS IoT SiteWise, what is the purpose of the `iotsitewise:GetAssetPropertyAggregate` API?","To retrieve aggregated values (e.g., average, sum) of an asset property over a specified time range","To get the current value of an asset property","To list all assets","To describe an asset model","This API is used to retrieve aggregated values like average, sum, min, max of a property."
"Which component of AWS IoT SiteWise is responsible for defining how to process raw data from a source before it becomes an asset property?","Expression","Stream","Rule","Connector","Expression defines how to process raw data before it becomes an asset property."
"How does AWS IoT SiteWise help in optimising energy consumption in industrial facilities?","By providing real-time visibility into energy usage patterns and identifying areas for improvement","By automatically adjusting energy consumption based on weather conditions","By providing a marketplace for energy-efficient equipment","By automatically negotiating energy prices with suppliers","Real-time visibility into energy usage patterns helps identify inefficiencies and areas where energy consumption can be reduced."
"Which is NOT a valid way to connect data to AWS IoT SiteWise?","Directly from an EC2 Instance","OPC-UA","MQTT","IoT Greengrass","You can’t directly connect data to SiteWise from EC2 without using Greengrass or another service."
"In AWS IoT SiteWise, what is the primary purpose of an asset model?","To define the structure and behaviour of industrial assets","To store historical data from sensors","To manage user access to data","To visualise data in dashboards","Asset models define the structure (hierarchy, properties, measurements) and behaviour (transformations, metrics) of assets in SiteWise."
"What is the role of a property alias in AWS IoT SiteWise?","To map a data stream to a specific property in an asset","To filter incoming data streams","To create virtual assets","To encrypt data at rest","A property alias maps a data stream (e.g., from an IoT sensor) to a specific property within an asset in SiteWise, allowing SiteWise to ingest data from that stream."
"Which AWS service is primarily used to ingest data into AWS IoT SiteWise?","AWS IoT Core","AWS Lambda","Amazon S3","Amazon Kinesis","AWS IoT Core is the primary service used for device connectivity and message routing, enabling the ingestion of data from IoT devices into AWS IoT SiteWise."
"What type of data can be ingested into AWS IoT SiteWise?","Time series data from industrial equipment","Relational data from databases","Images and videos","Text documents","AWS IoT SiteWise is specifically designed for ingesting and processing time series data from industrial equipment."
"What is a key advantage of using AWS IoT SiteWise compared to building a custom data ingestion and storage solution?","Reduced development and operational overhead","Unlimited storage capacity","Native support for all industrial protocols","Real-time video streaming","AWS IoT SiteWise provides a managed service, reducing the development and operational overhead associated with building and maintaining a custom solution."
"In AWS IoT SiteWise, what is a 'metric' primarily used for?","Calculating aggregates and transformations on asset properties","Defining the physical characteristics of an asset","Controlling access permissions for asset data","Storing raw sensor data","Metrics in SiteWise are used to perform calculations (e.g., averages, sums) and transformations on asset properties, providing insights into asset performance."
"What is the purpose of the AWS IoT SiteWise Monitor service?","To build and visualise industrial dashboards","To manage user identities and access","To automatically detect anomalies in data","To optimise data storage costs","AWS IoT SiteWise Monitor allows users to build and visualise industrial dashboards, providing a user-friendly interface for monitoring asset data."
"Which type of calculations can be performed within AWS IoT SiteWise metrics?","Time-weighted averages and standard deviations","Complex machine learning algorithms","Natural language processing","Financial modelling calculations","AWS IoT SiteWise metrics support calculations like time-weighted averages, standard deviations, and other common statistical functions relevant to industrial data analysis."
"What is the main purpose of AWS IoT SiteWise Edge?","To process data locally at the edge, reducing latency and bandwidth costs","To provide a backup solution for SiteWise data","To integrate SiteWise with other AWS services","To create user interfaces for SiteWise","AWS IoT SiteWise Edge allows you to process data locally at the edge (e.g., on-premise gateways), reducing latency and bandwidth costs by pre-processing data before sending it to the cloud."
"What is the recommended data storage format for time series data ingested into AWS IoT SiteWise?","Parquet","JSON","CSV","XML","AWS IoT SiteWise stores time series data in a columnar format which is simular to Parquet. This is more efficient than other formats for time series data."
"Which AWS IoT SiteWise component allows you to define custom calculations and transformations on asset data?","Transforms","Alarms","Models","Gateways","Transforms in AWS IoT SiteWise enable you to define custom calculations and transformations on asset data, allowing you to derive new insights from your raw data."
"What is the purpose of defining a hierarchy within an AWS IoT SiteWise asset model?","To represent the relationships between assets","To control access to asset data","To define the physical location of assets","To optimise data storage","Defining a hierarchy within an asset model allows you to represent the relationships between assets (e.g., a pump belonging to a specific machine)."
"What type of access control does AWS IoT SiteWise primarily use?","IAM policies and roles","Access control lists (ACLs)","Database-level permissions","Operating system permissions","AWS IoT SiteWise uses IAM policies and roles to manage access control, providing granular control over who can access and modify SiteWise resources."
"When should you consider using AWS IoT SiteWise Edge over sending data directly to the cloud?","When you need to minimise latency or bandwidth costs","When you need to store data for compliance reasons","When you need to perform complex machine learning","When you need to visualise data in real-time","AWS IoT SiteWise Edge is beneficial when you need to minimise latency (by processing data locally) or reduce bandwidth costs (by pre-processing data before sending it to the cloud)."
"Which AWS service is commonly used to trigger actions based on alarms generated by AWS IoT SiteWise?","AWS IoT Events","Amazon SQS","Amazon SNS","AWS Step Functions","AWS IoT Events can be used to trigger actions based on alarms generated by AWS IoT SiteWise, enabling automated responses to critical events."
"In AWS IoT SiteWise, what is the purpose of defining a 'unit' for a property?","To ensure data consistency and accuracy","To control access to the property","To optimise storage for the property","To visualise the property on a map","Defining a 'unit' for a property (e.g., degrees Celsius for temperature) ensures data consistency and accuracy by standardising the units used for data ingestion and analysis."
"What is the difference between a 'measurement' and an 'attribute' in an AWS IoT SiteWise asset model?","Measurements are time series data, while attributes are static metadata","Measurements are used for calculations, while attributes are used for storage","Measurements are required, while attributes are optional","Measurements are stored in S3, while attributes are stored in DynamoDB","Measurements represent time series data (e.g., sensor readings), while attributes represent static metadata about the asset (e.g., manufacturer, model number)."
"Which of the following is NOT a valid way to access data stored in AWS IoT SiteWise?","Using a custom API built with AWS Lambda","Using the AWS IoT SiteWise Query API","Using the AWS IoT SiteWise Monitor service","Directly accessing the underlying database","You cannot directly access the underlying database in AWS IoT SiteWise. You should use the public API to access the data in SiteWise"
"What is the relationship between AWS IoT SiteWise and AWS IoT Analytics?","SiteWise focuses on asset modelling and real-time data, while Analytics focuses on advanced analytics and data warehousing","SiteWise is a replacement for IoT Analytics","They are the same service","Analytics is used to ingest the data into SiteWise","AWS IoT SiteWise focuses on asset modelling, real-time data processing, and visualisation, while AWS IoT Analytics is designed for more advanced analytics and data warehousing of IoT data."
"How does AWS IoT SiteWise help with data contextualisation?","By allowing you to define asset models and hierarchies","By automatically cleaning and transforming data","By integrating with external databases","By encrypting data at rest","AWS IoT SiteWise helps with data contextualisation by allowing you to define asset models and hierarchies, providing a structured way to organise and understand your industrial data."
"In AWS IoT SiteWise, what is the purpose of defining 'alarms'?","To notify users when property values exceed predefined thresholds","To automatically back up data","To control access to data","To visualise data on a map","Alarms in AWS IoT SiteWise allow you to notify users when property values (e.g., temperature, pressure) exceed predefined thresholds, enabling proactive monitoring and response."
"What is the role of a 'gateway' in the context of AWS IoT SiteWise?","To collect and forward data from industrial equipment to AWS","To manage user identities and access","To visualise data in dashboards","To perform complex calculations on data","A gateway in AWS IoT SiteWise (often running SiteWise Edge) is responsible for collecting data from industrial equipment (e.g., PLCs, sensors) and forwarding it to AWS for processing and storage."
"Which of the following is a valid use case for AWS IoT SiteWise?","Monitoring the performance of a fleet of wind turbines","Storing images from security cameras","Managing user accounts in a factory","Creating a social media platform","Monitoring the performance of a fleet of wind turbines is a valid use case for AWS IoT SiteWise, as it involves collecting and analysing time series data from industrial assets."
"What is the benefit of using AWS IoT SiteWise for data aggregation?","It allows you to calculate metrics across multiple assets and time periods","It automatically encrypts data at rest","It automatically backs up data to S3","It allows you to connect to any type of data source","AWS IoT SiteWise allows you to calculate metrics (e.g., averages, sums) across multiple assets and time periods, providing aggregated insights into your industrial operations."
"Which feature of AWS IoT SiteWise allows you to visualise data in real-time?","AWS IoT SiteWise Monitor","AWS IoT SiteWise Edge","AWS IoT Analytics","AWS IoT Events","AWS IoT SiteWise Monitor provides a user-friendly interface for visualising data in real-time, allowing you to monitor the performance of your assets and operations."
"How does AWS IoT SiteWise integrate with other AWS services for data analysis?","It can send data to AWS IoT Analytics for advanced analytics","It can automatically back up data to Amazon S3","It can integrate with any database using JDBC","It cannot integrate with other AWS services","AWS IoT SiteWise can send data to AWS IoT Analytics for more advanced analytics and data warehousing, enabling deeper insights into your industrial data."
"In AWS IoT SiteWise, what is the primary function of defining property data types?","To ensure data consistency and validation","To control access to data","To optimise data storage","To define the unit of measure","Defining property data types (e.g., integer, double, string) ensures data consistency and validation, preventing invalid data from being ingested into SiteWise."
"What is the role of 'expressions' in AWS IoT SiteWise transforms?","To define the calculations performed on asset properties","To define the physical location of assets","To control access to asset data","To optimise data storage","Expressions in AWS IoT SiteWise transforms are used to define the calculations performed on asset properties, allowing you to derive new metrics and insights."
"Which AWS service is commonly used for authenticating users accessing AWS IoT SiteWise Monitor?","AWS IAM Identity Center (successor to AWS SSO)","Amazon Cognito","AWS Directory Service","AWS Certificate Manager","AWS IAM Identity Center (successor to AWS SSO) is commonly used for authenticating users accessing AWS IoT SiteWise Monitor, providing a centralised identity management solution."
"What is the key benefit of using time-weighted averages in AWS IoT SiteWise?","To account for irregular data sampling intervals","To improve data security","To reduce storage costs","To simplify data visualisation","Time-weighted averages in AWS IoT SiteWise account for irregular data sampling intervals, providing more accurate calculations when data is not collected at consistent intervals."
"How can you ingest data from OPC-UA servers into AWS IoT SiteWise?","Using AWS IoT SiteWise Edge with an OPC-UA connector","Using AWS Lambda functions","Using direct database connections","Using Amazon SQS","AWS IoT SiteWise Edge with an OPC-UA connector allows you to ingest data directly from OPC-UA servers, which are commonly used in industrial environments."
"Which of the following is a valid data source for AWS IoT SiteWise?","IoT sensors","Relational databases","Web APIs","Social media feeds","IoT sensors are a valid data source for AWS IoT SiteWise, as SiteWise is designed to ingest and process time series data from industrial equipment."
"What is the purpose of 'Asset Properties' in AWS IoT SiteWise?","To represent the characteristics or measurements of an asset","To control access to the asset","To define the physical location of the asset","To optimise data storage for the asset","Asset Properties in AWS IoT SiteWise represent the characteristics or measurements of an asset, such as temperature, pressure, or speed."
"In AWS IoT SiteWise, what is the role of 'Asset Variables'?","To store reusable values or parameters in calculations","To store images and videos","To control access to asset properties","To optimise data storage","Asset Variables in AWS IoT SiteWise are used to store reusable values or parameters that can be used in calculations, making transforms more flexible and maintainable."
"Which AWS service would you use to archive historical data from AWS IoT SiteWise for long-term storage?","Amazon S3","Amazon DynamoDB","Amazon Redshift","Amazon Glacier","Amazon S3 is commonly used to archive historical data from AWS IoT SiteWise for long-term storage, providing cost-effective storage for infrequently accessed data."
"What is the purpose of defining 'custom resource definitions' in AWS IoT SiteWise?","There are no custom resource definitions in AWS IoT SiteWise","To extend the functionality of AWS IoT SiteWise","To control access to AWS IoT SiteWise","To optimise data storage in AWS IoT SiteWise","To extend the functionality of AWS IoT SiteWise to handle different types of data."
"What security considerations should be taken into account when using AWS IoT SiteWise?","Proper IAM role configuration and data encryption","Setting up a firewall","Configuring a VPN","Installing anti-virus software","Proper IAM role configuration and data encryption are crucial security considerations when using AWS IoT SiteWise to protect your data and control access to your resources."
"How does AWS IoT SiteWise help in Predictive Maintenance?","By collecting, storing, and analysing sensor data to identify potential failures","By automatically repairing broken equipment","By providing financial reports on maintenance costs","By controlling access to maintenance records","AWS IoT SiteWise can help in predictive maintenance by collecting, storing, and analysing sensor data to identify potential failures before they occur, allowing for proactive maintenance."
"In AWS IoT SiteWise, what is the benefit of using 'asset transforms'?","To derive new metrics from existing data","To control access to asset data","To optimise data storage for asset data","To visualise asset data in dashboards","Asset transforms in AWS IoT SiteWise allow you to derive new metrics from existing data by performing calculations and transformations, providing deeper insights into your asset performance."
"Which AWS IoT SiteWise feature would you use to create a hierarchy of equipment within a manufacturing plant?","Asset Models","Asset Properties","Asset Metrics","Asset Transforms","Asset Models are used to define the structure and behaviour of assets, including creating hierarchies of equipment within a manufacturing plant."
"What is the key advantage of using AWS IoT SiteWise Edge for data processing compared to cloud-based processing?","Reduced latency","Increased storage capacity","Enhanced security","Improved user interface","AWS IoT SiteWise Edge processes data locally, reducing latency compared to sending data to the cloud for processing, which is especially important for time-sensitive applications."
"Which service is used to create a view of your Industrial IoT Data in near real-time in AWS?","AWS IoT SiteWise Monitor","AWS IoT Events","AWS IoT TwinMaker","AWS IoT Analytics","AWS IoT SiteWise Monitor is used to create a view of your Industrial IoT Data in near real-time and requires minimal set up for data visualisation and is the right choice for most SiteWise use cases."
"Which AWS service helps create alarms for real-time monitoring of asset properties in AWS IoT SiteWise?","AWS IoT Events","AWS IoT Core","Amazon CloudWatch","AWS Lambda","AWS IoT Events helps create alarms for real-time monitoring of asset properties in AWS IoT SiteWise."
"What are the key components for streaming time series data with AWS IoT SiteWise?","Asset Models, Properties, and Data Streams","Gateways, Databases, and Dashboards","Functions, Variables, and Alarms","Rules, Policies, and Certificates","Asset Models, Properties, and Data Streams are key components for streaming time series data with AWS IoT SiteWise."
"How do you create a virtual representation of a physical system for real-time monitoring and analysis in AWS IoT SiteWise?","By defining Asset Models and relationships","By creating IoT Rules and Actions","By configuring AWS Lambda functions","By setting up CloudWatch Alarms and Metrics","By defining Asset Models and relationships you create a virtual representation of a physical system for real-time monitoring and analysis."
"In AWS IoT SiteWise, which component is responsible for data ingestion from on-premise industrial equipment?","AWS IoT SiteWise Gateway","AWS IoT Core","AWS IoT Analytics","AWS Lambda","AWS IoT SiteWise Gateway is responsible for data ingestion from on-premise industrial equipment."
"Which AWS service can be integrated with AWS IoT SiteWise to provide serverless event-driven computing capabilities based on asset property changes?","AWS Lambda","AWS Glue","AWS SQS","AWS Step Functions","AWS Lambda can be integrated with AWS IoT SiteWise to provide serverless event-driven computing capabilities based on asset property changes."
"When using AWS IoT SiteWise, which IAM permission is essential for allowing devices to send data to SiteWise?","iotsitewise:BatchPutAssetPropertyValue","s3:GetObject","dynamodb:GetItem","lambda:InvokeFunction","iotsitewise:BatchPutAssetPropertyValue allows devices to send data to SiteWise."
