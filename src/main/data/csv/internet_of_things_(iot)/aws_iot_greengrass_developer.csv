"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS IoT Greengrass?","Extending cloud capabilities to edge devices","Managing AWS Lambda functions in the cloud","Providing serverless computing capabilities","Managing VPC configurations","Greengrass allows you to run cloud applications locally on edge devices, extending AWS functionality to the edge."
"Which AWS service does AWS IoT Greengrass use for device authentication and authorisation?","AWS IoT Core","AWS IAM","AWS CloudTrail","Amazon Cognito","Greengrass uses AWS IoT Core for device authentication and authorisation, allowing secure communication between devices and the cloud."
"What is a Greengrass Core device?","The device that hosts the Greengrass runtime and manages local processing","A low-power sensor that collects data","A cloud-based server that runs Greengrass components","A mobile phone used for device configuration","The Greengrass Core is the device that runs the Greengrass runtime and manages the local execution of components and functions."
"What is a Greengrass Group?","A collection of Greengrass Core devices and associated devices.","A set of AWS IAM users with access to Greengrass resources","A group of AWS Lambda functions deployed to the cloud","A virtual private cloud for Greengrass devices","A Greengrass Group is a collection of Greengrass Core devices, client devices, and associated resources that work together."
"What type of applications are best suited for AWS IoT Greengrass?","Applications requiring low latency and offline operation","Applications with constant internet connectivity","Applications performing batch processing of large datasets","Applications requiring only cloud-based processing","Greengrass is ideal for applications that require low latency, offline operation, and local data processing because it brings compute to the edge."
"Which programming languages are commonly supported for AWS IoT Greengrass Lambda functions?","Python, Java, Node.js","C#, Ruby, Go","PHP, Perl, Swift","Pascal, Fortran, COBOL","Greengrass supports AWS Lambda functions written in Python, Java, and Node.js for local execution on edge devices."
"What is the purpose of Greengrass Connectors?","To simplify integration with third-party services and devices","To manage AWS IAM roles and policies","To monitor device health and performance","To configure network settings for Greengrass devices","Greengrass Connectors simplify integration with various third-party services, hardware, and protocols, reducing the need for custom code."
"Which of the following is a key benefit of using AWS IoT Greengrass for machine learning inference at the edge?","Reduced latency and cost","Centralised data storage","Simplified application deployment","Increased cloud resource utilisation","Running inference at the edge reduces latency and cost by processing data locally rather than sending it to the cloud."
"How does AWS IoT Greengrass handle communication between devices and the cloud when the connection is intermittent?","It uses local queues to store messages until connectivity is restored","It discards messages when connectivity is lost","It redirects messages to other online devices","It uses a cellular network for backup","Greengrass uses local queues to buffer messages when connectivity is lost, ensuring data is delivered to the cloud when the connection is restored."
"Which AWS service is used to manage and deploy Greengrass components to edge devices?","AWS IoT Device Management","AWS Systems Manager","AWS CloudFormation","AWS CodeDeploy","AWS IoT Device Management is used to manage and deploy Greengrass components and software updates to edge devices."
"What is the role of the Greengrass Security Manager?","To manage security policies and encryption keys on the Greengrass Core","To monitor network traffic for security threats","To manage user access control on the cloud","To perform vulnerability scans on edge devices","The Greengrass Security Manager manages security policies, encryption keys, and secure communication on the Greengrass Core device."
"Which type of Greengrass Component allows you to perform custom logic on edge devices?","Lambda function components","Connector components","Stream manager components","Secret manager components","Lambda function components allow you to run custom code on the edge for various tasks like data processing and machine learning inference."
"What is the purpose of the Greengrass Stream Manager?","To handle high-throughput data streams from edge devices to the cloud","To manage video streams for surveillance applications","To monitor network traffic for performance issues","To control data flow between AWS Lambda functions","The Stream Manager handles high-throughput data streams, allowing you to reliably send data to the cloud even with intermittent connectivity."
"How does AWS IoT Greengrass support offline operation?","By caching cloud data and executing logic locally","By redirecting requests to backup servers","By suspending operations until connectivity is restored","By using a satellite connection","Greengrass supports offline operation by caching cloud data and executing logic locally, enabling devices to function even without an internet connection."
"What is the significance of the AWS IoT Greengrass Hardware Security Integration (HSI)?","Allows for secure key storage in hardware security modules","Provides secure over-the-air updates","Ensures compliance with GDPR regulations","Facilitates secure boot processes","HSI allows for secure key storage and cryptographic operations within hardware security modules, enhancing the security of Greengrass devices."
"What is the function of the Greengrass CLI?","To locally manage and troubleshoot Greengrass deployments","To manage cloud resources using command-line interface","To create and deploy AWS Lambda functions","To configure network settings on edge devices","The Greengrass CLI is used to locally manage, configure, and troubleshoot Greengrass deployments on the Core device."
"Which AWS IoT Greengrass feature is used to securely access secrets such as API keys and passwords on edge devices?","Secrets Manager integration","IAM roles","Encryption keys","Certificate pinning","Greengrass integrates with AWS Secrets Manager to securely access secrets such as API keys and passwords on edge devices, preventing hardcoding of credentials."
"What is the role of the 'Greengrass nucleus' component?","The core runtime environment for Greengrass, managing component lifecycle","The component responsible for cloud connectivity","The component managing local device networking","The component that runs machine learning models","The nucleus is the core runtime environment that manages the lifecycle of components on the Greengrass Core."
"How can you update the Greengrass software on a Greengrass Core device?","Using Over-the-Air (OTA) updates","Manually installing the new version","Re-imaging the device","Deleting and recreating the device","Greengrass supports Over-the-Air (OTA) updates to update the software on the Greengrass Core device remotely."
"In AWS IoT Greengrass, what is a 'deployment'?","The process of distributing and activating Greengrass components to a group","A single execution of an AWS Lambda function","The process of creating a new Greengrass Group","A collection of AWS IoT devices","A deployment is the process of distributing and activating Greengrass components, configuration, and AWS Lambda functions to a Greengrass Group."
"Which communication protocols are commonly supported by AWS IoT Greengrass for device-to-device communication?","MQTT and HTTP","FTP and SMTP","TCP and UDP","SNMP and CoAP","Greengrass supports MQTT and HTTP protocols for local communication between devices and components within a Greengrass Group."
"What is the advantage of using AWS IoT Greengrass with AWS IoT SiteWise?","To process and analyse industrial data locally","To store industrial data in the cloud","To visualise industrial data in real-time","To generate reports on industrial data","Greengrass allows local processing and analysis of industrial data from AWS IoT SiteWise, reducing latency and bandwidth costs."
"What type of Greengrass component allows you to ingest data from Modbus devices?","Connector component","Lambda function component","Stream manager component","Secret manager component","Connector components provide pre-built integration with various industrial protocols like Modbus."
"Which AWS service can be used to monitor the health and performance of Greengrass Core devices?","AWS CloudWatch","AWS X-Ray","AWS CloudTrail","AWS Config","AWS CloudWatch can be used to monitor metrics and logs from Greengrass Core devices, providing insights into their health and performance."
"What is the purpose of the Greengrass device shadow?","To store the last known state of a device","To encrypt data on the device","To manage device connectivity","To control device access permissions","The Greengrass device shadow stores the last known state of a device, allowing applications to query and interact with the device even when it is offline."
"How does AWS IoT Greengrass contribute to improved security in IoT solutions?","By enabling local encryption and secure boot","By providing centralised access control","By monitoring network traffic for security threats","By performing vulnerability scans on edge devices","Greengrass enhances security by enabling local encryption, secure boot, and secure key management on edge devices."
"Which statement about AWS IoT Greengrass is correct?","It enables local processing even with intermittent cloud connectivity","It requires a constant connection to the AWS Cloud","It is only suitable for static IoT devices","It supports only one programming language","Greengrass enables local processing and decision-making even when the connection to the cloud is intermittent."
"What is the main role of AWS Lambda functions in AWS IoT Greengrass?","Executing custom code on edge devices","Storing device data in the cloud","Managing device security policies","Monitoring device performance metrics","AWS Lambda functions are used to execute custom code on edge devices, allowing for local data processing and decision-making."
"What is the benefit of using AWS IoT Greengrass for running machine learning models on edge devices?","Reduced latency in inference","Increased cloud processing costs","Simplified cloud deployment procedures","Improved device power consumption","Running inference locally on edge devices reduces latency as data doesn't need to be sent to the cloud."
"Which of the following is an example of a use case for AWS IoT Greengrass?","Smart factories with real-time monitoring and control","Cloud-based data warehousing","Serverless web application hosting","Centralised user authentication","Smart factories benefit from Greengrass's real-time monitoring and control capabilities at the edge."
"How does AWS IoT Greengrass enable offline operation for IoT devices?","By caching data and executing logic locally","By redirecting requests to backup cloud servers","By suspending operations until connectivity is restored","By using a cellular network as a backup connection","Greengrass provides offline capabilities by caching data and executing logic locally on the edge."
"Which type of AWS IoT Greengrass component allows you to integrate with external hardware devices?","Connector component","Lambda function component","Stream manager component","Shadow component","Connector components offer pre-built integration with various hardware devices and protocols."
"What is the role of the AWS IoT Greengrass discovery service?","To enable devices to find the Greengrass Core device on the local network","To discover new AWS services","To manage device shadows","To configure network settings","The discovery service enables devices to find the Greengrass Core device on the local network, facilitating communication."
"Which AWS service is commonly integrated with AWS IoT Greengrass to securely manage device certificates?","AWS IoT Device Management","AWS Certificate Manager","AWS IAM","AWS Key Management Service (KMS)","AWS IoT Device Management is commonly integrated to securely manage device certificates."
"What is the purpose of the AWS IoT Greengrass OTA (Over-The-Air) update feature?","To remotely update the Greengrass Core software","To update device firmware","To update AWS IAM roles","To update cloud configurations","OTA updates allow for remote updates of the Greengrass Core software on edge devices."
"How can you deploy a new version of a Greengrass component to a fleet of devices?","By creating a new Greengrass deployment","By manually updating each device","By using AWS CloudFormation","By restarting the Greengrass Core","New versions of components are deployed to a fleet of devices by creating a new Greengrass deployment."
"What is the benefit of using AWS IoT Greengrass with containerised applications?","Improved portability and isolation of applications","Reduced hardware costs","Increased cloud storage capacity","Simplified server management","Containerisation with Greengrass improves application portability and isolation."
"Which AWS IoT Greengrass feature helps manage and control the flow of data between edge devices and the cloud?","Stream Manager","Lambda Manager","Shadow Manager","Device Manager","The Stream Manager helps manage and control the flow of data between edge devices and the cloud."
"What is the main advantage of using AWS IoT Greengrass for data processing at the edge?","Reduced latency and bandwidth usage","Increased cloud storage capacity","Simplified cloud deployment procedures","Improved cloud security","Data processing at the edge reduces latency and minimises the need to transfer large amounts of data to the cloud."
"Which component of AWS IoT Greengrass handles local messaging between devices and AWS Lambda functions?","Greengrass Core","AWS IoT Core","AWS Lambda","MQTT Broker","The Greengrass Core handles local messaging between devices and AWS Lambda functions within the Greengrass Group."
"What is a key security benefit of using AWS IoT Greengrass?","Secure local storage of encryption keys","Centralised security management in the cloud","Automated vulnerability patching","Improved network security","Greengrass provides secure local storage of encryption keys, enhancing the security of edge devices."
"What is the purpose of a Greengrass deployment configuration?","To define how Greengrass components are deployed and configured","To manage AWS IAM policies","To define network settings","To monitor device health","A Greengrass deployment configuration defines how Greengrass components are deployed and configured on edge devices."
"Which AWS service can be used to manage and monitor the software inventory on Greengrass Core devices?","AWS IoT Device Management","AWS Systems Manager","AWS CloudWatch","AWS Config","AWS IoT Device Management provides the ability to manage and monitor software inventory on Greengrass Core devices."
"In AWS IoT Greengrass, what does the term 'local resource access' refer to?","Allowing Greengrass components to access local hardware resources","Providing access to AWS resources from the edge","Managing network access policies","Managing user authentication","Local resource access refers to the ability of Greengrass components to access local hardware resources, such as cameras, sensors, and serial ports."
"Which component in AWS IoT Greengrass facilitates communication between devices and the Greengrass core?","MQTT bridge","HTTP client","TCP socket","Websocket","The MQTT bridge facilitates communication between devices and the Greengrass Core using the MQTT protocol."
"What is the benefit of using AWS IoT Greengrass for industrial automation applications?","Real-time control and reduced latency","Simplified cloud deployment procedures","Improved device power consumption","Increased cloud storage capacity","Industrial automation applications benefit from the real-time control and reduced latency that Greengrass provides."
"Which AWS service is used for securely managing, storing, and retrieving secrets used by Greengrass components?","AWS Secrets Manager","AWS IAM","AWS KMS","AWS Certificate Manager","AWS Secrets Manager is used for securely managing, storing, and retrieving secrets such as API keys and passwords used by Greengrass components."
"How can you isolate Greengrass components from each other on the Greengrass Core device?","Using containerisation","Using AWS IAM roles","Using virtual machines","Using hardware firewalls","Greengrass components can be isolated from each other by using containerisation, which provides a secure and isolated runtime environment."
"What is the purpose of AWS IoT Greengrass 'functions as a service' model?","To enable event-driven processing on the edge","To reduce server management overhead","To simplify cloud deployment","To improve device battery life","The 'functions as a service' model enables event-driven processing on the edge by allowing AWS Lambda functions to be executed in response to local events."
"Which communication pattern is used for sending telemetry data from Greengrass devices to the cloud?","MQTT","HTTP","CoAP","AMQP","MQTT is a lightweight messaging protocol commonly used for sending telemetry data from Greengrass devices to the cloud."
"What is the primary advantage of using AWS IoT Greengrass for edge computing in remote locations with limited network connectivity?","Enables local data processing and storage","Provides centralised security management","Simplifies cloud deployment procedures","Improves device battery life","Greengrass enables local data processing and storage, allowing IoT devices to operate effectively even in remote locations with limited or intermittent network connectivity."
"In AWS IoT Greengrass, what is the primary function of the Greengrass core device?","Running local inference and processing data","Managing AWS IAM roles","Hosting the AWS IoT Device Shadow service","Storing historical data for analytics","The Greengrass core device acts as a secure gateway and compute device, enabling local inference, messaging, and data processing closer to the source."
"What AWS service is most directly integrated with AWS IoT Greengrass to manage and deploy machine learning models to edge devices?","Amazon SageMaker Neo","AWS Lambda","Amazon Rekognition","Amazon S3","Amazon SageMaker Neo allows you to compile and optimise machine learning models for Greengrass devices."
"Which security component in AWS IoT Greengrass is responsible for securing the communication between Greengrass components?","Local Resource Access","Greengrass Device Defender","MQTT Broker","Greengrass Secret Manager","The MQTT Broker handles the secure communication between the different Greengrass components within the Greengrass core."
"In AWS IoT Greengrass, what is the role of a Greengrass Group?","A container for Greengrass core, devices, and configurations","A service for managing device authentication","A storage service for device logs","A monitoring tool for device health","A Greengrass Group is a collection of settings that define a Greengrass deployment, including core device, devices, subscriptions, and resources."
"How does AWS IoT Greengrass enable offline operation for IoT devices?","By caching data and executing Lambda functions locally","By redirecting requests to the cloud","By disabling device communication","By queuing all device messages for later delivery","Greengrass allows devices to continue operating even when disconnected from the cloud by caching data and executing Lambda functions locally."
"What type of function does AWS IoT Greengrass use to extend cloud capabilities to edge devices?","Lambda functions","EC2 functions","S3 functions","IAM functions","Greengrass utilises Lambda functions as a way to execute custom logic and extend cloud capabilities to edge devices."
"Which AWS service is used to securely store and manage secrets, such as API keys and passwords, that are used by Greengrass components?","AWS Secrets Manager","AWS Key Management Service (KMS)","Amazon S3","AWS IAM","AWS Secrets Manager is the recommended service for storing and managing secrets used by Greengrass components, providing secure access and rotation capabilities."
"What is the primary benefit of using AWS IoT Greengrass Connectors?","Simplifying integration with third-party services and protocols","Reducing the cost of device hardware","Improving device battery life","Enhancing device security","Greengrass Connectors provide pre-built integrations with various cloud services and protocols, simplifying the development process."
"In AWS IoT Greengrass, what is the purpose of the Greengrass nucleus?","To manage the lifecycle of Greengrass components","To store device configuration data","To handle device authentication","To provide a user interface for device management","The Greengrass nucleus is the core runtime environment responsible for managing the lifecycle of Greengrass components, including deployment, updates, and execution."
"How can you configure AWS IoT Greengrass to securely access local device resources, such as serial ports and cameras?","Using Local Resource Access policies","Using IAM roles","Using VPC endpoints","Using security groups","Local Resource Access policies allow you to define which resources a Greengrass component can access on the local device, ensuring secure access control."
"What is the role of 'Subscriptions' in the context of AWS IoT Greengrass?","To define message routing between components and the cloud","To manage device billing","To configure device software updates","To track device location","Subscriptions define how messages are routed between Greengrass components, AWS IoT Core, and other services."
"Which type of deployment strategy is typically used for updating AWS IoT Greengrass core software?","Over-the-air (OTA) updates","Rolling updates","Blue/Green deployments","Canary deployments","Greengrass core software is usually updated using over-the-air (OTA) updates to minimise downtime and ensure seamless transitions."
"What is the purpose of the Greengrass CLI?","To interact with a Greengrass core device locally","To monitor AWS IoT Core","To create IAM roles","To manage S3 buckets","The Greengrass CLI allows you to interact with a Greengrass core device locally, for tasks like deploying configurations and checking component status."
"In AWS IoT Greengrass, what is the difference between 'functions' and 'connectors'?","Functions are custom code, connectors are pre-built integrations","Functions are deployed on the cloud, connectors on the device","Functions manage device security, connectors manage data storage","Functions are written in Python, connectors in Java","Functions are custom code (often Lambda functions) that you write, while connectors are pre-built modules for integrating with other services and protocols."
"How does AWS IoT Greengrass handle intermittent connectivity to the cloud?","By queuing messages and synchronising data when connection is restored","By discarding messages when connection is lost","By switching to a backup network","By sending alerts to administrators","Greengrass queues messages and synchronises data when the connection to the cloud is restored, ensuring no data loss."
"Which AWS IoT Greengrass feature allows you to define which resources a Greengrass component can access on the local device, enhancing security?","Local Resource Access","IAM Roles","VPC Endpoints","Security Groups","Local Resource Access allows you to define fine-grained permissions for Greengrass components to access local resources, improving security."
"What is a primary advantage of running machine learning inference at the edge using AWS IoT Greengrass?","Reduced latency and improved privacy","Increased cloud compute costs","Simplified deployment process","Improved battery life of cloud servers","Running inference at the edge reduces latency, improves privacy, and allows devices to operate even without a constant cloud connection."
"Which deployment method is recommended for ensuring that AWS IoT Greengrass core devices always have the latest software updates?","Automatic OTA updates","Manual updates via the AWS Management Console","Using custom scripts","Disabling automatic updates","Automatic OTA updates are recommended to ensure that Greengrass core devices always have the latest security patches and features."
"What type of device can act as a Greengrass core device?","Any device running Linux or Windows and meeting minimum hardware requirements","Only devices certified by AWS","Only devices with a specific CPU architecture","Only devices connected to a wired network","A Greengrass core device can be any device running Linux or Windows and meeting the minimum hardware and software requirements."
"When defining a subscription in AWS IoT Greengrass, what does the 'topic filter' specify?","The MQTT topic to which the subscription applies","The geographical location of the device","The type of encryption used","The data format of the message","The topic filter specifies the MQTT topic to which the subscription applies, defining which messages are routed."
"How does AWS IoT Greengrass facilitate secure communication between devices and the cloud?","Using TLS encryption and device authentication","Using public IP addresses","Using unencrypted HTTP","Using SMS messages","Greengrass uses TLS encryption and device authentication to ensure secure communication between devices and the cloud."
"Which AWS service is commonly used to build and deploy containerised applications that run on AWS IoT Greengrass?","Amazon ECR","AWS Lambda","Amazon S3","AWS CodeBuild","Amazon ECR (Elastic Container Registry) is commonly used to store and manage container images that are deployed on Greengrass core devices."
"What is the purpose of the 'Greengrass Seed' group deployment?","To provision a new Greengrass core device","To update existing Greengrass groups","To delete a Greengrass core device","To monitor device health","The Greengrass Seed group deployment is used to provision a new Greengrass core device and configure its initial settings."
"In AWS IoT Greengrass, what is the purpose of the 'Stream Manager'?","To buffer and process data streams before sending them to the cloud","To manage device inventory","To control device access","To monitor device health","The Stream Manager buffers and processes data streams locally before sending them to the cloud, optimising bandwidth usage and ensuring data delivery."
"Which type of security certificates are used by AWS IoT Greengrass devices to authenticate with AWS IoT Core?","X.509 certificates","SSL certificates","Self-signed certificates","Kerberos tickets","Greengrass devices use X.509 certificates to authenticate with AWS IoT Core, ensuring secure device identity."
"What is the function of the 'Greengrass Resource Access Manager (RAM)'?","Not a valid Greengrass feature","Managing access to AWS resources from Greengrass components","Managing access to local device resources from Greengrass components","Managing access to cloud resources from local resources","The Resource Access Manager is not a valid AWS Greengrass feature."
"What is the significance of the Greengrass software being open-source?","It allows for community contributions and customisation","It eliminates the need for AWS support","It guarantees compatibility with all devices","It reduces the cost of using Greengrass","Being open-source, Greengrass allows community contributions, customisations, and transparency, enabling users to adapt the software to their specific needs."
"Which AWS service can be used to monitor the performance and health of AWS IoT Greengrass core devices?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch is used to monitor the performance and health of Greengrass core devices, providing metrics, logs, and alarms."
"What is a primary use case for AWS IoT Greengrass in an industrial setting?","Predictive maintenance and anomaly detection","Managing office printers","Creating websites","Running video games","Greengrass is commonly used for predictive maintenance and anomaly detection in industrial settings, enabling real-time insights and optimisation."
"How does AWS IoT Greengrass support containerisation of applications?","By integrating with Docker and other container runtimes","By automatically converting Lambda functions into containers","By requiring all applications to be written in Java","By limiting applications to a specific size","Greengrass supports containerisation by integrating with Docker and other container runtimes, allowing users to deploy containerised applications to edge devices."
"What is the maximum number of devices that can be associated with a single Greengrass group?","Unlimited","Hundreds","Thousands","Tens","A single Greengrass group can support hundreds of devices, enabling deployments across a significant number of edge nodes."
"Which programming language is commonly used to write custom Lambda functions for AWS IoT Greengrass?","Python","C++","Java","C#","Python is a common language used to write Lambda functions for Greengrass due to its ease of use and extensive libraries."
"What is the purpose of the Greengrass Secret Manager integration?","To securely store and manage secrets used by Greengrass components","To encrypt data at rest","To manage user authentication","To audit API calls","The Secret Manager integration allows you to securely store and manage secrets, such as API keys and passwords, used by Greengrass components."
"How does AWS IoT Greengrass handle data synchronisation between the edge and the cloud?","Using MQTT messages and shadow synchronisation","Using FTP","Using email","Using direct database connections","Greengrass uses MQTT messages and shadow synchronisation to ensure data consistency between the edge and the cloud."
"Which of the following is NOT a typical component of an AWS IoT Greengrass group definition?","IAM role","CloudFormation template","Subscriptions","Local resources","A CloudFormation template is not part of a Greengrass Group definition."
"What is the benefit of using AWS IoT Greengrass in a remote location with limited internet connectivity?","Enables local processing and reduces reliance on the cloud","Increases cloud compute costs","Simplifies device management","Improves device battery life","Greengrass enables local processing and reduces reliance on the cloud, which is especially beneficial in remote locations with limited connectivity."
"Which AWS IoT Greengrass feature allows you to control which local resources (e.g., serial ports, GPUs) Greengrass components can access?","Local Resource Access","IAM roles","VPC endpoints","Security groups","Local Resource Access allows you to define and control which local resources Greengrass components can access, enhancing security."
"What is the purpose of a Greengrass connector's 'configuration'?","To specify how the connector interacts with other components and services","To define the device's physical location","To configure the device's network settings","To set the device's time zone","A connector's configuration defines how it interacts with other components and services, allowing you to customise its behaviour."
"In AWS IoT Greengrass, what is the relationship between AWS Lambda functions and Greengrass components?","Lambda functions are packaged as Greengrass components for deployment to the edge","Lambda functions are only executed in the cloud","Greengrass components can only be written in Java","Lambda functions are not supported by Greengrass","Lambda functions are packaged as Greengrass components, allowing you to deploy custom logic to the edge."
"Which of the following is an advantage of using AWS IoT Greengrass for computer vision applications at the edge?","Reduced latency and bandwidth usage","Increased cloud storage costs","Simplified device management","Improved battery life of cloud servers","Greengrass reduces latency and bandwidth usage by processing image data locally, which is crucial for real-time computer vision applications."
"What is the role of the Greengrass Device Defender connector?","To monitor and report device security metrics","To encrypt data at rest","To manage user authentication","To control device access","The Device Defender connector monitors and reports device security metrics, helping to identify and mitigate security threats."
"How does AWS IoT Greengrass support machine learning inference at the edge?","By integrating with Amazon SageMaker Neo to optimise models","By requiring all models to be written in Python","By limiting model size to 1 MB","By disabling model encryption","Greengrass integrates with Amazon SageMaker Neo to compile and optimise machine learning models for efficient execution on edge devices."
"What is the purpose of the AWS IoT Greengrass core software?","To provide the runtime environment for Greengrass components","To manage device inventory","To control device access","To monitor device health","The Greengrass core software provides the runtime environment for Greengrass components, enabling them to execute on edge devices."
"What is the primary purpose of the Greengrass Cloud Discovery service?","To allow Greengrass core devices to discover AWS IoT Core endpoints","To manage device software updates","To encrypt data in transit","To control device access","The Cloud Discovery service allows Greengrass core devices to discover the necessary AWS IoT Core endpoints for communication."
"Which AWS service would you typically use to build and deploy a custom containerised application to run on AWS IoT Greengrass?","AWS ECS or AWS EKS","AWS Lambda","AWS IAM","Amazon S3","AWS ECS (Elastic Container Service) or AWS EKS (Elastic Kubernetes Service) would be used to build and deploy the application, then deploy it as a container to run on Greengrass."
"What is the main advantage of using AWS IoT Greengrass for data analytics at the edge?","Reduced latency and cost of data transfer","Improved device battery life","Simplified device management","Increased cloud compute costs","Greengrass reduces latency and cost by processing data locally, avoiding the need to transfer all data to the cloud for analysis."
"What is the key function of AWS IoT Greengrass subscriptions?","To define message routing between components, devices and the cloud","To control access to local device resources","To define the software to be installed on a device","To monitor the power usage of edge devices","Subscriptions define the flow of MQTT messages between components, devices, and the cloud, enabling flexible and dynamic communication."
