"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Location Service, which CLI command is used to create a new map resource?","aws location create-map","aws location new-map","aws location init-map","aws location start-map","'aws location create-map' is the correct command to create a new map resource in Amazon Location Service."
"Which IAM permission is required for a developer to publish geofence collections in Amazon Location Service?","geo:PutGeofence","location:PublishGeofence","geo:CreateGeofence","location:PutGeofence","'geo:PutGeofence' is required to publish geofence collections in Amazon Location Service."
"A developer needs to enable encryption at rest for an Amazon Location Service tracker. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","Amazon Location Service uses AWS KMS to manage encryption keys for data at rest."
"Which Amazon Location Service feature allows for sharing map resources across multiple accounts?","Resource-based policies","Cross-account domains","Map federation","Map replication","Resource-based policies enable sharing map resources across AWS accounts in Amazon Location Service."
"A developer wants to restrict access to a tracker resource to specific VPC endpoints. Which configuration should be used?","VPC endpoint policies and security group rules","Parameter group settings","Tracker policy only","IAM user group","VPC endpoint policies and security groups restrict access to tracker resources in Amazon Location Service."
"Which CLI command is used to associate a geofence collection with a tracker in Amazon Location Service?","aws location associate-tracker-consumer","aws location add-geofence","aws location link-tracker","aws location connect-geofence","'aws location associate-tracker-consumer' associates a geofence collection with a tracker in Amazon Location Service."
"A developer needs to monitor Amazon Location Service map usage. Which CloudWatch metric should they use?","MapViewCount","GeofenceCount","TrackerUpdateCount","RouteCalculationCount","'MapViewCount' shows the number of map views in Amazon Location Service."
"Which Amazon Location Service feature allows for route calculation between two points?","Route calculator resource","Geofence collection","Map resource only","Tracker resource only","A route calculator resource enables route calculation between two points in Amazon Location Service."
"A developer wants to connect to Amazon Location Service using IAM authentication. What must be enabled on the resource?","IAM authentication for Location Service","Kerberos authentication","LDAP integration","SAML federation","IAM authentication must be enabled to use IAM for connecting to Amazon Location Service."
"Which Amazon Location Service feature provides immutable storage for geofence events?","Geofence event history","Map resource only","Route calculator only","Tracker resource only","Geofence event history provides immutable storage for geofence events in Amazon Location Service."
"A developer needs to automate Amazon Location Service map resource creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate Amazon Location Service map resource provisioning as part of CI/CD pipelines."
"Which Amazon Location Service feature allows for cross-region map replication?","Resource-based policies with cross-region support","Map federation only","Geofence collection replication only","Route calculator replication only","Resource-based policies with cross-region support enable map replication in Amazon Location Service."
"A developer wants to monitor failed geofence events in Amazon Location Service. Which CloudWatch metric should be used?","GeofenceFailedEvents","MapViewCount","TrackerUpdateCount","RouteCalculationCount","'GeofenceFailedEvents' tracks failed geofence events in Amazon Location Service."
"Which CLI command is used to delete a geofence collection in Amazon Location Service?","aws location delete-geofence-collection","aws location remove-geofence-collection","aws location destroy-geofence-collection","aws location drop-geofence-collection","'aws location delete-geofence-collection' deletes a geofence collection in Amazon Location Service."
"A developer needs to restrict Amazon Location Service access to specific IAM roles. What should be configured?","IAM policies with role-level permissions","VPC endpoint policies only","Resource policy only","Parameter group","IAM policies can restrict Amazon Location Service access to specific IAM roles."
"Which Amazon Location Service feature provides geofence retention policies?","Geofence collection lifecycle policies","Map resource only","Route calculator only","Tracker resource only","Geofence collection lifecycle policies manage geofence retention in Amazon Location Service."
"A developer wants to automate Amazon Location Service tracker deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Amazon Location Service trackers."
"Which Amazon Location Service feature allows for restricting access to specific geofence collections?","IAM policy with collection-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific geofence collections in Amazon Location Service."
"A developer needs to monitor Amazon Location Service for excessive map views. Which CloudWatch metric should be used?","MapViewCount","GeofenceFailedEvents","TrackerUpdateCount","RouteCalculationCount","'MapViewCount' shows the number of map views in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence events?","IAM policy with event-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific geofence events in Amazon Location Service."
"A developer wants to automate Amazon Location Service geofence collection creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate geofence collection provisioning as part of CI/CD pipelines."
"Which Amazon Location Service feature allows for restricting access to specific map styles?","IAM policy with style-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific map styles in Amazon Location Service."
"A developer needs to monitor Amazon Location Service for excessive geofence events. Which CloudWatch metric should be used?","GeofenceEventCount","MapViewCount","TrackerUpdateCount","RouteCalculationCount","'GeofenceEventCount' tracks the number of geofence events in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific tracker resources?","IAM policy with tracker-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific tracker resources in Amazon Location Service."
"A developer wants to automate Amazon Location Service map style updates. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate map style updates for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific route calculator resources?","IAM policy with route calculator-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific route calculator resources in Amazon Location Service."
"A developer needs to monitor Amazon Location Service for failed tracker updates. Which CloudWatch metric should be used?","TrackerUpdateFailedEvents","MapViewCount","GeofenceEventCount","RouteCalculationCount","'TrackerUpdateFailedEvents' tracks failed tracker updates in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence event types?","IAM policy with event type-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific geofence event types in Amazon Location Service."
"A developer wants to automate Amazon Location Service tracker update scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tracker update scheduling for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific map resources?","IAM policy with map-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific map resources in Amazon Location Service."
"A developer needs to automate Amazon Location Service tracker resource creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate tracker resource provisioning as part of CI/CD pipelines."
"Which Amazon Location Service feature allows for restricting access to specific tracker update actions?","IAM policy with update action-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific tracker update actions in Amazon Location Service."
"A developer wants to monitor Amazon Location Service for excessive tracker updates. Which CloudWatch metric should be used?","TrackerUpdateCount","MapViewCount","GeofenceEventCount","RouteCalculationCount","'TrackerUpdateCount' tracks the number of tracker updates in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific tracker event types?","IAM policy with tracker event type-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific tracker event types in Amazon Location Service."
"A developer wants to automate Amazon Location Service geofence event scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate geofence event scheduling for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence event schedules?","IAM policy with schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific geofence event schedules in Amazon Location Service."
"A developer needs to monitor Amazon Location Service for excessive route calculations. Which CloudWatch metric should be used?","RouteCalculationCount","MapViewCount","GeofenceEventCount","TrackerUpdateCount","'RouteCalculationCount' tracks the number of route calculations in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific route calculation actions?","IAM policy with calculation action-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific route calculation actions in Amazon Location Service."
"A developer wants to automate Amazon Location Service route calculator updates. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate route calculator updates for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific route calculation schedules?","IAM policy with route schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific route calculation schedules in Amazon Location Service."
"A developer wants to automate Amazon Location Service route calculation scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate route calculation scheduling for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific route calculation endpoints?","VPC security group rules","Parameter group settings","Resource policy only","IAM user group","Security group rules can restrict access to specific route calculation endpoints in Amazon Location Service."
"A developer needs to ensure Amazon Location Service is only accessible from specific AWS accounts. What should be configured?","VPC endpoint policies and security group rules with account restrictions","Parameter group settings","Resource policy only","Map policy only","VPC endpoint policies and security groups can restrict access to specific AWS accounts for Amazon Location Service."
"Which Amazon Location Service feature allows for monitoring route calculation failures?","CloudWatch RouteCalculationFailedEvents metric","MapViewCount","GeofenceEventCount","TrackerUpdateCount","The RouteCalculationFailedEvents metric in CloudWatch tracks failed route calculations in Amazon Location Service."
"A developer wants to automate Amazon Location Service map resource tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for map resources in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific map resource tags?","IAM policy with tag-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon Location Service map resources."
"A developer needs to monitor Amazon Location Service for excessive map resource tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch MapViewCount metric","RouteCalculationFailedEvents metric","GeofenceEventCount metric","CloudTrail logs map resource tag changes in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific map resource endpoints?","VPC security group rules","Parameter group settings","Resource policy only","IAM user group","Security group rules can restrict access to specific endpoints in Amazon Location Service map resources."
"A developer wants to automate Amazon Location Service geofence collection backup scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup scheduling for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific backup jobs in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive geofence collection backup failures. Which CloudWatch metric should be used?","BackupFailure metric","RouteCalculationFailedEvents metric","MapViewCount metric","GeofenceEventCount metric","'BackupFailure' tracks the number of failed backup jobs in Amazon Location Service geofence collections."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection backup retention policies?","IAM policy with retention policy-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific backup retention policies in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection restore operations. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore operations for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore jobs?","IAM policy with restore job-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore jobs in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive restore job failures. Which CloudWatch metric should be used?","RestoreFailure metric","BackupFailure metric","RouteCalculationFailedEvents metric","MapViewCount metric","'RestoreFailure' tracks the number of failed restore jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore operations?","IAM policy with restore operation-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore operations in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection restore retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore retention policies for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore schedules?","IAM policy with restore schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore schedules in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive restore jobs. Which CloudWatch metric should be used?","RestoreJobCount","BackupFailure metric","RouteCalculationFailedEvents metric","MapViewCount metric","'RestoreJobCount' tracks the number of restore jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore formats?","IAM policy with restore format-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore formats in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection restore format management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore format management for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore schedules?","IAM policy with restore schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore schedules in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive geofence collection restore jobs. Which CloudWatch metric should be used?","RestoreJobCount","BackupFailure metric","RouteCalculationFailedEvents metric","MapViewCount metric","'RestoreJobCount' tracks the number of restore jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection restore formats?","IAM policy with restore format-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific restore formats in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection export scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export scheduling for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection export jobs?","IAM policy with export job-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific export jobs in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive export job failures. Which CloudWatch metric should be used?","ExportFailure metric","BackupFailure metric","RestoreFailure metric","RouteCalculationFailedEvents metric","'ExportFailure' tracks the number of failed export jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection export retention policies?","IAM policy with export retention policy-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific export retention policies in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection export retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export retention policies for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection export schedules?","IAM policy with export schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific export schedules in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection export format management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export format management for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection export formats?","IAM policy with export format-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific export formats in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive geofence collection export jobs. Which CloudWatch metric should be used?","ExportJobCount","BackupFailure metric","RestoreFailure metric","RouteCalculationFailedEvents metric","'ExportJobCount' tracks the number of export jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection export schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to export schedules by region in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection import scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import scheduling for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import jobs?","IAM policy with import job-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific import jobs in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive import job failures. Which CloudWatch metric should be used?","ImportFailure metric","ExportFailure metric","BackupFailure metric","RouteCalculationFailedEvents metric","'ImportFailure' tracks the number of failed import jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import retention policies?","IAM policy with import retention policy-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific import retention policies in Amazon Location Service geofence collections."
"A developer wants to automate Amazon Location Service geofence collection import retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import retention policies for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import schedules?","IAM policy with import schedule-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific import schedules in Amazon Location Service geofence collections."
"A developer needs to automate Amazon Location Service geofence collection import job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate import job provisioning as part of CI/CD pipelines for Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import actions?","IAM policy with import action-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific import actions in Amazon Location Service geofence collections."
"A developer wants to monitor Amazon Location Service for excessive import jobs. Which CloudWatch metric should be used?","ImportJobCount","ExportJobCount","BackupFailure metric","RestoreFailure metric","'ImportJobCount' tracks the number of import jobs in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import formats?","IAM policy with import format-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to specific import formats in Amazon Location Service geofence collections."
"A developer needs to ensure Amazon Location Service is only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","Resource policy only","Map policy only","Security group rules can restrict access to specific IP ranges in Amazon Location Service."
"Which Amazon Location Service feature allows for monitoring import job failures?","CloudWatch ImportFailure metric","ExportFailure metric","BackupFailure metric","RestoreFailure metric","The ImportFailure metric in CloudWatch tracks failed import jobs in Amazon Location Service."
"A developer wants to automate Amazon Location Service import retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import retention policies for geofence collections in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import endpoints?","VPC security group rules","Parameter group settings","Resource policy only","IAM user group","Security group rules can restrict access to specific import endpoints in Amazon Location Service geofence collections."
"A developer needs to monitor Amazon Location Service for excessive import job tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch ImportJobCount metric","ExportFailure metric","RestoreFailure metric","CloudTrail logs import job tag changes in Amazon Location Service."
"Which Amazon Location Service feature allows for restricting access to specific geofence collection import schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","Resource policy only","Parameter group","IAM policies can restrict access to import schedules by region in Amazon Location Service geofence collections."
