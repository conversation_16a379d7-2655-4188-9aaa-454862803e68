"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon DocumentDB, which CLI command is used to create a new cluster?","aws docdb create-db-cluster","aws documentdb create-cluster","aws docdb new-cluster","aws docdb start-cluster","'aws docdb create-db-cluster' is the correct command to create a new DocumentDB cluster."
"Which IAM permission is required for a developer to create a DocumentDB instance?","rds:CreateDBInstance","docdb:CreateInstance","documentdb:CreateDBInstance","rds:ProvisionDBInstance","'rds:CreateDBInstance' is required to create a DocumentDB instance as it uses the RDS API."
"A developer needs to enable encryption at rest for a DocumentDB cluster. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","DocumentDB uses AWS KMS to manage encryption keys for data at rest."
"Which DocumentDB feature allows for automatic failover in the event of a primary instance failure?","Replica promotion in a cluster","Read replica in another region","Manual failover only","Snapshot restore","Replica promotion in a cluster provides automatic failover in DocumentDB."
"A developer wants to restrict access to a DocumentDB cluster to specific VPC subnets. Which configuration should be used?","DB subnet group","Parameter group","Option group","Security group only","A DB subnet group defines which VPC subnets DocumentDB instances are deployed in, restricting access."
"Which CLI command is used to create a new parameter group for DocumentDB?","aws docdb create-db-cluster-parameter-group","aws documentdb create-parameter-group","aws docdb new-parameter-group","aws docdb create-parameter-group","'aws docdb create-db-cluster-parameter-group' is the correct command for creating a parameter group for DocumentDB."
"A developer needs to monitor query performance in DocumentDB. Which CloudWatch metric should they use?","DatabaseConnections","CPUUtilization","FreeableMemory","NetworkReceiveThroughput","'DatabaseConnections' shows the number of active connections and can indicate query performance issues in DocumentDB."
"Which DocumentDB feature allows for point-in-time recovery of a cluster?","Continuous backups with automated snapshots","Manual snapshot restore only","Read replica promotion","Cluster versioning","Continuous backups with automated snapshots enable point-in-time recovery in DocumentDB."
"A developer wants to connect to DocumentDB using IAM authentication. What must be enabled on the cluster?","IAM database authentication","Kerberos authentication","LDAP integration","SAML federation","IAM database authentication must be enabled to use IAM for connecting to DocumentDB."
"Which DocumentDB feature provides high availability within a single region?","Replica set with multiple instances","Global clusters","Manual failover only","Read replica promotion","A replica set with multiple instances provides high availability in DocumentDB."
"A developer needs to automate DocumentDB cluster creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate DocumentDB cluster provisioning as part of CI/CD pipelines."
"Which DocumentDB feature allows for cross-region disaster recovery?","Global clusters","Read replica in another region only","Manual snapshot export only","Cluster versioning","Global clusters enable cross-region replication and disaster recovery for DocumentDB."
"A developer wants to monitor slow queries in DocumentDB. Which tool or metric should be used?","Profiler logs and CloudWatch metrics","FreeableMemory metric only","Parameter group logs only","Option group logs only","Profiler logs and CloudWatch metrics help identify slow queries in DocumentDB."
"Which CLI command is used to modify an existing DocumentDB parameter group?","aws docdb modify-db-cluster-parameter-group","aws documentdb update-parameter-group","aws docdb edit-parameter-group","aws docdb change-parameter-group","'aws docdb modify-db-cluster-parameter-group' is the correct command for modifying parameter groups in DocumentDB."
"A developer needs to restrict DocumentDB access to specific EC2 instances. What should be configured?","Security group rules referencing EC2 security groups","Parameter group settings","DB subnet group only","Option group","Security group rules can reference EC2 security groups to control DocumentDB access."
"Which DocumentDB feature provides automated backups and restores?","Automated backups with retention period","Manual snapshot only","Read replica promotion","Option group backup","Automated backups with a retention period provide backup and restore capabilities in DocumentDB."
"A developer wants to automate DocumentDB snapshot deletion. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate DocumentDB snapshot deletion."
"Which CLI command is used to reboot a DocumentDB instance?","aws docdb reboot-db-instance","aws documentdb restart-instance","aws docdb restart-db-instance","aws docdb reboot-instance","'aws docdb reboot-db-instance' reboots a DocumentDB instance."
"A developer needs to monitor the number of open connections to a DocumentDB instance. Which CloudWatch metric should be used?","DatabaseConnections","DBLoad","FreeableMemory","NetworkTransmitThroughput","'DatabaseConnections' shows the number of active connections to a DocumentDB instance."
"Which DocumentDB feature allows for restricting access to specific database users?","IAM policies with user-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database users in DocumentDB."
"A developer needs to automate DocumentDB parameter group changes across environments. Which AWS service is best suited?","AWS Systems Manager Parameter Store","AWS Glue","Amazon Inspector","AWS Data Pipeline","Parameter Store can manage and automate DocumentDB parameter values across environments."
"Which DocumentDB feature allows for seamless scaling of read throughput?","Adding read replicas","Global clusters","Manual failover","DB subnet group","Adding read replicas increases read throughput by distributing read requests in DocumentDB."
"A developer wants to monitor DocumentDB failover events. Which AWS service should be used for alerting?","Amazon CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Events can trigger alerts based on DocumentDB failover events."
"Which CLI command is used to describe DocumentDB events?","aws docdb describe-events","aws documentdb list-events","aws docdb get-events","aws docdb show-events","'aws docdb describe-events' lists recent DocumentDB events."
"A developer needs to ensure DocumentDB is only accessible from private subnets. What should be configured?","DB subnet group with private subnets","Parameter group settings","Security group with public access","Option group","DB subnet groups determine which subnets DocumentDB instances are deployed in."
"Which DocumentDB feature allows for backup and restore of database data?","Automated and manual snapshots","Data tiering","Global clusters only","Read replica promotion","Automated and manual snapshots allow backup and restore of DocumentDB data."
"A developer wants to restrict MongoDB queries to read-only access in DocumentDB. What should be configured?","IAM policy with read-only permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict MongoDB queries to read-only operations in DocumentDB."
"Which DocumentDB query language supports aggregation pipelines?","MongoDB query language","SQL only","Gremlin only","SPARQL only","DocumentDB supports MongoDB's aggregation pipeline for advanced queries."
"A developer needs to monitor memory usage in DocumentDB. Which CloudWatch metric should be used?","FreeableMemory","DBLoad","DatabaseConnections","NetworkReceiveThroughput","'FreeableMemory' shows available memory in a DocumentDB instance."
"Which DocumentDB feature allows for restricting access to specific database endpoints?","VPC security group rules","Parameter group settings","DB subnet group only","Option group","Security group rules can restrict access to specific endpoints in DocumentDB."
"A developer wants to automate DocumentDB cluster deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for DocumentDB clusters."
"Which DocumentDB feature allows for restricting access to specific collections?","IAM policy with collection-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific collections in DocumentDB."
"A developer needs to monitor DocumentDB for failed authentication attempts. Which CloudWatch metric should be used?","FailedLoginAttempts","DBLoad","DatabaseConnections","FreeableMemory","'FailedLoginAttempts' tracks the number of failed authentication attempts in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific document fields?","IAM policy with field-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific document fields in DocumentDB."
"A developer wants to automate DocumentDB maintenance window changes. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate DocumentDB maintenance window configuration."
"Which DocumentDB feature allows for restricting access to specific indexes?","IAM policy with index-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific indexes in DocumentDB collections."
"A developer needs to monitor DocumentDB for excessive query retries. Which CloudWatch metric should be used?","QueryRetryAttempts","DBLoad","DatabaseConnections","FreeableMemory","'QueryRetryAttempts' tracks the number of query retries in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific tags?","IAM policy with tag-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific tags in DocumentDB resources."
"A developer wants to automate DocumentDB backup scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup scheduling for DocumentDB."
"Which DocumentDB feature allows for restricting access to specific backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific backup jobs in DocumentDB."
"A developer wants to automate DocumentDB cluster version upgrades across multiple environments. Which AWS service is best suited?","AWS Systems Manager Automation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS Systems Manager Automation can orchestrate DocumentDB version upgrades across environments."
"Which DocumentDB feature helps reduce the impact of noisy neighbour issues in a multi-tenant environment?","Resource-level monitoring and isolation via CloudWatch metrics","Cluster replication only","Manual failover","Read replica promotion","Resource-level monitoring helps identify and isolate noisy tenants in DocumentDB."
"A developer wants to ensure DocumentDB backups are encrypted. What must be enabled?","Encryption at rest for snapshots","Data tiering","Cluster replication only","Read replica promotion","Enabling encryption at rest ensures that DocumentDB backups (snapshots) are encrypted."
"Which CLI command is used to export a DocumentDB snapshot to S3?","aws rds export-db-cluster-snapshot","aws docdb export-snapshot","aws rds save-db-snapshot","aws docdb copy-snapshot","'aws rds export-db-cluster-snapshot' exports a DocumentDB snapshot to S3."
"A developer needs to monitor the time taken for DocumentDB snapshot creation. Which CloudWatch metric should be used?","SnapshottingTime","DBLoad","DatabaseConnections","NetworkReceiveThroughput","'SnapshottingTime' measures the duration of snapshot creation in DocumentDB."
"Which DocumentDB feature allows for limiting the number of connections a user can create?","max_connections parameter in parameter group","DB subnet group","Option group","Read replica promotion","The 'max_connections' parameter limits the number of simultaneous connections in DocumentDB."
"A developer wants to automate the rotation of DocumentDB cluster keys. Which AWS service should be used?","AWS KMS key rotation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS KMS supports automatic rotation of DocumentDB cluster encryption keys."
"Which DocumentDB feature allows for monitoring command statistics per client?","CloudWatch Enhanced Monitoring","DB subnet group","Option group","Read replica promotion","Enhanced Monitoring provides per-client command statistics in DocumentDB."
"A developer needs to ensure DocumentDB is compliant with PCI DSS. What must be enabled?","Encryption at rest and in transit, and access controls","Data tiering only","Cluster replication only","Read replica promotion only","PCI DSS compliance requires encryption and strict access controls in DocumentDB."
"Which CLI command is used to list all available DocumentDB engine versions?","aws docdb describe-db-engine-versions","aws documentdb list-engine-versions","aws docdb get-engine-versions","aws docdb show-engine-versions","'aws docdb describe-db-engine-versions' lists all supported DocumentDB engine versions."
"A developer needs to monitor DocumentDB for excessive connection attempts. Which CloudWatch metric should be used?","ConnectionAttempts","DBLoad","DatabaseConnections","FreeableMemory","'ConnectionAttempts' tracks the number of connection attempts to DocumentDB."
"Which DocumentDB feature allows for restricting access to specific restore jobs?","IAM policy with restore job-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific restore jobs in DocumentDB."
"A developer wants to automate DocumentDB query auditing. Which AWS service can help?","AWS CloudTrail","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudTrail can log and audit query execution in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific restore operations?","IAM policy with restore operation-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific restore operations in DocumentDB."
"A developer needs to ensure DocumentDB is only accessible from specific AWS accounts. What should be configured?","VPC endpoint policies and security group rules with account restrictions","Parameter group settings","Read replica promotion","Cluster policy only","VPC endpoint policies and security groups can restrict access to specific AWS accounts for DocumentDB."
"Which DocumentDB feature allows for monitoring failed restore jobs?","CloudWatch RestoreFailure metric","BackupFailure metric","ExportFailure metric","FailedQuery metric","The RestoreFailure metric in CloudWatch tracks failed restore jobs in DocumentDB."
"A developer wants to automate DocumentDB restore retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore retention policies for DocumentDB."
"Which DocumentDB feature allows for restricting access to specific cluster snapshots?","IAM policy with snapshot-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific cluster snapshots in DocumentDB."
"A developer needs to monitor DocumentDB for excessive failed writes. Which CloudWatch metric should be used?","FailedWriteRequests","FailedQuery metric","WriteIOPS","ExportFailure metric","'FailedWriteRequests' tracks the number of failed write operations in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific cluster parameter groups?","IAM policy with parameter group-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific parameter groups in DocumentDB."
"A developer wants to automate DocumentDB cluster snapshot exports to S3. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate DocumentDB cluster snapshot exports to S3."
"Which DocumentDB feature allows for restricting access to specific cluster backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific backup jobs in DocumentDB clusters."
"A developer needs to monitor DocumentDB for excessive read IOPS. Which CloudWatch metric should be used?","ReadIOPS","WriteIOPS","QueryLatency","ExportFailure metric","'ReadIOPS' shows the number of read operations per second in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific cluster restore jobs?","IAM policy with restore job-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific restore jobs in DocumentDB clusters."
"A developer wants to automate DocumentDB cluster maintenance window updates. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate maintenance window updates for DocumentDB clusters."
"Which DocumentDB feature allows for restricting access to specific cluster restore operations?","IAM policy with restore operation-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific restore operations in DocumentDB clusters."
"A developer needs to ensure DocumentDB is only accessible from specific VPC endpoints. What should be configured?","VPC endpoint policies and security group rules","Parameter group settings","Read replica promotion","Cluster policy only","VPC endpoint policies and security groups restrict access to specific VPC endpoints for DocumentDB."
"Which DocumentDB feature allows for monitoring cluster backup job failures?","CloudWatch BackupFailure metric","RestoreFailure metric","ExportFailure metric","FailedQuery metric","The BackupFailure metric in CloudWatch tracks failed backup jobs in DocumentDB clusters."
"A developer wants to automate DocumentDB cluster backup retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup retention policies for DocumentDB clusters."
"Which DocumentDB feature allows for restricting access to specific cluster backup retention policies?","IAM policy with retention policy-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific backup retention policies in DocumentDB clusters."
"A developer needs to monitor DocumentDB for excessive write IOPS. Which CloudWatch metric should be used?","WriteIOPS","ReadIOPS","QueryLatency","ExportFailure metric","'WriteIOPS' shows the number of write operations per second in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific cluster export jobs?","IAM policy with export job-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific export jobs in DocumentDB clusters."
"A developer wants to automate DocumentDB cluster export scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export scheduling for DocumentDB clusters."
"Which DocumentDB feature allows for restricting access to specific cluster export destinations?","IAM policy with destination-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific export destinations in DocumentDB clusters."
"A developer needs to ensure DocumentDB is only accessible from specific on-premises networks. What should be configured?","VPC peering and security group rules","Parameter group settings","Read replica promotion","Cluster policy only","VPC peering and security groups restrict access to specific networks for DocumentDB."
"Which DocumentDB feature allows for monitoring cluster export failures?","CloudWatch ExportFailure metric","BackupFailure metric","RestoreFailure metric","FailedQuery metric","The ExportFailure metric in CloudWatch tracks failed export jobs in DocumentDB clusters."
"A developer wants to automate DocumentDB cluster export retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export retention policies for DocumentDB clusters."
"Which DocumentDB feature allows for restricting access to specific cluster export schedules?","IAM policy with schedule-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific export schedules in DocumentDB clusters."
"A developer needs to monitor DocumentDB for excessive export jobs. Which CloudWatch metric should be used?","ExportJobCount","ReadIOPS","WriteIOPS","QueryLatency","'ExportJobCount' tracks the number of export jobs in DocumentDB."
"Which DocumentDB feature allows for restricting access to specific cluster export formats?","IAM policy with format-level permissions","VPC endpoint policy only","Cluster policy only","Parameter group","IAM policies can restrict access to specific export formats in DocumentDB clusters."
