"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Timestream, which CLI command is used to create a new database?","aws timestream-write create-database","aws timestream create-db","aws timestream-write new-database","aws timestream start-database","'aws timestream-write create-database' is the correct command to create a new Timestream database."
"Which IAM permission is required for a developer to write records to a Timestream table?","timestream:WriteRecords","timestream:PutItem","timestream:InsertRecord","timestream:PutRecords","'timestream:WriteRecords' is required to write records to a Timestream table."
"A developer needs to enable encryption at rest for an Amazon Timestream database. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","Timestream uses AWS KMS to manage encryption keys for data at rest."
"Which Timestream feature allows for automatic data tiering between memory and magnetic storage?","Data retention policies","Table partitioning","Table sharding","Data replication","Data retention policies in Timestream automatically tier data between memory and magnetic storage."
"A developer wants to restrict access to a Timestream database to specific VPC endpoints. Which configuration should be used?","VPC endpoint policies and security group rules","Parameter group settings","Database policy only","IAM user group","VPC endpoint policies and security groups restrict access to Timestream databases."
"Which CLI command is used to write records to a Timestream table?","aws timestream-write write-records","aws timestream put-records","aws timestream-write insert-records","aws timestream-write add-records","'aws timestream-write write-records' writes records to a Timestream table."
"A developer needs to monitor Timestream query latency. Which CloudWatch metric should they use?","QueryLatency","WriteLatency","ReadIOPS","TableSizeBytes","'QueryLatency' shows the query latency in Timestream operations."
"Which Timestream feature allows for point-in-time recovery of a table?","Continuous backups with retention policies","Manual snapshot restore only","Read replica promotion","Table versioning","Continuous backups with retention policies enable point-in-time recovery in Timestream."
"A developer wants to connect to Timestream using IAM authentication. What must be enabled on the database?","IAM database authentication","Kerberos authentication","LDAP integration","SAML federation","IAM database authentication must be enabled to use IAM for connecting to Timestream."
"Which Timestream feature provides an immutable transaction log?","Magnetic store write log","Memory store only","Table audit log","Table versioning","The magnetic store write log is an immutable transaction log for Timestream tables."
"A developer needs to automate Timestream database creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate Timestream database provisioning as part of CI/CD pipelines."
"Which Timestream feature allows for cross-region disaster recovery?","Export to S3 and import in another region","Multi-AZ deployments only","Manual snapshot export only","Table replication","Exporting data to S3 and importing in another region enables cross-region recovery for Timestream."
"A developer wants to monitor slow queries in Timestream. Which tool or metric should be used?","CloudWatch QueryDuration metric","TableSizeBytes metric","WriteLatency metric","ReadIOPS metric","The QueryDuration metric in CloudWatch helps identify slow queries in Timestream."
"Which CLI command is used to list all Timestream databases in an account?","aws timestream-write list-databases","aws timestream list-db","aws timestream-write get-databases","aws timestream show-databases","'aws timestream-write list-databases' lists all Timestream databases in an account."
"A developer needs to restrict Timestream access to specific IAM roles. What should be configured?","IAM policies with role-level permissions","VPC endpoint policies only","Database policy only","Parameter group","IAM policies can restrict Timestream access to specific IAM roles."
"Which Timestream feature provides cryptographic proof of data integrity?","Magnetic store digest API","Memory store only","Table audit log","Table versioning","The magnetic store digest API provides cryptographic proof of data integrity in Timestream."
"A developer wants to automate Timestream data export scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate Timestream data export scheduling."
"Which Timestream feature allows for restricting access to specific tables?","IAM policy with table-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific tables in Timestream."
"A developer needs to monitor Timestream for excessive write IOPS. Which CloudWatch metric should be used?","WriteIOPS","ReadIOPS","TableSizeBytes","QueryLatency","'WriteIOPS' shows the number of write operations per second in Timestream."
"Which Timestream feature allows for restricting access to specific query statements?","IAM policy with statement-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific query statements in Timestream."
"A developer wants to automate Timestream table deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Timestream tables."
"Which Timestream feature allows for restricting access to specific table columns?","IAM policy with column-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific columns in Timestream tables."
"A developer needs to monitor Timestream for failed authentication attempts. Which CloudWatch metric should be used?","FailedLoginAttempts","DBLoad","DatabaseConnections","FreeableMemory","'FailedLoginAttempts' tracks the number of failed authentication attempts in Timestream."
"Which Timestream feature allows for restricting access to specific table partitions?","IAM policy with partition-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific table partitions in Timestream."
"A developer wants to automate Timestream maintenance window changes. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate Timestream maintenance window configuration."
"Which Timestream feature allows for restricting access to specific table indexes?","IAM policy with index-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific indexes in Timestream tables."
"A developer needs to monitor Timestream for excessive query retries. Which CloudWatch metric should be used?","QueryRetryAttempts","DBLoad","DatabaseConnections","FreeableMemory","'QueryRetryAttempts' tracks the number of query retries in Timestream."
"Which Timestream feature allows for restricting access to specific table tags?","IAM policy with tag-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific tags in Timestream tables."
"A developer wants to automate Timestream backup scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup scheduling for Timestream."
"Which Timestream feature allows for restricting access to specific table retention policies?","IAM policy with retention policy-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific retention policies in Timestream tables."
"A developer wants to automate Timestream cluster version upgrades across multiple environments. Which AWS service is best suited?","AWS Systems Manager Automation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS Systems Manager Automation can orchestrate Timestream version upgrades across environments."
"Which Timestream feature helps reduce the impact of noisy neighbour issues in a multi-tenant environment?","Resource-level monitoring and isolation via CloudWatch metrics","Table replication only","Manual failover","Read replica promotion","Resource-level monitoring helps identify and isolate noisy tenants in Timestream."
"A developer wants to ensure Timestream backups are encrypted. What must be enabled?","Encryption at rest for S3 exports","Data tiering","Table replication only","Read replica promotion","Enabling encryption at rest ensures that Timestream backups (S3 exports) are encrypted."
"Which CLI command is used to export a Timestream table to S3?","aws timestream-write export-table-to-s3","aws timestream export-table","aws timestream-write save-table","aws timestream-write copy-table","'aws timestream-write export-table-to-s3' exports a Timestream table to S3."
"A developer needs to monitor the time taken for Timestream table export. Which CloudWatch metric should be used?","ExportDuration","ReadIOPS","WriteIOPS","QueryLatency","'ExportDuration' measures the duration of table export in Timestream."
"Which Timestream feature allows for limiting the number of active sessions?","max_sessions parameter in table settings","DB subnet group","Option group","Read replica promotion","The 'max_sessions' parameter limits the number of simultaneous sessions in Timestream."
"A developer wants to automate the rotation of Timestream table keys. Which AWS service should be used?","AWS KMS key rotation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS KMS supports automatic rotation of Timestream table encryption keys."
"Which Timestream feature allows for monitoring command statistics per client?","CloudWatch Enhanced Monitoring","DB subnet group","Option group","Read replica promotion","Enhanced Monitoring provides per-client command statistics in Timestream."
"A developer needs to ensure Timestream is compliant with PCI DSS. What must be enabled?","Encryption at rest and in transit, and access controls","Data tiering only","Table replication only","Read replica promotion only","PCI DSS compliance requires encryption and strict access controls in Timestream."
"Which CLI command is used to list all available Timestream engine versions?","aws timestream-write describe-engine-versions","aws timestream list-engine-versions","aws timestream-write get-engine-versions","aws timestream-write show-engine-versions","'aws timestream-write describe-engine-versions' lists all supported Timestream engine versions."
"A developer needs to monitor Timestream for excessive connection attempts. Which CloudWatch metric should be used?","ConnectionAttempts","DBLoad","DatabaseConnections","FreeableMemory","'ConnectionAttempts' tracks the number of connection attempts to Timestream."
"Which Timestream feature allows for restricting access to specific export jobs?","IAM policy with export job-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific export jobs in Timestream."
"A developer wants to automate Timestream query auditing. Which AWS service can help?","AWS CloudTrail","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudTrail can log and audit query execution in Timestream."
"Which Timestream feature allows for restricting access to specific export destinations?","IAM policy with destination-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific export destinations in Timestream."
"A developer needs to ensure Timestream is only accessible from specific AWS accounts. What should be configured?","VPC endpoint policies and security group rules with account restrictions","Parameter group settings","Read replica promotion","Database policy only","VPC endpoint policies and security groups can restrict access to specific AWS accounts for Timestream."
"Which Timestream feature allows for monitoring failed queries?","CloudWatch FailedQuery metric","DBLoad","DatabaseConnections","QueryLatency","The FailedQuery metric in CloudWatch tracks failed queries in Timestream."
"A developer wants to automate Timestream backup retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup retention policies for Timestream."
"Which Timestream feature allows for restricting access to specific table attributes?","IAM policy with attribute-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific table attributes in Timestream."
"A developer needs to monitor Timestream for excessive read latency. Which CloudWatch metric should be used?","ReadLatency","WriteLatency","QueryDuration","ExportDuration","'ReadLatency' shows the read latency in Timestream operations."
"Which Timestream feature allows for restricting access to specific export schedules?","IAM policy with schedule-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific export schedules in Timestream."
"A developer wants to automate Timestream query throttling. Which AWS service can help?","AWS WAF with API Gateway integration","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS WAF with API Gateway can help throttle query requests to Timestream."
"Which Timestream feature allows for restricting access to specific export formats?","IAM policy with format-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific export formats in Timestream."
"A developer needs to monitor Timestream for excessive write latency. Which CloudWatch metric should be used?","WriteLatency","ReadLatency","QueryDuration","ExportDuration","'WriteLatency' shows the write latency in Timestream operations."
"Which Timestream feature allows for restricting access to specific query types?","IAM policy with query type-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific query types in Timestream."
"A developer wants to automate Timestream table export to a compliance archive. Which AWS service can help?","AWS Lambda with S3 event triggers","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by S3 events can automate Timestream table export to compliance archives."
"Which Timestream feature allows for restricting access to specific export destinations by region?","IAM policy with region-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to export destinations by region in Timestream."
"A developer needs to ensure Timestream is only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","Read replica promotion","Database policy only","Security group rules can restrict access to specific IP ranges in Timestream."
"Which Timestream feature allows for monitoring export failures?","CloudWatch ExportFailure metric","DBLoad","DatabaseConnections","QueryLatency","The ExportFailure metric in CloudWatch tracks failed export jobs in Timestream."
"A developer wants to automate Timestream export retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export retention policies for Timestream."
"Which Timestream feature allows for restricting access to specific update operations?","IAM policy with update operation-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific update operations in Timestream."
"A developer wants to automate Timestream table schema changes as part of a deployment pipeline. Which tool is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate schema changes for Timestream tables."
"Which Timestream feature allows for restricting access to specific table schemas?","IAM policy with schema-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific schemas in Timestream tables."
"A developer needs to monitor Timestream for excessive export jobs. Which CloudWatch metric should be used?","ExportJobCount","ReadIOPS","WriteIOPS","QueryLatency","'ExportJobCount' tracks the number of export jobs in Timestream."
"Which Timestream feature allows for restricting access to specific table storage tiers?","IAM policy with storage tier-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific storage tiers in Timestream tables."
"A developer wants to automate Timestream table index management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate index management for Timestream tables."
"Which Timestream feature allows for restricting access to specific table write operations?","IAM policy with write operation-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific write operations in Timestream tables."
"A developer needs to ensure Timestream is only accessible from specific on-premises networks. What should be configured?","VPC peering and security group rules","Parameter group settings","Read replica promotion","Database policy only","VPC peering and security groups restrict access to specific networks for Timestream."
"Which Timestream feature allows for monitoring table storage usage?","CloudWatch TableSizeBytes metric","QueryLatency","WriteIOPS","ExportDuration","The TableSizeBytes metric in CloudWatch shows storage usage for Timestream tables."
"A developer wants to automate Timestream table tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for Timestream tables."
"Which Timestream feature allows for restricting access to specific table read operations?","IAM policy with read operation-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific read operations in Timestream tables."
"A developer needs to monitor Timestream for excessive failed writes. Which CloudWatch metric should be used?","FailedWriteRequests","FailedQuery metric","WriteIOPS","ExportFailure metric","'FailedWriteRequests' tracks the number of failed write operations in Timestream."
"Which Timestream feature allows for restricting access to specific table metadata?","IAM policy with metadata-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific metadata in Timestream tables."
"A developer wants to automate Timestream table backup exports to S3. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup exports for Timestream tables."
"Which Timestream feature allows for restricting access to specific table backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific backup jobs in Timestream tables."
"A developer needs to ensure Timestream is only accessible from specific VPC endpoints. What should be configured?","VPC endpoint policies and security group rules","Parameter group settings","Read replica promotion","Database policy only","VPC endpoint policies and security groups restrict access to specific VPC endpoints for Timestream."
"Which Timestream feature allows for monitoring backup job failures?","CloudWatch BackupFailure metric","ExportFailure metric","FailedQuery metric","QueryLatency","The BackupFailure metric in CloudWatch tracks failed backup jobs in Timestream."
"A developer wants to automate Timestream table backup retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup retention policies for Timestream tables."
"Which Timestream feature allows for restricting access to specific table restore operations?","IAM policy with restore operation-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific restore operations in Timestream tables."
"A developer needs to monitor Timestream for excessive restore job failures. Which CloudWatch metric should be used?","RestoreFailure metric","BackupFailure metric","ExportFailure metric","FailedQuery metric","'RestoreFailure' tracks the number of failed restore jobs in Timestream."
"Which Timestream feature allows for restricting access to specific table restore jobs?","IAM policy with restore job-level permissions","VPC endpoint policy only","Database policy only","Parameter group","IAM policies can restrict access to specific restore jobs in Timestream tables."
