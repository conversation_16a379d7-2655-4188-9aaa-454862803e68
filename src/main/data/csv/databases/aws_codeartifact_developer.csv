"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS CodeArtifact, which CLI command is used to create a new repository?","aws codeartifact create-repository","aws codeartifact new-repo","aws codeartifact init-repository","aws codeartifact start-repository","'aws codeartifact create-repository' is the correct command to create a new CodeArtifact repository."
"Which IAM permission is required for a developer to publish packages to a CodeArtifact repository?","codeartifact:PublishPackageVersion","codeartifact:PutPackage","codeartifact:UploadArtifact","codeartifact:PushPackage","'codeartifact:PublishPackageVersion' is required to publish packages to a CodeArtifact repository."
"A developer needs to enable encryption at rest for a CodeArtifact domain. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","CodeArtifact uses AWS KMS to manage encryption keys for data at rest."
"Which CodeArtifact feature allows for sharing packages across multiple accounts?","Cross-account domains","Repository mirroring","Package replication","Repository federation","Cross-account domains enable sharing packages across AWS accounts in CodeArtifact."
"A developer wants to restrict access to a CodeArtifact repository to specific VPC endpoints. Which configuration should be used?","VPC endpoint policies and security group rules","Parameter group settings","Repository policy only","IAM user group","VPC endpoint policies and security groups restrict access to CodeArtifact repositories."
"Which CLI command is used to associate an external upstream repository with a CodeArtifact repository?","aws codeartifact associate-external-connection","aws codeartifact add-upstream","aws codeartifact link-repository","aws codeartifact connect-external","'aws codeartifact associate-external-connection' associates an external upstream repository with a CodeArtifact repository."
"A developer needs to monitor CodeArtifact repository storage usage. Which CloudWatch metric should they use?","RepositorySizeBytes","PackageCount","PublishRequests","DomainSizeBytes","'RepositorySizeBytes' shows the storage usage of a CodeArtifact repository."
"Which CodeArtifact feature allows for package version promotion between repositories?","Repository upstream and downstream relationships","Package mirroring","Domain replication","Repository federation","Upstream and downstream relationships allow package version promotion in CodeArtifact."
"A developer wants to connect to CodeArtifact using IAM authentication. What must be enabled on the domain?","IAM authentication for CodeArtifact","Kerberos authentication","LDAP integration","SAML federation","IAM authentication must be enabled to use IAM for connecting to CodeArtifact."
"Which CodeArtifact feature provides immutable storage for published packages?","Package version immutability","Repository mirroring","Domain replication","Package federation","Package version immutability ensures published packages cannot be overwritten in CodeArtifact."
"A developer needs to automate CodeArtifact repository creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate CodeArtifact repository provisioning as part of CI/CD pipelines."
"Which CodeArtifact feature allows for cross-region package replication?","Domain replication","Repository mirroring only","Package federation only","Upstream repository only","Domain replication enables cross-region package replication in CodeArtifact."
"A developer wants to monitor failed package publish attempts in CodeArtifact. Which CloudWatch metric should be used?","PublishFailedRequests","RepositorySizeBytes","PackageCount","DomainSizeBytes","'PublishFailedRequests' tracks failed package publish attempts in CodeArtifact."
"Which CLI command is used to delete a CodeArtifact domain?","aws codeartifact delete-domain","aws codeartifact remove-domain","aws codeartifact destroy-domain","aws codeartifact drop-domain","'aws codeartifact delete-domain' deletes a CodeArtifact domain."
"A developer needs to restrict CodeArtifact access to specific IAM roles. What should be configured?","IAM policies with role-level permissions","VPC endpoint policies only","Repository policy only","Parameter group","IAM policies can restrict CodeArtifact access to specific IAM roles."
"Which CodeArtifact feature provides package version retention policies?","Repository lifecycle policies","Domain replication","Package mirroring","Repository federation","Repository lifecycle policies manage package version retention in CodeArtifact."
"A developer wants to automate CodeArtifact repository deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for CodeArtifact repositories."
"Which CodeArtifact feature allows for restricting access to specific package namespaces?","IAM policy with namespace-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific package namespaces in CodeArtifact."
"A developer needs to monitor CodeArtifact for excessive storage usage. Which CloudWatch metric should be used?","RepositorySizeBytes","PublishFailedRequests","PackageCount","DomainSizeBytes","'RepositorySizeBytes' shows the storage usage of a CodeArtifact repository."
"Which CodeArtifact feature allows for restricting access to specific package versions?","IAM policy with version-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific package versions in CodeArtifact."
"A developer wants to automate CodeArtifact domain creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate CodeArtifact domain provisioning as part of CI/CD pipelines."
"Which CodeArtifact feature allows for restricting access to specific repository actions?","IAM policy with action-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific repository actions in CodeArtifact."
"A developer needs to monitor CodeArtifact for excessive package versions. Which CloudWatch metric should be used?","PackageCount","RepositorySizeBytes","PublishFailedRequests","DomainSizeBytes","'PackageCount' tracks the number of package versions in a CodeArtifact repository."
"Which CodeArtifact feature allows for restricting access to specific repository tags?","IAM policy with tag-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific tags in CodeArtifact repositories."
"A developer wants to automate CodeArtifact repository policy updates. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate repository policy updates for CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific repository endpoints?","VPC security group rules","Parameter group settings","Repository policy only","IAM user group","Security group rules can restrict access to specific endpoints in CodeArtifact."
"A developer needs to ensure CodeArtifact is only accessible from specific AWS accounts. What should be configured?","VPC endpoint policies and security group rules with account restrictions","Parameter group settings","Repository policy only","Domain policy only","VPC endpoint policies and security groups can restrict access to specific AWS accounts for CodeArtifact."
"Which CodeArtifact feature allows for monitoring repository policy changes?","CloudTrail event logging","CloudWatch RepositorySizeBytes metric","PublishFailedRequests metric","PackageCount metric","CloudTrail logs repository policy changes in CodeArtifact."
"A developer wants to automate CodeArtifact repository tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for CodeArtifact repositories."
"Which CodeArtifact feature allows for restricting access to specific repository lifecycle policies?","IAM policy with lifecycle policy-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific lifecycle policies in CodeArtifact."
"A developer needs to monitor CodeArtifact for excessive failed downloads. Which CloudWatch metric should be used?","DownloadFailedRequests","PublishFailedRequests","RepositorySizeBytes","PackageCount","'DownloadFailedRequests' tracks failed download attempts in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain actions?","IAM policy with domain action-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific domain actions in CodeArtifact."
"A developer wants to automate CodeArtifact domain policy updates. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate domain policy updates for CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain tags?","IAM policy with domain tag-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific tags in CodeArtifact domains."
"A developer needs to ensure CodeArtifact is only accessible from specific VPC endpoints. What should be configured?","VPC endpoint policies and security group rules","Parameter group settings","Repository policy only","Domain policy only","VPC endpoint policies and security groups restrict access to specific VPC endpoints for CodeArtifact."
"Which CodeArtifact feature allows for monitoring domain policy changes?","CloudTrail event logging","CloudWatch DomainSizeBytes metric","PublishFailedRequests metric","PackageCount metric","CloudTrail logs domain policy changes in CodeArtifact."
"A developer wants to automate CodeArtifact domain tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain lifecycle policies?","IAM policy with domain lifecycle policy-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific lifecycle policies in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive domain storage usage. Which CloudWatch metric should be used?","DomainSizeBytes","RepositorySizeBytes","PublishFailedRequests","PackageCount","'DomainSizeBytes' shows the storage usage of a CodeArtifact domain."
"Which CodeArtifact feature allows for restricting access to specific domain package versions?","IAM policy with domain version-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific package versions in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain lifecycle policy management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate lifecycle policy management for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain endpoints?","VPC security group rules","Parameter group settings","Repository policy only","IAM user group","Security group rules can restrict access to specific endpoints in CodeArtifact domains."
"A developer needs to ensure CodeArtifact is only accessible from specific on-premises networks. What should be configured?","VPC peering and security group rules","Parameter group settings","Repository policy only","Domain policy only","VPC peering and security groups restrict access to specific networks for CodeArtifact."
"Which CodeArtifact feature allows for monitoring domain tag changes?","CloudTrail event logging","CloudWatch DomainSizeBytes metric","PublishFailedRequests metric","PackageCount metric","CloudTrail logs domain tag changes in CodeArtifact."
"A developer wants to automate CodeArtifact domain backup scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup scheduling for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific backup jobs in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive backup job failures. Which CloudWatch metric should be used?","BackupFailure metric","ExportFailure metric","PublishFailedRequests metric","PackageCount metric","'BackupFailure' tracks the number of failed backup jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain backup retention policies?","IAM policy with backup retention policy-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific backup retention policies in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain restore operations. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore operations for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain restore jobs?","IAM policy with restore job-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific restore jobs in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive restore job failures. Which CloudWatch metric should be used?","RestoreFailure metric","BackupFailure metric","ExportFailure metric","PublishFailedRequests metric","'RestoreFailure' tracks the number of failed restore jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain restore operations?","IAM policy with restore operation-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific restore operations in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain restore retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore retention policies for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain restore schedules?","IAM policy with restore schedule-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific restore schedules in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive restore jobs. Which CloudWatch metric should be used?","RestoreJobCount","BackupFailure metric","ExportFailure metric","PublishFailedRequests metric","'RestoreJobCount' tracks the number of restore jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain restore formats?","IAM policy with restore format-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific restore formats in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain export scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export scheduling for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain export jobs?","IAM policy with export job-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific export jobs in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive export job failures. Which CloudWatch metric should be used?","ExportFailure metric","BackupFailure metric","RestoreFailure metric","PublishFailedRequests metric","'ExportFailure' tracks the number of failed export jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain export retention policies?","IAM policy with export retention policy-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific export retention policies in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain export retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate export retention policies for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain export schedules?","IAM policy with export schedule-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific export schedules in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive export jobs. Which CloudWatch metric should be used?","ExportJobCount","BackupFailure metric","RestoreFailure metric","PublishFailedRequests metric","'ExportJobCount' tracks the number of export jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain export formats?","IAM policy with export format-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific export formats in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain import scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import scheduling for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain import jobs?","IAM policy with import job-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific import jobs in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive import job failures. Which CloudWatch metric should be used?","ImportFailure metric","ExportFailure metric","BackupFailure metric","PublishFailedRequests metric","'ImportFailure' tracks the number of failed import jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain import retention policies?","IAM policy with import retention policy-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific import retention policies in CodeArtifact domains."
"A developer wants to automate CodeArtifact domain import retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import retention policies for CodeArtifact domains."
"Which CodeArtifact feature allows for restricting access to specific domain import schedules?","IAM policy with import schedule-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific import schedules in CodeArtifact domains."
"A developer needs to automate CodeArtifact domain import job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate CodeArtifact domain import job provisioning as part of CI/CD pipelines."
"Which CodeArtifact feature allows for restricting access to specific domain import actions?","IAM policy with import action-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific import actions in CodeArtifact domains."
"A developer wants to monitor CodeArtifact for excessive import jobs. Which CloudWatch metric should be used?","ImportJobCount","ExportJobCount","BackupFailure metric","PublishFailedRequests metric","'ImportJobCount' tracks the number of import jobs in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain import formats?","IAM policy with import format-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to specific import formats in CodeArtifact domains."
"A developer needs to ensure CodeArtifact is only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","Repository policy only","Domain policy only","Security group rules can restrict access to specific IP ranges in CodeArtifact."
"Which CodeArtifact feature allows for monitoring import job failures?","CloudWatch ImportFailure metric","ExportFailure metric","BackupFailure metric","PublishFailedRequests metric","The ImportFailure metric in CloudWatch tracks failed import jobs in CodeArtifact."
"A developer wants to automate CodeArtifact import retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import retention policies for CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain import endpoints?","VPC security group rules","Parameter group settings","Repository policy only","IAM user group","Security group rules can restrict access to specific import endpoints in CodeArtifact domains."
"A developer needs to monitor CodeArtifact for excessive import job tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch ImportJobCount metric","ExportFailure metric","PublishFailedRequests metric","CloudTrail logs import job tag changes in CodeArtifact."
"Which CodeArtifact feature allows for restricting access to specific domain import schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","Repository policy only","Parameter group","IAM policies can restrict access to import schedules by region in CodeArtifact domains."
