"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the maximum size of an Amazon Lightsail managed database?","240 GB","16 GB","32 GB","64 GB","The maximum size of an Amazon Lightsail managed database is 240 GB."
"Which database engines are supported by Amazon Lightsail managed databases?","MySQL and PostgreSQL","Oracle and SQL Server","DB2 and MariaDB","MongoDB and Cassandra","Amazon Lightsail managed databases support MySQL and PostgreSQL database engines."
"What is the purpose of the daily snapshot feature in Amazon Lightsail managed databases?","To create a backup of the database","To monitor database performance","To scale the database","To optimise database queries","The daily snapshot feature in Amazon Lightsail managed databases creates a backup of the database, allowing for restoration to a previous state."
"Can you scale an Amazon Lightsail managed database vertically (increase its size)?","Yes, you can scale to a larger instance size","No, scaling is not supported","Only horizontally by adding read replicas","Only by migrating to RDS","You can scale an Amazon Lightsail managed database vertically by upgrading to a larger instance size with more memory and processing power."
"What is the primary benefit of using a managed database in Amazon Lightsail over self-managing a database on a Lightsail instance?","Simplified management and maintenance","Lower cost","Greater customisation options","Higher performance","Managed databases in Amazon Lightsail offer simplified management and maintenance, including automated backups, software patching, and infrastructure management."
"How do you connect to an Amazon Lightsail managed database from a Lightsail instance?","Using the database endpoint and credentials","Using the Lightsail console","Using SSH","Using a VPN","You connect to an Amazon Lightsail managed database from a Lightsail instance using the database endpoint and credentials provided in the Lightsail console."
"What is the purpose of read replicas in Amazon Lightsail managed databases?","To improve read performance","To improve write performance","To provide high availability","To encrypt data at rest","Read replicas in Amazon Lightsail managed databases improve read performance by distributing read traffic across multiple database instances."
"What happens to your data if you delete an Amazon Lightsail managed database without creating a snapshot?","The data is permanently lost","The data is automatically backed up","The data is moved to S3","The data is archived","If you delete an Amazon Lightsail managed database without creating a snapshot, the data is permanently lost. It is crucial to create a snapshot before deletion if you want to preserve the data."
"Which AWS service can you integrate with Amazon Lightsail managed databases for more advanced database features?","Amazon RDS","Amazon DynamoDB","Amazon S3","Amazon EC2","You can integrate Amazon Lightsail managed databases with Amazon RDS for more advanced database features, such as larger instance sizes, more storage, and additional database engines."
"What is the default backup retention period for Amazon Lightsail managed databases?","7 days","1 day","30 days","90 days","The default backup retention period for Amazon Lightsail managed databases is 7 days, during which you can restore your database to any point in time within that period."
"What is the cost implication of enabling Multi-AZ on an Amazon Lightsail managed database?","Doubles the database cost","Increases cost by 50%","No cost impact","Slightly reduces cost","Enabling Multi-AZ on an Amazon Lightsail managed database doubles the cost of the database, as it provisions a standby instance in a different Availability Zone."
"How does Amazon Lightsail handle patching and updates for managed databases?","Automatically applies patches during maintenance windows","Requires manual patching","Patches are applied during instance creation only","Patches are not supported","Amazon Lightsail automatically applies patches and updates to managed databases during maintenance windows to ensure the database is secure and up-to-date."
"Which metric in Amazon Lightsail can be used to monitor the CPU utilisation of a managed database?","CPUUtilization","MemoryUtilization","DiskQueueDepth","NetworkReceiveThroughput","The CPUUtilization metric in Amazon Lightsail can be used to monitor the CPU utilisation of a managed database, helping you identify performance bottlenecks."
"What is the maximum number of managed databases you can create in Amazon Lightsail?","5","1","10","Unlimited","The maximum number of managed databases you can create in Amazon Lightsail is 5."
"What type of storage is used by Amazon Lightsail managed databases?","SSD","HDD","NVMe","Tape","Amazon Lightsail managed databases use SSD storage for fast and reliable performance."
"How can you import data into an Amazon Lightsail managed database?","Using mysqldump or pg_dump","Using the Lightsail console","Using S3","Using Glacier","You can import data into an Amazon Lightsail managed database using tools like mysqldump (for MySQL) or pg_dump (for PostgreSQL)."
"What is the purpose of the 'Connect using SSH' option when connecting to a managed database?","To create a secure tunnel to the database","To bypass security groups","To directly access the database instance","To encrypt data in transit","The 'Connect using SSH' option creates a secure tunnel to the database, allowing you to connect to the database without exposing it to the public internet."
"Can you use AWS CloudFormation to manage Amazon Lightsail resources, including managed databases?","No, CloudFormation is not supported for Lightsail","Yes, with limited functionality","Yes, with full functionality","Only for EC2 instances","AWS CloudFormation does not directly support managing Amazon Lightsail resources, including managed databases. Lightsail has its own management tools and API."
"What is the difference between a 'Standard' and 'High Availability' plan for Amazon Lightsail managed databases?","High Availability provides automatic failover to a standby instance","Standard is cheaper","Standard offers higher performance","High Availability supports more database engines","The 'High Availability' plan provides automatic failover to a standby instance in case of a failure, ensuring minimal downtime."
"How can you monitor the disk space usage of an Amazon Lightsail managed database?","Using the FreeStorageSpace metric","Using the CPUUtilization metric","Using the DatabaseConnections metric","Using the NetworkReceiveThroughput metric","You can monitor the disk space usage of an Amazon Lightsail managed database using the FreeStorageSpace metric in the Lightsail console or via the AWS CLI."
"What is the impact of enabling automated backups on the performance of an Amazon Lightsail managed database?","Slight performance impact during backup windows","No performance impact","Significant performance impact during backup windows","Improves performance","Enabling automated backups on an Amazon Lightsail managed database may have a slight performance impact during the backup windows, but it is generally minimal."
"How can you restore an Amazon Lightsail managed database from a snapshot?","Using the Lightsail console or AWS CLI","Using Amazon RDS","Using Amazon S3","Using AWS Backup","You can restore an Amazon Lightsail managed database from a snapshot using the Lightsail console or the AWS CLI."
"What is the purpose of the maintenance window for Amazon Lightsail managed databases?","To apply software patches and updates","To perform backups","To scale the database","To optimise database queries","The maintenance window for Amazon Lightsail managed databases is used to apply software patches and updates to the database instance."
"Which security feature is enabled by default for Amazon Lightsail managed databases?","Encryption at rest","Encryption in transit","Multi-Factor Authentication","VPC peering","Encryption at rest is enabled by default for Amazon Lightsail managed databases, protecting your data when it is stored on disk."
"What is the maximum number of read replicas you can create for an Amazon Lightsail managed database?","5","1","3","Unlimited","The maximum number of read replicas you can create for an Amazon Lightsail managed database is 5."
"How can you improve the security of connections to your Amazon Lightsail managed database?","By using SSH tunneling","By disabling encryption","By using HTTP","By using public IP addresses","You can improve the security of connections to your Amazon Lightsail managed database by using SSH tunneling to create a secure connection."
"What is the purpose of the 'Public mode' setting for Amazon Lightsail managed databases?","To allow connections from any IP address","To disable encryption","To enable read replicas","To improve performance","The 'Public mode' setting allows connections from any IP address, which is generally not recommended for security reasons. It's better to use private mode and SSH tunneling."
"Which AWS CLI command can be used to create a snapshot of an Amazon Lightsail managed database?","aws lightsail create-relational-database-snapshot","aws rds create-db-snapshot","aws ec2 create-snapshot","aws s3 cp","The `aws lightsail create-relational-database-snapshot` command can be used to create a snapshot of an Amazon Lightsail managed database."
"What is the purpose of the 'Backup and Restore' tab in the Amazon Lightsail console for managed databases?","To manage database snapshots and restores","To monitor database performance","To scale the database","To configure database security","The 'Backup and Restore' tab in the Amazon Lightsail console allows you to manage database snapshots and restores, including creating new snapshots and restoring from existing ones."
"What is the maximum amount of memory available for the largest Amazon Lightsail managed database instance?","8 GB","4 GB","16 GB","32 GB","The maximum amount of memory available for the largest Amazon Lightsail managed database instance is 8 GB."
"What is the effect of enabling point-in-time recovery (PITR) on an Amazon Lightsail managed database?","Allows restoring the database to any point in the last 7 days","Increases write performance","Reduces storage costs","Disables automated backups","Enabling point-in-time recovery (PITR) allows you to restore the database to any point in the last 7 days, providing granular recovery options."
"Which protocol is used for encrypting data in transit to an Amazon Lightsail managed database?","TLS/SSL","HTTP","FTP","Telnet","TLS/SSL is used for encrypting data in transit to an Amazon Lightsail managed database, ensuring secure communication."
"What is the purpose of the 'Upgrade now' option in the Lightsail console for managed databases?","To upgrade to a larger database instance size","To upgrade the database engine version","To enable Multi-AZ","To enable automated backups","The 'Upgrade now' option allows you to upgrade to a larger database instance size with more memory and processing power."
"Which AWS Region are Amazon Lightsail managed databases available in?","All AWS Regions","Only US East (N. Virginia)","Only Europe (Ireland)","Only Asia Pacific (Tokyo)","Amazon Lightsail managed databases are available in all AWS Regions where Lightsail is offered."
"What is the pricing model for Amazon Lightsail managed databases?","Fixed monthly price","Pay-as-you-go","Reserved Instances","Spot Instances","Amazon Lightsail managed databases use a fixed monthly price, which includes the database instance, storage, and data transfer."
"How can you monitor the number of database connections to an Amazon Lightsail managed database?","Using the DatabaseConnections metric","Using the CPUUtilization metric","Using the FreeStorageSpace metric","Using the NetworkReceiveThroughput metric","You can monitor the number of database connections to an Amazon Lightsail managed database using the DatabaseConnections metric in the Lightsail console or via the AWS CLI."
"What is the maximum number of connections allowed to an Amazon Lightsail managed database?","Determined by the instance size","10","100","Unlimited","The maximum number of connections allowed to an Amazon Lightsail managed database is determined by the instance size. Larger instances support more connections."
"How can you determine the version of the database engine running on an Amazon Lightsail managed database?","Using the Lightsail console or AWS CLI","Using SSH","Using SQL queries","Using the operating system","You can determine the version of the database engine running on an Amazon Lightsail managed database using the Lightsail console or the AWS CLI."
"What is the purpose of the 'Test endpoint' option in the Lightsail console for managed databases?","To verify the database connection","To test database performance","To test database security","To test database backups","The 'Test endpoint' option allows you to verify the database connection and ensure that you can connect to the database from your Lightsail instance or other applications."
"What is the effect of enabling automatic minor version upgrades for an Amazon Lightsail managed database?","Automatically upgrades the database to the latest minor version","Upgrades the database to the latest major version","Disables automated backups","Reduces storage costs","Enabling automatic minor version upgrades automatically upgrades the database to the latest minor version, ensuring that you have the latest security patches and bug fixes."
"How can you access the logs for an Amazon Lightsail managed database?","Using the Lightsail console or AWS CLI","Using SSH","Using SQL queries","Using the operating system","You can access the logs for an Amazon Lightsail managed database using the Lightsail console or the AWS CLI, allowing you to troubleshoot issues and monitor database activity."
"What is the purpose of the 'Reset to default password' option in the Lightsail console for managed databases?","To reset the database password to a default value","To reset the database to its initial state","To reset the database user permissions","To reset the database backup settings","The 'Reset to default password' option allows you to reset the database password to a default value, which you can then change to a more secure password."
"Which AWS service can be used to create alarms for Amazon Lightsail managed databases?","Amazon CloudWatch","Amazon SNS","Amazon SQS","Amazon Lambda","Amazon CloudWatch can be used to create alarms for Amazon Lightsail managed databases, allowing you to monitor metrics and receive notifications when certain thresholds are exceeded."
"What is the maximum number of concurrent queries that can be executed on an Amazon Lightsail managed database?","Determined by the instance size","10","100","Unlimited","The maximum number of concurrent queries that can be executed on an Amazon Lightsail managed database is determined by the instance size. Larger instances support more concurrent queries."
"How can you optimise the performance of an Amazon Lightsail managed database?","By optimising SQL queries","By increasing the instance size","By enabling Multi-AZ","By enabling automated backups","You can optimise the performance of an Amazon Lightsail managed database by optimising SQL queries, increasing the instance size, and using read replicas."
"What is the purpose of the 'Import from S3' option in the Lightsail console for managed databases?","To import data from an S3 bucket","To export data to an S3 bucket","To create a backup in S3","To restore a backup from S3","The 'Import from S3' option allows you to import data from an S3 bucket into your Amazon Lightsail managed database."
"Which AWS CLI command can be used to delete an Amazon Lightsail managed database?","aws lightsail delete-relational-database","aws rds delete-db-instance","aws ec2 delete-instance","aws s3 rm","The `aws lightsail delete-relational-database` command can be used to delete an Amazon Lightsail managed database."
"What is the purpose of the 'Enable password authentication' option for Amazon Lightsail managed databases?","To allow password-based authentication","To disable password-based authentication","To enable multi-factor authentication","To enable SSH key-based authentication","The 'Enable password authentication' option allows password-based authentication to the database. It is recommended to use SSH tunneling for enhanced security."
"How can you create a custom domain name for your Amazon Lightsail managed database?","Not directly supported, use SSH tunneling","Using Amazon Route 53","Using Amazon CloudFront","Using Amazon Certificate Manager","Custom domain names are not directly supported for Amazon Lightsail managed databases. You can use SSH tunneling to connect to the database and access it through a custom domain on your Lightsail instance."
"What is the effect of enabling connection pooling for an Amazon Lightsail managed database?","Reduces the overhead of establishing new database connections","Increases write performance","Reduces storage costs","Disables automated backups","Enabling connection pooling reduces the overhead of establishing new database connections, improving the performance of applications that frequently connect to the database."
"How can you monitor the query performance of an Amazon Lightsail managed database?","Using slow query logs","Using the CPUUtilization metric","Using the FreeStorageSpace metric","Using the NetworkReceiveThroughput metric","You can monitor the query performance of an Amazon Lightsail managed database using slow query logs, which record queries that take longer than a specified time to execute."
"What is the purpose of the 'Download CA certificate' option in the Lightsail console for managed databases?","To download the certificate authority (CA) certificate for secure connections","To download the database backup","To download the database logs","To download the database schema","The 'Download CA certificate' option allows you to download the certificate authority (CA) certificate, which is used to establish secure connections to the database."
"What is the effect of enabling the 'auto-upgrade' setting on a Lightsail managed database?","Automatically upgrades the database engine to the latest version","Automatically scales the database instance size","Automatically creates read replicas","Automatically enables Multi-AZ","Enabling the 'auto-upgrade' setting automatically upgrades the database engine to the latest version, ensuring you have the latest features and security patches."
"How can you configure the firewall for an Amazon Lightsail managed database?","Using security groups","Using network ACLs","Using the Lightsail console","Using the operating system firewall","You configure the firewall for an Amazon Lightsail managed database using security groups, which control the inbound and outbound traffic to the database instance."
"What is the purpose of the 'Clone to new database' option in the Lightsail console for managed databases?","To create a copy of the database in a new Lightsail instance","To create a read replica","To create a backup","To migrate the database to RDS","The 'Clone to new database' option allows you to create a copy of the database in a new Lightsail instance, which can be useful for testing or development purposes."
"Which AWS service can be used to store backups of Amazon Lightsail managed databases?","Amazon S3","Amazon Glacier","Amazon EBS","Amazon EFS","Amazon S3 can be used to store backups of Amazon Lightsail managed databases, providing a durable and cost-effective storage solution."
"What is the effect of using a larger instance size for an Amazon Lightsail managed database?","Increased memory and processing power","Reduced storage costs","Improved network performance","Enhanced security","Using a larger instance size provides increased memory and processing power, allowing the database to handle more concurrent connections and larger workloads."
"How can you monitor the database engine version of an Amazon Lightsail managed database?","Using the Lightsail console or AWS CLI","Using SSH","Using SQL queries","Using the operating system","You can monitor the database engine version of an Amazon Lightsail managed database using the Lightsail console or the AWS CLI."
"What is the purpose of the 'Maintenance' tab in the Lightsail console for managed databases?","To configure maintenance settings","To monitor database performance","To scale the database","To configure database security","The 'Maintenance' tab in the Lightsail console allows you to configure maintenance settings, such as the preferred maintenance window."
"What is the effect of enabling the 'enhanced monitoring' setting on a Lightsail managed database?","Provides detailed metrics about the database instance","Increases write performance","Reduces storage costs","Disables automated backups","Enabling the 'enhanced monitoring' setting provides detailed metrics about the database instance, allowing you to identify performance bottlenecks and troubleshoot issues."
"How can you configure the character set and collation for an Amazon Lightsail managed database?","During database creation","After database creation","Using SSH","Using SQL queries","You can configure the character set and collation for an Amazon Lightsail managed database during database creation."
"What is the purpose of the 'View logs' option in the Lightsail console for managed databases?","To view database logs","To download database backups","To monitor database performance","To configure database security","The 'View logs' option allows you to view database logs, which can be useful for troubleshooting issues and monitoring database activity."
"What is the effect of enabling the 'automatic backup' setting on a Lightsail managed database?","Enables daily automated backups of the database","Increases write performance","Reduces storage costs","Disables manual snapshots","Enabling the 'automatic backup' setting enables daily automated backups of the database, providing a point-in-time recovery option."
"How can you determine the supported character sets and collations for an Amazon Lightsail managed database?","Using the database engine documentation","Using the Lightsail console","Using SSH","Using SQL queries","You can determine the supported character sets and collations for an Amazon Lightsail managed database by consulting the database engine documentation (MySQL or PostgreSQL)."
"What is the purpose of the 'Scale database' option in the Lightsail console for managed databases?","To change the database instance size","To create read replicas","To enable Multi-AZ","To migrate the database to RDS","The 'Scale database' option allows you to change the database instance size, increasing the memory and processing power available to the database."
