"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon RDS on VMware, which CLI command is used to register an on-premises vSphere environment?","aws rds register-db-environment","aws rds create-db-instance","aws rds add-vmware-host","aws rds setup-vsphere","'aws rds register-db-environment' is used to register a vSphere environment for RDS on VMware." 
"Which IAM permission is required for a developer to create a DB instance in RDS on VMware?","rds:CreateDBInstance","vmware:CreateInstance","rds:ProvisionVMInstance","rds:CreateVMwareDB","'rds:CreateDBInstance' is required to create a DB instance in RDS on VMware." 
"A developer needs to enable encryption at rest for an RDS on VMware instance. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","RDS on VMware uses AWS KMS to manage encryption keys for data at rest." 
"Which RDS on VMware feature allows for automatic failover in the event of a primary instance failure?","Multi-AZ deployments","Read replicas","Global clusters","Manual promotion","Multi-AZ deployments provide automatic failover to a standby instance in RDS on VMware." 
"A developer wants to restrict access to an RDS on VMware instance to specific on-premises subnets. Which configuration should be used?","DB subnet group","Parameter group","Option group","Security group only","A DB subnet group defines which on-premises subnets RDS instances are deployed in, restricting access." 
"Which CLI command is used to create a new parameter group for RDS on VMware?","aws rds create-db-parameter-group","aws vmware create-parameter-group","aws rds create-vmware-parameter-group","aws rds new-parameter-group","'aws rds create-db-parameter-group' is the correct command for creating a parameter group for RDS on VMware." 
"A developer needs to monitor query performance in RDS on VMware. Which CloudWatch metric should they use?","DBLoad","CPUUtilization","FreeableMemory","NetworkReceiveThroughput","DBLoad measures the database load and is useful for monitoring query performance in RDS on VMware." 
"Which RDS on VMware feature allows for point-in-time recovery of a database?","Automated backups with point-in-time restore","Manual snapshot restore only","Read replica promotion","Global cluster failover","Automated backups with point-in-time restore enable point-in-time recovery in RDS on VMware." 
"A developer wants to connect to RDS on VMware using IAM authentication. What must be enabled on the instance?","IAM database authentication","Kerberos authentication","LDAP integration","SAML federation","IAM database authentication must be enabled to use IAM for connecting to RDS on VMware." 
"Which RDS on VMware feature provides high availability within a single on-premises environment?","Multi-AZ deployments","Global clusters","Manual failover","Read replica promotion","Multi-AZ deployments provide high availability within a region for RDS on VMware."
"A developer needs to automate RDS on VMware instance creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate RDS on VMware instance provisioning as part of CI/CD pipelines."
"Which RDS on VMware feature allows for cross-site disaster recovery?","Read replica in another site","Multi-AZ deployments only","Manual snapshot export only","Parameter group replication","Read replicas can be created in another site for disaster recovery in RDS on VMware."
"A developer wants to monitor slow queries in RDS on VMware. Which tool or metric should be used?","Enhanced Monitoring and slow query logs","CloudWatch FreeableMemory metric","Parameter group logs only","Option group logs only","Enhanced Monitoring and slow query logs help identify slow queries in RDS on VMware."
"Which CLI command is used to modify an existing RDS on VMware parameter group?","aws rds modify-db-parameter-group","aws vmware update-parameter-group","aws rds edit-vmware-parameter-group","aws rds change-parameter-group","'aws rds modify-db-parameter-group' is the correct command for modifying parameter groups in RDS on VMware."
"A developer needs to restrict RDS on VMware access to specific on-premises servers. What should be configured?","Security group rules referencing server IPs","Parameter group settings","DB subnet group only","Option group","Security group rules can reference server IPs to control RDS on VMware access."
"Which RDS on VMware feature provides automated backups and restores?","Automated backups with retention period","Manual snapshot only","Read replica promotion","Option group backup","Automated backups with a retention period provide backup and restore capabilities in RDS on VMware."
"A developer wants to automate RDS on VMware snapshot deletion. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate RDS on VMware snapshot deletion."
"Which CLI command is used to reboot an RDS on VMware instance?","aws rds reboot-db-instance","aws vmware restart-instance","aws rds restart-vmware-instance","aws rds reboot-vmware","'aws rds reboot-db-instance' reboots an RDS on VMware instance."
"A developer needs to monitor the number of open connections to an RDS on VMware instance. Which CloudWatch metric should be used?","DatabaseConnections","DBLoad","FreeableMemory","NetworkTransmitThroughput","'DatabaseConnections' shows the number of active connections to an RDS on VMware instance."
"Which RDS on VMware feature allows for restricting access to specific database users?","IAM policies with user-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database users in RDS on VMware."
"A developer needs to automate RDS on VMware parameter group changes across environments. Which AWS service is best suited?","AWS Systems Manager Parameter Store","AWS Glue","Amazon Inspector","AWS Data Pipeline","Parameter Store can manage and automate RDS on VMware parameter values across environments."
"Which RDS on VMware feature allows for seamless scaling of read throughput?","Adding read replicas","Global clusters","Manual failover","DB subnet group","Adding read replicas increases read throughput by distributing read requests in RDS on VMware."
"A developer wants to monitor RDS on VMware failover events. Which AWS service should be used for alerting?","Amazon CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Events can trigger alerts based on RDS on VMware failover events."
"Which CLI command is used to describe RDS on VMware events?","aws rds describe-events","aws vmware list-events","aws rds get-vmware-events","aws rds show-events","'aws rds describe-events' lists recent RDS on VMware events."
"A developer needs to ensure RDS on VMware is only accessible from private subnets. What should be configured?","DB subnet group with private subnets","Parameter group settings","Security group with public access","Option group","DB subnet groups determine which subnets RDS on VMware instances are deployed in."
"Which RDS on VMware feature allows for backup and restore of database data?","Automated and manual snapshots","Data tiering","Global clusters only","Read replica promotion","Automated and manual snapshots allow backup and restore of RDS on VMware data."
"A developer wants to restrict SQL queries to read-only access in RDS on VMware. What should be configured?","IAM policy with read-only permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict SQL queries to read-only operations in RDS on VMware."
"Which RDS on VMware query language supports federated queries across multiple data sources?","SQL with federation support","MySQL UNION clause","PostgreSQL FDW only","SQL Server Linked Servers only","SQL with federation support enables querying multiple data sources in RDS on VMware."
"A developer needs to monitor memory usage in RDS on VMware. Which CloudWatch metric should be used?","FreeableMemory","DBLoad","DatabaseConnections","NetworkReceiveThroughput","'FreeableMemory' shows available memory in an RDS on VMware instance."
"Which RDS on VMware feature allows for restricting access to specific database endpoints?","Security group rules","Parameter group settings","DB subnet group only","Option group","Security group rules can restrict access to specific endpoints in RDS on VMware."
"A developer wants to automate RDS on VMware instance deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for RDS on VMware instances."
"Which RDS on VMware feature allows for restricting access to specific SQL commands?","IAM policy with command restrictions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific SQL commands in RDS on VMware."
"A developer needs to monitor RDS on VMware for failed authentication attempts. Which CloudWatch metric should be used?","FailedLoginAttempts","DBLoad","DatabaseConnections","FreeableMemory","'FailedLoginAttempts' tracks the number of failed authentication attempts in RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database schemas?","IAM policy with schema-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database schemas in RDS on VMware."
"A developer wants to automate RDS on VMware maintenance window changes. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate RDS on VMware maintenance window configuration."
"Which RDS on VMware feature allows for restricting access to specific database tables?","IAM policy with table-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database tables in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive query retries. Which CloudWatch metric should be used?","QueryRetryAttempts","DBLoad","DatabaseConnections","FreeableMemory","'QueryRetryAttempts' tracks the number of query retries in RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database columns?","IAM policy with column-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database columns in RDS on VMware."
"A developer wants to automate RDS on VMware backup scheduling. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup scheduling for RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database views?","IAM policy with view-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database views in RDS on VMware."
"A developer needs to automate RDS on VMware cluster version upgrades across multiple environments. Which AWS service is best suited?","AWS Systems Manager Automation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS Systems Manager Automation can orchestrate RDS on VMware version upgrades across environments."
"Which RDS on VMware feature helps reduce the impact of noisy neighbour issues in a multi-tenant environment?","Resource-level monitoring and isolation via CloudWatch metrics","Global clusters only","Manual failover","Read replica promotion","Resource-level monitoring helps identify and isolate noisy tenants in RDS on VMware."
"A developer wants to ensure RDS on VMware backups are encrypted. What must be enabled?","Encryption at rest for snapshots","Data tiering","Global clusters only","Read replica promotion","Enabling encryption at rest ensures that RDS on VMware backups (snapshots) are encrypted."
"Which CLI command is used to export an RDS on VMware snapshot to S3?","aws rds export-db-snapshot","aws vmware export-snapshot","aws rds save-vmware-snapshot","aws rds copy-snapshot","'aws rds export-db-snapshot' exports an RDS on VMware snapshot to S3."
"A developer needs to monitor the time taken for RDS on VMware snapshot creation. Which CloudWatch metric should be used?","SnapshottingTime","DBLoad","DatabaseConnections","NetworkReceiveThroughput","'SnapshottingTime' measures the duration of snapshot creation in RDS on VMware."
"Which RDS on VMware feature allows for limiting the number of connections a user can create?","max_connections parameter in parameter group","DB subnet group","Option group","Read replica promotion","The 'max_connections' parameter limits the number of simultaneous connections in RDS on VMware."
"A developer wants to automate the rotation of database credentials for RDS on VMware. Which AWS service should be used?","AWS Secrets Manager rotation","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS Secrets Manager supports automatic rotation of RDS on VMware database credentials."
"Which RDS on VMware feature allows for monitoring command statistics per client?","CloudWatch Enhanced Monitoring","DB subnet group","Option group","Read replica promotion","Enhanced Monitoring provides per-client command statistics in RDS on VMware."
"A developer needs to ensure RDS on VMware is compliant with PCI DSS. What must be enabled?","Encryption at rest and in transit, and access controls","Data tiering only","Global clusters only","Read replica promotion only","PCI DSS compliance requires encryption and strict access controls in RDS on VMware."
"Which CLI command is used to list all available RDS on VMware engine versions?","aws rds describe-db-engine-versions","aws vmware list-engine-versions","aws rds get-vmware-engine-versions","aws rds show-engine-versions","'aws rds describe-db-engine-versions' lists all supported RDS on VMware engine versions."
"A developer needs to monitor RDS on VMware for excessive connection attempts. Which CloudWatch metric should be used?","ConnectionAttempts","DBLoad","DatabaseConnections","FreeableMemory","'ConnectionAttempts' tracks the number of connection attempts to RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific stored procedures?","IAM policy with procedure-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific stored procedures in RDS on VMware."
"A developer wants to automate RDS on VMware snapshot exports to S3. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate RDS on VMware snapshot exports to S3."
"Which RDS on VMware feature allows for restricting access to specific database triggers?","IAM policy with trigger-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database triggers in RDS on VMware."
"A developer needs to ensure RDS on VMware is only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","Read replica promotion","DB subnet group only","Security group rules can restrict access to specific IP ranges in RDS on VMware."
"Which RDS on VMware feature allows for monitoring query latency?","CloudWatch QueryLatency metric","DBLoad","DatabaseConnections","FreeableMemory","The QueryLatency metric in CloudWatch measures query latency in RDS on VMware."
"A developer wants to automate RDS on VMware instance scaling based on usage. Which AWS service can help?","Amazon CloudWatch Alarms with Lambda automation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Alarms can trigger Lambda functions to automate RDS on VMware instance scaling."
"Which RDS on VMware feature allows for restricting access to specific database indexes?","IAM policy with index-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database indexes in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive CPU usage. Which CloudWatch metric should be used?","CPUUtilization","DBLoad","DatabaseConnections","FreeableMemory","'CPUUtilization' shows the percentage of CPU used by an RDS on VMware instance."
"Which RDS on VMware feature allows for restricting access to specific database sequences?","IAM policy with sequence-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database sequences in RDS on VMware."
"A developer wants to automate RDS on VMware parameter group updates as part of a deployment pipeline. Which tool is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate parameter group creation and updates for RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database partitions?","IAM policy with partition-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database partitions in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive disk usage. Which CloudWatch metric should be used?","FreeStorageSpace","DBLoad","DatabaseConnections","QueryLatency","'FreeStorageSpace' shows available disk space in an RDS on VMware instance."
"Which RDS on VMware feature allows for restricting access to specific database functions?","IAM policy with function-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database functions in RDS on VMware."
"A developer wants to automate RDS on VMware endpoint failover testing. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate endpoint failover testing for RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database synonyms?","IAM policy with synonym-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database synonyms in RDS on VMware."
"A developer needs to ensure RDS on VMware is only accessible from specific security groups. What should be configured?","Security group rules with group restrictions","Parameter group settings","Read replica promotion","DB subnet group only","Security group rules can restrict access to specific security groups in RDS on VMware."
"Which RDS on VMware feature allows for monitoring transaction conflicts?","CloudWatch TransactionConflicts metric","DBLoad","DatabaseConnections","QueryLatency","The TransactionConflicts metric in CloudWatch tracks transaction conflicts in RDS on VMware."
"A developer wants to automate RDS on VMware backup retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup retention policies for RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database aliases?","IAM policy with alias-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database aliases in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive network throughput. Which CloudWatch metric should be used?","NetworkReceiveThroughput","DBLoad","DatabaseConnections","QueryLatency","'NetworkReceiveThroughput' shows the incoming network traffic to an RDS on VMware instance."
"Which RDS on VMware feature allows for restricting access to specific database roles?","IAM policy with role-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database roles in RDS on VMware."
"A developer wants to automate RDS on VMware maintenance window updates. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate maintenance window updates for RDS on VMware instances."
"Which RDS on VMware feature allows for restricting access to specific database triggers?","IAM policy with trigger-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database triggers in RDS on VMware."
"A developer needs to ensure RDS on VMware is only accessible from specific VPC endpoints. What should be configured?","VPC endpoint policies and security group rules","Parameter group settings","Read replica promotion","DB subnet group only","VPC endpoint policies and security groups restrict access to specific VPC endpoints."
"Which RDS on VMware feature allows for monitoring failed transaction attempts?","CloudWatch FailedTransaction metric","DBLoad","DatabaseConnections","QueryLatency","The FailedTransaction metric in CloudWatch tracks failed transaction attempts in RDS on VMware."
"A developer wants to automate RDS on VMware snapshot retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate snapshot retention policies for RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database users' privileges?","IAM policy with privilege-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific user privileges in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive write IOPS. Which CloudWatch metric should be used?","WriteIOPS","DBLoad","DatabaseConnections","QueryLatency","'WriteIOPS' shows the number of write operations per second in RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database packages?","IAM policy with package-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific database packages in RDS on VMware."
"A developer wants to automate RDS on VMware cluster snapshot exports to S3. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate RDS on VMware cluster snapshot exports to S3."
"Which RDS on VMware feature allows for restricting access to specific database bufferpools?","IAM policy with bufferpool-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific bufferpools in RDS on VMware."
"A developer needs to monitor RDS on VMware for excessive read IOPS. Which CloudWatch metric should be used?","ReadIOPS","DBLoad","DatabaseConnections","QueryLatency","'ReadIOPS' shows the number of read operations per second in RDS on VMware."
"Which RDS on VMware feature allows for restricting access to specific database tablespaces?","IAM policy with tablespace-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific tablespaces in RDS on VMware."
"A developer wants to automate RDS on VMware cluster maintenance window updates. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can automate maintenance window updates for RDS on VMware clusters."
"Which RDS on VMware feature allows for restricting access to specific database bufferpool pages?","IAM policy with bufferpool page-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific bufferpool pages in RDS on VMware."
"A developer needs to ensure RDS on VMware is only accessible from specific AWS accounts. What should be configured?","VPC peering and security group rules with account restrictions","Parameter group settings","Read replica promotion","DB subnet group only","VPC peering and security groups can restrict access to specific AWS accounts for RDS on VMware." 
"Which RDS on VMware feature allows for monitoring deadlocks?","CloudWatch Deadlocks metric","DBLoad","DatabaseConnections","QueryLatency","The Deadlocks metric in CloudWatch tracks deadlocks in RDS on VMware." 
"A developer wants to automate RDS on VMware cluster deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for RDS on VMware clusters." 
"Which RDS on VMware feature allows for restricting access to specific database bufferpool attributes?","IAM policy with bufferpool attribute-level permissions","DB subnet group","Option group","Parameter group","IAM policies can restrict access to specific bufferpool attributes in RDS on VMware."
