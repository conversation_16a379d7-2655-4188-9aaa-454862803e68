"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Security Hub, what is the primary function of a finding?","To highlight potential security issues within your AWS environment.","To automatically remediate security vulnerabilities.","To generate compliance reports for external auditors.","To provide real-time network traffic analysis.","A finding in Security Hub represents a potential security issue identified in your AWS environment, often based on rules or integrations with other services."
"Which AWS service does Security Hub integrate with to provide automated security checks based on industry best practices?","AWS Config","AWS CloudTrail","Amazon GuardDuty","AWS IAM","Security Hub uses AWS Config rules to perform automated security checks and compliance assessments."
"Which of the following actions can you perform on findings in AWS Security Hub?","Change their status, add notes, and suppress them.","Automatically delete them after 30 days.","Export them directly to a third-party SIEM without any integration.","Use them to trigger automatic scaling events in EC2.","You can manage findings by changing their status (e.g., resolved, suppressed), adding notes for context, and suppressing findings that are not relevant."
"What is the purpose of the Security Hub custom action feature?","To create automated responses to specific security findings.","To define custom compliance standards.","To generate custom dashboards in Security Hub.","To create custom IAM roles for Security Hub access.","Custom actions enable you to automate responses to specific security findings, such as opening a support ticket or triggering a Lambda function."
"Which security standard is supported natively by AWS Security Hub for compliance checks?","CIS AWS Foundations Benchmark","PCI DSS","HIPAA","SOC 2","Security Hub natively supports the CIS AWS Foundations Benchmark, providing automated checks against its recommendations."
"In AWS Security Hub, what is the purpose of the 'insights' feature?","To provide visualisations and dashboards of security trends and risks.","To automatically block malicious traffic.","To create custom security policies.","To encrypt data at rest within Security Hub.","Insights in Security Hub provide visualisations and dashboards that help you understand security trends and risks in your AWS environment."
"Which of the following AWS services can send findings to AWS Security Hub?","Amazon GuardDuty","AWS CloudTrail","AWS CloudWatch","AWS IAM","Amazon GuardDuty is a threat detection service that can send its findings to Security Hub for centralised management."
"What is the role of the Security Hub administrator account in an AWS Organisations environment?","To aggregate findings from all member accounts.","To manage IAM roles for all accounts in the organisation.","To enforce security policies across the entire organisation.","To create and manage VPCs for all member accounts.","The administrator account in Security Hub aggregates findings from all member accounts, providing a centralised view of security posture."
"Which of the following is a benefit of using AWS Security Hub?","Centralised security management across multiple AWS accounts.","Automatic patching of vulnerabilities in EC2 instances.","Free DDOS protection for all AWS resources.","Unlimited storage for security logs.","Security Hub provides a centralised platform for managing security across multiple AWS accounts, simplifying monitoring and response."
"What type of data does AWS Security Hub primarily collect and analyse?","Security findings from various AWS services and integrated third-party products.","Network traffic logs from VPC Flow Logs.","Operating system logs from EC2 instances.","Application logs from Lambda functions.","Security Hub primarily collects and analyses security findings from AWS services and integrated third-party security products to provide a comprehensive view of security posture."
"Which action can you take to reduce the number of findings in AWS Security Hub related to overly permissive IAM roles?","Implement the principle of least privilege for IAM roles.","Enable multi-factor authentication for all IAM users.","Regularly rotate IAM access keys.","Encrypt all data at rest with KMS.","Implementing the principle of least privilege by granting only the necessary permissions to IAM roles helps reduce the risk of security issues related to overly permissive access."
"How does AWS Security Hub help with compliance requirements?","By providing automated checks against industry standards and regulations.","By automatically generating compliance reports for auditors.","By enforcing compliance policies across all AWS resources.","By encrypting data to meet compliance requirements.","Security Hub provides automated checks against industry standards and regulations like CIS AWS Foundations Benchmark and PCI DSS, helping organisations assess their compliance posture."
"When integrating a third-party security product with AWS Security Hub, what is typically required?","An integration or connector provided by the third-party vendor.","Granting full administrative access to the AWS account.","Disabling native AWS security services.","Manually uploading log files to Security Hub.","Third-party security products typically require an integration or connector to send their findings to Security Hub in a standardised format."
"What is the purpose of the AWS Security Hub custom insight feature?","To create custom rules and filters for identifying specific security concerns.","To create custom dashboards with specific metrics.","To automate the remediation of findings.","To create custom IAM roles for Security Hub.","Custom insights allow you to create specific rules and filters to identify security concerns relevant to your organisation's unique requirements."
"Which AWS service is used by AWS Security Hub to store and manage findings data?","Security Hub Managed Storage","Amazon S3","Amazon DynamoDB","Amazon RDS","Security Hub stores and manages findings data in its own managed storage, abstracting away the underlying storage mechanism."
"What is the relationship between AWS Security Hub and AWS Inspector?","Inspector is a vulnerability assessment service that sends its findings to Security Hub.","Security Hub is a vulnerability assessment service that sends its findings to Inspector.","They are the same service with different names.","They are completely unrelated services.","AWS Inspector is a vulnerability assessment service, which can send its findings to Security Hub to be viewed alongside findings from other sources."
"If you want to suppress findings related to a specific resource in AWS Security Hub, what is the recommended approach?","Create a suppression rule based on the resource ARN.","Delete the resource from the AWS environment.","Disable the AWS Config rule that generated the finding.","Ignore the findings in the Security Hub console.","Creating a suppression rule based on the resource ARN allows you to suppress findings related to a specific resource without affecting other findings."
"How does AWS Security Hub contribute to incident response?","By providing a centralised view of security alerts and enabling automated actions.","By automatically isolating compromised EC2 instances.","By automatically blocking malicious IP addresses.","By automatically restoring backups of compromised data.","Security Hub contributes to incident response by providing a centralised view of security alerts from various sources and enabling automated actions through custom actions."
"What is a common use case for integrating AWS Security Hub with a security information and event management (SIEM) system?","To forward security findings to the SIEM for further analysis and correlation.","To use the SIEM to manage AWS IAM roles.","To use the SIEM to configure AWS Config rules.","To use the SIEM to encrypt data in AWS S3 buckets.","Integrating Security Hub with a SIEM allows you to forward security findings to the SIEM for further analysis and correlation with other security events."
"What is the primary function of the AWS Security Hub integration with AWS Chatbot?","To send security findings to chat channels for real-time notifications.","To allow users to manage IAM roles from chat channels.","To allow users to launch EC2 instances from chat channels.","To allow users to configure AWS Config rules from chat channels.","The primary function of the Security Hub integration with AWS Chatbot is to send security findings to chat channels for real-time notifications and collaboration."
"In AWS Security Hub, what does it mean to 'accept findings'?","It means acknowledging and reviewing the findings, and taking appropriate action.","It means automatically resolving the findings.","It means deleting the findings from the system.","It means ignoring the findings and preventing them from being displayed.","'Accepting' findings in Security Hub typically means acknowledging and reviewing them to determine the appropriate course of action, such as remediation or suppression."
"How can you customise the severity of findings in AWS Security Hub?","By using custom actions to adjust the severity based on specific criteria.","By modifying the AWS Config rules that generate the findings.","By directly editing the severity field in the Security Hub console.","By creating custom IAM policies for Security Hub.","You can adjust the severity of findings using custom actions to reflect the organisation's risk appetite or specific context."
"What is the purpose of the AWS Security Hub API?","To programmatically manage findings, integrations, and other Security Hub resources.","To generate compliance reports in PDF format.","To create custom dashboards with drag-and-drop functionality.","To manage IAM roles and permissions for Security Hub users.","The Security Hub API allows you to programmatically manage findings, integrations, and other Security Hub resources, enabling automation and integration with other systems."
"How can you ensure that all new AWS accounts created in your organisation are automatically integrated with AWS Security Hub?","Enable automatic enablement in the Security Hub settings of the management account.","Manually configure Security Hub in each new account.","Use AWS CloudFormation StackSets to deploy Security Hub configurations.","Use AWS Organisations policies to enable Security Hub in all accounts.","Automatic enablement in the Security Hub settings of the management account ensures that all new accounts created in the organisation are automatically integrated."
"When should you consider using a custom action in AWS Security Hub?","When you want to trigger an automated response to specific security findings.","When you want to create a custom compliance standard.","When you want to generate a custom dashboard in Security Hub.","When you want to create a custom IAM role for Security Hub access.","Custom actions should be used when you want to trigger an automated response, such as opening a support ticket or triggering a Lambda function, based on specific security findings."
"Which AWS service can be used to automate the remediation of findings identified by AWS Security Hub?","AWS Systems Manager Automation","AWS CloudTrail","AWS CloudWatch Events","AWS Lambda","AWS Systems Manager Automation can be used to automate the remediation of findings identified by Security Hub, allowing you to quickly address security issues."
"What is the primary benefit of using AWS Security Hub in conjunction with Amazon GuardDuty?","Security Hub aggregates and centralises findings from GuardDuty, providing a unified view of security alerts.","Security Hub automatically remediates findings identified by GuardDuty.","GuardDuty automatically configures Security Hub settings.","GuardDuty provides encryption for data stored in Security Hub.","Security Hub aggregates and centralises findings from GuardDuty (and other services), providing a unified view of security alerts from multiple sources."
"Which of the following is a key component of the AWS Security Hub architecture?","The Findings Aggregator","The Rule Engine","The Data Lake","The IAM Role Generator","The Findings Aggregator is a key component that collects findings from various sources and centralises them in Security Hub."
"How does AWS Security Hub contribute to security best practices?","By providing automated checks against industry standards like CIS AWS Foundations Benchmark.","By automatically enforcing security policies across all AWS resources.","By providing free DDOS protection for all AWS resources.","By automatically patching vulnerabilities in EC2 instances.","Security Hub provides automated checks against industry standards, such as the CIS AWS Foundations Benchmark, to help organisations align with security best practices."
"Which of the following is NOT a typical data source for AWS Security Hub?","Operating system logs from on-premises servers.","Amazon GuardDuty findings.","AWS Config rule evaluations.","Third-party security product findings.","Security Hub primarily focuses on AWS-native and integrated third-party findings. It does not directly collect operating system logs from on-premises servers."
"How can you visualise security trends and patterns in AWS Security Hub?","By using the built-in insights and dashboards.","By exporting data to a third-party BI tool.","By querying the underlying data store directly.","By configuring custom alerts in AWS CloudWatch.","Security Hub provides built-in insights and dashboards to help you visualise security trends and patterns in your AWS environment."
"What is the recommended way to manage access to AWS Security Hub?","Using IAM roles and policies with appropriate permissions.","Using AWS Organisations policies to grant full access to all accounts.","Using AWS CloudTrail to monitor user activity in Security Hub.","Using AWS Config to track changes to Security Hub configurations.","Managing access to Security Hub is best done through IAM roles and policies, ensuring users have the least privilege necessary to perform their tasks."
"In AWS Security Hub, what does the term 'integration' refer to?","The connection between Security Hub and other AWS services or third-party security products.","The process of migrating data to AWS.","The process of encrypting data at rest.","The process of configuring VPC Flow Logs.","In Security Hub, 'integration' refers to the connection between Security Hub and other AWS services or third-party security products, allowing them to send findings to Security Hub."
"Which AWS service can be used to trigger automated actions based on findings in AWS Security Hub?","AWS Lambda","Amazon S3","Amazon EC2","Amazon RDS","AWS Lambda functions can be triggered by events in Security Hub, allowing you to automate actions based on security findings."
"Which compliance standard is NOT natively supported in AWS Security Hub?","ISO 27001","CIS AWS Foundations Benchmark","PCI DSS","NIST 800-53","ISO 27001 is not natively supported in AWS Security Hub, though custom integrations could potentially address aspects of this standard."
"How can you export findings from AWS Security Hub for external analysis?","By using the Security Hub API to retrieve findings data in a structured format.","By manually copying and pasting findings from the Security Hub console.","By configuring AWS CloudTrail to capture Security Hub activity.","By directly accessing the underlying data store used by Security Hub.","The Security Hub API can be used to retrieve findings data in a structured format (e.g., JSON) for external analysis."
"What is the purpose of enabling Security Hub across multiple AWS regions?","To gain a global view of your security posture and identify region-specific security issues.","To automatically replicate security findings across all regions.","To enforce a single security policy across all regions.","To improve the performance of Security Hub in each region.","Enabling Security Hub across multiple regions provides a global view of your security posture and helps identify region-specific security issues, as some findings may be region-specific."
"How can you automate the process of sending findings from a custom security tool to AWS Security Hub?","By using the Security Hub API and a custom integration script.","By manually uploading findings data to the Security Hub console.","By configuring AWS CloudTrail to capture findings from the custom tool.","By directly accessing the underlying data store used by Security Hub.","The Security Hub API and a custom integration script are the recommended way to automate the process of sending findings from a custom security tool to Security Hub."
"Which of the following actions is NOT supported by AWS Security Hub's custom actions feature?","Automatically deleting findings.","Opening a support ticket in a third-party system.","Triggering a Lambda function to remediate a vulnerability.","Sending a notification to a chat channel.","While custom actions can trigger many automated responses, they cannot directly delete findings."
"What is the relationship between AWS Security Hub and AWS Trusted Advisor?","Trusted Advisor provides recommendations that can be integrated into Security Hub as findings.","Security Hub automatically implements Trusted Advisor recommendations.","Trusted Advisor provides encryption for data stored in Security Hub.","They are the same service with different names.","Trusted Advisor provides recommendations that can be integrated into Security Hub as findings, providing a more holistic view of security and operational best practices."
"How can you identify the most critical security findings in AWS Security Hub?","By sorting findings by severity and focusing on those with the highest severity scores.","By reviewing the findings with the oldest timestamps.","By reviewing the findings with the most notes attached.","By reviewing the findings with the lowest resource counts.","Sorting findings by severity allows you to quickly identify and prioritise the most critical security issues in your AWS environment."
"Which of the following is NOT a benefit of integrating AWS Security Hub with a security information and event management (SIEM) system?","Automated remediation of security vulnerabilities.","Centralised security monitoring across multiple environments.","Enhanced threat detection and correlation capabilities.","Improved incident response and investigation processes.","Automated remediation of security vulnerabilities is not a direct benefit of integrating Security Hub with a SIEM system. Remediation typically requires additional tools and processes."
"How does AWS Security Hub support a multi-account AWS environment?","By providing centralised visibility and management of security findings across all accounts in an AWS Organisation.","By automatically creating IAM roles in each account.","By automatically configuring VPC peering connections between all accounts.","By automatically encrypting data in all accounts.","Security Hub provides centralised visibility and management of security findings across all accounts in an AWS Organisation, simplifying security monitoring and management in multi-account environments."
"What is the AWS Security Finding Format (ASFF)?","A standardised JSON format for representing security findings in Security Hub.","A proprietary encryption algorithm used by Security Hub.","A custom query language used to search for findings in Security Hub.","A set of pre-defined security rules used by Security Hub.","The AWS Security Finding Format (ASFF) is a standardised JSON format for representing security findings in Security Hub, allowing for consistent data exchange between different services and tools."
"In AWS Security Hub, what is the primary purpose of a 'finding'?","To represent a potential security issue or vulnerability","To store security compliance reports","To define custom security policies","To manage user access permissions","A 'finding' in Security Hub represents a potential security issue, vulnerability, or non-compliance with security standards."
"What does the term 'insight' refer to in the context of AWS Security Hub?","A collection of findings related to a specific security concern","A customisable dashboard view","A pre-configured security standard","A cost optimisation recommendation","An 'insight' in Security Hub is a collection of findings related to a particular security problem or area of interest, allowing for focused investigation."
"Which AWS service is commonly used as a data source for AWS Security Hub?","AWS Config","Amazon SQS","Amazon SNS","Amazon CloudFront","AWS Config is a common data source for Security Hub, providing information about the configuration of AWS resources."
"Which of the following security standards is supported by AWS Security Hub?","CIS AWS Foundations Benchmark","ISO 27001","PCI DSS","SOC 2","Security Hub supports the CIS AWS Foundations Benchmark, among other standards, to assess security posture."
"What is the purpose of the 'Remediation' section within a Security Hub finding?","To provide guidance on how to fix the security issue","To automatically fix the security issue","To escalate the issue to AWS support","To ignore the finding permanently","The 'Remediation' section provides guidance and recommendations on how to address the identified security issue or vulnerability."
"What is the AWS Security Hub integration with AWS CloudTrail used for?","To identify suspicious API activity within your AWS environment","To encrypt log data at rest","To configure multi-factor authentication","To manage IAM roles and policies","The CloudTrail integration allows Security Hub to identify potentially malicious or unauthorized API activity based on CloudTrail logs."
"How does AWS Security Hub contribute to compliance management?","By continuously monitoring your AWS environment against security standards and best practices","By automatically generating compliance reports for auditors","By managing user access permissions to comply with regulations","By providing encryption keys for data at rest","Security Hub helps with compliance by continuously monitoring your AWS environment against various security standards and best practices."
"Which of the following is a key benefit of using AWS Security Hub?","Centralised view of security alerts and compliance status","Automated patching of vulnerabilities","Real-time DDoS protection","Unlimited storage for security logs","Security Hub provides a centralised view of security alerts from various AWS services and third-party integrations."
"What type of data does AWS Security Hub *NOT* ingest and analyse?","Network traffic logs from VPC Flow Logs","Configuration details from AWS Config","Vulnerability scan results from third-party tools","Customer support tickets","Security Hub doesn't directly ingest or analyse customer support tickets. It focuses on security-related data from AWS services and integrated third-party tools."
"Which AWS service can be used to automate remediation actions based on Security Hub findings?","AWS Systems Manager","Amazon EC2 Auto Scaling","AWS Lambda","Amazon CloudWatch Events","AWS Systems Manager can be used to automate remediation tasks based on findings generated by Security Hub."
"What type of information does AWS Security Hub provide about EC2 instances?","Open ports and security group configurations","CPU utilisation metrics","Instance pricing details","Operating system version","Security Hub provides information about open ports and security group configurations for EC2 instances, identifying potential security risks."
"Which of the following AWS services *CANNOT* be directly integrated with AWS Security Hub for findings ingestion?","AWS GuardDuty","AWS Inspector","AWS CloudTrail","Amazon SQS","Amazon SQS is a messaging queue service and is not directly integrated with Security Hub for ingesting security findings. The others are integrated."
"In AWS Security Hub, what is the significance of the 'Workflow' status for a finding?","It indicates the progress of remediation efforts","It indicates whether the finding is a false positive","It determines the severity of the finding","It defines the cost of remediating the finding","The 'Workflow' status tracks the progress of investigation and remediation of a finding."
"Which AWS service helps Security Hub to identify vulnerabilities in EC2 instances?","AWS Inspector","AWS Shield","AWS Certificate Manager","AWS WAF","AWS Inspector is a vulnerability assessment service that integrates with Security Hub to identify vulnerabilities in EC2 instances."
"What is the purpose of the 'Rules' section in AWS Security Hub?","To define custom checks and automated responses to findings","To configure data retention policies","To manage user access permissions","To define cost allocation tags","The 'Rules' section allows you to create custom checks and define automated responses to specific security findings."
"What is the pricing model for AWS Security Hub?","Based on the number of security checks and findings","Flat monthly fee","Based on data storage volume","Free to use for all AWS customers","Security Hub is priced based on the number of security checks performed and the number of findings generated."
"Which AWS service can be used to centrally manage security policies and compliance across multiple AWS accounts using Security Hub?","AWS Organizations","AWS IAM","AWS CloudFormation","AWS Trusted Advisor","AWS Organizations enables centralised management of security policies and compliance across multiple AWS accounts, which integrates with Security Hub."
"Which of the following is a benefit of integrating AWS Security Hub with third-party security tools?","Consolidating security findings from various sources into a single view","Automating the deployment of security patches","Reducing the cost of AWS support","Improving network performance","Integration with third-party tools allows Security Hub to provide a consolidated view of security findings from multiple sources."
"What type of security information does AWS Security Hub provide about Amazon S3 buckets?","Bucket permissions and public access settings","Storage capacity utilisation","Data encryption status","Number of objects stored","Security Hub provides insights into S3 bucket permissions and public access settings, identifying potential misconfigurations."
"How does AWS Security Hub support incident response activities?","By providing a centralised view of security findings and remediation guidance","By automatically isolating compromised instances","By blocking malicious traffic","By encrypting incident response playbooks","Security Hub aids incident response by providing a central location to view security findings and access remediation guidance."
"What is the primary function of the AWS Security Hub API?","To programmatically interact with Security Hub and automate tasks","To monitor AWS resource utilisation","To manage AWS billing and costs","To create custom CloudWatch dashboards","The Security Hub API allows for programmatic interaction with the service, enabling automation of security tasks."
"Which AWS service can be used to receive notifications about new findings in AWS Security Hub?","Amazon EventBridge (formerly CloudWatch Events)","Amazon SNS","Amazon SQS","AWS CloudTrail","Amazon EventBridge (formerly CloudWatch Events) can be used to trigger notifications or actions based on Security Hub findings."
"What is the purpose of the 'Acceptance Testing' status in the workflow of a Security Hub finding?","It's not a valid status in the Security Hub workflow","To indicate that the finding has been acknowledged and is being investigated","To indicate that the finding is a known false positive","To indicate that the remediation has been deployed and is being tested","'Acceptance Testing' isn't a standard workflow status. Workflow statuses indicate the progress of investigation and remediation efforts for a finding."
"In AWS Security Hub, what type of action can be automated based on findings?","Sending notifications, triggering remediation workflows, and creating tickets","Generating cost optimisation reports","Creating new IAM users","Automatically terminating running EC2 instances","Security Hub allows for automating actions such as sending notifications, triggering remediation workflows, and creating tickets based on findings."
"Which AWS service helps to automate security best practices checks within AWS Security Hub?","AWS Config Rules","AWS CloudTrail Insights","AWS Trusted Advisor","AWS IAM Access Analyzer","AWS Config Rules can be used to define custom rules that are evaluated by Security Hub to identify deviations from security best practices."
"What is the function of the 'Findings Provider' in AWS Security Hub?","The service or tool that generates the security finding","The AWS account where the findings are stored","The AWS region where the findings are analysed","The user who acknowledged the finding","The 'Findings Provider' refers to the service or tool (e.g., GuardDuty, Inspector, third-party integrations) that generates the security finding ingested into Security Hub."
"Which AWS service provides threat intelligence data to AWS Security Hub?","Amazon GuardDuty","AWS Firewall Manager","AWS Shield","AWS Certificate Manager","Amazon GuardDuty is a threat detection service that provides threat intelligence data to Security Hub."
"What is the purpose of the 'Security Hub Master Account' in an AWS Organizations environment?","To aggregate and manage security findings from all member accounts","To store all security logs for the organisation","To enforce security policies across all accounts","To manage user access permissions for all accounts","The Security Hub Master Account serves as the central point for aggregating and managing security findings from all member accounts in an AWS Organizations environment."
"Which of the following is an example of a security finding category in AWS Security Hub?","IAM policy issues","CPU utilisation spikes","Network latency issues","Disk space exhaustion","Security Hub reports on IAM policy issues, providing insights into potential misconfigurations in IAM policies."
"What is the primary purpose of AWS Security Hub's 'Insights' feature?","To surface meaningful patterns and trends from security findings","To provide cost optimisation recommendations","To manage user access permissions","To generate compliance reports for auditors","The 'Insights' feature is designed to help surface meaningful patterns and trends from security findings, allowing for better prioritisation and investigation."
"Which of the following is an action you can take on a finding within the AWS Security Hub console?","Update the workflow status","Change the finding's severity","Delete the finding","Modify the resource associated with the finding","Within the Security Hub console, you can update the workflow status of a finding to track its progress through investigation and remediation."
"What data does AWS Security Hub use from AWS CloudTrail?","API call activity","EC2 instance types","VPC configurations","S3 bucket inventory","Security Hub uses CloudTrail data to detect suspicious API call activity, such as unauthorized attempts to modify security groups or IAM policies."
"How does AWS Security Hub help improve an organisation's security posture?","By providing a centralised view of security alerts and compliance status, along with remediation guidance","By automatically patching vulnerabilities in EC2 instances","By providing real-time DDoS protection","By encrypting all data in transit and at rest","Security Hub improves security posture by providing a single pane of glass for security alerts and compliance, along with guidance on how to remediate issues."
"What is the purpose of the 'Custom Actions' feature in AWS Security Hub?","To define custom responses to security findings, such as triggering a Lambda function","To define custom compliance standards","To create custom IAM roles","To define custom network policies","'Custom Actions' allows you to define custom responses to specific security findings, such as triggering a Lambda function to automate remediation steps."
"Which AWS service can be used to create custom visualisations of security data from AWS Security Hub?","Amazon QuickSight","Amazon CloudWatch Dashboards","AWS CloudFormation","AWS Systems Manager","Amazon QuickSight can be used to create custom visualisations of security data from Security Hub, enabling better understanding and monitoring of security trends."
"How does AWS Security Hub help with GDPR compliance?","By providing insights into data residency and access control configurations","By automatically encrypting all personal data","By generating GDPR compliance reports","By managing user consent for data processing","Security Hub helps with GDPR compliance by providing visibility into data residency, access control configurations, and other factors relevant to the regulation."
"What is the relationship between AWS Security Hub and AWS Firewall Manager?","Firewall Manager can be used to centrally manage firewall rules across multiple accounts, and Security Hub provides visibility into those rules and their effectiveness","Security Hub manages the deployment of AWS Firewall Manager rules","Firewall Manager is a component of Security Hub","They are not related","AWS Firewall Manager allows for central management of firewall rules, and Security Hub provides visibility into the effectiveness of those rules across multiple accounts."
"Which of the following is a key component of AWS Security Hub's architecture?","Findings Aggregation","Data Encryption","Load Balancing","Content Delivery Network","Findings Aggregation is a core component of Security Hub's architecture, consolidating findings from various sources into a central view."
"How does AWS Security Hub support organisations with multiple AWS accounts?","By providing a centralised view of security findings across all accounts within an AWS Organization","By automatically replicating security policies across all accounts","By providing discounted pricing for multi-account deployments","By automatically creating new accounts for security purposes","Security Hub supports multi-account environments by providing a centralised view of security findings across all accounts within an AWS Organization."
"What is the purpose of the 'Severity' attribute for a finding in AWS Security Hub?","To indicate the potential impact of the security issue","To indicate the cost of remediating the finding","To indicate the age of the finding","To indicate the complexity of the finding","The 'Severity' attribute reflects the potential impact or risk posed by the security issue identified in the finding."
"What is a use case for integrating AWS Security Hub with a Security Information and Event Management (SIEM) system?","To centralise security monitoring and incident response across AWS and on-premises environments","To automate the deployment of security patches","To reduce the cost of AWS support","To improve network performance","Integrating Security Hub with a SIEM system allows for centralising security monitoring and incident response across both AWS and on-premises environments."
"Which of the following is a supported input format for ingesting custom findings into AWS Security Hub?","AWS Security Finding Format (ASFF)","JSON","XML","CSV","The AWS Security Finding Format (ASFF) is the standard input format for ingesting custom findings into Security Hub."
"In AWS Security Hub, what is the purpose of the 'Standards Control'?","To provide guidance for satisfying compliance requirements of a supported standard","To manage which users can access Security Hub","To manage the cost of running Security Hub","To define custom compliance standards","'Standards Controls' provide guidance for satisfying compliance requirements of a supported standard, helping users understand how to meet those requirements."
"Which AWS service is used to automate the deployment of security configurations across an AWS environment, which can then be monitored by AWS Security Hub?","AWS CloudFormation","AWS Lambda","AWS Systems Manager","Amazon CloudWatch","AWS CloudFormation can be used to automate the deployment of security configurations, which can then be monitored for compliance and security by Security Hub."
"What is the name of the identity and access management role required to access and update AWS Security Hub?","AWSSecurityHubServiceRolePolicy","SecurityHubAdmin","SecurityHubReadOnlyAccess","AWSSecurityHubFullAccess","AWSSecurityHubServiceRolePolicy is the role needed to access and update AWS Security Hub."
"Which of the following action a security engineer can perform on a finding using AWS CLI?","Update finding's severity","They cannot perform any actions on the findings using CLI","Update finding's region","Update finding's account id","A security engineer cannot change the severity of a finding. The CLI can be used to accept findings or update their status in the workflow."
"What AWS service could you use to trigger automated actions when Security Hub detects a non-compliant resource?","AWS Lambda, using Security Hub findings as triggers","AWS Step Functions, orchestrating Security Hub findings","AWS CloudTrail, using Security Hub events","AWS IAM, using Security Hub rules","You can use AWS Lambda functions triggered by Security Hub findings to automate remediation actions for non-compliant resources. The event trigger would be a change in Security Hub findings."
"What is a pre-requisite to enabling cross-region aggregation in AWS Security Hub?","Enabling Security Hub in the aggregator region","Enabling AWS Config in the aggregator region","Enabling AWS CloudTrail in the aggregator region","Enabling VPC Flow logs in the aggregator region","To enable cross-region aggregation, you must first enable Security Hub in the designated aggregator region before setting up aggregation from other regions."
"In AWS Security Hub, what is the purpose of 'Insights'?","To provide actionable information and visualisations based on security findings","To automatically remediate security vulnerabilities","To encrypt data at rest","To manage user access permissions","Insights in Security Hub are designed to provide actionable information and visualisations based on security findings, helping users understand and prioritise security issues."
"Which AWS service integrates with AWS Security Hub to provide vulnerability scanning for EC2 instances?","AWS Inspector","AWS Shield","AWS Config","AWS IAM","AWS Inspector is a vulnerability assessment service that integrates with Security Hub to provide vulnerability scanning for EC2 instances and container images."
"What does a 'Finding' in AWS Security Hub represent?","A potential security issue identified by a security check or integration","A user account that needs MFA enabled","A cost optimisation recommendation","A list of all configured security controls","A 'Finding' in Security Hub represents a potential security issue identified by a security check from an integrated service or a custom action."
"When enabling AWS Security Hub for the first time, what is the primary action you need to take?","Enable Security Hub in your AWS account.","Create a new IAM user for Security Hub.","Configure a VPC endpoint for Security Hub.","Subscribe to AWS GuardDuty.","Enabling Security Hub in your AWS account is the first step to start using the service."
"In AWS Security Hub, what is the purpose of the 'CIS AWS Foundations Benchmark'?","To assess your AWS environment against a set of security best practices.","To automatically apply security patches to EC2 instances.","To monitor network traffic for malicious activity.","To manage encryption keys for S3 buckets.","The CIS AWS Foundations Benchmark provides a set of security best practices for configuring AWS resources, and Security Hub can assess your environment against these recommendations."
"What is the purpose of the AWS Security Hub 'Custom Actions' feature?","To automate responses to specific security findings","To create custom dashboards","To import findings from third-party security tools","To define custom compliance standards","Custom Actions allow you to automate responses to specific security findings in Security Hub."
"Which of the following AWS services does NOT directly integrate with AWS Security Hub for sending findings?","Amazon SQS","AWS GuardDuty","AWS Inspector","AWS Config","Amazon SQS is a message queuing service and does not directly send findings to Security Hub. GuardDuty, Inspector, and Config do."
"What is the primary purpose of the 'Findings Provider - Integration' in AWS Security Hub?","To ingest security findings from third-party security tools","To create custom security checks","To automatically remediate security vulnerabilities","To manage IAM permissions","The 'Findings Provider - Integration' feature allows Security Hub to ingest security findings from various third-party security tools and services."
"Which compliance standard is supported by AWS Security Hub out-of-the-box?","Payment Card Industry Data Security Standard (PCI DSS)","ISO 27001","SOC 2","HIPAA","Security Hub provides built-in support for the Payment Card Industry Data Security Standard (PCI DSS), allowing you to assess your environment's compliance with this standard."
"In AWS Security Hub, what does the 'Severity' of a finding indicate?","The potential impact of the security issue.","The cost to remediate the security issue.","The number of resources affected by the security issue.","The age of the security issue.","The 'Severity' of a finding in Security Hub indicates the potential impact or risk associated with the identified security issue."
"How can you centrally manage Security Hub across multiple AWS accounts?","Using AWS Organisations and enabling Security Hub as a delegated administrator.","Creating separate Security Hub configurations for each account.","Using IAM cross-account roles.","Forwarding all findings to a central S3 bucket.","Security Hub supports centralised management across multiple accounts through AWS Organisations, allowing you to designate a delegated administrator account."
"Which AWS service can be used to automate remediation actions based on AWS Security Hub findings?","AWS Systems Manager","AWS CloudTrail","AWS CloudWatch","AWS Trusted Advisor","AWS Systems Manager can be used to automate remediation actions based on Security Hub findings, allowing you to respond to security issues more efficiently."
"What type of data does AWS Security Hub primarily collect and analyse?","Security findings from various AWS services and third-party integrations.","Network traffic logs.","User activity logs.","System performance metrics.","Security Hub primarily collects and analyses security findings from various AWS services and integrated third-party tools."
"What is the benefit of using the AWS Security Hub API?","Automate the ingestion, management, and analysis of security findings.","Monitor resource utilisation.","Manage IAM permissions.","Create custom dashboards.","The Security Hub API allows you to automate the ingestion, management, and analysis of security findings, enabling programmatic interaction with the service."
"In AWS Security Hub, what is the purpose of the 'Workflow' status for a finding?","To track the progress of investigating and resolving a security issue.","To indicate the severity of the security issue.","To identify the resource affected by the security issue.","To specify the owner of the security issue.","The 'Workflow' status in Security Hub is used to track the progress of investigating and resolving a security issue, providing a way to manage the lifecycle of findings."
"When a new AWS service is enabled in your AWS account, how does AWS Security Hub handle the findings generated by that service?","Security Hub automatically starts ingesting and processing the findings, if the integration is supported.","You need to manually configure Security Hub to ingest findings from the new service.","You need to create a new custom action to process findings from the new service.","Security Hub does not support new services until it is manually updated.","Security Hub automatically starts ingesting and processing findings from newly enabled services, provided that the integration is supported."
"What is the primary reason for aggregating findings from multiple AWS services in AWS Security Hub?","To provide a centralised view of your security posture.","To reduce the cost of security monitoring.","To improve the performance of individual security tools.","To simplify IAM permission management.","Aggregating findings in Security Hub provides a centralised view of your security posture, making it easier to identify and prioritise security issues across your AWS environment."
"How does AWS Security Hub help in meeting compliance requirements?","By providing compliance checks and reports based on industry standards.","By automatically applying security patches to EC2 instances.","By encrypting data at rest.","By managing user access permissions.","Security Hub provides compliance checks and reports based on industry standards, helping you assess and demonstrate compliance with these requirements."
"Which AWS service can be used to enrich AWS Security Hub findings with additional context from threat intelligence feeds?","Amazon GuardDuty","AWS Shield","AWS Threat Intelligence","AWS CloudTrail","Amazon GuardDuty provides threat intelligence feeds that can enrich Security Hub findings with additional context about potential threats."
"In AWS Security Hub, what is the purpose of the 'Remediation' section within a finding's details?","To provide guidance and recommendations on how to fix the security issue.","To automatically apply security patches.","To disable the affected resource.","To delete the finding from Security Hub.","The 'Remediation' section within a finding's details provides guidance and recommendations on how to fix the identified security issue."
"Which of the following actions can be performed on a finding in AWS Security Hub?","Archive, update workflow status, and add notes.","Delete, ignore, and export.","Remediate, encrypt, and analyse.","Backup, restore, and monitor.","You can archive findings, update their workflow status, and add notes to them in Security Hub."
"How can you filter findings in AWS Security Hub to focus on the most critical security issues?","By filtering based on severity, resource type, and compliance status.","By filtering based on region, account ID, and user name.","By filtering based on cost, performance, and availability.","By filtering based on data type, encryption status, and network configuration.","You can filter findings in Security Hub based on severity, resource type, and compliance status to focus on the most critical security issues."
"What is the role of the AWS Security Hub standards control?","To assess AWS resource configurations against best practices.","To enforce IAM permissions across the organisation.","To encrypt network traffic between AWS resources.","To monitor CPU utilisation of EC2 instances.","Security Hub standards controls assess AWS resource configurations against best practices, providing a measure of compliance with various security standards."
"How does AWS Security Hub pricing work?","Based on the number of security checks performed and findings ingested.","Based on the amount of data stored in Security Hub.","Based on the number of users accessing Security Hub.","Based on the number of AWS accounts connected to Security Hub.","Security Hub pricing is based on the number of security checks performed and the number of findings ingested."
"What is the purpose of enabling AWS Config integration with AWS Security Hub?","To provide configuration details for resources identified in security findings.","To automatically remediate non-compliant resources.","To generate cost optimisation reports.","To manage IAM permissions for AWS resources.","Integrating AWS Config with Security Hub provides configuration details for resources identified in security findings, enabling better context for security investigations."
"Which AWS service can be used to create custom security checks for AWS Security Hub?","AWS Lambda","AWS CloudWatch","AWS CloudTrail","AWS IAM","AWS Lambda can be used to create custom security checks for Security Hub, allowing you to implement security logic tailored to your specific requirements."
"How can you export findings from AWS Security Hub for further analysis?","By using the Security Hub API or console to export findings in various formats.","By copying and pasting findings from the Security Hub console.","By creating a manual snapshot of the Security Hub database.","By sending findings directly to AWS CloudTrail.","You can export findings from Security Hub using the API or console in various formats like CSV or JSON for further analysis."
"What is the relationship between AWS Security Hub and AWS Trusted Advisor?","Security Hub focuses on security findings while Trusted Advisor focuses on cost optimisation, performance, and security recommendations.","Security Hub replaces Trusted Advisor for security recommendations.","Trusted Advisor integrates directly with Security Hub to provide automated remediation.","Security Hub uses Trusted Advisor as its underlying security engine.","Security Hub focuses on security findings from various services, while Trusted Advisor provides cost optimisation, performance, and security recommendations."
"Which AWS Region(s) is AWS Security Hub available in?","Security Hub is available in most AWS Regions.","Security Hub is only available in US East (N. Virginia).","Security Hub is only available in Europe (Ireland).","Security Hub is only available in Asia Pacific (Tokyo).","Security Hub is available in most AWS Regions, allowing you to centralise security monitoring across your global AWS footprint."
"What is the purpose of the 'Standards' section in AWS Security Hub?","To view the status of your environment against various compliance standards.","To create custom security checks.","To manage IAM permissions.","To configure network security groups.","The 'Standards' section in Security Hub allows you to view the status of your environment against various compliance standards like CIS AWS Foundations Benchmark and PCI DSS."
"When integrating a third-party security tool with AWS Security Hub, what is typically required?","An AWS Marketplace subscription for the third-party tool and enabling the integration in Security Hub.","A manual configuration of network ACLs.","A custom IAM role for the third-party tool.","A dedicated VPC endpoint for the third-party tool.","Integrating a third-party security tool with Security Hub typically requires an AWS Marketplace subscription and enabling the integration in Security Hub."
"How does AWS Security Hub handle findings from regions where Security Hub is not enabled?","Findings from regions where Security Hub is not enabled are not collected.","Findings are automatically forwarded to the nearest Security Hub enabled region.","You must manually configure cross-region replication for findings.","Findings are stored in S3 for later analysis.","Findings from regions where Security Hub is not enabled are not collected, so it's important to enable Security Hub in all relevant regions."
"What is the purpose of the 'Automation rules' in AWS Security Hub?","To automatically update the status of findings based on specific conditions.","To automatically create new security groups.","To automatically encrypt EBS volumes.","To automatically scale EC2 instances.","Automation rules in Security Hub are used to automatically update the status of findings based on specific conditions, such as changing the workflow status or adding notes."
"You have identified a false positive finding in AWS Security Hub. What action should you take?","Update the workflow status to 'Resolved' and add a note explaining the false positive.","Delete the finding from Security Hub.","Disable the security check that generated the finding.","Create a new security group to prevent similar findings.","You should update the workflow status to 'Resolved' and add a note explaining why it's a false positive to document the decision."
"What is the maximum retention period for findings in AWS Security Hub?","90 days","30 days","180 days","365 days","The maximum retention period for findings in Security Hub is 90 days."
"How can you monitor changes to security findings in AWS Security Hub in real time?","By configuring CloudWatch Events to trigger on finding changes.","By subscribing to Security Hub SNS topics.","By polling the Security Hub API every minute.","By enabling CloudTrail logging for Security Hub API calls.","You can configure CloudWatch Events to trigger on finding changes in Security Hub, enabling real-time monitoring of security events."
"What is the purpose of the 'RecordState' attribute of a Security Hub finding?","Indicates whether the finding is active or archived.","Indicates the severity of the finding.","Indicates the resource affected by the finding.","Indicates the user who created the finding.","The 'RecordState' attribute indicates whether the finding is active or archived, allowing you to filter and manage findings based on their current state."
"How does AWS Security Hub support integration with SIEM (Security Information and Event Management) systems?","By exporting findings to SIEM systems via the Security Hub API or integrations.","By running SIEM agents on Security Hub.","By creating custom dashboards in the SIEM system.","By replacing the SIEM system with Security Hub.","Security Hub supports integration with SIEM systems by exporting findings via the API or pre-built integrations, allowing you to feed security data into your existing SIEM for broader analysis."
"Which AWS service can be used to visualise AWS Security Hub findings and create custom dashboards?","Amazon QuickSight","AWS CloudWatch","AWS CloudTrail","AWS Trusted Advisor","Amazon QuickSight can be used to visualise Security Hub findings and create custom dashboards to gain deeper insights into your security posture."
"What is the impact of disabling AWS Security Hub in an AWS account?","Security findings will no longer be collected and analysed, and existing findings will eventually be deleted.","IAM users will lose access.","All EC2 instances will be stopped.","All S3 buckets will be encrypted.","Disabling Security Hub will stop the collection and analysis of security findings, and existing findings will eventually be deleted, so you will lose visibility into your security posture."
"You want to automate the process of enabling AWS Security Hub in new AWS accounts. Which approach is most suitable?","Using AWS CloudFormation or AWS Organisations StackSets to deploy Security Hub configurations.","Manually enabling Security Hub in each new account.","Creating a custom IAM role for Security Hub.","Configuring a VPC endpoint for Security Hub.","Using CloudFormation or Organisations StackSets allows you to automate the deployment of Security Hub configurations to new accounts, ensuring consistent security monitoring across your environment."
"What type of data does AWS Security Hub NOT analyse?","Network flow logs","Vulnerability scans","Configuration compliance checks","Threat intelligence feeds","Security Hub doesn't analyse network flow logs, instead it analyses vulnerability scans, configuration compliance checks, and threat intelligence feeds."
"Which feature of AWS Security Hub allows you to define your own rules and actions based on security findings?","Custom actions","Automation rules","Insights","Standards","Custom actions enable you to define your own rules and actions based on security findings, allowing for tailored security responses."
"What is the benefit of using AWS Security Hub with AWS Organisations?","Centralised management of security posture across multiple AWS accounts.","Automatic encryption of data at rest.","Simplified IAM permission management.","Reduced cost of security monitoring.","Using Security Hub with Organisations provides a centralised view and management of security posture across multiple AWS accounts, making it easier to enforce consistent security policies."
"Which AWS service is commonly used to store and analyse long-term historical security data collected from AWS Security Hub?","Amazon S3","Amazon DynamoDB","Amazon RDS","Amazon EC2","Amazon S3 is commonly used to store long-term historical security data collected from Security Hub for archival and analysis purposes."
"What is the main difference between AWS Security Hub and AWS Audit Manager?","Security Hub focuses on security findings and compliance checks, while Audit Manager focuses on automating compliance assessments.","Security Hub replaces Audit Manager for compliance reporting.","Audit Manager integrates directly with Security Hub to provide automated remediation.","Security Hub is only for security, while Audit Manager is for cost and performance.","Security Hub focuses on security findings and compliance checks, while Audit Manager focuses on automating compliance assessments."
"How does the use of AWS Security Hub contribute to improved security governance?","Provides a centralised view of security findings and compliance status, enabling informed decision-making.","It automatically applies security patches.","Enforces IAM permissions across the organisation.","Reduces the cost of security monitoring.","Security Hub provides a centralised view of security findings and compliance status, enabling informed decision-making and improved security governance."
"What is the purpose of the 'BatchImportFindings' API call in AWS Security Hub?","To import multiple security findings from external sources into Security Hub.","To export multiple security findings from Security Hub to external systems.","To delete multiple security findings from Security Hub.","To update the status of multiple security findings in Security Hub.","The 'BatchImportFindings' API call allows you to import multiple security findings from external sources into Security Hub, enabling integration with third-party security tools."
"In AWS Security Hub, what is the primary function of a 'finding'?","A potential security issue identified in your AWS environment","A report of all security-related events","A custom rule for identifying threats","A group of related security standards","A finding represents a potential security issue discovered by Security Hub or integrated security tools."
"What is the main purpose of the AWS Security Hub integration with AWS Organizations?","To centrally manage security across multiple AWS accounts","To automate the creation of IAM roles","To provide cost optimisation recommendations","To simplify the process of creating VPCs","The integration with AWS Organizations allows you to aggregate and manage security findings from all accounts within your organisation in a single Security Hub console."
"Which AWS service does Security Hub primarily use to collect security-relevant events and logs?","AWS Config","AWS CloudWatch","AWS CloudTrail","AWS Lambda","Security Hub aggregates findings and insights from other AWS services, including AWS Config, to provide a comprehensive view of your security posture."
"How does AWS Security Hub help with compliance?","By providing a consolidated view of compliance status against industry standards","By automatically remediating non-compliant resources","By enforcing specific security policies","By generating compliance documentation","Security Hub provides a dashboard that shows your compliance status against supported security standards and best practices, such as CIS benchmarks and PCI DSS."
"What is the purpose of the AWS Security Hub 'Insights' feature?","To identify trends and patterns in security findings","To automatically generate security reports","To configure custom security rules","To integrate with third-party security tools","Insights help you identify trends and patterns in your security findings, allowing you to focus on the most critical issues."
"In AWS Security Hub, what does enabling a security standard (e.g., CIS AWS Foundations Benchmark) do?","It activates a set of pre-defined rules to check your resources against that standard.","It automatically remediates any non-compliant resources.","It restricts access to certain AWS services.","It generates a compliance report based on the resources in your account.","Enabling a security standard in Security Hub activates pre-defined rules and checks to evaluate your AWS resources against the requirements of that standard."
"Which of the following is a benefit of using AWS Security Hub's automated security checks?","Reduced manual effort in identifying security issues","Guaranteed compliance with all industry regulations","Elimination of all security vulnerabilities","Automatic patching of security flaws","Automated security checks reduce the manual effort required to identify potential security issues and ensure consistent monitoring."
"How can you send AWS Security Hub findings to other security information and event management (SIEM) systems?","By using the Security Hub API or integrating with partner solutions","By manually exporting the findings as CSV files","By creating a custom CloudWatch Events rule","By directly configuring a connection to the SIEM system within Security Hub","Security Hub provides APIs and integrates with various partner solutions to allow you to send findings to your preferred SIEM system for centralised analysis."
"What is the significance of the 'Severity' attribute in an AWS Security Hub finding?","It indicates the potential impact of the security issue","It represents the cost of remediating the issue","It reflects the complexity of the security vulnerability","It defines the user who reported the finding","The severity attribute indicates the potential impact or risk associated with the security issue identified in the finding, helping prioritize remediation efforts."
"How does AWS Security Hub assist in incident response?","By providing a centralised view of security alerts and findings","By automatically isolating compromised resources","By shutting down affected AWS accounts","By generating pre-written incident response plans","Security Hub provides a centralised view of security alerts and findings, allowing incident responders to quickly assess and respond to security incidents."
"Which AWS service can be used to automatically remediate findings identified by AWS Security Hub?","AWS Systems Manager","AWS CloudWatch Events","AWS Lambda","AWS IAM","AWS Systems Manager Automation can be used to automatically remediate certain types of findings identified by Security Hub, such as correcting misconfigured security group rules."
"In AWS Security Hub, what is the purpose of creating a custom action?","To trigger specific actions when a finding matches certain criteria","To define custom security standards","To create new types of security findings","To automate the process of gathering security data","Custom actions in Security Hub allow you to trigger specific actions, such as sending a finding to a ticketing system, when a finding matches certain criteria."
"How does AWS Security Hub contribute to a 'shift left' security approach?","By enabling developers to address security issues earlier in the development lifecycle","By automating the deployment of security patches","By restricting access to production environments","By mandating specific coding practices","Security Hub helps to 'shift left' by providing developers with early visibility into potential security issues, enabling them to address them before they reach production."
"Which of the following security standards is NOT supported directly by AWS Security Hub?","HIPAA","CIS AWS Foundations Benchmark","PCI DSS","NIST 800-53","While Security Hub provides integrations and findings related to compliance, it directly supports CIS AWS Foundations Benchmark, PCI DSS, and NIST 800-53. HIPAA requires a different approach and is not directly supported"
"What level of access is required to enable and manage Security Hub for an AWS account?","IAM permissions to manage Security Hub and related services","Root account access","Read-only access to all AWS services","Administrator access to the organisation's master account","To enable and manage Security Hub, you need IAM permissions that allow you to manage Security Hub resources and access related services like AWS Config and AWS CloudTrail."
"When aggregating findings from multiple AWS regions in Security Hub, what is the designated region called?","Aggregation Region","Primary Region","Home Region","Master Region","The aggregation region (also known as the Home Region) is the designated region where findings from multiple AWS regions are consolidated in Security Hub."
"How can you suppress a finding in AWS Security Hub?","By changing the finding's workflow status to 'Suppressed'","By deleting the finding from the Security Hub console","By modifying the underlying resource that generated the finding","By disabling the security standard that generated the finding","Changing the workflow status of a finding to 'Suppressed' hides the finding from the active findings list, allowing you to focus on actionable items."
"What is the relationship between AWS Security Hub and AWS Trusted Advisor?","Security Hub provides a consolidated view of Trusted Advisor's security checks","Trusted Advisor automatically remediates findings in Security Hub","Security Hub replaces the functionality of Trusted Advisor","Trusted Advisor only provides cost optimisation recommendations","Security Hub integrates with Trusted Advisor to provide a consolidated view of security-related recommendations from Trusted Advisor."
"What type of data does AWS Security Hub NOT analyse?","Network traffic logs","AWS CloudTrail logs","AWS Config configuration items","Findings from integrated partner solutions","Security Hub primarily focuses on analysing AWS CloudTrail logs, AWS Config configuration items, and findings from integrated partner solutions. It doesn't directly analyse network traffic logs."
"Which IAM condition key can be used to control access to specific findings within AWS Security Hub?","securityhub:FindingArn","iam:Resource","aws:ResourceTag","securityhub:SecurityStandardArn","The `securityhub:FindingArn` IAM condition key allows you to control access to specific findings based on their ARN within Security Hub."
"You need to integrate a custom security tool with AWS Security Hub. What is the recommended approach?","Use the Security Hub API to send findings to Security Hub in the AWS Security Finding Format (ASFF)","Directly write to the Security Hub database","Modify the Security Hub source code","Use AWS CloudTrail to forward events to Security Hub","The recommended approach for integrating a custom security tool is to use the Security Hub API to send findings in the AWS Security Finding Format (ASFF)."
"Which AWS service can provide you with detailed information about API calls made within your AWS account, which Security Hub can then analyse?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS IAM","AWS CloudTrail provides detailed information about API calls made within your AWS account, which Security Hub can use to identify potential security issues."
"What is the AWS Security Finding Format (ASFF) used for?","Standardising the format of security findings reported to Security Hub","Encrypting data at rest within Security Hub","Defining custom security policies in Security Hub","Generating reports from Security Hub data","The AWS Security Finding Format (ASFF) is a standardised format used to represent security findings reported to Security Hub, ensuring consistency across different sources."
"How does AWS Security Hub help with prioritising security issues?","By assigning a severity score to each finding","By automatically remediating high-severity issues","By recommending specific security tools","By blocking access to vulnerable resources","Security Hub assigns a severity score to each finding, allowing you to prioritise remediation efforts based on the potential impact of the issue."
"What is the purpose of the 'Workflow Status' field in an AWS Security Hub finding?","To track the progress of the finding's remediation","To indicate the severity of the finding","To identify the user who reported the finding","To specify the AWS region where the finding originated","The 'Workflow Status' field in a Security Hub finding is used to track the progress of the finding's remediation, allowing you to manage the lifecycle of security issues."
"In a multi-account AWS environment, how can you centrally manage Security Hub settings and configurations?","By using AWS Organizations to delegate administrative access","By manually configuring each account individually","By creating a custom IAM role","By using AWS CloudFormation StackSets","By using AWS Organizations and designating a master account, you can centrally manage Security Hub settings and configurations across all accounts within your organisation."
"Which of the following is a key benefit of using AWS Security Hub compared to manually configuring security checks in each AWS service?","Centralised visibility and management of security posture","Lower cost of operation","Increased flexibility in defining security rules","Automated incident response workflows","Security Hub provides a centralised view of your security posture, simplifying the management and monitoring of security across your AWS environment."
"What is the default data retention period for findings in AWS Security Hub?","90 days","30 days","365 days","7 days","The default data retention period for findings in AWS Security Hub is 90 days."
"How does AWS Security Hub help organisations meet their security compliance requirements?","By automating the process of gathering evidence for audits","By guaranteeing compliance with all security standards","By providing a list of recommended security tools","By restricting access to non-compliant resources","Security Hub provides a consolidated view of compliance status and helps organisations gather evidence for audits by tracking compliance against supported security standards."
"What is the role of the AWS Security Hub administrator account in a multi-account setup?","To centrally manage security settings and view findings across all member accounts","To manage IAM roles for all accounts","To configure network settings for all accounts","To create and manage S3 buckets for all accounts","In a multi-account setup, the Security Hub administrator account is responsible for centrally managing security settings and viewing findings across all member accounts."
"Which AWS service is required for AWS Security Hub to function properly?","AWS Config","AWS CloudWatch Logs","AWS IAM","AWS Trusted Advisor","AWS Config is essential for Security Hub to function properly, as it provides configuration change information and enables continuous monitoring of resource configurations."
"How can you customise AWS Security Hub to focus on specific security threats relevant to your organisation?","By creating custom insights and filters","By modifying the Security Hub source code","By disabling certain security standards","By creating custom IAM policies","You can customise Security Hub to focus on specific security threats by creating custom insights and filters based on your organisation's specific needs and risk profile."
"When integrating AWS Security Hub with third-party security tools, what is the primary requirement?","The third-party tool must support the AWS Security Finding Format (ASFF)","The third-party tool must be installed on an EC2 instance","The third-party tool must be open-source","The third-party tool must be certified by AWS","The primary requirement for integrating with third-party security tools is that they must support the AWS Security Finding Format (ASFF) to ensure seamless data exchange."
"Which of the following AWS Security Hub resources is billable?","The number of security findings ingested","The number of accounts enabled in Security Hub","The number of security standards enabled","The number of custom actions created","AWS Security Hub charges are based on the number of security findings ingested, providing a cost-effective approach to security monitoring."
"What is the advantage of using AWS Security Hub's pre-built integrations?","Simplified deployment and management of security tools","Reduced licensing costs for security tools","Increased performance of security tools","Automatic updates to security tools","Pre-built integrations in Security Hub simplify the deployment and management of integrated security tools, allowing you to quickly leverage their capabilities."
"How can you automate the process of enabling AWS Security Hub across multiple AWS accounts?","By using AWS CloudFormation StackSets","By manually enabling Security Hub in each account","By creating a custom AWS Lambda function","By using AWS Systems Manager Automation","You can automate the process of enabling Security Hub across multiple accounts by using AWS CloudFormation StackSets, which allows you to deploy consistent configurations across multiple regions and accounts."
"What is the primary benefit of using AWS Security Hub's integration with AWS Inspector?","To automatically identify and assess security vulnerabilities in EC2 instances and container images","To automatically patch security vulnerabilities","To automatically encrypt data at rest","To automatically perform penetration testing","The integration with AWS Inspector allows Security Hub to automatically identify and assess security vulnerabilities in EC2 instances and container images, providing a comprehensive view of your security posture."
"How can you use AWS Security Hub to track progress on addressing security issues over time?","By monitoring the trend of findings with specific workflow statuses","By generating compliance reports","By comparing findings across different AWS regions","By analysing CloudTrail logs","You can track progress on addressing security issues over time by monitoring the trend of findings with specific workflow statuses, such as 'In Progress' or 'Resolved'."
"Which of the following is NOT a typical use case for AWS Security Hub?","Cost optimisation","Compliance monitoring","Threat detection","Incident response","Security Hub is primarily focused on compliance monitoring, threat detection, and incident response. Cost optimisation is typically handled by other AWS services like Cost Explorer."
"What is the purpose of the 'Record State' field in an AWS Security Hub finding?","Indicates whether the finding is active or archived","Shows the severity of the finding","Specifies the AWS region where the finding originated","Indicates the user who reported the finding","The 'Record State' field indicates whether the finding is active or archived, allowing you to manage the lifecycle of security findings and focus on actionable items."
"How does AWS Security Hub help with the implementation of a zero-trust security model?","By providing visibility into security risks and enabling continuous monitoring of resource configurations","By automatically enforcing multi-factor authentication","By automatically segmenting the network","By automatically restricting access to sensitive data","Security Hub helps with the implementation of a zero-trust security model by providing visibility into security risks and enabling continuous monitoring of resource configurations, allowing you to verify trust at every stage."
"Which AWS service can be integrated with AWS Security Hub to enrich findings with threat intelligence data?","Amazon GuardDuty","AWS CloudTrail","AWS Config","AWS IAM","Amazon GuardDuty can be integrated with Security Hub to enrich findings with threat intelligence data, providing additional context and helping you identify potentially malicious activity."
"What is the primary reason for using AWS Security Hub alongside other security services like AWS GuardDuty and AWS Inspector?","To provide a centralised view of security findings from multiple sources","To reduce the cost of security monitoring","To increase the performance of security tools","To automate the deployment of security tools","The primary reason for using Security Hub alongside other security services is to provide a centralised view of security findings from multiple sources, simplifying security management and improving visibility."
"What type of remediation actions can be initiated directly from AWS Security Hub?","Custom actions can be configured to trigger remediation workflows","Security Hub automatically remediates all findings","Security Hub can only generate reports and alerts, not initiate remediation","Remediation actions must be performed manually in other AWS services","Custom actions can be configured in Security Hub to trigger remediation workflows, such as running an AWS Systems Manager Automation document, when a finding matches certain criteria."
"How can you integrate AWS Security Hub with your existing ticketing system?","By creating a custom action to send findings to the ticketing system","By manually creating tickets for each finding","By using AWS CloudWatch Events to forward findings to the ticketing system","Security Hub does not integrate with ticketing systems","You can integrate Security Hub with your existing ticketing system by creating a custom action that sends findings to the ticketing system when a specific event occurs or a finding matches certain criteria."
"In AWS Security Hub, what is the primary purpose of a finding?",To highlight a potential security issue or vulnerability,To automate security patching,To configure network access control lists,To manage IAM permissions,"Findings in Security Hub represent potential security issues, vulnerabilities, or deviations from security best practices detected in your AWS environment."
Which AWS service does Security Hub integrate with to provide vulnerability scanning of EC2 instances?,Amazon Inspector,AWS Shield,Amazon GuardDuty,AWS Config,Amazon Inspector is the service that Security Hub integrates with to provide vulnerability scanning for EC2 instances and container images.
What is the function of custom actions in AWS Security Hub?,To automate remediation steps based on findings,To create custom dashboards,To define custom compliance standards,To block specific IP addresses,"Custom actions in Security Hub allow you to send findings to other AWS services for investigation or remediation, automating responses to security events."
Which of the following is NOT a security standard supported by AWS Security Hub?,CIS AWS Foundations Benchmark,PCI DSS,NIST Cybersecurity Framework,ISO 27001,"ISO 27001 is not directly supported as a built-in standard in Security Hub, although custom actions can be created to integrate with it."
How does AWS Security Hub help with compliance?,By automatically fixing non-compliant configurations,By providing a centralised view of compliance status across supported standards,By encrypting data at rest,By preventing DDoS attacks,Security Hub helps with compliance by providing a consolidated view of compliance status against supported security standards and best practices.
You are using AWS Security Hub and want to automatically send findings to a ticketing system. What feature would you use?,Custom Actions,Insights,Filters,Standards,"Custom actions allow you to send findings to other AWS services, including ticketing systems or other security tools, for further investigation or remediation."
What is the purpose of Insights in AWS Security Hub?,To identify trends and patterns in security findings,To define custom security standards,To encrypt data in transit,To manage IAM roles,"Insights in Security Hub help you identify trends and patterns in your security findings, allowing you to focus on the most critical issues."
What is the AWS Security Hub's 'Control Finding Aggregation' setting used for?,To suppress duplicate findings from multiple regions,To aggregate findings based on severity level,To control the data retention period for findings,To enable or disable specific security standards,Control Finding Aggregation setting is used to suppress the duplicate findings received from multiple regions.
"If you want to view security findings from multiple AWS accounts in a single Security Hub console, what do you need to configure?",Cross-account aggregation,Multi-Region aggregation,VPC peering,IAM cross-account roles,Cross-account aggregation allows you to aggregate findings from multiple AWS accounts into a single Security Hub console in a designated aggregator account.
What is the correct approach to programmatically interact with and retrieve findings from AWS Security Hub?,Use the Security Hub API,Use AWS CloudTrail logs,Use AWS Config rules,Use AWS Trusted Advisor,"The Security Hub API allows you to programmatically interact with Security Hub, retrieve findings, and automate security workflows."
"In AWS Security Hub, what is the primary function of an 'insight'?",To provide a focused view of security findings based on specific criteria,To automatically remediate security vulnerabilities,To configure AWS WAF rules,To manage IAM roles and policies,"Insights in Security Hub are designed to provide a focused view of security findings, helping security teams quickly identify and address the most critical issues."
Which AWS service is directly integrated with AWS Security Hub to provide vulnerability scanning for EC2 instances and container images?,Amazon Inspector,Amazon GuardDuty,AWS Shield,AWS Config,"Amazon Inspector integrates with Security Hub to send its findings, providing vulnerability assessments for EC2 instances and container images."
"When using AWS Security Hub, what does the term 'finding' typically refer to?",A potential security issue identified by a security check or service,A historical record of security events,A configuration setting for a security service,A set of pre-defined security standards,A 'finding' in Security Hub represents a potential security issue or vulnerability that has been identified by a security check or integrated service.
You want to ensure that AWS Security Hub is enabled across all accounts in your AWS Organization. Which approach is the MOST efficient?,Enable Security Hub in the management account and delegate administrator accounts to manage member accounts.,Manually enable Security Hub in each account within the organisation.,Use AWS CloudFormation StackSets to deploy Security Hub configurations to each account.,Configure AWS Config to automatically enable Security Hub in new accounts.,Enabling Security Hub in the management account and designating delegated administrator accounts simplifies management and ensures consistent security posture across the entire organisation. The delegated administrator can then manage Security Hub settings for all member accounts.
What is the primary purpose of the AWS Security Hub's 'security standards' feature?,To provide pre-defined sets of security best practices and rules for compliance,To create custom security policies for your AWS environment,To manage encryption keys,To configure network access control lists (ACLs),"Security standards in Security Hub provide pre-defined sets of security best practices and rules based on industry standards and regulatory frameworks, helping users assess and improve their security posture."
"Within AWS Security Hub, which AWS service can be integrated to gain insights into potentially malicious or unauthorised behaviour in your AWS environment?",Amazon GuardDuty,AWS CloudTrail,AWS Config,Amazon Inspector,Amazon GuardDuty integrates with Security Hub and provides findings related to potentially malicious activity and unauthorised behaviour detected in your AWS environment.
Which AWS service can be used to automatically remediate findings within AWS Security Hub?,AWS Systems Manager,AWS CloudWatch Events,AWS Lambda,Amazon CloudFront,"AWS Systems Manager can be used to automatically remediate certain findings within Security Hub through its automation capabilities, such as running scripts or patching systems."
Which of the following actions CANNOT be performed directly from the AWS Security Hub console?,Create new IAM roles,Suppress findings,Investigate findings from integrated services,View compliance status against security standards,"Security Hub allows viewing compliance status, suppressing findings, and investigating findings. Creating IAM roles is managed through IAM."
You need to export the security findings from AWS Security Hub for compliance reporting. What is the RECOMMENDED way to accomplish this?,Use the Security Hub API to retrieve the findings and format them as needed.,Take screenshots of the findings in the Security Hub console.,Manually copy and paste the findings into a spreadsheet.,Download a PDF report from the Security Hub console.,"The Security Hub API allows for programmatic retrieval of findings, enabling flexible formatting and integration with reporting tools. This is the most scalable and maintainable approach."
Which compliance standard is NOT natively supported as a security standard within AWS Security Hub?,CIS AWS Foundations Benchmark,Payment Card Industry Data Security Standard (PCI DSS),National Institute of Standards and Technology (NIST) Cybersecurity Framework,HIPAA,"While Security Hub provides a framework to assess compliance with HIPAA, it's not offered as a pre-defined security standard like CIS, PCI DSS, and NIST."