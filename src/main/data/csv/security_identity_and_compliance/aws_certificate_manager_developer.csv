"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS Certificate Manager (ACM)?","Provisioning, managing, and deploying SSL/TLS certificates","Managing IAM users and roles","Monitoring network traffic","Managing EC2 instances","ACM simplifies the process of obtaining, renewing, and deploying SSL/TLS certificates for use with AWS services and your internal connected resources."
"Which type of SSL/TLS certificate can ACM provision?","Public and private certificates","Only public certificates","Only private certificates","Self-signed certificates only","ACM can provision both public certificates, trusted by public browsers, and private certificates for internal use."
"How can you request a public SSL/TLS certificate using AWS Certificate Manager (ACM)?","Through the AWS Management Console or AWS CLI","Only through the AWS CLI","Only through the AWS Management Console","By submitting a request to AWS Support","ACM offers the flexibility to request certificates either through the console or the command line interface."
"Where can ACM-managed public certificates be directly deployed?","Elastic Load Balancers, Amazon CloudFront distributions, and API Gateway endpoints","EC2 instances, S3 buckets, and RDS instances","Lambda functions, DynamoDB tables, and CloudWatch dashboards","VPC networks, Route 53 records, and IAM roles","ACM-managed certificates integrate directly with ELB, CloudFront, and API Gateway, simplifying the deployment process."
"What is the process ACM uses to automatically renew public SSL/TLS certificates?","ACM validates domain ownership before renewal","ACM requires manual renewal of all certificates","ACM automatically renews without domain validation","ACM sends renewal reminders via email","ACM automatically renews certificates, validated to ensure the domain ownership is still valid, as long as they're in use with integrated AWS services."
"What is required to use an ACM-managed private certificate?","An ACM Private CA","A third-party CA","An IAM role","An S3 bucket","To use private certificates provisioned by ACM, you need an ACM Private Certificate Authority to issue those certificates."
"Which domain validation method does ACM support for public certificates?","Email validation and DNS validation","Only email validation","Only DNS validation","File-based validation","ACM supports both email and DNS validation methods for proving domain ownership when requesting a public certificate."
"What is the cost associated with using AWS Certificate Manager (ACM) to provision public SSL/TLS certificates?","Public SSL/TLS certificates provisioned through ACM are free of charge","ACM charges a monthly fee for certificate management","ACM charges per certificate issued","ACM charges for certificate renewal","ACM offers free public SSL/TLS certificates when used with integrated AWS services such as ELB, CloudFront, and API Gateway."
"What happens when an ACM-managed certificate is close to expiry?","ACM attempts to automatically renew the certificate if it's in use with integrated services","ACM disables the services using the certificate","ACM sends a notification to AWS support","ACM deletes the certificate","ACM automatically attempts to renew the certificate if its in use with integrated services before it expires."
"How can you import a third-party SSL/TLS certificate into AWS Certificate Manager (ACM)?","Using the AWS Management Console or AWS CLI","Only using the AWS CLI","Only using the AWS Management Console","By sending the certificate to AWS Support","ACM allows you to import certificates from third-party providers, either via the console or the command line interface."
"What is the purpose of ACM Private CA?","To create and manage private SSL/TLS certificates for internal resources","To manage public SSL/TLS certificates","To monitor network traffic","To manage IAM policies","ACM Private CA allows you to create and manage your own private certificate authority for issuing certificates within your organisation."
"What is the validity period for certificates issued by ACM Private CA by default?","10 years","1 year","90 days","30 days","The default validity period for certificates issued by ACM Private CA is 10 years, however you can configure shorter validity periods."
"How are ACM Private CA certificates renewed?","Automatic renewal through ACM if the certificate is in use with integrated AWS services or manual renewal","Manual renewal by the user only","Automatic renewal by ACM regardless of usage","Renewal through a third-party certificate authority","Private certificates can be automatically renewed if they're configured to be used by other integrated AWS services, or manually renewed."
"Which of the following AWS services can integrate with ACM Private CA to automate certificate issuance?","AWS CloudFormation and AWS CodePipeline","Amazon S3 and Amazon EC2","Amazon DynamoDB and Amazon Lambda","Amazon VPC and Amazon Route 53","ACM Private CA can integrate with services like AWS CloudFormation to automate the issuance of certificates as part of infrastructure deployments."
"What is the benefit of using ACM Private CA over self-signed certificates for internal applications?","Private CA certificates are trusted within your organisation without manual configuration on each client","Self-signed certificates are more secure","Self-signed certificates are easier to manage","Self-signed certificates are automatically renewed","ACM Private CA certificates provide a trusted and manageable solution for internal SSL/TLS, eliminating the need to manually install certificates on each device."
"How does ACM integrate with CloudFront?","ACM provides SSL/TLS certificates for securing connections between viewers and CloudFront","ACM manages CloudFront distributions","ACM monitors CloudFront performance","ACM provides CDN content","ACM provides the necessary SSL/TLS certificates to secure connections between your viewers and your CloudFront distributions."
"What does DNS validation involve when requesting an ACM certificate?","Creating a CNAME record in your DNS configuration","Creating an A record in your DNS configuration","Creating an MX record in your DNS configuration","Creating an TXT record in your DNS configuration","DNS validation requires you to add a CNAME record to your DNS configuration to prove ownership of the domain."
"What happens to an ACM certificate after it's imported?","ACM cannot renew an imported certificate","ACM automatically renews the imported certificate","ACM sends reminders for manual renewal of the imported certificate","ACM replaces the imported certificate with a new one","ACM cannot automatically renew imported certificates. You must manually renew them and re-import them before they expire."
"Can you use ACM to manage certificates for on-premises servers?","Yes, you can export ACM certificates for use on on-premises servers","No, ACM only works with AWS services","Yes, ACM can directly deploy certificates to on-premises servers","Only if the on-premises server is connected to a VPC","You can export ACM-managed private certificates for use on on-premises servers, but you cannot export public certificates."
"What is the process for revoking an ACM certificate?","Revocation is only possible for ACM Private CA certificates, and is done via the AWS console or API.","ACM automatically revokes compromised certificates","ACM does not support certificate revocation","You must contact AWS Support to revoke a certificate","Certificate revocation is only supported and possible for ACM Private CA certificates."
"What is the default key algorithm used by ACM for newly provisioned certificates?","RSA","DSA","ECDSA","SHA-256","ACM supports RSA and ECDSA. RSA is the default algorithm."
"How does ACM help in meeting compliance requirements?","By ensuring secure SSL/TLS communication and simplifying certificate management","By managing IAM users and roles","By monitoring network traffic","By providing auditing tools","ACM helps in meeting compliance requirements by ensuring secure SSL/TLS communication and simplifying the management of certificates."
"What is the purpose of certificate chaining in the context of ACM Private CA?","To establish trust from a root CA to subordinate CAs","To encrypt data in transit","To manage IAM policies","To monitor network traffic","Certificate chaining in ACM Private CA involves establishing a hierarchy of trust from a root CA to subordinate CAs."
"How does ACM help prevent man-in-the-middle attacks?","By encrypting communication between clients and servers using SSL/TLS","By managing IAM policies","By monitoring network traffic","By blocking suspicious IP addresses","ACM helps prevent man-in-the-middle attacks by ensuring encrypted communication between clients and servers using SSL/TLS."
"What is the maximum number of ACM Private CA that can be created per AWS account?","Five","One","Unlimited","Ten","An AWS account is limited to a maximum of five private CAs."
"Which AWS service is best suited for distributing public keys associated with ACM issued certificates?","AWS Systems Manager Parameter Store","AWS Key Management Service (KMS)","AWS CloudHSM","AWS IAM","Public keys associated with ACM issued certificates can be distributed through AWS Systems Manager Parameter Store."
"What is the benefit of using ACM with Elastic Load Balancer (ELB)?","It automates the process of SSL/TLS certificate installation and renewal on the ELB","It reduces the cost of ELB","It monitors ELB performance","It automatically scales ELB capacity","ACM simplifies and automates the process of installing and renewing SSL/TLS certificates on Elastic Load Balancers."
"Which of the following security best practices should be followed when using ACM?","Restrict access to ACM using IAM policies","Share ACM certificates publicly to ensure maximum compatibility","Disable automatic certificate renewal to avoid unexpected charges","Use weak encryption algorithms for ACM certificates to improve performance","Restrict access to ACM using IAM policies to prevent unauthorized modification or deletion of certificates."
"Which AWS CLI command is used to request a certificate using AWS Certificate Manager?","aws acm request-certificate","aws acm create-certificate","aws acm issue-certificate","aws acm get-certificate","The correct AWS CLI command to request a certificate using AWS Certificate Manager is `aws acm request-certificate`."
"What is the role of Certificate Transparency logs in the context of ACM?","To provide a public record of certificate issuance","To encrypt certificate data","To manage certificate revocation","To monitor network traffic","Certificate Transparency logs provide a public record of certificate issuance, enhancing security and accountability."
"How does ACM support wildcard certificates?","ACM allows you to request wildcard certificates to secure multiple subdomains","ACM only supports certificates for specific domain names","ACM automatically converts domain certificates into wildcard certificates","ACM does not support wildcard certificates","ACM supports wildcard certificates, which can secure multiple subdomains of a domain with a single certificate."
"When requesting a certificate from ACM, what information is required?","Domain name(s)","IAM role ARN","Public IP address","AWS account ID","The most basic required information when requesting a certificate from ACM is the domain name(s) for which the certificate will be valid."
"What type of encryption is used by ACM to protect certificate private keys at rest?","AES-256","RSA","DES","MD5","ACM uses AES-256 encryption to protect certificate private keys at rest."
"Which AWS service can be used to log all ACM API calls made in your AWS account?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail is used to log all ACM API calls made in your AWS account for auditing and compliance purposes."
"How can you control access to ACM resources?","Using IAM policies","Using VPC security groups","Using Network ACLs","Using Route 53 policies","IAM policies are used to control access to ACM resources, allowing you to define who can create, import, delete, or view certificates."
"Which action cannot be performed on an ACM certificate once it has been issued?","Change the domain names associated with the certificate","Export the private key of a public ACM certificate","Renew the certificate","View the certificate details","You cannot export the private key of a public ACM certificate issued by ACM."
"What happens if domain validation fails for an ACM certificate request?","The certificate request is rejected","The certificate is issued with a warning","The certificate is issued with limited functionality","The certificate is automatically re-validated","If domain validation fails for an ACM certificate request, the request is rejected, and a certificate is not issued."
"How long are ACM certificates valid for?","13 months","6 months","2 years","5 years","ACM certificates are valid for 13 months."
"How does ACM ensure high availability of certificates?","By storing certificates in multiple AWS regions","By distributing certificates across multiple Availability Zones","By replicating certificates across multiple accounts","By using a single, highly resilient certificate authority","ACM ensures high availability of certificates by storing them in multiple AWS regions."
"What is the relationship between ACM and Key Management Service (KMS)?","ACM uses KMS to protect private keys","ACM manages KMS keys","KMS manages ACM certificates","ACM and KMS are completely independent services","ACM uses KMS to protect the private keys associated with the certificates it manages."
"What is the purpose of the 'extendedKeyUsage' field in an ACM certificate?","To specify the intended use of the certificate","To specify the domain names the certificate is valid for","To specify the encryption algorithm used by the certificate","To specify the issuer of the certificate","The 'extendedKeyUsage' field in an ACM certificate specifies the intended use of the certificate, such as server authentication or client authentication."
"Which security measure can you implement to restrict ACM Private CA certificate issuance to only approved AWS accounts?","Resource-based policies","IAM roles","VPC endpoints","AWS Firewall Manager","Resource-based policies provide granular control over access to ACM Private CA, enabling restriction of certificate issuance to approved AWS accounts."
"What is the benefit of integrating AWS Certificate Manager with AWS CloudFormation?","Automates certificate provisioning as part of infrastructure deployment","Automates CloudFormation template creation","Automates cost optimization of CloudFormation stacks","Automates security patching of CloudFormation resources","Integrating AWS Certificate Manager with AWS CloudFormation enables automated provisioning of SSL/TLS certificates during infrastructure deployment."
"What should you configure to receive notifications about ACM certificate expiration or renewal failures?","Amazon CloudWatch Events","AWS Config Rules","AWS Trusted Advisor","Amazon Inspector","You should configure Amazon CloudWatch Events (now Amazon EventBridge) to receive notifications about ACM certificate expiration or renewal failures."
"What is the difference between a certificate signing request (CSR) generated by ACM and one generated externally?","ACM doesn't allow you to export the private key so you cannot generate a CSR externally","ACM-generated CSRs are longer","ACM-generated CSRs are shorter","There is no difference","When you request ACM to provide a certificate it will create the certificate, which you cannot do when using a third party issuer as you cannot export the private key."
"What is one of the primary benefits of using ACM over manually managing SSL/TLS certificates?","Automated certificate lifecycle management","Greater customisation options","Lower certificate prices","Increased security","One of the primary benefits of using ACM is its automated certificate lifecycle management, including renewal and deployment."
"What is a certificate chain in the context of SSL/TLS?","A hierarchical list of certificates used to verify the authenticity of a certificate","A list of domain names secured by a single certificate","A list of encryption algorithms used by a certificate","A list of IP addresses associated with a domain name","A certificate chain is a hierarchical list of certificates used to establish trust from a root certificate authority to the certificate presented by a server."
"What is the purpose of using a Subject Alternative Name (SAN) in an ACM certificate?","To specify additional domain names or subdomains the certificate should secure","To specify the certificate's expiration date","To specify the issuing certificate authority","To specify the encryption algorithm used","A Subject Alternative Name (SAN) allows a single certificate to secure multiple domain names or subdomains."
"In AWS Certificate Manager (ACM), what is the primary use case for requesting a public certificate?","Securing public-facing websites and applications","Securing internal applications only","Encrypting data at rest in S3","Authenticating users in IAM","ACM public certificates are primarily used to secure public-facing websites and applications, ensuring secure communication over HTTPS."
"How does AWS Certificate Manager (ACM) validate domain ownership when requesting a certificate?","Using DNS records or email validation","By checking the server's IP address","By verifying the AWS account details","By pinging the domain","ACM validates domain ownership by requiring you to add specific DNS records to your domain's DNS configuration or by responding to an email sent to the domain's registered contacts."
"What is the benefit of using AWS Certificate Manager (ACM) for certificate management instead of managing certificates manually?","Automated renewal and deployment","Manual certificate renewal is still required","Lower cost for single certificates","Increased control over certificate content","ACM automates the renewal and deployment of SSL/TLS certificates, reducing the operational burden of manual certificate management."
"Which AWS service can be directly integrated with AWS Certificate Manager (ACM) for deploying certificates?","Elastic Load Balancer (ELB)","Amazon SQS","AWS Lambda","Amazon EC2","ACM integrates directly with Elastic Load Balancer (ELB) to deploy certificates, enabling secure HTTPS connections for your applications."
"What type of encryption does AWS Certificate Manager (ACM) use for the private keys of the certificates it manages?","Hardware Security Modules (HSMs)","Software-based encryption only","No encryption","User-provided encryption","ACM uses Hardware Security Modules (HSMs) to protect the private keys of the certificates it manages, providing a high level of security."
"In AWS Certificate Manager (ACM), what is the difference between a public certificate and a private certificate?","Public certificates are trusted by default; private certificates require trust to be established","Public certificates are free; private certificates are always paid","Public certificates are stored locally; private certificates are stored in the cloud","Public certificates are only used for internal applications; private certificates are for public websites","Public certificates are trusted by default by browsers and other clients, while private certificates are issued by your own private CA and require trust to be explicitly established."
"What is the purpose of using AWS Certificate Manager Private Certificate Authority (ACM PCA)?","To create and manage private certificates for internal use","To manage public certificates only","To manage SSL/TLS certificates for AWS services","To monitor certificate expiration dates","ACM PCA allows you to create and manage private certificates for internal use, such as securing communication between services within your organisation."
"How does AWS Certificate Manager (ACM) integrate with AWS CloudFormation?","To automate the provisioning of certificates","To monitor the health of ACM service","To manage IAM roles","To track billing for ACM services","ACM integrates with CloudFormation to automate the provisioning of certificates as part of your infrastructure-as-code deployments."
"What is the main advantage of using AWS Certificate Manager (ACM) with Elastic Load Balancing (ELB)?","Simplified SSL/TLS configuration and management","Increased ELB throughput","Reduced ELB costs","Improved ELB security","ACM simplifies the SSL/TLS configuration and management for ELB, making it easier to secure your applications behind load balancers."
"If your AWS Certificate Manager (ACM) certificate fails domain validation, what is the most likely reason?","Incorrect DNS records or failure to respond to email","Expired certificate","ACM service outage","Incorrect IAM permissions","Incorrect DNS records or failure to respond to the validation email are the most common reasons for ACM certificate domain validation failures."
"When importing a certificate into AWS Certificate Manager (ACM), what is the required format for the private key?","PEM encoded","DER encoded","PKCS#12","Base64 encoded","The private key must be in PEM encoded format when importing a certificate into ACM."
"With AWS Certificate Manager (ACM), what happens if you don't renew a certificate before it expires?","The website becomes inaccessible over HTTPS","The certificate is automatically renewed without intervention","You receive a warning in the AWS console, but the website remains accessible","ACM automatically switches to a self-signed certificate","If you don't renew a certificate before it expires, the website will become inaccessible over HTTPS because the browser will no longer trust the certificate."
"Which AWS Certificate Manager (ACM) feature can help you discover and manage certificates issued by public CAs and ACM PCA in your AWS account?","ACM Certificate Manager Console","AWS Trusted Advisor","AWS CloudTrail","AWS Config","The ACM Certificate Manager console allows you to discover and manage certificates issued by public CAs and ACM PCA in your AWS account."
"What is the purpose of the 'Certificate Transparency' feature in AWS Certificate Manager (ACM)?","To provide public logs of issued certificates","To encrypt certificate data","To provide certificate revocation status","To limit certificate usage","Certificate Transparency provides public logs of issued certificates, helping to detect mis-issued or fraudulent certificates."
"Which AWS Certificate Manager (ACM) feature allows you to issue certificates valid for multiple subdomain levels with a single certificate?","Wildcard certificates","Multi-domain certificates","Extended Validation (EV) certificates","Self-signed certificates","Wildcard certificates allow you to issue certificates valid for multiple subdomain levels (e.g., *.example.com) with a single certificate."
"In AWS Certificate Manager (ACM), how can you ensure that your certificate remains valid when switching from HTTP to HTTPS?","By using an ACM-managed certificate","By configuring HTTP redirects","By disabling HTTP access","By using a self-signed certificate","Using an ACM-managed certificate automates the renewal process, ensuring your certificate remains valid when switching to HTTPS."
"How does AWS Certificate Manager (ACM) help in meeting compliance requirements?","By automating certificate management and encryption","By providing network security groups","By managing IAM policies","By scanning for vulnerabilities","ACM helps meet compliance requirements by automating certificate management and ensuring proper encryption of data in transit."
"When requesting a certificate in AWS Certificate Manager (ACM), which validation method is recommended if you have DNS access?","DNS validation","Email validation","IP Address validation","File validation","DNS validation is recommended if you have DNS access because it allows ACM to automatically renew the certificate without manual intervention."
"What is the advantage of using AWS Certificate Manager (ACM) over self-signed certificates for public-facing websites?","ACM certificates are trusted by browsers and clients","Self-signed certificates are more secure","Self-signed certificates are easier to manage","ACM certificates offer lower encryption levels","ACM certificates are trusted by browsers and clients, eliminating the need for users to manually trust the certificate."
"Which AWS service is required for using ACM-managed certificates for a website hosted on Amazon S3?","Amazon CloudFront","Amazon EC2","Amazon Route 53","Amazon VPC","Amazon CloudFront is required to use ACM-managed certificates for a website hosted on S3. CloudFront acts as a CDN and supports HTTPS with ACM."
"What is the purpose of the AWS Certificate Manager (ACM) 'Import Certificate' feature?","To import existing certificates from other providers","To export certificates to other AWS services","To generate self-signed certificates","To renew existing certificates","The 'Import Certificate' feature allows you to import existing certificates obtained from other providers into ACM for centralised management."
"When using AWS Certificate Manager (ACM), what type of key algorithm is supported for the certificate?","RSA and Elliptic Curve Cryptography (ECC)","DES","MD5","SHA-1","ACM supports RSA and Elliptic Curve Cryptography (ECC) key algorithms for the certificate."
"What is the default validity period for certificates issued by AWS Certificate Manager Private CA (ACM PCA)?","13 months","30 days","3 months","5 years","The default validity period for certificates issued by ACM PCA is 13 months, but it can be customised."
"How does AWS Certificate Manager (ACM) support the principle of least privilege?","By integrating with IAM roles and policies","By disabling certificate renewal","By allowing unrestricted access to certificates","By storing certificates in plain text","ACM supports the principle of least privilege by integrating with IAM roles and policies, allowing you to control who has access to manage certificates."
"You need to issue certificates to your EC2 instances without public internet access. Which ACM feature should you use?","ACM Private CA","ACM public certificates","ACM managed renewal","ACM import certificate","ACM Private CA is designed for issuing certificates within your private network for EC2 instances without public internet access."
"What type of certificate is best suited for encrypting communication between internal microservices?","Private certificate from ACM PCA","Public certificate from ACM","Wildcard certificate","Self-signed certificate","A private certificate from ACM PCA is best suited for encrypting communication between internal microservices."
"Which of the following is a benefit of using AWS Certificate Manager (ACM) instead of Let's Encrypt for public websites?","Automated renewal and integration with AWS services","Lower cost for simple websites","Better support for wildcard certificates","Greater control over certificate details","ACM offers automated renewal and seamless integration with other AWS services like ELB, CloudFront, and API Gateway."
"What is the main purpose of the AWS Certificate Manager (ACM) 'Renewal Eligibility' status?","To indicate if a certificate is eligible for automatic renewal","To show if a certificate is about to expire","To display the domain validation status","To indicate if a certificate is being used","The 'Renewal Eligibility' status indicates whether ACM can automatically renew the certificate without manual intervention."
"How does AWS Certificate Manager (ACM) contribute to improved security posture?","By enforcing strong encryption and automating certificate management","By scanning for vulnerabilities in your application code","By managing network security groups","By providing DDoS protection","ACM contributes to improved security by ensuring strong encryption and automating the management of SSL/TLS certificates, reducing the risk of expired or misconfigured certificates."
"What is the purpose of Certificate Revocation Lists (CRLs) and Online Certificate Status Protocol (OCSP) stapling in the context of AWS Certificate Manager (ACM)?","To verify the revocation status of certificates","To encrypt certificate data","To validate domain ownership","To automate certificate renewal","CRLs and OCSP stapling are used to verify the revocation status of certificates, ensuring that clients don't trust compromised certificates."
"Which AWS service can you use to monitor the expiration of certificates managed by AWS Certificate Manager (ACM)?","AWS Certificate Manager Console and AWS CloudWatch","AWS IAM Console","AWS Trusted Advisor","AWS Config","You can use the ACM console and CloudWatch to monitor the expiration of certificates managed by ACM."
"When importing a certificate into AWS Certificate Manager (ACM), what information is required besides the certificate body and private key?","Certificate chain","Domain name","IP address","AWS account ID","The certificate chain (intermediate certificates) is required in addition to the certificate body and private key when importing a certificate into ACM."
"What is the main reason for using DNS validation over email validation in AWS Certificate Manager (ACM)?","DNS validation supports automated renewals","Email validation is more secure","DNS validation is faster","Email validation supports wildcard certificates","DNS validation supports automated renewals, meaning ACM can renew the certificate without manual intervention if DNS records are properly configured."
"How does AWS Certificate Manager (ACM) work with AWS CloudFront to deliver content securely?","ACM provides the SSL/TLS certificate for CloudFront distributions","ACM automatically configures CloudFront distributions","ACM monitors CloudFront performance","ACM routes traffic to CloudFront distributions","ACM provides the SSL/TLS certificate that CloudFront uses to secure content delivery via HTTPS."
"What is the difference between using AWS Certificate Manager (ACM) to issue a certificate and purchasing a certificate from a third-party Certificate Authority (CA)?","ACM automates the renewal process and integrates with AWS services","Third-party CAs offer lower prices","ACM certificates are less secure","Third-party CAs offer better customer support","ACM automates the renewal process and seamlessly integrates with AWS services, making it easier to manage certificates within the AWS ecosystem."
"You need to create a certificate that will be used for multiple domains owned by different AWS accounts. Which approach should you take?","Request a certificate in one account and share it with the others","Request separate certificates in each account","Use a wildcard certificate across all accounts","Create a private certificate authority in one account","You should request separate certificates in each account, as certificates are tied to the account in which they were issued."
"How can you ensure that an application is only accessible via HTTPS when using AWS Certificate Manager (ACM)?","Configure the load balancer to redirect HTTP traffic to HTTPS","Disable HTTP access at the server level","Use an ACM certificate","Configure IAM policies","The load balancer should be configured to redirect HTTP traffic to HTTPS, forcing all connections to use the secure protocol when using ACM."
"What is a key advantage of using ACM's managed renewal for SSL/TLS certificates?","Reduces operational overhead of certificate management","Eliminates the need for domain validation","Provides better security than self-signed certificates","Guarantees 100% uptime","Managed renewal reduces the operational overhead of manually renewing certificates, ensuring continuous HTTPS connectivity."
"In AWS Certificate Manager (ACM), what is the purpose of the 'Key Usage' and 'Extended Key Usage' extensions?","To define how the certificate can be used","To encrypt the private key","To validate the certificate","To specify the certificate's validity period","The 'Key Usage' and 'Extended Key Usage' extensions define how the certificate can be used, such as for server authentication or client authentication."
"How does AWS Certificate Manager (ACM) ensure the confidentiality of private keys?","Storing the private keys in HSMs","Encrypting private keys with a user-provided password","Storing the private keys in AWS Secrets Manager","Using software encryption only","ACM stores private keys in Hardware Security Modules (HSMs) for enhanced security and confidentiality."
"You are setting up an internal website that needs to be accessed securely. Which AWS Certificate Manager (ACM) service would be most suitable?","ACM Private CA","ACM Public Certificates","ACM Certificate Renewal","ACM Certificate Import","ACM Private CA is suitable for creating and managing private certificates for internal applications, ensuring secure communication."
"What role does Certificate Transparency (CT) play in improving the security of SSL/TLS certificates issued through AWS Certificate Manager (ACM)?","CT helps detect mis-issued certificates","CT provides certificate revocation lists","CT encrypts certificate data","CT accelerates certificate validation","Certificate Transparency helps detect mis-issued certificates by providing publicly auditable logs of issued certificates."
"Which AWS service is commonly used to distribute SSL/TLS certificates managed by AWS Certificate Manager (ACM) to a global audience?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","Amazon CloudFront, a content delivery network (CDN), is commonly used to distribute content and SSL/TLS certificates managed by ACM to a global audience."
"How does AWS Certificate Manager (ACM) simplify the process of deploying SSL/TLS certificates on Elastic Load Balancers (ELB)?","ACM integrates directly with ELB to automate certificate deployment","ACM requires manual configuration on ELB","ACM automatically configures ELB listeners","ACM replaces ELB's security groups","ACM integrates directly with ELB, allowing you to choose an ACM-managed certificate directly from the ELB configuration, simplifying the deployment process."
"What is the maximum validity period that AWS Certificate Manager Private CA (ACM PCA) allows for issued certificates?","Customisable, up to 100 years","13 months","5 years","10 years","ACM PCA allows you to customise the validity period of issued certificates, up to a maximum of 100 years."
"What happens to an expired SSL/TLS certificate managed by AWS Certificate Manager (ACM) if auto-renewal is not enabled?","The website or application will become inaccessible over HTTPS","ACM automatically replaces the expired certificate with a self-signed certificate","ACM sends a warning notification, but the certificate remains valid for a grace period","The certificate will be renewed manually by AWS","If auto-renewal is not enabled, the website or application will become inaccessible over HTTPS as browsers will no longer trust the expired certificate."
"Which is the MOST secure way to store an SSL/TLS certificate private key when using AWS Certificate Manager?","Using ACM's managed storage","Storing it in an S3 bucket","Storing it in plaintext on an EC2 instance","Storing it in AWS Secrets Manager","Using ACM's managed storage, which utilises Hardware Security Modules (HSMs), is the most secure way to store a private key."
"With AWS Certificate Manager (ACM), which type of certificates can you NOT import?","Certificates issued by a private CA that are already in use by another AWS service.","Self-signed certificates.","Certificates signed by a trusted public CA.","Certificates used for client authentication.","ACM doesn't allow importing certificates that are already managed by another AWS service to prevent conflicts and maintain centralised control."
"What is the primary benefit of using AWS Certificate Manager (ACM) to provision SSL/TLS certificates?","ACM provides free SSL/TLS certificates for services integrated with ACM.","ACM automatically configures your firewall settings.","ACM automatically configures your load balancer rules.","ACM allows you to use self-signed certificates for free.","ACM provides free SSL/TLS certificates for integrated services, reducing the cost and complexity of managing certificates."
"In AWS Certificate Manager (ACM), what is the purpose of a Certificate Authority (CA)?","To issue and manage digital certificates.","To store encrypted data.","To monitor network traffic.","To perform vulnerability scans.","A Certificate Authority (CA) is responsible for issuing and managing digital certificates, which are used to verify the identity of websites and servers."
"You're using AWS Certificate Manager (ACM) to manage SSL/TLS certificates for your website. What is the recommended method for proving that you own the domain?","DNS validation","Email validation using WHOIS information","HTTP validation using a specific file name","Manual approval by AWS support","DNS validation is recommended because it allows ACM to automatically renew certificates without manual intervention, and it's more reliable than email validation."
"How does AWS Certificate Manager (ACM) handle certificate renewals for certificates it manages?","ACM automatically renews certificates that are in use with integrated AWS services.","ACM requires manual renewal of all certificates.","ACM sends email reminders to renew certificates.","ACM only renews certificates purchased directly from Amazon.","ACM automatically renews certificates used with integrated AWS services, reducing the administrative burden of managing certificate lifecycles."
"Which AWS service can you use with AWS Certificate Manager (ACM) to automatically deploy SSL/TLS certificates to your load balancer?","Elastic Load Balancing (ELB)","Amazon EC2 Auto Scaling","Amazon CloudWatch","Amazon S3","ACM integrates with ELB, allowing you to easily deploy SSL/TLS certificates to your load balancer to secure traffic."
"What is the difference between AWS Certificate Manager (ACM) and AWS Certificate Manager Private Certificate Authority (ACM PCA)?","ACM is for public SSL/TLS certificates, while ACM PCA is for private certificates.","ACM is for private SSL/TLS certificates, while ACM PCA is for public certificates.","ACM manages all types of certificates, while ACM PCA only monitors certificate usage.","ACM handles certificate revocation, while ACM PCA handles certificate issuance.","ACM manages public SSL/TLS certificates for use with AWS services, while ACM PCA allows you to create and manage your own private certificate authority for internal use."
"You need to secure communication between your internal microservices using SSL/TLS certificates. Which AWS service is most suitable for issuing and managing these certificates?","AWS Certificate Manager Private Certificate Authority (ACM PCA)","AWS Certificate Manager (ACM)","Amazon Route 53","AWS Secrets Manager","ACM PCA allows you to create and manage your own private certificate authority, enabling you to issue and manage SSL/TLS certificates for internal use."
"You have a certificate issued by a third-party Certificate Authority (CA) and you want to use it with your Application Load Balancer (ALB). How can you import this certificate into AWS?","Import the certificate into AWS Certificate Manager (ACM).","Upload the certificate directly to the Application Load Balancer (ALB).","Store the certificate in Amazon S3 and configure the ALB to use it.","Store the certificate in AWS Secrets Manager and configure the ALB to use it.","Certificates from third-party CAs need to be imported into ACM before they can be used with other AWS services like Application Load Balancer."
"What information does AWS Certificate Manager (ACM) require when you request a public SSL/TLS certificate?","The domain name(s) you want to secure.","Your AWS account ID.","Your company's physical address.","Your credit card information.","When requesting a public SSL/TLS certificate, ACM needs the domain name(s) that the certificate will secure to validate ownership."
"What is the primary benefit of using DNS validation with AWS Certificate Manager (ACM)?","Automated certificate renewal","Faster certificate issuance","Lower cost compared to email validation","Enhanced security against man-in-the-middle attacks","DNS validation allows ACM to automatically renew your certificates without requiring manual intervention, which is a key benefit for long-term certificate management."
"Which AWS service CANNOT be directly integrated with AWS Certificate Manager (ACM) for certificate deployment?","Amazon EC2 instances without using Elastic Load Balancing (ELB) or CloudFront.","Elastic Load Balancing (ELB)","Amazon CloudFront","Amazon API Gateway","ACM cannot directly deploy certificates to standalone EC2 instances without using ELB or CloudFront as intermediaries. ACM integrates directly with services like ELB, CloudFront, and API Gateway."
"What happens to a certificate managed by AWS Certificate Manager (ACM) when it expires?","ACM automatically attempts to renew the certificate if it's in use with integrated AWS services.","The certificate is automatically revoked.","The certificate remains valid until manually revoked.","The certificate is automatically deleted from ACM.","ACM automatically attempts to renew certificates that are in use with integrated AWS services, ensuring continuous security."
"You have an existing SSL/TLS certificate that you want to import into AWS Certificate Manager (ACM). What format should the certificate and private key be in?","PEM format","DER format","PKCS#7 format","PKCS#12 format","ACM requires the certificate and private key to be in PEM format when importing an existing certificate."
"How can you use AWS Certificate Manager (ACM) to secure an Amazon CloudFront distribution?","Request or import a certificate in ACM and associate it with the CloudFront distribution.","Upload the certificate directly to the CloudFront distribution settings.","Configure CloudFront to use a certificate stored in Amazon S3.","Configure CloudFront to use a certificate stored in AWS Secrets Manager.","You can request or import a certificate in ACM and then associate it with your CloudFront distribution to secure communication."
"What is the purpose of certificate revocation in AWS Certificate Manager (ACM)?","To invalidate a certificate before its expiration date if it's compromised.","To extend the validity period of a certificate.","To change the domain name associated with a certificate.","To change the encryption algorithm used by a certificate.","Certificate revocation invalidates a certificate before its expiration date, typically because the certificate has been compromised or is no longer needed."
"What is a key consideration when choosing between AWS Certificate Manager (ACM) and Let's Encrypt for SSL/TLS certificates?","ACM provides automated certificate renewal for integrated AWS services, while Let's Encrypt requires manual configuration for renewal in some scenarios.","ACM offers certificates for a wider range of domains than Let's Encrypt.","ACM certificates are significantly cheaper than Let's Encrypt certificates.","ACM provides stronger encryption algorithms than Let's Encrypt.","ACM offers automated certificate renewal for services integrated with ACM, providing a seamless management experience compared to Let's Encrypt, where renewal automation may require more configuration."
"You need to create a private certificate authority (CA) in AWS for issuing certificates to your internal applications. Which AWS service should you use?","AWS Certificate Manager Private Certificate Authority (ACM PCA)","AWS Certificate Manager (ACM)","AWS Identity and Access Management (IAM)","AWS Key Management Service (KMS)","ACM PCA allows you to create and manage your own private CA within AWS, enabling you to issue certificates for internal applications."
"When using AWS Certificate Manager (ACM), what is the difference between requesting a certificate and importing a certificate?","Requesting a certificate involves ACM issuing a new certificate, while importing involves bringing an existing certificate into ACM for management.","Requesting a certificate involves using a private CA, while importing involves using a public CA.","Requesting a certificate is free, while importing a certificate incurs a cost.","Requesting a certificate requires DNS validation, while importing does not.","Requesting a certificate means ACM will generate and issue a new certificate for you. Importing a certificate means you already have a certificate from another source and you want ACM to manage it."
"What is the purpose of the Certificate Transparency (CT) logs that AWS Certificate Manager (ACM) uses for public certificates?","To provide a public record of all issued certificates to help detect mis-issued certificates.","To encrypt certificate data to protect it from unauthorized access.","To track the usage of certificates across different AWS services.","To verify the identity of certificate requesters.","Certificate Transparency (CT) logs provide a public record of issued certificates, which helps detect mis-issued certificates and improve overall security."
"You are migrating your on-premises application to AWS and need to use the same SSL/TLS certificate for your application running on EC2 instances behind a load balancer. How can you achieve this?","Import the certificate into AWS Certificate Manager (ACM) and associate it with the load balancer.","Upload the certificate directly to the EC2 instances and configure the load balancer to use it.","Store the certificate in Amazon S3 and configure both the EC2 instances and load balancer to use it.","Store the certificate in AWS Secrets Manager and configure both the EC2 instances and load balancer to use it.","The certificate needs to be imported into ACM so it can be easily and centrally managed and associated with the load balancer."
"What is the main advantage of using AWS Certificate Manager (ACM) for managing certificates used with Elastic Load Balancing (ELB)?","ACM automates the deployment and renewal of certificates on ELB.","ACM provides stronger encryption algorithms for ELB compared to other certificate providers.","ACM offers certificates for free, while other providers charge for them.","ACM allows you to use self-signed certificates with ELB without any security warnings.","ACM automates the deployment and renewal of certificates, simplifying the management of SSL/TLS certificates for ELB."
"When you request a certificate from AWS Certificate Manager (ACM), what is the initial status of the certificate?","Pending validation","Issued","Active","Revoked","After you request a certificate, its status is 'Pending validation' until you complete the validation process (either DNS or email)."
"You have multiple AWS accounts and want to use the same SSL/TLS certificate across different accounts. How can you achieve this with AWS Certificate Manager (ACM)?","Share the certificate using AWS Resource Access Manager (RAM).","Copy the certificate to each account manually.","Request the same certificate in each account.","Create a dedicated account for managing certificates and grant access to other accounts.","AWS Resource Access Manager (RAM) allows you to share ACM certificates with other AWS accounts, enabling centralized certificate management."
"What is the purpose of a wildcard certificate in AWS Certificate Manager (ACM)?","To secure multiple subdomains of a domain with a single certificate.","To secure multiple top-level domains with a single certificate.","To encrypt all traffic between AWS services.","To provide identity verification for users accessing your application.","A wildcard certificate secures multiple subdomains of a domain with a single certificate, simplifying certificate management."
"How can you monitor the expiration dates of SSL/TLS certificates managed by AWS Certificate Manager (ACM)?","Use Amazon CloudWatch metrics and alarms.","Check the ACM console manually.","Receive email notifications from ACM.","Use AWS Trusted Advisor.","Amazon CloudWatch can be used to monitor the expiration dates of ACM certificates and set up alarms to notify you before they expire."
"You need to ensure that your website uses the latest SSL/TLS protocols and ciphers supported by AWS Certificate Manager (ACM). How can you achieve this?","Use the default security policy provided by ACM for integrated services.","Manually configure the SSL/TLS protocols and ciphers in your web server configuration.","Regularly update the ACM certificate.","Use a custom security policy with specific SSL/TLS protocols and ciphers.","Using the default security policy provided by ACM ensures that your website uses the latest SSL/TLS protocols and ciphers supported by ACM for integrated services."
"You are using AWS Certificate Manager (ACM) to manage certificates for your website. After changing your DNS records, how long does it typically take for ACM to validate the certificate request using DNS validation?","Up to 72 hours","A few seconds","Up to 1 hour","Up to 24 hours","DNS propagation can take time, but ACM validation typically occurs within a few minutes to a few hours, but it can take up to 72 hours in some cases."
"Which statement is true regarding the cost of using AWS Certificate Manager (ACM) for public SSL/TLS certificates?","ACM certificates are free for use with integrated AWS services.","ACM charges a monthly fee for each certificate issued.","ACM certificates are only free for the first year.","ACM charges based on the number of requests to the certificate.","ACM certificates are free when used with integrated AWS services like ELB, CloudFront, and API Gateway."
"How does AWS Certificate Manager (ACM) help improve the security of your applications?","By automating certificate management and reducing the risk of expired certificates.","By providing built-in firewall protection.","By automatically patching security vulnerabilities in your applications.","By providing intrusion detection and prevention services.","ACM automates certificate management, which reduces the risk of expired certificates and helps maintain a secure connection."
"What is the maximum validity period for certificates issued by AWS Certificate Manager (ACM)?","13 months","3 months","2 years","5 years","Certificates issued by ACM have a maximum validity period of 13 months to promote better security practices by requiring more frequent rotation."
"You are using AWS Certificate Manager Private Certificate Authority (ACM PCA). What is the root of trust for the private certificates issued by your CA?","The private key of the root CA certificate that you control.","The AWS root certificate authority.","A hardware security module (HSM) managed by AWS.","A third-party certificate authority.","The root of trust for private certificates is the private key of the root CA certificate, which you control when using ACM PCA."
"What is the maximum number of domain names that can be included in a single SSL/TLS certificate requested from AWS Certificate Manager (ACM)?","100","5","50","250","You can include up to 100 domain names (including the base domain and subdomains) in a single certificate requested from ACM."
"What is the key difference between a regional ACM certificate and an ACM certificate for CloudFront?","ACM certificates for CloudFront must be requested in the US East (N. Virginia) Region.","Regional ACM certificates can only be used with Elastic Load Balancers.","ACM certificates for CloudFront are automatically deployed globally.","Regional ACM certificates are cheaper than ACM certificates for CloudFront.","ACM certificates for CloudFront must be requested in the US East (N. Virginia) Region because CloudFront is a global service and requires certificates to be stored in this region."
"You are using AWS Certificate Manager (ACM) to manage SSL/TLS certificates for your website. Your website is experiencing intermittent connectivity issues. How can you verify that the ACM certificate is correctly configured on your load balancer?","Check the load balancer's listener configuration to ensure that the ACM certificate is selected.","Check the ACM console to ensure that the certificate status is 'Issued'.","Check the DNS records for your domain.","Check the CloudWatch metrics for your load balancer.","Verifying the load balancer's listener configuration is crucial to ensure that the correct ACM certificate is selected and associated with the listener, enabling secure connections."
"You need to revoke a compromised SSL/TLS certificate managed by AWS Certificate Manager (ACM). What steps should you take?","Use the ACM console or API to revoke the certificate.","Delete the certificate from ACM.","Contact AWS Support to revoke the certificate.","Update the certificate's DNS records.","You can use the ACM console or API to initiate the revocation process for a compromised certificate."
"What is the recommended best practice for managing SSL/TLS certificates for your applications running on AWS?","Use AWS Certificate Manager (ACM) to automate certificate management and renewal.","Manually manage certificates on each server.","Store certificates in Amazon S3.","Use self-signed certificates.","Using ACM automates the process, reduces manual effort, and helps ensure that certificates are always up-to-date and valid."
"What is the purpose of the 'Subject Alternative Name' (SAN) field in an SSL/TLS certificate managed by AWS Certificate Manager (ACM)?","To specify additional domain names or subdomains that the certificate is valid for.","To specify the organisation that owns the certificate.","To specify the encryption algorithm used by the certificate.","To specify the certificate's expiration date.","The SAN field allows a single certificate to be valid for multiple domain names or subdomains, providing flexibility and reducing the need for multiple certificates."
"When using AWS Certificate Manager Private Certificate Authority (ACM PCA), how do you control access to your private CA?","Using AWS Identity and Access Management (IAM) policies.","Using ACM PCA's built-in access control lists (ACLs).","Using Amazon S3 bucket policies.","Using AWS Secrets Manager.","Access to your private CA is controlled through IAM policies, allowing you to define who can create, manage, and issue certificates."
"What is the purpose of the certificate chain when using SSL/TLS certificates with AWS Certificate Manager (ACM)?","To establish trust between the certificate and the issuing Certificate Authority (CA).","To encrypt the certificate's private key.","To specify the domain names that the certificate is valid for.","To verify the identity of the certificate owner.","The certificate chain provides a hierarchy of trust, linking the certificate back to the root CA and establishing trust between the certificate and the issuing authority."
"You are deploying a website using Amazon S3 and CloudFront. How can you use AWS Certificate Manager (ACM) to secure the website with HTTPS?","Request or import a certificate in ACM and associate it with the CloudFront distribution.","Upload the certificate directly to the S3 bucket.","Configure S3 to redirect HTTP requests to HTTPS.","Enable HTTPS in the CloudFront distribution settings without a certificate.","ACM certificates must be associated with the CloudFront distribution to enable HTTPS for traffic to your website served from S3."
"You need to create a certificate that can be used for multiple levels of subdomains (e.g., *.example.com and *.sub.example.com). Which type of certificate should you request from AWS Certificate Manager (ACM)?","A single wildcard certificate for *.example.com","Two separate wildcard certificates, one for *.example.com and one for *.sub.example.com","A multi-domain certificate with all subdomains listed in the Subject Alternative Name (SAN) field","ACM does not support certificates for multiple levels of subdomains.","You would need two separate wildcard certificates, one for each level of subdomain as ACM issues certificates for one level of subdomain only."
"What is the best way to automate the process of requesting and deploying SSL/TLS certificates using AWS Certificate Manager (ACM)?","Use AWS CloudFormation or AWS SDKs to automate certificate requests and deployments.","Manually request and deploy certificates through the ACM console.","Use a third-party certificate management tool.","Use AWS Lambda functions to automate certificate requests.","Using CloudFormation or AWS SDKs allows you to fully automate the process, integrating certificate management into your infrastructure as code."
"When using AWS Certificate Manager (ACM), what is the role of the 'Request Certificate' action?","To request a new public or private SSL/TLS certificate from ACM.","To import an existing certificate into ACM.","To renew an existing certificate in ACM.","To delete a certificate from ACM.","The 'Request Certificate' action initiates the process of obtaining a new SSL/TLS certificate from ACM, either public or private."
"You are using AWS Certificate Manager (ACM) to manage SSL/TLS certificates for your website. Your website is accessible over both HTTP and HTTPS. What is the recommended approach to ensure that all traffic is encrypted?","Redirect all HTTP traffic to HTTPS using a load balancer or web server configuration.","Disable HTTP access to your website.","Configure ACM to automatically redirect HTTP traffic to HTTPS.","Use a Content Security Policy (CSP) header to enforce HTTPS.","Redirecting all HTTP traffic to HTTPS ensures that all communication between the client and server is encrypted."
"Which of the following statements is correct regarding the use of AWS Certificate Manager (ACM) with AWS Lambda?","ACM can be used to manage certificates for custom domain names used with API Gateway that invoke Lambda functions.","ACM can be directly integrated with Lambda functions to encrypt data in transit.","ACM is not required when using Lambda functions as AWS handles the encryption automatically.","ACM is only used for securing Lambda functions that are exposed through API Gateway.","ACM certificates are associated with API Gateway, which then invokes Lambda functions. ACM does not directly manage certificates for Lambda function code itself."
"You have a website hosted on Amazon EC2 instances and want to use AWS Certificate Manager (ACM) to secure it with HTTPS. What is the first step you should take?","Create an Elastic Load Balancer (ELB) in front of your EC2 instances.","Install the ACM agent on your EC2 instances.","Request an SSL/TLS certificate from ACM.","Configure the EC2 instances to use HTTPS.","You need an ELB to integrate the certificate. Then secure communication between clients and your application."
"What is the primary function of AWS Certificate Manager (ACM)?","Provisioning, managing, and deploying SSL/TLS certificates","Managing IAM users and roles","Monitoring application performance","Storing and retrieving secrets","ACM simplifies the process of obtaining, managing, and deploying SSL/TLS certificates for use with AWS services and your internal connected resources."
"Which type of certificate can ACM *not* directly provision?","Private Certificates for internal use","Public Certificates for external websites","Wildcard Certificates","Self-Signed Certificates","ACM Public certificates are used for your external websites. Private certificates are used for your internal connected resources."
"When using ACM to provision a public SSL/TLS certificate, how does ACM typically validate domain ownership?","By sending an email to the domain's registered contact","By automatically updating DNS records","By checking the server's IP address","By requiring a phone call to the domain registrar","ACM typically validates domain ownership using either email validation or DNS validation, allowing you to prove you control the domain you're requesting a certificate for."
"You want to use an SSL/TLS certificate with an Elastic Load Balancer (ELB). How can ACM simplify this process?","ACM integrates directly with ELB, allowing you to select certificates you've provisioned in ACM when configuring your ELB.","ACM provides a dedicated API for ELB certificate management only.","ACM can't be used with ELB.","ACM requires manual upload of certificates to ELB.","ACM integrates with other AWS services like ELB, making it easy to use your provisioned certificates without manual uploading or management."
"What is the benefit of using ACM's managed renewal for SSL/TLS certificates?","ACM automatically renews certificates before they expire, preventing downtime.","ACM automatically lowers certificate costs.","ACM automatically increases server security.","ACM automatically updates your website content.","ACM handles the renewal process automatically, ensuring your certificates are always up-to-date without manual intervention."
"How does ACM handle the deployment of certificates to AWS resources?","It automatically deploys certificates to integrated AWS resources.","It requires manual certificate deployment.","It only supports deployment to EC2 instances.","It only supports deployment to S3 buckets.","ACM is integrated with other AWS services and automatically deploys the certificate."
"You need to provision a certificate for an internal website that is *not* publicly accessible. Which type of certificate should you request using ACM?","Private certificate","Public certificate","Wildcard certificate","Self-signed certificate","For internal resources, a private certificate allows you to secure communication without relying on a public CA."
"What happens when an ACM-managed certificate is about to expire?","ACM automatically renews the certificate if the validation is still valid.","ACM automatically revokes the certificate.","ACM sends a notification to the AWS account owner to renew it manually.","ACM shuts down the associated AWS resources.","ACM handles renewals automatically as long as the domain validation is still valid and the service is integrated."
"When configuring a load balancer, which AWS service integrates directly with AWS Certificate Manager (ACM) to enable HTTPS?","Elastic Load Balancing (ELB)","Amazon EC2","Amazon S3","Amazon CloudWatch","Elastic Load Balancing (ELB) directly integrates with ACM allowing you to select certificates when configuring the load balancer's HTTPS listener."
"What is the cost associated with using ACM to provision public SSL/TLS certificates?","Public SSL/TLS certificates provisioned through ACM are free.","ACM charges a monthly fee per certificate.","ACM charges a per-request fee for certificate issuance.","ACM charges based on the size of the encrypted data.","ACM provides public SSL/TLS certificates at no cost, simplifying HTTPS configuration for AWS resources. You pay only for the AWS resources you use with the certificate."
"You have an existing SSL/TLS certificate issued by a third-party Certificate Authority (CA). Can you import it into ACM?","Yes, you can import certificates issued by other CAs into ACM.","No, ACM only supports certificates it provisions itself.","You can only import certificates issued by AWS Marketplace CAs.","You can only import certificates for internal use.","ACM allows you to import certificates issued by third-party CAs, providing a centralised management location."
"How does ACM assist with maintaining PCI DSS compliance for your web applications?","By simplifying the deployment and management of SSL/TLS certificates, which are required for secure data transmission.","By automatically auditing your application code for vulnerabilities.","By providing a firewall to protect against network attacks.","By managing user access controls.","Using ACM to easily implement and maintain SSL/TLS certificates helps ensure that your web applications comply with PCI DSS requirements for secure data transmission."
"What is the recommended method for requesting a certificate that covers multiple subdomains (e.g., *.example.com)?","Request a wildcard certificate","Request individual certificates for each subdomain","Use a SAN (Subject Alternative Name) certificate with all subdomains listed","Use a multi-domain certificate","A wildcard certificate covers all subdomains within a specified domain, simplifying certificate management."
"When requesting a public certificate in ACM, what information is required?","The domain name(s) the certificate should cover","The IP address of the server hosting the domain","The AWS account ID","The user's social security number","You need to provide the domain name(s) that the certificate will cover to identify which domain the certificate should secure."
"What is the benefit of using ACM with AWS CloudFront?","ACM can automatically deploy certificates to CloudFront distributions, enabling HTTPS for your content delivery network (CDN).","ACM can encrypt data stored in CloudFront.","ACM can compress data delivered through CloudFront.","ACM can cache data in CloudFront.","ACM simplifies the process of enabling HTTPS for your CloudFront distributions, providing secure content delivery."
"How can you use ACM to manage certificates for services running on Amazon EC2 instances?","You can import and manage certificates in ACM, then manually deploy them to your EC2 instances.","ACM automatically deploys certificates to EC2 instances.","ACM only supports certificate management for EC2 instances running Windows.","ACM cannot be used with EC2 instances directly.","While ACM doesn't automatically deploy to EC2, you can import certificates and then manually configure them on your EC2 instances."
"Which AWS service can you use to retrieve a private certificate issued by ACM for use on an EC2 instance?","AWS Secrets Manager","AWS Systems Manager Parameter Store","Amazon S3","Amazon CloudWatch Logs","AWS Secrets Manager can store, encrypt and retrieve the private keys."
"If domain ownership validation for ACM fails, what is the next step to troubleshoot?","Verify the DNS records or email address associated with the domain.","Contact AWS Support immediately.","Delete and recreate the certificate request.","Switch to a different AWS region.","The most common cause of validation failure is incorrect or missing DNS records or incorrect email addresses. Verifying this information is the first step."
"Which of the following is *not* a supported method for domain validation in ACM?","DNS validation","Email validation","File validation","HTTPS validation","ACM supports DNS validation and Email validation only."
"What is the maximum validity period for certificates issued by ACM?","13 months","1 year","2 years","5 years","ACM issues certificates with a validity period of 13 months."
"You are using ACM to manage certificates for your organisation. How can you restrict access to ACM functions?","Use IAM policies to control which users can create, import, or manage certificates.","ACM has no access control capabilities.","Use CloudTrail to monitor ACM activity.","Use VPC security groups to restrict network access.","IAM policies enable granular control over who can perform actions within ACM."
"Which of the following is the most secure way to automate certificate renewal using ACM?","ACM Managed Renewal","AWS Lambda function triggered by CloudWatch Events","Manual renewal via the ACM console","Custom script running on EC2","ACM Managed Renewal is the easiest and most secure as it automates the entire renewal process without exposing credentials or requiring custom code."
"You have multiple AWS accounts. Can you use ACM to centrally manage certificates across all accounts?","Yes, using AWS Certificate Manager Private CA with cross-account access.","No, ACM certificates are specific to the AWS account in which they are issued.","You can, but you need to create a separate ACM instance in each account.","Only if all accounts are part of the same AWS organisation.","With ACM Private CA and appropriate IAM policies, cross-account certificate management is possible."
"How does ACM help reduce the operational burden of managing SSL/TLS certificates?","By automating certificate renewal and deployment.","By automatically patching your operating systems.","By providing a centralised database for all your security logs.","By automatically backing up your EC2 instances.","ACM removes the manual effort of renewal and deployment by automating these tasks, reducing operational overhead."
"What happens to your ACM certificates when you terminate your AWS account?","All certificates are revoked.","All certificates are transferred to another AWS account.","All certificates are automatically downloaded to your local machine.","All certificates remain active.","Certificates are revoked when the account is terminated as there's no longer an active AWS environment associated with them."
"Can ACM issue certificates for internal domains that are not publicly resolvable?","Yes, using ACM Private CA","No, ACM only issues certificates for publicly resolvable domains.","Yes, by manually importing the certificate into ACM.","Yes, using the ACM CLI.","ACM Private CA allows you to create a private certificate authority and issue certificates for internal domains."
"You want to ensure that only authorised users can request certificates using ACM. Which AWS service should you use to control access?","IAM (Identity and Access Management)","AWS CloudTrail","AWS Config","Amazon Inspector","IAM allows you to create policies that grant specific permissions for ACM actions, such as requesting, importing, or deleting certificates."
"How does ACM assist in meeting compliance requirements for data encryption?","By providing and managing SSL/TLS certificates that enable encryption in transit.","By automatically encrypting data at rest in S3 buckets.","By auditing user access to sensitive data.","By detecting and preventing malware.","ACM helps you secure data in transit by providing and managing SSL/TLS certificates, a key component of many compliance standards."
"You need to deploy a certificate to an AWS Lambda function. What's the recommended approach using ACM?","You cannot directly deploy an ACM certificate to a Lambda function.","Import the certificate into the Lambda function's environment variables.","Use the AWS CLI to upload the certificate to the Lambda function.","Attach the certificate to an API Gateway endpoint that triggers the Lambda function.","ACM does not directly integrate with Lambda.  You need to use API Gateway, or manually manage the certificate outside of ACM."
"Which statement is true about the security of private keys managed by ACM?","ACM automatically protects the private keys of your certificates.","You are responsible for manually storing and securing the private keys.","ACM stores the private keys in plain text in S3.","ACM does not manage the private keys.","ACM is designed to protect the private keys used with your SSL/TLS certificates, using strong encryption and access controls."
"What is the purpose of the 'Request Certificate' action in AWS Certificate Manager?","To request a new SSL/TLS certificate from ACM","To renew an existing certificate","To import an existing certificate","To delete a certificate","The 'Request Certificate' action allows you to initiate the process of obtaining a new SSL/TLS certificate from AWS Certificate Manager (ACM) for use with your AWS services."
"When you request a public certificate from ACM, what happens if you do not validate domain ownership?","The certificate will not be issued.","The certificate will be issued, but with a warning.","The certificate will be issued, but with limited functionality.","The certificate will be issued, but will expire quickly.","Domain ownership validation is a mandatory step, ensuring that you have the right to use the certificate for the requested domain."
"What is the default key algorithm used when ACM generates a private key for a new certificate?","RSA","DSA","ECC","AES","ACM Defaults to RSA, which is one of the most common, and secure algorithms to create private keys with."
"Which AWS service primarily uses ACM for automatic certificate rotation and deployment on edge locations?","CloudFront","S3","EC2","RDS","CloudFront uses ACM to automatically deploy certificates to edge locations."
"Which of the following certificate types cannot be requested directly through the AWS Certificate Manager (ACM) console?","Self-Signed Certificates","Wildcard Certificates","Public Certificates","Private Certificates","Self-signed certificates are not supported within ACM and must be externally provisioned and imported."
"You have an ACM PCA set to expire in 30 days. What happens to the certificates issued by this PCA?","They will not be revoked, but new certificates cannot be issued.","All issued certificates are automatically revoked.","Certificates remain valid for the duration they were initially issued for.","ACM Automatically extends the PCA expiration date.","Existing certificates will remain valid, but it is best practice to create a new PCA and migrate the certificates to that PCA."
"What is the best practice for monitoring the expiration of ACM certificates?","Use AWS CloudWatch Events to trigger notifications.","Manually check the ACM console regularly.","Use AWS Trusted Advisor.","Use AWS Inspector.","CloudWatch events can be configured to trigger a notification when a certificate is about to expire, providing automated reminders."
"What is the purpose of the ACM Private CA service?","To create and manage a private certificate authority for issuing certificates within your organisation.","To provide pre-configured SSL/TLS certificates for common AWS services.","To automatically renew public SSL/TLS certificates.","To manage access control for SSL/TLS certificates.","ACM Private CA allows you to create a private CA hierarchy and issue certificates for internal resources that don't require public trust."
"When requesting a private certificate using ACM Private CA, what determines the certificate's validity period?","The configuration settings of your private CA.","The default settings of ACM.","The validity period of the root CA certificate.","The validity period specified in the certificate request.","The validity period is determined by the configuration of your ACM Private CA, allowing you to define the maximum lifespan of the issued certificates."
"Which protocol is used to retrieve a certificate from ACM to an EC2 instance?","AWS Secrets Manager API","HTTPS","SSH","FTP","ACM integrates with Secrets Manager to securely retrieve certificates which are made available over the AWS APIs."
"How do you enable automatic certificate renewal for a public certificate managed by ACM?","Automatic renewal is enabled by default for eligible certificates.","You must manually configure automatic renewal in the ACM console.","You must create a Lambda function to handle certificate renewal.","Automatic renewal is only available for private certificates.","If DNS or Email validation is successful, certificates are automatically renewed. No further configuration is needed."
"What is the primary benefit of integrating ACM with Amazon API Gateway?","To enable HTTPS endpoints for your APIs with managed SSL/TLS certificates.","To automatically scale your API Gateway endpoints.","To monitor the performance of your APIs.","To control access to your APIs.","ACM integration with API Gateway allows you to easily configure HTTPS for your APIs using managed certificates, simplifying the process of securing your API endpoints."
"Which domain validation method is generally preferred for automated certificate issuance and renewal in ACM?","DNS validation","Email validation","File validation","HTTPS validation","DNS validation allows ACM to automatically renew certificates without manual intervention, making it the preferred method for automation."
"What type of encryption does ACM use to protect private keys?","Hardware Security Modules (HSMs)","Software encryption","No encryption","Client-side encryption","Private keys are protected using HSMs."
"You need to create a certificate that is valid for both example.com and www.example.com. What type of certificate should you request?","A certificate with Subject Alternative Names (SANs)","A wildcard certificate","Two separate certificates, one for each domain","A self-signed certificate","Certificates with Subject Alternative Names (SANs) allow you to specify multiple domain names that the certificate should cover."
"You have a root CA and a subordinate CA created within ACM PCA. What is the next step to issue certificates?","Issue certificates from the subordinate CA","Import the root CA certificate into AWS Certificate Manager","Issue certificates from the root CA","Distribute the root CA public certificate to clients","Subordinate CAs are for certificate issuance and should not be performed from the root CA."