"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS IAM, what is the primary purpose of a Role?","To grant permissions to AWS services and applications","To store user credentials","To define network access control lists","To manage billing information","An IAM Role provides temporary security credentials for AWS services and applications to access other AWS resources."
"What is the purpose of an IAM policy's 'Principal' element?","Specifies the account, user, role, or service that is allowed or denied access to the resource.","Defines the actions that are allowed or denied.","Specifies the AWS region where the policy is effective.","Sets the expiration date for the policy.","The 'Principal' element identifies the entity that is being granted or denied permissions in the policy."
"Which AWS IAM feature allows you to grant temporary access to your AWS resources?","Security Token Service (STS)","IAM Groups","IAM Roles for EC2","IAM User","AWS Security Token Service (STS) enables you to request temporary, limited-privilege credentials for IAM users or for users that you authenticate (federated users)."
"What is the recommended best practice for managing AWS IAM users' access keys?","Rotate access keys regularly","Share access keys across multiple users","Embed access keys directly in application code","Store access keys in plain text files","Rotating access keys regularly minimizes the risk associated with compromised keys."
"In AWS IAM, what is the purpose of a Permission Boundary?","To set the maximum permissions that an IAM entity can have","To define the network perimeter for AWS resources","To encrypt data at rest","To enforce multi-factor authentication","A permission boundary is an advanced feature that lets you use a managed policy to set the maximum permissions that an identity-based policy can grant to an IAM entity (user or role)."
"Which AWS IAM component is a collection of permissions that define what a user or role can do in AWS?","Policy","Group","Role","User","An IAM Policy is a document that formally defines the permissions assigned to a user, group, or role. It determines what actions the identity can perform on which resources."
"What is the purpose of AWS IAM Access Analyzer?","To identify resources in your organisation that are shared with an external entity","To automatically apply security patches to EC2 instances","To detect and prevent DDoS attacks","To monitor network traffic for suspicious activity","IAM Access Analyzer helps you identify the resources in your organisation, such as S3 buckets or IAM roles, that are shared with an external entity. This lets you identify unintended access to your resources and data, which is a critical security concern."
"What is the purpose of using IAM Roles for EC2 instances?","To provide temporary security credentials for applications running on the instance.","To encrypt the root volume of the instance.","To define the network security group for the instance.","To store SSH keys for accessing the instance.","IAM roles provide temporary security credentials for applications running on EC2 instances to securely access other AWS services."
"Which of the following is a characteristic of AWS IAM groups?","IAM groups cannot be used to manage permissions for AWS services.","IAM groups grant permissions to all users in the AWS account.","IAM groups are primarily used for managing network configurations.","IAM groups simplify the management of permissions for multiple IAM users.","IAM groups are a collection of IAM users. Groups let you specify permissions for multiple users, which makes it easier to manage the permissions for those users."
"What type of policy is directly attached to a user, group, or role in AWS IAM?","Identity-based policy","Resource-based policy","Service control policy (SCP)","Session policy","Identity-based policies are attached to IAM identities (users, groups, and roles)."
"What type of AWS IAM policy controls access to a specific AWS resource?","Resource-based policy","Identity-based policy","Service control policy (SCP)","Session policy","Resource-based policies are attached to resources and specify who has access to the resource and what actions they can perform on it."
"What is the function of AWS IAM Service Control Policies (SCPs)?","To centrally manage permissions across multiple AWS accounts in an organisation","To define custom AWS service limits for an account","To monitor API calls made by IAM users","To enforce MFA for all IAM users","SCPs are a feature of AWS Organisations that enable you to centrally control the maximum available permissions for all accounts in your organisation."
"Which AWS IAM feature is used to grant users temporary access to resources based on their identity federation?","AWS Security Token Service (STS)","AWS CloudTrail","AWS Config","AWS KMS","AWS Security Token Service (STS) is used to grant temporary access based on identity federation, allowing users to access AWS resources using credentials from an external identity provider."
"What is the principle of least privilege in AWS IAM?","Granting only the minimum permissions necessary to perform a task","Granting all possible permissions to ensure maximum functionality","Granting permissions based on job title","Granting no permissions at all by default","The principle of least privilege states that users should only be granted the minimum permissions necessary to perform their required tasks, enhancing security."
"In AWS IAM, what is the purpose of multi-factor authentication (MFA)?","To add an extra layer of security when signing in","To encrypt data at rest","To monitor network traffic","To manage access keys","MFA adds an extra layer of security by requiring users to provide two or more authentication factors before granting access to AWS resources."
"Which of the following is a security best practice related to AWS IAM?","Regularly audit and review IAM permissions","Use the root account for daily tasks","Share access keys among multiple users","Store access keys in public repositories","Regularly auditing and reviewing IAM permissions helps ensure that users and roles have only the necessary access rights and prevents privilege escalation."
"What is the main benefit of using AWS IAM roles over IAM users for applications running on EC2 instances?","IAM roles avoid storing long-term credentials on the EC2 instance","IAM roles allow direct access to the underlying operating system","IAM roles have higher security limits than IAM users","IAM roles are cheaper than IAM users","IAM roles provide temporary credentials, eliminating the need to store long-term access keys on the EC2 instance and improving security."
"Which AWS service is used to track user activity and API calls within your AWS account?","AWS CloudTrail","AWS IAM","AWS Config","AWS Trusted Advisor","AWS CloudTrail provides visibility into user activity by recording API calls made within your AWS account."
"What is the purpose of the 'Condition' element in an AWS IAM policy?","To specify the circumstances under which the policy applies","To define the actions that are allowed or denied","To identify the user or role to whom the policy applies","To set the expiration date for the policy","The 'Condition' element allows you to define the specific circumstances under which the permissions in the policy are granted."
"What is the difference between an IAM User and an IAM Role?","An IAM User represents a person or application that interacts with AWS, while an IAM Role is assumed by an AWS service or user to perform actions.","An IAM User is used for administrative tasks only, while an IAM Role is used for developers.","An IAM User has unlimited permissions, while an IAM Role has limited permissions.","An IAM User is free, while an IAM Role requires a subscription fee.","An IAM User is a distinct identity within AWS, while an IAM Role is a temporary identity that can be assumed by a user, application, or service."
"You want to allow an external application to access your S3 bucket. Which AWS IAM approach is most suitable?","Use IAM Roles and STS to provide temporary credentials.","Create a new IAM User for the external application.","Share your AWS account root credentials.","Add the external application's IP address to your security group.","Using IAM Roles and STS allows you to grant temporary access to the S3 bucket without sharing long-term credentials, which is more secure."
"Which of the following AWS IAM features allows you to define permissions based on tags?","Attribute-Based Access Control (ABAC)","Role-Based Access Control (RBAC)","Resource-Based Policies","Service Control Policies (SCPs)","Attribute-Based Access Control (ABAC) allows you to define permissions based on resource tags, making it easier to manage permissions at scale."
"What is the benefit of using IAM instance profiles with EC2 instances?","Instance profiles provide a secure way to manage credentials for applications running on EC2 instances.","Instance profiles allow you to automatically scale EC2 instances.","Instance profiles encrypt the data stored on the EC2 instance root volume.","Instance profiles provide network security for EC2 instances.","IAM instance profiles provide a secure and manageable way to provide credentials to applications running on EC2 instances without having to embed or manage long-term credentials."
"Which AWS IAM feature can be used to grant cross-account access to resources?","IAM Roles","IAM Groups","IAM Users","IAM Policies","IAM Roles can be configured to allow users or services in one AWS account to access resources in another account."
"What does the 'Action' element specify in an AWS IAM policy?","The specific AWS service actions that are allowed or denied","The AWS region where the policy is in effect","The IAM user or role to whom the policy applies","The condition under which the policy applies","The 'Action' element lists the specific AWS service actions that the policy grants or denies permission to perform."
"You need to ensure that all IAM users in your AWS account enable MFA. What is the best way to enforce this?","Use a Service Control Policy (SCP) to deny access to resources without MFA.","Manually check each user's MFA status regularly.","Create a CloudWatch alarm to notify you when MFA is not enabled.","Send a weekly email reminder to all users.","Using a Service Control Policy (SCP) is the most effective way to centrally enforce MFA by denying access to resources for users who do not have MFA enabled."
"In AWS IAM, what is the purpose of a customer managed policy?","A policy that you create and manage in your own AWS account","A pre-defined policy created by AWS","A policy managed by a third-party","A policy that automatically applies to all users","A customer managed policy is created and managed by you in your own AWS account, giving you greater control over permissions."
"Which IAM feature allows you to centrally manage access to multiple AWS accounts?","AWS Organisations","AWS IAM Access Analyzer","AWS IAM Policy Simulator","AWS CloudTrail","AWS Organisations allows you to centrally manage access, billing, and security policies across multiple AWS accounts."
"What is the purpose of AWS IAM Policy Simulator?","To test and troubleshoot IAM policies before deploying them","To automatically generate IAM policies","To monitor IAM user activity","To enforce MFA for all IAM users","The AWS IAM Policy Simulator allows you to test and troubleshoot IAM policies to ensure they grant the correct permissions before deploying them."
"Which of the following is a valid use case for AWS IAM federation?","Allowing users to authenticate with their existing corporate credentials","Encrypting data at rest in S3","Monitoring network traffic in VPC","Automating EC2 instance backups","IAM federation allows users to authenticate with their existing corporate credentials (e.g., Active Directory) to access AWS resources without creating separate IAM users."
"What is the AWS recommended way to manage programmatic access to AWS services?","Using IAM roles for applications, and access keys for users","Storing access keys directly in application code","Using the AWS Management Console for all access","Sharing the root user credentials","IAM roles are the recommended way to manage programmatic access for applications, while access keys should be used only for individual users who need CLI or SDK access."
"What is the primary function of the AWS IAM Credential Report?","To list all the IAM users and their credential status in your AWS account","To monitor CPU utilisation of EC2 instances","To track API calls made in your AWS account","To identify unused IAM roles","The IAM Credential Report provides a downloadable report that lists all IAM users and the status of their various credentials, such as passwords, access keys, and MFA devices."
"What is the purpose of using conditions like 'aws:SourceIp' in an IAM policy?","To restrict access based on the IP address from which the request is made","To limit the time period during which the policy is valid","To specify the region in which the policy is in effect","To require MFA for all requests","The `aws:SourceIp` condition can be used to restrict access based on the IP address from which the request is made, enhancing security."
"Which AWS IAM feature can help you discover and visualize permission relationships in your AWS environment?","AWS IAM Access Analyzer","AWS IAM Policy Simulator","AWS Trusted Advisor","AWS CloudTrail","AWS IAM Access Analyzer helps you discover and visualise permission relationships, making it easier to understand who has access to what resources."
"You have an application running on an EC2 instance that needs access to an S3 bucket. What is the recommended way to grant this access using AWS IAM?","Attach an IAM role to the EC2 instance.","Store the IAM user's access keys on the EC2 instance.","Grant public access to the S3 bucket.","Use the root user's credentials.","Attaching an IAM role to the EC2 instance provides temporary credentials to the application without storing long-term access keys, improving security."
"What is the purpose of the 'sts:AssumeRole' action in an AWS IAM policy?","To allow a user or service to assume an IAM role","To create a new IAM role","To delete an existing IAM role","To modify an existing IAM role","The `sts:AssumeRole` action allows a user or service to assume an IAM role, granting them temporary access to resources based on the role's permissions."
"Which of the following is a key advantage of using temporary security credentials provided by AWS STS?","They reduce the risk of long-term credential compromise.","They provide unlimited access to AWS resources.","They are easier to manage than IAM user credentials.","They automatically encrypt data at rest.","Temporary security credentials reduce the risk of long-term credential compromise because they expire after a certain period."
"What is the purpose of using the AWS IAM Policy Generator?","To simplify the creation of IAM policies by providing a visual interface","To automatically enforce security best practices","To monitor IAM user activity","To encrypt data at rest","The AWS IAM Policy Generator provides a visual interface to simplify the creation of IAM policies, making it easier to grant the correct permissions."
"Which AWS service allows you to store and manage secrets, such as database credentials, securely?","AWS Secrets Manager","AWS CloudHSM","AWS IAM","AWS KMS","AWS Secrets Manager helps you securely store and manage secrets, such as database credentials, API keys, and other sensitive information."
"What is the purpose of the 'NotAction' element in an AWS IAM policy?","To specify the actions that are explicitly denied","To define the conditions under which the policy does not apply","To list the AWS resources that are not affected by the policy","To identify the IAM users who are not subject to the policy","The `NotAction` element specifies the actions that are explicitly denied by the policy, allowing you to exclude certain actions from a broader set of permissions."
"Which of the following IAM policy elements is required?","Version and Statement","Description and Principal","Action and Resource","Condition and Sid","The `Version` and `Statement` elements are required in an IAM policy. The `Statement` element includes `Action` and `Resource`."
"What is the purpose of the 'Sid' element in an AWS IAM policy statement?","To provide an optional statement identifier","To define the security boundary of the policy","To specify the service to which the policy applies","To set the expiration date of the policy","The `Sid` (Statement ID) element provides an optional identifier for the policy statement, making it easier to reference and manage individual statements within a policy."
"When should you use resource-based policies instead of identity-based policies in AWS IAM?","When you need to grant access to a specific resource to users in other AWS accounts","When you need to define permissions for IAM users within your own account","When you need to enforce MFA for all IAM users","When you need to monitor IAM user activity","Resource-based policies are best used when you need to grant access to a specific resource (e.g., S3 bucket) to users in other AWS accounts."
"What is the AWS recommended first step in securing a newly created AWS account?","Enable multi-factor authentication (MFA) for the root user","Create IAM users for all administrators","Create a strong password for the root user","Delete the root user","Enabling MFA for the root user adds an extra layer of security to the most privileged account in your AWS environment."
"What is the maximum length of an IAM role name?","64 characters","32 characters","128 characters","256 characters","The maximum length of an IAM role name is 64 characters."
"Which feature allows you to test if a set of IAM policies will allow access to a specific resource before applying the policy?","IAM Policy Simulator","IAM Access Analyser","AWS Trusted Advisor","AWS Config","IAM Policy Simulator allows you to test if a set of IAM policies will allow access to a specific resource before applying the policy."
"You want to implement a central location to manage AWS security settings across all of your AWS accounts. Which AWS service is the best choice for this requirement?","AWS Organisations","AWS IAM","AWS Config","AWS CloudTrail","AWS Organisations allows you to centrally manage security settings across multiple AWS accounts."
"In AWS IAM, what is the primary function of a Role?","To grant permissions to an AWS service or application","To define the password policy for IAM users","To store temporary credentials","To manage MFA devices","IAM roles grant permissions to an AWS service (like EC2) or an application to access AWS resources. It's not about managing users directly."
"Which AWS IAM feature allows you to grant permissions to users to access AWS resources based on conditions such as the source IP address?","IAM Policies with Conditions","IAM Groups","IAM Roles","IAM User Permissions Boundaries","IAM Policies with Conditions allow you to specify conditions like source IP address that must be met for the policy to be in effect."
"What is the recommended best practice for granting permissions in AWS IAM?","Grant only the minimum necessary permissions (least privilege)","Grant broad permissions to simplify management","Grant permissions based on job titles","Grant all permissions by default, then restrict","The principle of least privilege recommends granting only the permissions required to perform a task, minimising the risk of unintended actions."
"Which AWS IAM entity is best suited for granting temporary access to your AWS resources to a third-party vendor?","IAM Role","IAM User","IAM Group","IAM Policy","IAM Roles are designed for granting temporary access to AWS resources. You can define a trust policy on the role to specify who can assume the role and for how long."
"In AWS IAM, what is the purpose of a 'Trust Policy' associated with an IAM Role?","To define which entities can assume the role","To specify the permissions granted by the role","To set the password policy for users assuming the role","To restrict the regions where the role can be used","The 'Trust Policy' determines which AWS accounts or services can assume the role. It essentially defines the trusted entities."
"You have an application running on an EC2 instance that needs to access an S3 bucket. What is the recommended way to grant the application the necessary permissions using AWS IAM?","Attach an IAM Role to the EC2 instance","Embed IAM User credentials in the application code","Store IAM User credentials in the EC2 instance's metadata","Create an IAM Group and add the EC2 instance to it","Attaching an IAM Role to the EC2 instance allows the application to assume the role's permissions without needing to manage credentials directly."
"In AWS IAM, what is the function of a 'Permissions Boundary'?","To set the maximum permissions that an IAM identity can have","To define the minimum permissions required for a user","To restrict access to specific AWS regions","To enforce multi-factor authentication","A Permissions Boundary sets the maximum permissions that an IAM user or role can have. It does not grant permissions directly."
"Which AWS IAM feature helps you analyse and refine permissions by identifying unused permissions?","IAM Access Advisor","IAM Policy Simulator","IAM Credential Report","IAM Best Practices","IAM Access Advisor analyses access patterns to identify unused permissions, enabling you to refine your policies for better security."
"What is the purpose of the AWS IAM Security Token Service (STS)?","To provide temporary security credentials","To manage IAM users and groups","To define IAM policies","To monitor IAM activity","STS provides temporary security credentials for users or applications to access AWS resources. This is useful for federated access or for assuming roles."
"You need to grant an external user (outside your AWS account) access to an S3 bucket in your account. Which AWS IAM approach is most suitable?","Create an IAM Role with a trust policy allowing the external user's account to assume it","Create an IAM User for the external user in your account","Share the access key and secret key of an IAM User in your account","Add the external user to an IAM Group in your account","Creating an IAM Role with a trust policy is the correct way to grant cross-account access. The external user's account can assume the role."
"Which AWS IAM feature helps you centrally manage access to multiple AWS accounts?","AWS Organizations","AWS IAM Identity Center (Successor to AWS SSO)","AWS IAM Access Analyzer","AWS IAM Roles","AWS Organizations helps you centrally manage and govern multiple AWS accounts. AWS IAM Identity Center provides centralised access management across multiple accounts and applications."
"What is the purpose of the AWS IAM Credential Report?","To list all IAM users and their credential status","To analyse IAM policy effectiveness","To generate IAM policies","To track IAM role usage","The IAM Credential Report provides a comprehensive list of all IAM users in your account and the status of their various credentials (passwords, access keys, MFA)."
"How can you enforce multi-factor authentication (MFA) for all IAM users in your AWS account using IAM policies?","By adding an MFA condition to IAM policies","By enabling MFA on the root account","By enabling MFA for each IAM user individually","By creating a separate IAM group for MFA users","You can enforce MFA by adding conditions to IAM policies that require MFA to be used for sensitive actions. If an IAM user has MFA configured, the policy will grant access. If not, the policy will deny access."
"Which AWS IAM feature can help you discover unintended resource access by continuously monitoring permissions granted to your AWS accounts and resources?","AWS IAM Access Analyzer","AWS IAM Policy Simulator","AWS IAM Credential Report","AWS Trusted Advisor","AWS IAM Access Analyzer helps you identify resources in your organisation and accounts that are shared with an external entity. It identifies unintended access to your AWS resources, which helps you reduce the security risk to your organisation."
"When should you use IAM roles instead of IAM users?","When you need to grant access to AWS services or applications","When you need to grant access to individual people","When you need to define password policies","When you need to create groups of users","IAM roles should be used when you need to grant access to AWS services or applications. IAM users are for individual people."
"What is the purpose of the IAM Policy Simulator?","To test the effectiveness of IAM policies before deploying them","To generate IAM policies automatically","To monitor IAM user activity in real-time","To create IAM roles","The IAM Policy Simulator lets you test and troubleshoot IAM policies to make sure that they grant the intended permissions before you deploy them in your AWS environment. This helps you avoid unintended access."
"Which statement is true regarding IAM policies?","IAM policies are JSON documents that define permissions","IAM policies are automatically created by AWS","IAM policies are only used for IAM users, not roles","IAM policies are stored locally on your computer","IAM policies are JSON documents that define the permissions that IAM users, groups, and roles have to access AWS resources."
"What is the benefit of using IAM roles with temporary security credentials instead of long-term access keys?","Temporary credentials reduce the risk of compromise","Temporary credentials are required for all AWS services","Temporary credentials are easier to manage","Temporary credentials provide faster performance","Temporary security credentials significantly reduce the risk of compromise. If the temporary credentials are stolen, they are only valid for a limited time, which limits the scope of potential damage. Long-term access keys, if compromised, can be used indefinitely until revoked."
"Which of the following is a recommended best practice for managing IAM access keys?","Rotate access keys regularly","Embed access keys directly in code","Share access keys between multiple users","Store access keys in plain text files","Rotating access keys regularly is a security best practice. It limits the time that compromised keys are valid and reduces the risk of long-term exposure."
"What is the primary difference between an IAM user and an IAM group?","An IAM user represents a single person or application, while an IAM group is a collection of IAM users","IAM users can be assigned permissions directly, while IAM groups cannot","IAM users are only for AWS administrators, while IAM groups are for regular users","IAM users are stored locally on your computer, while IAM groups are stored in the cloud","An IAM user represents a single person or application that interacts with AWS, while an IAM group is a collection of IAM users. You can assign permissions to an IAM group, and all users in the group inherit those permissions, simplifying permission management."
"You need to allow an application running in one AWS account to access resources in another AWS account. What is the best approach using IAM?","Create an IAM role in the resource account and grant the application in the first account permission to assume that role.","Create an IAM user in the resource account and share the access key with the application in the first account.","Copy the IAM policies from the resource account to the first account.","Grant the root user of the first account access to the resources in the second account.","Creating an IAM role in the resource account with a trust policy that allows the application's AWS account to assume the role is the recommended method for cross-account access. It provides secure and auditable access without sharing credentials."
"What is the purpose of an IAM 'Service-Linked Role'?","To grant AWS services permissions to access other AWS resources on your behalf","To grant IAM users access to specific AWS services","To limit the permissions that AWS services can have","To monitor the activity of AWS services","A service-linked role is a special type of IAM role that is pre-defined by AWS and grants AWS services permissions to access other AWS resources on your behalf. This simplifies the process of configuring services that need to interact with other AWS resources."
"Which AWS service can be used to centrally manage identities and access across multiple AWS accounts and applications, including federated users from external identity providers?","AWS IAM Identity Center (Successor to AWS SSO)","AWS IAM Access Analyzer","AWS Organizations","AWS IAM Roles","AWS IAM Identity Center (Successor to AWS SSO) provides a single place to manage access to multiple AWS accounts and cloud applications. It supports federated users from external identity providers, such as Active Directory or Okta."
"You want to restrict access to an S3 bucket based on the user's source IP address. How can you accomplish this using AWS IAM?","Use an IAM policy with a Condition element that checks the source IP address","Use an S3 bucket policy with a Condition element that checks the user's IAM role","Use an IAM policy with a Resource element that specifies the S3 bucket","Use an S3 bucket ACL to restrict access based on IP address","You can use an IAM policy with a Condition element that checks the source IP address to restrict access to resources based on the user's network location."
"What is the purpose of the 'aws:PrincipalArn' condition key in an IAM policy?","To specify the ARN of the AWS principal that is allowed to access the resource","To specify the region where the resource is located","To specify the IAM policy version","To specify the AWS service that is allowed to access the resource","The 'aws:PrincipalArn' condition key is used in IAM policies to specify the ARN (Amazon Resource Name) of the AWS principal (e.g., an IAM user, role, or service) that is allowed to access the resource. This allows you to grant access based on the identity of the caller."
"You need to ensure that all IAM users in your AWS account enable multi-factor authentication (MFA). How can you enforce this requirement using an IAM policy?","Create an IAM policy that denies access to all AWS resources unless MFA is enabled.","Enable MFA on the root account, which automatically applies to all IAM users.","Create a separate IAM group for MFA users and assign them specific permissions.","Manually configure MFA for each IAM user in your account.","You can create an IAM policy that denies access to all AWS resources unless MFA is enabled. This policy would include a 'Condition' element that checks for the presence of an MFA token."
"Which of the following is a valid use case for IAM access keys?","Programmatic access to AWS services from outside AWS","Granting permissions to EC2 instances","Allowing users to log in to the AWS Management Console","Managing access to S3 buckets within an AWS account","IAM access keys are used for programmatic access to AWS services from outside the AWS environment, such as from applications running on your local computer or in other cloud providers."
"What is the purpose of the IAM Policy Simulator?","To test and troubleshoot IAM policies before deploying them","To automatically generate IAM policies based on user activity","To monitor IAM user activity in real time","To manage and rotate IAM access keys","The IAM Policy Simulator allows you to test and troubleshoot IAM policies before deploying them to your AWS environment. This helps you ensure that your policies grant the intended permissions and prevent unintended access."
"Which AWS service provides centralised identity management for granting users access to multiple AWS accounts and applications?","AWS IAM Identity Center (Successor to AWS SSO)","AWS IAM Access Analyzer","AWS Organizations","AWS IAM Roles","AWS IAM Identity Center (Successor to AWS SSO) provides a single place to manage access to multiple AWS accounts and cloud applications. It simplifies the process of granting users access to the resources they need while maintaining security and compliance."
"What is the purpose of an IAM Permissions Boundary?","To set the maximum permissions that an IAM identity can have","To define the minimum permissions required for a user","To restrict access to specific AWS regions","To enforce multi-factor authentication (MFA)","An IAM Permissions Boundary sets the maximum permissions that an IAM identity (user or role) can have. It acts as a ceiling on the permissions that can be granted to the identity, regardless of the policies attached to it."
"How can you restrict access to resources based on the tags applied to them?","Use an IAM policy with a Condition element that checks for specific tags","Use an S3 bucket policy with a Resource element that specifies the tags","Use an IAM policy with a Resource element that specifies the tags","Use an IAM policy with a Principal element that specifies the tags","You can use an IAM policy with a Condition element that checks for specific tags. This allows you to grant or deny access based on the presence or value of tags applied to AWS resources."
"What is the purpose of the `aws:userid` condition key in an IAM policy?","To specify the unique identifier of the IAM user or role","To specify the region where the resource is located","To specify the AWS account ID","To specify the AWS service that is allowed to access the resource","The `aws:userid` condition key is used to specify the unique identifier of the IAM user or role that is making the request. This can be useful for auditing and tracking user activity."
"You need to grant temporary access to an S3 bucket to a third-party application that is running outside of AWS. Which approach is the most secure?","Use AWS STS to generate temporary credentials for the application","Create an IAM user for the application and share the access key and secret key","Grant the application access to the S3 bucket using an S3 bucket policy","Add the application to an IAM group that has access to the S3 bucket","Using AWS STS to generate temporary credentials for the application is the most secure approach. STS allows you to create temporary, limited-privilege credentials that expire after a specified duration, reducing the risk of credential compromise."
"What is the purpose of the IAM Access Advisor?","To identify unused permissions and recommend policy updates","To automatically generate IAM policies","To monitor IAM user activity in real time","To manage and rotate IAM access keys","The IAM Access Advisor analyses access patterns to identify unused permissions and recommend policy updates. This helps you to refine your policies for better security and reduce the risk of granting excessive permissions."
"Which of the following is a recommended security best practice for managing IAM access keys?","Rotate access keys regularly","Embed access keys directly in code","Share access keys between multiple users","Store access keys in plain text files","Rotating access keys regularly is a critical security best practice. It limits the time that compromised keys are valid and reduces the risk of long-term exposure. AWS recommends rotating access keys at least every 90 days."
"How can you centrally manage IAM policies across multiple AWS accounts?","Use AWS Organizations and IAM Identity Center (Successor to AWS SSO)","Use AWS IAM Access Analyzer","Use AWS IAM Policy Simulator","Use AWS IAM Roles","You can centrally manage IAM policies across multiple AWS accounts using AWS Organizations and AWS IAM Identity Center (Successor to AWS SSO). AWS Organizations allows you to create a hierarchical structure of AWS accounts, and AWS IAM Identity Center (Successor to AWS SSO) allows you to manage identities and access across those accounts."
"What is the purpose of the 'aws:SourceVpc' condition key in an IAM policy?","To restrict access to resources based on the VPC from which the request originates","To specify the region where the resource is located","To specify the AWS account ID","To specify the AWS service that is allowed to access the resource","The `aws:SourceVpc` condition key is used to restrict access to resources based on the VPC (Virtual Private Cloud) from which the request originates. This allows you to ensure that only requests from within a specific VPC are allowed."
"You need to create a custom IAM policy that allows users to manage only EC2 instances with a specific tag. How can you achieve this?","Use an IAM policy with a Condition element that checks for the tag on the EC2 instance","Use an IAM policy with a Resource element that specifies the tag on the EC2 instance","Use an IAM policy with a Principal element that specifies the tag on the IAM user","Use an S3 bucket policy with a Condition element that checks for the tag on the EC2 instance","You can create a custom IAM policy with a Condition element that checks for the tag on the EC2 instance. The Condition element would use the aws:ResourceTag condition key to match the tag key and value on the EC2 instance."
"Which of the following is a key benefit of using IAM roles instead of IAM users for granting access to AWS resources?","IAM roles eliminate the need to manage long-term credentials","IAM roles allow for more granular control over permissions","IAM roles are easier to create and manage than IAM users","IAM roles provide faster performance than IAM users","IAM roles eliminate the need to manage long-term credentials, such as access keys and secret keys. This reduces the risk of credential compromise and simplifies security management."
"You are granting a partner organisation access to certain resources within your AWS account. Which IAM best practice should you follow?","Use IAM roles with appropriate trust policies for cross-account access","Share IAM user credentials with the partner organisation","Add the partner organisation's users to your IAM groups","Grant the partner organisation full administrator access to your account","When granting a partner organisation access to resources in your AWS account, use IAM roles with appropriate trust policies to enable cross-account access. This allows you to grant temporary and limited access without sharing long-term credentials."
"What is the function of the 'aws:RequestedRegion' condition key in an IAM policy?","To ensure that requests are only made to a specific AWS region","To specify the region where the resource is located","To specify the AWS account ID","To specify the AWS service that is allowed to access the resource","The `aws:RequestedRegion` condition key is used in IAM policies to ensure that requests are only made to a specific AWS region. This can be useful for enforcing regional compliance or preventing data from leaving a specific geographic area."
"Which AWS tool helps you identify resources in your organisation and accounts that are shared with an external entity?","AWS IAM Access Analyzer","AWS Trusted Advisor","AWS IAM Policy Simulator","AWS CloudTrail","AWS IAM Access Analyzer helps you identify resources in your organisation and accounts that are shared with an external entity. This helps you detect unintended access to your AWS resources and reduce the security risk to your organisation."
"You want to delegate administrative access to a specific service within your AWS account without granting full administrator privileges. What's the recommended way?","Use a service-linked role for the specific service.","Create an IAM user with full administrator privileges.","Use an IAM group with full administrator privileges.","Share your root account credentials.","Using a service-linked role provides specific permissions for a service, avoiding the need for full administrator privileges and minimizing potential security risks."
"What is the primary purpose of enabling MFA (Multi-Factor Authentication) for IAM users?","To add an extra layer of security to protect against unauthorised access.","To simplify password management for IAM users.","To enable auditing of user actions in AWS.","To improve the performance of AWS services.","MFA adds an extra layer of security by requiring users to provide a second factor of authentication, such as a code from a mobile app or a hardware token, in addition to their password. This helps protect against unauthorised access in case the password is compromised."
"When configuring an EC2 instance to access S3, what's the benefit of using an IAM role over using access keys?","IAM roles eliminate the need to manage and rotate access keys.","IAM roles provide faster network performance.","IAM roles allow for more granular permission control.","IAM roles reduce the cost of S3 storage.","IAM roles eliminate the need to manage and rotate access keys, improving security and simplifying the management of credentials for EC2 instances accessing S3."
"What is the main advantage of using policy variables in IAM policies?","Allows you to create dynamic policies that adapt to different contexts.","Simplifies policy management.","Improves policy readability.","Enhances policy performance.","Policy variables in IAM policies allow you to create dynamic policies that adapt to different contexts, such as user ID, resource name, or current time. This reduces the number of policies you need to manage and makes it easier to grant fine-grained permissions."
"In AWS IAM, what is the primary purpose of a Role?",To grant permissions to AWS services,To manage EC2 instance types,To configure network settings,To store encryption keys,IAM roles are used to grant permissions to AWS services or applications running on AWS so they can access other AWS resources.
Which AWS IAM feature allows you to grant temporary access to your AWS resources?,Security Token Service (STS),Virtual Private Cloud (VPC),Elastic Compute Cloud (EC2),Simple Storage Service (S3),"The Security Token Service (STS) allows you to request temporary credentials for IAM users or roles, which can be used to access AWS resources for a limited time."
Which AWS IAM entity is best suited for granting permissions to applications running on EC2 instances?,IAM Role,IAM User,IAM Group,IAM Policy,"IAM Roles are designed to be assumed by AWS services like EC2, providing a secure way to grant permissions to applications running on those services."
What is the purpose of an AWS IAM policy?,"To define permissions for users, groups, and roles",To configure network access control lists (ACLs),To manage AWS CloudTrail logs,To monitor resource utilisation,"IAM policies are JSON documents that define the permissions granted to IAM users, groups, and roles, controlling what actions they can perform on AWS resources."
"When configuring multi-factor authentication (MFA) for an AWS IAM user, which of the following is a supported option?","Virtual MFA device (e.g., Google Authenticator)",Hardware firewall,VPN connection,Software license,"AWS IAM supports the use of virtual MFA devices, such as Google Authenticator or Authy, to add an extra layer of security to user accounts."
Which AWS IAM feature helps you identify overly permissive IAM policies?,IAM Access Analyzer,AWS Config,AWS CloudWatch,AWS Trusted Advisor,"IAM Access Analyzer analyses your IAM policies and identifies which resources can be accessed by each role or user, helping you refine permissions and reduce security risks."
What is the recommended best practice for managing AWS IAM user credentials?,Use IAM roles instead of IAM users whenever possible,Store credentials in a public S3 bucket,Share the root account credentials,Embed credentials directly in application code,Using IAM roles instead of IAM users is a best practice because roles provide temporary credentials and eliminate the need to store long-term credentials directly in applications or instances.
What is the purpose of the 'Principal' element in an AWS IAM policy?,To specify who or what is allowed to assume the role or use the policy,To define the actions that are allowed or denied,To specify the resources that are affected by the policy,To configure the policy version,"The 'Principal' element specifies the entity (user, account, or service) that is allowed to assume the role or be granted the permissions defined in the policy."
Which of the following is the most secure way to store AWS IAM user's access keys?,Using AWS Secrets Manager,Embedding them directly in code,Storing them in environment variables on an EC2 instance,Sharing them in a public forum,"AWS Secrets Manager helps you securely store, rotate, and manage secrets such as database credentials, API keys, and IAM user access keys."
What is the purpose of an AWS IAM Group?,To simplify the assignment of permissions to multiple users,To isolate network traffic,To manage application deployments,To monitor system performance,"IAM groups allow you to assign the same permissions to multiple users, simplifying user management and ensuring consistent access control across your AWS environment."
"Which AWS IAM component is used to define the 'who', 'what', 'where', and 'how' of access to AWS resources?",IAM Policy,IAM Role,IAM User,IAM Group,An IAM Policy is the primary way to define who has access to what AWS resources and how they can access those resources.
What is the benefit of using AWS IAM roles for EC2 instances compared to embedding access keys directly into the instance?,It avoids the need to manage long-term credentials on the instance,It provides faster access to AWS services,It reduces the cost of running EC2 instances,It automatically encrypts data stored on the instance,"IAM roles for EC2 instances provide temporary credentials, eliminating the need to store and manage long-term access keys directly on the instance, which improves security and simplifies management."
What is the purpose of the AWS IAM Policy Simulator?,To test and troubleshoot IAM policies before deploying them,To automatically generate IAM policies,To monitor IAM user activity,To enforce password policies,The IAM Policy Simulator allows you to test and troubleshoot IAM policies to ensure they grant the intended permissions before you deploy them to your production environment.
Which AWS service can be integrated with IAM to provide centralised identity management for AWS and other cloud resources?,AWS Directory Service,AWS CloudTrail,AWS Config,AWS Systems Manager,"AWS Directory Service allows you to connect your existing on-premises Microsoft Active Directory to AWS, providing centralised identity management for AWS resources and simplifying user access control."
You want to grant a user access to only specific S3 buckets in AWS. What is the most appropriate way to accomplish this using IAM?,Create an IAM policy with resource-level permissions,Create an IAM group with full S3 access,Grant the user the 'AdministratorAccess' managed policy,Enable S3 bucket policies,"Resource-level permissions in IAM policies allow you to specify which specific S3 buckets a user can access, providing fine-grained control over resource access."
What is the AWS IAM best practice for granting least privilege?,Grant only the permissions required to perform a specific task,Grant full administrative access to all users,Grant permissions based on job titles,Grant permissions based on department,"The principle of least privilege dictates that you should grant users only the permissions they need to perform their specific tasks, minimising the potential impact of security breaches."
"In AWS IAM, what is the difference between an Identity-based policy and a Resource-based policy?","Identity-based policies are attached to IAM users, groups, or roles, while Resource-based policies are attached to AWS resources",Identity-based policies are more secure than Resource-based policies,"Identity-based policies only apply to EC2 instances, while Resource-based policies apply to S3 buckets","Identity-based policies are managed by AWS, while Resource-based policies are managed by the user","Identity-based policies are attached to IAM identities (users, groups, or roles) and specify what actions the identity can perform, while Resource-based policies are attached to resources (e.g., S3 buckets) and specify who can access the resource."
What is the purpose of AWS IAM access keys?,To enable programmatic access to AWS services,To encrypt data at rest,To monitor network traffic,To manage AWS budgets,"IAM access keys are used to provide programmatic access to AWS services, allowing users and applications to interact with AWS resources through APIs and command-line tools."
What is the function of AWS IAM credentials report?,To list all IAM users and their credential status,To monitor real-time API calls,To generate cost allocation reports,To configure CloudTrail logging,"The IAM credentials report provides a list of all IAM users in your account and the status of their various credentials, such as passwords and access keys, allowing you to identify and manage potential security risks."
Which element of an IAM policy specifies the AWS service and action to be allowed or denied?,Action,Resource,Principal,Condition,"The 'Action' element in an IAM policy specifies the AWS service and the actions within that service that the policy applies to, defining what the policy allows or denies."
What is the primary benefit of using AWS managed policies?,They are maintained and updated by AWS,They provide custom permissions tailored to specific use cases,They are more secure than customer-managed policies,They are less expensive than customer-managed policies,"AWS managed policies are pre-defined policies maintained and updated by AWS, which simplifies policy management and ensures that your policies are up-to-date with the latest AWS security best practices."
Which AWS IAM feature can be used to enforce password complexity requirements?,Password Policy,Multi-Factor Authentication,Access Analyzer,Credential Report,"IAM password policies allow you to enforce password complexity requirements, such as minimum length, character requirements, and password expiration, to improve the security of your user accounts."
What is the purpose of an AWS IAM Role's trust policy?,To define which entities are allowed to assume the role,To define the permissions granted to the role,To specify the resources the role can access,To configure the role's session duration,"The trust policy of an IAM role defines which principals (users, services, or accounts) are allowed to assume the role, establishing the trust relationship between the role and the entities that can use it."
Which of the following is a benefit of using AWS IAM to manage access to AWS resources?,Centralised access control and auditing,Automated scaling of resources,Real-time threat detection,Improved network performance,"IAM provides centralised access control and auditing capabilities, allowing you to manage and monitor access to your AWS resources from a single location."
You need to grant an external AWS account access to your S3 bucket. Which approach is most appropriate using AWS IAM?,Use resource-based policy on the S3 bucket,Create an IAM user in your account for the external account,Share your AWS root account credentials,Use a cross-account IAM role,A resource-based policy on the S3 bucket allows you to grant permissions to users or roles in another AWS account to access the bucket.
What does the 'Deny' effect in an AWS IAM policy override?,Any 'Allow' effect,Any 'Allow' effect in the same policy,Any 'Allow' effect in a different policy,The implicit 'Deny' effect,"An explicit 'Deny' effect always overrides any 'Allow' effect, regardless of where the 'Allow' effect is defined, providing a way to explicitly restrict access even if other policies grant it."
Which AWS IAM feature allows you to restrict access to AWS resources based on the originating IP address?,Condition,Role,Group,User,"IAM policy conditions allow you to specify conditions under which the policy is in effect, such as restricting access based on the source IP address, time of day, or other factors."
What is the AWS IAM service that enables federated access to AWS resources using existing identities?,AWS Security Token Service (STS),AWS Cloud Directory,AWS Identity Center (Successor to AWS SSO),AWS Cognito,"AWS Identity Center (Successor to AWS SSO) facilitates federated access to AWS resources by integrating with your existing identity providers, such as Active Directory or Okta, allowing users to use their existing credentials to access AWS."
What is the difference between AWS Identity Center (Successor to AWS SSO) and AWS IAM?,IAM manages access within a single AWS account while AWS Identity Center (Successor to AWS SSO) provides centralised access management across multiple AWS accounts.,IAM is used for programmatic access while AWS Identity Center (Successor to AWS SSO) is used for console access.,IAM is free while AWS Identity Center (Successor to AWS SSO) is a paid service.,IAM provides temporary credentials while AWS Identity Center (Successor to AWS SSO) provides permanent credentials.,"IAM manages access within a single AWS account, while AWS Identity Center (Successor to AWS SSO) provides centralised access management across multiple AWS accounts and integrates with existing identity providers."
Which of the following AWS IAM features can help you automate the rotation of access keys?,AWS Secrets Manager,IAM Access Analyzer,IAM Policy Simulator,AWS Config,"AWS Secrets Manager automates the rotation of access keys and other secrets, reducing the risk of compromised credentials and simplifying secret management."
What is the purpose of the 'aws:PrincipalOrgID' condition key in an IAM policy?,To restrict access to users belonging to a specific AWS Organization,To restrict access based on the AWS Region,To restrict access based on the IAM role,To restrict access based on the user agent,"The 'aws:PrincipalOrgID' condition key allows you to restrict access to users who belong to a specific AWS Organization, providing a way to control access based on organizational affiliation."
Which AWS service can be used to grant users access to AWS resources without requiring them to have an IAM user?,AWS Identity Center (Successor to AWS SSO),Amazon CloudWatch,AWS CloudTrail,AWS Config,"AWS Identity Center (Successor to AWS SSO) allows you to grant users access to AWS resources using their existing identities from your corporate directory, eliminating the need to create separate IAM users."
You want to ensure that all IAM users in your AWS account are using multi-factor authentication (MFA). How can you enforce this?,Use an IAM policy with a condition that requires MFA,Enable MFA at the AWS account level,Disable password authentication for all users,Use AWS Config to monitor MFA usage,You can enforce MFA by creating an IAM policy with a condition that requires users to authenticate with MFA before accessing certain resources or performing certain actions.
What is the purpose of AWS IAM session tags?,To pass user attributes to downstream applications,To configure network security groups,To automate resource tagging,To encrypt data in transit,"IAM session tags allow you to pass user attributes to downstream applications when a user assumes a role, providing context and control over the user's session."
Which of the following is NOT a component of the IAM authentication process?,Virtual Private Cloud (VPC),Access key ID,Secret access key,Security token,Virtual Private Cloud (VPC) is a networking component and not involved in the authentication process of IAM.
"In AWS IAM, what does it mean to 'assume' a role?",To temporarily take on the permissions of the role,To permanently assign the role to a user,To delete the role,To rename the role,"Assuming a role means to temporarily take on the permissions associated with that role, allowing a user or service to perform actions that the role is authorised to do."
Which of the following is an example of an AWS IAM best practice related to monitoring and auditing?,Regularly review and audit IAM policies and roles,Disable AWS CloudTrail logging,Grant broad administrative access,Store access keys directly in application code,"Regularly reviewing and auditing IAM policies and roles helps identify overly permissive permissions and potential security risks, ensuring that your access control configurations are up-to-date and aligned with security best practices."
You need to grant access to an S3 bucket to an application running on an EC2 instance. Which AWS IAM best practice should you follow?,Use an IAM role,Store the access keys in the EC2 instance metadata,Store the access keys in the application's configuration file,Share the root account credentials,Using an IAM role for the EC2 instance is the recommended best practice because it provides temporary credentials and avoids the need to store long-term access keys on the instance.
What is the purpose of the AWS IAM Access Advisor?,To identify the last time an IAM user or role accessed an AWS service,To automatically generate IAM policies,To simulate IAM policy evaluations,To encrypt IAM user passwords,"IAM Access Advisor helps you identify the last time an IAM user or role accessed an AWS service, which can be used to identify and remove unused permissions, reducing the attack surface of your AWS environment."
Which AWS IAM feature can be used to implement attribute-based access control (ABAC)?,IAM Conditions,IAM Roles,IAM Groups,IAM Users,"IAM conditions allow you to implement attribute-based access control (ABAC) by granting permissions based on attributes associated with the user, resource, or environment."
"In AWS IAM, what is the purpose of the 'sts:AssumeRole' action?",To allow an entity to assume an IAM role,To create a new IAM role,To delete an IAM role,To update an IAM role,"The 'sts:AssumeRole' action in an IAM policy allows an entity (user, service, or account) to assume an IAM role, granting it the permissions associated with that role."
Which AWS IAM feature helps you meet compliance requirements by providing detailed logs of all IAM actions?,AWS CloudTrail,AWS Config,AWS Trusted Advisor,AWS CloudWatch,"AWS CloudTrail logs all API calls made to AWS services, including IAM, providing a detailed audit trail of all IAM actions for compliance and security monitoring purposes."
What is the recommended approach for managing AWS IAM users and permissions in a multi-account environment?,Use AWS Identity Center (Successor to AWS SSO),Create a separate IAM user in each account,Share the root account credentials across all accounts,Use a single IAM user for all accounts,"AWS Identity Center (Successor to AWS SSO) provides centralised access management across multiple AWS accounts, allowing you to manage users and permissions from a single location and simplifying user access control."
What is the purpose of the 'aws:userid' condition key in an IAM policy?,To restrict access to a specific IAM user,To restrict access based on the AWS Region,To restrict access based on the IAM role,To restrict access based on the user agent,"The 'aws:userid' condition key allows you to restrict access to a specific IAM user, providing fine-grained control over user access to AWS resources."
Which of the following actions requires root account credentials?,Changing the AWS Support plan,Creating an IAM user,Launching an EC2 instance,Creating an S3 bucket,Changing the AWS Support plan requires root account credentials. This is a privileged operation and shouldn't be performed with IAM user credentials.
What is the purpose of service-linked roles in AWS IAM?,To allow AWS services to access other AWS resources on your behalf,To allow IAM users to access AWS services,To create custom IAM policies,To manage AWS CloudTrail logs,"Service-linked roles are a special type of IAM role that allows AWS services to access other AWS resources on your behalf, simplifying the configuration of service integrations and ensuring that the service has the necessary permissions to function correctly."
"In an AWS IAM policy, what does the 'Effect' element specify?",Whether the statement allows or denies access,The resources that the statement applies to,The conditions under which the statement applies,The actions that the statement allows or denies,The 'Effect' element in an IAM policy specifies whether the statement allows or denies access. It can be set to either 'Allow' or 'Deny'.
What is the main purpose of IAM Access Keys?,Provide programmatic access to AWS services,Grant console access to IAM users,Encrypt data at rest,Monitor network traffic,"IAM Access Keys are used to programmatically access AWS services from the AWS CLI, SDKs, or APIs, without needing a web browser or console."
What is the AWS IAM recommended frequency to rotate IAM user access keys?,Every 90 days,Every 365 days,Only when compromised,Never,"Rotating IAM user access keys regularly, ideally every 90 days, is a best practice to reduce the risk associated with compromised keys and improve security posture."
"In AWS IAM, what is the primary purpose of an IAM role?",To grant permissions to AWS services,To manage network configurations,To store user credentials,To define security groups,"An IAM role is designed to grant permissions to AWS services, allowing them to perform actions on your behalf without using long-term credentials."
Which AWS IAM feature allows you to grant temporary access to your AWS resources?,IAM Roles,IAM Groups,IAM Policies,IAM Users,IAM roles provide temporary security credentials to control access to AWS resources.
What is the recommended way to manage permissions for a large number of AWS IAM users with similar access requirements?,Use IAM Groups,Use Inline Policies,Use IAM Roles,Use temporary credentials,"IAM Groups allows you to assign permissions to a group of users, simplifying the management of permissions for multiple users with similar access needs."
You need to grant an external user access to a specific S3 bucket in your AWS account. What is the recommended AWS IAM method?,Create an IAM role with a trust policy,Create an IAM user with an access key,Create an IAM group with permissions,Share your root account credentials,Creating an IAM role with a trust policy enables you to grant access to resources in your AWS account to external users and applications.
Which AWS IAM component defines the permissions associated with an identity?,IAM Policy,IAM Group,IAM Role,IAM User,"An IAM Policy is a document that defines the permissions associated with an identity (user, group, or role). It specifies what actions are allowed or denied on which resources."
"In AWS IAM, what type of policy is directly attached to a user, group, or role?",Identity-based policy,Resource-based policy,Service-linked policy,Service control policy (SCP),"Identity-based policies are attached to IAM users, groups, or roles, granting permissions to those identities."
You want to restrict access to your AWS resources based on the source IP address. What AWS IAM feature can you use?,IAM Conditions,IAM Policies,IAM Roles,IAM Groups,"IAM Conditions allow you to specify conditions in your IAM policies, such as source IP address, to restrict access to your resources."
"Which AWS IAM feature is best suited for granting permissions to AWS services, such as EC2, to access other AWS resources?",IAM Roles,IAM Users,IAM Groups,IAM Policies,"IAM Roles are specifically designed for granting permissions to AWS services, enabling them to access other AWS resources securely."
What is the purpose of the AWS IAM Security Token Service (STS)?,To issue temporary security credentials,To manage user passwords,To create IAM users,To configure MFA,The AWS STS is used to issue temporary security credentials for users or applications that need to access AWS resources.
Which AWS IAM feature allows you to centrally manage access to multiple AWS accounts from a single location?,AWS Organizations,AWS IAM Access Analyzer,AWS IAM Roles,AWS IAM Groups,"AWS Organizations lets you centrally manage and govern multiple AWS accounts, including controlling access to AWS resources."
What is the purpose of AWS IAM Access Analyzer?,To identify unintended resource access,To monitor user activity,To encrypt data,To manage access keys,"AWS IAM Access Analyzer helps you identify unintended resource access to your AWS resources, ensuring that only authorised entities have access."
Which AWS IAM best practice helps prevent accidental deletion of important resources?,Enabling MFA for all users,Using IAM roles instead of IAM users,Applying the principle of least privilege,Enabling deletion protection,Applying the principle of least privilege means only giving the permissions required to perform an action. It does not specifically prevent accidental deletion of resources
"You need to allow a user to only view information about EC2 instances, but not launch, stop, or terminate them. Which AWS IAM permission should you grant?",ec2:DescribeInstances,ec2:*,ec2:RunInstances,ec2:TerminateInstances,The `ec2:DescribeInstances` permission allows a user to view information about EC2 instances without granting any other permissions.
What is the effect of explicitly denying a permission in an AWS IAM policy?,It overrides any allow statements,It is ignored if there is an allow statement,It is only effective for the root user,It is only effective for IAM roles,"An explicit deny in an IAM policy always overrides any allow statements, ensuring that the action is never permitted."
Which AWS IAM resource helps you manage permissions for applications running on EC2 instances?,IAM Roles for EC2,IAM Users,IAM Groups,IAM Policies,IAM Roles for EC2 allow you to grant permissions to applications running on EC2 instances without embedding credentials in the instances themselves.
You want to ensure that users can only access AWS resources from within your corporate network. What AWS IAM condition can you use?,aws:SourceIp,aws:UserAgent,aws:CurrentTime,aws:PrincipalTag,"The `aws:SourceIp` condition can be used to restrict access to AWS resources based on the source IP address, ensuring that users can only access resources from within your corporate network."
Which AWS IAM component can be used to group multiple IAM users together for easier permission management?,IAM Group,IAM Role,IAM Policy,IAM User,"IAM Groups are used to group multiple IAM users together, making it easier to manage permissions for a group of users with similar access needs."
What is the recommended approach for rotating AWS IAM access keys?,Use AWS STS to generate temporary credentials,Store access keys in a public repository,Share access keys among multiple users,Disable access key rotation,Using AWS STS to generate temporary credentials is the recommended approach for rotating AWS IAM access keys.
You need to grant a third-party company access to your S3 bucket for a limited time. What AWS IAM feature should you use?,IAM Roles with temporary credentials,IAM Users with long-term credentials,IAM Groups with broad permissions,Sharing your AWS account root credentials,IAM Roles with temporary credentials allows you to grant access to your S3 bucket for a limited time without sharing long-term credentials.
"Which AWS IAM feature is used to grant permissions to users to manage IAM resources themselves (e.g., create users, roles, or policies)?",IAM Permissions Boundaries,IAM Groups,IAM Roles,IAM Users,IAM Permissions Boundaries are used to grant permissions to users to manage IAM resources themselves.
"In AWS IAM, what is the purpose of a trust policy?",To define which entities can assume a role,To define the permissions a role grants,To define the authentication method,To define the password policy,"A trust policy defines which entities (AWS accounts, services, etc.) are allowed to assume the IAM role."
You have an application that needs to access multiple AWS services. What is the best way to manage its permissions?,Create an IAM role and attach it to the application's instance,Create an IAM user for the application,Store AWS credentials in the application's code,Share your AWS account root credentials,Creating an IAM role and attaching it to the application's instance is the best way to manage permissions for applications running on EC2.
What does the principle of least privilege mean in the context of AWS IAM?,Granting only the minimum necessary permissions,Granting full administrative access,Granting no permissions at all,Granting the widest possible permissions,The principle of least privilege means granting users only the minimum necessary permissions to perform their tasks.
Which AWS IAM feature provides a way to audit and review access to your AWS resources?,AWS CloudTrail,AWS Config,AWS Trusted Advisor,AWS IAM Access Analyzer,"AWS CloudTrail records API calls made to your AWS account, providing an audit trail of who accessed which resources and when."
What is the maximum validity duration for temporary security credentials issued by AWS STS?,12 hours,24 hours,1 hour,7 days,The maximum validity duration for temporary security credentials issued by AWS STS is 12 hours.
You want to automate the process of rotating IAM access keys. Which AWS service can help you achieve this?,AWS Lambda,AWS CloudWatch Events,AWS Config,AWS Trusted Advisor,AWS Lambda can be used to automate the process of rotating IAM access keys.
Which statement about AWS IAM is correct?,IAM is a global service.,IAM is a regional service tied to a specific AWS region.,IAM requires a separate subscription.,IAM is only available for EC2 instances.,"IAM is a global service, meaning that IAM users, groups, roles, and policies are available in all AWS regions."
You are granting cross-account access to an S3 bucket. What two components are required?,A bucket policy and an IAM role,Two IAM users in different accounts,Two S3 buckets in different accounts,A security group and a network ACL,Cross-account access to an S3 bucket requires a bucket policy on the S3 bucket and an IAM role in the accessing account.
What is the purpose of the `AssumeRole` API call in AWS STS?,To obtain temporary security credentials,To create a new IAM role,To delete an existing IAM role,To list all IAM roles,The `AssumeRole` API call in AWS STS is used to obtain temporary security credentials for an IAM role.
What is the difference between IAM users and IAM roles?,"IAM users represent people, IAM roles represent services.","IAM users are temporary, IAM roles are permanent.",IAM users have more permissions than IAM roles.,"IAM roles are used for authentication, IAM users are used for authorization.","IAM users represent people or applications that need to access AWS services, while IAM roles are used by AWS services to access other AWS resources."
Which type of AWS IAM policy applies to the resource itself rather than to a user or role?,Resource-based policy,Identity-based policy,Service-linked policy,Service control policy (SCP),"Resource-based policies are attached to specific AWS resources, such as S3 buckets or KMS keys, and control access to those resources."
How do you enable Multi-Factor Authentication (MFA) for an AWS IAM user?,By configuring MFA in the IAM user's settings,By creating a separate IAM role for MFA,By enabling MFA at the AWS account level,MFA is enabled by default for all IAM users,MFA is enabled for an IAM user directly in the user's security credentials settings.
You want to create a policy that allows users to only access resources with a specific tag. Which AWS IAM feature can you use?,IAM Conditions,IAM Roles,IAM Groups,IAM Policies,"IAM Conditions allow you to specify conditions in your IAM policies, such as resource tags, to restrict access to your resources."
When should you use IAM roles instead of IAM users?,When granting access to AWS services,When granting access to human users,When managing network configurations,When storing user passwords,"IAM roles are designed for granting permissions to AWS services, such as EC2 instances, Lambda functions, and other AWS resources."
What is the purpose of an AWS IAM password policy?,To enforce password complexity requirements,To control access to AWS resources,To manage user groups,To store user credentials,"An IAM password policy enforces complexity requirements for user passwords, such as minimum length, required characters, and password rotation."
Which AWS IAM service allows you to evaluate and visualise your IAM policies to help you implement permissions?,AWS IAM Policy Simulator,AWS CloudTrail,AWS Config,AWS Trusted Advisor,AWS IAM Policy Simulator helps you test and troubleshoot your IAM policies to ensure they grant the intended permissions.
What is the AWS best practice for using the root account?,Use it for initial setup only,Use it for all administrative tasks,Share it with all users,Store it in a safe place,The AWS best practice is to use the root account only for initial setup and avoid using it for day-to-day administrative tasks.
How can you grant temporary access to an AWS resource without creating an IAM user?,Use an IAM role with temporary credentials,Share your AWS account root credentials,Create a temporary access key,Use an S3 pre-signed URL,IAM roles with temporary credentials is the safest and most flexible option.
"You want to grant a user permission to create, update, and delete S3 buckets, but restrict them from listing existing buckets. What permissions should you grant?","s3:CreateBucket, s3:PutBucket, s3:DeleteBucket",s3:*,s3:ListBucket,s3:GetBucket,"`s3:CreateBucket`, `s3:PutBucket`, and `s3:DeleteBucket` allow users to create, update and delete buckets, and deny them from listing buckets."
What is the main benefit of using AWS IAM roles for EC2 instances?,They eliminate the need to store credentials on the instance,They provide permanent credentials,They offer faster access to resources,They automatically encrypt data,"IAM roles for EC2 instances eliminate the need to store long-term credentials on the instance, improving security."
Which AWS IAM feature can you use to define the maximum permissions that an identity-based policy can grant to an IAM entity (user or role)?,Permissions Boundary,IAM Policy Simulator,IAM Access Analyzer,Service Control Policy (SCP),A Permissions Boundary is an advanced feature that allows you to set the maximum permissions that an IAM entity can have.
What is the purpose of AWS IAM Access Keys?,To enable programmatic access to AWS services,To manage user passwords,To encrypt data,To monitor user activity,"AWS IAM Access Keys are used for programmatic access to AWS services via the AWS CLI, SDKs, and APIs."
Which AWS IAM feature allows you to delegate permissions to users without granting them permanent access to your AWS account?,IAM Roles,IAM Groups,IAM Policies,IAM Users,IAM Roles enable you to delegate permissions to users or services without granting them long-term access to your AWS account.
What is the difference between Service Control Policies (SCPs) and IAM Policies?,"SCPs manage permissions at the AWS account level, IAM Policies manage permissions for IAM identities","SCPs are used for temporary credentials, IAM Policies are used for permanent credentials","SCPs are attached to IAM users, IAM Policies are attached to AWS accounts","SCPs are deprecated, IAM Policies are the current method for managing permissions","Service Control Policies (SCPs) are used to centrally manage permissions across multiple AWS accounts within an AWS organisation, while IAM Policies manage permissions for IAM identities (users, groups, roles) within a single AWS account."
"When you assume an AWS IAM role, what security credentials do you receive?",Temporary security credentials,Permanent access keys,User passwords,Root account credentials,"When you assume an IAM role, you receive temporary security credentials, including an access key ID, secret access key, and session token."
Which of the following AWS IAM actions is considered a security best practice?,Regularly rotating IAM access keys,Sharing the root account password,Storing access keys in code repositories,Disabling MFA for administrative users,Regularly rotating IAM access keys is a security best practice to reduce the risk of compromised keys.
"You need to provide fine-grained access control to your S3 buckets, allowing users to only access objects with a specific prefix. Which AWS IAM feature should you use?",IAM Policy Conditions,IAM Roles,IAM Groups,IAM Users,"IAM Policy Conditions enable you to specify conditions in your IAM policies, such as object prefixes in S3, to restrict access to your resources."
"In AWS IAM, what is the purpose of a 'Policy'?",To define permissions for accessing AWS resources,To store user credentials,To define the region for AWS resources,To create a virtual private cloud,IAM Policies are used to define permissions and grant access to AWS resources. They specify what actions are allowed or denied on which resources.
An administrator needs to grant a user temporary access to an S3 bucket in AWS IAM. Which of the following is the BEST approach?,Use IAM Roles with STS (Security Token Service) to generate temporary credentials.,Create a new IAM user with access to the S3 bucket.,Share the root account credentials.,Modify the existing IAM user's policy to include access to the S3 bucket.,"Using IAM Roles with STS to generate temporary credentials is the best practice for granting temporary access, as it avoids long-term credential exposure."
Which of the following AWS IAM entities is designed to be used by applications running on EC2 instances?,IAM Role,IAM User,IAM Group,IAM Policy,"IAM Roles are designed to be assumed by AWS services, such as EC2 instances, to grant them permissions to access other AWS resources. This avoids embedding credentials directly into the application."
What is the purpose of an IAM 'Group' in AWS?,To manage a collection of IAM users with similar permissions,To define network security rules,To store encryption keys,To configure billing alerts,"IAM Groups are used to manage a collection of IAM users, making it easier to assign and manage permissions for multiple users who require similar access levels."
"In AWS IAM, what is the purpose of the 'Principle of Least Privilege'?",Granting only the permissions required to perform a specific task,Granting full administrative access to all users,Denying all access to AWS resources,Sharing credentials widely for ease of access,"The Principle of Least Privilege is a security best practice that involves granting only the permissions required to perform a specific task, minimising the risk of accidental or malicious misuse of AWS resources."
Which AWS IAM feature allows you to delegate permissions to access your AWS resources to trusted entities without sharing your AWS security credentials?,IAM Roles,IAM Users,IAM Groups,IAM Policies,IAM Roles allow you to delegate access to your AWS resources without sharing your AWS security credentials. A role is an IAM identity that you can create in your account that has specific permissions.
Which of the following is NOT a component of an IAM policy document?,Resource,Version,Action,Instance Type,"Instance Type is not part of an IAM Policy Document, the main components are Version, Statement (which contains Sid, Effect, Principal, Action, Resource, Condition)."
How can you centrally manage access to multiple AWS accounts within an organisation using AWS IAM?,AWS Organizations,AWS Config,AWS CloudTrail,AWS Trusted Advisor,"AWS Organizations lets you centrally manage billing; control access, compliance, and security; and share resources across your AWS accounts."
What is the purpose of Multi-Factor Authentication (MFA) in AWS IAM?,To add an extra layer of security by requiring a second authentication factor,To monitor user activity,To automatically patch EC2 instances,To encrypt data at rest in S3,"MFA adds an extra layer of security by requiring a second authentication factor (e.g., a code from a mobile app) in addition to the user's password, making it more difficult for unauthorised users to gain access to the account."
Which of the following AWS IAM features helps you analyse the permissions granted by your policies and identify overly permissive rules?,IAM Access Analyzer,AWS CloudTrail,AWS Config,AWS Inspector,IAM Access Analyzer helps you refine permissions and identify potential security risks by analysing your policies and providing recommendations for tightening them based on actual access patterns.
"In AWS IAM, what is the purpose of a policy?","To define permissions for users, groups and roles",To monitor network traffic,To encrypt data at rest,To manage EC2 instances,"Policies define what actions an identity (user, group, or role) can perform on AWS resources."
What is the difference between an IAM User and an IAM Role?,An IAM User is for people; an IAM Role is for services or applications,An IAM User is used for administrative tasks; an IAM Role is for developers,An IAM User can assume roles; an IAM Role cannot,An IAM User is used for temporary access; an IAM Role is for permanent access,"IAM Users represent people or applications within your AWS account, while IAM Roles are intended for services or applications needing temporary access."
"When granting permissions using AWS IAM, what is the principle of least privilege?",Granting only the permissions required to perform a specific task,Granting full administrative access to all users,Granting permissions based on the number of resources accessed,Granting permissions based on the seniority of the user,The principle of least privilege is a security best practice that involves granting users only the permissions they need to perform their assigned tasks.
You need to grant an EC2 instance access to an S3 bucket. What is the recommended way to do this using AWS IAM?,Create an IAM role and attach it to the EC2 instance,Embed the IAM user's credentials directly in the EC2 instance,Create an access key and secret key for the EC2 instance,Grant the EC2 instance full administrative access,"Attaching an IAM role to an EC2 instance allows the instance to assume the role's permissions, providing a secure way to access other AWS services."
"In AWS IAM, what does the term 'IAM principal' refer to?",The entity that is making the request,The AWS region where the IAM service is running,The IAM policy document itself,The list of available AWS services,"An IAM principal is the entity (user, group, role, or service) that is making a request for access to an AWS resource."
What is the purpose of multi-factor authentication (MFA) in AWS IAM?,To add an extra layer of security when accessing AWS resources,To improve the performance of IAM operations,To simplify IAM policy management,To reduce the cost of using AWS IAM,"MFA adds an extra layer of security by requiring users to provide two or more authentication factors, such as a password and a code from a mobile app."
Which AWS IAM feature allows you to grant temporary access to your AWS resources?,Security Token Service (STS),IAM Access Analyzer,IAM Credentials Report,IAM Password Policy,"AWS STS enables you to request temporary, limited-privilege credentials for IAM users or roles."
What is the function of the AWS IAM Access Analyzer?,To identify resources shared with external entities,To automatically rotate IAM access keys,To enforce password complexity requirements,To detect unused IAM roles,"The IAM Access Analyzer helps you identify resources in your organisation and accounts, such as S3 buckets or IAM roles, that are shared with an external entity."
How can you audit AWS IAM access within your AWS account?,By using AWS CloudTrail to log IAM API calls,By enabling detailed monitoring on all EC2 instances,By creating regular snapshots of all IAM policies,By monitoring network traffic to AWS services,"AWS CloudTrail records all API calls made to your AWS account, including IAM API calls, providing a detailed audit trail of IAM activity."
You are tasked with setting up centralised access control for multiple AWS accounts within your organisation. Which AWS service can help you achieve this?,AWS Organisations with IAM Identity Center (successor to AWS SSO),AWS Cloud Directory,AWS Cognito,AWS Certificate Manager,"AWS Organisations allows you to centrally manage access to multiple AWS accounts using IAM Identity Center (successor to AWS SSO), which enables you to assign users and groups to roles in different accounts."
"In AWS IAM, what is the primary purpose of a Role?",To grant permissions to AWS services and applications,To store user passwords securely,To define network access control lists,To manage EC2 instance sizes,"An IAM Role is used to grant permissions to AWS services and applications, allowing them to access other AWS resources without needing long-term credentials."
An engineer is designing an AWS solution and needs to grant a web application running on EC2 instance access to an S3 bucket. Which IAM approach is MOST secure?,Assign an IAM role to the EC2 instance.,Embed AWS credentials directly in the application code.,Store AWS credentials in environment variables on the EC2 instance.,Create an IAM user and distribute the credentials to the application.,"Assigning an IAM role to the EC2 instance is the most secure way to grant access, as it avoids storing long-term credentials on the instance."
Which of the following AWS IAM features allows you to grant temporary access to your AWS resources?,IAM Roles,IAM Groups,IAM Policies,IAM Users,"IAM Roles allow you to grant temporary access to your AWS resources, defining a set of permissions that entities can assume."
"In AWS IAM, what is the purpose of a policy?","To define permissions for users, groups, and roles",To store user authentication information,To configure network security groups,To monitor resource utilisation,"An IAM policy defines the permissions that determine what users, groups, and roles can do in AWS."
Which AWS IAM feature helps you to control who (identities) and what (resources) can access your AWS resources?,IAM Policies,IAM Groups,IAM Users,IAM Roles,"IAM Policies are the primary mechanism for controlling access to AWS resources by defining permissions for identities (users, groups, roles) and resources."
What is the main benefit of using AWS IAM Groups?,To manage permissions for multiple users collectively,To store user passwords securely,To define network configurations,To monitor resource utilisation,"IAM Groups allow you to manage permissions for multiple users collectively, simplifying permission management by assigning permissions to the group rather than individual users."
"In AWS IAM, what type of policy is directly attached to a user, group, or role?",Identity-based policy,Resource-based policy,Service control policy (SCP),Trust policy,"An identity-based policy is directly attached to an IAM user, group, or role, defining the permissions that the identity has."
You are creating an AWS IAM policy. Which of the following elements is required in every policy statement?,Action,Condition,Resource,Principal,"The 'Action' element is a required element in an IAM policy statement, specifying the operation that the policy allows or denies."
Which AWS IAM feature is best suited for enabling multi-factor authentication (MFA) for users?,Virtual MFA device,IAM Roles,IAM Groups,IAM Policies,"A Virtual MFA device is best suited for enabling multi-factor authentication (MFA) for users, providing an extra layer of security beyond passwords."
"When using AWS IAM to grant access to S3 buckets, what type of policy is attached to the S3 bucket itself to control who can access it?",Resource-based policy,Identity-based policy,Service control policy (SCP),Session policy,"A resource-based policy, such as a bucket policy, is attached to the S3 bucket itself to control who can access the bucket and its contents."