"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
What is the primary function of AWS Resource Access Manager (RAM)?,To securely share AWS resources across AWS accounts,To monitor resource utilisation across AWS accounts,To automate resource deployment across AWS accounts,To manage IAM roles and policies,AWS RAM enables you to securely share your AWS resources with any AWS account or within your AWS organisation. It simplifies resource management and reduces operational overhead.
Which of the following resource types CANNOT be shared using AWS Resource Access Manager (RAM)?,EC2 Instances,VPCs,Subnets,Transit Gateways,"While RAM can share many resource types, EC2 instances are not directly shared using RAM. Instead, other resources like VPCs, subnets, and transit gateways are commonly shared to allow cross-account network connectivity."
"When using AWS Resource Access Manager (RAM), what is a 'resource share'?",A container for resources you want to share,A report showing resource utilisation,A policy document defining access permissions,A log of all resource sharing activities,A resource share is a container for resources you want to share with other accounts. It specifies the resources and the accounts or organisations to share them with.
"With AWS Resource Access Manager (RAM), which of the following is a benefit of sharing resources within an AWS Organisation?",Simplified resource management,Increased compute capacity,Reduced security risks,Automatic cost optimisation,Sharing resources within an AWS Organisation using RAM simplifies resource management by centralising the process and reducing the need for individual IAM policies and resource-level permissions.
"If you share a subnet with another AWS account using AWS Resource Access Manager (RAM), what is a responsibility of the owner account?",Maintaining the subnet's route table,Managing the guest account's IAM permissions,Ensuring the guest account's resources comply with security policies,Paying for the guest account's resource usage,"The owner account retains responsibility for managing the subnet's route table, NACLs and other network configurations. The guest account uses the subnet within the owner's network infrastructure."
What is the purpose of 'customer managed permissions' in the context of AWS Resource Access Manager (RAM)?,To define custom access controls for shared resources,To delegate administrative control to guest accounts,To monitor resource usage by guest accounts,To automatically grant permissions based on resource type,"Customer managed permissions in AWS RAM allow you to define custom access controls for shared resources, giving you fine-grained control over what actions guest accounts can perform on those resources."
"Using AWS Resource Access Manager (RAM), how can you ensure that newly created resources are automatically shared with the intended accounts?",By enabling automatic resource discovery,By creating a tag-based resource share,By configuring a CloudWatch event rule,By creating a default resource share,You can use tag-based resource sharing in RAM to automatically share new resources that have specific tags with the accounts in your organisation.
"When using AWS Resource Access Manager (RAM), what does the term 'principal' refer to?",The AWS account or organisation being granted access to the shared resource,The IAM role used to access shared resources,The AWS region where the shared resource is located,The type of resource being shared,"In the context of AWS RAM, the 'principal' refers to the AWS account, OU or organisation that is being granted access to the shared resource."
"With AWS Resource Access Manager (RAM), what is the impact of deleting a resource share on the resources that have been shared?",The shared resources are deleted,The shared resources are no longer accessible to the principals,The shared resources are moved to a new AWS account,The shared resources are automatically backed up,"When a resource share is deleted in AWS RAM, the resources that were shared are no longer accessible to the principals that were granted access. The resources themselves are not deleted."
You have shared a VPC using AWS Resource Access Manager (RAM). What steps does the receiving account need to take to use the shared VPC?,Accept the resource share invitation,Create an IAM role to access the VPC,Configure a peering connection to the VPC,Replicate the VPC in their own account,The receiving account must accept the resource share invitation in the AWS RAM console before they can use the shared VPC. This grants them access to the shared resources.
"With AWS Resource Access Manager (RAM), what is the primary purpose of a resource share?",To grant access to your resources to other AWS accounts or within your organisation,To encrypt data stored in your resources,To monitor resource utilisation,To automate resource provisioning,Resource shares in AWS RAM are used to share your AWS resources with other AWS accounts or within your organisation. This allows you to centrally manage access to your resources across multiple accounts.
"When using AWS Resource Access Manager (RAM), which of the following resource types CANNOT be shared directly?",EC2 Instances,VPCs,Route 53 Resolver rules,Subnets,"EC2 instances cannot be directly shared using AWS RAM. Other resources like VPCs, Route 53 Resolver rules and subnets can be shared."
What is the benefit of using AWS Resource Access Manager (RAM) within an AWS Organisation?,Simplified cross-account resource sharing within the organisation,Automated security patching of EC2 instances,Cost optimisation through consolidated billing,Enhanced monitoring of network traffic,"AWS RAM simplifies cross-account resource sharing within an organisation by allowing you to share resources centrally, eliminating the need for individual IAM policies."
"In AWS Resource Access Manager (RAM), what is the purpose of a 'permission'?",It defines what actions a principal can perform on a shared resource,It specifies the geographical region in which the resource can be accessed,It determines the cost of using the shared resource,It defines the maximum number of principals that can access the resource,"A 'permission' in AWS RAM defines the specific actions that a principal (e.g., an AWS account or IAM user) can perform on a shared resource. This controls the level of access granted."
What is the recommended approach for auditing resource sharing activities performed using AWS Resource Access Manager (RAM)?,Use AWS CloudTrail to log RAM API calls,Use AWS Config to track resource configurations,Use Amazon CloudWatch to monitor resource utilisation,Use AWS Trusted Advisor to check for security vulnerabilities,"AWS CloudTrail logs all API calls made to AWS RAM, allowing you to audit resource sharing activities, track changes, and ensure compliance."
"When sharing a resource using AWS Resource Access Manager (RAM), what is a 'principal'?",An AWS account or IAM entity that is granted access to the shared resource,The AWS region where the resource is located,"The type of resource being shared (e.g., VPC, subnet)",The administrator responsible for managing the resource share,"In AWS RAM, a 'principal' is an AWS account, IAM user, or IAM role that is granted access to the resource shared through a resource share. It identifies who can use the resource."
You want to share an AWS Transit Gateway using AWS Resource Access Manager (RAM). What is the first step you should take?,Create a resource share and associate the Transit Gateway with it,Enable cross-account access in the Transit Gateway settings,Create an IAM role with permissions to manage the Transit Gateway,Create a CloudWatch alarm to monitor the Transit Gateway,The first step to share a Transit Gateway using AWS RAM is to create a resource share and then associate the Transit Gateway with that resource share. This defines who you are sharing the resource with.
"Using AWS Resource Access Manager (RAM), if a resource is shared from Account A to Account B, and Account B already has an identical resource, what happens?","Account B can choose to accept the shared resource, effectively having two identical resources.",Account B's existing resource is automatically replaced with the shared resource from Account A.,The sharing fails because Account B already has a resource of the same type.,Account B's resource is automatically merged with the shared resource from Account A.,"Account B can choose to accept the shared resource, effectively having two identical resources. It’s up to Account B to manage the duplicate if accepted."
"With AWS Resource Access Manager (RAM), what happens to the shared resource if the resource share is deleted?",The shared resources are no longer accessible to the principals in the target accounts.,The shared resources are automatically deleted.,The shared resources are moved to a different AWS account.,The shared resources remain accessible but are no longer managed by RAM.,"When a resource share is deleted in AWS RAM, the shared resources are no longer accessible to the principals in the target accounts. They lose the permissions granted through the resource share."
"When using AWS Resource Access Manager (RAM), what is the significance of the 'aws:PrincipalOrgID' condition key in a resource policy?",It restricts access to resources based on the organisation ID of the principal.,It specifies the AWS region where the resource is located.,It defines the cost associated with using the resource.,It sets the maximum duration for which the resource can be shared.,The `aws:PrincipalOrgID` condition key in a resource policy restricts access to resources based on the organisation ID of the principal. This ensures that only principals within a specific AWS organisation can access the resource.
What is the primary purpose of AWS Resource Access Manager (RAM)?,To securely share AWS resources across AWS accounts or within an organisation,To centrally manage AWS IAM users and roles,To monitor the cost and usage of AWS resources,To automate the deployment of AWS resources,"AWS RAM enables you to securely share your AWS resources, such as VPCs, subnets, and transit gateways, with other AWS accounts or within your AWS organisation."
Which type of AWS resource cannot be shared using AWS Resource Access Manager (RAM)?,EC2 instances,VPCs,Subnets,Transit Gateways,"EC2 instances cannot be directly shared via RAM. RAM focuses on networking, database, and other infrastructure resources."
"In AWS Resource Access Manager (RAM), what is a 'resource share'?",A container for managing and sharing resources,A snapshot of resources at a point in time,A log of resource access events,A tool for optimising resource costs,"A resource share is the central construct in RAM, acting as a container that specifies the resources to be shared and the principals (accounts or organisations) with whom they are shared."
"When sharing a subnet using AWS Resource Access Manager (RAM), what access is granted to the participating accounts?",They can launch resources into the shared subnet,They gain full administrative control over the subnet,They can modify the subnet's route tables,They can delete the subnet,"Sharing a subnet via RAM allows participating accounts to launch resources into it. They don't gain administrative control, modify route tables, or delete the subnet."
You want to share an AWS Transit Gateway with multiple accounts in your organisation using AWS Resource Access Manager (RAM). What is the first step?,Create a resource share and associate the Transit Gateway with it,Create a new VPC in each participating account,Update the routing tables in each participating account,Enable cross-account access using IAM roles,The first step is to create a resource share and associate the Transit Gateway with it. This defines what you're sharing and who you're sharing it with.
What is the benefit of using AWS Organisations in conjunction with AWS Resource Access Manager (RAM)?,Simplified resource sharing across all accounts in the organisation,Automatic scaling of resources across accounts,Centralised billing for all shared resources,Enhanced security auditing of shared resources,AWS Organisations simplifies resource sharing across all accounts within the organisation through integration with RAM. This avoids needing to manually specify each account.
Which AWS service does AWS Resource Access Manager (RAM) integrate with to provide fine-grained access control over shared resources?,AWS Identity and Access Management (IAM),AWS CloudTrail,AWS Config,AWS CloudWatch,RAM uses IAM policies and resource-based policies to control access to shared resources. This allows for fine-grained permissions and restrictions.
"After sharing a resource using AWS Resource Access Manager (RAM), how does the receiving account access the shared resource?",The resource appears directly in their AWS Management Console,They must explicitly accept the resource share invitation,They must create an IAM role to assume access to the shared resource,The resource is automatically replicated into their account,The receiving account needs to explicitly accept the invitation to the resource share. This ensures they are aware of the shared resource and agree to its use.
You are sharing a VPC using AWS Resource Access Manager (RAM). What happens if the owner account deletes the shared VPC?,All resources launched into the VPC by the participating accounts are terminated,"The shared VPC remains active in the participating accounts, but new resources cannot be launched",The participating accounts automatically gain ownership of the VPC,The shared VPC is automatically backed up before deletion,"If the owner account deletes the shared VPC, all resources launched into it by participating accounts are terminated. The VPC is no longer accessible."
"In AWS Resource Access Manager (RAM), what is the purpose of a 'principal'?",The AWS account or organisation with which a resource is shared,The IAM role used to access the shared resource,The individual user who manages the shared resource,The type of AWS resource being shared,"A 'principal' in RAM refers to the AWS account, organisational unit (OU), or organisation with which you are sharing a resource. They are the recipients of the share."
"With AWS Resource Access Manager (RAM), what is a 'resource share'?",A container of AWS resources that you want to share,A public marketplace for selling your AWS resources,A tool for monitoring resource utilisation,A feature for optimising resource costs,A resource share in AWS RAM is a container that allows you to share your AWS resources with other AWS accounts or within your organisation.
What is the primary benefit of using AWS Resource Access Manager (RAM)?,Centralised resource sharing and management,Automated cost optimisation across accounts,Enhanced security through isolated resource access,Simplified compliance reporting for all AWS resources,"AWS RAM enables centralised resource sharing and management, simplifying the process of sharing resources across multiple AWS accounts or within an organisation."
Which types of AWS resources can be shared using AWS Resource Access Manager (RAM)?,"Subnets, Transit Gateways, License Manager configurations","EC2 instances, S3 buckets, CloudFront distributions","IAM roles, Lambda functions, API Gateway APIs","DynamoDB tables, Kinesis streams, SQS queues","RAM supports sharing a variety of resources including subnets, transit gateways, and License Manager configurations."
What is the 'principal' in the context of AWS Resource Access Manager (RAM)?,The AWS account or organisation that receives access to the shared resource,The AWS service responsible for managing resource sharing,The IAM role used to access shared resources,The AWS region where the shared resources are located,"The principal in AWS RAM is the AWS account, organisation or organisational unit that receives access to the shared resources."
"When sharing a subnet using AWS Resource Access Manager (RAM), what additional configuration is typically required?",Modifying the subnet's route tables to include the sharing accounts' VPCs,Attaching an IAM role to the subnet allowing access to other accounts,Creating a new security group with specific ingress and egress rules,Encrypting the subnet using KMS keys,"When sharing a subnet, you'll need to modify the subnet's route tables to include the sharing accounts' VPCs to allow traffic to flow between them."
What happens when an AWS account leaves an AWS Organisation that has been sharing resources through AWS Resource Access Manager (RAM)?,Resource shares associated with the account are automatically deleted,Access to shared resources is immediately revoked for the leaving account,The account retains access to shared resources until manually removed,The leaving account becomes the owner of all shared resources,"When an account leaves an organisation, access to shared resources is immediately revoked to maintain security and compliance."
"If you want to share resources only within your AWS Organisation, what type of sharing should you configure in AWS Resource Access Manager (RAM)?",Sharing within your organisation,Public sharing,Cross-account sharing,External sharing,"To share resources only within your organisation, you should configure sharing within your organisation in AWS RAM. This ensures that only accounts within your AWS Organisation can access the shared resources."
How does AWS Resource Access Manager (RAM) integrate with AWS Organisations?,RAM can automatically share resources across all accounts within an organisation,RAM replaces AWS Organisations for resource management,AWS Organisations policies automatically govern RAM resource sharing,AWS Organisations is not required to use RAM,"RAM integrates with AWS Organisations to simplify sharing resources across all accounts within the organisation, making it easier to manage access centrally."
"In AWS Resource Access Manager (RAM), what is the purpose of 'resource types'?",To specify the types of resources that can be shared,To filter resources based on tags,To define the billing model for shared resources,To control the geographic location of shared resources,"Resource types in AWS RAM specify the types of AWS resources (e.g., subnets, transit gateways) that can be shared using RAM."
Can resources shared using AWS Resource Access Manager (RAM) be modified by the principal receiving the share?,It depends on the permissions granted in the resource share,"Yes, the principal has full control over shared resources","No, shared resources are read-only",Only the resource owner can modify the resources,"The principal's ability to modify shared resources depends on the permissions granted in the resource share, often defined through resource-based policies."
You need to share a Transit Gateway across multiple AWS accounts. Which AWS service should you use?,AWS Resource Access Manager (RAM),AWS Transit Gateway Network Manager,AWS Direct Connect,AWS Virtual Private Cloud (VPC),AWS Resource Access Manager (RAM) is designed for sharing resources like Transit Gateways across multiple AWS accounts.
What is the role of a Resource Access Manager (RAM) resource share invitation?,To grant access to shared resources to specific AWS accounts,To request access to resources owned by other AWS accounts,To track the usage of shared resources,To manage the cost allocation of shared resources,A RAM resource share invitation is used to grant access to shared resources to specific AWS accounts. The invited account must accept the invitation to gain access.
Which AWS service helps you discover resources shared with you via AWS Resource Access Manager (RAM)?,AWS Resource Groups,AWS Systems Manager Inventory,AWS Config,AWS Trusted Advisor,AWS Resource Groups can be used to discover resources shared with you via AWS Resource Access Manager (RAM) by grouping them based on tags or other criteria.
How does AWS Resource Access Manager (RAM) contribute to achieving security best practices?,By centralising resource sharing and reducing the need for IAM role duplication,By automatically rotating IAM credentials for shared resources,By encrypting all data transferred between shared resources,By providing real-time threat detection for shared resources,AWS RAM contributes to security best practices by centralising resource sharing and reducing the need for creating and managing multiple IAM roles across accounts.
What happens when the owner of a resource shared via AWS Resource Access Manager (RAM) deletes that resource?,"The resource share is automatically updated, and the resource becomes unavailable to all principals","The resource share remains active, but principals can no longer access the resource",The resource is automatically transferred to one of the principals,The resource is backed up to S3,"When the owner of a shared resource deletes it, the resource share is automatically updated, and the resource becomes unavailable to all principals who were granted access through the share."
Can you share resources across different AWS Regions using AWS Resource Access Manager (RAM)?,"No, RAM resource shares are limited to a single AWS Region","Yes, RAM supports cross-region resource sharing by default",Only some resource types support cross-region sharing,You must enable cross-region sharing manually via the AWS CLI,"No, AWS Resource Access Manager (RAM) resource shares are limited to a single AWS Region."
Which AWS service is typically used in conjunction with AWS Resource Access Manager (RAM) to manage access to shared resources?,AWS Identity and Access Management (IAM),AWS CloudTrail,AWS Config,AWS CloudWatch,AWS Identity and Access Management (IAM) is used in conjunction with AWS Resource Access Manager (RAM) to define and manage access permissions to shared resources.
What is the benefit of using AWS Resource Access Manager (RAM) for sharing VPC subnets?,Simplified network architecture and resource utilisation,Automated subnet resizing based on traffic,Improved network security with isolated subnets,Reduced cost of NAT Gateways,Using AWS RAM to share VPC subnets simplifies network architecture and improves resource utilisation by allowing multiple accounts to share the same subnets.
How can you control the versions of RAM resource share configurations?,Resource share versions are automatically managed by AWS,You must manually manage resource share versions using AWS CLI,AWS Config tracks changes to RAM configurations,AWS CloudTrail captures RAM configuration changes,"Resource share versions are automatically managed by AWS. AWS handles the versioning internally, ensuring compatibility and consistency."
You want to share an AWS License Manager configuration with another account using AWS Resource Access Manager (RAM). What permission model applies?,The receiver's account must have compatible IAM policies to use the configuration,The configuration automatically applies to the receiver's account,The configuration is only accessible via the AWS CLI,No additional IAM policies are required,The receiver's account must have compatible IAM policies in place to use the AWS License Manager configuration that's been shared using AWS RAM. This ensures that the receiver has the necessary permissions to utilise the shared configuration.
How does AWS Resource Access Manager (RAM) help with compliance requirements?,"By providing centralised control over resource sharing, reducing the risk of unauthorized access",By automatically generating compliance reports for shared resources,By encrypting all shared data to meet regulatory standards,By automatically patching shared resources to address security vulnerabilities,"AWS RAM helps with compliance by providing centralised control over resource sharing, reducing the risk of unauthorized access and ensuring that resources are shared according to defined policies."
What happens if you share a resource using AWS Resource Access Manager (RAM) and then disable RAM in your AWS account?,"The resource shares are terminated, and access is revoked for all principals","The resource shares remain active, but cannot be modified",The resource shares are automatically migrated to another account,The resource shares are converted to standard IAM policies,"If you disable RAM in your AWS account, the resource shares are terminated, and access is revoked for all principals, as RAM is the mechanism through which sharing is enabled."
How can you monitor the usage of resources shared via AWS Resource Access Manager (RAM)?,Using AWS CloudWatch metrics associated with the shared resources,Using AWS RAM's built-in monitoring dashboards,Using AWS Trusted Advisor's resource utilisation reports,Using AWS Cost Explorer's shared cost allocation feature,"You can monitor the usage of resources shared via AWS RAM using AWS CloudWatch metrics associated with the shared resources. This allows you to track performance, utilisation and other relevant metrics."
"In AWS Resource Access Manager (RAM), what is the purpose of a 'customer managed permission'?",To define custom permissions for accessing shared resources,To automatically grant full access to all shared resources,To delegate permission management to the principal account,To encrypt resource shares with customer managed keys,"A customer managed permission in AWS RAM is used to define custom permissions for accessing shared resources, allowing for more granular control over what actions the principal can perform."
What is a key difference between using AWS Resource Access Manager (RAM) and creating cross-account IAM roles for resource sharing?,"RAM provides centralised management of resource sharing, while IAM roles require distributed management",IAM roles offer better security than RAM,RAM is cheaper than using IAM roles for resource sharing,IAM roles are easier to set up than RAM,"RAM provides centralised management of resource sharing, simplifying the process of granting and revoking access to resources across multiple accounts compared to the distributed management required with IAM roles."
"When sharing a subnet via AWS Resource Access Manager (RAM), what considerations should be made regarding IP address management?",Ensure that there are no overlapping IP address ranges between the shared subnet and the VPCs of the principals,Automatically allocate new IP addresses to principals' resources in the subnet,Force all resources in the shared subnet to use public IP addresses,Restrict the number of IP addresses that principals can use,"When sharing a subnet via AWS RAM, ensure there are no overlapping IP address ranges between the shared subnet and the VPCs of the principals to avoid routing conflicts."
How does AWS Resource Access Manager (RAM) simplify multi-account governance?,By centralising resource sharing policies and reducing the need for individual account configurations,By automatically enforcing security best practices across all shared resources,By providing a single pane of glass for managing all AWS resources,By automating the creation of AWS accounts within an organisation,"AWS RAM simplifies multi-account governance by centralising resource sharing policies, reducing the need for individual account configurations and making it easier to enforce consistent policies across multiple accounts."
What is the primary advantage of using AWS Resource Access Manager (RAM) compared to manually creating VPC peering connections for shared resources?,RAM simplifies the management and reduces the operational overhead of resource sharing,VPC peering provides better security than RAM,VPC peering is more cost-effective than using RAM,VPC peering is easier to configure than RAM,"The primary advantage of using AWS RAM compared to manually creating VPC peering connections is that RAM simplifies the management and reduces the operational overhead of resource sharing, especially when dealing with multiple accounts."
How can you automate the process of accepting resource share invitations in AWS Resource Access Manager (RAM)?,Using AWS CloudFormation templates to automatically accept invitations,Using AWS Lambda functions triggered by resource share events,Using AWS Config rules to automatically approve invitations,Manual acceptance is always required,"You can automate the process of accepting resource share invitations using AWS Lambda functions triggered by resource share events, allowing for programmatic acceptance based on predefined criteria."
What is the impact on billing when sharing resources using AWS Resource Access Manager (RAM)?,The resource owner is responsible for all billing costs associated with the shared resource,Billing is split evenly between the resource owner and the principals,Billing is transferred to the principals,AWS automatically optimises billing across accounts,"The resource owner is responsible for all billing costs associated with the shared resource, regardless of which accounts are using it."
Which statement is correct about the compatibility of AWS RAM with different AWS account types?,"AWS RAM can share resources between standalone accounts, and accounts in an AWS Organisation",AWS RAM only works with accounts that are part of an AWS Organisation,AWS RAM only works with standalone AWS accounts,AWS RAM requires all accounts to be part of the same AWS Region,"AWS RAM can share resources between standalone accounts and accounts in an AWS Organisation, providing flexibility in resource sharing scenarios."
"If you are sharing a resource with an AWS account that is not part of your AWS organisation, how does the other account get access to the resource share?",They must accept a resource share invitation sent via AWS Resource Access Manager (RAM),The resource is automatically shared when the resource share is created,They must request access via AWS Support,The resource owner must manually configure IAM roles in both accounts,"When sharing a resource with an AWS account that's not part of your AWS organisation, that account must accept a resource share invitation sent via AWS Resource Access Manager (RAM) to gain access to the resource."
When should you consider using AWS Resource Access Manager (RAM) instead of AWS Control Tower account factory?,When you need granular control over sharing existing AWS resources,When you need to create new AWS accounts automatically,When you need to enforce compliance across all accounts,When you need to manage user access to AWS resources,"You should consider using AWS RAM when you need granular control over sharing existing AWS resources among accounts, as opposed to creating new accounts (which is the primary function of AWS Control Tower account factory)."
How can you revoke access to a resource shared via AWS Resource Access Manager (RAM)?,Remove the principal from the resource share,Delete the resource,Disable AWS RAM,Modify the resource-based policy,"To revoke access to a resource shared via AWS RAM, you simply remove the principal (AWS account or organisation) from the resource share. This immediately revokes their access."
What is the relationship between AWS Resource Access Manager (RAM) and resource-based policies?,Resource-based policies define the permissions granted to principals accessing resources shared via RAM,RAM replaces resource-based policies for resource sharing,RAM automatically generates resource-based policies,Resource-based policies cannot be used with RAM,Resource-based policies define the permissions granted to principals accessing resources shared via RAM. They work together to control access to shared resources.
What security considerations should be taken into account when sharing resources via AWS Resource Access Manager (RAM)?,Regularly review and update resource share permissions to ensure least privilege,All shared resources are automatically encrypted,All shared resources are isolated from each other,Shared resources cannot be accessed from outside the AWS network,"When sharing resources via AWS RAM, it's crucial to regularly review and update resource share permissions to ensure the principle of least privilege is followed, minimising potential security risks."
How does AWS Resource Access Manager (RAM) handle dependencies between shared resources?,RAM does not automatically manage dependencies; you must manually configure them,RAM automatically manages all dependencies between shared resources,RAM only manages dependencies within a single account,Dependencies must be defined in the resource-based policy,AWS RAM does not automatically manage dependencies between shared resources. You must manually configure these dependencies to ensure proper functionality.
What is the main advantage of using AWS Resource Access Manager (RAM) over manually creating resource sharing scripts?,"RAM provides a managed, secure, and auditable way to share resources",Custom scripts offer more flexibility,Custom scripts are more cost-effective,RAM is only beneficial for sharing a small number of resources,"The main advantage of using AWS RAM over manually creating resource sharing scripts is that RAM provides a managed, secure, and auditable way to share resources, reducing the operational burden and potential security risks."
Can you use AWS Resource Access Manager (RAM) to share AWS Marketplace subscriptions?,"No, AWS RAM does not support sharing AWS Marketplace subscriptions","Yes, AWS RAM can share all types of AWS Marketplace subscriptions",Only specific AWS Marketplace subscriptions can be shared,You must contact AWS Support to enable sharing of AWS Marketplace subscriptions,"No, AWS RAM does not support sharing AWS Marketplace subscriptions. Marketplace subscriptions typically have their own sharing mechanisms."
What is the impact of deleting a resource share in AWS Resource Access Manager (RAM)?,Access to shared resources is revoked for all principals,The underlying resources are deleted,The resource share is automatically recreated,The resource share is archived,"When you delete a resource share in AWS RAM, access to the shared resources is revoked for all principals. The underlying resources are not deleted, just the sharing configuration."
How does AWS Resource Access Manager (RAM) help simplify the management of hybrid cloud environments?,By allowing you to share AWS resources with on-premises infrastructure,By automatically migrating on-premises resources to AWS,By providing a unified management console for both AWS and on-premises resources,RAM does not directly manage hybrid cloud environments,"AWS RAM helps simplify the management of hybrid cloud environments by allowing you to share AWS resources (such as VPCs) with other AWS accounts that might be connected to on-premises infrastructure via VPN or Direct Connect. However, it doesn't directly manage on-premises resources."
Which AWS service can be used to automate the creation and management of AWS Resource Access Manager (RAM) resource shares?,AWS CloudFormation,AWS Systems Manager,AWS Config,AWS CloudTrail,"AWS CloudFormation can be used to automate the creation and management of AWS Resource Access Manager (RAM) resource shares, enabling infrastructure as code for resource sharing."
You are using AWS Resource Access Manager (RAM) to share a subnet and the receiving account makes changes to the route table associated with that subnet. What is the impact of these changes?,The changes only affect the resources within the receiving account,The changes affect all resources using the shared subnet,The resource owner is notified of all changes,The resource share is automatically terminated,"The changes affect all resources using the shared subnet, regardless of which account owns them. This is because route tables govern the routing of traffic for all resources within the subnet."
Can you share an EC2 instance directly using AWS Resource Access Manager (RAM)?,"No, you cannot directly share EC2 instances using RAM","Yes, you can share any EC2 instance",Only EC2 instances without public IP addresses can be shared,You must stop the EC2 instance before sharing it,"No, you cannot directly share EC2 instances using RAM. However, you can share other resources, like VPC subnets, that EC2 instances might use."
How does AWS Resource Access Manager (RAM) ensure that shared resources are not unintentionally exposed to the public internet?,By requiring explicit permission grants for access to shared resources,By automatically placing all shared resources in private subnets,By automatically encrypting all data transferred through shared resources,By disabling internet access for all shared resources,AWS RAM ensures that shared resources are not unintentionally exposed to the public internet by requiring explicit permission grants for access to shared resources. This means that only authorised principals can access the resources.
What is the benefit of integrating AWS Resource Access Manager (RAM) with AWS Service Catalog?,To allow users to launch pre-approved resources into shared subnets,To automate the creation of AWS Service Catalog portfolios,To track the usage of AWS Service Catalog products,To manage the cost allocation of AWS Service Catalog resources,"Integrating AWS RAM with AWS Service Catalog allows users to launch pre-approved resources directly into shared subnets, streamlining the deployment process and ensuring consistent configurations."
What is the typical use-case of using AWS RAM to share a network interface?,Sharing a common network interface with multiple EC2 instances for high availability,Sharing an Elastic Fabric Adapter interface for high performance computing,Sharing a virtual network interface with other AWS accounts,You cannot use AWS RAM to share a network interface,You cannot use AWS RAM to share a network interface. These interfaces are directly tied to EC2 instances and their networking configurations.
What is the primary purpose of AWS Resource Access Manager (RAM)?,To securely share AWS resources across AWS accounts or within an organisation,To monitor resource utilisation and performance across AWS accounts,To automate the deployment of resources across multiple AWS accounts,To manage IAM roles and policies centrally across AWS accounts,"AWS RAM allows you to share your AWS resources with any AWS account or within your AWS Organization. You can share resources such as VPC subnets, Transit Gateways, and more."
Which of the following is NOT a type of resource that can be shared using AWS RAM?,EC2 Instances,VPC Subnets,Transit Gateways,License Manager configurations,EC2 instances cannot be directly shared via AWS RAM. Other options are valid resource types for sharing.
"In AWS RAM, what is a 'resource share'?",A container for resources to be shared and the principals they are shared with,A snapshot of resource configurations to be replicated across accounts,A tool for optimising the cost of shared resources,A log of all sharing activities performed within the organisation,"A resource share is the central entity in AWS RAM that contains the resources you want to share and the principals (AWS accounts, AWS Organisations entities) you are sharing them with."
"When sharing resources via AWS RAM, what is a 'principal'?",The AWS account or AWS Organisation entity with whom the resources are shared,The IAM role responsible for managing shared resources,The AWS service that manages the resource sharing process,The cost centre associated with the shared resources,"A principal is the entity (AWS account, Organisational Unit, or Organisation) that is granted access to the shared resources through AWS RAM."
What is the benefit of using AWS RAM within an AWS Organisation?,Simplifies resource sharing and reduces administrative overhead compared to sharing between individual accounts,Automatically optimises resource costs across the entire organisation,Provides enhanced security monitoring and compliance reporting for shared resources,Enables cross-region resource replication for disaster recovery,"Sharing within an organisation using AWS RAM is simplified, reducing administrative complexity as permission management can be delegated and controlled centrally at the organisational level."
"If you want to share an AWS resource with an external AWS account using AWS RAM, what is the first step you need to take?",Ensure resource-based policies allow access from external accounts,Enable cross-account IAM roles,Create a customer gateway,Configure VPC peering,You need to ensure the resource-based policies of the resources allow sharing with external accounts as a pre-requisite for AWS RAM to facilitate sharing.
What is the purpose of RAM's 'Managed Permissions'?,To define the level of access principals have to shared resources,To restrict the geographic regions where shared resources can be accessed,To automate the creation of IAM roles for shared resources,To encrypt data transmitted between shared resources and principals,"Managed permissions define the level of access principals have to the shared resources, specifying what actions they can perform on those resources."
Which statement is true about Resource Access Manager (RAM) pricing?,There is no additional charge for using AWS RAM,AWS RAM charges based on the number of resource shares created,AWS RAM charges based on the amount of data transferred between shared resources,AWS RAM charges based on the number of principals accessing shared resources,AWS RAM is provided at no additional charge. You only pay for the resources you are sharing.
You need to ensure that a specific resource cannot be shared via AWS RAM. What action can you take?,Modify the resource's resource-based policy to deny sharing through AWS RAM,Disable AWS RAM for the entire AWS account,Delete the resource,Remove the resource from the AWS Organisation,"Modifying the resource-based policy is the correct way to control resource sharing at the resource level, denying access via AWS RAM."
What is the relationship between AWS RAM and AWS Organisations?,"AWS RAM simplifies resource sharing within an AWS Organisation, making it easier to manage resource access",AWS RAM is required to set up an AWS Organisation,AWS RAM automatically creates AWS Organisations based on resource sharing patterns,AWS RAM replaces the need for AWS Organisations when sharing resources,"AWS RAM streamlines resource sharing within an AWS Organisation, enhancing resource management and access control within the defined organisational structure."
How can you discover which AWS resources are shared with your account using AWS RAM?,Through the AWS RAM console or API,By examining the IAM role policies,By analysing CloudTrail logs,By querying the AWS Config service,The AWS RAM console and API provide the primary means to discover what resources have been shared with your AWS account.
Which of the following is a common use case for sharing VPC subnets using AWS RAM?,Centralised management of network infrastructure across multiple accounts,Automating the creation of VPCs across multiple regions,Encrypting network traffic between VPCs,Enabling cross-account access to S3 buckets,"Sharing VPC subnets allows for a centralised network management approach, enabling resources in different accounts to utilise the same subnets."
What is the significance of 'resource-based policies' when using AWS RAM?,They control which principals can access the resource via AWS RAM,They define the cost allocation for shared resources,They determine the geographic region where shared resources are deployed,They manage the encryption keys used for shared resources,"Resource-based policies play a crucial role in controlling access to resources shared via AWS RAM, explicitly defining which principals are allowed to access the resource."
How does AWS RAM integrate with AWS CloudTrail?,AWS RAM actions are logged in CloudTrail for auditing and monitoring purposes,AWS RAM uses CloudTrail logs to optimise resource sharing patterns,AWS RAM automatically creates CloudTrail trails for shared resources,AWS RAM encrypts CloudTrail logs related to shared resources,"AWS RAM actions, such as creating and modifying resource shares, are logged in CloudTrail, providing an audit trail for security and compliance purposes."
Which AWS service can be shared using AWS RAM to allow multiple accounts to access the same service quota?,Service Quotas,CloudWatch,IAM,EC2,Service Quotas can be shared using RAM allowing multiple accounts to access the same quota.
Which of the following is a benefit of using AWS RAM over creating custom IAM roles for resource sharing?,Simplified management and reduced administrative overhead,Enhanced security features beyond IAM roles,Automatic cost optimisation for shared resources,Greater control over resource access policies,"AWS RAM offers a simplified management experience, reducing the administrative burden associated with manually creating and managing complex IAM roles for resource sharing."
You want to share an AWS License Manager configuration with another account using AWS RAM. What permissions are needed on the receiving account?,The receiving account needs permissions to associate the shared configuration with their resources,The receiving account needs full administrative permissions to manage the shared configuration,The receiving account needs no specific permissions; sharing is automatic,The receiving account needs permissions to modify the shared configuration's settings,"The receiving account needs appropriate permissions to utilise the shared configuration, typically to associate it with their resources."
What is the impact of deleting a resource share in AWS RAM?,Shared resources are no longer accessible to the principals they were shared with,The underlying AWS resources are also deleted,The resource share is archived and can be restored later,The billing for shared resources is transferred to the principal's account,"Deleting a resource share revokes access to the shared resources from the previously associated principals, effectively stopping the sharing arrangement."
"When sharing a Transit Gateway using AWS RAM, what are the implications for routing?",The receiving accounts can create routes to the shared Transit Gateway from their VPCs,The sharing account automatically controls all routing decisions for the Transit Gateway,The receiving accounts must use a default route provided by the sharing account,The sharing account cannot modify routing tables after sharing the Transit Gateway,The receiving accounts need to configure their own routes to utilise the shared Transit Gateway for inter-VPC or on-premises connectivity.
How does AWS RAM help with regulatory compliance requirements?,By providing a central view of all shared resources and their access policies,By automatically encrypting all shared resources,By generating compliance reports for shared resources,By restricting resource sharing to specific geographic regions,"AWS RAM can aid in compliance by providing a centralised view of shared resources and their associated access policies, facilitating auditing and governance."
What is the difference between sharing a resource within an AWS Organisation versus sharing with an individual AWS account via RAM?,Sharing within an organisation simplifies management as permissions can be inherited and centrally controlled,Sharing with an individual account offers greater security due to isolated access policies,Sharing within an organisation incurs additional costs compared to individual account sharing,Sharing with an individual account allows for more customisation of resource access,Sharing within an organisation benefits from simplified management due to the hierarchical structure and centralised control offered by AWS Organisations.
You need to share a specific set of VPC subnets with multiple AWS accounts. What is the most efficient way to do this using AWS RAM?,Create a resource share and add all subnets and accounts to it,Create separate resource shares for each subnet and account combination,Use AWS CloudFormation to automate the creation of individual shares,Manually configure network ACLs to allow access from each account,Creating a single resource share containing all the subnets and accounts provides the most efficient and manageable approach for sharing resources with multiple principals.
Which of the following is NOT a valid identity type that can be specified as a principal in an AWS RAM resource share?,IAM User,AWS Account ID,AWS Organisation,Organisational Unit,"IAM users can't be directly added as principals to an AWS RAM resource share. You can only use AWS Accounts, AWS Organizations or Organizational Units."
Can you share resources across different AWS regions using AWS RAM?,"No, AWS RAM only supports sharing resources within the same AWS region","Yes, but only for specific types of resources","Yes, but it requires additional configuration","Yes, it's enabled by default for all supported resources",AWS RAM does not support sharing resources across different AWS regions. Resource sharing is limited to within the same region.
What happens if the sharing AWS account is suspended?,The principals lose access to the shared resources,The shared resources are automatically transferred to the principal's account,The resource share is paused until the account is reinstated,The principals are notified and given the option to take over ownership of the resources,"If the sharing account is suspended, the principals will no longer have access to the resources as the resource share is effectively terminated."
"When using AWS RAM to share a resource, who is responsible for monitoring and maintaining the shared resource?",The sharing account remains responsible for monitoring and maintaining the resource,The principal account becomes responsible for monitoring and maintaining the resource,Responsibility is shared equally between the sharing and principal accounts,AWS RAM automatically monitors and maintains the resource,"The sharing account retains responsibility for monitoring and maintaining the shared resource, even though other accounts have access to it."
Which of the following is NOT a resource-level permission that can be controlled when using AWS RAM with VPC Endpoints?,ec2:AcceptVpcEndpointConnections,ec2:CreateVpcEndpoint,ec2:DeleteVpcEndpoints,ec2:ModifyVpcEndpoint,`ec2:DeleteVpcEndpoints` is not related to the acceptance of VPC Endpoint Connections and controlling access to the VPC endpoint resources.
You have an AWS Organisation with multiple accounts and want to share a central NAT Gateway for outbound internet access. How can AWS RAM help?,By sharing the VPC subnet containing the NAT Gateway with all member accounts,By automatically routing traffic from member accounts through the central NAT Gateway,By creating separate NAT Gateways in each member account and managing them centrally,By enabling direct internet access for all member accounts through the central account,"By sharing the subnet in which the NAT Gateway resides, you can centralise and control the outbound internet access for your accounts."
What is the impact of deleting the AWS Organisation that owns a resource share in AWS RAM?,"All resource shares associated with the organisation are deleted, and principals lose access",The resource shares are automatically transferred to another AWS Organisation,The resource shares are paused until a new organisation is created,The principals are notified and given the option to take over ownership of the resources,"Deleting the AWS Organisation results in the deletion of the resource shares, and the principals will no longer have access to the shared resources."
Which AWS service can be shared using AWS RAM to simplify the management of cross-account networking for hybrid cloud environments?,Transit Gateway,Direct Connect Gateway,Virtual Private Gateway,Customer Gateway,Transit Gateway simplifies cross-account networking and can be shared using RAM.
How does AWS RAM affect the AWS Trusted Advisor checks related to shared resources?,Trusted Advisor checks are run independently on both the sharing and principal accounts,Trusted Advisor checks are only run on the sharing account,Trusted Advisor checks are only run on the principal account,AWS RAM disables Trusted Advisor checks for shared resources,Trusted Advisor checks are run independently on both the sharing and principal accounts providing recommendations for each account's perspective.
"When sharing resources across AWS accounts using AWS RAM, what is the recommended approach for managing access control?",Using resource-based policies in conjunction with IAM roles,Relying solely on IAM roles in the sharing account,Relying solely on resource-based policies in the principal accounts,Disabling IAM roles and using only AWS RAM permissions,A combination of resource-based policies and IAM roles provides a flexible and secure method for managing access control to shared resources.
Which of the following is NOT a valid use case for AWS RAM?,Sharing VPC CIDR blocks between accounts,Sharing custom AMI images,Sharing Route53 Resolver rules,Sharing AWS CloudFormation templates,"Custom AMI images can be shared using the AWS Marketplace, but not directly using AWS RAM."
How can you automate the process of sharing AWS resources using AWS RAM?,Using AWS CloudFormation or the AWS SDK,Using AWS Config rules,Using AWS CloudTrail,Using AWS Systems Manager Automation,AWS CloudFormation and the AWS SDK provide the tools necessary to automate the process of creating and managing resource shares.
Which AWS RAM feature allows you to delegate the administration of a resource share to another principal?,Managed Permissions,Delegated Administrator,Resource-based Policies,Cross-account IAM roles,Managed Permissions control what access the principal has to a resource.
What type of network resource policies you cannot manage through AWS RAM?,Direct Connect Gateways,VPC Subnets,Transit Gateways,VPN Gateways,VPN Gateways' resource policies cannot be managed through AWS RAM.
What IAM policy permissions are needed to create a Resource Share in RAM?,ram:CreateResourceShare,ram:EnableSharingWithAwsOrganization,ram:TagResource,ram:UpdateResourceShare,The `ram:CreateResourceShare` permission is required to create Resource Shares.
What action is required by the receiving account when a VPC subnet is shared with them?,The receiving account needs to associate the shared subnet with their route table,The receiving account needs to accept the resource share invitation,The receiving account needs to create a new VPC Peering connection,The receiving account needs to update their IAM policies,"The receiving account does not need to accept a resource share invitation for VPC subnets, instead, the account needs to associate the shared subnet with their route table."
How can you monitor changes to AWS RAM resource shares over time?,Using AWS CloudTrail to track API calls related to AWS RAM,Using AWS Config to track resource configurations,Using AWS CloudWatch to monitor resource utilisation,Using AWS Trusted Advisor to check for compliance,"AWS CloudTrail tracks API calls and can be used to monitor the create, update, or delete operations of AWS RAM resources."
Which of the following is NOT a benefit of using AWS RAM?,Reduced operational overhead,Improved security posture,Simplified resource management,Automatic disaster recovery,Automatic disaster recovery is not a direct benefit of using AWS RAM.
What is the maximum number of accounts a shared AWS RAM Resource can be shared with?,There is no limit,100,10,5,AWS RAM Resources can be shared with unlimited amount of accounts.
Which of the following is NOT a typical reason for sharing resources using AWS RAM?,Centralised resource management,Cost optimisation,Enhanced security,Simplified compliance,Enhanced security is not a typical reason for sharing resources using AWS RAM as it's often related to security boundary violation.
You're a security engineer sharing a Transit Gateway using AWS RAM with another team's account. Which security consideration is MOST important to address?,Ensuring appropriate route table configurations to prevent unintended traffic routing,Encrypting all traffic traversing the Transit Gateway,Implementing strict network ACLs on the shared subnet,Requiring multi-factor authentication for all access to the Transit Gateway,Proper route table configurations are crucial to ensure traffic flows as intended and does not expose resources unintentionally.
"When a resource share is created in AWS RAM, what happens to existing IAM policies attached to the resources being shared?",The existing IAM policies remain in effect and are combined with the RAM permissions,The existing IAM policies are automatically removed,The existing IAM policies are overridden by the RAM permissions,The existing IAM policies are duplicated in the receiving account,Existing IAM policies remain in effect. AWS RAM permissions work in conjunction with the resource's existing permissions structure.
You are sharing an AWS resource using AWS RAM. The receiving account is unable to access the resource. What is the MOST likely cause?,The receiving account's IAM policies do not grant the necessary permissions,The resource has not been properly tagged,The resource is not supported by AWS RAM,AWS RAM is not enabled in the receiving account's region,"If the receiving account's IAM policies lack the necessary permissions, they will be unable to access the shared resource, regardless of the resource share configuration."
What is the best way to revoke access to a shared AWS resource when using AWS RAM?,Remove the principal from the resource share,Delete the shared resource,Modify the IAM policies on the shared resource,Disable AWS RAM in the sharing account,Removing the principal from the resource share immediately revokes their access to the resource. This is the most direct and intended method.
You are using AWS RAM to share resources within your AWS Organization. A new account is created in the Organization. How does this affect existing resource shares?,The new account automatically inherits access to any relevant resource shares,You must explicitly add the new account to the existing resource shares,The new account cannot access any resources until RAM is reconfigured,Resource shares are automatically recreated to include the new account,You need to explicitly add the new account to any relevant resource shares. Automatic inheritance isn't a default behaviour.
What is the primary purpose of AWS Resource Access Manager (RAM)?,To securely share AWS resources across AWS accounts,To manage AWS IAM users and roles,To monitor resource utilisation across AWS accounts,To automate resource provisioning in AWS,AWS RAM enables you to securely share AWS resources that you own with other AWS accounts within your organisation or AWS Organisations.
Which types of AWS resources can be shared using AWS Resource Access Manager (RAM)?,"Subnets, Transit Gateways, License Manager configurations","EC2 instances, S3 buckets, Lambda functions","IAM roles, CloudWatch dashboards, CloudFront distributions","RDS databases, DynamoDB tables, ECS clusters","AWS RAM supports sharing resources such as subnets, Transit Gateways, and License Manager configurations."
"When sharing a subnet using AWS Resource Access Manager (RAM), what happens to its route tables and network ACLs?",They remain under the ownership of the original account,They are automatically migrated to the sharing account,They become read-only for the sharing account,They are duplicated in the sharing account,The route tables and network ACLs associated with the subnet remain under the ownership and control of the original account that owns the subnet.
What is an AWS RAM resource share?,"A container for the resources you want to share, the principals you want to share with, and any associated permissions",A security group that controls access to shared resources,A set of IAM policies that define the permissions for shared resources,A log of all sharing activity across AWS accounts,"An AWS RAM resource share is a container that holds the resources you want to share, the principals you want to share with, and the associated permissions that govern access to these resources."
What is a 'principal' in the context of AWS Resource Access Manager (RAM)?,"An AWS account, organisation, or organisational unit with which you are sharing resources",The administrator responsible for managing resource sharing,A type of resource that can be shared using AWS RAM,A security token used to authenticate access to shared resources,"A principal in AWS RAM is an AWS account, organisation, or organisational unit that you're sharing resources with."
"With AWS Resource Access Manager (RAM), how are permissions managed for shared resources?",Using resource-based policies attached to the resource shares,Using IAM policies attached to the principal's IAM roles,Using network ACLs configured on the shared resources,Using security groups associated with the shared resources,Permissions for shared resources are managed using resource-based policies which are attached to the resource shares themselves.
"If an AWS account is removed from an AWS Organisation, what happens to its access to resources shared through AWS Resource Access Manager (RAM)?",Access is automatically revoked,Access remains until manually revoked by the resource owner,Access is limited to read-only permissions,Access is suspended temporarily,"When an AWS account is removed from an AWS Organisation, its access to resources shared through AWS RAM is automatically revoked."
What is the benefit of using AWS Resource Access Manager (RAM) to share resources within an AWS Organisation?,Simplified resource management and reduced operational overhead,Increased network bandwidth between accounts,Automatic cost optimisation for shared resources,Enhanced security monitoring across accounts,AWS RAM simplifies resource management by allowing you to share resources centrally and reduces operational overhead by eliminating the need to manage resource access on an individual basis.
Can resources shared using AWS Resource Access Manager (RAM) be accessed from outside the AWS Organisation?,"No, resources can only be shared within an AWS Organisation or with individual AWS accounts","Yes, resources can be shared with any AWS account, regardless of organisation membership","Yes, but only if the sharing account has explicitly granted external access","Yes, as long as the resource has a public endpoint","By default, resources can only be shared within your AWS Organisation or with individual AWS accounts. You can enable sharing with external accounts, but this requires careful consideration of security implications."
What is the purpose of AWS RAM's cross-account resource sharing feature?,To allow resource sharing between AWS accounts that are not part of the same organisation,To enable resource sharing between different AWS regions,To facilitate resource sharing between different IAM users,To allow resource sharing between different AWS services,AWS RAM's cross-account resource sharing feature allows you to share resources between AWS accounts that are not part of the same organisation. This enables collaboration and resource sharing across different teams or businesses.
What happens if the owner account of a resource shared via AWS Resource Access Manager (RAM) is suspended?,The shared resources become inaccessible to the principals.,The shared resources are automatically transferred to the principal accounts.,The resource sharing configuration is paused until the owner account is reactivated.,The principal accounts gain full ownership of the shared resources.,"If the owner account of a resource shared via AWS RAM is suspended, the shared resources become inaccessible to the principals, as the owner account is no longer active."
"When using AWS Resource Access Manager (RAM), what does the 'resource type' refer to?","The specific AWS service resource being shared (e.g., subnet, transit gateway)",The IAM role required to access the shared resource,The geographic region where the shared resource is located,The security policy applied to the shared resource,"The 'resource type' in AWS RAM refers to the specific AWS service resource that is being shared, such as a subnet, transit gateway, or license configuration."
Which AWS service integrates directly with AWS Resource Access Manager (RAM) to allow you to share network resources?,Amazon VPC,AWS CloudFormation,AWS IAM,Amazon EC2,Amazon VPC integrates directly with AWS RAM to allow you to share subnets and other network resources with other AWS accounts.
What is the recommended approach for managing access to shared resources within an AWS Organisation using AWS Resource Access Manager (RAM)?,Using AWS Organisations service control policies (SCPs) to define permissions boundaries,Using IAM roles and policies attached to individual users,Using security groups to control network traffic,Using resource-based policies attached to individual resources,"The recommended approach for managing access to shared resources within an AWS Organisation is to use AWS Organisations service control policies (SCPs) to define permissions boundaries, ensuring consistent access control across all accounts."
What action is required by the recipient account to access a resource shared via AWS Resource Access Manager (RAM)?,The recipient account must accept the resource share invitation.,The recipient account must create a new IAM role with specific permissions.,The recipient account must configure a VPN connection to the owner account.,The recipient account must enable cross-account access in their AWS account settings.,The recipient account must accept the resource share invitation before they can access the shared resource. This ensures that the recipient is aware of and agrees to the sharing arrangement.
What is the role of 'resource-based policies' in AWS Resource Access Manager (RAM)?,To define who can access the shared resources and what actions they can perform,To encrypt the data transferred between the sharing and recipient accounts,To monitor the usage of shared resources,To automate the provisioning of shared resources,"Resource-based policies in AWS RAM define who can access the shared resources and what actions they can perform, providing fine-grained control over access permissions."
Which of the following is NOT a valid use case for AWS Resource Access Manager (RAM)?,Sharing EC2 instances across accounts,Sharing subnets for centralised networking,Sharing License Manager configurations for software licensing,Sharing Transit Gateways for inter-VPC connectivity,AWS RAM does not support sharing EC2 instances directly. Other options are valid use cases.
How does AWS Resource Access Manager (RAM) contribute to centralising AWS resource management?,By providing a single interface for sharing resources across multiple AWS accounts,By automatically moving resources from individual accounts to a central management account,By enforcing a standard naming convention for all AWS resources,By providing a centralised dashboard for monitoring resource utilisation,"AWS RAM contributes to centralising AWS resource management by providing a single interface for sharing resources across multiple AWS accounts, simplifying the process of managing access to shared resources."
Which AWS service can be used in conjunction with AWS Resource Access Manager (RAM) to govern permissions across an entire AWS Organisation?,AWS Organisations,AWS CloudTrail,AWS Config,AWS Trusted Advisor,"AWS Organisations can be used in conjunction with AWS RAM to govern permissions across an entire AWS Organisation, enabling consistent and centralised access control."
What is the impact of deleting a resource share in AWS Resource Access Manager (RAM)?,Sharing is stopped and access is revoked for all principals,The shared resources are also deleted,The resource share is archived but not removed,The shared resources are moved to the recipient accounts,Deleting a resource share in AWS RAM stops the sharing and revokes access for all principals that were previously granted access through that resource share.
"Which type of resource sharing is typically used for sharing network infrastructure components, such as subnets, within an AWS Organisation?",AWS Resource Access Manager (RAM),AWS IAM roles,VPC Peering,AWS Direct Connect,"AWS Resource Access Manager (RAM) is typically used for sharing network infrastructure components, such as subnets, within an AWS Organisation, providing a centralised and controlled way to manage network resources."
What security considerations are important when using AWS Resource Access Manager (RAM) to share resources with external AWS accounts?,Careful evaluation of the resource-based policies and permissions granted,Enabling multi-factor authentication for all users in the recipient account,Requiring the recipient account to use a specific AWS region,Limiting the duration of access to the shared resources,"When sharing resources with external AWS accounts using AWS RAM, it's crucial to carefully evaluate the resource-based policies and permissions granted to ensure that you are not inadvertently granting excessive access or exposing sensitive data."
How can you monitor the sharing activity of resources managed by AWS Resource Access Manager (RAM)?,Using AWS CloudTrail to log API calls related to resource sharing,Using AWS Config to track changes to resource configurations,Using Amazon CloudWatch to monitor resource utilisation,Using AWS Trusted Advisor to identify potential security risks,"You can monitor the sharing activity of resources managed by AWS RAM by using AWS CloudTrail to log API calls related to resource sharing, providing an audit trail of all sharing actions."
What is the role of a 'customer managed permission' in the context of AWS RAM?,It allows you to define custom permissions to be used when sharing resources of supported resource types,It allows you to grant permissions to customers to access your AWS account,It allows you to manage permissions for AWS managed resources,It allows you to create custom IAM policies for your AWS resources,A 'customer managed permission' in the context of AWS RAM allows you to define custom permissions to be used when sharing resources of supported resource types. This allows for more granular control over the access being granted.
"When sharing a subnet using AWS RAM, what happens to the security group rules associated with EC2 instances launched in that subnet?",The security group rules are controlled by the owner of the subnet,The security group rules are controlled by the owner of the EC2 instances,The security group rules are merged between the subnet owner and the instance owner,The security group rules become read-only for both the subnet owner and the instance owner,"When sharing a subnet using AWS RAM, the security group rules associated with EC2 instances launched in that subnet are controlled by the owner of the EC2 instances. The subnet owner does not control the security group rules for instances in the shared subnet."
What is the primary difference between AWS RAM and AWS IAM roles for cross-account access?,"AWS RAM shares the actual resources, while IAM roles delegate access to resources","AWS RAM provides temporary access, while IAM roles grant permanent access","AWS RAM is used for sharing within an organisation, while IAM roles are used for sharing with external accounts","AWS RAM is used for sharing compute resources, while IAM roles are used for sharing data resources","The primary difference is that AWS RAM shares the actual resources (e.g., subnets), while IAM roles delegate access to resources. With RAM, the recipient account directly uses the shared resource. With IAM roles, the recipient account assumes a role in the owner account to access resources."
Which of the following statements is TRUE regarding the use of AWS RAM in a multi-account environment?,"AWS RAM simplifies the sharing of resources across multiple AWS accounts, reducing administrative overhead",AWS RAM requires each account to have identical IAM configurations,AWS RAM replaces the need for IAM roles for cross-account access,AWS RAM automatically creates resource backups in all participating accounts,"AWS RAM simplifies the sharing of resources across multiple AWS accounts, reducing administrative overhead by providing a centralised and controlled way to manage resource sharing."
You have shared a Transit Gateway using AWS RAM. What does the recipient account need to do to start using the Transit Gateway?,The recipient account needs to create a Transit Gateway attachment from their VPC to the shared Transit Gateway.,The recipient account needs to modify the route tables in the sharing account.,The recipient account needs to create a new Transit Gateway in their own account.,The recipient account needs to contact AWS Support to enable access.,"After a Transit Gateway is shared using AWS RAM, the recipient account needs to create a Transit Gateway attachment from their VPC to the shared Transit Gateway. This establishes the connection between the recipient's VPC and the shared Transit Gateway."
What is the relationship between AWS Resource Access Manager (RAM) and AWS License Manager?,AWS RAM can be used to share License Manager configurations across accounts,AWS RAM replaces the functionality of AWS License Manager,AWS License Manager is a prerequisite for using AWS RAM,AWS RAM and AWS License Manager are mutually exclusive services,"AWS RAM can be used to share License Manager configurations across accounts, allowing you to centrally manage and distribute software licenses across your AWS Organisation."
"When sharing resources using AWS RAM, what is the 'Allow external principals to access your shared AWS resources' setting used for?",To allow sharing with AWS accounts outside of your AWS Organisation,To enable encryption for data transferred between accounts,To allow resources in other AWS regions to be shared,To allow IAM users outside of your account to access the resources,"The 'Allow external principals to access your shared AWS resources' setting is used to allow sharing with AWS accounts outside of your AWS Organisation, enabling collaboration with trusted partners or customers."
Which of the following is a key benefit of using AWS Resource Access Manager (RAM) for sharing VPC subnets?,Simplified network management and reduced operational complexity,Automatic encryption of data in transit between subnets,Increased network bandwidth between VPCs,Automatic scaling of subnet capacity,"Using AWS RAM for sharing VPC subnets simplifies network management and reduces operational complexity by allowing you to centrally manage and share subnets across multiple AWS accounts, promoting consistent network configurations and reducing the need for duplicated resources."
How does AWS Resource Access Manager (RAM) ensure that shared resources are accessed securely?,By using resource-based policies to define access permissions,By automatically encrypting all data in transit,By requiring multi-factor authentication for all users,By isolating shared resources in dedicated virtual private clouds,"AWS RAM ensures that shared resources are accessed securely by using resource-based policies to define access permissions, providing fine-grained control over who can access the shared resources and what actions they can perform."
"If you have an AWS Organisation with multiple accounts, what is the best practice for centrally managing shared resources using AWS RAM?",Designate a management account to own and share all resources using AWS RAM,Require each account to manage its own resource sharing configurations,Use AWS CloudFormation to automate resource sharing across accounts,Manually configure resource sharing settings in each account,"The best practice is to designate a management account to own and share all resources using AWS RAM. This centralises the management of shared resources, simplifies administration, and ensures consistent access control policies across the organisation."
"When a resource is shared using AWS Resource Access Manager (RAM), who is responsible for the cost of the shared resource?",The account that owns the resource,The account that uses the shared resource,The cost is split evenly between the owner and the user,AWS determines the cost allocation based on usage patterns,The account that owns the resource is responsible for the cost of the shared resource. The shared resource is still owned and managed by the original account.
You need to share an AWS Transit Gateway with another AWS account. Which of the following steps is necessary using AWS RAM?,"Create a resource share, add the Transit Gateway, and specify the recipient account",Create a cross-account IAM role in both accounts,Configure VPC peering between the accounts,Launch a new Transit Gateway in the recipient account,"The necessary step using AWS RAM is to create a resource share, add the Transit Gateway to the resource share, and specify the recipient account with whom you want to share the Transit Gateway."
Which of the following is a benefit of using AWS RAM with Transit Gateway?,Simplified inter-VPC connectivity and centralized management of network resources,Automatic encryption of traffic flowing through the Transit Gateway,Increased bandwidth for network traffic,Automatic scaling of Transit Gateway capacity,"AWS RAM simplifies inter-VPC connectivity and centralizes the management of network resources when used with Transit Gateway, by allowing you to share a Transit Gateway across multiple AWS accounts within your organization."
Which AWS service can you integrate with AWS RAM to manage user identities and access across multiple AWS accounts?,AWS IAM Identity Center (Successor to AWS SSO),AWS Directory Service,Amazon Cognito,AWS Shield,"AWS IAM Identity Center (Successor to AWS SSO) can be integrated with AWS RAM to manage user identities and access across multiple AWS accounts, providing a centralized identity management solution."
"Which of the following actions is required when an account leaves an AWS Organisation, regarding resources shared via AWS RAM?",The account's access to shared resources is automatically revoked,The account retains access to shared resources indefinitely,The account must manually terminate the sharing relationship,The account's resources are automatically transferred to the organisation's management account,"When an account leaves an AWS Organisation, the account's access to shared resources is automatically revoked, ensuring that the departing account no longer has access to the organisation's resources."
What is the purpose of a 'resource share invitation' in AWS Resource Access Manager (RAM)?,To notify the recipient account that a resource is being shared and require them to accept the sharing,To automatically grant access to shared resources without requiring explicit approval,To allow the resource owner to track who is accessing the shared resources,To provide detailed documentation about the shared resources,"A 'resource share invitation' in AWS RAM is used to notify the recipient account that a resource is being shared with them, and it requires them to explicitly accept the sharing before they can access the shared resource. This ensures that the recipient is aware of and approves the sharing arrangement."
Which statement best describes the relationship between AWS RAM and service-linked roles?,AWS RAM uses service-linked roles to manage permissions for sharing resources with other accounts,AWS RAM replaces the need for service-linked roles in AWS,Service-linked roles are used to grant access to AWS RAM,Service-linked roles are created by AWS RAM for each shared resource,AWS RAM uses service-linked roles to manage permissions for sharing resources with other accounts. These roles are pre-defined by AWS and automatically grant AWS RAM the necessary permissions to perform actions on your behalf.
You want to ensure that only specific AWS accounts within your Organisation can access resources shared via AWS RAM. How can you achieve this?,By specifying the account IDs directly in the resource share configuration,By creating a separate AWS Organisation for those accounts,By using IAM policies attached to the resources,By using tags to identify the accounts,You can specify the account IDs directly in the resource share configuration to ensure that only specific AWS accounts within your Organisation can access the shared resources.
What happens if a resource shared using AWS Resource Access Manager (RAM) is deleted by the owning account?,The shared resource is immediately inaccessible to the recipient accounts,The shared resource remains accessible to the recipient accounts for a grace period,The recipient accounts automatically receive a copy of the resource,The recipient accounts are notified of the pending deletion and can prevent it,"If a resource shared using AWS RAM is deleted by the owning account, the shared resource is immediately inaccessible to the recipient accounts, as the resource no longer exists."
Which of the following is NOT a supported resource type for sharing via AWS RAM?,AWS CloudFormation Stacks,Amazon VPC Subnets,AWS Transit Gateways,AWS License Manager Configurations,AWS CloudFormation Stacks are not a supported resource type for sharing via AWS RAM.
You are sharing a VPC subnet using AWS RAM. How do you control which resources the recipient account can launch in that subnet?,By using resource-based policies to restrict the resource types,By using IAM policies in the recipient account,By using network ACLs on the subnet,By using security groups attached to the resources,You can control which resources the recipient account can launch in the shared VPC subnet by using resource-based policies to restrict the resource types that can be launched.
Which AWS CLI command is used to create a resource share using AWS RAM?,aws ram create-resource-share,aws ram share-resource,aws ram add-resource-share,aws ram new-resource-share,The `aws ram create-resource-share` command is used to create a new resource share using AWS RAM.
What is the purpose of the 'permission ARNs' attribute when creating an AWS RAM resource share?,To specify the permissions to grant to the principals for the shared resource,To specify the AWS regions where the resource can be shared,To specify the IAM roles that can access the shared resource,To specify the tags that should be applied to the shared resource,The 'permission ARNs' attribute when creating an AWS RAM resource share is used to specify the permissions to grant to the principals for the shared resource. These permission ARNs point to AWS managed or customer managed permissions.
"When sharing a resource via AWS RAM, what is the maximum number of principals that can be associated with a single resource share?",There is no fixed limit,50,100,200,"There is no fixed limit to the number of principals that can be associated with a single resource share in AWS RAM. The limit is dynamic and depends on various factors, including the size and complexity of the resource share."
What is the purpose of the 'tags' parameter in AWS RAM resource shares?,To add metadata to the resource share for identification and filtering,To control access to the resource share based on tag values,To specify the AWS services that can access the resource share,To specify the accounts that can access the resource share,The 'tags' parameter in AWS RAM resource shares is used to add metadata to the resource share for identification and filtering. Tags can help you organise and manage your resource shares effectively.
You want to share resources from multiple accounts in your AWS Organisation to a single central account using AWS RAM. How can you achieve this?,Create a resource share in each account and share it with the central account,Create a single resource share in the central account and import resources from the other accounts,Share the entire AWS Organisation with the central account,It's not possible to share resources from multiple accounts to a single account using AWS RAM,You can achieve this by creating a resource share in each account and sharing it with the central account. This allows each account to independently share its resources with the central account.
What is the primary function of AWS Resource Access Manager (RAM)?,Sharing AWS resources across AWS accounts,Monitoring resource utilisation,Automating resource deployment,Managing IAM permissions,"AWS RAM enables you to securely share your AWS resources across AWS accounts, within your organisation or organisational units."
Which AWS resources can be shared using AWS Resource Access Manager (RAM)?,"Subnets, Transit Gateways, and License Manager configurations","EC2 instances, S3 buckets, and Lambda functions","CloudFront distributions, Route 53 hosted zones, and DynamoDB tables","IAM roles, CloudWatch alarms, and VPC peering connections","RAM supports sharing of resources like subnets, Transit Gateways, License Manager configurations, and more. It does not support sharing compute, storage, or serverless resources directly."
Which of the following is a prerequisite for sharing resources using AWS Resource Access Manager (RAM)?,Enabling AWS Organisations,Configuring VPC Peering,Creating an IAM role with specific permissions,Setting up a cross-account trust relationship,"To share resources using AWS RAM, you must first enable AWS Organisations in your account."
What is a Resource Share in the context of AWS Resource Access Manager (RAM)?,A container for the resources being shared and the accounts they are shared with,A security group that controls access to shared resources,A virtual private cloud used for resource sharing,An IAM policy that defines permissions for shared resources,A Resource Share is a container that holds the resources you want to share and specifies the AWS accounts or organisations you want to share them with.
"With AWS Resource Access Manager (RAM), what is the effect of deleting a Resource Share?",Shared resources are no longer accessible by the consumer accounts,The shared resources are permanently deleted,IAM policies associated with the resources are revoked,The AWS accounts that consume the resources are suspended,Deleting a Resource Share immediately revokes access to the shared resources for the consumer accounts.
"When using AWS Resource Access Manager (RAM) to share a VPC subnet, what is required to enable cross-account traffic flow?",Route tables in the VPC must be updated to include the consumer account subnets,Network ACLs must be modified to allow traffic between the accounts,IAM roles must be created in both accounts,A VPN connection must be established between the accounts,"When sharing a VPC subnet with RAM, you must update the VPC route tables to route traffic to the consumer account's subnets or resources. Network ACLs will also need to be modified to allow traffic."
What type of sharing is supported by AWS Resource Access Manager (RAM),Sharing within an AWS Organisation and with individual AWS accounts,Sharing only within the same AWS account,Sharing only with AWS Marketplace subscriptions,Sharing only with AWS Partner Network (APN) partners,AWS RAM allows you to share resources both within your AWS Organisation and with individual AWS accounts outside of your organisation.
How does AWS Resource Access Manager (RAM) simplify cross-account management?,By centralising resource sharing and permission management,By automatically migrating resources between accounts,By providing a single console for all AWS services,By eliminating the need for IAM roles,AWS RAM simplifies cross-account management by providing a centralised location to share resources and manage permissions across multiple AWS accounts.
What IAM permissions are necessary to allow an AWS account to accept an AWS Resource Access Manager (RAM) share?,Permissions to manage the resource share invitation,Permissions to create resource shares,Permissions to modify the resources being shared,Permissions to create IAM roles in the provider account,"The recipient account needs permissions to manage the resource share invitation, allowing them to accept or reject the share."
What is the benefit of using AWS Resource Access Manager (RAM) to share resources instead of creating custom IAM policies?,Simplified management and reduced administrative overhead,Increased security and compliance,Improved performance and scalability,Lower costs for resource utilisation,AWS RAM simplifies resource sharing by providing a managed service that reduces the administrative overhead associated with creating and maintaining custom IAM policies for cross-account access.
"When using AWS Resource Access Manager (RAM) to share a Transit Gateway, what must the consumer account do to utilise it?",Create a Transit Gateway attachment to the shared Transit Gateway,Recreate the Transit Gateway in their account,Request access through AWS Support,Configure VPC Peering with the provider account's VPC,The consumer account must create a Transit Gateway attachment from their VPC or other resource to the shared Transit Gateway.
Which statement best describes the security model of AWS Resource Access Manager (RAM)?,It leverages IAM policies and resource-based policies to control access,It relies solely on network ACLs to control access,It uses a centralised access control list (ACL) for all resources,It depends on the AWS Shared Responsibility Model,"AWS RAM leverages IAM policies and resource-based policies to control access to shared resources, ensuring secure and granular access control."
Can you share EC2 instances directly using AWS Resource Access Manager (RAM)?,"No, you cannot share EC2 instances directly using RAM","Yes, you can share individual EC2 instances","Yes, you can share an entire Auto Scaling group","Yes, you can share EC2 launch templates",You cannot directly share EC2 instances with RAM.
When should you consider using AWS Resource Access Manager (RAM)?,When you need to share AWS resources across multiple AWS accounts within your organisation,When you need to migrate data between AWS regions,When you need to back up your AWS resources,When you need to monitor the cost of your AWS resources,"AWS RAM is ideal when you need to share AWS resources, such as subnets or Transit Gateways, across multiple AWS accounts within your organisation."
What happens when a shared resource in AWS Resource Access Manager (RAM) is deleted by the owning account?,The resource is automatically removed from all resource shares and becomes inaccessible to consumer accounts,The resource continues to be available to consumer accounts until they manually remove it,The deletion is blocked until all consumer accounts disassociate from the resource,Consumer accounts receive a notification but can continue using the resource,"When the owning account deletes a shared resource, it is automatically removed from all resource shares and becomes inaccessible to consumer accounts."
Which of the following is NOT a valid use case for AWS Resource Access Manager (RAM)?,Sharing Amazon S3 buckets,Sharing VPC subnets,Sharing Transit Gateways,Sharing License Manager configurations,RAM does not support sharing S3 buckets.
What is the purpose of 'aws:ram:shareablePrincipals' in the context of resource-based policies and AWS Resource Access Manager (RAM)?,"It specifies the principals that can be shared with, such as accounts or organisations",It defines the IAM role that can access the resource,It restricts access to specific IP addresses,It identifies the owner of the resource,'aws:ram:shareablePrincipals' in resource-based policies specifies the principals (AWS accounts or AWS Organizations) that the resource can be shared with using AWS RAM. This condition key enables the resource owner to control which principals can be included in a resource share.
How does AWS Resource Access Manager (RAM) integrate with AWS Organisations?,It allows sharing of resources within the organisation's organisational units (OUs),It automatically creates IAM roles in member accounts,It replaces the need for IAM policies,It manages the AWS Organisations hierarchy,"AWS RAM integrates seamlessly with AWS Organisations, allowing you to share resources within the organisation's organisational units (OUs) for simplified management."
"If an AWS account is removed from an AWS Organisation, what happens to its access to shared resources via AWS Resource Access Manager (RAM)?",The account immediately loses access to all shared resources,The account retains access for a grace period,The account's access depends on the resource-based policies,The account's access is transferred to another account in the organisation,"When an account is removed from an AWS Organisation, it immediately loses access to all resources shared through AWS RAM."
Which of the following is an advantage of using AWS Resource Access Manager (RAM) over setting up cross-account access manually?,Simplified management and centralised control,Higher levels of security,Lower cost of implementation,Faster resource provisioning,"AWS RAM provides simplified management and centralised control compared to setting up cross-account access manually, reducing administrative overhead and improving consistency."
"When sharing a resource using AWS Resource Access Manager (RAM), what determines the permissions granted to the consumer account?",The IAM policies attached to the resource and the resource-based policies,The IAM roles assumed by the consumer account,The AWS Managed Policies attached to the consumer account,The network ACLs associated with the resource,The permissions granted to the consumer account are determined by the IAM policies attached to the resource and the resource-based policies that control access to the resource.
What is the role of the AWS RAM console?,To manage and monitor resource shares,To configure AWS Organisations settings,To create IAM roles,To monitor resource utilisation,"The AWS RAM console allows you to manage and monitor resource shares, including creating, modifying, and deleting resource shares."
What is the main advantage of sharing VPC subnets via AWS Resource Access Manager (RAM)?,Centralised management of network resources,Reduced latency for cross-account communication,Increased security for shared resources,Simplified cost allocation,Sharing VPC subnets with RAM centralises the management of network resources.
Which condition key can be used in a resource-based policy to control resource sharing with AWS Resource Access Manager (RAM)?,ram:PrincipalType,ec2:ResourceTag,s3:ExistingObjectTag,dynamodb:AttributeName,"The `ram:PrincipalType` condition key is used in resource-based policies to control resource sharing with AWS RAM, allowing you to specify whether resources can be shared with AWS accounts or organisations."
"In AWS Resource Access Manager (RAM), what is a 'Managed Permission'?",A set of permissions defined by AWS that can be attached to a resource share,A custom IAM policy that defines access to shared resources,An AWS CloudTrail log that tracks resource sharing activity,A tool for monitoring resource utilisation across shared accounts,"A 'Managed Permission' in AWS RAM is a set of permissions defined by AWS that can be attached to a resource share, simplifying the management of permissions for shared resources."
Which of the following is a valid resource type that can be shared via AWS RAM?,AWS CloudFormation StackSets,AWS Lambda functions,Amazon S3 buckets,Amazon EC2 Instances,CloudFormation StackSets can be shared.
Which AWS service is required for AWS RAM to function correctly when sharing resources across multiple AWS accounts?,AWS Organisations,AWS IAM,AWS CloudTrail,AWS Config,AWS Organisations is required for AWS RAM to function correctly when sharing resources across multiple AWS accounts.
What is the key benefit of using AWS RAM when managing complex AWS environments with multiple accounts?,It simplifies resource sharing and centralises management,It automatically scales resources based on demand,It encrypts data at rest and in transit,It provides detailed cost allocation reports,"AWS RAM simplifies resource sharing and centralises management, making it easier to manage complex AWS environments with multiple accounts."
"When a resource owner shares a resource using AWS RAM, what happens to the existing IAM policies associated with that resource?",They remain in effect and control access to the resource,They are automatically replaced by resource-based policies,They are temporarily disabled,They are automatically duplicated in the consumer account,"The existing IAM policies remain in effect and control access to the resource, in addition to any resource-based policies."
What should you consider when sharing a VPC subnet across accounts using AWS RAM?,Ensure route tables and network ACLs are correctly configured,Ensure IAM roles are properly assigned,Ensure security groups are properly configured,Ensure KMS keys are properly configured,"When sharing a VPC subnet across accounts using AWS RAM, ensure that the route tables and network ACLs are correctly configured to allow traffic flow between the accounts."
Which AWS service can you use to monitor the activity and usage of shared resources managed by AWS Resource Access Manager (RAM)?,AWS CloudTrail,AWS CloudWatch,AWS Config,AWS Trusted Advisor,You can use AWS CloudTrail to monitor the activity and usage of shared resources managed by AWS Resource Access Manager (RAM).
What is the recommended approach for granting granular access to shared resources via AWS RAM?,Use resource-based policies to control access,Use IAM roles to control access,Use security groups to control access,Use network ACLs to control access,The recommended approach for granting granular access to shared resources via AWS RAM is to use resource-based policies.
What is a typical use case for sharing a Transit Gateway using AWS Resource Access Manager (RAM)?,Centralising network connectivity for multiple accounts,Creating a disaster recovery site in another region,Improving the performance of database replication,Securing access to S3 buckets,Sharing a Transit Gateway centralises network connectivity for multiple accounts.
Which of the following is NOT a limitation of AWS Resource Access Manager (RAM)?,Limited support for sharing IAM roles,Certain resources cannot be shared,Requires AWS Organisations,Shares are region specific,AWS RAM does not support the sharing of IAM roles.
How can you ensure that a shared resource in AWS Resource Access Manager (RAM) is used correctly by consumer accounts?,Define clear usage guidelines and communicate them to consumer accounts,Enforce usage restrictions with IAM policies,Monitor resource utilisation with AWS CloudWatch,Automatically terminate unused resources,Defining clear usage guidelines and communicating them to consumer accounts is crucial for ensuring that shared resources are used correctly.
What is the key difference between sharing resources using AWS Resource Access Manager (RAM) and using VPC peering?,RAM provides a centralised and managed approach to resource sharing,VPC peering is more secure,VPC peering supports more resource types,RAM is easier to configure,"RAM provides a centralised and managed approach to resource sharing, simplifying cross-account management compared to VPC peering."
What happens if an AWS account accepts a resource share invitation and then leaves the AWS Organisation?,The account loses access to the shared resources,The account retains access to the shared resources indefinitely,The account's access is transferred to another account,The share is automatically deleted,The account loses access to the shared resources when it leaves the AWS Organisation.
What is the primary advantage of using AWS Resource Access Manager (RAM) for sharing AWS License Manager configurations?,Centralised license management and cost control,Automated license compliance,Simplified software deployment,Improved application performance,Sharing AWS License Manager configurations via RAM enables centralised license management and cost control across multiple AWS accounts.
How does AWS Resource Access Manager (RAM) contribute to improving operational efficiency in an AWS environment?,By streamlining resource sharing and reducing administrative overhead,By automating infrastructure deployment,By enhancing security and compliance,By optimising resource utilisation,AWS RAM improves operational efficiency by streamlining resource sharing and reducing administrative overhead.
Which of the following is a key factor to consider when designing a multi-account AWS environment using AWS Resource Access Manager (RAM)?,Resource sharing strategy and permission management,Network bandwidth and latency,Cost optimisation and budgeting,Security and compliance requirements,A key factor is the resource sharing strategy and permission management to ensure that resources are shared efficiently and securely.
"When using AWS RAM, what should you do to ensure resources are shared securely?",Implement least-privilege access using resource-based policies and IAM policies,Encrypt all shared resources,Use strong passwords for all AWS accounts,Disable public access to all resources,Implementing least-privilege access using resource-based policies and IAM policies ensures that resources are shared securely and only necessary permissions are granted.
Which AWS service can be used with AWS RAM to provide enhanced security for shared resources?,AWS Key Management Service (KMS),AWS CloudHSM,AWS Shield,AWS WAF,AWS Key Management Service (KMS) can be used to provide enhanced security for shared resources by encrypting the data and controlling access to the encryption keys.
How does AWS Resource Access Manager (RAM) help in achieving compliance requirements?,By providing centralised control over resource sharing and access,By automating compliance audits,By encrypting all data at rest,By providing real-time security alerts,"AWS RAM helps in achieving compliance requirements by providing centralised control over resource sharing and access, ensuring that resources are shared in accordance with compliance policies."
"In AWS Resource Access Manager (RAM), what are resource-based policies used for?",To define who can access a shared resource,To define the cost of the shared resource,To define the performance characteristics of the shared resource,To define the size of the shared resource,"Resource-based policies in AWS RAM are used to define who (which AWS accounts or organisations) can access a shared resource, providing granular control over access permissions."
What is the impact on existing resources when AWS Organisations is enabled for AWS RAM?,Existing resources are unaffected unless explicitly shared,All existing resources are automatically shared,Existing resources are deleted,Existing resources are migrated to a new region,"When AWS Organisations is enabled for AWS RAM, existing resources are unaffected unless you explicitly share them."
You are sharing a Transit Gateway using AWS RAM. Which action is required in the consumer account to use this shared TGW?,Create a TGW attachment,Create a route table,Create a VPC endpoint,Create a new VPC,The consumer account must create a TGW attachment to connect their resources to the shared TGW.
What is the best practice when sharing resources in a multi-account AWS environment using AWS Resource Access Manager (RAM)?,Adopt a least-privilege approach to permission management,Share all resources by default for ease of access,Use a single IAM role for all cross-account access,Ignore resource-based policies and rely solely on IAM policies,"The best practice is to adopt a least-privilege approach to permission management, ensuring that only necessary permissions are granted to consumer accounts."
Which AWS service allows you to track changes to resource shares created by AWS Resource Access Manager (RAM)?,AWS CloudTrail,AWS CloudWatch,AWS Config,AWS Trusted Advisor,"AWS CloudTrail allows you to track changes to resource shares created by AWS Resource Access Manager (RAM), providing auditability and compliance."
What is the primary benefit of using AWS Resource Access Manager (RAM) to share resources across accounts compared to setting up individual IAM roles and policies?,Simplified management and reduced administrative overhead,Enhanced security and compliance,Improved performance and scalability,Reduced cost of resource utilisation,RAM simplifies management and reduces administrative overhead when compared to manually creating IAM roles and policies for cross-account access.