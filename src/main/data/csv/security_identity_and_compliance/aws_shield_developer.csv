"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Shield?","To protect applications from DDoS attacks","To encrypt data at rest","To manage user identities","To monitor network traffic","AWS Shield is designed to protect applications running on AWS from Distributed Denial of Service (DDoS) attacks."
"Which AWS Shield tier provides enhanced DDoS protection and 24/7 support from the AWS DDoS Response Team (DRT)?","Shield Advanced","Shield Standard","Shield Basic","Shield Pro","Shield Advanced provides enhanced DDoS protection and access to the AWS DDoS Response Team (DRT) for 24/7 support."
"Which type of DDoS attack does AWS Shield help mitigate at the network and transport layers?","Volumetric attacks","Application-layer attacks","Data breaches","SQL injection attacks","Volumetric attacks, such as UDP floods and SYN floods, are mitigated by AWS Shield at the network and transport layers."
"Which AWS service integrates with AWS Shield Advanced to provide application-layer DDoS protection?","AWS WAF","AWS CloudTrail","AWS Config","AWS IAM","AWS WAF (Web Application Firewall) integrates with Shield Advanced to provide application-layer DDoS protection."
"What is the default level of protection offered by AWS Shield Standard?","Automatic inline mitigations for common infrastructure attacks","No DDoS protection","Limited DDoS protection with best-effort support","Advanced DDoS protection with 24/7 support","AWS Shield Standard provides automatic inline mitigations for common infrastructure attacks, at no additional charge."
"Which AWS resource is NOT automatically protected by AWS Shield Standard?","Amazon EC2 instances","Amazon CloudFront distributions","Elastic Load Balancers (ELB)","Amazon Route 53","Amazon EC2 instances are not automatically protected by AWS Shield Standard. You need to use Shield Advanced for comprehensive protection."
"What does the AWS DDoS Response Team (DRT) do when you subscribe to Shield Advanced?","Provides 24/7 support and custom mitigations for complex DDoS attacks","Automatically blocks all incoming traffic","Encrypts all your data","Manages your AWS account security","The AWS DRT provides 24/7 support and custom mitigations for complex DDoS attacks when you subscribe to Shield Advanced."
"Which pricing model does AWS Shield Standard use?","No additional cost (included with other AWS services)","Pay-as-you-go based on traffic volume","Fixed monthly fee per protected resource","Hourly fee based on attack duration","AWS Shield Standard is included at no additional cost with other AWS services."
"Which benefit is exclusive to AWS Shield Advanced users compared to Shield Standard?","Access to the AWS DDoS Response Team (DRT)","Automatic protection against network-layer DDoS attacks","Protection for Amazon Route 53","Integration with AWS IAM","Only Shield Advanced users get access to the AWS DDoS Response Team (DRT)."
"Which AWS service allows you to view detailed DDoS attack reports and mitigation summaries when using AWS Shield Advanced?","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS CloudWatch provides detailed DDoS attack reports and mitigation summaries when using Shield Advanced."
"What is the purpose of setting up health checks with AWS Shield Advanced?","To detect and mitigate application-layer attacks","To monitor the health of backend servers","To optimise database performance","To manage network traffic","Health checks help AWS Shield Advanced to detect and mitigate application-layer attacks by monitoring the health and availability of your application."
"Which of the following best describes the 'cost protection' benefit offered by AWS Shield Advanced?","Reimbursement of scaling costs due to DDoS attacks","Discount on AWS WAF usage","Free access to AWS Support","Lower data transfer costs","Shield Advanced offers 'cost protection' which reimburses scaling costs that occur as a direct result of a DDoS attack. This reduces unexpected expenses."
"What type of DDoS attack does AWS Shield Advanced provide the best protection against?","Sophisticated and large-scale attacks targeting applications","Basic network-layer attacks","Data breach attempts","SQL injection attacks","AWS Shield Advanced provides more comprehensive protection against sophisticated and large-scale attacks that target your application."
"How does AWS Shield Advanced help to reduce false positives in AWS WAF rules?","By providing pre-configured WAF rules tailored to your application","By automatically adjusting WAF rules based on attack patterns","By integrating with AWS IAM to manage user permissions","By encrypting data at rest","Shield Advanced can help reduce false positives in AWS WAF rules by providing visibility into traffic patterns and helping to fine-tune WAF rules based on real-time attack data."
"Which metric can you monitor in AWS CloudWatch to understand the effectiveness of AWS Shield mitigations?","DDoSDetected","CPUUtilization","NetworkPacketsIn","DiskReadBytes","DDoSDetected metric in CloudWatch indicates the presence and severity of DDoS attacks and can be used to assess the effectiveness of Shield mitigations."
"What is the first step you should take to onboard an application to AWS Shield Advanced?","Associate your Elastic IP addresses or CloudFront distributions with Shield Advanced","Enable AWS WAF for your application","Configure health checks","Subscribe to Shield Advanced","You need to associate your Elastic IP addresses or CloudFront distributions with Shield Advanced to protect them."
"What is the 'attack surface' that AWS Shield protects?","The set of AWS resources exposed to the internet that can be targeted by attackers","The internal network of your organisation","The database layer of your application","The code running on your application servers","AWS Shield protects the set of AWS resources that are exposed to the internet and can be targeted by attackers."
"If you are using AWS Shield Advanced with AWS WAF, how are custom rules best managed?","Through AWS WAF's rule builder interface","Through the AWS CLI","Through AWS Config","Through AWS CloudTrail","Custom rules are best managed through AWS WAF's rule builder interface, which allows you to create and fine-tune rules to protect your application."
"What is a key difference between reactive and proactive DDoS protection strategies offered by AWS Shield Advanced?","Proactive protection involves pre-configuring mitigations, while reactive protection responds to attacks as they occur","Reactive protection is cheaper than proactive protection","Proactive protection is only available for network-layer attacks","Reactive protection provides 24/7 support from the AWS DRT","Proactive protection involves pre-configuring mitigations and engaging the AWS DRT for tailored protection plans, while reactive protection responds to attacks as they occur."
"When using AWS Shield Advanced, what is the benefit of engaging the AWS DDoS Response Team (DRT) prior to an actual attack?","The DRT can help you design a tailored protection plan and pre-configure mitigations","The DRT can provide you with a free AWS Support plan","The DRT can automatically block all traffic from suspicious IP addresses","The DRT can encrypt your data at rest","Engaging the DRT prior to an attack allows them to help you design a tailored protection plan and pre-configure mitigations to better protect your application."
"How does AWS Shield work with AWS Global Accelerator to protect your application?","AWS Shield protects the Global Accelerator endpoints from DDoS attacks","AWS Global Accelerator automatically blocks malicious traffic","AWS Shield encrypts traffic passing through Global Accelerator","AWS Global Accelerator monitors traffic patterns for anomalies","AWS Shield protects the AWS Global Accelerator endpoints (Elastic IPs and Regional Application Load Balancers) from DDoS attacks."
"What type of data is typically logged and analysed by AWS Shield to detect and mitigate DDoS attacks?","Network traffic metadata (source IPs, protocols, ports)","Application data (user credentials, session cookies)","Database queries","Operating system logs","AWS Shield logs and analyses network traffic metadata (source IPs, protocols, ports) to detect and mitigate DDoS attacks."
"What is the impact of a DDoS attack on an unprotected AWS environment?","Increased latency, application unavailability, and potential financial losses","Data breaches and data loss","Compliance violations","Improved security posture","DDoS attacks can lead to increased latency, application unavailability, and potential financial losses in an unprotected AWS environment."
"How does AWS Shield mitigate SYN flood attacks?","By using SYN cookies and rate limiting","By blocking all incoming traffic","By encrypting all network traffic","By redirecting traffic to a scrubbing centre","AWS Shield mitigates SYN flood attacks by using techniques like SYN cookies and rate limiting to prevent malicious traffic from overwhelming your servers."
"Which AWS service can you use to implement custom DDoS mitigation strategies in conjunction with AWS Shield?","AWS Lambda","AWS CloudTrail","AWS Config","AWS IAM","AWS Lambda can be used to implement custom DDoS mitigation strategies in conjunction with AWS Shield. For example, you can use Lambda to analyse traffic patterns and automatically update AWS WAF rules."
"What is the 'Application Load Balancer (ALB) request surge queue' and how does it relate to AWS Shield?","A buffer that absorbs sudden spikes in traffic, which AWS Shield can help manage","A feature that automatically scales your ALB capacity","A tool for monitoring ALB performance","A mechanism for encrypting traffic to your ALB","The ALB request surge queue is a buffer that absorbs sudden spikes in traffic. AWS Shield can help manage the queue during a DDoS attack to prevent your application from being overwhelmed."
"Which AWS service provides visibility into potential vulnerabilities in your AWS environment that could be exploited during a DDoS attack?","AWS Trusted Advisor","AWS CloudTrail","AWS Config","AWS IAM","AWS Trusted Advisor can identify potential vulnerabilities in your AWS environment that could be exploited during a DDoS attack."
"What is the purpose of 'request throttling' as a DDoS mitigation technique employed by AWS Shield and AWS WAF?","To limit the number of requests from a single source within a given timeframe","To block all traffic from specific countries","To encrypt all incoming traffic","To redirect traffic to a different region","Request throttling limits the number of requests from a single source within a given timeframe, preventing attackers from overwhelming your application."
"Which type of DNS record is best suited for use with AWS Shield and AWS Global Accelerator to provide DDoS protection for your application?","Alias record","CNAME record","A record","TXT record","An Alias record is the best suited to work seamlessly with AWS Shield and AWS Global Accelerator for the most efficient and comprehensive DDoS protection."
"How does AWS Shield Advanced protect against volumetric DDoS attacks that saturate network bandwidth?","By scrubbing malicious traffic and absorbing the attack volume","By redirecting traffic to a backup region","By encrypting all network traffic","By blocking all traffic from unknown sources","AWS Shield Advanced scrubs malicious traffic and absorbs the attack volume, preventing it from reaching your application and saturating your network bandwidth."
"What is the purpose of AWS Shield Advanced's 'attack visualisation' feature?","To provide real-time insights into ongoing DDoS attacks and their impact","To automatically block all malicious traffic","To encrypt all data in transit","To generate compliance reports","AWS Shield Advanced's attack visualisation feature provides real-time insights into ongoing DDoS attacks and their impact, allowing you to understand the nature of the attack and the effectiveness of the mitigations."
"When should you consider using AWS Shield Advanced over AWS Shield Standard?","When you require enhanced protection against sophisticated and large-scale DDoS attacks","When you only need basic protection against common network-layer attacks","When you want to encrypt data at rest","When you want to manage user identities","You should consider using Shield Advanced when you require enhanced protection against sophisticated and large-scale DDoS attacks and need access to the AWS DDoS Response Team."
"Which AWS service can you use to create custom dashboards and alerts to monitor the performance and security of your AWS Shield-protected resources?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS IAM","Amazon CloudWatch allows you to create custom dashboards and alerts to monitor the performance and security of your AWS Shield-protected resources."
"What is the relationship between AWS Shield and AWS Identity and Access Management (IAM)?","IAM controls access to AWS Shield resources and features","AWS Shield automatically manages IAM roles and policies","IAM encrypts data at rest for AWS Shield","AWS Shield automatically creates IAM users","IAM controls access to AWS Shield resources and features, allowing you to manage who can configure and monitor your DDoS protection."
"What is the purpose of the 'estimated monthly AWS Shield Advanced charges' displayed in the AWS Management Console?","To provide an estimate of your expected monthly Shield Advanced bill","To display your actual Shield Advanced bill","To provide a discount on AWS services","To show the cost savings from using Shield Advanced","The 'estimated monthly AWS Shield Advanced charges' provide an estimate of your expected monthly bill based on your usage of protected resources."
"Which of the following is NOT a typical symptom of a DDoS attack?","Sudden increase in website traffic from a single source","Slow website loading times","Increased database query times","Website becoming completely unavailable","Increased database query times may be a symptom of database performance issues but would likely not be caused by a DDoS attack directly."
"How can you use AWS Firewall Manager to centrally manage AWS Shield Advanced protections across multiple AWS accounts?","Firewall Manager allows you to create and enforce Shield Advanced policies across your organisation","Firewall Manager automatically blocks all malicious traffic","Firewall Manager encrypts all data in transit","Firewall Manager manages user identities and access control","AWS Firewall Manager allows you to centrally manage AWS Shield Advanced protections, along with AWS WAF and other security services, across multiple AWS accounts in your organisation."
"What is the significance of the 'top contributors' metric displayed in AWS Shield Advanced's attack summary?","It identifies the sources of the most traffic during an attack","It identifies the most active users of your application","It identifies the most vulnerable parts of your infrastructure","It identifies the most expensive resources in your AWS account","The 'top contributors' metric identifies the sources of the most traffic during an attack, helping you understand the attack's characteristics and potentially block malicious sources."
"What is the purpose of the AWS Shield Advanced 'proactive engagement' process?","To work with the AWS DDoS Response Team to develop a custom protection plan","To automatically block all traffic from suspicious IP addresses","To encrypt all data at rest","To generate compliance reports","The AWS Shield Advanced 'proactive engagement' process involves working with the AWS DDoS Response Team to develop a custom protection plan tailored to your application's specific needs and attack profile."
"How does AWS Shield Advanced contribute to compliance with industry regulations such as PCI DSS?","By helping to protect against DDoS attacks, which can disrupt business operations and compromise sensitive data","By automatically encrypting all data at rest","By managing user identities and access control","By providing a compliance dashboard","AWS Shield Advanced helps contribute to compliance with industry regulations such as PCI DSS by protecting against DDoS attacks, which can disrupt business operations and compromise sensitive data."
"What is the AWS recommended method for testing your DDoS resilience against AWS Shield?","Using the AWS SimSpace Weaver service to simulate a DDoS attack","Using a third-party DDoS simulation service","Contacting AWS Support to arrange a penetration test","Disabling AWS Shield and monitoring the impact","AWS recommends using the AWS SimSpace Weaver service to simulate a DDoS attack."
"If an attacker manages to bypass AWS Shield and AWS WAF and cause an outage, what other AWS service may be used to help bring the service back online quickly?","AWS Auto Scaling","AWS CloudTrail","AWS Config","AWS IAM","AWS Auto Scaling can be used to automatically scale resources to handle unexpected traffic spikes, potentially mitigating the impact of a DDoS attack that bypasses initial protections."
"Which of the following is a valid reason to configure AWS Shield Advanced with health checks?","To allow AWS Shield to detect and respond to application-layer DDoS attacks","To allow AWS Shield to encrypt network traffic","To allow AWS Shield to generate compliance reports","To allow AWS Shield to manage IAM policies","The health checks allow AWS Shield to detect and respond to application-layer DDoS attacks by monitoring the health and availability of the protected resource."
"When using AWS Shield Advanced, what is the 'attack summary' and where can it be found?","A report detailing the attack characteristics and mitigation actions, found in AWS CloudWatch","A list of blocked IP addresses, found in AWS WAF","A summary of AWS Shield's pricing, found in the AWS Billing console","A list of vulnerable resources, found in AWS Trusted Advisor","The attack summary is a report detailing the attack characteristics and mitigation actions, and it can be found in AWS CloudWatch."
"How does AWS Shield Standard protect against common infrastructure layer attacks?","By automatically absorbing and mitigating attacks on AWS infrastructure","By routing traffic through a scrubbing centre","By blacklisting malicious IP addresses","By encrypting network traffic","AWS Shield Standard automatically absorbs and mitigates common infrastructure layer attacks, such as SYN floods and UDP floods, on the AWS infrastructure."
"How does enabling deletion protection on a resource protected by AWS Shield Advanced benefit security?","Prevents accidental or malicious deletion of the protected resource","Encrypts the data stored in the resource","Provides detailed logging of all access to the resource","Ensures high availability of the resource","Enabling deletion protection prevents accidental or malicious deletion of the resource, ensuring that it remains protected by AWS Shield Advanced."
"When configuring AWS Shield Advanced, what type of resource is referred to as a 'protected resource'?","An Elastic IP address or a CloudFront distribution","An AWS IAM role","An AWS Lambda function","An Amazon S3 bucket","An Elastic IP address or a CloudFront distribution are examples of resources that can be protected by AWS Shield Advanced."
"When dealing with a suspected DDoS attack, what is the first thing you should review on AWS?","AWS CloudWatch metrics for your protected resources","AWS IAM policies to check for unauthorized access","AWS Config rules for compliance violations","AWS Trusted Advisor recommendations for security vulnerabilities","You should first review AWS CloudWatch metrics for your protected resources to understand the attack's characteristics and the effectiveness of Shield's mitigations."
"What is the AWS cost protection benefit that AWS Shield Advanced subscribers may have access to?","Reimbursement for AWS usage charges incurred due to scaling to mitigate a DDoS attack","Credits to use against future AWS charges","A discount on AWS Support plans","Coverage of legal fees resulting from a data breach","Shield Advanced subscribers may be eligible for reimbursement of AWS usage charges incurred due to scaling to mitigate a DDoS attack, providing cost protection during an attack."
"With AWS Shield, what are the general options when responding to an emerging DDoS attack that is affecting your application's availability?","To block traffic via AWS WAF rules or engage the AWS DDoS Response Team","To shut down your application and wait for the attack to subside","To increase database capacity and retry all requests","To change your application code and redeploy","The typical options are to block malicious traffic via AWS WAF rules or engage the AWS DDoS Response Team for assistance with complex attacks."
"What is the primary purpose of AWS Shield?","To protect against DDoS attacks","To encrypt data at rest","To manage IAM roles","To monitor network traffic","AWS Shield is a managed DDoS protection service that safeguards applications running on AWS."
"Which AWS Shield tier provides always-on detection and automatic in-line mitigations?","AWS Shield Advanced","AWS Shield Standard","AWS WAF","AWS Inspector","AWS Shield Advanced provides enhanced DDoS protection with 24/7 threat monitoring and incident response team support."
"What type of attacks does AWS Shield Standard help protect against?","Common, most frequently occurring infrastructure layer (Layer 3 and 4) attacks","Application-layer (Layer 7) attacks","Sophisticated and large attacks","All types of attacks","AWS Shield Standard protects against common infrastructure-layer DDoS attacks."
"Which AWS service integrates with AWS Shield Advanced to provide application-layer (Layer 7) protection?","AWS WAF","AWS CloudTrail","AWS Config","AWS IAM","AWS WAF (Web Application Firewall) works with AWS Shield Advanced to provide comprehensive protection against both infrastructure and application-layer attacks."
"How does AWS Shield Advanced provide cost protection?","By providing credits for usage spikes during DDoS events","By offering discounted EC2 instances","By automatically reducing your bill","By eliminating all costs associated with DDoS attacks","AWS Shield Advanced provides cost protection by providing credits for usage spikes during DDoS events, helping to mitigate unexpected costs."
"What is the role of the AWS Shield Response Team (SRT)?","To provide 24/7 support and assist with custom mitigations","To manage AWS infrastructure","To handle billing inquiries","To provide training on AWS services","The AWS Shield Response Team offers 24/7 support to Shield Advanced customers, assisting with custom mitigations and attack analysis."
"Which of the following is a benefit of using AWS Shield Advanced's proactive engagement?","Early warnings and custom mitigation strategies","Automated security audits","Reduced AWS service costs","Unlimited data transfer","AWS Shield Advanced's proactive engagement allows for early warnings and custom mitigation strategies tailored to specific applications and threats."
"Which pricing model does AWS Shield Standard use?","No additional charge; it's included with AWS services","Pay-as-you-go","Reserved Instance pricing","A fixed monthly fee","AWS Shield Standard is provided at no additional charge for all AWS customers."
"What type of resource can be protected using AWS Shield Advanced?","EC2 instances behind an Elastic Load Balancer","S3 buckets","IAM roles","CloudWatch alarms","AWS Shield Advanced can protect resources such as EC2 instances behind an Elastic Load Balancer."
"Which AWS service can be used to gain visibility into the DDoS attacks being mitigated by AWS Shield?","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS CloudWatch provides metrics and logs that offer visibility into DDoS attacks being mitigated by AWS Shield."
"When considering AWS Shield, what does 'Layer 3 and 4' protection refer to?","Network and Transport Layer protection","Application Layer protection","Data Link Layer protection","Presentation Layer protection","Layer 3 (Network) and Layer 4 (Transport) refer to the OSI model layers where infrastructure-level attacks are mitigated."
"How does AWS Shield Advanced utilise health checks?","To detect unhealthy resources during an attack and redirect traffic","To optimise database queries","To verify IAM permissions","To monitor network bandwidth","AWS Shield Advanced uses health checks to detect unhealthy resources during an attack, enabling it to redirect traffic and maintain application availability."
"What is the purpose of setting up custom rules in AWS WAF when using AWS Shield Advanced?","To further refine application-layer protection against specific attack patterns","To manage infrastructure security","To reduce AWS costs","To improve network performance","Custom rules in AWS WAF allow for refining application-layer protection against specific attack patterns, enhancing security beyond the default protections provided by Shield Advanced."
"Which of the following actions can AWS Shield Advanced automatically take to mitigate DDoS attacks?","Traffic shaping and traffic filtering","Deleting compromised resources","Encrypting data in transit","Changing IAM policies","AWS Shield Advanced can automatically implement traffic shaping and traffic filtering to mitigate DDoS attacks."
"What is the first step you should take when you suspect you are under a DDoS attack and you have AWS Shield Advanced?","Contact the AWS Shield Response Team (SRT)","Reboot your EC2 instances","Increase your database capacity","Disable network access","Contacting the AWS Shield Response Team (SRT) is the first step to receive expert assistance and custom mitigation strategies."
"Which AWS service can be used to log and monitor the actions taken by AWS Shield?","AWS CloudTrail","AWS Config","AWS Inspector","AWS Trusted Advisor","AWS CloudTrail logs API calls made to AWS services, including actions taken by AWS Shield, providing an audit trail."
"In AWS Shield Advanced, what is the benefit of enabling request throttling in AWS WAF?","To limit the number of requests from a single IP address within a specified time period","To encrypt request headers","To reduce latency","To compress data","Request throttling in AWS WAF limits the number of requests from a single IP address, mitigating volumetric attacks and preventing resource exhaustion."
"Which of the following scenarios would benefit most from using AWS Shield Advanced?","A mission-critical e-commerce website","A static blog hosted on S3","A development environment","A small internal application","A mission-critical e-commerce website that requires high availability and advanced DDoS protection would benefit most from AWS Shield Advanced."
"What does the 'Application Load Balancer' (ALB) provide in the context of AWS Shield protection?","Distribution of traffic to multiple targets, enhancing resilience","Data encryption","Intrusion detection","Network firewall","The ALB distributes traffic across multiple targets, improving resilience and making it harder for DDoS attacks to overwhelm the application."
"How does AWS Shield Advanced help protect against application-layer (Layer 7) DDoS attacks?","By integrating with AWS WAF to filter malicious requests","By automatically scaling EC2 instances","By providing network-level filtering","By encrypting data","AWS Shield Advanced integrates with AWS WAF to provide application-layer filtering, blocking malicious requests based on patterns and rules."
"You have AWS Shield Advanced enabled. How can you view detailed attack reports and mitigation actions?","Through the AWS Management Console and CloudWatch","By contacting AWS Support","By using the AWS CLI","By analysing VPC Flow Logs","Attack reports and mitigation actions can be viewed through the AWS Management Console and CloudWatch, providing visibility into ongoing and past attacks."
"What is the purpose of the AWS Global Accelerator in the context of DDoS mitigation?","To optimise routing of traffic and provide static entry points","To encrypt all traffic","To provide serverless computing","To manage database connections","AWS Global Accelerator optimises routing and provides static entry points, making it more difficult for attackers to target specific resources directly."
"What kind of cost optimisation does AWS Shield Advanced offer beyond DDoS protection?","Credits for usage spikes during DDoS attacks","Free data transfer","Reduced EC2 instance costs","Discounted S3 storage","AWS Shield Advanced offers credits for usage spikes during DDoS attacks, helping to manage unexpected costs related to attack mitigation."
"Which of the following services is NOT directly protected by AWS Shield?","AWS Lambda","Amazon EC2","Amazon CloudFront","Elastic Load Balancing (ELB)","AWS Lambda is a serverless compute service and is not directly protected by AWS Shield in the same way as EC2, CloudFront, and ELB."
"What is the benefit of using AWS Shield Advanced's 'health check aggregation'?","Improves the accuracy of DDoS detection and mitigation","Reduces the cost of CloudWatch metrics","Simplifies IAM role management","Enhances data encryption","Health check aggregation improves the accuracy of DDoS detection and mitigation by considering the health status of multiple resources."
"What is the scope of protection provided by AWS Shield Standard?","Protects against common infrastructure-layer attacks for all AWS customers","Protects against all types of attacks for all AWS customers","Provides customised protection for individual resources","Protects against application-layer attacks","AWS Shield Standard provides basic protection against common infrastructure-layer attacks for all AWS customers at no additional charge."
"You are using AWS Shield Advanced and notice a large spike in traffic. What should you do?","Monitor the attack and contact the Shield Response Team (SRT) if needed","Immediately shut down your resources","Increase your EC2 instance capacity","Change your DNS records","Monitor the attack through CloudWatch and contact the Shield Response Team (SRT) if needed for expert assistance and custom mitigation strategies."
"What is a common attack vector that AWS Shield helps to mitigate?","Volumetric attacks","SQL injection","Cross-site scripting (XSS)","Brute-force attacks","Volumetric attacks, which aim to overwhelm a target with massive amounts of traffic, are a common type of attack that AWS Shield helps mitigate."
"What type of traffic filtering does AWS Shield utilise to mitigate DDoS attacks?","Ingress and egress filtering","Internal traffic filtering","Encrypted traffic filtering","DNS traffic filtering","AWS Shield utilises both ingress (incoming) and egress (outgoing) traffic filtering to mitigate DDoS attacks, ensuring malicious traffic is blocked while legitimate traffic is allowed."
"What is the role of 'AWS Firewall Manager' in relation to AWS Shield Advanced?","Centralised management of WAF rules across multiple accounts","Data encryption","Intrusion detection","Network firewall","AWS Firewall Manager allows for centralised management of WAF rules across multiple accounts, simplifying the process of configuring and maintaining application-layer protection in conjunction with AWS Shield Advanced."
"What is the purpose of the 'AWS Shield Advanced API'?","To programmatically manage and configure AWS Shield Advanced features","To monitor network traffic","To encrypt data","To manage IAM roles","The AWS Shield Advanced API allows for programmatic management and configuration of AWS Shield Advanced features, enabling automation and integration with other tools."
"How does AWS Shield help maintain application availability during a DDoS attack?","By automatically scaling resources and mitigating malicious traffic","By encrypting data at rest","By managing DNS records","By providing intrusion detection","AWS Shield helps maintain application availability by automatically scaling resources and mitigating malicious traffic, ensuring that legitimate users can still access the application."
"What is the key difference between AWS Shield Standard and AWS Shield Advanced regarding mitigation capabilities?","AWS Shield Advanced provides custom mitigation strategies and 24/7 support","AWS Shield Standard offers real-time mitigation","AWS Shield Standard offers custom mitigation strategies and 24/7 support","AWS Shield Advanced offers only basic protection","AWS Shield Advanced provides enhanced DDoS protection with custom mitigation strategies and 24/7 support from the AWS Shield Response Team."
"Which of the following is a key component for effective DDoS mitigation when using AWS Shield Advanced?","Properly configured AWS WAF rules","Strong passwords","Multi-factor authentication","Regular software updates","Properly configured AWS WAF rules are crucial for effective application-layer DDoS mitigation when using AWS Shield Advanced."
"In AWS Shield Advanced, what is the purpose of 'protected resources'?","Resources that are monitored and protected against DDoS attacks","Resources used for testing","Resources used for backup","Resources used for development","'Protected resources' in AWS Shield Advanced refer to the specific AWS resources (e.g., EC2 instances behind an ELB) that are being monitored and protected against DDoS attacks."
"What is a typical sign of a volumetric DDoS attack that AWS Shield helps to mitigate?","A sudden spike in network traffic","Database corruption","Website defacement","Account hijacking","A sudden spike in network traffic is a typical sign of a volumetric DDoS attack, which AWS Shield helps to mitigate by filtering malicious traffic."
"How does AWS Shield Advanced utilise CloudFront's global network to mitigate DDoS attacks?","By distributing traffic and caching content closer to users","By encrypting data in transit","By monitoring network traffic","By managing DNS records","AWS Shield Advanced utilises CloudFront's global network to distribute traffic and cache content closer to users, reducing the impact of DDoS attacks by absorbing malicious traffic at the edge."
"What is the purpose of using 'AWS Shield Advanced Rate-Based Rules' within AWS WAF?","To limit the number of requests from a single IP address within a defined period","To encrypt all data in transit","To reduce the cost of data transfer","To block all traffic from specific countries","Rate-Based Rules limit the number of requests from a single IP address within a defined period, mitigating volumetric attacks and preventing resource exhaustion."
"Which of the following attack vectors targets the application layer (Layer 7) and can be mitigated with AWS Shield Advanced and WAF?","HTTP floods","SYN floods","UDP floods","ICMP floods","HTTP floods target the application layer (Layer 7) and can be effectively mitigated using AWS Shield Advanced in conjunction with AWS WAF rules."
"What is the relationship between AWS Shield Advanced and 'AWS penetration testing guidelines'?","Penetration testing helps identify vulnerabilities that AWS Shield Advanced can protect","Penetration testing replaces the need for AWS Shield Advanced","AWS Shield Advanced prevents all penetration testing","Penetration testing is unrelated to AWS Shield Advanced","Penetration testing helps identify vulnerabilities that AWS Shield Advanced can protect by allowing organisations to proactively assess their security posture and identify potential weaknesses."
"How does AWS Shield Advanced help protect against resource exhaustion attacks?","By automatically scaling resources and filtering malicious traffic","By encrypting data at rest","By managing DNS records","By providing intrusion detection","AWS Shield Advanced helps protect against resource exhaustion attacks by automatically scaling resources and filtering malicious traffic, ensuring that legitimate users can still access the application."
"What is the benefit of subscribing to AWS Shield Advanced for cost predictability?","Cost protection against usage spikes during DDoS attacks","Reduced AWS service costs","Free data transfer","Discounted S3 storage","Subscribing to AWS Shield Advanced provides cost protection against usage spikes during DDoS attacks, offering greater cost predictability compared to being charged solely based on usage."
"Which of the following is a recommendation for configuring AWS WAF with AWS Shield Advanced to maximise protection?","Use AWS Managed Rules and custom rules tailored to your application","Only use AWS Managed Rules","Only use custom rules","Do not use AWS WAF with AWS Shield Advanced","Using both AWS Managed Rules and custom rules tailored to your application maximises protection against a wide range of threats when using AWS WAF with AWS Shield Advanced."
"What is the primary advantage of using AWS Shield Advanced compared to relying solely on AWS Shield Standard?","Enhanced DDoS protection with 24/7 support and custom mitigations","Free data transfer","Reduced AWS service costs","Unlimited storage","AWS Shield Advanced offers enhanced DDoS protection with 24/7 support from the AWS Shield Response Team and custom mitigation strategies tailored to specific applications and threats."
"How does AWS Shield Advanced enhance visibility into DDoS attacks?","Detailed attack reports and metrics in CloudWatch","Automated security audits","Reduced AWS service costs","Unlimited storage","AWS Shield Advanced provides detailed attack reports and metrics in CloudWatch, offering greater visibility into ongoing and past attacks compared to AWS Shield Standard."
"In AWS Shield Advanced, what is the purpose of 'attack signatures'?","To identify and mitigate specific types of DDoS attacks","To encrypt data","To manage IAM roles","To optimise network performance","'Attack signatures' are used to identify and mitigate specific types of DDoS attacks, allowing AWS Shield Advanced to recognise and respond to known attack patterns."
"How does AWS Shield Advanced help protect against zero-day DDoS attacks?","By using machine learning to detect and mitigate anomalous traffic patterns","By encrypting data","By managing DNS records","By providing intrusion detection","AWS Shield Advanced uses machine learning to detect and mitigate anomalous traffic patterns, helping to protect against zero-day DDoS attacks that have not been seen before."
"What is the best practice when configuring AWS WAF rules with AWS Shield Advanced?","Regularly review and update rules based on attack patterns","Only use AWS Managed Rules","Only use custom rules","Do not use any rules","Regularly reviewing and updating WAF rules based on emerging attack patterns ensures that the application remains protected against the latest threats when using AWS Shield Advanced."
"What is the primary purpose of AWS Shield?","To protect against DDoS attacks","To encrypt data at rest","To manage user identities","To monitor network traffic","AWS Shield is a managed Distributed Denial of Service (DDoS) protection service that safeguards applications running on AWS."
"Which AWS Shield tier offers advanced custom mitigations?","Shield Advanced","Shield Standard","Shield Basic","Shield Pro","Shield Advanced offers expanded DDoS attack protection for your applications."
"Which type of AWS resource can be protected by AWS Shield Advanced?","Elastic Load Balancers (ELB), CloudFront distributions, Global Accelerator, and Route 53 hosted zones","S3 buckets, EC2 instances, and DynamoDB tables","Lambda functions and API Gateway endpoints","VPC networks and subnets","Shield Advanced protection extends to Elastic Load Balancers (ELB), CloudFront distributions, Global Accelerator, and Route 53 hosted zones."
"What type of attack does AWS Shield Standard automatically protect against?","Common infrastructure layer attacks","Application layer attacks","Zero-day exploits","SQL injection attacks","AWS Shield Standard provides protection against common, frequently occurring infrastructure layer DDoS attacks that target all AWS customers."
"What is the cost of AWS Shield Standard?","No additional charge, it is included for all AWS customers","A monthly subscription fee","A per-attack charge","Based on the amount of network traffic","AWS Shield Standard is automatically enabled and free of charge for all AWS customers."
"What is the primary benefit of using AWS Shield Advanced?","Enhanced visibility and control over DDoS attacks, and access to the DDoS Response Team (DRT)","Free DDoS protection","Automated compliance reporting","Increased network bandwidth","AWS Shield Advanced provides enhanced visibility and control, and access to the DDoS Response Team (DRT)."
"If you are using AWS Shield Advanced and experience a DDoS attack, who can you contact for assistance?","The DDoS Response Team (DRT)","AWS Support","Your account manager","The AWS security team","AWS Shield Advanced customers can contact the DDoS Response Team (DRT) for assistance."
"What is the purpose of the AWS WAF integration with AWS Shield Advanced?","To provide application-layer protection against DDoS attacks","To provide infrastructure-layer protection against DDoS attacks","To encrypt network traffic","To provide intrusion detection","AWS WAF integration with AWS Shield Advanced allows you to mitigate application-layer (Layer 7) DDoS attacks."
"Which of the following is NOT a feature of AWS Shield Advanced?","DDoS cost protection","Automated vulnerability scanning","24/7 access to the DDoS Response Team","Enhanced visibility into DDoS attacks","Automated vulnerability scanning is not a feature of AWS Shield Advanced.  It focuses on DDoS protection."
"Which AWS service does AWS Shield Advanced integrate with to provide enhanced protection for web applications?","AWS WAF (Web Application Firewall)","AWS IAM (Identity and Access Management)","AWS CloudTrail","Amazon GuardDuty","AWS Shield Advanced integrates with AWS WAF to provide enhanced protection for web applications against application-layer DDoS attacks."
"What type of reporting does AWS Shield Advanced provide?","Real-time attack dashboards and detailed attack summaries","Compliance reports","Cost optimisation recommendations","Security audit logs","AWS Shield Advanced provides real-time attack dashboards and detailed attack summaries."
"How does AWS Shield Advanced mitigate DDoS attacks targeting Elastic Load Balancers (ELB)?","By automatically scaling ELB capacity and applying traffic filtering rules","By restarting the ELB instance","By redirecting traffic to a backup ELB","By terminating malicious connections","AWS Shield Advanced mitigates DDoS attacks targeting ELB by automatically scaling ELB capacity and applying traffic filtering rules."
"For what duration are you typically committed when subscribing to AWS Shield Advanced?","One year","One month","Three months","Six months","When subscribing to AWS Shield Advanced, you are typically committed for one year."
"Which of the following AWS services benefits most from AWS Shield Advanced protection against DDoS attacks?","Public facing web applications","Internal databases","Offline data processing pipelines","Static website hosted on S3","Public facing web applications, particularly those critical for business operations, benefit most from AWS Shield Advanced."
"Which AWS Shield feature helps protect against large volumetric DDoS attacks?","Traffic filtering","Rate limiting","IP reputation lists","Connection draining","Traffic filtering is a key component in mitigating large volumetric DDoS attacks by dropping malicious traffic."
"What does the AWS Shield Advanced 'DDoS cost protection' feature provide?","Credit for usage spikes due to a DDoS attack","Discount on AWS Shield Advanced subscription fees","Free access to AWS support during a DDoS attack","Guaranteed uptime during a DDoS attack","AWS Shield Advanced 'DDoS cost protection' helps protect against usage spikes during a DDoS attack."
"How does AWS Shield Advanced improve visibility into DDoS attacks?","By providing detailed attack timelines and traffic analysis","By sending email notifications","By integrating with AWS CloudTrail","By providing network flow logs","AWS Shield Advanced provides detailed attack timelines and traffic analysis to improve visibility."
"Which of the following is a key advantage of using AWS Shield over building your own DDoS mitigation solution?","Lower operational overhead and faster time to protection","Higher level of customisation","Lower cost","Greater control over traffic routing","AWS Shield offers lower operational overhead and faster time to protection compared to building your own solution."
"What does the AWS Shield Advanced DDoS Response Team (DRT) do?","Provides expert assistance during DDoS attacks","Manages AWS Shield infrastructure","Develops new AWS security features","Conducts penetration testing","The DDoS Response Team (DRT) provides expert assistance during DDoS attacks for AWS Shield Advanced customers."
"How does AWS Shield Standard protect against SYN flood attacks?","By filtering malicious SYN packets","By redirecting traffic to a cleaning centre","By increasing the SYN backlog queue size","By blocking all incoming SYN packets","AWS Shield Standard protects against SYN flood attacks by filtering malicious SYN packets."
"What type of information can you find in the AWS Shield Advanced attack summary reports?","Attack timeline, top attack vectors, and impacted resources","List of compromised EC2 instances","Vulnerability assessment results","Compliance audit reports","The AWS Shield Advanced attack summary reports provide an attack timeline, top attack vectors, and impacted resources."
"When should you consider using AWS Shield Advanced instead of AWS Shield Standard?","When you require advanced custom mitigations and expert support for DDoS attacks","When you only need basic DDoS protection","When your application is not critical","When you want to save money","You should consider using AWS Shield Advanced when you require advanced custom mitigations and expert support for DDoS attacks."
"Which AWS service works with AWS Shield to log all API calls made for security analysis, operational troubleshooting, and compliance auditing?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail logs all API calls made for security analysis, operational troubleshooting, and compliance auditing."
"How can you enable AWS Shield Standard?","It is automatically enabled for all AWS customers","You must manually enable it in the AWS Management Console","You must contact AWS support to enable it","You must purchase a subscription to enable it","AWS Shield Standard is automatically enabled for all AWS customers."
"What is the primary benefit of using AWS Global Accelerator with AWS Shield Advanced?","Improved application availability and performance during DDoS attacks","Lower latency for global users","Reduced cost for network traffic","Simplified network management","AWS Global Accelerator improves application availability and performance during DDoS attacks by routing traffic through the AWS global network."
"How does AWS Shield help reduce the impact of DDoS attacks on your AWS bill?","By providing DDoS cost protection for AWS Shield Advanced customers","By offering discounts on AWS services during an attack","By automatically scaling down resources during an attack","By preventing all DDoS attacks","AWS Shield Advanced offers DDoS cost protection for AWS Shield Advanced customers to reduce the impact of DDoS attacks on your AWS bill."
"What is the best way to test the effectiveness of your AWS Shield Advanced configuration?","Simulate a DDoS attack using a controlled testing environment","Review AWS Shield documentation","Monitor AWS CloudWatch metrics","Contact AWS support","Simulating a DDoS attack in a controlled testing environment is the best way to test the effectiveness of your AWS Shield Advanced configuration."
"Which of the following is a key step in preparing for a DDoS attack with AWS Shield?","Configure AWS WAF rules to filter malicious traffic","Disable all incoming traffic","Encrypt all data at rest","Back up all databases","Configuring AWS WAF rules to filter malicious traffic is a key step in preparing for a DDoS attack with AWS Shield."
"What AWS service can be used in conjunction with AWS Shield to provide detailed analysis of network traffic patterns and identify potential security threats?","Amazon GuardDuty","AWS Inspector","AWS Config","AWS Trusted Advisor","Amazon GuardDuty can be used in conjunction with AWS Shield to provide detailed analysis of network traffic patterns and identify potential security threats."
"Which AWS service, when integrated with AWS Shield Advanced, provides automatic application layer DDoS mitigation based on observed traffic patterns?","AWS WAF","AWS Firewall Manager","AWS Network Firewall","AWS Route 53","AWS WAF, when integrated with AWS Shield Advanced, provides automatic application layer DDoS mitigation."
"How does AWS Shield Advanced help protect against resource exhaustion attacks?","By automatically scaling resources to handle increased traffic volume","By blocking all incoming traffic","By redirecting traffic to a backup environment","By terminating malicious connections","AWS Shield Advanced helps protect against resource exhaustion attacks by automatically scaling resources."
"Which of the following is an important consideration when configuring AWS WAF rules for DDoS protection with AWS Shield?","Blocking known malicious IP addresses and patterns","Allowing all traffic from trusted sources","Disabling all logging","Using the default WAF rules","Blocking known malicious IP addresses and patterns is an important consideration when configuring AWS WAF rules for DDoS protection."
"What is the benefit of using AWS Firewall Manager with AWS Shield Advanced?","Centralised management of AWS WAF rules across multiple accounts and applications","Automated compliance reporting","Enhanced encryption of data at rest","Improved network performance","AWS Firewall Manager enables centralised management of AWS WAF rules across multiple accounts."
"How does AWS Shield help protect against application-layer DDoS attacks targeting HTTP GET floods?","By using AWS WAF rules to filter malicious requests based on patterns and rate limiting","By blocking all HTTP GET requests","By redirecting all HTTP GET requests to a caching server","By increasing the size of the HTTP GET buffer","AWS Shield, in conjunction with WAF, filters malicious requests using rules and rate limiting."
"What is the significance of the 'attack vector' in an AWS Shield Advanced attack summary report?","It identifies the type of DDoS attack used","It lists the compromised EC2 instances","It shows the geographic location of the attack","It indicates the severity of the attack","The 'attack vector' identifies the type of DDoS attack used."
"Which AWS service provides real-time metrics and monitoring dashboards that can be used to visualise the effectiveness of AWS Shield?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides real-time metrics and monitoring dashboards to visualise the effectiveness of AWS Shield."
"How does AWS Shield Advanced protect against DNS query floods?","By using Route 53 to filter malicious DNS queries","By blocking all DNS queries","By redirecting DNS queries to a backup DNS server","By increasing the DNS query rate limit","AWS Shield Advanced leverages Route 53 to filter malicious DNS queries."
"What type of proactive measure can you take with AWS Shield Advanced to prepare for potential DDoS attacks?","Define and test incident response plans","Disable all incoming traffic","Reduce the size of your application","Move your application to a different region","Defining and testing incident response plans is a proactive measure you can take."
"When using AWS Shield Advanced, what is the recommended approach for defining AWS WAF rules to mitigate DDoS attacks?","Start with a restrictive set of rules and gradually relax them as needed","Start with a permissive set of rules and gradually tighten them as needed","Use only the default AWS WAF rules","Disable all AWS WAF rules","A restrictive set of rules relaxed as needed is recommended."
"What is a common use case for AWS Shield Advanced's DDoS cost protection benefit?","Protecting against unexpected AWS usage charges due to a DDoS attack","Reducing the overall cost of AWS services","Preventing all DDoS attacks","Guaranteeing a certain level of uptime","DDoS cost protection helps protect against unexpected AWS usage charges due to a DDoS attack."
"How does AWS Shield Advanced help protect against state exhaustion attacks targeting TCP connections?","By using SYN cookies and connection limiting techniques","By blocking all TCP connections","By redirecting TCP connections to a backup server","By increasing the maximum number of allowed TCP connections","SYN cookies and connection limiting are used to mitigate state exhaustion attacks."
"What is the primary advantage of using AWS Shield Advanced with AWS Global Accelerator for global applications?","Improved application availability and performance with global traffic management","Simplified network configuration","Lower cost for global data transfer","Enhanced data encryption","Improved application availability and performance through global traffic management."
"Which of the following AWS services can be used to automatically create AWS WAF rules in response to DDoS attacks detected by AWS Shield?","AWS Firewall Manager","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS Firewall Manager can automate WAF rule creation based on Shield's detections."
"How can you determine if your application is currently under a DDoS attack when using AWS Shield Standard?","Monitor AWS CloudWatch metrics and look for unusual traffic patterns","Check the AWS Service Health Dashboard","Contact AWS support","Review AWS CloudTrail logs","Monitor CloudWatch metrics to identify unusual traffic patterns."
"What is the purpose of configuring AWS WAF rate-based rules when using AWS Shield?","To limit the number of requests from a single IP address within a given time period","To block all traffic from specific countries","To encrypt all incoming traffic","To compress all outgoing traffic","Rate-based rules limit the number of requests from a single IP within a period."
"What is the primary responsibility of the customer when using AWS Shield Standard for DDoS protection?","Ensuring that their application architecture is resilient and scalable","Configuring AWS WAF rules","Monitoring network traffic","Contacting AWS support during an attack","Resilient and scalable application architecture is key for DDoS protection."
"When using AWS Shield Advanced, what does the DDoS Response Team (DRT) provide in addition to mitigation strategies?","Guidance on application architecture best practices for DDoS resilience","Automated code deployment","Free AWS training","Guaranteed uptime","The DRT provides guidance on application architecture best practices."
"How can you enhance the effectiveness of AWS Shield against sophisticated application-layer DDoS attacks?","By customising AWS WAF rules based on your application's specific traffic patterns","By disabling all logging","By increasing the size of your EC2 instances","By using a static IP address","Customised WAF rules tailored to application patterns enhance protection."
"What is the key difference between AWS Shield Standard and AWS Shield Advanced regarding reporting?","Shield Advanced provides more detailed and real-time attack reports","Shield Standard provides compliance reports","Shield Standard provides real-time attack reports","Shield Advanced provides only summary reports","Shield Advanced offers more detailed and real-time attack reports."
"Which of the following best describes the DDoS mitigation strategy provided by AWS Shield Advanced?","A combination of automatic mitigation techniques and custom rules managed by the DDoS Response Team","A completely automated system with no human intervention","A manual process that requires constant monitoring and intervention","A set of pre-defined rules that cannot be customised","A combination of automatic and custom rules managed by the DRT."
"What is the primary purpose of AWS Shield?","To protect applications against DDoS attacks","To encrypt data at rest","To manage user identities and access","To monitor network traffic for intrusions","AWS Shield is a managed DDoS protection service that safeguards applications running on AWS."
"Which AWS Shield tier provides enhanced protection against sophisticated DDoS attacks and offers 24/7 access to the AWS DDoS Response Team (DRT)?","AWS Shield Advanced","AWS Shield Standard","AWS Shield Basic","AWS Shield Pro","AWS Shield Advanced offers enhanced protection and access to the DRT."
"Which types of resources are protected by AWS Shield Standard by default?","All AWS resources","Only EC2 instances","Only S3 buckets","Only resources behind CloudFront and Route 53","AWS Shield Standard automatically protects all AWS customers using CloudFront and Route 53 against common infrastructure (Layer 3 and 4) attacks."
"Which AWS service integrates with AWS Shield Advanced to provide additional protection for web applications at Layer 7?","AWS WAF","AWS IAM","AWS CloudTrail","AWS Config","AWS WAF (Web Application Firewall) works with AWS Shield Advanced to protect web applications against Layer 7 attacks."
"Which AWS Shield feature allows you to customise your DDoS protections based on your application's traffic patterns?","Custom Rules","Global Threat Intelligence","Automatic Mitigation","Traffic Mirroring","With Custom Rules, users can tailor DDoS protections in Shield Advanced."
"How does AWS Shield respond to a detected DDoS attack?","It automatically mitigates the attack","It sends a notification to the customer","It shuts down the affected resource","It redirects traffic to a backup region","AWS Shield automatically mitigates DDoS attacks by absorbing and scrubbing malicious traffic."
"Which AWS service provides detailed visibility into DDoS attacks mitigated by AWS Shield Advanced?","AWS CloudWatch","AWS Trusted Advisor","AWS Inspector","AWS Cost Explorer","AWS CloudWatch provides metrics and dashboards to monitor DDoS attacks and mitigations."
"What is the best approach to mitigate a large UDP flood attack using AWS Shield Advanced?","Engage the DDoS Response Team (DRT) for assistance","Manually block the offending IP addresses","Scale up the EC2 instances","Move the application to a different region","Engaging the DRT allows them to analyse and mitigate the attack using advanced techniques."
"What is the key benefit of using AWS Shield Advanced's proactive engagement model?","Faster response times during an attack","Lower monthly costs","Unlimited DDoS mitigation capacity","Automated patching of vulnerabilities","Proactive engagement allows for faster response times as the DRT is familiar with your environment."
"Which of the following is NOT a feature of AWS Shield Standard?","Automatic DDoS mitigation","Protection against common infrastructure layer attacks","Cost optimization recommendations","Protection for CloudFront and Route 53 resources","Cost optimization recommendations are not a feature of AWS Shield Standard."
"You want to protect your web application against application-layer DDoS attacks. Which AWS service should you primarily use in conjunction with AWS Shield?","AWS WAF","AWS IAM","AWS KMS","AWS CloudTrail","AWS WAF provides protection against application-layer (Layer 7) attacks, complementing AWS Shield's infrastructure protection."
"How does AWS Shield Advanced help reduce false positives in DDoS mitigation?","By using machine learning to analyse traffic patterns","By completely blocking all traffic from unknown sources","By requiring manual approval for every mitigation action","By ignoring traffic below a certain threshold","AWS Shield Advanced uses machine learning to analyse traffic patterns and reduce false positives during mitigation."
"What is the role of AWS Shield Advanced's 'Attack Diagnostics' feature?","To provide detailed information about mitigated DDoS attacks","To predict future DDoS attacks","To simulate DDoS attacks for testing purposes","To encrypt sensitive data during attacks","Attack Diagnostics provides detailed information about DDoS attacks that have been mitigated, aiding in analysis and prevention."
"Which statement is true regarding the pricing of AWS Shield Standard?","It is offered at no additional cost for AWS customers","It requires a monthly subscription fee","It is based on the amount of mitigated traffic","It is determined by the number of protected resources","AWS Shield Standard is offered at no additional cost to all AWS customers using CloudFront and Route 53."
"What action should you take if you are under a large DDoS attack that AWS Shield Standard cannot mitigate effectively?","Consider upgrading to AWS Shield Advanced and engaging the DRT","Immediately move your application to another cloud provider","Manually block all incoming traffic to your application","Ignore the attack and hope it subsides","Upgrading to AWS Shield Advanced provides enhanced protection and expert assistance from the DRT for complex attacks."
"Which of the following is a prerequisite for using AWS Shield Advanced?","A valid AWS Support plan","An active subscription to AWS GuardDuty","Enabling AWS Config in your account","Registering your protected resources with AWS Shield","To use Shield Advanced you need to subscribe and register your protected resources"
"What type of attacks does AWS Shield Standard primarily protect against?","Volumetric attacks targeting network and transport layers","Application layer attacks targeting specific vulnerabilities","Malicious bots scraping content from websites","SQL injection attacks against databases","AWS Shield Standard protects against common, frequently occurring network and transport layer attacks that target infrastructure."
"Which of the following is a key difference between AWS Shield Standard and AWS Shield Advanced?","AWS Shield Advanced offers 24/7 access to the DDoS Response Team (DRT)","AWS Shield Standard protects against a wider range of attack types","AWS Shield Standard is a paid service, while AWS Shield Advanced is free","AWS Shield Advanced only protects resources in specific AWS regions","AWS Shield Advanced offers 24/7 access to the DDoS Response Team (DRT) which is not available in Standard."
"You need to protect your web application against HTTP floods and SQL injection attacks. Which AWS services should you use in conjunction with AWS Shield?","AWS WAF and AWS Shield Advanced","AWS IAM and AWS CloudTrail","AWS Lambda and Amazon API Gateway","Amazon EC2 and Amazon S3","AWS WAF protects against application-layer attacks like HTTP floods and SQL injection, and works with Shield Advanced for comprehensive protection."
"How can you monitor the effectiveness of AWS Shield Advanced mitigations?","By using AWS CloudWatch metrics and dashboards","By reviewing AWS CloudTrail logs","By analysing VPC Flow Logs","By checking the AWS Trusted Advisor recommendations","AWS CloudWatch provides detailed metrics and dashboards for monitoring the effectiveness of Shield Advanced mitigations."
"Which of the following is NOT a benefit of using AWS Shield Advanced?","Cost protection against DDoS-related spikes in AWS charges","Improved application performance during attacks","Reduced operational overhead for DDoS mitigation","Automatic patching of application vulnerabilities","Shield Advanced provides cost protection, not automatic patching of application vulnerabilities."
"What is the recommended best practice for configuring AWS Shield Advanced for your applications?","Enable proactive engagement with the AWS DDoS Response Team (DRT)","Disable automatic DDoS mitigation to minimise false positives","Use the default AWS Shield Standard settings for all resources","Ignore the AWS Shield recommendations and create custom rules","Proactive engagement with the DRT is a recommended best practice for faster and more effective response during attacks."
"Which compliance standards are supported by AWS Shield?","PCI DSS, HIPAA, SOC","ISO 9001, ISO 27001, GDPR","CSA STAR, FedRAMP, NIST","CCPA, COPPA, FERPA","AWS Shield supports PCI DSS, HIPAA, and SOC compliance."
"What is the primary focus of AWS Shield's 'Application Layer Protection'?","Protecting against attacks targeting application vulnerabilities","Protecting against attacks targeting the network infrastructure","Protecting against attacks targeting databases","Protecting against attacks targeting DNS servers","Application Layer Protection in AWS Shield focuses on mitigating attacks that exploit vulnerabilities in web applications."
"What is the purpose of the AWS Shield Advanced 'Health-Based Detection' feature?","To detect attacks based on changes in application health","To detect attacks based on IP reputation","To detect attacks based on geographic location","To detect attacks based on user behaviour","Health-Based Detection detects attacks by monitoring changes in the health of your applications."
"What is the AWS DDoS Response Team (DRT)?","A team of AWS experts who help customers mitigate sophisticated DDoS attacks","A team of AWS sales representatives who promote AWS Shield","A team of AWS support engineers who troubleshoot technical issues","A team of AWS security auditors who perform penetration testing","The DRT is a team of AWS experts who provide assistance with mitigating sophisticated DDoS attacks."
"Which of the following is a common type of volumetric DDoS attack that AWS Shield helps mitigate?","UDP flood","SQL injection","Cross-site scripting (XSS)","Brute-force login attempts","UDP flood attacks are a common type of volumetric DDoS attack."
"How does AWS Shield Advanced integrate with AWS Firewall Manager?","To centrally manage and deploy WAF rules across multiple accounts and resources","To automatically patch vulnerabilities in EC2 instances","To monitor network traffic for malicious activity","To encrypt data at rest in S3 buckets","AWS Firewall Manager integrates with Shield Advanced to provide centralised management of WAF rules across multiple accounts and resources."
"What is the cost model for AWS Shield Advanced's 'DDoS cost protection' feature?","It provides credits for AWS usage spikes caused by DDoS attacks","It automatically reduces AWS charges during DDoS attacks","It offers a fixed monthly price regardless of AWS usage","It refunds all AWS charges incurred during DDoS attacks","DDoS cost protection provides credits for AWS usage spikes caused by DDoS attacks, helping to offset the increased costs."
"Which AWS service is typically used as a content delivery network (CDN) in conjunction with AWS Shield to distribute content and absorb traffic?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","Amazon CloudFront is a CDN that works with AWS Shield to distribute content and absorb traffic during attacks."
"What is the key benefit of using AWS Shield Advanced's 'Global Threat Intelligence' feature?","It provides real-time insights into global DDoS attack trends","It automatically blocks traffic from specific geographic regions","It allows you to share threat intelligence with other AWS customers","It encrypts all network traffic to protect against eavesdropping","Global Threat Intelligence provides insights into global DDoS attack trends, helping to proactively protect against emerging threats."
"You suspect that your application is under a DDoS attack, but you are not sure. What is the first step you should take to investigate?","Check AWS CloudWatch metrics for unusual traffic patterns","Immediately block all incoming traffic to your application","Contact the AWS DDoS Response Team (DRT) without investigation","Assume it is a false alarm and ignore the issue","Checking CloudWatch metrics is the first step to investigate potential DDoS attacks."
"What is a potential downside of over-relying on automated DDoS mitigation without proper configuration?","Potential for false positives and legitimate traffic being blocked","Increased latency for all users of the application","Higher costs due to increased AWS usage","Decreased security posture due to reduced visibility","Over-relying on automated mitigation can lead to false positives and blocking of legitimate traffic if not properly configured."
"Which of the following is NOT a recommended step for preparing for a potential DDoS attack?","Testing your DDoS mitigation strategy with simulated attacks","Documenting your incident response plan","Limiting access to your application to only trusted users","Setting up monitoring and alerting for unusual traffic patterns","Limiting access to only trusted users is not a practical recommendation."
"What is the role of AWS Shield Advanced's 'Protected Resources' list?","To specify the resources that should be protected by AWS Shield Advanced","To exclude specific resources from AWS Shield Advanced protection","To prioritise resources for DDoS mitigation","To track the cost of AWS Shield Advanced for each resource","The 'Protected Resources' list specifies the resources that should be protected by AWS Shield Advanced."
"How does AWS Shield help improve the availability of your applications during a DDoS attack?","By absorbing and mitigating malicious traffic, preventing it from reaching your application","By automatically scaling up your application infrastructure to handle the increased load","By redirecting traffic to a backup region in case of an attack","By completely isolating your application from the internet during an attack","AWS Shield improves availability by absorbing and mitigating malicious traffic, preventing it from overwhelming your application."
"Which of the following is a key consideration when choosing between AWS Shield Standard and AWS Shield Advanced?","The complexity and sophistication of the expected DDoS attacks","The geographic location of your users","The type of database used by your application","The programming language used to develop your application","The complexity and sophistication of the expected DDoS attacks is a key factor in choosing between Shield Standard and Advanced."
"Which statement is true about AWS Shield Advanced cost protection?","It only applies to resources covered by a Business or Enterprise Support plan","It applies to EC2, ELB, CloudFront, and Route 53","It fully covers all excess charges regardless of magnitude","It requires explicit configuration of billing alerts","It applies to EC2, ELB, CloudFront, and Route 53 charges during a DDoS attack."
"What's the advantage of using AWS Shield Advanced's anomaly detection capabilities?","It identifies and alerts you to unusual traffic patterns which might indicate an attack","It automatically blocks all traffic that doesn't conform to a set baseline","It creates detailed forensic reports on every packet that traverses your network","It allows you to replay past attacks to test your mitigation strategies","AWS Shield Advanced's anomaly detection identifies unusual traffic patterns that may indicate an attack."
"When using AWS Shield Advanced, what's the purpose of configuring 'response pages'?","To serve a static page to users if the application is under attack to maintain some level of service","To automatically redirect users to a backup application during an attack","To display error messages to users when the application is experiencing problems","To collect user feedback about the application's performance during normal operation","Response pages serve a static page to users during an attack, providing some level of service and indicating the situation."
"What is the main reason to define CloudWatch alarms related to network traffic when you're protected by AWS Shield?","To be proactively notified of potential DDoS attacks before they significantly impact your application","To automatically trigger AWS Shield Advanced mitigation features","To generate compliance reports proving your application is protected from DDoS attacks","To optimise the cost of your AWS Shield Advanced subscription","CloudWatch alarms enable proactive notification of potential DDoS attacks."
"Which of the following is NOT a resource type that can be protected by AWS Shield Advanced?","AWS Lambda functions","Elastic Load Balancers (ELBs)","Amazon CloudFront distributions","Amazon Route 53 hosted zones","AWS Lambda functions cannot be directly protected by AWS Shield Advanced."
"Why is it important to review and update your AWS WAF rules regularly when using AWS Shield Advanced?","To ensure that the rules are effective against the latest application-layer attack techniques","To reduce the cost of your AWS Shield Advanced subscription","To improve the performance of your application","To comply with AWS security best practices","Regular updates ensure your WAF rules remain effective against evolving threats."
"How does the AWS Shield Advanced DDoS Response Team (DRT) typically interact with customers during an active DDoS attack?","They provide guidance and support to help customers fine-tune their mitigation strategies","They automatically take over control of the customer's AWS account to resolve the issue","They redirect all traffic to a pre-configured disaster recovery site","They contact law enforcement to report the attack","The DRT provides guidance and supports customers in fine-tuning their mitigation strategies."
"What is a key consideration when designing your application architecture to maximise the effectiveness of AWS Shield?","Use a distributed architecture across multiple AWS regions","Store all your data in a single S3 bucket","Run all your application code on a single EC2 instance","Disable logging to reduce network traffic","Using a distributed architecture across multiple AWS regions improves resilience."
"You're using AWS Shield Advanced. What kind of support can you expect from the DDoS Response Team (DRT) regarding custom mitigation rules?","They can help you create and refine custom rules based on your application's specific traffic patterns","They will automatically generate custom rules for you based on historical traffic data","They will only provide support for pre-defined rules and cannot assist with custom configurations","They will review your existing custom rules to ensure they comply with AWS best practices","The DRT helps with creating and refining custom rules tailored to your application's specific needs."
"Which of the following is an example of a layer 7 DDoS attack that AWS Shield Advanced in conjunction with AWS WAF would be effective at mitigating?","HTTP floods targeting specific URLs","SYN floods overwhelming the network","UDP reflection attacks consuming bandwidth","DNS amplification attacks saturating the DNS servers","HTTP floods are application layer attacks that AWS Shield Advanced and WAF are effective at mitigating."
"What is the primary benefit of using AWS Shield Advanced's real-time metrics and reporting during a DDoS attack?","To gain better visibility into the attack characteristics and the effectiveness of the mitigation efforts","To automatically adjust your application's scaling policies based on the attack intensity","To predict future DDoS attacks based on historical data","To automatically generate invoices for AWS usage related to the attack","Real-time metrics provide visibility into the attack and the effectiveness of mitigation."
"In the context of AWS Shield Advanced, what is the 'attack surface'?","The set of resources that are exposed to potential DDoS attacks","The physical location of the servers hosting your application","The total amount of network bandwidth available to your application","The number of users accessing your application at any given time","The attack surface is the set of resources exposed to potential DDoS attacks."