"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS IAM Identity Center, what is the primary function of a permission set?","To define access levels to AWS resources.","To manage user passwords.","To configure multi-factor authentication.","To define network access control lists.","A permission set in IAM Identity Center defines the level of access that users and groups have to AWS resources. It specifies the actions that they are allowed to perform."
"What is the benefit of using AWS IAM Identity Center over managing individual IAM users in each AWS account?","Centralised management of identities and access.","Increased network bandwidth.","Automated server patching.","Improved database performance.","IAM Identity Center provides a single place to manage user identities and their access to multiple AWS accounts, simplifying administration and improving security."
"Which AWS service is commonly integrated with AWS IAM Identity Center to provide directory services?","AWS Directory Service","AWS Config","AWS CloudTrail","AWS Systems Manager","AWS Directory Service provides directory services like AWS Managed Microsoft AD, which can be integrated with IAM Identity Center to manage user identities."
"What type of resource can you NOT directly assign a permission set to in AWS IAM Identity Center?","An individual IAM user","An AWS account","A group of users","A role in an AWS Account","Permission sets are assigned to accounts. Users and groups are then granted access to those accounts via the permission sets."
"What is the purpose of the AWS IAM Identity Center sync with external identity providers?","To synchronise user accounts and group memberships.","To synchronise S3 bucket contents.","To synchronise EC2 instance configurations.","To synchronise Route 53 DNS records.","The sync feature enables IAM Identity Center to maintain up-to-date information about users and their group memberships from external identity providers."
"In AWS IAM Identity Center, what is the relationship between a user, a group, and a permission set?","Users are assigned to groups, which are then assigned permission sets.","Users are assigned permission sets, which are then assigned to groups.","Permission sets are assigned to users, who are then assigned to groups.","Groups are assigned to users, which are then assigned permission sets.","Users are organised into groups, and these groups are then assigned permission sets to grant access to AWS accounts."
"What is the first step you typically take when setting up AWS IAM Identity Center for the first time?","Enable IAM Identity Center in your AWS organisation.","Create a new IAM user for each employee.","Configure a VPC endpoint for secure access.","Create an S3 bucket for storing access logs.","IAM Identity Center needs to be enabled for your AWS organisation before other configurations can be made."
"If a user in AWS IAM Identity Center is assigned multiple permission sets for the same AWS account, how is the effective set of permissions determined?","The permissions are combined to create a union of all allowed actions.","The permissions from the permission set assigned earliest take precedence.","The permissions from the permission set assigned latest take precedence.","The permissions are denied, and the user has no access.","When multiple permission sets are assigned, the permissions are combined, effectively granting the user all the actions allowed by any of the assigned permission sets."
"What AWS service is used to view audit logs for AWS IAM Identity Center activity?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail records API calls made to IAM Identity Center, providing an audit trail of user activity and configuration changes."
"What is a key advantage of using AWS IAM Identity Center with AWS Organisations?","Centralised access management across multiple AWS accounts.","Automated patching of EC2 instances.","Cost optimisation recommendations for S3 storage.","Real-time threat detection for VPC traffic.","IAM Identity Center simplifies managing access to multiple accounts within an organisation from a single point."
"What happens when a user is deactivated in AWS IAM Identity Center?","The user's access to all AWS accounts is immediately revoked.","The user's access is revoked only after a 24-hour delay.","The user's access is revoked only after a manual approval process.","The user's access is only revoked when they are deleted.","Deactivating a user in IAM Identity Center immediately revokes their access to all AWS accounts and applications integrated with the service."
"When integrating AWS IAM Identity Center with an external Identity Provider (IdP), what is typically exchanged between the two systems?","SAML assertions","SSH keys","X.509 certificates","API keys","IAM Identity Center integrates with external Identity Providers using SAML (Security Assertion Markup Language) assertions for authentication and authorisation."
"Which of the following is a key feature of the AWS IAM Identity Center console?","Centralised dashboard for managing users and groups.","Automated deployment of EC2 instances.","Real-time monitoring of network traffic.","Automated backup of RDS databases.","IAM Identity Center provides a centralised dashboard for managing users, groups, applications, and permission sets."
"How does AWS IAM Identity Center simplify the process of granting temporary access to AWS resources?","By providing short-term credentials for users and applications.","By automatically rotating IAM keys every hour.","By integrating with AWS KMS for encrypting data in transit.","By enforcing multi-factor authentication for all IAM users.","IAM Identity Center provides short-term credentials for users so that they can access the AWS resources they require only for the duration needed. It does not control IAM keys, encryption or MFA directly."
"What is the maximum session duration you can configure in AWS IAM Identity Center?","12 hours","24 hours","72 hours","Unlimited","The maximum session duration that can be configured in AWS IAM Identity Center is 12 hours."
"What is the primary benefit of using AWS IAM Identity Center with AWS Command Line Interface (CLI) and AWS Software Development Kits (SDKs)?","Simplified credential management.","Automated code deployment.","Real-time debugging of applications.","Automated scaling of database instances.","IAM Identity Center simplifies credential management when using the AWS CLI and SDKs by handling the process of obtaining and refreshing temporary credentials."
"Which AWS IAM Identity Center feature allows you to automatically provision users and groups from your identity provider?","SCIM (System for Cross-domain Identity Management)","STS (Security Token Service)","CloudTrail","CloudWatch","SCIM enables automated user and group provisioning from your identity provider to IAM Identity Center, simplifying user management."
"What is the purpose of the 'AWSAdministatorAccess' built-in permission set in AWS IAM Identity Center?","Grants full administrative access to AWS resources.","Grants read-only access to AWS resources.","Grants limited access to specific AWS services.","Denies access to all AWS resources.","The 'AWSAdministratorAccess' permission set grants full administrative privileges to AWS resources, allowing users to perform any action."
"In AWS IAM Identity Center, what is the 'Principal' in the context of access management?","The user or group being granted access.","The AWS resource being accessed.","The permission set being applied.","The network connection being used.","In access management, the 'Principal' refers to the entity (user, group, or service) that is requesting access to a resource."
"What is the role of an IAM Identity Center instance in a multi-account AWS environment?","To serve as a central identity provider for all accounts.","To serve as a backup system for IAM users.","To manage network configurations across accounts.","To manage security groups across accounts.","IAM Identity Center is designed to be a central point for managing identities and access across multiple AWS accounts within an organisation."
"How does AWS IAM Identity Center improve security posture compared to individual IAM users per account?","Reduces the attack surface by centralising identity management.","Increases network bandwidth between AWS accounts.","Automates security patching across all EC2 instances.","Provides real-time threat detection for VPC traffic.","Centralising identity management in IAM Identity Center reduces the attack surface by limiting the number of individual IAM users and their associated keys, improving overall security."
"Which of the following is NOT a supported attribute for user provisioning via SCIM in AWS IAM Identity Center?","Email address","User's favourite colour","Display name","Username","While SCIM supports standard attributes like email, display name and username, arbitrary attributes such as 'user's favourite colour' are not supported."
"How can you enforce multi-factor authentication (MFA) for users accessing AWS resources through AWS IAM Identity Center?","Configure MFA in your identity provider.","Configure MFA directly on AWS IAM roles.","Configure MFA in AWS CloudTrail.","Configure MFA in AWS Config.","IAM Identity Center relies on your identity provider for managing MFA. You configure MFA within your IdP, and that is then enforced for access to AWS resources."
"What is the purpose of the AWS IAM Identity Center 'Assignments' tab?","To manage user and group assignments to AWS accounts.","To manage permission set assignments to AWS resources.","To manage network configurations for AWS accounts.","To manage security groups for AWS resources.","The 'Assignments' tab in the IAM Identity Center console is used to manage the assignments of users and groups to specific AWS accounts."
"When integrating AWS IAM Identity Center with a custom application, what is typically required?","Configuring the application to trust SAML assertions from IAM Identity Center.","Installing the AWS CLI on the application server.","Configuring the application to use AWS KMS for encryption.","Configuring the application to use AWS CloudWatch for logging.","To integrate a custom application, it needs to be configured to trust SAML assertions provided by IAM Identity Center for authentication."
"How does AWS IAM Identity Center assist with compliance requirements?","By providing centralised audit logs of user access and activity.","By automatically patching security vulnerabilities in EC2 instances.","By automatically encrypting all data in S3 buckets.","By automatically configuring network firewalls for VPCs.","IAM Identity Center improves compliance by providing centralised audit logs through CloudTrail, enabling you to track user access and activity across your AWS accounts."
"What is the main difference between AWS IAM and AWS IAM Identity Center?","IAM manages permissions within a single AWS account, while IAM Identity Center manages access across multiple accounts.","IAM is used for managing network access, while IAM Identity Center manages user authentication.","IAM is used for managing server configurations, while IAM Identity Center manages database access.","IAM is used for managing cost optimisation, while IAM Identity Center manages security alerts.","IAM is designed to manage access within a single AWS account. IAM Identity Center focuses on providing centralised access management across multiple accounts in an AWS organisation."
"In AWS IAM Identity Center, what is the purpose of 'AWS SSO Access Analyzer'?","To identify unused access permissions across AWS accounts.","To identify network vulnerabilities in VPCs.","To identify cost optimisation opportunities for EC2 instances.","To identify misconfigurations in S3 bucket policies.","AWS SSO Access Analyzer helps you identify unused access permissions granted through IAM Identity Center, enabling you to remove unnecessary privileges and improve security."
"Which of the following AWS services is commonly used to store logs generated by AWS IAM Identity Center?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","Logs generated by AWS IAM Identity Center are typically stored in Amazon S3 buckets for long-term storage and analysis."
"How can you ensure that users in AWS IAM Identity Center only have access to specific AWS services within an account?","By creating custom permission sets with granular access policies.","By configuring network access control lists (ACLs) for the account.","By configuring security groups for EC2 instances.","By configuring IAM roles directly within the account.","Custom permission sets allow you to define specific access policies that restrict users to only the necessary AWS services, improving security and compliance."
"What is the maximum number of AWS accounts that can be managed by a single AWS IAM Identity Center instance?","There is no explicit limit.","100","500","1000","AWS IAM Identity Center has no explicit limit on the number of AWS accounts it can manage. The service is designed to scale to meet the needs of organisations of any size."
"In AWS IAM Identity Center, what is the purpose of the 'Trust for AWS SSO' setting in AWS Managed Microsoft AD?","To enable IAM Identity Center to trust users and groups in AWS Managed Microsoft AD.","To enable AWS Managed Microsoft AD to trust users and groups in IAM Identity Center.","To enable cross-account access between AWS accounts.","To enable multi-factor authentication for AWS Managed Microsoft AD users.","The 'Trust for AWS SSO' setting allows IAM Identity Center to trust the user identities and group memberships defined in your AWS Managed Microsoft AD directory."
"Which of the following factors should you consider when choosing between the AWS IAM Identity Center directory and an external Identity Provider?","Existing infrastructure and the need for user lifecycle management.","The cost of EC2 instances in your AWS account.","The number of S3 buckets you are using.","The region in which your AWS resources are deployed.","When deciding whether to use the IAM Identity Center directory or an external IdP, you should consider your existing infrastructure, user lifecycle management needs, and any existing investments in identity management solutions."
"What is a primary use case for integrating AWS IAM Identity Center with Amazon QuickSight?","To manage user access to QuickSight dashboards and datasets.","To automate the creation of QuickSight visualisations.","To optimise the performance of QuickSight queries.","To integrate QuickSight with AWS CloudTrail for auditing.","Integrating IAM Identity Center with QuickSight allows you to centrally manage user access to QuickSight dashboards and datasets."
"How can you use AWS IAM Identity Center to control access to applications running on Amazon EC2 instances?","By integrating IAM Identity Center with an identity provider and configuring applications to trust SAML assertions.","By directly assigning IAM roles to EC2 instances.","By configuring security groups to restrict network access.","By configuring AWS Config rules to monitor application configurations.","To control access to applications running on EC2, you need to integrate IAM Identity Center with your identity provider and configure your applications to trust SAML assertions from IAM Identity Center."
"Which of the following features is NOT provided by AWS IAM Identity Center?","Automated security patching of EC2 instances.","Centralised user management across multiple AWS accounts.","Single sign-on (SSO) access to AWS resources.","Granular access control using permission sets.","IAM Identity Center does not provide automated security patching of EC2 instances. That functionality is managed by other services like AWS Systems Manager."
"When should you consider using AWS IAM Identity Center instead of AWS IAM roles for EC2 instances?","When you need to grant temporary access to human users across multiple AWS accounts.","When you need to grant permissions to an EC2 instance to access other AWS services.","When you need to configure network access control lists (ACLs) for EC2 instances.","When you need to encrypt the root volume of an EC2 instance.","IAM Identity Center is designed for managing access for human users, particularly across multiple AWS accounts. IAM roles are designed for granting permissions to AWS resources like EC2 instances."
"In AWS IAM Identity Center, what is the purpose of the 'Trust relationship' configuration?","To establish a trust between IAM Identity Center and an external identity provider.","To establish a trust between AWS accounts within an organisation.","To establish a trust between EC2 instances and IAM roles.","To establish a trust between S3 buckets and IAM users.","The 'Trust relationship' configuration is used to establish a secure connection between IAM Identity Center and your external identity provider, allowing users to authenticate with their existing credentials."
"How can you centrally manage access to AWS resources based on user attributes, such as department or job title, using AWS IAM Identity Center?","By configuring attribute-based access control (ABAC) policies in permission sets.","By configuring resource-based policies in IAM.","By configuring security groups to restrict network access based on user attributes.","By configuring AWS Config rules to enforce compliance based on user attributes.","Attribute-based access control (ABAC) allows you to define access policies based on user attributes, such as department or job title, providing fine-grained access control."
"What is the purpose of the 'AWS SSO URL' provided after enabling AWS IAM Identity Center?","The URL that users access to sign in to AWS resources through IAM Identity Center.","The URL that administrators use to configure IAM Identity Center settings.","The URL that is used to download the AWS CLI.","The URL that is used to access the AWS Management Console.","The AWS SSO URL is the URL that users use to access the IAM Identity Center sign-in page, from where they can access their assigned AWS resources."
"How does AWS IAM Identity Center integrate with AWS CloudShell?","It provides a mechanism for users to authenticate to CloudShell using their IAM Identity Center credentials.","It automatically configures CloudShell environments for users.","It provides a mechanism for users to access AWS resources in a sandbox environment.","It provides a mechanism for users to access AWS resources without credentials.","AWS IAM Identity Center provides a seamless way to use your SSO credentials with CloudShell environments allowing for temporary secure access to your AWS environments."
"In AWS IAM Identity Center, what is a 'Permission Set'?","A collection of policies that define access to AWS resources.","A group of users with similar access requirements.","A method to enforce multi-factor authentication.","A way to monitor user activity.","A Permission Set in IAM Identity Center is a collection of policies that specify the level of access a user or group has to AWS resources. It defines what actions they can perform."
"Which of the following is a benefit of using AWS IAM Identity Center over managing individual IAM users in each AWS account?","Centralised access management and single sign-on.","Lower cost for small deployments.","Increased flexibility in assigning permissions.","Direct access to EC2 instances without authentication.","IAM Identity Center provides centralised access management, making it easier to manage user access across multiple AWS accounts and enabling Single Sign-On."
"What authentication method does AWS IAM Identity Center primarily use to authenticate users?","SAML 2.0","LDAP","Kerberos","X.509 Certificates","IAM Identity Center uses SAML 2.0 to federate identities, allowing users to authenticate through their existing identity provider."
"What is the purpose of the AWS IAM Identity Center dashboard?","To provide a central view of user activity and access assignments.","To manage EC2 instances.","To configure VPC settings.","To create and manage S3 buckets.","The IAM Identity Center dashboard provides a central view of user activity, access assignments, and configured AWS accounts."
"What is the relationship between AWS IAM Identity Center and AWS Organizations?","IAM Identity Center integrates with AWS Organizations to manage access across multiple accounts.","IAM Identity Center replaces AWS Organizations.","IAM Identity Center is a feature of AWS Organizations.","There is no relationship between IAM Identity Center and AWS Organizations.","IAM Identity Center integrates with AWS Organizations, allowing you to centrally manage access to multiple AWS accounts within your organisation."
"In AWS IAM Identity Center, how do you grant a user access to an AWS account?","By assigning a Permission Set to the user for that account.","By creating an IAM user in the account and assigning the user to a group in IAM Identity Center.","By directly adding the user to the AWS account's IAM policy.","By enabling root user access for the AWS account.","You grant a user access to an AWS account by assigning a Permission Set to the user or group within IAM Identity Center. This defines what the user can do in the AWS account."
"Which of the following is NOT a feature of AWS IAM Identity Center?","Password complexity enforcement within AWS.","Single Sign-On (SSO).","Centralised user management.","Multi-account access management.","IAM Identity Center uses your existing identity provider for password complexity requirements, so password complexity enforcement within AWS is NOT a feature."
"What type of directory service does AWS IAM Identity Center utilise?","AWS IAM Identity Center Directory or it integrates with external identity providers.","Microsoft Active Directory.","OpenLDAP.","AWS Cloud Directory.","IAM Identity Center utilises its own built-in directory or integrates with external identity providers, such as Okta or Azure AD."
"What is the first step in setting up AWS IAM Identity Center for multi-account access?","Enable IAM Identity Center and connect it to your identity source.","Create IAM users in each AWS account.","Configure VPC peering.","Launch an EC2 instance to host the IAM Identity Center service.","The first step is to enable IAM Identity Center and connect it to your existing identity source. Then connect it to your AWS accounts."
"How can you audit user access and activity in AWS IAM Identity Center?","By viewing the AWS CloudTrail logs.","By enabling S3 access logging.","By monitoring CloudWatch metrics.","By configuring VPC Flow Logs.","AWS IAM Identity Center integrates with AWS CloudTrail, allowing you to audit user access and activity by viewing the CloudTrail logs."
"What is the purpose of 'Claims' in the context of AWS IAM Identity Center?","Attributes about a user passed from the identity provider to AWS.","Security credentials stored in IAM Identity Center.","A list of AWS resources that a user can access.","A request for access to an AWS account.","Claims are attributes about a user (e.g., name, email, group membership) passed from the identity provider to AWS during the authentication process."
"What is the maximum number of AWS accounts that can be managed by a single AWS IAM Identity Center instance?","There is no specific limit; it scales with AWS Organizations.","10","100","1000","IAM Identity Center scales with AWS Organizations and can manage access to a large number of AWS accounts, effectively without a practical limit for most use cases."
"Which of the following AWS services can be used as an identity source for AWS IAM Identity Center?","AWS IAM Identity Center Directory, Active Directory, Okta, Azure AD.","Amazon Cognito.","AWS STS.","AWS KMS.","IAM Identity Center supports the IAM Identity Center Directory and many other identity providers such as Active Directory, Okta, and Azure AD."
"What is a recommended security best practice when using AWS IAM Identity Center?","Enforce multi-factor authentication (MFA) for all users.","Disable CloudTrail.","Grant root access to all users.","Use a single, shared AWS account for all resources.","Enforcing MFA for all users is a crucial security best practice that adds an extra layer of security to the authentication process."
"What is the primary advantage of using AWS IAM Identity Center for managing access to multiple AWS accounts compared to using IAM roles?","Centralised management and Single Sign-On (SSO).","Lower cost for small deployments.","Greater control over individual IAM policies.","No need for an identity provider.","IAM Identity Center provides centralised management and SSO, simplifying access management across multiple AWS accounts."
"Which AWS service is commonly used with AWS IAM Identity Center for providing users access to command-line tools and AWS APIs?","AWS CLI with configured SSO profile.","AWS CloudShell.","AWS CodeCommit.","Amazon SQS.","AWS CLI can be configured to use profiles generated by the AWS IAM Identity Center. This allows users to authenticate and assume roles to access AWS resources through the command line."
"How does AWS IAM Identity Center simplify the process of granting temporary access to AWS resources?","It allows users to assume predefined Permission Sets for limited durations.","It automatically generates temporary IAM users.","It removes the need for IAM roles.","It disables all security checks.","IAM Identity Center lets users assume predefined Permission Sets for a defined period of time, providing temporary and secure access."
"Which of the following best describes the 'Principle of Least Privilege' in the context of AWS IAM Identity Center?","Granting users only the minimum permissions required to perform their tasks.","Granting users full administrative access.","Denying all access to AWS resources.","Granting users access to all AWS accounts.","The 'Principle of Least Privilege' means giving users only the minimum permissions needed to perform their tasks, enhancing security."
"What is the relationship between AWS IAM Identity Center and AWS Single Sign-On (SSO)?","AWS IAM Identity Center is the successor to AWS Single Sign-On (SSO).","AWS IAM Identity Center is a component of AWS Single Sign-On (SSO).","AWS Single Sign-On (SSO) is a component of AWS IAM Identity Center.","There is no relationship between AWS IAM Identity Center and AWS Single Sign-On (SSO).","AWS IAM Identity Center is the successor to AWS Single Sign-On (SSO) and provides enhanced capabilities for centralised identity management."
"In AWS IAM Identity Center, what does the term 'identity source' refer to?","The directory service or identity provider that stores user identities.","The AWS region where IAM Identity Center is deployed.","The IAM roles used to access AWS resources.","The EC2 instances hosting the IAM Identity Center service.","An identity source is the directory service or identity provider, such as the AWS IAM Identity Center directory or an external provider, that stores and manages user identities."
"What is the best way to manage application access using AWS IAM Identity Center?","Assigning applications to Permission Sets.","Creating dedicated IAM users for each application.","Managing application access directly in IAM.","Using AWS Cognito for application authentication.","You should assign the applications to Permission Sets for each user or group who needs access to it."
"Which of the following is a key benefit of using AWS IAM Identity Center with AWS Control Tower?","Centralised access control across all accounts provisioned by Control Tower.","Automated patching of EC2 instances.","Simplified VPC configuration.","Automated creation of S3 buckets.","IAM Identity Center and Control Tower provide centralised access control across all accounts governed by AWS Control Tower, enforcing consistent security policies."
"How does AWS IAM Identity Center help to reduce the operational overhead of managing AWS access?","By centralising user management and providing single sign-on.","By automating the creation of IAM roles.","By eliminating the need for IAM policies.","By providing direct access to EC2 instances without authentication.","IAM Identity Center helps reduce operational overhead by centralising user management and providing single sign-on, simplifying the management of AWS access across multiple accounts."
"When configuring AWS IAM Identity Center, what is the purpose of the 'Attribute Mapping' feature?","To map user attributes from the identity provider to AWS attributes.","To map AWS resources to IAM roles.","To map IAM policies to Permission Sets.","To map user groups to AWS accounts.","The 'Attribute Mapping' feature maps user attributes from the identity provider (e.g., email, name) to corresponding AWS attributes, ensuring accurate user identification and authorisation."
"What is the recommended way to grant temporary access to sensitive AWS resources using AWS IAM Identity Center?","Using temporary Permission Sets with limited permissions and duration.","Granting permanent access to all resources.","Disabling all security checks.","Sharing root account credentials.","Temporary Permission Sets with limited permissions and duration are the best approach to granting secure, temporary access."
"Which of the following is a valid use case for AWS IAM Identity Center?","Providing employees with single sign-on access to multiple AWS accounts and applications.","Managing network traffic within a VPC.","Monitoring the performance of EC2 instances.","Storing and retrieving data from S3 buckets.","IAM Identity Center is specifically designed to provide employees with single sign-on access to multiple AWS accounts and applications."
"What is the main difference between an IAM role and a Permission Set in AWS IAM Identity Center?","An IAM role is assumed by an AWS service or application, while a Permission Set is assigned to users or groups.","An IAM role is assigned to users, while a Permission Set is assumed by AWS services.","IAM roles are used for federated access, while Permission Sets are used for internal access.","There is no difference; they are interchangeable.","IAM roles are assumed by AWS services or applications, while Permission Sets are assigned to users or groups to define their access to AWS resources."
"How does AWS IAM Identity Center support compliance requirements such as GDPR and HIPAA?","By providing centralised auditing and access control.","By automatically encrypting all data.","By scanning for vulnerabilities.","By providing a compliance dashboard.","IAM Identity Center helps support compliance requirements by providing centralised auditing and access control, enabling organisations to demonstrate and enforce security policies."
"You need to ensure that users in your organisation can only access AWS resources from within your corporate network. How can you achieve this using AWS IAM Identity Center?","By configuring Conditional Access policies based on IP address.","By disabling all public internet access.","By creating a VPN connection to each AWS account.","By using AWS Shield.","Conditional Access policies can be configured to restrict access based on network location or other contextual factors."
"What is the primary benefit of integrating AWS IAM Identity Center with a third-party identity provider (IdP) like Okta or Azure AD?","Leveraging existing user directories and authentication mechanisms.","Reducing the cost of AWS services.","Improving the performance of EC2 instances.","Simplifying VPC configuration.","Integrating with a third-party IdP allows you to leverage your existing user directories and authentication mechanisms, providing a seamless user experience."
"Which of the following is NOT a supported attribute for attribute-based access control (ABAC) in AWS IAM Identity Center?","User's department","User's job title","User's IP address","User's location","The IP address of the user is not directly supported as an attribute for attribute-based access control (ABAC) in AWS IAM Identity Center."
"How can you ensure that users are automatically provisioned and deprovisioned in AWS IAM Identity Center when they join or leave your organisation?","By configuring automatic provisioning with your identity provider.","By manually creating and deleting users in IAM Identity Center.","By disabling all user management.","By using AWS CloudFormation.","Automatic provisioning with your identity provider allows you to synchronise user accounts and groups, ensuring that users are automatically provisioned and deprovisioned."
"What is the purpose of the 'AWS Reserved' prefix for Permission Set names in AWS IAM Identity Center?","To indicate that the Permission Set is managed by AWS and cannot be modified.","To indicate that the Permission Set is used for administrative access.","To indicate that the Permission Set is used for temporary access.","To indicate that the Permission Set is used for read-only access.","The 'AWS Reserved' prefix indicates that the Permission Set is managed by AWS and cannot be modified, providing a consistent set of baseline permissions."
"What is the impact of deleting an AWS IAM Identity Center instance?","All user access to AWS accounts managed by the instance will be revoked.","All AWS accounts will be deleted.","All IAM roles will be deleted.","All S3 buckets will be deleted.","Deleting an IAM Identity Center instance will revoke user access to all AWS accounts managed by that instance."
"Which AWS service should you use to monitor the sign-in activity of users using AWS IAM Identity Center?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail is the AWS service you should use to monitor the sign-in activity of users using AWS IAM Identity Center"
"Which of the following is a key advantage of using AWS IAM Identity Center in conjunction with AWS Organizations?","Centralised management of user access across all accounts in the organisation.","Automated cost optimisation across all accounts.","Automated patching of EC2 instances across all accounts.","Automated creation of VPCs in all accounts.","IAM Identity Center provides centralised management of user access across all accounts within an AWS Organization, simplifying multi-account management."
"Which of the following is a common use case for AWS IAM Identity Center in a hybrid cloud environment?","Providing single sign-on access to both AWS resources and on-premises applications.","Migrating on-premises servers to AWS.","Managing network traffic between AWS and on-premises environments.","Encrypting data in transit between AWS and on-premises environments.","IAM Identity Center facilitates single sign-on access to both AWS resources and on-premises applications, enabling a seamless hybrid cloud experience."
"Which of the following is the primary way to customize the user sign-in experience in AWS IAM Identity Center?","By configuring custom branding and messaging.","By modifying the underlying IAM policies.","By creating custom IAM roles.","By using AWS CloudFormation.","You can customise the user sign-in experience in IAM Identity Center by configuring custom branding, adding logos, and customising sign-in messages."
"How does AWS IAM Identity Center help to simplify the process of granting access to contractors or temporary workers?","By providing temporary Permission Sets with limited access and duration.","By granting full administrative access.","By disabling all security checks.","By sharing root account credentials.","Temporary Permission Sets allow you to grant contractors or temporary workers limited access to AWS resources for a specific duration, enhancing security."
"You are using AWS IAM Identity Center to manage access to multiple AWS accounts. A user needs access to an S3 bucket in one of the accounts, but only for read-only access. How would you grant this access?","Create a Permission Set with read-only access to the S3 bucket and assign it to the user for that account.","Grant the user full administrative access to the account.","Create an IAM user in the account and grant it read-only access to the S3 bucket.","Disable all access controls for the S3 bucket.","The correct approach is to create a Permission Set with read-only access to the S3 bucket and assign it to the user for the relevant account. This follows the principle of least privilege."
"When integrating AWS IAM Identity Center with an existing Active Directory, what is the recommended approach?","Use the AWS AD Connector.","Create IAM users for each Active Directory user.","Migrate the Active Directory to AWS.","Disable Active Directory authentication.","AWS AD Connector is the recommended service to connect an existing on-premises Active Directory to AWS, enabling users to use their existing credentials for authentication."
"What is the maximum session duration that can be configured for a Permission Set in AWS IAM Identity Center?","12 hours","1 hour","24 hours","Unlimited","The maximum session duration that can be configured for a Permission Set in AWS IAM Identity Center is 12 hours."
"Which of the following AWS IAM Identity Center features would you use to delegate administrative access to another user in your organisation, while adhering to the principle of least privilege?","Create a Permission Set with the necessary administrative privileges and assign it to the user.","Grant the user full administrative access to all AWS accounts.","Share the root account credentials with the user.","Disable all access controls.","Create a Permission Set with the specific administrative privileges required and assign it to the user. This ensures that the user only has the permissions they need, adhering to the principle of least privilege."
"Which AWS service is used for storing and managing the users and groups when choosing AWS IAM Identity Center Directory as an identity source?","AWS IAM Identity Center Directory","AWS IAM","Amazon Cognito","AWS Directory Service","IAM Identity Center Directory is a basic cloud directory that is part of IAM Identity Center and can store users and groups"
"What is the best practice for revoking user access to AWS resources managed by AWS IAM Identity Center when an employee leaves the company?","Disable the user's account in the identity source (e.g., Active Directory, IAM Identity Center Directory).","Delete the user's IAM role.","Delete the user's AWS account.","Disable all access controls.","Disabling the user's account in the identity source (e.g., Active Directory, IAM Identity Center Directory) will automatically revoke their access to all AWS resources managed by IAM Identity Center."
"Which of the following is true regarding the use of IAM roles and Permission Sets when using AWS IAM Identity Center?","Permission Sets are created within IAM Identity Center and assigned to users, whereas IAM roles are created within individual AWS accounts and can be assumed by users who are authorized to do so.","IAM roles are created within IAM Identity Center and assigned to users, whereas Permission Sets are created within individual AWS accounts and can be assumed by users who are authorized to do so.","IAM roles and Permission Sets are interchangeable and can be used in any combination.","IAM roles and Permission Sets are not used together; IAM Identity Center eliminates the need for IAM roles.","With IAM Identity Center, Permission Sets are created within the IAM Identity Center to grant specified permissions and an IAM Role is created within each AWS account to which access is being granted. A trust relationship is created between IAM Identity Center and the role in the account."
"Which of the following security controls is NOT directly provided by AWS IAM Identity Center?","Vulnerability scanning of EC2 instances.","Multi-factor authentication enforcement.","Centralised access management.","Single sign-on (SSO).","Vulnerability scanning of EC2 instances requires a separate service like Amazon Inspector."
"In AWS IAM Identity Center, what is a permission set?","A collection of permissions that define access to AWS resources","A group of users with similar access requirements","A policy defining authentication methods","A role assumed by an AWS service","A permission set defines the level of access that a user or group has to specific AWS resources and services when they access them through IAM Identity Center."
"What is the primary purpose of AWS IAM Identity Center?","To centrally manage access to multiple AWS accounts and applications","To provide federated access to on-premises resources only","To manage AWS IAM roles within a single account","To act as a replacement for AWS IAM","IAM Identity Center is designed to centrally manage access to multiple AWS accounts and integrated cloud applications, simplifying identity management."
"Which authentication method is NOT natively supported by AWS IAM Identity Center?","Multi-Factor Authentication (MFA)","Password-based authentication","Kerberos authentication","Social Identity Providers (e.g., Google, Facebook)","Kerberos authentication is not natively supported. IAM Identity Center supports MFA, password authentication and integration with SAML-based and social identity providers."
"What type of directory can be used as an identity source for AWS IAM Identity Center?","AWS Directory Service for Microsoft Active Directory","Amazon S3 bucket","AWS CodeCommit repository","Amazon EC2 instance","IAM Identity Center integrates with AWS Directory Service for Microsoft Active Directory, allowing you to use your existing Active Directory users and groups."
"Which of the following AWS services can be integrated with AWS IAM Identity Center for single sign-on (SSO)?","Amazon EC2","AWS Lambda","AWS IAM","AWS SSO-integrated applications","AWS IAM Identity Center provides SSO access to AWS accounts and SSO-integrated cloud applications."
"What is the relationship between AWS IAM roles and AWS IAM Identity Center?","IAM roles are used within IAM Identity Center to grant permissions to users and groups","IAM roles are replaced by IAM Identity Center permission sets","IAM roles are used for service-linked roles only","IAM roles and IAM Identity Center are mutually exclusive","IAM Identity Center leverages permission sets, which are similar to IAM roles, to grant access to AWS resources. These permission sets are assigned to users and groups."
"When using AWS IAM Identity Center, where are the users and groups typically managed?","In the configured identity source (e.g., AWS Directory Service)","Within the individual AWS accounts","In AWS IAM directly","In Amazon Cognito","Users and groups are managed within the configured identity source, such as AWS Directory Service or a third-party identity provider (IdP)."
"What is the purpose of the AWS IAM Identity Center application portal?","To provide a single place for users to access their assigned AWS accounts and applications","To manage AWS IAM roles and policies","To monitor AWS account activity","To create and manage AWS IAM users","The application portal provides a centralised location for users to launch their assigned AWS accounts and SSO-enabled applications."
"How does AWS IAM Identity Center enhance security?","By centralising access management and reducing the need for individual IAM users in each account","By removing the need for Multi-Factor Authentication","By automatically granting full administrator access to all users","By storing user credentials directly in AWS","IAM Identity Center enhances security by centralising access management, reducing the reliance on individual IAM users, and promoting the use of least privilege access."
"Which AWS service provides a fully managed, highly available directory for AWS IAM Identity Center?","AWS Directory Service","Amazon Cognito","AWS IAM","AWS Organizations","AWS Directory Service provides a fully managed directory service, which can be used as an identity source for AWS IAM Identity Center."
"In AWS IAM Identity Center, what does 'Just-In-Time (JIT) provisioning' refer to?","Automatically creating user accounts in IAM Identity Center when they first access an application","Manually creating user accounts in IAM Identity Center","Deleting user accounts after a period of inactivity","Granting temporary access to resources","JIT provisioning automatically creates user accounts in IAM Identity Center when they first access an application through their identity provider."
"Which of the following is a benefit of using AWS IAM Identity Center with AWS Organizations?","Simplified management of access across multiple AWS accounts","Elimination of the need for AWS IAM","Automatic creation of IAM users in all accounts","Reduced cost for AWS services","IAM Identity Center simplifies the management of user access across multiple AWS accounts managed by AWS Organizations."
"What is a 'trust relationship' in the context of AWS IAM Identity Center?","The connection between IAM Identity Center and an identity provider","The agreement between AWS and its customers","The link between AWS accounts in an organisation","The relationship between users in a group","A trust relationship defines the connection between IAM Identity Center and your chosen identity provider (e.g., AWS Directory Service, Okta, Azure AD)."
"Which AWS IAM Identity Center feature helps enforce the principle of least privilege?","Permission Sets","Identity Source","Application Assignments","Trust Relationship","Permission sets are the primary mechanism for defining and enforcing least privilege by assigning specific permissions to users and groups."
"How can you delegate administration of AWS IAM Identity Center?","By assigning specific administrative permissions within the IAM Identity Center console","By creating IAM users with administrative roles","By granting full access to all users","By disabling all administrative access","Delegation of administration can be achieved by assigning specific administrative permissions within the IAM Identity Center console to designated users or groups."
"What type of identities can be used as a source of identity for AWS IAM Identity Center?","Existing corporate directories (e.g. Active Directory) and cloud identity providers","AWS IAM users","Amazon S3 buckets","AWS Lambda functions","IAM Identity Center allows integrating with existing corporate directories (e.g., Active Directory) and cloud identity providers, as well as the IAM Identity Center directory."
"Which access method is most appropriate for programmatic access with AWS IAM Identity Center?","AWS CLI with temporary credentials","IAM user access keys","Root user access keys","Console access with username and password","AWS CLI with temporary credentials, acquired through the AWS IAM Identity Center, is the most appropriate method for programmatic access."
"Which is the best way to centrally control users' access to AWS accounts when using AWS IAM Identity Center?","Using permission sets","Using IAM roles directly in each account","Using S3 bucket policies","Using EC2 instance profiles","Permission sets in AWS IAM Identity Center provides the best way to centrally control users' access to AWS accounts."
"What is the main advantage of using AWS IAM Identity Center over managing IAM users directly in each AWS account?","Centralised access management","Lower cost","Higher performance","More granular control","AWS IAM Identity Center allows you to manage access to AWS accounts and cloud applications from one central location, simplifying user management."
"Which feature of AWS IAM Identity Center can be used to grant temporary access to AWS resources?","Session duration configuration","Permanent access policies","IAM user access keys","Root user credentials","IAM Identity Center allows configuring the session duration which is a way to grant temporary access to AWS resources."
"What is the AWS IAM Identity Center dashboard used for?","To view user activity, account assignments, and application access","To configure AWS IAM roles and policies","To monitor EC2 instance performance","To manage S3 bucket storage","The AWS IAM Identity Center dashboard provides a central view of user activity, account assignments, and application access, allowing administrators to monitor and manage access across the organisation."
"How does AWS IAM Identity Center help with compliance requirements?","By centralising auditing and access control","By encrypting data in transit","By providing DDoS protection","By automating security patching","IAM Identity Center helps with compliance by centralising auditing, access control, and reporting, making it easier to meet regulatory requirements."
"What is the relationship between AWS IAM Identity Center and SAML 2.0?","IAM Identity Center can integrate with SAML 2.0 identity providers","IAM Identity Center replaces SAML 2.0 authentication","IAM Identity Center only supports OAuth 2.0","SAML 2.0 is not compatible with AWS","IAM Identity Center can integrate with SAML 2.0 identity providers, allowing you to use your existing IdP for authentication."
"Which of the following is a key component of AWS IAM Identity Center's architecture?","Directory integration","Load balancing","Database replication","Content Delivery Network (CDN)","Directory integration is a key component of AWS IAM Identity Center's architecture, allowing you to connect to your existing identity sources."
"How do you assign users to AWS accounts using AWS IAM Identity Center?","By assigning them to permission sets within specific accounts","By creating IAM users in each account","By manually configuring access keys","By granting root user access","Users are assigned to AWS accounts by associating them with permission sets within those accounts, providing specific access levels."
"What happens if a user is assigned multiple permission sets in AWS IAM Identity Center for the same AWS account?","The user receives the combined permissions of all assigned sets","The user receives only the permissions from the first assigned set","The user is denied access to the account","The user is prompted to choose a permission set upon login","When a user is assigned multiple permission sets for the same account, they receive the combined permissions of all assigned sets, effectively granting them the broadest access defined across all the sets."
"Which of the following is a supported method for synchronising users from Active Directory to AWS IAM Identity Center?","AWS Directory Service AD Connector","Manual CSV upload","Amazon SQS queue","AWS Config rules","AWS Directory Service AD Connector is a supported method for synchronising users and groups from Active Directory to AWS IAM Identity Center."
"How can you monitor user access and activity within AWS IAM Identity Center?","Using AWS CloudTrail logs","Using Amazon CloudWatch metrics","Using AWS Trusted Advisor","Using AWS Config","AWS CloudTrail logs can be used to monitor user access and activity within AWS IAM Identity Center, providing a detailed audit trail."
"What is the maximum session duration configurable for AWS IAM Identity Center?","12 hours","1 hour","24 hours","8 hours","The maximum session duration configurable for AWS IAM Identity Center is 12 hours."
"When using AWS IAM Identity Center, what determines the level of access a user has to an AWS account?","The permission set assigned to the user","The IAM role assigned to the user in the account","The root user credentials","The AWS account ID","The level of access a user has to an AWS account is determined by the permission set assigned to the user within AWS IAM Identity Center. This permission set defines the specific actions and resources the user can access."
"Which of the following is NOT a typical use case for AWS IAM Identity Center?","Managing access to on-premises servers","Providing single sign-on to AWS accounts","Centralising access management for multiple AWS accounts","Simplifying user provisioning and deprovisioning","Managing access to on-premises servers is not a typical use case for AWS IAM Identity Center, which primarily focuses on AWS accounts and cloud applications."
"What is the advantage of using custom attributes with AWS IAM Identity Center?","To pass additional user information to applications for personalisation and authorisation","To improve the performance of authentication","To reduce the cost of IAM Identity Center","To simplify the user interface","Custom attributes allow you to pass additional user information to applications, enabling personalised experiences and fine-grained authorisation decisions."
"Which AWS service can be used to manage the identity source for AWS IAM Identity Center if you don't have an existing directory?","AWS IAM Identity Center Directory","Amazon Cognito","AWS IAM","AWS Secrets Manager","AWS IAM Identity Center Directory can be used to manage the identity source for AWS IAM Identity Center if you don't have an existing directory."
"When integrating AWS IAM Identity Center with an external identity provider (IdP), which standard is typically used for federated authentication?","SAML 2.0","OAuth 2.0","LDAP","Kerberos","SAML 2.0 is commonly used for federated authentication when integrating AWS IAM Identity Center with an external identity provider (IdP)."
"Which of the following is the best practice for managing access to sensitive data using AWS IAM Identity Center?","Granting users the least privilege necessary to perform their tasks","Providing all users with administrator access","Sharing root user credentials","Storing access keys in code","The best practice is to grant users only the least privilege necessary to perform their tasks, ensuring that they have the minimum access required to do their job."
"How can you ensure that users are automatically deprovisioned from AWS IAM Identity Center when they leave the organisation?","By integrating with your HR system for automated provisioning/deprovisioning","By manually deleting users from the IAM Identity Center directory","By disabling user accounts in AWS IAM","By revoking all permission sets","Integrating with your HR system for automated provisioning/deprovisioning ensures that users are automatically removed from AWS IAM Identity Center when they leave the organisation, reducing the risk of unauthorised access."
"What is the primary benefit of using AWS IAM Identity Center's built-in directory?","Simplified setup for organisations without an existing directory","Increased security compared to Active Directory","Lower cost compared to other identity providers","Automatic integration with all AWS services","The primary benefit of using AWS IAM Identity Center's built-in directory is the simplified setup for organisations that do not already have an existing directory service, making it easier to get started with centralised access management."
"How does AWS IAM Identity Center integrate with AWS CloudTrail?","It logs all user access and activity within AWS accounts","It encrypts CloudTrail logs","It archives CloudTrail logs to S3","It analyses CloudTrail logs for security threats","AWS IAM Identity Center integrates with AWS CloudTrail by logging all user access and activity within AWS accounts, providing a detailed audit trail for compliance and security monitoring."
"Which of the following is NOT a common use case for AWS IAM Identity Center application assignments?","Granting access to specific AWS accounts","Providing SSO to third-party SaaS applications","Managing access to on-premises databases","Assigning users to custom-built cloud applications","Managing access to on-premises databases is not a common use case for application assignments in AWS IAM Identity Center, which primarily focuses on AWS accounts and cloud applications."
"What is the significance of the 'Principal' in an AWS IAM Identity Center permission set?","It identifies the user or group being granted permissions","It specifies the AWS account the permission set applies to","It defines the type of AWS resource being accessed","It indicates the authentication method used","The 'Principal' in an AWS IAM Identity Center permission set identifies the user or group being granted permissions, defining who the permission set applies to."
"How do you typically manage AWS IAM Identity Center using Infrastructure as Code (IaC)?","Using AWS CloudFormation or Terraform","Using AWS Management Console directly","Using AWS CLI commands","Using Amazon S3 bucket policies","AWS CloudFormation or Terraform allows defining and managing AWS IAM Identity Center resources using Infrastructure as Code."
"Which of the following is a key consideration when designing permission sets in AWS IAM Identity Center?","Following the principle of least privilege","Granting full administrator access to all users","Creating a single, all-encompassing permission set","Ignoring AWS service limits","When designing permission sets in AWS IAM Identity Center, it is crucial to follow the principle of least privilege, granting users only the minimum permissions required to perform their tasks."
"What type of attributes can be passed from the identity provider to AWS IAM Identity Center?","User profile attributes (e.g., email, department)","EC2 instance metadata","S3 bucket ACLs","IAM role policies","User profile attributes, such as email, department, and other relevant information, can be passed from the identity provider to AWS IAM Identity Center for use in applications and authorisation decisions."
"When using AWS IAM Identity Center, how can you restrict access to specific AWS regions?","By defining conditions in permission set policies","By creating separate AWS accounts for each region","By using IAM policies in each account","By configuring VPC endpoints","Access to specific AWS regions can be restricted by defining conditions in permission set policies, ensuring that users can only access resources in authorised regions."
"What is the purpose of the AWS IAM Identity Center API?","To automate management tasks and integrate with other systems","To monitor user activity","To configure billing alerts","To manage S3 bucket permissions","The AWS IAM Identity Center API allows you to automate management tasks, integrate with other systems, and programmatically manage your IAM Identity Center environment."
"How can you ensure that AWS IAM Identity Center is highly available?","It's a managed service and is automatically highly available","By configuring Multi-AZ deployment","By creating read replicas","By using Auto Scaling groups","IAM Identity Center is a managed service and is designed to be highly available by AWS. No additional configuration is required to make it highly available."
"When migrating from individual IAM users to AWS IAM Identity Center, what is a key consideration?","Mapping existing IAM users to IAM Identity Center users or groups","Deleting all IAM users immediately","Creating new IAM roles","Ignoring existing permissions","Mapping existing IAM users to IAM Identity Center users or groups, and ensuring their permissions are appropriately transferred to permission sets, is a key consideration when migrating from individual IAM users to AWS IAM Identity Center."
"What is the difference between AWS IAM Identity Center and AWS IAM?","IAM Identity Center provides centralised access management across multiple accounts, while IAM manages access within a single account","IAM Identity Center replaces AWS IAM","IAM Identity Center is only for SaaS applications","IAM is only for AWS services","AWS IAM Identity Center provides centralised access management across multiple AWS accounts and applications, while AWS IAM manages access within a single account."
"In AWS IAM Identity Center, what is the purpose of 'Application Metadata'?","Defines how IAM Identity Center interacts with integrated cloud applications","Specifies user login history","Controls network traffic","Monitors CPU utilisation","'Application Metadata' in IAM Identity Center defines how IAM Identity Center interacts with integrated cloud applications, including authentication protocols and user attribute mappings."