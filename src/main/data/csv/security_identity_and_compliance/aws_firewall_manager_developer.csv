"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Firewall Manager?","To centrally manage and configure firewall rules across multiple AWS accounts and resources.","To monitor network traffic for malicious activity using machine learning.","To provide a managed intrusion detection and prevention system.","To create and manage VPN connections to on-premises networks.","Firewall Manager simplifies security administration by enabling centralised management of firewall rules."
"Which AWS WAF rule groups can be centrally deployed using AWS Firewall Manager?","AWS Managed Rules and custom rule groups.","Only AWS Managed Rules.","Only custom rule groups.","Only rules targeting specific HTTP headers.","Firewall Manager supports deploying both AWS Managed Rules and custom rule groups across your organisation."
"Which AWS resource is NOT directly supported by AWS Firewall Manager for policy deployment?","EC2 instances.","AWS WAF web ACLs.","Amazon VPC security groups.","AWS Network Firewall firewalls.","Firewall Manager policies are not directly deployed to EC2 instances.  It focuses on WAF, security groups and Network Firewall."
"How does AWS Firewall Manager help in achieving compliance requirements?","By providing a centralised view of security posture and ensuring consistent rule enforcement.","By automatically generating compliance reports.","By encrypting data at rest and in transit.","By managing IAM roles and permissions.","Firewall Manager's centralised management helps maintain consistent security controls, aiding in compliance efforts."
"What is the primary benefit of using AWS Firewall Manager with AWS Organisations?","It allows for automatic deployment of firewall policies across all accounts in the organisation.","It provides cost optimisation recommendations for AWS resources.","It simplifies the management of IAM users and roles across the organisation.","It enables centralised logging of all AWS CloudTrail events.","Firewall Manager integrates with AWS Organisations to automatically apply policies to all member accounts."
"Which AWS service is required to be integrated with AWS Firewall Manager for managing web application firewalls?","AWS WAF.","AWS Shield.","Amazon CloudFront.","AWS API Gateway.","Firewall Manager uses AWS WAF to protect web applications by managing web ACLs."
"What type of policies can be created using AWS Firewall Manager to manage security groups?","Audit policies and remediation policies.","Only audit policies.","Only remediation policies.","Only policies that enforce inbound traffic restrictions.","Firewall Manager enables both audit (detecting non-compliant rules) and remediation (automatically fixing non-compliant rules) for security groups."
"When creating an AWS Firewall Manager policy, what is the scope of the resources to which the policy applies?","The resources within the specified AWS accounts and regions.","Only the resources in the current AWS account and region.","Only the resources in the AWS organisation's management account.","Only the resources that are tagged with a specific tag.","Firewall Manager policies can be applied to resources across multiple accounts and regions."
"What is the purpose of the AWS Firewall Manager 'common policies' feature?","To enforce a baseline set of security rules across all applications.","To share firewall rules between different AWS accounts.","To simplify the creation of new firewall policies.","To monitor network traffic for common security threats.","Common policies allow you to set a baseline of firewall rules that are applied consistently across all applications."
"Which AWS service does AWS Firewall Manager use to manage and enforce firewall rules for web applications?","AWS WAF.","AWS Network Firewall.","Amazon Inspector.","AWS Shield.","Firewall Manager leverages AWS WAF to define and enforce firewall rules for web applications."
"How does AWS Firewall Manager assist with managing AWS Network Firewall?","By centrally deploying and managing Network Firewall rules and firewall policies across multiple VPCs.","By providing real-time threat intelligence to Network Firewall.","By automatically scaling Network Firewall based on traffic volume.","By encrypting network traffic passing through Network Firewall.","Firewall Manager simplifies Network Firewall management by enabling centralised deployment and configuration of rules and policies."
"What is the relationship between AWS Firewall Manager and AWS Shield?","AWS Firewall Manager helps manage AWS WAF rules that protect against attacks mitigated by AWS Shield.","AWS Firewall Manager replaces the need for AWS Shield.","AWS Shield is used to manage AWS Firewall Manager policies.","AWS Firewall Manager automatically configures AWS Shield protection.","Firewall Manager helps manage WAF rules that protect against the same types of attacks that Shield mitigates, offering a layered security approach."
"Which AWS service does Firewall Manager use to manage VPC Security Groups?","AWS Config.","AWS CloudTrail.","Amazon Inspector.","AWS Trusted Advisor.","Firewall Manager uses AWS Config to audit and remediate security group rules that violate your policies."
"What is the role of 'protection policies' in AWS Firewall Manager?","To automate the process of applying security configurations to resources.","To generate reports on security vulnerabilities.","To encrypt data at rest.","To manage IAM permissions.","Protection policies automate the application of security configurations to resources, ensuring consistent security across your environment."
"How can you use AWS Firewall Manager to ensure that all new AWS accounts created in your organisation are automatically protected by a WAF?","By enabling automatic policy creation for new accounts in AWS Organisations.","By manually creating a firewall policy for each new account.","By using AWS CloudFormation to provision WAF resources in each new account.","By configuring AWS Config rules to detect unprotected accounts.","Firewall Manager integrates with AWS Organisations and automatically applies policies to new accounts."
"What is the purpose of the 'Compliance status' dashboard in AWS Firewall Manager?","To provide a centralised view of the compliance status of your firewall policies.","To generate compliance reports for regulatory audits.","To automatically remediate non-compliant resources.","To monitor network traffic for malicious activity.","The Compliance status dashboard provides a single view of the compliance status of your firewall policies, highlighting any resources that are not in compliance."
"In the context of AWS Firewall Manager, what is a 'resource tag'?","A key-value pair that is used to identify and group AWS resources for policy enforcement.","A tag that is automatically added to all resources protected by Firewall Manager.","A tag that is used to grant access permissions to resources.","A tag that is used to track the cost of resources.","Resource tags are used to identify and group resources so that Firewall Manager policies can be applied selectively based on these tags."
"Which of the following is a supported remediation action in AWS Firewall Manager for security group policies?","Automatically removing overly permissive rules.","Automatically adding new security group rules.","Automatically encrypting network traffic.","Automatically blocking traffic from specific IP addresses.","Firewall Manager can automatically remove overly permissive rules from security groups to improve security posture."
"How does AWS Firewall Manager handle policy conflicts?","It prioritises policies based on their creation order.","It allows you to define policy precedence rules.","It automatically resolves conflicts based on AWS best practices.","It prevents conflicting policies from being created.","Firewall Manager allows you to define the order in which policies are applied, allowing you to specify precedence rules."
"What is the purpose of the 'Policy manager' section in the AWS Firewall Manager console?","To create, manage, and deploy firewall policies across your organisation.","To monitor network traffic for security threats.","To configure AWS WAF rules.","To manage IAM permissions for firewall resources.","The Policy manager section provides the interface for creating, managing, and deploying firewall policies to enforce security across your AWS environment."
"Can AWS Firewall Manager be used to manage firewall rules in hybrid cloud environments?","No, it only manages AWS resources.","Yes, through integration with third-party firewall vendors.","Yes, by extending its policies to on-premises firewalls via VPN.","Yes, but it requires manual synchronisation of rules between AWS and on-premises firewalls.","Firewall Manager is designed to manage firewalls within AWS and does not extend to on-premises or other cloud environments."
"What is the benefit of using AWS Firewall Manager to manage WAF rules compared to managing them individually on each WAF?","Centralised management and consistent enforcement of WAF rules across multiple accounts and applications.","Lower cost for WAF rule management.","Improved WAF performance.","Automatic scaling of WAF capacity.","Firewall Manager simplifies WAF management by allowing you to centrally manage and enforce WAF rules consistently across multiple resources and accounts."
"Which of the following AWS regions is AWS Firewall Manager available in?","Most AWS regions.","Only US East (N. Virginia).","Only EU (Ireland).","Only Asia Pacific (Sydney).","AWS Firewall Manager is available in most AWS regions globally."
"How does AWS Firewall Manager support multi-account environments?","By allowing you to create policies that apply to all or selected accounts within an AWS Organisation.","By requiring you to manually create and manage policies in each account.","By automatically synchronising policies across all accounts.","By providing a centralised dashboard for monitoring security events across all accounts.","Firewall Manager is designed to work with AWS Organizations to centrally manage security policies across multiple accounts."
"Which AWS service is commonly used with AWS Firewall Manager to centrally manage and monitor security events across your AWS environment?","AWS Security Hub.","Amazon CloudWatch.","AWS CloudTrail.","Amazon Inspector.","AWS Security Hub aggregates security findings from various AWS services, including Firewall Manager, to provide a unified view of your security posture."
"When using AWS Firewall Manager to manage security groups, what type of rules can be audited?","Inbound and outbound rules.","Only inbound rules.","Only outbound rules.","Only rules that allow traffic from specific IP addresses.","Firewall Manager can audit both inbound and outbound security group rules."
"What is the primary use case for AWS Firewall Manager's DNS Firewall?","To protect against DNS-based attacks.","To manage DNS records.","To accelerate DNS resolution.","To encrypt DNS traffic.","DNS Firewall protects against DNS-based attacks by inspecting and filtering DNS traffic."
"What kind of attacks does AWS Firewall Manager's DNS Firewall protect against?","Data exfiltration and command and control (C2) communication.","SQL injection and cross-site scripting (XSS).","Distributed denial-of-service (DDoS) attacks.","Brute-force attacks.","DNS Firewall protects against data exfiltration and command and control (C2) communication by inspecting and filtering DNS queries."
"What is the purpose of configuring scope when defining an AWS Firewall Manager policy?","To specify the accounts and resource types that the policy applies to.","To limit the amount of AWS resources that the policy can consume.","To define the geographical regions where the policy is enforced.","To control the level of access that the policy has to AWS resources.","The scope defines which accounts and resource types the policy will impact."
"What is the AWS Firewall Manager concept of 'Policy Actions'?","Actions taken when a resource violates a policy (e.g., auto-remediation).","Actions that trigger alerts when a policy is created or modified.","Actions that grant access to AWS resources.","Actions that encrypt data at rest.","Policy Actions determine what happens when a resource is not compliant with the policy (e.g., automatically correcting it)."
"How does AWS Firewall Manager assist with managing web application firewalls (WAFs) across multiple AWS accounts?","By allowing centralised creation, deployment, and management of AWS WAF rules and rule groups.","By providing a centralised dashboard for monitoring WAF traffic logs.","By automatically scaling WAF capacity based on traffic volume.","By encrypting WAF traffic.","Firewall Manager provides a central point to create, deploy, and manage WAF rules and rule groups across all your accounts."
"What is the purpose of AWS Firewall Manager's 'Centralised Logging' feature?","To enable centralised logging of all security events generated by AWS WAF and AWS Network Firewall.","To encrypt log data.","To automatically back up log data.","To generate compliance reports from log data.","Centralised Logging allows you to aggregate and analyse security events from multiple firewalls in a single location."
"When should you consider using AWS Firewall Manager instead of managing individual firewalls manually?","When you have multiple AWS accounts and resources that need consistent security policies.","When you need to encrypt network traffic.","When you need to monitor network performance.","When you only have a single AWS account.","Firewall Manager's central management capabilities become valuable when you have a multi-account environment and want to enforce consistent security policies."
"How does AWS Firewall Manager integrate with AWS Organisations to manage security policies?","It allows you to create policies that are automatically applied to all or selected accounts within your organisation.","It requires you to manually create and deploy policies to each account in your organisation.","It automatically synchronises security policies across all accounts in your organisation.","It provides a centralised dashboard for monitoring security events across your organisation.","Firewall Manager integrates with Organisations to streamline security policy management across many accounts."
"What is the key difference between AWS Firewall Manager and AWS Config?","AWS Firewall Manager manages firewall policies, while AWS Config assesses resource configurations against desired state.","AWS Firewall Manager encrypts data, while AWS Config monitors data access.","AWS Firewall Manager monitors network traffic, while AWS Config manages IAM permissions.","AWS Firewall Manager scales resources, while AWS Config manages costs.","Firewall Manager focuses on firewall management, while Config provides configuration assessments and drift detection."
"What is the function of the AWS Firewall Manager pre-defined AWS WAF rule groups?","To offer baseline protection against common web application vulnerabilities.","To automatically scale WAF capacity.","To encrypt WAF traffic.","To monitor WAF traffic logs.","AWS Managed Rule groups offer pre-configured protection against common web application threats, simplifying WAF configuration."
"If you delete an AWS Firewall Manager policy, what happens to the resources that were previously protected by the policy?","The protection is removed and the resources are no longer protected by the policy.","The resources remain protected with the last configuration enforced by the policy.","The resources are automatically deleted.","The resources are automatically migrated to a new firewall policy.","Deleting the policy will remove the enforced configurations. The firewall and resources will no longer be protected by the policy."
"Which type of policy in AWS Firewall Manager supports remediation actions to automatically fix non-compliant resources?","Security Group policies with auto-remediation enabled.","Only WAF policies support auto-remediation.","Only Network Firewall policies support auto-remediation.","All policy types in Firewall Manager automatically support auto-remediation.","Only Security Group policies can be configured with auto-remediation to automatically fix non-compliant resources."
"Can AWS Firewall Manager be used to deploy a custom intrusion detection system (IDS) across multiple VPCs?","Yes, by integrating with AWS Network Firewall and custom Suricata rules.","No, it only supports pre-defined AWS security rules.","Yes, by deploying a custom EC2 instance running the IDS software in each VPC.","Yes, by configuring AWS Config rules to detect intrusion attempts.","Firewall Manager can be used to deploy Network Firewall with custom Suricata rules, enabling a custom IDS across multiple VPCs."
"When using AWS Firewall Manager, what is the purpose of the 'administrator account'?","To centrally manage and configure firewall policies across the organisation.","To monitor network traffic for security threats.","To manage IAM permissions for firewall resources.","To encrypt data at rest.","The administrator account in AWS Organisations is used to centrally manage and configure Firewall Manager policies."
"Which of the following actions can be performed using AWS Firewall Manager's security group policy audit feature?","Identify security groups with overly permissive rules.","Automatically encrypt network traffic.","Automatically block traffic from specific IP addresses.","Automatically scale security group capacity.","Firewall Manager can audit security groups to identify rules that are too permissive."
"What is the AWS Firewall Manager 'DNS Firewall' primarily used for?","Preventing DNS exfiltration attempts.","Monitoring DNS server health.","Accelerating DNS resolution.","Managing DNS records.","The DNS Firewall feature is specifically designed to prevent data exfiltration attempts via DNS requests."
"What is the scope of resources that AWS Firewall Manager can manage?","Resources within specified AWS accounts and regions.","Only resources in the current AWS account and region.","Only resources in the AWS organisation's management account.","Only resources that are tagged with a specific tag.","Firewall Manager is designed to cover multiple accounts and regions when needed."
"You want to use AWS Firewall Manager to ensure all VPCs in your organisation have Network Firewall enabled. What type of policy would you create?","Network Firewall policy.","WAF policy.","Security Group policy.","Config Policy.","A Network Firewall policy in Firewall Manager allows you to enforce the use of Network Firewall across your VPCs."
"What is one limitation of AWS Firewall Manager?","It can only manage security groups in a single AWS Region.","It cannot be used with AWS Organizations.","It does not provide reporting capabilities.","It primarily focuses on centrally managing firewall rules and lacks detailed traffic analysis capabilities.","While effective for policy management, Firewall Manager doesn't provide the same level of in-depth traffic analysis as some dedicated network monitoring tools."
"Which is NOT a feature of AWS Firewall Manager?","Threat Intelligence Feeds Integration.","Centralised Firewall Policy Management.","Cross-Account Security Group Auditing.","Automatic Policy Deployment.","Threat Intelligence Feeds are not directly managed by Firewall Manager itself. You must configure and manage threat intelligence feeds in other services."
"What is the primary purpose of AWS Firewall Manager?","To centrally manage and configure firewall rules across multiple AWS accounts and resources","To provide a managed intrusion detection system","To monitor network traffic for suspicious activity","To perform vulnerability assessments on EC2 instances","Firewall Manager simplifies the administration and maintenance of security rules, providing consistent protection across an organisation's AWS environment."
"Which AWS service is NOT directly integrated with AWS Firewall Manager for policy enforcement?","AWS Shield Advanced","AWS Web Application Firewall (WAF)","Amazon GuardDuty","Amazon VPC Security Groups","Firewall Manager primarily works with WAF, Shield Advanced and VPC Security Groups to enforce policies. GuardDuty is a threat detection service but is not directly managed by Firewall Manager policies."
"When creating a Firewall Manager policy, what is the scope of resources to which the policy can be applied?","All AWS resources within the organisation","Only EC2 instances","Only S3 buckets","Only resources in a single AWS account","Firewall Manager policies can be scoped to specific resource types across multiple accounts within an organisation using AWS Organizations."
"What type of policy can you create with AWS Firewall Manager to protect your applications from common web exploits?","AWS WAF policy","AWS Shield Advanced policy","VPC Security Group policy","Network ACL policy","Firewall Manager can deploy and manage AWS WAF rules to protect your web applications from attacks such as SQL injection and cross-site scripting."
"Which of the following is a benefit of using AWS Firewall Manager centralised management?","Reduced administrative overhead","Increased EC2 instance performance","Automated patching of operating systems","Cost optimisation of S3 storage","Firewall Manager centralises the configuration and deployment of security rules, reducing the effort required to manage security across multiple accounts and resources."
"With AWS Firewall Manager, what does the 'Remediation' action typically refer to?","Automatically correcting non-compliant configurations","Alerting administrators of non-compliant configurations","Stopping all traffic to non-compliant resources","Deleting non-compliant resources","Remediation in Firewall Manager involves automatically correcting resource configurations that don't comply with the defined security policies."
"How does AWS Firewall Manager help ensure compliance with security standards?","By enforcing consistent firewall rules across the organisation","By automatically generating compliance reports","By providing penetration testing services","By encrypting all data in transit","Firewall Manager helps maintain compliance by ensuring that all resources adhere to the specified firewall rules, reducing the risk of misconfiguration and security gaps."
"What is the role of AWS Organizations in conjunction with AWS Firewall Manager?","To define the scope of accounts to be managed by Firewall Manager","To provide identity and access management for Firewall Manager","To store Firewall Manager policies","To monitor Firewall Manager performance","AWS Organizations allows you to group AWS accounts into an organisation, which Firewall Manager can then use to apply policies consistently across the entire organisation or specific organisational units (OUs)."
"If a resource is found to be non-compliant with a Firewall Manager policy, what typically happens?","Firewall Manager automatically attempts to remediate the issue","The resource is automatically terminated","An alert is sent to AWS Support","The resource is moved to a quarantine account","Firewall Manager policies can be configured to automatically remediate non-compliant resources, bringing them into compliance with the specified rules."
"Which of the following AWS WAF rule actions can be managed through AWS Firewall Manager?","Allow, Block, Count","Allow, Block, Log","Allow, Count, Redirect","Block, Count, Redirect","Firewall Manager allows you to deploy and manage WAF rules with actions such as Allow, Block, and Count to control web traffic."
"What is a common use case for AWS Firewall Manager's support for VPC Security Groups?","Enforcing baseline security rules across all VPCs","Automating the creation of new VPCs","Monitoring network bandwidth utilisation in VPCs","Managing DNS settings for VPCs","Firewall Manager can be used to ensure that all VPCs within an organisation adhere to a consistent set of security group rules, reducing the risk of misconfiguration."
"How does AWS Firewall Manager simplify the management of AWS Shield Advanced?","By centrally managing protection for resources against DDoS attacks","By providing detailed DDoS attack reports","By automatically scaling resources to handle DDoS attacks","By creating custom DDoS mitigation strategies","Firewall Manager provides a single pane of glass to manage AWS Shield Advanced protection for resources, simplifying the configuration and management of DDoS protection."
"When configuring an AWS Firewall Manager policy, what does the 'Policy action' define?","The action to take when a resource is non-compliant","The AWS service to be protected","The geographical region to apply the policy to","The users who can modify the policy","The 'Policy action' specifies what Firewall Manager should do when a resource doesn't comply with the defined rules, such as automatically remediating the configuration."
"What type of resources can you protect with AWS Firewall Manager using AWS WAF?","Application Load Balancers, API Gateways, and CloudFront distributions","EC2 instances, RDS databases, and S3 buckets","Lambda functions, DynamoDB tables, and ECS clusters","VPC subnets, Route 53 domains, and CloudWatch dashboards","AWS Firewall Manager using AWS WAF can protect Application Load Balancers, API Gateways, and CloudFront distributions from web exploits."
"Which of the following AWS Firewall Manager features helps to maintain compliance across different environments (e.g., development, staging, production)?","Centralised policy enforcement","Automated resource discovery","Real-time threat intelligence","Integration with IAM","Firewall Manager's centralised policy enforcement ensures consistent security rules across all environments, helping to maintain compliance throughout the software development lifecycle."
"What is the primary difference between AWS Firewall Manager and AWS Network Firewall?","Firewall Manager centrally manages firewalls, while Network Firewall is a fully managed, scalable firewall service","Firewall Manager is free, while Network Firewall incurs charges based on usage","Firewall Manager provides only basic firewall capabilities, while Network Firewall offers advanced features","Firewall Manager is used for web application protection, while Network Firewall is used for network traffic inspection","AWS Firewall Manager is a management service for centrally configuring and managing other AWS security services (like AWS WAF and Network Firewall itself), while Network Firewall is a fully managed, scalable firewall service that inspects network traffic."
"Which AWS service can be used to provide granular network traffic filtering rules for VPC subnets and is manageable via AWS Firewall Manager?","Amazon VPC Network ACLs","Amazon Route 53","Amazon CloudFront","AWS IAM","Amazon VPC Network ACLs (Network Access Control Lists) can be configured with granular rules to filter network traffic at the subnet level, and AWS Firewall Manager can be used to centrally manage these rules across multiple VPCs."
"When creating an AWS WAF policy with Firewall Manager, which of the following is a key component?","Rule groups containing WAF rules","IP address ranges to allow or block","EC2 instance security groups","AWS IAM roles and policies","An AWS WAF policy managed by Firewall Manager will typically consist of one or more rule groups containing the actual WAF rules that define how to inspect and filter web traffic."
"How does AWS Firewall Manager assist in automating the deployment of security rules?","By automatically creating and updating firewall rules based on predefined policies","By automatically patching EC2 instances","By automatically rotating encryption keys","By automatically scaling resources based on traffic patterns","Firewall Manager helps automate the deployment of security rules by providing a centralised way to define and apply policies across multiple AWS accounts and resources, automating the creation and update of firewall rules."
"Which of the following is a key benefit of using AWS Firewall Manager with AWS Organizations?","Centralised security policy management across multiple AWS accounts","Automated cost optimisation of AWS resources","Simplified deployment of EC2 instances","Improved network performance for VPCs","One of the primary benefits of using Firewall Manager with AWS Organizations is the ability to centrally manage and enforce security policies across all accounts within the organisation, ensuring consistent security posture."
"What is the purpose of the AWS Firewall Manager's 'common security configuration' feature?","To define a baseline set of security rules that should be applied to all resources","To automatically detect and remediate security vulnerabilities","To generate compliance reports based on security configurations","To encrypt data in transit between AWS services","The 'common security configuration' feature in Firewall Manager allows you to define a baseline set of security rules that should be consistently applied to all resources within your AWS environment, ensuring a minimum level of security."
"With AWS Firewall Manager, how can you ensure that all new AWS accounts created within your organisation automatically inherit the required security policies?","By enabling automatic policy deployment to new accounts","By manually configuring policies for each new account","By creating custom IAM roles for each new account","By using AWS CloudTrail to monitor account creation","AWS Firewall Manager can be configured to automatically deploy security policies to new AWS accounts as they are created within your AWS Organizations environment, ensuring that they immediately inherit the required security posture."
"How does AWS Firewall Manager integrate with AWS Security Hub?","By providing a centralised view of security findings across multiple accounts","By automatically remediating security vulnerabilities identified by Security Hub","By encrypting data at rest in Security Hub","By managing IAM permissions for Security Hub","While both are security services, AWS Firewall Manager does not directly integrate with Security Hub. Firewall Manager focuses on managing firewalls, while Security Hub focuses on centralising and prioritising security findings."
"What is the purpose of AWS Firewall Manager's 'resource tagging' support?","To allow policies to be applied to specific resources based on their tags","To automatically generate cost allocation reports","To encrypt data at rest on tagged resources","To manage IAM permissions for tagged resources","Firewall Manager's support for resource tagging allows you to apply policies to specific resources based on their tags, enabling fine-grained control over which resources are protected by which policies."
"Which type of AWS resource is NOT directly supported for policy enforcement by AWS Firewall Manager?","AWS Lambda functions","Application Load Balancers","CloudFront distributions","API Gateways","AWS Firewall Manager focuses on resources that are typically associated with network security, such as Application Load Balancers, CloudFront distributions, and API Gateways. AWS Lambda functions do not have native integration with Firewall Manager."
"What does AWS Firewall Manager use to define the scope of AWS resources that a security policy applies to?","AWS Organizations organizational units (OUs) and resource tags","AWS IAM roles and policies","Amazon CloudWatch alarms","AWS CloudTrail logs","AWS Firewall Manager primarily uses AWS Organizations organizational units (OUs) and resource tags to define the scope of resources that a security policy applies to. This allows you to target specific groups of resources or resources with particular tags."
"You need to centrally manage and enforce security group rules across all your VPCs. Which AWS service should you use?","AWS Firewall Manager","AWS Network Firewall","AWS Shield Advanced","Amazon Inspector","AWS Firewall Manager allows you to centrally manage and enforce security group rules across all your VPCs, ensuring consistent security posture."
"What is the main function of AWS Firewall Manager when used in conjunction with AWS WAF?","To simplify the deployment and management of WAF rules across multiple accounts and resources","To provide enhanced DDoS protection beyond WAF's capabilities","To automatically generate WAF rule sets based on threat intelligence","To encrypt data inspected by WAF","AWS Firewall Manager simplifies the deployment and management of WAF rules across multiple accounts and resources, making it easier to maintain a consistent security posture."
"Which AWS service, when used with AWS Firewall Manager, helps protect your applications from Distributed Denial of Service (DDoS) attacks?","AWS Shield Advanced","AWS Network Firewall","AWS WAF","Amazon GuardDuty","AWS Shield Advanced, when integrated with AWS Firewall Manager, provides enhanced DDoS protection for your applications."
"What type of action can you configure AWS Firewall Manager to take on a non-compliant AWS resource?","Automatic remediation","Manual approval","Automatic termination","Log the event only","AWS Firewall Manager allows you to configure automatic remediation actions on non-compliant resources, bringing them back into compliance with your security policies."
"What is the primary purpose of configuring AWS Firewall Manager with AWS Organizations?","To centrally manage security policies across all AWS accounts within the organisation","To automate the creation of new AWS accounts","To monitor resource utilisation across the organisation","To manage IAM permissions for users within the organisation","Configuring AWS Firewall Manager with AWS Organizations enables centralised security policy management across all AWS accounts within the organisation, ensuring consistent security posture."
"Which of the following is NOT a key capability of AWS Firewall Manager?","Threat detection","Centralised policy management","Automatic remediation","Cross-account policy deployment","Threat detection is primarily handled by services like Amazon GuardDuty and Amazon Inspector, not AWS Firewall Manager."
"When configuring a Firewall Manager policy, what does the 'resource type' specify?","The type of AWS resource to which the policy applies (e.g., ALB, CloudFront distribution)","The region where the resource is located","The AWS account that owns the resource","The compliance status of the resource","The 'resource type' in a Firewall Manager policy specifies the type of AWS resource to which the policy should be applied, such as an Application Load Balancer (ALB) or a CloudFront distribution."
"What AWS service is frequently used in conjunction with AWS Firewall Manager to protect web applications from common web exploits?","AWS Web Application Firewall (WAF)","AWS Network Firewall","AWS Shield Advanced","Amazon GuardDuty","AWS Web Application Firewall (WAF) is commonly used with AWS Firewall Manager to protect web applications from common web exploits by inspecting and filtering web traffic."
"If you want to ensure that all new Application Load Balancers (ALBs) are automatically protected by AWS WAF, which AWS service should you use to automate this process?","AWS Firewall Manager","AWS CloudFormation","AWS Config","AWS Systems Manager","AWS Firewall Manager allows you to automate the process of ensuring that all new Application Load Balancers (ALBs) are automatically protected by AWS WAF by deploying a WAF policy across your organisation."
"Which of the following statements is true regarding AWS Firewall Manager's support for custom rules?","Firewall Manager allows you to deploy custom AWS WAF rules across your organisation","Firewall Manager automatically generates custom rules based on your traffic patterns","Firewall Manager does not support custom rules; you must use pre-defined rule sets","Firewall Manager only supports custom rules for EC2 instances","Firewall Manager allows you to deploy custom AWS WAF rules across your organisation, providing flexibility in defining your security policies."
"What does AWS Firewall Manager use to group related firewall rules together?","Rule groups","Security groups","Network ACLs","Firewall policies","AWS Firewall Manager uses rule groups to group related firewall rules together, making it easier to manage and deploy complex security policies."
"Which of the following is a key component of an AWS Firewall Manager policy?","Scope","Rules","Schedule","Encryption","The scope of a Firewall Manager policy defines which AWS accounts and resources the policy applies to."
"How does AWS Firewall Manager help reduce the risk of misconfiguration in security settings?","By centrally managing and enforcing security policies across multiple accounts","By automatically patching operating systems","By providing penetration testing services","By encrypting data in transit","AWS Firewall Manager helps reduce the risk of misconfiguration by providing a centralised platform for managing and enforcing security policies across multiple AWS accounts, ensuring consistent security configurations."
"Which of the following is an advantage of using AWS Firewall Manager over manually configuring firewalls in each AWS account?","Simplified management and consistent policy enforcement","Lower cost","Increased network performance","Automated incident response","AWS Firewall Manager simplifies management and ensures consistent policy enforcement across multiple accounts, which is difficult to achieve with manual configuration."
"You need to implement a common set of security rules for all web applications across multiple AWS accounts. Which service should you use?","AWS Firewall Manager","AWS Network Firewall","AWS Shield Advanced","Amazon GuardDuty","AWS Firewall Manager is designed to centrally manage and enforce security rules for web applications across multiple AWS accounts, making it the appropriate choice in this scenario."
"Which feature of AWS Firewall Manager allows you to automatically correct non-compliant resources?","Auto-remediation","Compliance reporting","Threat detection","Vulnerability scanning","AWS Firewall Manager's auto-remediation feature automatically corrects non-compliant resources, bringing them back into compliance with the defined security policies."
"What is the benefit of using AWS Firewall Manager alongside AWS Organizations?","Enables centralised management of security policies across all member accounts","Automates the creation of new AWS accounts","Provides detailed cost analysis of AWS services","Enhances network performance within the organisation","Using AWS Firewall Manager with AWS Organizations enables centralised management of security policies across all member accounts, simplifying security administration and ensuring consistent protection."
"Which AWS Firewall Manager policy type is specifically designed to protect against common web exploits like SQL injection and cross-site scripting?","AWS WAF Policy","Shield Advanced Policy","Security Group Policy","Network ACL Policy","AWS WAF Policies managed by Firewall Manager, protect against common web exploits like SQL injection and cross-site scripting."
"What is a primary function of AWS Firewall Manager in the context of security group management?","To enforce a baseline set of security group rules across multiple VPCs","To provide real-time monitoring of security group activity","To automatically create security groups based on traffic patterns","To encrypt data flowing through security groups","AWS Firewall Manager enforces a baseline set of security group rules across multiple VPCs, ensuring a minimum level of security and reducing the risk of misconfiguration."
"Which of the following services can AWS Firewall Manager centrally manage?","AWS WAF, AWS Shield Advanced, and VPC Security Groups","AWS IAM, AWS CloudTrail, and Amazon S3","Amazon EC2, Amazon RDS, and Amazon DynamoDB","AWS Lambda, Amazon ECS, and AWS EKS","AWS Firewall Manager centrally manages AWS WAF, AWS Shield Advanced, and VPC Security Groups, enabling consistent security policies across your AWS environment."
"Which AWS service assists AWS Firewall Manager in defining and managing the scope of AWS accounts and resources?","AWS Organizations","AWS IAM","Amazon VPC","AWS CloudTrail","AWS Organizations assists AWS Firewall Manager in defining and managing the scope of AWS accounts and resources to which security policies are applied."
"When configuring an AWS Firewall Manager policy for AWS WAF, what does a 'rule group' contain?","A set of WAF rules that define how to inspect web traffic","A list of AWS accounts to which the policy applies","A schedule for when the policy should be enforced","A collection of IAM permissions for managing the policy","A 'rule group' in an AWS Firewall Manager policy for AWS WAF contains a set of WAF rules that define how to inspect and filter web traffic, such as blocking known bad IP addresses or preventing SQL injection attacks."
"What is the primary purpose of AWS Firewall Manager?","To centrally manage and configure firewalls across multiple AWS accounts and resources.","To monitor network traffic for suspicious activity only.","To provide DDoS protection for individual EC2 instances only.","To manage IAM roles and permissions across an organisation.","AWS Firewall Manager allows you to centrally manage and configure WAF rules, Shield Advanced protections, and security groups across your AWS organisation."
"Which AWS services can AWS Firewall Manager be used to manage?","AWS WAF, AWS Shield Advanced, and VPC Security Groups.","Amazon GuardDuty, AWS Config, and AWS CloudTrail.","AWS IAM, AWS KMS, and AWS CloudWatch.","Amazon Inspector, AWS Trusted Advisor, and Amazon Macie.","Firewall Manager focuses on firewall-related services like WAF for web application protection, Shield Advanced for DDoS protection, and security groups for network access control."
"What is a 'policy' in the context of AWS Firewall Manager?","A set of rules that define how AWS WAF rules, AWS Shield Advanced protections, or security groups are applied.","A configuration file that defines the settings for an EC2 instance.","A document that outlines the security best practices for an organisation.","A script that automates the deployment of AWS resources.","A Firewall Manager policy defines the scope of resources to be protected and the specific security rules (e.g., WAF rules) to be applied."
"How does AWS Firewall Manager simplify security management across multiple AWS accounts?","By allowing you to create and deploy security policies centrally, which are then automatically applied to resources in multiple accounts.","By automatically replicating security groups across all AWS accounts.","By providing a single dashboard to monitor security events across all AWS accounts without central policy creation.","By requiring you to manually configure firewalls in each AWS account.","Firewall Manager provides centralised management by allowing you to define policies once and apply them consistently across your entire organisation."
"Which of the following is a benefit of using AWS Firewall Manager for AWS WAF rule management?","Centralised creation and deployment of WAF rules across multiple applications and accounts.","Automatic updates to WAF rules based on threat intelligence feeds only.","Unlimited WAF rule capacity without any cost implications only.","Simplified integration with third-party security information and event management (SIEM) systems only.","Firewall Manager enables centralised WAF rule creation and deployment, ensuring consistent protection across all web applications in your organisation."
"What is the scope of AWS Firewall Manager policies?","They can be applied to your entire AWS organisation, specific accounts, or resource groups.","They only apply to the AWS account where Firewall Manager is configured.","They only apply to specific EC2 instances within an AWS account.","They apply only to resources tagged with a specific tag.","Firewall Manager policies can be scoped to your entire organisation, specific accounts, or resource groups, providing flexibility in how you apply security controls."
"In AWS Firewall Manager, what is the purpose of a 'protection policy'?","To define the AWS WAF rules and AWS Shield Advanced protections to be applied to your resources.","To define IAM roles and permissions for accessing AWS resources.","To define the network configuration for your VPCs.","To define the data encryption settings for your S3 buckets.","A protection policy in Firewall Manager is specifically used to define how AWS WAF rules and AWS Shield Advanced protections are configured and applied to resources."
"What type of remediation action can AWS Firewall Manager perform for non-compliant resources?","Automatically apply the required security configurations to bring resources into compliance.","Generate a report of non-compliant resources without automatic remediation.","Delete non-compliant resources automatically.","Send an email notification to the resource owner.","Firewall Manager can automatically remediate non-compliant resources by applying the necessary security configurations, ensuring consistent security posture."
"Which AWS service is integrated with AWS Firewall Manager for central logging and analysis of security events?","AWS CloudWatch Logs","AWS Config","Amazon Inspector","AWS IAM","Firewall Manager integrates with CloudWatch Logs to collect and analyse security events from AWS WAF and Shield Advanced, providing a centralised logging solution."
"What is the role of AWS Organizations in relation to AWS Firewall Manager?","AWS Organizations is required to centrally manage firewall policies across multiple accounts.","AWS Organizations is optional, and Firewall Manager can be used with individual accounts.","AWS Organizations is only required for very large enterprises.","AWS Organizations is only required for AWS Shield Advanced.","AWS Organizations is fundamental for using Firewall Manager across multiple accounts, as it provides the organizational structure for centralized management."
"How does AWS Firewall Manager assist with managing AWS Shield Advanced?","By allowing you to centrally configure and deploy AWS Shield Advanced protections to your resources.","By automatically enabling AWS Shield Advanced for all resources in your AWS account.","By providing a dashboard to monitor DDoS attacks without any configuration options.","By automatically reducing your AWS Shield Advanced costs.","Firewall Manager enables centralized configuration and deployment of Shield Advanced protections, making it easier to manage DDoS protection across your organisation."
"What is the relationship between AWS Firewall Manager and security groups?","Firewall Manager can centrally manage and enforce security group rules across multiple VPCs and accounts.","Firewall Manager replaces the need for security groups entirely.","Firewall Manager only monitors security group configurations without any enforcement capabilities.","Firewall Manager only manages security groups within a single VPC.","Firewall Manager provides the ability to centrally manage and enforce security group rules, ensuring consistent network access control across your organisation."
"You want to enforce a common set of security group rules across all your AWS accounts using AWS Firewall Manager. How can you achieve this?","Create a Firewall Manager security group policy and apply it to your organisation.","Manually create the same security group rules in each AWS account.","Use AWS Config to enforce security group configurations.","Use AWS IAM to control security group access.","Firewall Manager's security group policy allows you to define and enforce a common set of rules across all accounts, simplifying security group management."
"Which AWS service provides visibility into your security posture by aggregating, organising and prioritising your security alerts and findings across AWS accounts, and is an optional integration for Firewall Manager?","AWS Security Hub","AWS Config","Amazon Inspector","AWS Trusted Advisor","Security Hub is often used in conjunction with Firewall Manager to aggregate and prioritise security findings across AWS accounts, providing comprehensive security posture visibility."
"What is the difference between using AWS Firewall Manager and manually configuring AWS WAF rules in each account?","Firewall Manager provides centralised management and automated deployment, while manual configuration is more time-consuming and prone to errors.","There is no difference, as both methods achieve the same outcome.","Manual configuration is always more secure than using Firewall Manager.","Firewall Manager only supports a limited set of WAF rules.","Firewall Manager simplifies WAF management by providing a centralised platform for rule creation, deployment, and maintenance across multiple accounts."
"Which type of AWS Firewall Manager policy is used to manage AWS Shield Advanced protection?","DDoS Protection Policy","WAF Policy","Security Group Policy","Data Protection Policy","A DDoS Protection Policy in AWS Firewall Manager is specifically designed to manage and apply AWS Shield Advanced protections to your resources, helping to mitigate DDoS attacks."
"You have deployed a new web application in AWS and want to protect it from common web exploits using AWS Firewall Manager. What steps should you take?","Create a WAF policy in Firewall Manager and associate it with your application load balancer.","Manually configure AWS WAF rules for your application load balancer.","Create a security group policy in Firewall Manager and associate it with your application load balancer.","Enable AWS Shield Advanced for your application load balancer.","By creating a WAF policy in Firewall Manager and associating it with your application load balancer, you can centrally manage and deploy WAF rules to protect your application from common web exploits."
"You need to ensure that all new AWS accounts created in your organisation automatically inherit a baseline set of security group rules. How can AWS Firewall Manager help?","By applying a security group policy to your organisation's root account, which is automatically inherited by all new accounts.","By manually creating security groups in each new AWS account.","By using AWS Config to enforce security group configurations.","By using AWS IAM to control security group access.","Firewall Manager can apply policies to your organisation's root account, ensuring that all new accounts automatically inherit the defined security group rules."
"Which AWS Firewall Manager policy type allows you to define and enforce a consistent set of security group rules across multiple VPCs?","Common Security Groups Policy","VPC Security Group Policy","Security Group Baseline Policy","Security Group Rule Policy","A Common Security Groups Policy allows you to define and enforce a consistent set of security group rules across multiple VPCs, ensuring uniform network access control."
"What action does AWS Firewall Manager take when it detects a non-compliant resource in a managed account?","It automatically remediates the resource by applying the required security configurations.","It sends an email notification to the account owner only.","It deletes the non-compliant resource automatically only.","It generates a report of non-compliant resources without taking any action only.","Firewall Manager is designed to automatically remediate non-compliant resources, bringing them into compliance with the defined security policies."
"Which of the following AWS services is NOT directly managed by AWS Firewall Manager?","Amazon GuardDuty","AWS WAF","AWS Shield Advanced","VPC Security Groups","Firewall Manager primarily focuses on managing WAF, Shield Advanced, and security groups, while GuardDuty is a separate service for threat detection."
"Which feature of AWS Firewall Manager allows you to test the impact of a policy before deploying it to production?","Dry Run mode","Simulated Deployment","Test Policy","Policy Preview","Firewall Manager's Dry Run mode allows you to test the impact of a policy before deploying it to production, helping you avoid unintended consequences."
"What is the benefit of using AWS Firewall Manager for centrally managing security group rules across multiple AWS accounts?","Ensuring consistent network access control across your entire organisation.","Replacing the need for security groups entirely.","Eliminating the need for IAM roles and permissions.","Reducing the cost of AWS WAF and AWS Shield Advanced.","Firewall Manager enables centralised management of security group rules, ensuring consistent network access control across all accounts in your organisation."
"How can you use AWS Firewall Manager to protect your web applications from OWASP Top 10 vulnerabilities?","By creating a WAF policy with pre-configured OWASP Top 10 rules.","By enabling AWS Shield Advanced for your web applications only.","By creating a security group policy that blocks all incoming traffic only.","By manually configuring AWS WAF rules for each web application.","Firewall Manager simplifies the process of protecting web applications from OWASP Top 10 vulnerabilities by allowing you to create a WAF policy with pre-configured rules."
"When using AWS Firewall Manager, what is the relationship between a policy and a resource group?","You can associate a policy with a resource group to apply the policy to all resources within that group.","A policy automatically creates a resource group.","A resource group automatically creates a policy.","Policies and resource groups are unrelated.","By associating a policy with a resource group, you can easily apply the policy to all resources within that group, simplifying policy deployment and management."
"What is the maximum number of AWS accounts that can be managed by a single AWS Firewall Manager administrator account?","There is no limit, Firewall Manager can manage your entire AWS Organization.","100","1000","10000","Firewall Manager is designed to scale and manage your entire AWS Organisation, supporting a large number of accounts."
"Which AWS service is used to build custom, automated workflows for security incident response with AWS Firewall Manager?","AWS Systems Manager Automation","AWS Step Functions","AWS Lambda","AWS CloudWatch Events","Systems Manager Automation can be used to orchestrate workflows for security incident response based on events detected by Firewall Manager, such as automatically isolating an infected EC2 instance."
"Which AWS service provides a centralised view of your security alerts and findings across all AWS accounts, complementing AWS Firewall Manager's proactive protection?","AWS Security Hub","AWS CloudTrail","AWS Config","Amazon Inspector","Security Hub aggregates findings from various AWS security services, providing a comprehensive view of security posture and complementing Firewall Manager's proactive security measures."
"If you have an existing AWS WAF configuration that you want to centrally manage with AWS Firewall Manager, what is the recommended approach?","Import the existing WAF configuration into Firewall Manager and deploy it as a policy.","Manually recreate the WAF configuration within Firewall Manager.","Delete the existing WAF configuration and start from scratch in Firewall Manager.","Migrate the existing WAF configuration to AWS Shield Advanced.","Firewall Manager enables importing existing WAF configurations, making it easier to centralise management without having to recreate everything manually."
"Which of the following is a key benefit of using AWS Firewall Manager policies to manage AWS Shield Advanced protection?","Simplified and consistent DDoS protection across multiple accounts and resources.","Automatic cost optimisation for AWS Shield Advanced.","Real-time threat intelligence updates for AWS Shield Advanced without any configuration.","Unlimited DDoS protection capacity.","Firewall Manager ensures that Shield Advanced protections are consistently applied across your organization, simplifying DDoS protection management."
"What type of AWS Firewall Manager policy would you use to ensure that all newly created VPCs automatically have a default set of security group rules?","A security group policy.","A WAF policy.","An AWS Shield Advanced policy.","An IAM policy.","A security group policy is the appropriate type of policy to enforce default security group rules on newly created VPCs, ensuring a baseline level of network security."
"Which of the following is NOT a key component of an AWS Firewall Manager policy?","Resource scope","Remediation action","IAM role","Policy name","IAM roles are not typically part of the Firewall manager policy definition but used to grant the necessary permissions to the Firewall manager to manage other service resources."
"What is the main difference between AWS WAF and AWS Firewall Manager?","AWS WAF protects individual web applications, while AWS Firewall Manager centrally manages WAF rules and other security configurations across multiple accounts.","AWS WAF is a free service, while AWS Firewall Manager is a paid service.","AWS WAF provides DDoS protection, while AWS Firewall Manager provides vulnerability scanning.","AWS WAF is managed through the AWS Management Console, while AWS Firewall Manager is managed through the AWS CLI.","AWS WAF focuses on protecting individual web applications, whereas Firewall Manager provides centralized management of multiple AWS WAF instances and other security policies."
"How does AWS Firewall Manager help in meeting compliance requirements?","By enforcing consistent security policies across all AWS accounts and resources.","By automatically generating compliance reports only.","By providing vulnerability scanning only.","By encrypting data at rest only.","Firewall Manager helps meet compliance requirements by ensuring consistent application of security policies, reducing the risk of misconfigurations and security gaps."
"What is the primary use case for the AWS Firewall Manager policy type 'Common Security Groups'?","To enforce a baseline set of security group rules across multiple VPCs in an organisation.","To create a centralised inventory of security groups across all AWS accounts only.","To automatically delete unused security groups only.","To encrypt security group data at rest only.","The 'Common Security Groups' policy type is specifically designed to enforce a consistent set of security group rules across multiple VPCs within an organization, promoting better network security."
"Which of the following AWS services can be used as a data source for AWS Firewall Manager's security policies?","AWS CloudTrail","AWS Config","Amazon Inspector","AWS Shield Advanced","CloudTrail can be used to monitor the enforcement of policies that Firewall Manager put in place and flag any changes that are made to it."
"Which AWS Firewall Manager feature helps you maintain a consistent security posture across new AWS resources?","Automatic resource discovery and protection.","Automatic vulnerability scanning.","Automatic threat intelligence updates.","Automatic cost optimisation.","AWS Firewall Manager will automatically discover and protect new AWS resources in your organisation if they are added to existing accounts or if new accounts are added."
"Which AWS Firewall Manager policy action automatically applies necessary configurations to non-compliant resources?","Auto-remediation","Manual remediation","Notification","Detection","The Auto-remediation action in AWS Firewall Manager automatically applies the required configurations to bring non-compliant resources back into compliance, ensuring consistent security posture."
"What is the role of AWS Organizations when using AWS Firewall Manager to manage resources across multiple AWS accounts?","AWS Organizations provides the organizational structure for Firewall Manager to centrally manage policies across accounts.","AWS Organizations is not required to use AWS Firewall Manager.","AWS Organizations is only used for billing purposes.","AWS Organizations is only used for IAM management.","AWS Organizations is essential for Firewall Manager's centralised management capabilities, providing the hierarchical structure for policy deployment and enforcement across multiple accounts."
"What is a key advantage of using AWS Firewall Manager over manually configuring security settings on each AWS resource?","Centralized management and automated enforcement of security policies across multiple accounts and resources.","Lower cost compared to manually configuring security settings.","Increased security performance compared to manually configured resources.","Simplified compliance reporting compared to manually configured resources.","AWS Firewall Manager's centralised management and automated enforcement provide significant advantages in terms of efficiency, consistency, and scalability compared to manual configuration."
"How does AWS Firewall Manager integrate with AWS Shield Advanced to provide enhanced DDoS protection?","AWS Firewall Manager centrally manages the configuration and deployment of AWS Shield Advanced protections across multiple resources.","AWS Firewall Manager automatically upgrades all AWS accounts to AWS Shield Advanced.","AWS Firewall Manager provides real-time DDoS attack alerts only.","AWS Firewall Manager automatically reduces the cost of AWS Shield Advanced only.","By centrally managing the configuration and deployment of AWS Shield Advanced protections, AWS Firewall Manager simplifies DDoS protection and ensures consistent protection across your resources."
"Which AWS Firewall Manager policy type is best suited for protecting against common web application vulnerabilities?","AWS WAF policy","AWS Shield Advanced policy","Security Group policy","Network ACL policy","The AWS WAF policy type is specifically designed to protect against common web application vulnerabilities, such as those listed in the OWASP Top 10, by using predefined or custom WAF rules."
"What type of AWS Firewall Manager policy would you use to establish a minimum baseline of security group rules that all new and existing VPCs must adhere to?","A security group policy","An AWS WAF policy","An AWS Shield Advanced policy","An IAM policy","A security group policy is the correct policy type to establish a minimum baseline of security group rules, ensuring consistent network security across your VPCs."
"What is a 'resource scope' in the context of an AWS Firewall Manager policy?","The set of AWS resources to which the policy applies (e.g., EC2 instances, VPCs, Application Load Balancers).","The geographical region where the policy is enforced.","The time period during which the policy is active.","The number of AWS accounts that are managed by the policy.","The resource scope defines the set of AWS resources that will be protected and governed by the policy, allowing for targeted application of security controls."
"Which AWS service is integrated with AWS Firewall Manager to provide detailed logging and monitoring of security-related events?","AWS CloudWatch","AWS Config","AWS IAM","AWS Trusted Advisor","AWS CloudWatch provides detailed logging and monitoring capabilities that integrate seamlessly with AWS Firewall Manager, enabling you to track and analyse security-related events."
"What is the relationship between AWS Firewall Manager and IAM roles?","IAM roles are used to grant AWS Firewall Manager the necessary permissions to manage resources across multiple AWS accounts.","AWS Firewall Manager automatically creates IAM roles for all AWS accounts.","AWS Firewall Manager replaces the need for IAM roles entirely.","AWS Firewall Manager does not require any IAM roles.","IAM roles are essential for AWS Firewall Manager to operate across multiple AWS accounts, as they grant the service the necessary permissions to manage resources and enforce policies."
"In AWS Firewall Manager, what does the term 'remediation' refer to?","The process of automatically fixing non-compliant resources to adhere to the defined security policies.","The process of detecting security vulnerabilities in AWS resources only.","The process of generating security reports only.","The process of manually configuring security settings only.","Remediation in AWS Firewall Manager refers to the automated process of bringing non-compliant resources into compliance with the defined security policies."
"Which AWS service is used to automatically assess AWS resources for vulnerabilities and deviations from best practices, complementing AWS Firewall Manager's protection policies?","Amazon Inspector","AWS Config","AWS CloudTrail","Amazon GuardDuty","Amazon Inspector can be used to automatically assess AWS resources for vulnerabilities and deviations from best practices, complementing AWS Firewall Manager's protection policies."
"With AWS Firewall Manager, what is a common use case for centralising security policy management?","Ensuring consistent security policies across multiple AWS accounts and resources","Automating the deployment of EC2 instances","Managing IAM roles and permissions","Monitoring network traffic for intrusion detection","Firewall Manager allows you to centrally configure and manage firewall rules across your organisation's AWS accounts, ensuring consistent security."
"In AWS Firewall Manager, what is the primary function of a 'protection policy'?","To define the scope of AWS resources that the policy applies to and the security actions to be taken","To automatically patch operating systems on EC2 instances","To manage user access to AWS resources","To create and manage AWS Lambda functions","A protection policy in Firewall Manager defines which resources are protected and the actions taken to enforce security, such as configuring WAF rules."
"What AWS services can Firewall Manager use to enforce policies?","AWS WAF, AWS Shield Advanced, VPC Security Groups, and Network Firewall","AWS IAM, AWS CloudTrail, AWS Config, and AWS Lambda","AWS EC2, AWS S3, AWS RDS, and AWS DynamoDB","AWS CloudWatch, AWS SNS, AWS SQS, and AWS KMS","Firewall Manager integrates with WAF, Shield Advanced, Security Groups and Network Firewall to enforce security policies across your AWS resources."
"Which AWS Firewall Manager feature allows you to automatically remediate non-compliant resources?","Auto-remediation","Compliance Advisor","Security Hub Integration","Centralised Logging","Auto-remediation allows Firewall Manager to automatically correct configurations that violate your defined security policies, reducing manual intervention."
"How does AWS Firewall Manager help with compliance?","By providing a central location to enforce and monitor security policies, ensuring adherence to regulatory requirements","By automatically generating compliance reports for auditors","By replacing the need for compliance audits","By managing encryption keys for compliance","Firewall Manager aids compliance by ensuring consistent security policies across your organisation, making it easier to meet regulatory requirements and demonstrate compliance."
"What is the scope of policy deployment in AWS Firewall Manager?","Organisation or individual accounts","Only individual accounts","Only specific AWS regions","Only VPCs in a single account","Firewall Manager allows you to deploy policies across your entire AWS Organization or to selected accounts within the organisation."
"Which AWS service must be enabled and configured to use AWS Firewall Manager across an organisation?","AWS Organizations","AWS IAM","AWS CloudTrail","AWS Config","AWS Organizations is a prerequisite for using Firewall Manager across multiple accounts, allowing for centralised management."
"What resource types can AWS Firewall Manager protect with AWS WAF?","Application Load Balancers, API Gateways, and CloudFront distributions","EC2 instances, S3 buckets, and RDS databases","IAM roles, Lambda functions, and DynamoDB tables","VPC endpoints, Network Load Balancers, and Transit Gateways","Firewall Manager can protect Application Load Balancers, API Gateways and CloudFront distributions using WAF."
"If a new AWS account is added to an AWS Organization managed by Firewall Manager, what happens to the existing security policies?","They are automatically applied to the new account if the policy scope includes the organisation.","They must be manually deployed to the new account.","The new account is automatically excluded from the policies.","A new policy must be created specifically for the new account.","Firewall Manager automatically applies existing security policies to new accounts added to the organisation if the policy scope includes the organisation, ensuring consistent protection."
"What type of rules can be centrally managed and deployed using AWS Firewall Manager for VPC Security Groups?","Audit, Enforcement, and Common Security Groups","Only Ingress rules","Only Egress rules","Only default security group rules","Firewall Manager allows you to manage Audit, Enforcement, and Common Security Groups across your VPCs."
"In AWS Firewall Manager, what does the 'Audit' security group policy do?","Identifies security group rules that deviate from a baseline configuration.","Automatically enforces a specific security group configuration.","Creates new security groups across multiple accounts.","Deletes unused security groups.","The 'Audit' policy identifies security group rules that are out of compliance, allowing you to identify deviations from a baseline."
"What is the advantage of using AWS Firewall Manager with AWS Shield Advanced?","Centralised management of DDoS protection across multiple resources.","Automated vulnerability scanning of EC2 instances.","Centralised management of IAM permissions.","Automated patching of operating systems.","Firewall Manager allows you to centrally configure and manage DDoS protection using Shield Advanced across multiple AWS resources, providing consistent protection."
"Which of the following is NOT a supported security policy type in AWS Firewall Manager?","AWS IAM Policy","AWS WAF Policy","AWS Shield Advanced Policy","VPC Security Group Policy","Firewall Manager does not directly manage AWS IAM Policies."
"Which of the following can be used as scope of resources to be protected by AWS Firewall Manager?","AWS accounts and resource tags","Only AWS accounts","Only resource tags","Only AWS Regions","AWS Firewall Manager allows you to define the scope of resources to be protected based on AWS accounts and resource tags."
"How can you monitor the compliance status of your AWS Firewall Manager policies?","Using the Firewall Manager dashboard and AWS Config rules","By checking the CloudWatch logs for each account","By manually inspecting each resource","Using AWS Trusted Advisor","Firewall Manager provides a dashboard for monitoring compliance status, and integrates with AWS Config for detailed compliance reporting."
"What is the purpose of a 'common security group' policy in AWS Firewall Manager?","To ensure that a specific set of security group rules are consistently applied across multiple accounts.","To block common security threats.","To simplify the process of creating new security groups.","To provide a template for security group creation.","A 'common security group' policy ensures that a specific set of security group rules are consistently applied across multiple accounts."
"Which action can you take to investigate why a particular AWS resource is non-compliant with a Firewall Manager policy?","Review the AWS Config rules associated with the Firewall Manager policy.","Check the CloudWatch logs for the resource.","Review the IAM policies attached to the resource.","Check the VPC flow logs.","AWS Config rules are used by Firewall Manager to evaluate compliance, so reviewing these rules provides insights into why a resource is non-compliant."
"What is the relationship between AWS Firewall Manager and AWS Security Hub?","Firewall Manager integrates with Security Hub to provide a consolidated view of security alerts and compliance status.","Firewall Manager replaces Security Hub.","Security Hub replaces Firewall Manager.","They are completely independent services.","Firewall Manager integrates with Security Hub to provide a centralised view of security findings and compliance status across your AWS environment."
"Can AWS Firewall Manager manage Network Firewall rules?","Yes, Firewall Manager can centrally manage Network Firewall rules across multiple VPCs.","No, Firewall Manager only manages WAF rules.","Firewall Manager can only monitor Network Firewall rules, not manage them.","Network Firewall does not integrate with Firewall Manager.","Firewall Manager allows you to centrally manage Network Firewall rules, providing consistent network security policies across multiple VPCs."
"Which of the following statements is correct regarding the deployment of AWS WAF rules through Firewall Manager?","Firewall Manager simplifies the process of deploying and managing WAF rules across multiple Application Load Balancers, API Gateways, and CloudFront distributions.","Firewall Manager only supports deploying WAF rules to Application Load Balancers.","Firewall Manager only supports deploying WAF rules to CloudFront distributions.","Firewall Manager does not support deploying WAF rules.","Firewall Manager simplifies the process of deploying and managing WAF rules across multiple ALBs, API Gateways, and CloudFront distributions."
"What is the purpose of the 'Enforcement' policy in AWS Firewall Manager for Security Groups?","To automatically enforce a specific set of security group rules across multiple AWS accounts.","To audit security group configurations.","To create new security groups.","To delete unused security groups.","The 'Enforcement' policy automatically enforces a specific set of security group rules across multiple AWS accounts, ensuring consistent security."
"When deploying AWS Firewall Manager policies, what is the 'resource tag' criteria used for?","To specify which resources the policy should apply to based on their tags.","To automatically tag resources.","To identify non-compliant resources.","To generate compliance reports.","Resource tags allow you to precisely target which resources should be protected by a Firewall Manager policy."
"Which of the following AWS services provides integration with AWS Firewall Manager to help you manage and monitor the security of your AWS infrastructure?","AWS Security Hub","AWS CloudTrail","AWS CloudWatch","AWS IAM","AWS Security Hub integrates with Firewall Manager to provide a centralised view of security alerts and compliance status across your AWS infrastructure."
"In AWS Firewall Manager, what are the benefits of using a central firewall policy?","It ensures consistent security policies across multiple accounts, simplifies management, and reduces the risk of misconfiguration.","It eliminates the need for individual firewall configurations.","It automatically patches operating systems.","It improves network performance.","A central firewall policy ensures consistent security, simplifies management and reduces misconfiguration risk."
"Which AWS Firewall Manager feature helps you identify overly permissive security group rules?","Security Group Audit Policy","WAF Rule Evaluation","Network Firewall Logging","VPC Flow Logs Analysis","The Security Group Audit Policy in Firewall Manager helps you identify security group rules that are too open, reducing potential security risks."
"What is the recommended approach for deploying AWS Firewall Manager policies?","Start with an audit policy, then move to an enforcement policy once you are confident in the configuration.","Immediately deploy an enforcement policy.","Deploy policies manually in each account.","Only use the default policies provided by AWS.","Starting with an audit policy allows you to assess the impact of the rules before automatically enforcing them."
"How can you use AWS Firewall Manager to protect against common web exploits, such as SQL injection and cross-site scripting (XSS)?","By deploying AWS WAF rules that block these types of attacks.","By enabling AWS Shield Advanced.","By configuring VPC security groups.","By enabling CloudTrail logging.","AWS Firewall Manager can deploy WAF rules to protect against common web exploits like SQL injection and XSS."
"What is the main advantage of using AWS Firewall Manager over manually configuring security policies in each AWS account?","Centralised management and consistent enforcement of policies across multiple accounts.","Lower cost of AWS services.","Improved network performance.","Automatic patching of EC2 instances.","Firewall Manager centralises management and ensures consistent enforcement across accounts, reducing administrative overhead and improving security posture."
"Which AWS service does AWS Firewall Manager utilise to evaluate if a resource is compliant with its assigned security policies?","AWS Config","AWS CloudTrail","AWS CloudWatch","AWS Trusted Advisor","AWS Config is used by Firewall Manager to assess whether resources comply with the defined security policies."
"You need to ensure that all new AWS accounts created within your organisation are automatically protected by your existing AWS Firewall Manager policies. How can you achieve this?","By configuring the Firewall Manager policy to apply to the entire AWS Organization.","By manually adding each new account to the policy scope.","By creating a separate policy for each new account.","By enabling AWS CloudTrail.","Configuring the Firewall Manager policy to apply to the entire AWS Organization ensures that any new accounts are automatically protected."
"What is the purpose of the 'prevention' policy type in AWS Firewall Manager for Network Firewall?","To enforce Network Firewall rules on new VPCs and subnets automatically","To only monitor Network Firewall rules, and not automatically apply them","To create reports of Network Firewall rule compliance","To delete unused Network Firewall rules","The 'prevention' policy type in AWS Firewall Manager enforces Network Firewall rules automatically on new VPCs and subnets."
"If you have a complex AWS environment with numerous AWS accounts and resources, how does AWS Firewall Manager simplify security management?","By providing a single pane of glass to manage and enforce security policies across the entire environment.","By automatically patching operating systems on EC2 instances.","By replacing the need for security experts.","By eliminating the need for logging.","Firewall Manager gives you a single place to manage and enforce security policies across your environment."
"What is the maximum number of AWS accounts that can be managed by a single AWS Firewall Manager administrator account?","There is no limit, as long as the accounts are part of the same AWS Organization.","100","1000","10000","Firewall Manager can manage any number of AWS accounts within the same AWS Organization, with no inherent limit."
"When using AWS Firewall Manager, what is the benefit of tagging resources?","Allows for granular control over which resources are protected by policies.","Automatically configures resource permissions.","Reduces the cost of AWS services.","Improves network performance.","Resource tags enable you to precisely control which resources are protected by Firewall Manager policies based on custom criteria."
"How does AWS Firewall Manager assist with enforcing PCI DSS compliance?","By providing centralised management of security policies that align with PCI DSS requirements.","By automatically generating PCI DSS compliance reports.","By replacing the need for PCI DSS audits.","By managing encryption keys for PCI DSS compliance.","Firewall Manager allows you to centrally manage security policies that align with PCI DSS requirements, helping you to meet compliance standards."
"Which AWS service is commonly used alongside AWS Firewall Manager to provide threat intelligence and security analytics?","AWS Security Hub","AWS CloudTrail","AWS CloudWatch","AWS IAM","AWS Security Hub provides a centralised view of security findings and compliance status, complementing Firewall Manager's policy management capabilities."
"Can you use AWS Firewall Manager to manage security policies across multiple AWS Regions?","Yes, Firewall Manager supports managing policies across multiple AWS Regions.","No, Firewall Manager is limited to a single AWS Region.","Firewall Manager can only monitor policies in multiple regions, not manage them.","Policies must be deployed manually in each region.","Firewall Manager allows you to centrally manage security policies across multiple AWS Regions, ensuring global consistency."
"What is the primary purpose of the 'AWSNetworkFirewallPolicy' in Firewall Manager?","To define and manage network firewall rules for VPCs using AWS Network Firewall.","To manage WAF rules.","To manage security group rules.","To manage IAM permissions.","The 'AWSNetworkFirewallPolicy' defines and manages Network Firewall rules for VPCs, enabling centralised network security management."
"You are configuring AWS Firewall Manager to protect your web applications. Which AWS service would you typically configure to work with Firewall Manager in this scenario?","AWS WAF","AWS Shield","AWS IAM","AWS Config","AWS WAF is typically configured with Firewall Manager to protect web applications by defining rules that filter malicious traffic."
"Which of the following actions CANNOT be automated by AWS Firewall Manager?","Patching EC2 instances operating systems","Enforcing AWS WAF rules across accounts","Enforcing Network Firewall rules across accounts","Auditing Security Group rules across accounts","AWS Firewall Manager cannot patch EC2 instances operating systems."
"When AWS Firewall Manager detects a non-compliant AWS WAF rule, what actions can it take?","Auto remediate to enforce compliance or alert the administrators","Only alert the administrators","Only auto remediate","Ignore the non-compliance","AWS Firewall Manager can be configured to either automatically remediate non-compliant WAF rules or alert administrators."
"Which of the following is NOT a valid scope for an AWS Firewall Manager policy?","Individual EC2 instances","All accounts in your AWS Organization","Specific AWS accounts","Accounts with specific tags","Firewall Manager policies cannot be scoped down to individual EC2 instances; the minimum scope is an AWS account."
"You want to create a new AWS Firewall Manager policy that protects all new Application Load Balancers (ALBs) created in your AWS Organization. What type of policy would you create?","AWS WAF Policy","AWS Shield Advanced Policy","VPC Security Group Policy","AWS IAM Policy","AWS WAF Policy is used to manage WAF rules to protect web applications, including new ALBs."
"Which of the following is a valid remediation action for a non-compliant Security Group when managed by AWS Firewall Manager?","Firewall Manager modifies the Security Group rules to enforce the desired state.","Firewall Manager deletes the non-compliant Security Group.","Firewall Manager moves the non-compliant Security Group to a quarantine account.","Firewall Manager creates a new Security Group with the correct rules.","Firewall Manager directly modifies the Security Group rules to align with the defined policy and enforce compliance."
"How can you ensure that all AWS accounts in your organisation inherit the same baseline security group rules using AWS Firewall Manager?","Use a common security groups policy and enforce it across your organisation","Manually create the same security group rules in each account.","Write a script to copy security group rules between accounts.","Use AWS CloudFormation to deploy security group rules.","A common security groups policy ensures that a specific set of security group rules are consistently applied across multiple accounts."
"What is the purpose of setting precedence within AWS Firewall Manager policies?","To determine the order in which policies are evaluated and applied when multiple policies overlap.","To determine which policy is the most important.","To determine which policy should be deployed first.","To determine which policy should be deleted first.","Precedence defines the order in which policies are evaluated, especially when they overlap, ensuring the correct policy is applied first."
"Which AWS Firewall Manager policy setting allows you to specify which resources should be excluded from a policy's scope?","Exclusion scope","Inclusion scope","Resource scope","Policy scope","The exclusion scope setting allows you to define resources that should be excluded from the policy's protection."
"Which component of AWS Firewall Manager provides the ability to centrally view and manage all firewall configurations across an organisation's AWS accounts?","Firewall Manager Console","AWS Config Console","AWS Security Hub Console","AWS CloudWatch Console","The Firewall Manager Console provides a centralised view and management interface for firewall configurations across AWS accounts."