"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Amazon Inspector?","Automated security vulnerability management","Continuous compliance monitoring","Real-time threat detection","Data loss prevention","Amazon Inspector automatically assesses AWS workloads for software vulnerabilities and unintended network exposure."
"Which Amazon Inspector feature allows you to define the scope of an assessment?","Target","Exclusion","Rule Package","Finding","The 'Target' defines the AWS resources that <PERSON> will assess for vulnerabilities."
"Which type of rule package in Amazon Inspector helps identify security vulnerabilities in your EC2 instances' operating system and applications?","Common Vulnerabilities and Exposures (CVE)","Network Reachability","Security Best Practices","Runtime Behaviour Analysis","CVE rule packages detect vulnerabilities based on published CVEs in the operating system and applications."
"In Amazon Inspector, what does a 'finding' represent?","A potential security issue identified during an assessment","A defined security policy","A resource excluded from assessment","A summary of assessment results","A 'finding' in Inspector indicates a specific security vulnerability or deviation from best practices that was detected during an assessment."
"Which AWS service does Amazon Inspector integrate with to get information about your AWS environment?","AWS Systems Manager Agent (SSM Agent)","AWS CloudTrail","Amazon CloudWatch","AWS Config","Amazon Inspector requires the SSM Agent to be installed on the targeted EC2 instances to collect inventory and security data."
"Which of the following is a valid Target type for an Amazon Inspector assessment?","EC2 instances","S3 buckets","Lambda functions","IAM roles","Amazon Inspector assesses EC2 instances."
"Which Amazon Inspector assessment template setting allows you to specify the duration of the assessment run?","Duration","Frequency","Scope","Severity","The duration setting determines how long the assessment will run, impacting the thoroughness of the analysis."
"What is the purpose of 'Exclusions' in Amazon Inspector assessment targets?","To prevent certain resources from being assessed","To specify allowed network traffic","To define security boundaries","To encrypt assessment data","Exclusions allow you to exclude specific resources from being scanned, such as test or development environments."
"Which Amazon Inspector reporting feature allows you to export findings in a detailed format?","Assessment Reports","Summary Reports","Compliance Reports","Dashboard Reports","Assessment Reports provide a comprehensive view of the findings, including details and recommendations."
"What is the first step to use Amazon Inspector to assess an EC2 instance?","Install the AWS Systems Manager (SSM) Agent on the instance","Create an IAM role for Inspector","Create an Amazon Inspector assessment target","Create an Amazon Inspector assessment template","The SSM Agent needs to be installed so the EC2 instance can be inspected."
"Which Amazon Inspector assessment type identifies potential network vulnerabilities?","Network Reachability","Host Assessment","Compliance Assessment","Security Best Practices","The Network Reachability assessment type identifies unintended network access to your EC2 instances."
"What does the Amazon Inspector 'Severity' level indicate for a finding?","The potential impact of the vulnerability","The ease of remediation","The cost of fixing the vulnerability","The time to remediate","The Severity level reflects the potential impact a vulnerability could have on your application or infrastructure."
"How can you trigger an Amazon Inspector assessment automatically based on a schedule?","Using Amazon CloudWatch Events (now EventBridge)","Using AWS Config rules","Using AWS Lambda functions","Using AWS Step Functions","CloudWatch Events can be configured to trigger Inspector assessments on a schedule."
"Which AWS IAM policy permission is required to allow Amazon Inspector to perform security assessments on your EC2 instances?","AmazonInspectorServiceRolePolicy","AmazonInspectorFullAccess","AmazonEC2ReadOnlyAccess","IAMFullAccess","The AmazonInspectorServiceRolePolicy is required to grant Inspector the necessary permissions to access and assess your EC2 instances."
"What is the purpose of the Amazon Inspector Agent?","Collect system information and send it to Inspector","Encrypt data at rest","Manage IAM roles","Monitor network traffic","The Inspector Agent collects data from your EC2 instances and sends it to the Inspector service for analysis."
"With regards to Amazon Inspector, what does 'telemetry data' refer to?","Data collected from AWS resources for analysis","Data used to create reports","Data used to configure assessments","Data encrypted at rest","Telemetry data is the information collected from the EC2 instances or other resources being assessed."
"How can you integrate Amazon Inspector findings with your existing security tools?","Using Amazon EventBridge to forward findings","Using AWS Config to remediate findings","Using AWS CloudTrail to monitor assessment activity","Using AWS IAM to control access","EventBridge allows you to route Inspector findings to other security tools for further analysis or remediation."
"Which Amazon Inspector feature allows you to define custom security rules?","Custom Rules Packages","Assessment Templates","Assessment Targets","Findings Filters","While you can use existing rule packages, you cannot define fully custom security rules directly within Inspector. You can however filter findings based on existing rules."
"Which Amazon Inspector feature helps in prioritising findings based on their potential impact?","Severity levels","Rule package descriptions","Assessment reports","Exclusion lists","Severity levels (High, Medium, Low) help you prioritise which findings to address first."
"Which is the best practice for using Amazon Inspector to automate continuous security assessment?","Integrate Inspector with CI/CD pipelines","Manually run assessments on a regular basis","Rely solely on AWS Security Hub for security checks","Disable Inspector by default to save costs","Integrating Inspector into your CI/CD pipelines ensures that new infrastructure and application deployments are automatically assessed for security vulnerabilities."
"When using Amazon Inspector, which step comes after defining the assessment target?","Create an assessment template","Install the Amazon Inspector agent","Review the assessment findings","Define the security groups","After defining the target, you need to define the template to execute a new assessment."
"What type of information does Amazon Inspector gather from EC2 instances to identify vulnerabilities?","Operating system and application versions","Network traffic patterns","User login history","CPU utilisation metrics","Inspector collects operating system and application versions to match against known vulnerabilities in databases."
"Which service is most commonly used to deliver Amazon Inspector findings to a ticketing system?","Amazon EventBridge","AWS CloudTrail","Amazon CloudWatch Logs","AWS Config","Amazon EventBridge allows routing findings to services that can create tickets or trigger other automated remediation actions."
"What is the benefit of regularly updating the Amazon Inspector agent on your EC2 instances?","Ensures the agent has the latest vulnerability definitions","Improves the performance of the EC2 instance","Reduces network bandwidth consumption","Prevents the agent from crashing","Regular updates to the Inspector agent ensure it has the latest vulnerability definitions, leading to more accurate and comprehensive assessments."
"Which of the following is a key benefit of using Amazon Inspector for vulnerability management?","Automated and continuous assessment","Manual and periodic assessments","Real-time threat detection","Data encryption at rest","Amazon Inspector provides automated and continuous assessment of your AWS workloads for vulnerabilities, reducing the need for manual effort."
"How can you use Amazon Inspector to identify publicly accessible EC2 instances?","Using the Network Reachability rule package","Using the Security Best Practices rule package","Using the Common Vulnerabilities and Exposures (CVE) rule package","Using a custom rule package","The Network Reachability rule package specifically focuses on identifying network exposure, including publicly accessible instances."
"What is the maximum assessment duration you can configure in an Amazon Inspector assessment template?","12 hours","24 hours","72 hours","168 hours","The maximum duration for an assessment is 12 hours."
"When viewing Amazon Inspector findings, what does a 'High' severity level typically indicate?","A critical vulnerability that needs immediate attention","A low-risk issue with minimal impact","An informational finding with no security implications","A potential performance bottleneck","A 'High' severity finding indicates a critical vulnerability that could have a significant impact on your application or data."
"What is the recommended approach for remediating findings identified by Amazon Inspector?","Follow the recommendations provided in the Inspector findings","Ignore low-severity findings","Disable the affected resources","Contact AWS support immediately","Inspector findings provide specific recommendations on how to address the identified vulnerabilities."
"Which of the following is a limitation of Amazon Inspector?","It only supports assessing EC2 instances","It requires manual installation of the agent on all instances","It cannot be integrated with other security tools","It does not provide remediation guidance","While Inspector primarily targets EC2, it doesn't only support that. The biggest limitation here is it requires the SSM agent."
"What is the relationship between Amazon Inspector and AWS Security Hub?","Security Hub aggregates findings from Inspector and other security services","Inspector replaces Security Hub","Security Hub configures Inspector assessments","Inspector remediates findings identified by Security Hub","Security Hub aggregates findings from various AWS security services, including Amazon Inspector, providing a centralised view of your security posture."
"Which AWS account permission is required to enable and configure Amazon Inspector?","AdministratorAccess","SecurityAudit","ReadOnlyAccess","Billing","AdministratorAccess is generally required to enable and configure AWS services like Inspector."
"Which of the following best describes the scope of assessment for the Amazon Inspector 'Runtime Behavior Analysis' rule package (if it existed)?","Analysing the behaviour of running applications for suspicious activity","Identifying network misconfigurations","Scanning for known software vulnerabilities","Checking compliance against industry standards","This rule package, if it existed, would focus on monitoring the runtime behaviour of applications to detect anomalous or suspicious activity."
"What is the purpose of the Amazon Inspector 'Suppression Rules'?","To automatically ignore specific findings based on defined criteria","To automatically fix identified vulnerabilities","To create custom security rule packages","To automatically scale EC2 instances based on security needs","Suppression Rules are used to ignore findings that are deemed acceptable or not relevant based on specific criteria."
"Which of the following can be used to automatically deploy the Amazon Inspector agent to new EC2 instances?","AWS Systems Manager Automation","AWS CloudFormation","AWS Config Rules","Amazon EC2 Auto Scaling","AWS Systems Manager Automation can be used to automate the installation and configuration of the Inspector agent on EC2 instances."
"When an Amazon Inspector assessment is completed, how can you access the findings?","Through the Amazon Inspector console or API","Through Amazon CloudWatch Logs","Through AWS Config","Through Amazon S3","Findings are available directly within the Inspector console and can also be accessed programmatically via the API."
"What is the primary purpose of the Amazon Inspector API?","Automate security assessments and reporting","Manage IAM roles and permissions","Monitor network traffic in real-time","Deploy EC2 instances","The Inspector API allows you to programmatically manage assessments, retrieve findings, and automate other tasks."
"Which of the following is a key benefit of integrating Amazon Inspector with your CI/CD pipeline?","Automated security testing of new code and infrastructure","Faster deployment of applications","Reduced infrastructure costs","Simplified compliance reporting","Integrating Inspector into your CI/CD pipeline allows you to automatically test new code and infrastructure for vulnerabilities before they are deployed to production."
"How does Amazon Inspector help with compliance requirements?","By identifying vulnerabilities that could violate compliance standards","By automatically enforcing compliance policies","By generating compliance reports","By encrypting data at rest","Inspector helps identify vulnerabilities that could potentially violate compliance standards, allowing you to address them proactively."
"What is the most efficient way to manage Amazon Inspector findings at scale across multiple AWS accounts?","Using AWS Security Hub","Using AWS Organizations","Using AWS Trusted Advisor","Using Amazon CloudWatch","AWS Security Hub provides a centralised view of security findings across multiple AWS accounts, making it the most efficient way to manage Inspector findings at scale."
"Which Amazon Inspector setting determines how often assessments are run?","Frequency","Duration","Scope","Severity","The frequency setting specifies how often assessments are scheduled to run (e.g., daily, weekly, monthly)."
"Which of the following represents a 'best practice' when configuring Amazon Inspector?","Regularly review and update assessment templates","Rely solely on the default rule packages","Disable Inspector when not actively assessing resources","Grant Inspector overly permissive IAM roles","Regularly reviewing and updating assessment templates ensures they are aligned with your current security needs and policies."
"If Amazon Inspector identifies a vulnerability in a third-party library used by your application, what should you do?","Update the library to the latest secure version","Disable the affected EC2 instance","Ignore the finding if it's a low-severity issue","Rebuild the entire application from scratch","Updating the library to the latest version is the most common and effective way to address vulnerabilities in third-party components."
"What is the purpose of the Amazon Inspector 'Network Reachability' rules package?","To identify unintended network exposure of EC2 instances","To detect malware on EC2 instances","To check for compliance with PCI DSS standards","To monitor network traffic patterns","The Network Reachability rules package focuses on identifying instances that are unintentionally exposed to the internet or other unauthorized networks."
"Which of the following actions would NOT be considered part of a typical workflow when using Amazon Inspector?","Automatically remediating findings without human review","Defining assessment targets and templates","Running assessments on a regular schedule","Reviewing and prioritising findings","While automation is desirable, automatically remediating findings without human review can be risky. A best practice is to review and validate the remediation before applying it automatically."
"What is the main benefit of integrating Amazon Inspector findings with a ticketing system (e.g., Jira)?","Streamlining the remediation workflow","Automating vulnerability assessments","Reducing the cost of security audits","Improving application performance","Integrating with a ticketing system streamlines the process of assigning, tracking, and resolving security vulnerabilities identified by Inspector."
"Which AWS service can be used to create a dashboard to visualise Amazon Inspector findings?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch dashboards can be used to visualise metrics and logs related to Inspector findings, providing a consolidated view of your security posture."
"You need to ensure that all new EC2 instances launched in your AWS account are automatically assessed by Amazon Inspector. Which approach is the most effective?","Automate the deployment of the Amazon Inspector agent using AWS Systems Manager","Manually install the Amazon Inspector agent on each new instance","Create an AWS Lambda function to trigger an assessment every time a new instance is launched","Schedule a daily Amazon Inspector assessment that includes all EC2 instances","Automating the agent deployment with SSM ensures that all new instances are automatically covered by Inspector assessments."
"What is the relationship between Amazon Inspector and AWS Config?","AWS Config can track changes to Inspector configurations","Inspector replaces AWS Config for security assessments","AWS Config triggers Inspector assessments","Inspector remediates findings identified by AWS Config","AWS Config can track configuration changes to Inspector, providing an audit trail of how your assessments are configured."
"Which of the following IAM policies is required to allow a user to view Amazon Inspector findings?","AmazonInspectorReadOnlyAccess","AmazonInspectorFullAccess","AmazonEC2ReadOnlyAccess","IAMFullAccess","The `AmazonInspectorReadOnlyAccess` policy grants the user the necessary permissions to view Inspector findings without allowing them to make changes to the configuration."
"When prioritising Amazon Inspector findings, what factor should be considered in addition to the 'Severity' level?","The exploitability of the vulnerability","The cost of remediation","The age of the finding","The name of the affected resource","The exploitability of the vulnerability (how easily it can be exploited) is a crucial factor to consider when prioritising remediation efforts, as it indicates the immediate risk posed by the finding."
"What is the primary function of Amazon Inspector?","Automated security vulnerability assessments","Real-time threat detection","Network traffic analysis","Compliance reporting","Amazon Inspector automates security assessments to help improve the security and compliance of applications deployed on AWS."
"What types of resources can Amazon Inspector assess for vulnerabilities?","EC2 instances and container images","S3 buckets and IAM roles","VPC configurations and CloudTrail logs","Lambda functions and API Gateways","Amazon Inspector assesses EC2 instances and container images for vulnerabilities."
"Which of the following is a key benefit of using Amazon Inspector?","Reduced operational overhead for security assessments","Simplified compliance reporting for all AWS services","Automated remediation of security findings","Real-time monitoring of network traffic","Amazon Inspector automates the security assessment process, reducing the operational overhead required for manual security checks."
"What does Amazon Inspector use to identify vulnerabilities in EC2 instances?","Security agents installed on the instances","Network traffic analysis","AWS CloudTrail logs","IAM role policies","Amazon Inspector uses a security agent installed on the EC2 instances to collect and analyse security-related data."
"In Amazon Inspector, what is a 'finding'?","A potential security issue detected by Inspector","A rule defined for security assessments","A report generated after an assessment","A configuration setting for Inspector","A finding in Amazon Inspector represents a potential security issue identified during an assessment."
"Which AWS service can you integrate with Amazon Inspector to automatically remediate vulnerabilities?","AWS Systems Manager","AWS Config","AWS CloudWatch","AWS CloudTrail","AWS Systems Manager can be used to automatically patch or remediate vulnerabilities identified by Amazon Inspector."
"What is the purpose of the Amazon Inspector agent?","To collect security data from EC2 instances","To configure network access control lists","To encrypt data at rest in S3","To manage IAM roles and policies","The Amazon Inspector agent is responsible for collecting security-related data from EC2 instances and sending it to the Inspector service for analysis."
"Which of the following is a supported operating system for the Amazon Inspector agent on EC2 instances?","Amazon Linux 2","Windows XP","macOS Mojave","Ubuntu 14.04","Amazon Linux 2 is a supported operating system for the Amazon Inspector agent."
"What type of vulnerability does Amazon Inspector commonly detect in container images?","Known software vulnerabilities in packages","Network configuration errors","Unauthorised IAM role access","Exposed API keys","Amazon Inspector identifies known software vulnerabilities in packages used in container images."
"How does Amazon Inspector assess the security of container images stored in Amazon ECR?","By scanning the image layers for vulnerabilities","By analysing the Dockerfile configuration","By monitoring container runtime activity","By inspecting network traffic from the container","Amazon Inspector scans the image layers of container images stored in ECR for known software vulnerabilities."
"What is the role of the Amazon Inspector assessment template?","To define the scope and rules for an assessment run","To store the results of an assessment run","To manage IAM permissions for Inspector","To configure network settings for EC2 instances","The assessment template defines the scope of the assessment, including the target resources and the rules packages to use."
"Which of the following is a common use case for Amazon Inspector?","Automating vulnerability assessments as part of a CI/CD pipeline","Monitoring network performance of EC2 instances","Managing IAM roles and permissions","Encrypting data at rest in S3 buckets","Amazon Inspector is commonly used to automate vulnerability assessments as part of a continuous integration and continuous deployment (CI/CD) pipeline."
"How can you schedule Amazon Inspector assessments to run automatically?","Using AWS CloudWatch Events (now EventBridge) to trigger assessment runs","Using AWS Lambda to invoke the Inspector API","Using AWS Config rules to evaluate resource configurations","Using AWS Step Functions to orchestrate assessment workflows","AWS CloudWatch Events (now EventBridge) can be used to schedule assessment runs based on a defined schedule."
"What type of data does the Amazon Inspector agent collect from EC2 instances?","System configuration information, software inventory, and application behaviour","Network traffic logs, CPU utilisation metrics, and memory usage statistics","IAM role assignments, security group rules, and VPC settings","S3 bucket access logs, CloudTrail events, and DNS query logs","The Amazon Inspector agent collects system configuration information, software inventory, and application behaviour data from EC2 instances."
"What are Amazon Inspector rules packages?","Collections of security rules that Inspector uses to assess resources","Sets of IAM permissions granted to Inspector","Templates for creating assessment reports","Configuration files for the Inspector agent","Amazon Inspector rules packages contain security rules that Inspector uses to evaluate resources for vulnerabilities and compliance issues."
"How does Amazon Inspector integrate with AWS Security Hub?","Inspector findings are automatically sent to Security Hub","Security Hub triggers Inspector assessments","Inspector uses Security Hub to manage IAM permissions","Security Hub provides dashboards for Inspector findings","Amazon Inspector findings are automatically sent to AWS Security Hub, providing a centralised view of security alerts and compliance status."
"What is the significance of the 'severity' level assigned to Amazon Inspector findings?","Indicates the potential impact of the vulnerability","Determines the cost of remediation efforts","Prioritises the order in which findings should be addressed","Classifies the type of vulnerability detected","The severity level indicates the potential impact of the vulnerability on the application or system."
"Which of the following is a key factor in determining the scope of an Amazon Inspector assessment?","The resources targeted by the assessment template","The number of EC2 instances in the AWS account","The geographic region where the resources are deployed","The IAM roles assigned to the EC2 instances","The scope of an Inspector assessment is determined by the resources targeted in the assessment template."
"How can you reduce the number of findings generated by Amazon Inspector?","By configuring the assessment template to exclude certain rules packages","By increasing the CPU resources allocated to the EC2 instances","By enabling encryption at rest for all S3 buckets","By implementing a strict password policy for IAM users","The number of findings can be reduced by configuring the assessment template to exclude rules packages that are not relevant to the specific environment or application."
"What type of reporting capabilities does Amazon Inspector offer?","Detailed reports on identified vulnerabilities and compliance issues","Real-time dashboards of network traffic patterns","Automated notifications of security events","Compliance reports for specific regulatory frameworks","Amazon Inspector provides detailed reports on identified vulnerabilities and compliance issues, including remediation recommendations."
"What is the relationship between Amazon Inspector and AWS Config?","Inspector focuses on runtime security, while Config focuses on configuration compliance","Inspector uses Config to manage IAM permissions","Config uses Inspector to detect vulnerabilities","Inspector and Config are mutually exclusive and cannot be used together","Amazon Inspector focuses on runtime security assessments, while AWS Config focuses on configuration compliance and change management."
"Which of the following is a key consideration when interpreting Amazon Inspector findings?","The context of the application and its potential impact on the business","The cost of remediating the identified vulnerabilities","The number of EC2 instances affected by the vulnerability","The geographic location of the affected resources","When interpreting Inspector findings, it's important to consider the context of the application and the potential impact of the vulnerability on the business."
"How can you integrate Amazon Inspector into a DevOps pipeline?","By automating assessment runs as part of the build and deployment process","By using Inspector to manage IAM roles for developers","By monitoring network traffic generated by development tools","By encrypting source code repositories with Inspector","Amazon Inspector can be integrated into a DevOps pipeline by automating assessment runs as part of the build and deployment process, providing continuous security feedback."
"What is the purpose of the Amazon Inspector 'rules' within a rules package?","To define the specific security checks performed during an assessment","To specify the target resources for an assessment","To configure the agent's behaviour on EC2 instances","To manage IAM permissions for the Inspector service","Rules within a rules package define the specific security checks performed by Amazon Inspector during an assessment."
"What is the relationship between Amazon Inspector and penetration testing?","Inspector provides automated vulnerability assessments, while penetration testing involves manual security testing","Inspector automates penetration testing","Penetration testing automates Inspector assessments","Inspector and penetration testing are mutually exclusive and cannot be used together","Amazon Inspector provides automated vulnerability assessments, while penetration testing involves manual security testing performed by security experts."
"What is the purpose of the Amazon Inspector API?","To programmatically manage and automate Inspector assessments","To directly access data stored in S3 buckets","To configure network settings for EC2 instances","To manage IAM roles and permissions for Inspector","The Amazon Inspector API allows users to programmatically manage and automate Inspector assessments, enabling integration with other tools and services."
"What type of resources can you exclude from an Amazon Inspector assessment?","Specific EC2 instances or container images based on tags or IDs","All resources in a specific AWS region","Resources that are already protected by a firewall","Resources that are not internet-facing","You can exclude specific EC2 instances or container images from an assessment based on tags or resource IDs."
"How does Amazon Inspector handle false positives?","Inspector provides mechanisms to suppress or ignore findings that are deemed false positives","Inspector automatically remediates false positives","Inspector never generates false positives","Inspector flags all findings as potential false positives","Amazon Inspector provides mechanisms to suppress or ignore findings that are determined to be false positives, allowing users to focus on the most critical issues."
"What is a common use case for Amazon Inspector in a containerised environment?","Identifying vulnerabilities in container images before deployment","Monitoring network traffic between containers","Managing IAM roles for container orchestration","Encrypting data stored within containers","Amazon Inspector is commonly used to identify vulnerabilities in container images before they are deployed to a container orchestration platform like Amazon ECS or Amazon EKS."
"What is the benefit of using Amazon Inspector with Amazon ECR?","Automated vulnerability scanning of container images stored in ECR","Simplified management of IAM permissions for ECR","Enhanced network security for ECR repositories","Reduced storage costs for container images in ECR","Amazon Inspector provides automated vulnerability scanning of container images stored in Amazon ECR, helping to ensure the security of containerised applications."
"How can you configure Amazon Inspector to scan for specific types of vulnerabilities?","By selecting specific rules packages in the assessment template","By manually configuring the Inspector agent on each EC2 instance","By modifying the default IAM role used by Inspector","By creating custom AWS Config rules for vulnerability detection","You can configure Amazon Inspector to scan for specific types of vulnerabilities by selecting the appropriate rules packages in the assessment template."
"What is the relationship between Amazon Inspector and AWS Trusted Advisor?","Inspector focuses on security vulnerabilities, while Trusted Advisor provides cost optimisation and performance recommendations","Inspector uses Trusted Advisor to manage IAM permissions","Trusted Advisor uses Inspector to detect security vulnerabilities","Inspector and Trusted Advisor are mutually exclusive and cannot be used together","Amazon Inspector focuses on security vulnerabilities, while AWS Trusted Advisor provides recommendations on cost optimisation, performance, and security."
"What is a 'session' in the context of Amazon Inspector?","A single execution of an assessment run","A period of time during which an EC2 instance is running","A connection between the Inspector agent and the Inspector service","A user's login session to the AWS Management Console","A session in the context of Amazon Inspector refers to a single execution of an assessment run."
"How can you use Amazon Inspector to improve your organisation's security posture?","By identifying and remediating vulnerabilities in EC2 instances and container images","By monitoring network traffic for malicious activity","By managing IAM roles and permissions more effectively","By encrypting data at rest in S3 buckets","Amazon Inspector helps improve security posture by identifying and remediating vulnerabilities in EC2 instances and container images."
"Which AWS service can you use to view and analyse Amazon Inspector findings?","AWS Security Hub","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Security Hub provides a centralised view of security alerts and compliance status across your AWS environment, including findings from Amazon Inspector."
"How does Amazon Inspector contribute to compliance efforts for PCI DSS?","By identifying vulnerabilities that could lead to a data breach","By encrypting data in transit and at rest","By managing user access to sensitive data","By monitoring network traffic for suspicious activity","Amazon Inspector helps organisations achieve PCI DSS compliance by identifying vulnerabilities that could potentially lead to a data breach of cardholder data."
"What is the difference between Amazon Inspector Standard assessment and Inspector Classic assessment?","Inspector Classic is deprecated and Standard is the current version with more features","Inspector Standard is for EC2 only and Inspector Classic is for containers only","Inspector Classic scans for infrastructure issues and Inspector Standard scans for software issues","There is no difference, the terms are interchangeable","Inspector Classic is the original version of Inspector which has been superseded by Inspector Standard, offering more features and broader coverage."
"Which of the following best describes the function of an Amazon Inspector assessment target?","It defines the scope of resources that Inspector will assess","It determines the severity level of findings","It defines the remediation steps for identified vulnerabilities","It sets the schedule for assessment runs","An assessment target in Amazon Inspector defines the scope of resources, such as EC2 instances or container images, that will be included in the assessment."
"What is the advantage of using Amazon Inspector's automatic re-assessment feature?","Ensures continuous monitoring for newly discovered vulnerabilities","Reduces the cost of assessment runs","Simplifies the configuration of assessment templates","Automates the remediation of findings","Automatic re-assessment ensures continuous monitoring of resources for newly discovered vulnerabilities, helping to maintain a strong security posture."
"If Amazon Inspector identifies a vulnerability in a third-party library, what information does it provide?","Details about the vulnerability, including its severity and potential impact","The cost of remediating the vulnerability","The steps to manually exploit the vulnerability","The contact information for the library's developers","Amazon Inspector provides detailed information about the vulnerability, including its severity, potential impact, and recommended remediation steps."
"What is the purpose of adding tags to EC2 instances and using them in Amazon Inspector assessment targets?","To dynamically include or exclude instances from assessments based on specific criteria","To improve the performance of assessment runs","To simplify the management of IAM permissions for Inspector","To automatically remediate vulnerabilities on tagged instances","Tags allow you to dynamically include or exclude EC2 instances from assessments based on specific criteria, making it easier to manage the scope of assessments."
"How can you leverage Amazon Inspector to continuously monitor the security of your AWS environment?","By scheduling regular assessment runs and integrating with AWS Security Hub","By manually reviewing Inspector findings on a daily basis","By enabling encryption at rest for all S3 buckets","By implementing a strict password policy for IAM users","Continuous monitoring can be achieved by scheduling regular assessment runs and integrating Amazon Inspector with AWS Security Hub for centralised security visibility."
"What is the purpose of the 'suppression rules' feature in Amazon Inspector?","To prevent specific findings from being generated in future assessments","To automatically remediate identified vulnerabilities","To encrypt data in transit between EC2 instances","To manage IAM permissions for Inspector service roles","Suppression rules allow you to prevent specific findings from being generated in future assessments, which is useful for addressing false positives or known issues that are not relevant to your environment."
"You receive an Amazon Inspector finding with a 'critical' severity level. What should be your immediate course of action?","Investigate the finding immediately and prioritise remediation efforts","Ignore the finding as it might be a false positive","Delete the affected resource to prevent potential exploitation","Disable Amazon Inspector to avoid further alerts","Findings with a 'critical' severity level should be investigated immediately and remediation efforts should be prioritised due to the potential for significant impact."
"When creating an Amazon Inspector assessment target, what is the purpose of specifying tags?","To filter and select specific resources for the assessment","To assign IAM roles to the targeted resources","To configure network access control lists for the targeted resources","To encrypt data at rest on the targeted resources","Specifying tags allows you to filter and select specific resources for the assessment based on defined criteria, such as environment, application, or owner."
"How does Amazon Inspector help reduce the risk of deploying vulnerable applications?","By identifying vulnerabilities early in the development lifecycle","By automatically remediating vulnerabilities before deployment","By encrypting application code at rest and in transit","By monitoring network traffic for suspicious activity after deployment","Amazon Inspector helps reduce the risk of deploying vulnerable applications by identifying vulnerabilities early in the development lifecycle, allowing developers to address them before deployment."
"What role does the Amazon Inspector agent play in the vulnerability assessment process?","It collects data from the EC2 instance and sends it to the Inspector service for analysis","It remediates the vulnerabilities identified by the Inspector service","It filters the network traffic to prevent attacks","It encrypts the data at rest on the EC2 instance","The Amazon Inspector agent is installed on EC2 instances and collects data about the operating system, installed software, and network configuration, sending this information to the Inspector service for analysis."
"Which Amazon Inspector feature helps to ensure that new EC2 instances are automatically assessed for vulnerabilities as soon as they are launched?","Automatic reassessment","Manual assessment","One-time assessment","Scheduled assessment","Amazon Inspector's automatic reassessment feature ensures that new EC2 instances are automatically assessed for vulnerabilities as soon as they are launched, without requiring manual intervention."
"When configuring an Amazon Inspector assessment, what is the purpose of choosing a specific rules package?","To define the types of security checks that will be performed","To specify the resources to be included in the assessment","To set the schedule for the assessment","To define the actions to be taken when vulnerabilities are found","Choosing a specific rules package determines the types of security checks that will be performed during the assessment, such as common vulnerabilities and exposures (CVEs) or security best practices."
"You want to ensure that your Amazon Inspector assessments only focus on vulnerabilities that have a high potential impact. How can you achieve this?","Configure the assessment to only include rules packages with high severity vulnerabilities","Manually review all findings and ignore low-severity issues","Enable automatic remediation of all vulnerabilities","Create a custom IAM role with limited permissions for Inspector","You can configure the assessment to only include rules packages that focus on high-severity vulnerabilities, reducing the number of findings and focusing on the most critical issues."
"What is the primary purpose of Amazon Inspector?","Automated security vulnerability management","Cost optimisation of EC2 instances","Performance monitoring of applications","Automated deployment of infrastructure","Amazon Inspector's primary purpose is to automate security vulnerability assessments and management for your AWS resources."
"Which of the following assessment types is NOT supported by Amazon Inspector?","Network Reachability","Malware Detection","Host-based Vulnerability Assessments","Security Best Practices","Malware Detection is not a standard assessment type offered directly within Amazon Inspector."
"What does an Amazon Inspector assessment template define?","The scope of the security assessment","The IAM role used by Inspector","The frequency of backups","The AWS region to be scanned","An assessment template in Amazon Inspector defines the scope of the assessment, including the targeted resources and the rules packages to be used."
"In Amazon Inspector, what is a 'finding'?","A potential security issue identified during an assessment","A summary of the total cost of running assessments","A list of compliant resources","A notification of a scheduled assessment","A finding in Amazon Inspector represents a potential security issue identified during an assessment, requiring further investigation."
"Which AWS service is commonly used to trigger Amazon Inspector assessments based on events?","Amazon CloudWatch Events (EventBridge)","AWS Config","AWS CloudTrail","Amazon SQS","Amazon CloudWatch Events (now EventBridge) can be configured to trigger Inspector assessments automatically based on defined event patterns."
"Which of the following is a key benefit of using Amazon Inspector?","Automated and continuous security assessments","Automated patching of vulnerabilities","Real-time threat detection across all AWS services","Simplified compliance reporting for GDPR","Amazon Inspector provides automated and continuous security assessments, reducing the manual effort required for vulnerability management."
"What type of resources can be assessed by Amazon Inspector?","EC2 instances and container images stored in Amazon ECR","S3 Buckets and Lambda functions","DynamoDB tables and RDS instances","IAM roles and policies","Amazon Inspector can assess EC2 instances and container images stored in Amazon ECR for vulnerabilities."
"What is the purpose of the 'Rules Packages' in Amazon Inspector?","To define the types of security checks performed during an assessment","To specify the target groups for an assessment","To configure the notification settings for findings","To manage the IAM permissions for Inspector","'Rules Packages' in Amazon Inspector contain the rules and checks that are performed during an assessment to identify vulnerabilities and security issues."
"When using Amazon Inspector, what is the relationship between a Target and an Assessment Template?","An Assessment Template is associated with a specific Target","A Target is associated with multiple Assessment Templates","They are independent of each other","A Target is automatically created from an Assessment Template","An Assessment Template is associated with a specific Target, defining the scope and rules for that target."
"Which of the following is a common use case for integrating Amazon Inspector with AWS Security Hub?","Centralised security findings management","Automated remediation of vulnerabilities","Compliance reporting for PCI DSS","Real-time threat intelligence sharing","Integrating Amazon Inspector with AWS Security Hub allows for centralised security findings management across multiple AWS services."
"How does Amazon Inspector help with compliance requirements?","By providing reports on identified vulnerabilities that can impact compliance","By automatically enforcing compliance policies","By encrypting data at rest and in transit","By providing a firewall to protect against attacks","Amazon Inspector helps with compliance requirements by identifying vulnerabilities and providing reports that can be used to demonstrate security posture and address compliance needs."
"Which action can you perform on Amazon Inspector findings?","Suppression","Automatic Remediation","Encryption","Archiving to S3","Amazon Inspector findings can be suppressed to reduce noise and focus on relevant security issues."
"What does Amazon Inspector use to perform security assessments on EC2 instances?","An agent installed on the instance","Network sniffing","Direct API calls to the instance's operating system","AWS CloudTrail logs","Amazon Inspector uses an agent installed on the EC2 instance to perform security assessments."
"Which AWS service can be used to automate remediation actions based on Amazon Inspector findings?","AWS Systems Manager","AWS CloudFormation","AWS Lambda","Amazon SQS","AWS Systems Manager can be used to automate remediation actions based on Inspector findings, such as patching vulnerabilities."
"What is the purpose of the Amazon Inspector Score?","To provide a risk rating for identified vulnerabilities","To track the cost of running assessments","To measure the performance of the Inspector agent","To compare the security posture of different AWS accounts","The Amazon Inspector Score provides a risk rating for identified vulnerabilities, helping prioritise remediation efforts."
"How does Amazon Inspector detect vulnerabilities in container images?","By scanning the container image layers for known vulnerabilities","By monitoring network traffic from the container","By analysing the application code within the container","By checking the container's IAM permissions","Amazon Inspector detects vulnerabilities in container images by scanning the image layers for known vulnerabilities in the base OS and installed packages."
"What level of access does the Amazon Inspector agent require on an EC2 instance?","Read and write access to all files","Root access","Read-only access to system logs and configuration files","Network access to all ports","The Amazon Inspector agent requires read-only access to system logs and configuration files to perform security assessments."
"Which of the following is an advantage of using Amazon Inspector over manual vulnerability scanning?","Automated and continuous assessments","Lower cost","More detailed findings","Support for a wider range of operating systems","Amazon Inspector provides automated and continuous assessments, reducing the manual effort and time required for vulnerability management."
"Which AWS Region is NOT supported by Amazon Inspector?","Africa (Cape Town)","US East (N. Virginia)","Europe (Ireland)","Asia Pacific (Tokyo)","Amazon Inspector is not available in every AWS region, you need to check the list of supported regions on the official documentation."
"How can you schedule recurring Amazon Inspector assessments?","Using CloudWatch Events (EventBridge) to trigger assessments periodically","Using the Inspector console to set a schedule","By setting a lifecycle policy on the assessment template","By configuring a cron job on the EC2 instance","Recurring assessments can be scheduled by using CloudWatch Events (EventBridge) to trigger assessments periodically."
"What is the purpose of the 'Exclusions' feature in Amazon Inspector?","To prevent specific findings from being reported","To exclude specific resources from the assessment scope","To disable specific rules packages","To exclude specific users from accessing Inspector","The 'Exclusions' feature in Amazon Inspector allows you to prevent specific findings from being reported, reducing noise and focusing on relevant issues."
"Which of the following is a limitation of Amazon Inspector?","It requires an agent to be installed on EC2 instances","It cannot assess container images stored in private registries","It only supports Linux-based EC2 instances","It cannot be integrated with AWS Security Hub","Amazon Inspector requires an agent to be installed on EC2 instances to perform host-based assessments."
"What is the correct IAM permission to allow a user to create an Amazon Inspector assessment template?","inspector:CreateAssessmentTemplate","inspector:RunAssessment","inspector:StartAssessment","inspector:ConfigureAssessment","`inspector:CreateAssessmentTemplate` is the specific IAM permission required to allow a user to create an assessment template."
"How does Amazon Inspector integrate with AWS Organizations?","To perform security assessments across multiple AWS accounts","To manage IAM roles for Inspector across accounts","To consolidate billing for Inspector across accounts","To share findings between accounts","Amazon Inspector can be used to perform security assessments across multiple AWS accounts within an AWS Organization, providing a centralised view of security posture."
"What type of information is NOT included in an Amazon Inspector finding?","Suggested remediation steps","The affected resource","The severity of the vulnerability","The estimated cost of remediation","The estimated cost of remediation is not typically included in an Amazon Inspector finding. Findings focus on the vulnerability, affected resource, severity, and suggested remediation steps."
"Which of the following is a valid Amazon Inspector assessment target?","An EC2 instance with a specific tag","An IAM role","An S3 bucket","A Lambda function","An EC2 instance with a specific tag can be a valid assessment target. Inspector uses tags to identify the resources to be included in the assessment."
"What is the difference between an 'Assessment Run' and an 'Assessment Template' in Amazon Inspector?","An Assessment Template defines the assessment configuration, while an Assessment Run is an execution of that template.","An Assessment Run defines the assessment configuration, while an Assessment Template is an execution of that run.","They are the same thing","An Assessment Template is used for cost optimisation, while an Assessment Run is for security.","An Assessment Template defines the scope and rules for an assessment, while an Assessment Run is the actual execution of that template."
"How does Amazon Inspector contribute to the 'Shift Left' security approach?","By enabling early detection of vulnerabilities during the development lifecycle","By automatically patching vulnerabilities in production","By providing real-time threat intelligence","By automating incident response","Amazon Inspector contributes to 'Shift Left' by enabling early detection of vulnerabilities during the development lifecycle, allowing for proactive remediation."
"What is the purpose of the Amazon Inspector 'Telemetry' feature?","To collect and analyse data about the Inspector agent's performance","To visualise security findings in a dashboard","To integrate with third-party SIEM tools","To collect network traffic data","Telemetry in Amazon Inspector is primarily used to collect and analyse data about the Inspector agent's performance and health."
"Which of the following actions cannot be automated directly from within the Amazon Inspector console?","Patching a vulnerability","Suppressing a finding","Scheduling an assessment","Creating an assessment target","Patching a vulnerability is not something you can do directly from the Amazon Inspector console, you'll need to use other services."
"You need to grant Amazon Inspector access to your EC2 instances. What IAM role do you need to create and attach to the instances?","AWSServiceRoleForAmazonInspectorEC2","AmazonInspectorFullAccess","AmazonEC2ReadOnlyAccess","AWSServiceRoleForEC2","The correct service-linked role is `AWSServiceRoleForAmazonInspectorEC2`, which allows Inspector to perform actions on your EC2 instances on your behalf."
"How does Amazon Inspector handle vulnerabilities found in third-party libraries included in your application?","It identifies and reports vulnerabilities based on known CVEs","It automatically updates the libraries to the latest version","It blocks the application from running","It ignores vulnerabilities in third-party libraries","Inspector identifies and reports vulnerabilities in third-party libraries based on known Common Vulnerabilities and Exposures (CVEs)."
"What is the maximum duration allowed for an Amazon Inspector assessment run?","12 hours","24 hours","72 hours","1 week","The maximum duration allowed for an Amazon Inspector assessment run is 12 hours."
"Which of the following best describes the principle of least privilege in the context of Amazon Inspector?","Granting the Inspector agent only the necessary permissions to perform its tasks","Enabling all available rules packages for the most comprehensive assessment","Running assessments as frequently as possible","Giving all users full access to Inspector data","Applying the principle of least privilege means granting the Inspector agent only the necessary permissions to perform its tasks, minimising the risk of unintended consequences."
"What is the impact of disabling the Amazon Inspector agent on an EC2 instance?","Inspector will no longer be able to perform assessments on that instance","The instance will be automatically terminated","The instance will be isolated from the network","All security groups associated with the instance will be removed","If the Inspector agent is disabled, Inspector will no longer be able to perform host-based assessments on that instance."
"Which of the following is NOT a standard report format available in Amazon Inspector?","PDF","CSV","JSON","XML","XML is not a standard report format offered directly within Amazon Inspector."
"How can you filter Amazon Inspector findings to focus on high-severity vulnerabilities?","By using the Inspector console to filter by severity level","By exporting findings to a spreadsheet and filtering there","By using AWS Config rules to filter findings","By creating a custom script to analyse the findings","You can filter Amazon Inspector findings directly in the Inspector console by severity level to focus on the most critical vulnerabilities."
"What is the relationship between Amazon Inspector and Amazon GuardDuty?","Inspector focuses on vulnerability management, while GuardDuty focuses on threat detection","Inspector and GuardDuty perform the same function","Inspector is a replacement for GuardDuty","GuardDuty is a prerequisite for using Inspector","Amazon Inspector focuses on vulnerability management by identifying potential security issues in your resources, while Amazon GuardDuty focuses on threat detection by monitoring for malicious activity and unauthorised behaviour."
"Which AWS service can be used to centrally manage and visualise Amazon Inspector findings across multiple AWS accounts?","AWS Security Hub","AWS CloudWatch","AWS Trusted Advisor","AWS Systems Manager","AWS Security Hub provides a centralised view of security findings across multiple AWS services, including Amazon Inspector, making it easier to manage and respond to security issues."
"What type of event can trigger an Amazon Inspector assessment run using CloudWatch Events (EventBridge)?","An EC2 instance launch","A change in an S3 bucket policy","A user login attempt","A DynamoDB table creation","An EC2 instance launch can trigger an Inspector assessment, allowing you to automatically assess newly deployed instances."
"What is a 'Rules Package' in Amazon Inspector?","A collection of security checks and rules used to assess a target","A pre-defined set of IAM permissions for Inspector","A list of excluded resources from an assessment","A schedule for running assessments","A 'Rules Package' in Amazon Inspector is a collection of security checks and rules used to assess a target for vulnerabilities and security issues."
"What is the purpose of the Amazon Inspector 'Suppression' feature?","To prevent specific findings from being displayed","To automatically remediate vulnerabilities","To encrypt findings","To delete findings","The 'Suppression' feature is used to prevent specific findings from being displayed, reducing noise and focusing on relevant issues."
"Which of the following can be used as an assessment target in Amazon Inspector?","A group of EC2 instances with a specific tag","An IAM user","An SQS queue","An SNS topic","A group of EC2 instances with a specific tag can be used as an assessment target, allowing you to assess multiple instances with similar configurations."
"How can you ensure that Amazon Inspector assessments are performed automatically whenever a new EC2 instance is launched?","By creating a CloudWatch Events (EventBridge) rule to trigger an assessment on instance launch","By configuring an AWS Config rule to trigger an assessment","By using AWS Systems Manager Automation to trigger an assessment","By scheduling the assessment in the Inspector console","A CloudWatch Events (EventBridge) rule can be configured to trigger an Inspector assessment whenever a new EC2 instance is launched, ensuring continuous security assessments."
"Which of the following is NOT a type of rule included in Amazon Inspector rules packages?","Network Reachability rules","Common Vulnerabilities and Exposures (CVE) rules","Security Best Practices rules","Data Loss Prevention (DLP) rules","Data Loss Prevention (DLP) rules are not a standard component of Amazon Inspector rules packages. Inspector focuses on network reachability, CVEs, and security best practices."
"What is the benefit of integrating Amazon Inspector with AWS Systems Manager Patch Manager?","To automate the patching of vulnerabilities identified by Inspector","To automate the creation of Inspector assessment templates","To automate the collection of Inspector findings","To automate the deployment of the Inspector agent","Integrating Amazon Inspector with AWS Systems Manager Patch Manager allows you to automate the patching of vulnerabilities identified by Inspector, improving your security posture."
"What is the significance of the CVSS score in Amazon Inspector findings?","It indicates the severity of the vulnerability","It indicates the cost of remediating the vulnerability","It indicates the number of affected resources","It indicates the date the vulnerability was discovered","The CVSS (Common Vulnerability Scoring System) score indicates the severity of the vulnerability, helping prioritise remediation efforts."
"In Amazon Inspector, what does a 'finding' represent?","A potential security issue or vulnerability","A compliance report","A performance bottleneck","A cost optimisation recommendation","A finding in Amazon Inspector signifies a potential security vulnerability or compliance issue detected in your assessed resources."
"What is the primary purpose of an Amazon Inspector assessment template?","To define the scope and rules for a security assessment","To create a snapshot of your infrastructure configuration","To automatically remediate security vulnerabilities","To monitor network traffic in real-time","An assessment template in Amazon Inspector is used to define the scope of your security assessment and the rules packages to use."
"Which AWS service can you integrate with Amazon Inspector to automate remediation actions?","AWS Systems Manager","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Systems Manager can be integrated with Amazon Inspector to automate remediation steps based on detected findings."
"What type of assessment is available with Amazon Inspector that checks for unintended network accessibility of EC2 instances?","Network Reachability assessment","Vulnerability assessment","Compliance assessment","Best Practices assessment","The Network Reachability assessment in Amazon Inspector specifically checks for unintended network accessibility of your EC2 instances."
"With Amazon Inspector, what is the purpose of defining a target?","To specify the resources to be assessed for vulnerabilities","To define the remediation steps for identified vulnerabilities","To define the reporting format for assessment results","To specify the IAM role used by Inspector","Defining a target in Amazon Inspector specifies which AWS resources (e.g., EC2 instances) should be assessed for security vulnerabilities."
"Which Amazon Inspector feature helps reduce the number of findings by grouping related issues?","Finding Suppression","Finding Export","Finding Reporting","Finding Aggregation","Finding Suppression in Amazon Inspector allows you to suppress findings that are not relevant to your environment, reducing noise and focusing on important issues."
"What is the benefit of using Amazon Inspector's 'rules packages' feature?","They provide pre-defined security checks and best practices","They automatically patch vulnerabilities","They automate infrastructure provisioning","They provide cost optimisation recommendations","Amazon Inspector's rules packages provide pre-defined security checks and best practices, making it easier to identify common vulnerabilities."
"Which of the following AWS resource types can be assessed using Amazon Inspector?","EC2 instances and container images in ECR","S3 buckets and Lambda functions","DynamoDB tables and RDS databases","CloudFront distributions and API Gateways","Amazon Inspector can assess EC2 instances and container images stored in ECR (Elastic Container Registry)."
"How does Amazon Inspector pricing work?","Pay-as-you-go, based on the number of assessments run and EC2 instances assessed","Fixed monthly fee per AWS account","Annual subscription based on the number of EC2 instances","Free for all AWS users","Amazon Inspector uses a pay-as-you-go pricing model, charging based on the number of assessments run and the EC2 instances assessed."
"What type of security information does Amazon Inspector utilise for vulnerability assessment?","Common Vulnerabilities and Exposures (CVE) database","AWS Trusted Advisor recommendations","AWS Config rules","AWS Security Hub findings","Amazon Inspector utilizes the Common Vulnerabilities and Exposures (CVE) database to identify known vulnerabilities in your assessed resources."
"What is the role of the Amazon Inspector Agent?","Collects telemetry data and performs security assessments on EC2 instances","Manages IAM roles for Inspector","Automates patching of vulnerabilities","Generates compliance reports","The Amazon Inspector Agent is installed on EC2 instances and collects telemetry data and performs security assessments."
"What is the benefit of integrating Amazon Inspector with AWS Security Hub?","Centralises security findings from multiple AWS services","Automates incident response workflows","Provides real-time threat intelligence feeds","Enables multi-factor authentication","Integrating Amazon Inspector with AWS Security Hub centralises security findings from multiple AWS services into a single pane of glass."
"Which type of Amazon Inspector assessment determines if your EC2 instances comply with security best practices?","Security Best Practices assessment","Network Reachability assessment","Vulnerability assessment","Compliance assessment","The Security Best Practices assessment in Amazon Inspector checks your EC2 instances against established security best practices."
"Which AWS service can be used to store and analyse Amazon Inspector findings?","Amazon S3","Amazon CloudWatch Logs","Amazon DynamoDB","Amazon VPC","Amazon S3 can be used to store and analyse Amazon Inspector findings, allowing for long-term data retention and analysis."
"What is the maximum assessment duration that can be configured in Amazon Inspector?","24 hours","12 hours","72 hours","48 hours","The maximum assessment duration that can be configured in Amazon Inspector is 24 hours."
"Which security standard can be assessed by Amazon Inspector?","CIS Benchmarks","ISO 27001","PCI DSS","SOC 2","Amazon Inspector allows you to assess resources against the CIS Benchmarks."
"Which of the following actions can be performed using the Amazon Inspector API?","Create and manage assessment templates","Launch EC2 instances","Configure network security groups","Manage IAM users","The Amazon Inspector API allows you to programmatically create and manage assessment templates, start assessments, and retrieve findings."
"What is the significance of the 'CVSS score' in Amazon Inspector findings?","Represents the severity of the identified vulnerability","Represents the cost to remediate the vulnerability","Represents the number of affected resources","Represents the time to remediate the vulnerability","The CVSS score in Amazon Inspector findings indicates the severity of the identified vulnerability."
"What is the purpose of Amazon Inspector's 'Exclusions' functionality?","To exclude specific findings from reports","To exclude specific resources from assessments","To exclude specific users from accessing Inspector","To exclude specific IP addresses from network reachability assessments","Amazon Inspector's 'Exclusions' functionality allows you to exclude specific resources from assessments."
"What is the first step in setting up Amazon Inspector?","Enabling Amazon Inspector in the AWS Management Console","Creating an assessment template","Installing the Inspector Agent on EC2 instances","Defining a target","The first step in setting up Amazon Inspector is enabling the service in the AWS Management Console."
"How does Amazon Inspector help with the AWS Shared Responsibility Model?","By identifying vulnerabilities in the customer's responsibility area","By managing AWS infrastructure security","By automating patching of AWS services","By providing compliance reports for AWS services","Amazon Inspector helps with the Shared Responsibility Model by identifying vulnerabilities in the customer's responsibility area, specifically the security of their EC2 instances and applications."
"What is the difference between a 'High' and 'Critical' severity finding in Amazon Inspector?","'Critical' indicates a more severe and immediately exploitable vulnerability","'High' findings require immediate attention, while 'Critical' findings do not","'High' findings are automatically remediated, while 'Critical' findings require manual remediation","There is no difference between 'High' and 'Critical'","A 'Critical' severity finding in Amazon Inspector indicates a more severe and immediately exploitable vulnerability compared to a 'High' severity finding."
"Which AWS service can be used to trigger Amazon Inspector assessments automatically based on events?","AWS CloudWatch Events (EventBridge)","AWS Lambda","AWS Step Functions","AWS Config","AWS CloudWatch Events (EventBridge) can be used to trigger Amazon Inspector assessments automatically based on events, such as the creation of a new EC2 instance."
"When using Amazon Inspector, which of the following is considered the assessment target?","EC2 instance(s) or container image(s) to be assessed","The IAM role used to perform the assessment","The S3 bucket where the assessment reports are stored","The region where the assessment is performed","The assessment target in Amazon Inspector refers to the EC2 instance(s) or container image(s) that will be assessed for vulnerabilities."
"Which action will improve the accuracy of findings within Amazon Inspector?","Ensuring the Inspector Agent is running and up to date","Using the default rule packages","Scheduling assessments to run infrequently","Disabling network reachability assessments","Ensuring the Inspector Agent is running and up-to-date allows it to collect the necessary telemetry data for accurate vulnerability assessments."
"What are the pre-defined assessment templates in Amazon Inspector designed to do?","To conduct different types of security assessments","To automatically remediate vulnerabilities","To optimise the cost of assessments","To restrict access to assessment results","The pre-defined assessment templates in Amazon Inspector are designed to conduct different types of security assessments, such as network reachability and common vulnerabilities and exposures."
"How does Amazon Inspector facilitate compliance auditing?","By providing reports that map findings to compliance standards","By automatically enforcing compliance policies","By encrypting data at rest","By managing user access control","Amazon Inspector facilitates compliance auditing by providing reports that map findings to specific compliance standards, helping you demonstrate adherence to regulatory requirements."
"In Amazon Inspector, what does 'Telemetry data' refer to?","Data collected from assessed resources, such as software versions and configurations","Data about network traffic patterns","Data about user activity on AWS","Data about AWS service usage","Telemetry data in Amazon Inspector refers to the data collected from assessed resources, such as software versions, configurations, and patch levels, which is used to identify vulnerabilities."
"What is the function of the Amazon Inspector Scan Type?","Determines the breadth of the assessment","Determines the resources to be assessed","Determines the time of day for assessment","Determines the severity of the findings","The Amazon Inspector Scan Type (e.g. network or host) determines the breadth of the assessment and what it will scan for."
"You want to ensure that every new EC2 instance launched in your AWS account is automatically assessed by Amazon Inspector. How can you achieve this?","Use AWS CloudWatch Events (EventBridge) to trigger an assessment on instance launch","Manually create an assessment for each new instance","Enable automatic patching","Create a scheduled assessment that runs daily","By using AWS CloudWatch Events (EventBridge) to trigger an assessment on instance launch, you can ensure that every new EC2 instance is automatically assessed by Amazon Inspector."
"Which security role does Amazon Inspector play in a cloud environment?","Vulnerability Management","Identity and Access Management","Cost Optimisation","Data Encryption","Amazon Inspector's primary role is Vulnerability Management, helping you identify and assess security vulnerabilities in your AWS resources."
"Which AWS service can be used to monitor Amazon Inspector's activity?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail can be used to monitor Amazon Inspector's activity, providing an audit trail of API calls and actions performed."
"When configuring an assessment in Amazon Inspector, what is the purpose of the 'Assessment Name'?","To provide a unique identifier for the assessment","To specify the IAM role used by the assessment","To define the schedule for the assessment","To define the scope of the assessment","The 'Assessment Name' in Amazon Inspector provides a unique identifier for the assessment, making it easier to manage and track."
"What is the benefit of using custom rules packages in Amazon Inspector?","To create security checks tailored to specific application requirements","To automatically patch vulnerabilities","To restrict access to assessment results","To optimise the cost of assessments","Custom rules packages in Amazon Inspector allow you to create security checks tailored to specific application requirements, providing more granular and targeted assessments."
"What is the purpose of 'finding attributes' in Amazon Inspector findings?","To provide additional context and information about the vulnerability","To define remediation steps for the vulnerability","To specify the severity of the vulnerability","To exclude the vulnerability from future assessments","Finding attributes in Amazon Inspector findings provide additional context and information about the vulnerability, such as the affected resource, the CVE ID, and the CVSS score."
"What does Amazon Inspector provide regarding compliance readiness?","Reports that detail compliance gaps related to security vulnerabilities","Tools to automatically enforce compliance policies","Encryption keys for data at rest","User authentication mechanisms","Amazon Inspector provides reports that detail compliance gaps related to security vulnerabilities, helping you understand your compliance posture and identify areas for improvement."
"How does Amazon Inspector contribute to proactive security management?","By continuously scanning for vulnerabilities and providing actionable findings","By automatically patching all identified vulnerabilities","By preventing unauthorised access to AWS resources","By optimising the cost of security services","Amazon Inspector contributes to proactive security management by continuously scanning for vulnerabilities and providing actionable findings, allowing you to address security issues before they can be exploited."
"You need to demonstrate to auditors that your EC2 instances are regularly scanned for vulnerabilities. How can you use Amazon Inspector to achieve this?","Schedule regular assessments and generate reports","Enable automatic patching","Restrict access to assessment results","Optimise the cost of assessments","By scheduling regular assessments and generating reports in Amazon Inspector, you can provide evidence to auditors that your EC2 instances are regularly scanned for vulnerabilities."
"What is one of the primary differences between Amazon Inspector and AWS Trusted Advisor?","Amazon Inspector focuses on security vulnerabilities, while Trusted Advisor focuses on best practices across multiple categories","Amazon Inspector automatically remediates vulnerabilities, while Trusted Advisor only provides recommendations","Amazon Inspector is free to use, while Trusted Advisor requires a subscription","Amazon Inspector is only for EC2 instances, while Trusted Advisor covers all AWS services","Amazon Inspector focuses specifically on identifying security vulnerabilities, while AWS Trusted Advisor provides recommendations across multiple categories, including cost optimisation, performance, and security."
"How can you reduce the cost of running Amazon Inspector assessments?","By narrowing the scope of the assessments to only critical resources","By running assessments less frequently","By disabling network reachability assessments","By using the default rule packages","You can reduce the cost of running Amazon Inspector assessments by narrowing the scope of the assessments to only critical resources, reducing the number of instances and rules checked."
"Which of the following is a limitation of Amazon Inspector?","It cannot assess resources in hybrid cloud environments","It cannot assess container images","It requires manual configuration of all security checks","It does not provide integration with other AWS security services","Amazon Inspector has limited ability to assess resources in hybrid cloud environments; it primarily focuses on AWS resources."
"What level of access is required to configure and run Amazon Inspector assessments?","Sufficient IAM permissions to access and configure Inspector and the target resources","Root account access","Administrator access to all AWS services","Read-only access to AWS resources","You need sufficient IAM permissions to access and configure Inspector and also the permissions to access the target resources you will be assessing."
"Which of the following is a key component of Amazon Inspector's workflow?","Defining Targets, Creating Assessment Templates, Running Assessments, Reviewing Findings","Creating Users, Defining Groups, Assigning Permissions, Monitoring Activity","Provisioning EC2 instances, Configuring Security Groups, Deploying Applications, Monitoring Performance","Backing up Data, Restoring Data, Encrypting Data, Archiving Data","The key components of Amazon Inspector's workflow involve defining assessment targets, creating assessment templates, running assessments based on those templates, and then reviewing the resulting findings."
"What output does Amazon Inspector generate following an assessment?","A detailed list of security findings with remediation recommendations","A cost optimisation report","A performance analysis report","A network traffic analysis report","Amazon Inspector generates a detailed list of security findings outlining identified vulnerabilities and providing recommendations for remediation."
"Which feature of Amazon Inspector helps prioritise security findings based on their potential impact?","Severity scoring (e.g., CVSS score)","Automated Remediation","Finding Suppression","Rules Package customisation","Amazon Inspector utilises severity scoring systems, such as CVSS, to help prioritise security findings based on their potential impact and exploitability."