"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Audit Manager, what is the primary purpose of a control?","To define the security requirements for a specific area of your business.","To manage user access to resources.","To monitor network traffic.","To encrypt data at rest.","Controls are used to define the security requirements for a specific area, such as data security or access control."
"Which of the following AWS services is NOT directly integrated with AWS Audit Manager for evidence collection?","AWS CloudTrail","AWS Config","Amazon GuardDuty","Amazon SQS","Amazon SQS is a messaging queue service and is not typically integrated with Audit Manager for evidence collection in the same way as CloudTrail, Config, and GuardDuty."
"In AWS Audit Manager, what is an assessment?","A collection of audit reports and evidence.","A single audit finding.","A configuration setting for a resource.","A dashboard for monitoring security events.","An assessment is a collection of audit reports and evidence gathered to evaluate compliance against a specific standard or regulation."
"What does an AWS Audit Manager framework represent?","A pre-built or custom compliance standard.","A list of IAM users with audit permissions.","A network security group configuration.","A set of CloudWatch alarms.","A framework represents a pre-built or custom compliance standard that Audit Manager uses to perform assessments."
"Which of the following is a key benefit of using AWS Audit Manager?","Automating evidence collection for audits.","Implementing network firewalls.","Managing AWS budgets.","Configuring multi-factor authentication.","Audit Manager automates the process of collecting evidence, making audits more efficient and less manual."
"Within AWS Audit Manager, what is the role of an assessment report?","To provide a summary of the assessment findings.","To configure AWS Config rules.","To manage IAM policies.","To set up CloudTrail logging.","The assessment report provides a summary of the assessment findings, including evidence and compliance status."
"What type of evidence can AWS Audit Manager automatically collect from AWS CloudTrail?","API activity logs.","Security group configurations.","EC2 instance types.","S3 bucket sizes.","Audit Manager can collect API activity logs from CloudTrail, providing insights into user actions and resource configurations."
"You are setting up an AWS Audit Manager assessment. What is the first step you should take?","Choose an appropriate framework.","Configure AWS Config rules.","Enable AWS CloudTrail.","Create an S3 bucket for storing reports.","Choosing an appropriate framework is the first step, as it defines the scope and requirements of the assessment."
"In AWS Audit Manager, what does the 'control set' contain?","A group of related controls focused on a specific area.","A list of IAM users assigned to an assessment.","A collection of AWS Config rules.","A set of CloudWatch dashboards.","A control set contains a group of related controls focused on a specific area of compliance, such as data security or access management."
"What is the benefit of using pre-built frameworks in AWS Audit Manager?","They provide a starting point for common compliance standards.","They automatically remediate non-compliant resources.","They provide real-time threat detection.","They automatically create IAM policies.","Pre-built frameworks provide a starting point for common compliance standards, saving time and effort in configuring assessments."
"How does AWS Audit Manager help with continuous monitoring?","By automating evidence collection and assessment.","By providing real-time threat intelligence.","By automatically patching EC2 instances.","By managing network traffic.","Audit Manager automates evidence collection and assessment, enabling continuous monitoring of compliance status."
"What type of customisation is allowed when using frameworks in AWS Audit Manager?","You can modify existing controls and create new ones.","You cannot modify pre-built frameworks.","You can only change the frequency of assessments.","You can only change the S3 bucket for storing reports.","You can modify existing controls and create new ones to tailor the framework to your specific requirements."
"What is the primary use case for tagging AWS resources in the context of AWS Audit Manager?","To help Audit Manager identify and include relevant resources in assessments.","To manage the cost allocation for different departments.","To improve the performance of EC2 instances.","To enhance the security of S3 buckets.","Tagging helps Audit Manager identify and include relevant resources in assessments, ensuring comprehensive coverage."
"How does AWS Audit Manager handle evidence from manual sources?","It allows you to upload and incorporate manual evidence.","It automatically ignores manual evidence.","It converts manual evidence into automated evidence.","It uses machine learning to generate evidence from text documents.","Audit Manager allows you to upload and incorporate manual evidence to supplement automated evidence."
"What is the purpose of the data residency setting in AWS Audit Manager?","To specify where assessment data is stored.","To configure the region for AWS Config rules.","To manage IAM user access.","To set up network firewalls.","The data residency setting specifies where assessment data is stored, helping you comply with data sovereignty requirements."
"Which AWS service is primarily used to collect user activity logs that are used as evidence in AWS Audit Manager?","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS IAM","AWS CloudTrail is the primary service used to collect user activity logs, which are then used as evidence in Audit Manager."
"How does AWS Audit Manager support compliance with regulations like GDPR?","By providing frameworks and controls aligned with GDPR requirements.","By automatically encrypting data at rest.","By managing user consent.","By preventing data breaches.","Audit Manager provides frameworks and controls aligned with GDPR requirements to help you assess and demonstrate compliance."
"When creating a custom control in AWS Audit Manager, what is the purpose of the 'testing information' field?","To document the steps to validate the control's effectiveness.","To configure AWS Config rules.","To define IAM permissions.","To specify the frequency of automated evidence collection.","The 'testing information' field is used to document the steps to validate the control's effectiveness."
"What is the role of AWS Organisations in the context of AWS Audit Manager?","To manage assessments across multiple AWS accounts.","To configure AWS Config rules across multiple accounts.","To manage IAM policies across multiple accounts.","To monitor network traffic across multiple accounts.","AWS Organisations allows you to manage assessments across multiple AWS accounts from a central location."
"What type of data can be collected as evidence from AWS Config in AWS Audit Manager?","Configuration changes to AWS resources.","API activity logs.","Security group configurations.","EC2 instance types.","AWS Config can collect data related to configuration changes to AWS resources, which can be used as evidence in Audit Manager."
"In AWS Audit Manager, what is the purpose of the 'delegation' feature?","To assign responsibility for completing assessment tasks to specific users.","To configure AWS Config rules.","To manage IAM permissions.","To set up network firewalls.","The 'delegation' feature allows you to assign responsibility for completing assessment tasks to specific users."
"What is a common use case for integrating AWS Audit Manager with other AWS services, such as AWS Security Hub?","To centralise security findings and compliance data.","To automate patching of EC2 instances.","To manage network traffic.","To encrypt data at rest.","Integration with services like Security Hub helps centralise security findings and compliance data for a unified view."
"You need to demonstrate compliance with a specific industry standard using AWS Audit Manager. What should you do first?","Select the corresponding pre-built framework.","Create a new custom framework.","Enable AWS CloudTrail in all regions.","Configure AWS Config rules for all resources.","Selecting the corresponding pre-built framework is the first step in demonstrating compliance with a specific industry standard."
"What is the benefit of creating custom frameworks in AWS Audit Manager?","To tailor assessments to specific business requirements.","To automatically remediate non-compliant resources.","To provide real-time threat detection.","To automatically create IAM policies.","Creating custom frameworks allows you to tailor assessments to specific business requirements that are not covered by pre-built frameworks."
"How can you use AWS Audit Manager to track the remediation of findings?","By integrating with AWS Security Hub or other ticketing systems.","By automatically patching EC2 instances.","By managing network traffic.","By encrypting data at rest.","Integrating with Security Hub or other ticketing systems allows you to track the remediation of findings identified during assessments."
"In AWS Audit Manager, what is the purpose of the 'evidence finder' feature?","To quickly locate specific pieces of evidence.","To configure AWS Config rules.","To manage IAM permissions.","To set up network firewalls.","The 'evidence finder' feature helps you quickly locate specific pieces of evidence within an assessment."
"What type of report can AWS Audit Manager generate to demonstrate compliance?","Assessment reports containing collected evidence and compliance status.","Network traffic analysis reports.","Cost optimisation reports.","IAM policy review reports.","Audit Manager generates assessment reports containing collected evidence and compliance status to demonstrate compliance."
"How does AWS Audit Manager help reduce the manual effort associated with audits?","By automating evidence collection and organisation.","By providing real-time threat intelligence.","By automatically patching EC2 instances.","By managing network traffic.","Audit Manager automates evidence collection and organisation, significantly reducing the manual effort associated with audits."
"What is a potential drawback of relying solely on automated evidence collection in AWS Audit Manager?","It may not capture all relevant information for demonstrating compliance.","It is more expensive than manual evidence collection.","It is less accurate than manual evidence collection.","It requires more technical expertise than manual evidence collection.","Automated evidence collection may not capture all relevant information, especially for controls that require manual processes or documentation."
"In AWS Audit Manager, what is the purpose of the 'lifecycle' of an assessment?","To manage the different stages of the assessment process.","To configure AWS Config rules.","To manage IAM permissions.","To set up network firewalls.","The 'lifecycle' of an assessment manages the different stages of the assessment process, such as planning, evidence collection, and reporting."
"What is the relationship between AWS Audit Manager and AWS Control Tower?","Audit Manager can be used to assess compliance with Control Tower's guardrails.","Audit Manager replaces Control Tower's functionality.","Control Tower replaces Audit Manager's functionality.","They are unrelated services.","Audit Manager can be used to assess compliance with Control Tower's guardrails, providing a way to validate that your multi-account environment adheres to security best practices."
"How can you share assessment reports generated by AWS Audit Manager with external auditors?","By granting them access to the S3 bucket where the reports are stored.","By emailing them the reports directly.","By creating temporary IAM users for them.","By giving them access to the Audit Manager console.","Granting external auditors access to the S3 bucket where the reports are stored is a common way to share assessment reports."
"What is the maximum number of custom frameworks you can create in AWS Audit Manager?","The number is unlimited.","5","10","20","AWS does not limit the amount of custom frameworks you can create in AWS Audit Manager."
"Which of the following is NOT a type of evidence that AWS Audit Manager can collect?","Security group rules","Code commit history","EC2 instance types","IAM policy documents","Code commit history can be collected with other AWS services but is not typically handled as direct evidence by AWS Audit Manager."
"When you delete an AWS Audit Manager assessment, what happens to the collected evidence?","The evidence remains stored in your designated S3 bucket.","The evidence is automatically deleted.","The evidence is moved to Glacier.","The evidence is encrypted with a new key.","When you delete an assessment, the collected evidence remains stored in your designated S3 bucket."
"You want to ensure that your AWS Audit Manager assessment only includes resources that are tagged with a specific key-value pair. How do you achieve this?","By specifying the tag key-value pair in the assessment's scope.","By configuring AWS Config rules to only evaluate tagged resources.","By setting up CloudWatch alarms to monitor tagged resources.","By creating a custom IAM policy that restricts access to tagged resources.","Specifying the tag key-value pair in the assessment's scope ensures that only resources with the specified tag are included."
"What is the purpose of the 'assessment template' feature in AWS Audit Manager?","To define the common settings for multiple assessments.","To configure AWS Config rules.","To manage IAM permissions.","To set up network firewalls.","Assessment templates allow you to define the common settings for multiple assessments, ensuring consistency and reducing configuration effort."
"Which AWS service can be used to send notifications based on the status of your AWS Audit Manager assessments?","Amazon SNS","Amazon SQS","AWS Lambda","AWS CloudWatch Events","Amazon SNS (Simple Notification Service) can be used to send notifications based on the status of your Audit Manager assessments."
"How can you ensure that your AWS Audit Manager assessments are aligned with the latest regulatory requirements?","By regularly updating the frameworks and controls.","By enabling automatic updates in Audit Manager.","By subscribing to AWS Security Hub notifications.","By attending AWS re:Invent every year.","Regularly updating the frameworks and controls ensures that your assessments are aligned with the latest regulatory requirements."
"What is the best practice for storing AWS Audit Manager assessment reports?","Store them in an S3 bucket with appropriate access controls.","Store them in an EBS volume attached to an EC2 instance.","Store them in AWS CodeCommit.","Store them in Amazon DynamoDB.","Storing assessment reports in an S3 bucket with appropriate access controls ensures durability, availability, and security."
"What is the purpose of the 'resource selection' criteria when configuring an AWS Audit Manager assessment?","To define which AWS resources are included in the assessment.","To configure AWS Config rules.","To manage IAM permissions.","To set up network firewalls.","The 'resource selection' criteria defines which AWS resources are included in the assessment, ensuring that the relevant resources are evaluated for compliance."
"In AWS Audit Manager, what is the difference between a 'standard' framework and a 'custom' framework?","A standard framework is pre-built by AWS, while a custom framework is created by the user.","A standard framework automatically remediates non-compliant resources, while a custom framework does not.","A standard framework provides real-time threat detection, while a custom framework does not.","A standard framework automatically creates IAM policies, while a custom framework does not.","A standard framework is pre-built by AWS, while a custom framework is created by the user to meet specific business requirements."
"Which of the following actions CANNOT be performed through the AWS Audit Manager console?","Automate the remediation of non-compliant findings.","Create and customise frameworks.","Generate assessment reports.","Delegate assessment tasks.","AWS Audit Manager does not automatically remediate the finding for you but relies on other services and your own resources to take remediation actions."
"How can AWS Audit Manager help in the process of obtaining and maintaining industry certifications like SOC 2 or ISO 27001?","By automating evidence collection and providing pre-built frameworks aligned with certification requirements.","By providing real-time threat intelligence.","By automatically patching EC2 instances.","By managing network traffic.","Audit Manager helps in obtaining and maintaining certifications by automating evidence collection and providing pre-built frameworks aligned with certification requirements."
"What is the role of the 'delegated administrator' in AWS Audit Manager in the context of an AWS Organisation?","The delegated administrator can manage Audit Manager assessments for all accounts within the organisation.","The delegated administrator can manage IAM permissions for all accounts within the organisation.","The delegated administrator can manage network traffic for all accounts within the organisation.","The delegated administrator can manage AWS Config rules for all accounts within the organisation.","The delegated administrator can manage Audit Manager assessments for all accounts within the organisation, providing a centralised point of control."
"Which AWS service is most commonly used to trigger workflows or actions based on AWS Audit Manager findings?","AWS Lambda","AWS Step Functions","Amazon SQS","Amazon SNS","AWS Lambda is most commonly used to trigger workflows or actions based on Audit Manager findings, enabling automated remediation or notification processes."
"How can you use AWS Audit Manager to assess the security posture of your serverless applications?","By including the relevant AWS Lambda functions, API Gateway endpoints, and other serverless resources in the assessment scope.","By configuring AWS Config rules to evaluate the security configurations of serverless resources.","By using AWS Security Hub to identify security vulnerabilities in serverless applications.","By creating custom IAM policies to restrict access to serverless resources.","Including the relevant serverless resources in the assessment scope allows Audit Manager to evaluate their security posture based on the selected framework and controls."
"In AWS Audit Manager, what is the primary purpose of a control?","To define the preventative or detective measure in place to meet a requirement.","To grant permissions to AWS resources.","To define the infrastructure code for your application.","To create a CloudWatch alarm.","Controls in Audit Manager represent the measures in place to satisfy compliance requirements, whether preventative or detective."
"Which AWS service is directly integrated with AWS Audit Manager for evidence collection from AWS resources?","AWS Config","Amazon CloudWatch","AWS CloudTrail","Amazon Inspector","AWS Config is a key integration for Audit Manager, as it provides configuration details and change history, serving as important evidence."
"In AWS Audit Manager, what is an assessment?","A collection of AWS resources being audited for compliance.","A single security group configuration.","A scheduled Lambda function execution.","A set of IAM permissions.","An assessment in Audit Manager is a collection of resources being audited, often related to a specific compliance standard or regulation."
"When setting up an AWS Audit Manager assessment, what is the purpose of the assessment template?","To predefine the scope, controls, and evidence collection settings.","To create a custom IAM role for the assessment.","To define the networking configuration for the assessment.","To specify the S3 bucket for assessment reports.","Assessment templates provide a structured way to define the scope, controls, and settings for an audit, streamlining the setup process."
"Which of the following is a benefit of using AWS Audit Manager?","Automates evidence collection to reduce manual effort.","Automatically remediates non-compliant resources.","Encrypts data in transit using TLS 1.3.","Eliminates the need for IAM roles.","Audit Manager automates much of the evidence collection process, saving time and effort compared to manual audits."
"In AWS Audit Manager, what is the purpose of frameworks?","To provide prebuilt collections of controls mapped to specific compliance standards.","To define the infrastructure as code.","To visualise security findings.","To manage IAM users.","Frameworks in Audit Manager are prebuilt collections of controls aligned with specific compliance standards, offering a starting point for assessments."
"How does AWS Audit Manager help with continuous compliance?","By continuously collecting evidence and monitoring control performance.","By automatically applying security patches.","By automatically creating AWS Lambda functions.","By deleting unused AWS resources.","Audit Manager continuously collects evidence and monitors control performance, providing ongoing insights into compliance posture."
"What type of evidence does AWS Audit Manager collect?","Configuration data, user activity logs, and compliance check results.","Network traffic captures.","Operating system patches.","Application code.","Audit Manager collects a variety of evidence, including configuration details, user activity logs, and results from compliance checks."
"In AWS Audit Manager, what is a custom control?","A control you define yourself to meet specific compliance requirements.","A control automatically created by AWS.","A control managed by AWS Config.","A control specific to AWS Lambda.","Custom controls allow you to tailor your audit to meet specific, unique compliance requirements beyond the standard frameworks."
"When generating a report in AWS Audit Manager, what information is included?","Evidence collected, control performance, and compliance status.","VPC configuration.","List of IAM users.","CloudWatch metrics.","Reports in Audit Manager consolidate evidence, control performance data, and overall compliance status for the audited resources."
"What is the role of the AWS Audit Manager delegated administrator?","To manage assessments, frameworks, and other Audit Manager configurations.","To manage IAM roles and permissions.","To manage S3 buckets.","To manage CloudTrail logs.","The delegated administrator role in Audit Manager allows a user to manage assessments, frameworks, and other Audit Manager-specific configurations."
"Which security best practice does AWS Audit Manager help implement?","Automated compliance monitoring and reporting.","DDoS attack mitigation.","Penetration testing.","Vulnerability scanning.","Audit Manager aids in automated compliance monitoring and reporting, reducing manual effort and improving accuracy."
"What is the relationship between AWS Audit Manager and AWS CloudTrail?","Audit Manager uses CloudTrail logs as a source of evidence.","Audit Manager disables CloudTrail logs.","CloudTrail is not used by Audit Manager.","Audit Manager manages CloudTrail log groups.","Audit Manager leverages CloudTrail logs to gather evidence about user activity and API calls for auditing purposes."
"You need to demonstrate compliance with PCI DSS. How can AWS Audit Manager help?","By providing a prebuilt PCI DSS framework with associated controls.","By automatically encrypting data at rest.","By automatically blocking malicious traffic.","By automatically backing up your databases.","Audit Manager offers prebuilt frameworks aligned with compliance standards like PCI DSS, streamlining the auditing process."
"What is the primary function of the AWS Audit Manager API?","To automate assessment creation, evidence collection, and report generation.","To manage IAM users and roles.","To manage S3 buckets and objects.","To manage EC2 instances.","The Audit Manager API allows for programmatic management of assessments, evidence collection, and report generation, enabling automation."
"In AWS Audit Manager, what is the 'scope' of an assessment?","The AWS resources and accounts included in the audit.","The IAM permissions assigned to the assessment.","The duration of the assessment.","The cost of the assessment.","The scope defines which AWS resources and accounts are being audited within the assessment."
"What is the benefit of using AWS Audit Manager with AWS Organizations?","Centralised management of compliance across multiple AWS accounts.","Automatic enforcement of IAM policies across all accounts.","Automatic scaling of EC2 instances across all accounts.","Automatic cost optimisation across all accounts.","Using Audit Manager with AWS Organizations allows for centralised compliance management across multiple AWS accounts, providing a unified view."
"How does AWS Audit Manager ensure the integrity of the collected evidence?","By digitally signing the evidence and storing it securely.","By encrypting data in transit.","By applying access control lists to the evidence.","By storing evidence in RAM.","Audit Manager digitally signs the collected evidence to ensure its integrity and stores it securely to prevent tampering."
"Which of the following is a key step when customising an AWS Audit Manager assessment template?","Modifying the controls to align with your specific requirements.","Configuring the EC2 instance type for the assessment.","Selecting the AWS region for the assessment.","Defining the VPC configuration for the assessment.","Customising an assessment template often involves modifying the controls to better align with your unique compliance needs."
"When should you consider using custom controls in AWS Audit Manager?","When the prebuilt controls do not fully address your compliance requirements.","When you need to reduce the cost of an assessment.","When you need to increase the speed of an assessment.","When you need to integrate with AWS Lambda.","Custom controls should be used when the prebuilt controls don't fully meet the specific compliance requirements of your organisation."
"Which of the following AWS services can provide evidence for an AWS Audit Manager assessment related to network security?","AWS Config and Amazon VPC Flow Logs.","Amazon S3.","Amazon EC2.","AWS Lambda.","AWS Config provides configuration details, and VPC Flow Logs provides network traffic data, both useful for network security assessments."
"In AWS Audit Manager, what is the role of control objectives?","To define the desired outcome of a control.","To define the infrastructure code for your application.","To define the IAM permissions for your users.","To define the cost of your resources.","Control objectives define the intended outcome or goal of a control, clarifying what the control is meant to achieve."
"You want to automate the creation of AWS Audit Manager assessments. Which AWS service would you use?","AWS CloudFormation or AWS SDK.","Amazon CloudWatch.","AWS IAM.","Amazon S3.","AWS CloudFormation or the AWS SDK can be used to automate the creation and management of Audit Manager assessments."
"What is the significance of tagging AWS resources when using AWS Audit Manager?","Tags help Audit Manager identify and group resources for assessments.","Tags automatically encrypt data at rest.","Tags automatically back up resources.","Tags automatically apply security patches.","Tags are essential for identifying and grouping resources, allowing Audit Manager to accurately scope assessments."
"How can you use AWS Audit Manager to demonstrate compliance to an external auditor?","By generating a comprehensive audit report with collected evidence.","By providing direct access to the Audit Manager console.","By creating a read-only IAM role for the auditor.","By automatically remediating non-compliant resources.","Generating a detailed audit report with collected evidence is the primary way to demonstrate compliance to external auditors using Audit Manager."
"In AWS Audit Manager, what is the difference between preventative and detective controls?","Preventative controls prevent incidents, while detective controls identify incidents after they occur.","Preventative controls are automated, while detective controls are manual.","Preventative controls are cheaper, while detective controls are more expensive.","Preventative controls are managed by AWS, while detective controls are managed by the user.","Preventative controls aim to prevent security incidents from happening, while detective controls are designed to identify incidents that have already occurred."
"What is the function of 'data source' in AWS Audit Manager?","The AWS service from which Audit Manager collects evidence.","The S3 bucket where assessment reports are stored.","The IAM role used by Audit Manager.","The KMS key used to encrypt data.","The 'data source' refers to the specific AWS service (e.g., AWS Config, CloudTrail) from which Audit Manager collects evidence."
"How does AWS Audit Manager support GDPR compliance?","By providing a framework with controls mapped to GDPR requirements.","By automatically encrypting personal data.","By automatically deleting personal data.","By automatically obtaining consent from users.","Audit Manager offers frameworks that align with GDPR requirements, helping you assess and demonstrate compliance."
"You need to ensure that your AWS Audit Manager data is encrypted at rest. What should you do?","Use an AWS KMS key to encrypt the S3 bucket where Audit Manager stores data.","Enable server-side encryption on your EC2 instances.","Enable encryption on your RDS instances.","Enable encryption on your VPC.","Encrypting the S3 bucket where Audit Manager data is stored using a KMS key ensures encryption at rest."
"Which of the following is a key consideration when planning an AWS Audit Manager assessment?","Defining the scope of the assessment and selecting the appropriate framework.","Configuring the network settings for the assessment.","Selecting the EC2 instance type for the assessment.","Choosing the AWS region for the assessment.","Defining the scope (resources, accounts) and selecting a relevant framework are crucial for a successful Audit Manager assessment."
"What is the purpose of the AWS Audit Manager console?","To configure and manage assessments, frameworks, and reports.","To monitor EC2 instance performance.","To manage IAM users and roles.","To manage S3 buckets and objects.","The Audit Manager console provides a central interface for configuring and managing assessments, frameworks, and reports."
"How does AWS Audit Manager integrate with other AWS security services?","By using services like AWS Config and CloudTrail to collect evidence.","By replacing other AWS security services.","By disabling other AWS security services.","By managing IAM roles for other AWS security services.","Audit Manager integrates with other AWS security services like Config and CloudTrail to gather data and evidence for compliance."
"What happens to evidence collected by AWS Audit Manager when an assessment is complete?","It is stored securely and can be used for future audits.","It is automatically deleted.","It is moved to Glacier storage.","It is sent to Amazon S3.","Evidence collected by Audit Manager is securely stored and available for use in future audits, providing a historical record of compliance."
"You need to provide an auditor with read-only access to an AWS Audit Manager assessment. What is the best approach?","Create a custom IAM role with read-only permissions to Audit Manager.","Share your AWS account credentials with the auditor.","Grant the auditor full administrative access to AWS.","Provide the auditor with the KMS key used to encrypt the data.","Creating a custom IAM role with limited, read-only permissions is the most secure way to provide auditor access."
"What is the relationship between AWS Audit Manager and compliance standards like ISO 27001?","Audit Manager provides frameworks mapped to the controls in ISO 27001.","Audit Manager automatically enforces ISO 27001 compliance.","Audit Manager replaces the need for ISO 27001 certification.","Audit Manager is not related to ISO 27001.","Audit Manager provides prebuilt frameworks mapped to the controls within various compliance standards, including ISO 27001, facilitating compliance efforts."
"When customising an AWS Audit Manager control, what aspects can you modify?","Control description, testing procedures, and data sources.","The AWS region where the control is applied.","The EC2 instance type used by the control.","The IAM permissions associated with the control.","When customising controls, you can modify elements like the control description, testing procedures, and the data sources used for evidence collection."
"What is a control objective in AWS Audit Manager?","A specific goal or requirement that a control is designed to meet.","A list of AWS resources being audited.","A schedule for running assessments.","A type of IAM permission.","A control objective represents a specific goal or requirement that a control is intended to fulfil, guiding its implementation and assessment."
"What AWS service provides information to AWS Audit Manager about changes made to AWS resources?","AWS Config.","Amazon CloudWatch.","AWS CloudTrail.","Amazon S3.","AWS Config provides continuous monitoring of AWS resource configurations and changes, which is valuable information for Audit Manager."
"In AWS Audit Manager, what is the purpose of the 'assessment status'?","To indicate the progress and state of an assessment.","To indicate the security status of AWS resources.","To indicate the cost of an assessment.","To indicate the IAM permissions assigned to an assessment.","The assessment status indicates the current progress and overall state of the assessment (e.g., active, in progress, complete)."
"Which AWS service can be used to trigger an AWS Audit Manager assessment based on an event?","AWS CloudWatch Events (EventBridge).","AWS IAM.","Amazon S3.","AWS Lambda.","AWS CloudWatch Events (now EventBridge) can be used to trigger an Audit Manager assessment based on a specific event, enabling automated auditing."
"What is the key difference between a standard framework and a custom framework in AWS Audit Manager?","Standard frameworks are predefined by AWS, while custom frameworks are created by the user.","Standard frameworks are cheaper, while custom frameworks are more expensive.","Standard frameworks are managed by AWS, while custom frameworks are managed by the user.","Standard frameworks are more secure, while custom frameworks are less secure.","Standard frameworks are prebuilt by AWS and aligned with common compliance standards, while custom frameworks are user-defined to address specific requirements."
"How can you use AWS Audit Manager to track the remediation of non-compliant resources?","By monitoring the control performance and evidence collection over time.","By automatically patching non-compliant resources.","By automatically deleting non-compliant resources.","By automatically creating IAM roles for non-compliant resources.","Audit Manager allows you to monitor control performance and evidence collection, enabling you to track the remediation progress of non-compliant resources."
"When should you consider using the 'grouping' feature in AWS Audit Manager?","To organise controls based on a specific criteria.","To encrypt data at rest.","To manage IAM users and roles.","To manage S3 buckets and objects.","The 'grouping' feature in Audit Manager allows you to organise controls based on specific criteria, improving the structure and manageability of your assessments."
"What type of data can be used as evidence in an AWS Audit Manager assessment?","AWS Config configuration history, CloudTrail logs, and manual uploads.","Network traffic captures.","Operating system patches.","Application code.","Evidence in Audit Manager can include a variety of data sources, such as AWS Config configuration history, CloudTrail logs, and manually uploaded documents."
"Which of the following actions can you perform after an AWS Audit Manager assessment is completed?","Generate a report and share it with auditors.","Automatically remediate non-compliant resources.","Automatically delete non-compliant resources.","Automatically create IAM roles for non-compliant resources.","After an assessment is complete, you can generate a report to document the findings and share it with auditors or other stakeholders."
"You are setting up AWS Audit Manager for the first time. What is the first step you should take?","Enable AWS Config and AWS CloudTrail in the AWS account.","Create a new IAM user.","Create an S3 bucket.","Create a new VPC.","Ensuring that AWS Config and CloudTrail are enabled is crucial, as these services provide the data sources for Audit Manager's evidence collection."
"How does AWS Audit Manager help improve audit readiness?","By automating evidence collection and providing continuous monitoring.","By automatically patching vulnerabilities.","By automatically backing up data.","By automatically scaling resources.","Audit Manager improves audit readiness by automating evidence collection and providing continuous monitoring of compliance posture."
"When creating a custom framework in AWS Audit Manager, what is a key consideration for designing the controls?","Ensuring the controls align with the specific compliance requirements.","Ensuring the controls are cheap.","Ensuring the controls are easy to implement.","Ensuring the controls are managed by AWS.","When designing custom controls, it's essential to ensure they directly address the specific requirements of the compliance standard you're targeting."
"How can you use AWS Audit Manager to monitor the effectiveness of your security controls?","By tracking control performance and identifying deviations from expected results.","By automatically patching vulnerabilities.","By automatically backing up data.","By automatically scaling resources.","Audit Manager allows you to monitor control performance and identify deviations, giving you insight into the effectiveness of your security controls."
"In AWS Audit Manager, what is the primary function of a control?","To define the security and compliance requirements for a specific area","To encrypt data at rest","To monitor network traffic","To manage user permissions","Controls represent the security and compliance requirements that you want to assess and enforce."
"Which of the following AWS services is LEAST integrated with AWS Audit Manager for evidence collection?","AWS Config","AWS CloudTrail","Amazon S3","AWS Lambda","While Lambda can be used indirectly, it isn't a direct data source for Audit Manager's pre-built data collection capabilities."
"What is an assessment in AWS Audit Manager?","A collection of controls and data sources to evaluate compliance","A snapshot of your AWS infrastructure","A tool for encrypting data","A service for managing IAM roles","An assessment in Audit Manager is a project that gathers evidence to help you demonstrate compliance with specific regulations or standards."
"Within AWS Audit Manager, what is the purpose of a framework?","To provide a pre-built collection of controls and data sources aligned to a specific compliance standard","To define custom security policies","To manage user access permissions","To monitor real-time security threats","Frameworks in Audit Manager offer pre-built structures aligned with common compliance standards, saving you time in setting up assessments."
"What does AWS Audit Manager use to automatically collect evidence?","Data source integrations with other AWS services","Manual uploads of documents","Third-party security tools","Periodic penetration tests","Audit Manager leverages integrations with services like CloudTrail, Config, and Security Hub to automatically gather evidence."
"In AWS Audit Manager, what is the role of the assessment report?","To provide a summary of the assessment results and collected evidence","To trigger automatic remediation actions","To configure security policies","To generate compliance documentation for external auditors","The assessment report consolidates the findings and evidence collected during the assessment, enabling you to review compliance status and generate reports."
"Which of the following is a supported data source for AWS Audit Manager's automated evidence collection?","AWS Config","Microsoft Active Directory","Salesforce","GitHub","AWS Config provides configuration details that can be used as evidence in Audit Manager assessments."
"When configuring an AWS Audit Manager assessment, what does the term 'delegation' refer to?","Assigning responsibility for reviewing and remediating control failures to specific users","Granting access to external auditors","Configuring data source integrations","Setting up automated workflows","Delegation allows you to assign tasks related to the assessment to relevant personnel for review and remediation."
"What is the benefit of using custom controls in AWS Audit Manager?","They allow you to address specific compliance requirements not covered by pre-built frameworks","They provide real-time threat detection","They automatically encrypt data at rest","They optimise AWS resource costs","Custom controls provide flexibility to tailor your assessments to unique requirements or internal policies."
"What is the purpose of evidence finder in AWS Audit Manager?","Allows searching and filtering through collected evidence based on various attributes","To identify potential security vulnerabilities","To automatically generate compliance reports","To configure data source integrations","Evidence finder allows efficient filtering and searching of collected evidence based on attributes like resource ID, control ID, or time range."
"How does AWS Audit Manager help with continuous compliance?","By automating evidence collection and assessment, enabling ongoing monitoring of compliance posture","By providing vulnerability scanning services","By automatically patching operating systems","By performing penetration testing on a regular basis","Audit Manager helps with continuous compliance by automating the collection and assessment of evidence, allowing for ongoing monitoring."
"Which AWS service provides the underlying configuration data used by AWS Audit Manager to assess compliance?","AWS Config","AWS CloudWatch","AWS IAM","AWS Trusted Advisor","AWS Config continuously monitors and records AWS resource configurations, providing the data needed for compliance assessments."
"When creating an AWS Audit Manager assessment, what does the assessment template define?","The scope of the assessment, including the controls and data sources used","The level of encryption applied to the collected evidence","The geographical region where the assessment will be performed","The budget allocated for the assessment","The assessment template predefines the controls, data sources, and other settings for the assessment, streamlining the setup process."
"What is the relationship between AWS Audit Manager and AWS Security Hub?","Audit Manager can use findings from Security Hub as evidence for compliance assessments","Security Hub automatically remediates compliance violations detected by Audit Manager","Audit Manager replaces the functionality of Security Hub","Security Hub is not related to AWS Audit Manager","Security Hub findings regarding security posture can be included as evidence within Audit Manager assessments."
"Which statement about AWS Audit Manager frameworks is correct?","Frameworks provide pre-built controls and data sources aligned with compliance standards.","Frameworks are only for custom controls and cannot be used with pre-built controls.","Frameworks automatically remediate non-compliant resources.","Frameworks only work with AWS CloudTrail data.","Frameworks offer a structured approach with pre-built controls tailored to specific compliance regulations or internal policies."
"In AWS Audit Manager, what is the purpose of the 'Evidence Locker'?","To store the collected evidence gathered during assessments","To configure security policies","To manage user access permissions","To monitor real-time security threats","The Evidence Locker is the central repository where all the evidence collected during your assessments is stored."
"Which of the following is NOT a benefit of using AWS Audit Manager?","Automated evidence collection","Real-time threat detection","Pre-built compliance frameworks","Customisable controls","Real-time threat detection is not a primary function of Audit Manager. It focuses on compliance assessment using collected evidence."
"Which AWS service can be used to trigger automated actions based on AWS Audit Manager assessment results?","AWS Step Functions","AWS CloudWatch Logs","AWS X-Ray","AWS CodePipeline","AWS Step Functions allows you to orchestrate complex workflows based on events, including assessment outcomes from Audit Manager."
"How can you demonstrate compliance to an external auditor using AWS Audit Manager?","By generating assessment reports containing collected evidence","By providing direct access to your AWS Management Console","By scheduling an automated penetration test","By enabling multi-factor authentication for all users","Assessment reports from Audit Manager consolidate the compliance findings and supporting evidence, making it easier to demonstrate compliance to auditors."
"What type of data can AWS Audit Manager collect from AWS CloudTrail?","API call history for AWS resources","Real-time network traffic data","Operating system logs","Database query logs","CloudTrail logs API calls made to AWS services, providing valuable evidence of user activity and resource management."
"In AWS Audit Manager, what does the term 'control objective' refer to?","A high-level statement of what a control is intended to achieve","A specific technical configuration setting","A performance metric for an AWS resource","A risk assessment rating","Control objectives articulate the purpose and desired outcome of a particular control."
"Which of the following actions CANNOT be performed directly within the AWS Audit Manager console?","Remediating non-compliant resources","Creating and managing assessments","Reviewing collected evidence","Customising controls","Audit Manager focuses on assessing compliance and collecting evidence, not direct remediation of non-compliant resources."
"When customising a control in AWS Audit Manager, what type of information can you modify?","The control description and testing procedures","The underlying AWS service configuration","The pricing plan for the assessment","The geographical region of the assessment","You can tailor the description and testing procedures to better align with your specific requirements."
"What is the maximum retention period for evidence stored in the AWS Audit Manager evidence locker?","7 years","30 days","1 year","90 days","The maximum retention period for evidence in Audit Manager is 7 years."
"What is the relationship between AWS Audit Manager and third-party risk assessments?","Audit Manager can provide evidence to support third-party risk assessments","Audit Manager automates third-party risk assessments","Audit Manager replaces the need for third-party risk assessments","Audit Manager is not related to third-party risk assessments","Evidence gathered by Audit Manager can be used to provide evidence and information for third-party risk assessments."
"When configuring data source for AWS Audit Manager what type of permissions are required to allow Audit Manager to collect the data?","IAM role with appropriate permissions to access the data sources","MFA Enabled for the AWS Account","Explicit DENY Permissions","AWS Support Access","Audit Manager requires an IAM role with the right permissions to access and collect data from the integrated AWS services."
"What is an advantage of using pre-built AWS Audit Manager frameworks over creating custom assessments?","Faster assessment setup and standardised compliance guidance","Greater flexibility in customising controls","Lower cost for running assessments","Automatic remediation of non-compliant resources","Pre-built frameworks offer a quick start with controls aligned to common compliance standards."
"What happens to the collected evidence in AWS Audit Manager when an assessment is deleted?","The evidence remains in the evidence locker until manually deleted","The evidence is automatically archived","The evidence is permanently deleted","The evidence is moved to Amazon S3","When an assessment is deleted, the associated evidence is also permanently deleted from the evidence locker."
"What does AWS Audit Manager use to track changes to your environment over time?","AWS Config","AWS CloudWatch Metrics","AWS Trusted Advisor","AWS Inspector","AWS Config tracks configuration changes, providing a historical record for compliance assessments."
"How does AWS Audit Manager help reduce the manual effort associated with compliance audits?","By automating evidence collection and report generation","By automatically remediating compliance violations","By providing a dedicated security team","By eliminating the need for external auditors","Audit Manager significantly reduces manual effort by automating data gathering and report creation."
"In AWS Audit Manager, what is a 'control owner'?","The individual responsible for the implementation and maintenance of a control","The AWS account owner","The auditor performing the assessment","The service responsible for generating the evidence","The control owner is responsible for ensuring the control is effectively implemented and maintained."
"Which of the following is NOT a characteristic of AWS Audit Manager?","It offers real-time threat detection capabilities","It integrates with other AWS services","It provides pre-built compliance frameworks","It automates evidence collection","Real-time threat detection is not a core function of Audit Manager."
"How does AWS Audit Manager ensure the integrity of collected evidence?","By using cryptographic hashing to verify that the evidence hasn't been tampered with","By encrypting the evidence at rest and in transit","By storing the evidence in a geographically isolated location","By requiring multi-factor authentication for access","Audit Manager uses cryptographic hashing to maintain the integrity of the collected evidence."
"What type of information is included in an AWS Audit Manager assessment report?","A summary of the assessment scope, results, and collected evidence","Real-time network traffic data","Operating system logs","Vulnerability scan results","Assessment reports provide a consolidated view of the assessment, its findings, and the supporting evidence."
"Which of the following actions can you take to improve the efficiency of AWS Audit Manager assessments?","Use pre-built frameworks and automate evidence collection","Manually collect all evidence and create custom reports","Disable integration with other AWS services","Reduce the number of controls included in the assessment","Using pre-built frameworks and automating evidence collection streamlines the assessment process."
"When you delegate tasks within an AWS Audit Manager assessment, what type of access do the delegates receive?","Access to the specific assessment and assigned tasks","Full access to the AWS account","Read-only access to all AWS resources","Access to the evidence locker","Delegates only receive access to the assessment and tasks that have been specifically assigned to them."
"What is a key benefit of using AWS Audit Manager for organisations operating in regulated industries?","It helps demonstrate compliance with industry-specific regulations","It automatically remediates security vulnerabilities","It guarantees compliance with all regulations","It eliminates the need for internal security teams","Audit Manager facilitates compliance by providing evidence and documentation required by industry regulations."
"How can AWS Audit Manager help with internal audits?","By automating the collection and organisation of evidence for internal audits","By replacing the need for internal auditors","By performing penetration testing on internal systems","By providing a vulnerability scanning service for internal infrastructure","Audit Manager can streamline internal audits by automating evidence gathering and organising it for review."
"What is a common use case for creating a custom framework in AWS Audit Manager?","To address compliance requirements that are not covered by pre-built frameworks","To automatically remediate non-compliant resources","To monitor real-time security threats","To optimise AWS resource costs","Custom frameworks allow you to tailor your assessments to unique requirements or internal policies that pre-built frameworks don't address."
"In AWS Audit Manager, what is the purpose of the 'assessment status'?","To indicate the current stage of the assessment process (e.g., active, completed)","To indicate the overall compliance posture of the assessed resources","To indicate the performance of the assessment","To indicate the cost of the assessment","The assessment status tracks the progress of the assessment from initiation to completion."
"Which of the following AWS services can be used to enhance the security of the evidence stored in AWS Audit Manager's evidence locker?","AWS Key Management Service (KMS)","Amazon GuardDuty","AWS WAF","AWS Shield","AWS KMS can be used to encrypt the data in the evidence locker adding an extra layer of protection."
"How does AWS Audit Manager facilitate collaboration between different teams involved in the compliance process?","By allowing you to delegate tasks and share assessment reports","By automatically routing compliance findings to the appropriate teams","By providing a central repository for all compliance documentation","By automating the remediation of compliance violations","Audit Manager facilitates collaboration by enabling task delegation and report sharing, allowing different teams to contribute to the compliance process."
"You have a requirement to prove to an auditor that access to your S3 buckets are restricted to specific IAM roles. How could AWS Audit Manager help?","AWS Audit Manager can collect CloudTrail logs showing IAM role usage with S3 buckets","AWS Audit Manager can automatically restrict bucket access","AWS Audit Manager can encrypt data in transit to S3","AWS Audit Manager can generate fake CloudTrail logs","AWS Audit Manager integrates with CloudTrail and can collect logs about resource access."
"In AWS Audit Manager, what is the scope of an assessment defined by?","The selected AWS accounts, services, and controls","The number of users with access to the assessment","The geographical region where the assessment is performed","The budget allocated for the assessment","The scope defines which AWS accounts, services, and specific controls are part of the assessment."
"What is the primary purpose of the AWS Audit Manager API?","To automate the creation, management, and reporting of assessments","To directly manage AWS resources","To monitor real-time security threats","To configure network settings","The Audit Manager API allows programmatic interaction with the service, enabling automation of assessment-related tasks."
"Which statement best describes the relationship between AWS Audit Manager and human auditors?","AWS Audit Manager provides human auditors with tools to improve the efficiency of the audit process","AWS Audit Manager replaces the need for human auditors","AWS Audit Manager automatically approves or rejects compliance findings","AWS Audit Manager provides technical training for auditors","Audit Manager assists auditors by streamlining evidence collection and reporting, enabling them to focus on analysis and decision-making."
"What is the maximum number of AWS accounts that can be included in a single AWS Audit Manager assessment?","There is no fixed limit","10","100","1000","AWS Audit Manager does not have a fixed limit."
"In AWS Audit Manager, what is the primary purpose of a control?","To define the security requirements that need to be met","To monitor network traffic","To manage user access permissions","To create backups of data","Controls in Audit Manager represent the security requirements that need to be met to comply with a standard or regulation."
"What is an AWS Audit Manager assessment?","A collection of evidence to demonstrate compliance with a specific standard or regulation","A tool to monitor the health of EC2 instances","A service for managing IAM roles","A method for deploying applications","An assessment in Audit Manager is a collection of evidence gathered to demonstrate compliance with a specific standard or regulation."
"Which AWS service is primarily used by AWS Audit Manager to collect evidence?","AWS Config","AWS CloudWatch","AWS CloudTrail","Amazon S3","Audit Manager integrates with services like AWS Config and CloudTrail to automatically collect evidence related to your AWS resources and configurations."
"What type of evidence does AWS Audit Manager collect?","Resource configuration, user activity, and compliance checks","Customer feedback, market research, and sales data","Competitor analysis, industry trends, and financial reports","Employee performance reviews, training records, and HR policies","Audit Manager gathers evidence such as resource configurations (AWS Config), user activity (CloudTrail), and results from compliance checks (Security Hub)."
"In AWS Audit Manager, what is a framework?","A pre-built set of controls and evidence sources aligned to a specific compliance standard","A tool for managing AWS budgets","A method for deploying containerised applications","A type of database","A framework in Audit Manager provides a pre-built set of controls and evidence sources aligned to a specific compliance standard or regulation, simplifying the assessment setup."
"You are using AWS Audit Manager to prepare for a PCI DSS audit. Which component provides pre-defined controls for PCI DSS?","Framework","Delegation","Control Tower","Customisation","Frameworks in AWS Audit Manager provide pre-defined controls mapped to specific compliance standards, such as PCI DSS."
"Which of the following is a benefit of using AWS Audit Manager?","Automated evidence collection","Real-time threat detection","Cost optimisation of EC2 instances","Automated deployment of Lambda functions","Audit Manager automates the evidence collection process, reducing manual effort and improving efficiency."
"How does AWS Audit Manager help streamline the audit process?","By automating evidence collection and organisation","By providing encryption keys","By managing network configurations","By patching operating systems","AWS Audit Manager helps streamline the audit process by automating the collection and organisation of evidence, making it easier to demonstrate compliance."
"Which AWS service can be integrated with AWS Audit Manager to monitor user activity?","AWS CloudTrail","AWS IAM","AWS KMS","AWS Shield","AWS CloudTrail provides a record of user activity in your AWS environment, which can be collected as evidence in Audit Manager."
"When using AWS Audit Manager, what is a 'control objective'?","A statement that describes the intended outcome of a control","A schedule for running backups","A setting that limits access to resources","A notification about security vulnerabilities","A control objective is a statement that describes the intended outcome of a control, providing a clear goal for compliance efforts."
"In AWS Audit Manager, what is the purpose of the 'delegation' feature?","To assign responsibilities to team members for reviewing evidence","To delegate access to AWS accounts","To delegate IAM roles","To delegate security groups","The 'delegation' feature in Audit Manager allows you to assign responsibilities to specific team members for reviewing evidence and ensuring compliance."
"What type of report can be generated in AWS Audit Manager to document assessment results?","Compliance report","Cost optimisation report","Performance analysis report","Vulnerability assessment report","Audit Manager allows you to generate compliance reports that document the results of your assessments, making it easier to share compliance status with auditors."
"Which of the following is NOT a typical use case for AWS Audit Manager?","Automating compliance audits","Managing AWS costs","Demonstrating compliance to auditors","Tracking compliance progress","AWS Audit Manager focuses on compliance, NOT cost management."
"What is the first step in setting up an assessment in AWS Audit Manager?","Choose a framework","Configure CloudTrail","Define IAM roles","Create an S3 bucket","Choosing a framework is the first step in setting up an assessment, as it defines the controls and evidence sources to be used."
"In AWS Audit Manager, what does the 'evidence finder' tool allow you to do?","Search for specific evidence across your AWS environment","Automatically fix security vulnerabilities","Predict future compliance risks","Manage encryption keys","The evidence finder tool in Audit Manager allows you to search for specific evidence related to your controls and assessments, improving efficiency."
"You need to demonstrate compliance with a custom security policy. How can you use AWS Audit Manager?","Create a custom framework","Use a pre-built framework and modify it","Use AWS Config rules","Use AWS Trusted Advisor","You can create a custom framework in Audit Manager to define your own controls and evidence sources for demonstrating compliance with custom security policies."
"Which AWS service provides information about the configuration of your AWS resources that AWS Audit Manager uses as evidence?","AWS Config","AWS CloudWatch","AWS CloudTrail","Amazon Inspector","AWS Config provides detailed configuration information about your AWS resources, which Audit Manager can use as evidence."
"What is a key benefit of using pre-built frameworks in AWS Audit Manager?","Reduces the time and effort required to set up compliance assessments","Automatically encrypts data at rest","Automatically scales AWS resources","Automatically generates network diagrams","Pre-built frameworks reduce the time and effort required to set up compliance assessments by providing pre-defined controls and evidence sources."
"What is the purpose of the 'assessment report' in AWS Audit Manager?","To provide a summary of the assessment findings and compliance status","To provide a detailed cost analysis of AWS usage","To provide a list of security vulnerabilities","To provide a network topology diagram","The assessment report in Audit Manager provides a summary of the assessment findings and compliance status, making it easier to share with auditors."
"Which of the following is a key feature of the AWS Audit Manager evidence repository?","Centralised storage and management of collected evidence","Real-time threat detection and response","Automated patching of operating systems","Automated deployment of applications","The evidence repository provides centralised storage and management of all the evidence collected during an assessment, ensuring it is readily available for review."
"In AWS Audit Manager, what is the significance of the 'control status'?","Indicates whether a control is meeting its objective","Indicates the cost of implementing a control","Indicates the performance of a control","Indicates the level of risk associated with a control","The control status indicates whether a control is meeting its intended objective, providing insight into the overall compliance status."
"What is the relationship between AWS Audit Manager and AWS Security Hub?","Audit Manager can use Security Hub findings as evidence","Security Hub can directly create Audit Manager assessments","Security Hub manages Audit Manager permissions","There is no relationship between the two services","Audit Manager can integrate with Security Hub to use security findings as evidence in your compliance assessments."
"You are planning to use AWS Audit Manager for a SOC 2 audit. What should you do first?","Select the SOC 2 framework in Audit Manager","Configure AWS CloudTrail","Create an IAM role for Audit Manager","Enable AWS Config","Selecting the appropriate framework (SOC 2 in this case) is the first step in setting up an assessment."
"What type of role does AWS Audit Manager assume to collect evidence from your AWS resources?","IAM role","Service-linked role","AWS account","EC2 instance profile","Audit Manager assumes an IAM role with appropriate permissions to collect evidence from your AWS resources."
"Which of the following is NOT a component of an AWS Audit Manager framework?","Evidence sources","Controls","Assessment reports","AWS budgets","AWS budgets are not a component of Audit Manager frameworks; frameworks consist of evidence sources, controls and other components that related to compliance."
"In AWS Audit Manager, what is the purpose of the 'lifecycle' of an assessment?","To track the progress of the assessment from creation to completion","To manage the cost of the assessment","To manage the security of the assessment data","To manage the backups of the assessment data","The lifecycle tracks the progress of the assessment, from initial setup to completion and reporting."
"Which of the following is a benefit of using AWS Audit Manager in a multi-account environment?","Centralised compliance monitoring across multiple AWS accounts","Automated deployment of resources across multiple accounts","Simplified cost management across multiple accounts","Automated security vulnerability patching across multiple accounts","Audit Manager enables centralised compliance monitoring across multiple AWS accounts, providing a unified view of your compliance posture."
"What type of data does AWS Audit Manager encrypt at rest?","Evidence collected during assessments","IAM roles and policies","EC2 instance data","S3 bucket contents","Audit Manager encrypts the evidence collected during assessments to protect sensitive data."
"You need to grant an auditor access to your AWS Audit Manager assessment results. What is the recommended approach?","Create a dedicated IAM role with limited access to the assessment report","Share your AWS account credentials","Grant full administrator access to your AWS account","Send the auditor a copy of the raw evidence files","Creating a dedicated IAM role with limited access to the assessment report is the most secure way to grant access to auditors."
"What is the purpose of the 'custom control' feature in AWS Audit Manager?","To define controls that are not included in pre-built frameworks","To automatically remediate security vulnerabilities","To automatically scale AWS resources based on demand","To automatically generate compliance reports","The 'custom control' feature allows you to define controls that are specific to your organisation and are not included in pre-built frameworks."
"How does AWS Audit Manager help with continuous compliance?","By continuously collecting and monitoring evidence","By automatically patching security vulnerabilities","By automatically scaling AWS resources based on demand","By automatically encrypting data in transit","Audit Manager helps with continuous compliance by continuously collecting and monitoring evidence, providing ongoing visibility into your compliance posture."
"Which AWS service can be integrated with AWS Audit Manager to collect evidence about network configurations?","AWS Config","Amazon VPC","AWS CloudTrail","AWS Network Firewall","AWS Config is the primary service used to track the configuration of AWS resources, including network configurations within Amazon VPC."
"What is the function of the 'assessment template' in AWS Audit Manager?","To predefine the settings for creating new assessments","To automatically remediate security vulnerabilities","To automatically scale AWS resources based on demand","To automatically generate compliance reports","Assessment templates allow you to predefine settings for creating new assessments, ensuring consistency and efficiency."
"In AWS Audit Manager, what does the 'evidence' refer to?","The data collected to demonstrate compliance with a control","The code used to deploy AWS resources","The encryption keys used to protect data","The network configurations of AWS resources","Evidence refers to the data collected by Audit Manager to demonstrate that a specific control is being effectively implemented."
"You are using AWS Audit Manager and notice a control is failing. What is the next step you should take?","Investigate the evidence and identify the root cause of the failure","Immediately disable the control","Delete the assessment","Contact AWS support","Investigating the evidence is the logical next step to understand why the control is failing and identify the root cause."
"Which of the following is a key difference between AWS Audit Manager and AWS CloudTrail?","Audit Manager is focused on compliance, while CloudTrail is focused on auditing user activity","Audit Manager manages IAM roles, while CloudTrail monitors network traffic","Audit Manager manages encryption keys, while CloudTrail manages data backups","Audit Manager is free of charge, while CloudTrail incurs costs","Audit Manager is designed for compliance assessments while CloudTrail focuses on logging and auditing user activity and API calls."
"How does AWS Audit Manager support the 'shared responsibility model'?","By providing tools to manage your compliance responsibilities in the cloud","By managing AWS's compliance responsibilities","By providing a single point of contact for all compliance issues","By automating all compliance tasks","Audit Manager helps you manage your portion of the shared responsibility model by providing tools to manage and demonstrate compliance for your own resources."
"In AWS Audit Manager, what is the purpose of the 'assessment scope'?","To define the AWS resources and accounts included in the assessment","To define the time period covered by the assessment","To define the cost of the assessment","To define the users who have access to the assessment","The assessment scope defines the specific AWS resources and accounts that will be included in the assessment."
"Which AWS service provides information about user identities and access that AWS Audit Manager uses as evidence?","AWS IAM","AWS KMS","AWS SSO","AWS Cognito","AWS IAM is the service that manages user identities and access, providing crucial evidence for compliance assessments related to access control."
"What is the benefit of using AWS Audit Manager for demonstrating compliance with industry regulations like GDPR?","It provides pre-built frameworks and controls mapped to GDPR requirements","It automatically encrypts all data stored in AWS","It provides a legal opinion on GDPR compliance","It automatically generates GDPR compliance reports without any configuration","Audit Manager offers pre-built frameworks tailored to industry regulations like GDPR, streamlining the assessment process."
"How does AWS Audit Manager help reduce the burden of manual audits?","By automating evidence collection and organisation","By providing free AWS credits","By providing dedicated AWS support engineers","By automatically deploying applications","Audit Manager reduces manual effort by automating the process of collecting and organizing evidence needed for audits."
"In AWS Audit Manager, what is the purpose of the 'continuous monitoring' feature?","To continuously monitor your AWS environment for compliance violations","To continuously monitor the cost of your AWS resources","To continuously monitor the performance of your AWS resources","To continuously monitor the security of your AWS resources","The continuous monitoring feature provides ongoing visibility into your compliance posture by continuously collecting and analysing evidence."
"Which of the following is NOT a key capability of AWS Audit Manager?","Automated evidence collection","Risk assessment","Centralised compliance dashboard","Pre-built compliance frameworks","Risk assessment is not a direct capability of AWS Audit Manager. While it informs compliance, Audit Manager primarily focuses on evidence gathering and reporting."
"When using AWS Audit Manager, how can you ensure that the evidence collected is accurate and reliable?","By configuring the evidence sources correctly and reviewing the collected evidence","By using a third-party audit firm","By encrypting all data in transit","By using a multi-factor authentication","Configuring evidence sources correctly and reviewing the collected evidence is the best approach to ensure accuracy and reliability."
"You need to quickly assess your compliance status against a specific regulatory standard. Which AWS Audit Manager feature can help?","Using a pre-built framework","Creating a custom control","Delegating tasks to team members","Generating a compliance report","Using a pre-built framework allows you to quickly assess your compliance status as it provides pre-defined controls and evidence sources."
"What is the key purpose of AWS Audit Manager's integration with AWS Organisations?","To enable centralised compliance monitoring across multiple AWS accounts within an organisation","To automatically deploy resources across multiple AWS accounts","To simplify cost management across multiple AWS accounts","To automatically enforce security policies across multiple AWS accounts","The primary goal is to provide a centralised view and management of compliance across all accounts within the organisation."
"How does AWS Audit Manager help ensure data privacy during compliance assessments?","By encrypting evidence at rest and in transit","By automatically redacting sensitive data from evidence","By restricting access to assessment reports","By providing a dedicated compliance officer","Audit Manager encrypts the evidence at rest and in transit to protect sensitive data during compliance assessments."
"Which AWS service should be used as a primary data source when defining and validating AWS Audit Manager controls related to configuration compliance?","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS Inspector","AWS Config provides detailed configuration information about your AWS resources, making it ideal for configuration compliance controls."
"How can AWS Audit Manager assist with demonstrating adherence to internal security policies, beyond external compliance frameworks?","By allowing the creation of custom frameworks and controls aligned with specific internal policies","By automatically enforcing security policies through resource configurations","By providing a pre-built library of internal security policy templates","By completely automating the compliance process without manual intervention","Custom frameworks and controls allow organisations to tailor Audit Manager to their unique internal policies."
"In AWS Audit Manager, what is the primary purpose of a framework?",To define the compliance requirements and evidence collection procedures for an audit.,To define the AWS regions where audit data is stored.,To manage IAM permissions for auditors.,To automate the creation of AWS accounts.,Frameworks in Audit Manager provide a pre-built or customisable structure for defining compliance requirements and specifying how evidence should be collected and assessed.
"With AWS Audit Manager, what is an assessment?",A collection of audit reports generated for a specific timeframe.,A group of AWS accounts under audit.,"A container that organises your audit evidence and automates evidence collection, based on a chosen framework.",A tool for automatically remediating non-compliant resources.,An assessment is the central object in Audit Manager where you organise evidence collection and review compliance status against the chosen framework's requirements.
How does AWS Audit Manager help with automating evidence collection?,It automatically gathers evidence from various AWS services based on the controls defined in your assessment framework.,It automatically generates compliance reports and distributes them to stakeholders.,It automatically detects and remediates security vulnerabilities in your AWS environment.,It automatically scales your AWS resources based on compliance requirements.,"Audit Manager integrates with various AWS services to collect evidence related to the controls defined in your assessment framework, reducing the manual effort involved in audit preparation."
Which of the following AWS services is NOT directly integrated with AWS Audit Manager for automated evidence collection?,AWS CloudTrail,AWS Config,AWS Security Hub,Amazon SQS,"While AWS Audit Manager integrates with CloudTrail, Config, and Security Hub, it does not directly integrate with Amazon SQS for evidence collection."
What is the role of a control in AWS Audit Manager?,To define the specific requirements or objectives that need to be met for compliance.,To automatically apply security patches to EC2 instances.,To encrypt data stored in S3 buckets.,To manage network access control lists.,Controls in Audit Manager represent the specific requirements or objectives that need to be met to achieve compliance with a particular standard or regulation.
What is the purpose of the AWS Audit Manager evidence finder?,To allow auditors to easily query and retrieve specific pieces of evidence related to their audit.,To automatically identify and flag non-compliant resources.,To create and manage custom controls.,To schedule automatic backups of audit data.,"The Evidence Finder allows users to search, filter, and retrieve specific pieces of evidence within their assessment, enabling efficient audit review and analysis."
Which of the following is a key benefit of using custom frameworks within AWS Audit Manager?,Tailoring the audit process to specific organisational needs and compliance requirements.,Automatically generating compliance reports for all AWS regions.,Enforcing multi-factor authentication for all AWS users.,Automatically optimising AWS resource costs.,"Custom frameworks enable organisations to define their own controls and requirements, aligning the audit process with their unique needs and compliance obligations."
"When granting access to AWS Audit Manager assessments, what is the principle of least privilege?",Granting users only the minimum permissions necessary to perform their specific tasks related to the audit.,Granting all users full administrative access to all assessments.,Granting access based on job title rather than individual needs.,Granting access to all AWS services in addition to Audit Manager.,"Adhering to the principle of least privilege involves granting users only the permissions they need to perform their specific tasks, minimising the risk of accidental or malicious actions."
What is the AWS Audit Manager term for the documentation generated as a result of the evidence collection and assessment process?,Audit report,Compliance report,Risk assessment,Security assessment,"Audit Manager generates audit reports that consolidate evidence, compliance status, and other relevant information related to the assessment."
What type of data retention policies can be configured within AWS Audit Manager?,Audit Manager uses AWS Config rules to define retention policies.,Audit Manager integrates with AWS Backup for data retention.,"You can set the retention period for assessment data, which determines how long the evidence and reports are stored.",Audit Manager data is retained indefinitely.,"Audit Manager allows you to configure the retention period for assessment data, providing control over how long evidence and reports are stored for compliance and auditing purposes."
"In AWS Audit Manager, what is the primary function of a framework?",To provide a prebuilt set of controls and audit procedures aligned to a specific standard or regulation,To define user access permissions to audit data,To automatically remediate non-compliant resources,To monitor network traffic for security threats,"Frameworks in Audit Manager provide a structured approach to auditing by offering pre-defined sets of controls and procedures tailored to specific compliance standards or regulations, saving time and effort in audit preparation."
What is the purpose of a control in AWS Audit Manager?,To define a security requirement that needs to be assessed,To automate the creation of AWS accounts,To generate compliance reports,To configure network firewalls,Controls in Audit Manager represent the security requirements that need to be assessed to ensure compliance with a given standard. They serve as the foundation for the audit process.
"When setting up an assessment in AWS Audit Manager, what is a key benefit of using service control policies (SCPs) as a data source?",They provide automated evidence collection for compliance,They automatically correct non-compliant resources,They can be used to deny access to specific AWS services,They allow you to monitor CPU utilisation of EC2 instances,"SCPs, when used as a data source, facilitate automated evidence collection by providing data related to access controls and resource configurations, streamlining the audit process."
"Within AWS Audit Manager, what is the role of an assessment report?",To provide a summary of the audit findings and compliance status,To automatically deploy security patches,To configure network security groups,To manage AWS IAM roles,"Assessment reports in Audit Manager summarise the audit findings and compliance status based on the evidence collected, giving a clear overview of the organisation's adherence to the relevant standards."
How does AWS Audit Manager help in continuous compliance monitoring?,By automating evidence collection and providing real-time compliance status updates,By automatically encrypting data at rest,By automatically scaling EC2 instances,By automatically backing up database instances,"Audit Manager automates evidence collection and provides real-time compliance status updates, enabling continuous monitoring and early detection of potential compliance issues."
"In AWS Audit Manager, what does it mean to delegate a control?",To assign responsibility for providing evidence for a specific control to another user,To deny access to a specific control,To automatically remediate a non-compliant finding,To encrypt a control's data,Delegating a control in Audit Manager means assigning the responsibility for providing evidence and demonstrating compliance with that control to a specific user within the organisation.
What type of evidence can be automatically collected by AWS Audit Manager?,Configuration settings of AWS resources,Manual penetration testing results,Employee background check reports,Customer survey data,"Audit Manager can automatically collect evidence related to the configuration settings of AWS resources, helping to streamline the evidence gathering process."
What is the significance of the 'evidence finder' feature in AWS Audit Manager?,It helps locate and review evidence related to a specific control,It automatically fixes security vulnerabilities,It scans the network for malware,It generates compliance reports automatically,"The evidence finder feature allows users to efficiently locate and review evidence associated with a particular control, simplifying the process of verifying compliance."
Which AWS service integrates with AWS Audit Manager to provide configuration compliance data?,AWS Config,AWS CloudTrail,Amazon CloudWatch,AWS IAM,"AWS Config integrates with Audit Manager to supply configuration compliance data, offering insights into whether resources are configured according to defined standards."
"When using AWS Audit Manager, what is the main purpose of customising a standard framework?",To tailor the framework to specific organisational needs and compliance requirements,To change the pricing model for Audit Manager,To disable certain AWS services,To create a new AWS Region,"Customising a standard framework enables organisations to adapt the framework to their unique requirements, allowing them to focus on the controls and procedures most relevant to their specific compliance obligations."
"In AWS Audit Manager, what is the primary purpose of a framework?","To provide a prebuilt collection of controls, risks, and audit procedures.",To define the security groups for your AWS resources.,To configure network settings for your audit assessments.,To manage user access policies within the AWS account.,Frameworks in Audit Manager offer a structured approach with predefined elements to simplify and accelerate audit preparation and execution.
"With AWS Audit Manager, what is an assessment?",A collection of evidence gathered to evaluate compliance against a specific standard.,A real-time dashboard displaying resource utilisation metrics.,An automated process for patching operating systems.,A system for managing identity and access across multiple AWS accounts.,"An assessment in Audit Manager is designed to evaluate your organisation's compliance status against the requirements of a specific standard, regulation, or internal policy."
What is the role of a data source in AWS Audit Manager?,To provide evidence for the controls in your audit assessment.,To encrypt data at rest.,To monitor network traffic.,To manage IAM permissions.,"Data sources feed relevant information into Audit Manager, acting as the foundation for generating evidence related to control performance."
Which of the following actions can you perform with AWS Audit Manager after an assessment is complete?,Generate an audit report.,Automatically remediate non-compliant findings.,Migrate the data to a different region.,Delete the AWS account.,Audit Manager allows you to compile your assessment findings into a comprehensive report for auditors or internal stakeholders.
What is the purpose of custom controls in AWS Audit Manager?,To address requirements not covered by prebuilt controls.,To automatically block malicious traffic.,To manage AWS budgets.,To configure instance types.,"Custom controls provide flexibility, enabling you to tailor audit processes to specific needs beyond the scope of existing frameworks."
Which of the following AWS services is directly integrated with AWS Audit Manager to automatically collect evidence?,AWS Config,Amazon SQS,Amazon EC2,AWS Lambda,AWS Config is integrated with Audit Manager to collect configuration history as evidence.
"In AWS Audit Manager, what is the meaning of 'control objectives'?",They define the scope and purpose of each control within a framework.,They define the encryption keys used.,They define the instance size to be used.,They define the allowed IP ranges.,"Control objectives clarify what each control aims to achieve, providing context for assessment and evaluation."
What is the purpose of the 'delegation' feature in AWS Audit Manager?,To assign responsibility for evidence review and remediation to specific users.,To delegate access to AWS support.,To delegate IAM role creation to another user.,To delegate billing tasks to another department.,"Delegation in Audit Manager allows you to distribute audit tasks, ensuring accountability and streamlined workflows."
How does AWS Audit Manager assist with continuous compliance?,By automating evidence collection and assessment.,By automatically scaling EC2 instances.,By automatically updating security group rules.,By automatically patching operating systems.,"Audit Manager automates data gathering, which supports continuous compliance monitoring and helps identify deviations promptly."
"Regarding AWS Audit Manager pricing, what determines the charges you incur?",The number of assessments and custom controls you create.,The number of EC2 instances you run.,The amount of data transferred out of AWS.,The number of IAM users in your account.,"Audit Manager pricing is based on the number of assessments you run and the number of custom controls you've created, providing a flexible and predictable cost model."
"In AWS Audit Manager, what is the primary function of an assessment?",To collect evidence related to compliance requirements.,To encrypt data at rest.,To manage IAM permissions.,To configure network access control lists.,An assessment in Audit Manager is designed to automate the evidence collection process based on prebuilt or custom frameworks to assess compliance.
Which of the following AWS services is NOT directly integrated with AWS Audit Manager for automated evidence collection?,AWS CloudTrail,AWS Config,Amazon GuardDuty,Amazon SQS,"Amazon SQS is not a directly integrated service for automated evidence collection in Audit Manager. CloudTrail, Config, and GuardDuty are."
"In AWS Audit Manager, what is a 'control' in the context of compliance assessments?",A safeguard or countermeasure designed to reduce risk and meet compliance requirements.,A metric used to monitor the health of AWS services.,A role used to manage access permissions to AWS resources.,A tag used to organise AWS resources.,"In Audit Manager, a control represents a safeguard or countermeasure implemented to meet specific compliance requirements and mitigate risks."
"When creating a custom framework in AWS Audit Manager, what is the purpose of defining a 'control set'?",To group related controls together for easier management.,To define the scope of the audit assessment.,To specify the region where audit data is stored.,To configure encryption settings for audit logs.,"A control set in a custom framework allows you to group related controls, providing a structured approach to organising and managing compliance requirements."
How does AWS Audit Manager help with continuous compliance?,By automating evidence collection and providing a centralised dashboard for monitoring.,By automatically remediating non-compliant resources.,By providing a free tier for all users.,By offering a built-in vulnerability scanner.,"Audit Manager automates the collection of evidence and offers a dashboard to view the compliance status, which enables continuous monitoring and helps identify potential issues proactively."
What type of evidence does AWS Audit Manager collect from AWS CloudTrail?,API activity and user actions.,Network traffic logs.,CPU utilisation metrics.,Database query logs.,Audit Manager leverages CloudTrail to collect evidence related to API activity and user actions performed within the AWS environment.
What is the purpose of the 'delegation' feature within an AWS Audit Manager assessment?,To assign ownership of specific tasks or controls to different users.,To grant temporary access to AWS resources.,To configure cross-account access.,To create read-only access to audit reports.,"Delegation allows you to assign ownership of specific tasks or controls within the assessment to different users, facilitating collaboration and accountability."
Which AWS Audit Manager feature allows you to reuse assessment configurations across multiple accounts?,Custom Frameworks,AWS Organisations integration,Control Customisation,Automated Evidence Collection,"By integrating with AWS Organisations, Audit Manager allows you to apply assessment configurations, including custom frameworks, consistently across multiple accounts within your organisation."
What is the difference between a Standard Framework and a Custom Framework in AWS Audit Manager?,"Standard Frameworks are pre-built, while Custom Frameworks are defined by the user.","Standard Frameworks are free to use, while Custom Frameworks require a subscription.","Standard Frameworks are region-specific, while Custom Frameworks are global.",Standard Frameworks collect more evidence than Custom Frameworks.,Standard Frameworks are provided by AWS and map to common compliance standards. Custom Frameworks allow organisations to define their own specific requirements.
"In AWS Audit Manager, what is the role of assessment reports?",To summarise the evidence collected and provide insights into compliance status.,To configure the assessment settings.,To define the scope of the audit.,To manage user access to audit data.,Assessment reports in Audit Manager provide a comprehensive summary of the collected evidence and offer insights into the organisation's compliance posture against the defined framework.