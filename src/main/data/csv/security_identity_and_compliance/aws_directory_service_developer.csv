"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS Directory Service?","To connect your AWS resources to an existing on-premises Microsoft Active Directory or to set up a new directory in the AWS Cloud.","To manage AWS Identity and Access Management (IAM) roles.","To provision virtual machines in the AWS Cloud.","To monitor network traffic in your VPC.","AWS Directory Service provides multiple ways to use Microsoft Active Directory or other directory services with other AWS services."
"Which type of AWS Directory Service allows you to join EC2 instances to a domain without the need for a VPN?","AWS Managed Microsoft AD","AD Connector","Simple AD","Cloud Directory","AWS Managed Microsoft AD creates an actual Active Directory in the AWS Cloud, enabling seamless domain joining."
"Which AWS Directory Service option is a proxy service, redirecting directory requests to your on-premises Active Directory without caching any information in AWS?","AD Connector","AWS Managed Microsoft AD","Simple AD","Cloud Directory","AD Connector simply forwards requests to your on-premises directory."
"What is the primary use case for AWS Directory Service's 'Simple AD'?","A standalone directory for basic directory services such as user management and authentication.","Connecting to an existing on-premises Active Directory.","Managing AWS IAM users and roles.","Providing identity management for serverless applications.","Simple AD is a cost-effective option for basic directory functionality, without the complexities of full Active Directory."
"What's the primary difference between 'AWS Managed Microsoft AD' and 'AD Connector' within AWS Directory Service?","'AWS Managed Microsoft AD' creates a new, fully-managed Active Directory in AWS, while 'AD Connector' connects to your existing on-premises AD.","'AWS Managed Microsoft AD' is only used for Linux instances, while 'AD Connector' is only used for Windows instances.","'AWS Managed Microsoft AD' is more cost-effective than 'AD Connector'.","'AWS Managed Microsoft AD' does not support multi-factor authentication, while 'AD Connector' does.","'AWS Managed Microsoft AD' provides a managed AD environment within AWS, whereas 'AD Connector' acts as a bridge to your on-premises AD."
"When would you choose to use AWS Directory Service 'Cloud Directory' over 'AWS Managed Microsoft AD'?","When you need a flexible directory service for cloud-native applications that does not rely on traditional AD.","When you need to manage Windows EC2 instances in a domain.","When you need to integrate with on-premises applications that require Kerberos authentication.","When you need to provide SSO to Microsoft Office 365.","Cloud Directory is designed for cloud-native applications with hierarchical data and flexible schema needs."
"Which AWS Directory Service option is best suited for providing Single Sign-On (SSO) to AWS applications and resources using your existing on-premises Active Directory credentials?","AD Connector","Simple AD","Cloud Directory","AWS Managed Microsoft AD (with trust relationships)","AD Connector and AWS Managed Microsoft AD with trust relationship allow users to use existing on-premises credentials."
"What authentication protocol is primarily used by 'AWS Managed Microsoft AD' to allow users to authenticate against AWS resources?","Kerberos","SAML","LDAP","OAuth","AWS Managed Microsoft AD is a real instance of Active Directory, and thus leverages Kerberos for authentication."
"Which of the following is a key benefit of using AWS Directory Service for managing user access to AWS resources?","Centralised user management and authentication.","Automated infrastructure provisioning.","Real-time threat detection.","Simplified network configuration.","AWS Directory Service allows you to manage user identities in one place and apply consistent access policies across AWS resources."
"What type of trust relationship can be established between an AWS Managed Microsoft AD directory and an on-premises Active Directory domain?","Two-way trust","One-way incoming trust","One-way outgoing trust","No trust relationships are possible","A two-way trust allows users in either domain to access resources in the other."
"When setting up AWS Directory Service, which of the following network configurations is required for 'AWS Managed Microsoft AD'?","A VPC with private subnets.","A public subnet with an internet gateway.","A direct connection to the internet.","A VPN connection to your on-premises network.","AWS Managed Microsoft AD requires private subnets for security reasons."
"What is the purpose of a 'Conditional Forwarder' when configuring 'AD Connector' in AWS Directory Service?","To forward DNS queries for specific domains to your on-premises DNS servers.","To encrypt traffic between AWS and your on-premises Active Directory.","To automatically update the AD Connector software.","To create a backup of your on-premises Active Directory in AWS.","Conditional Forwarders allow your AWS resources to resolve names in your on-premises Active Directory domain."
"Which of the following AWS services can be integrated with AWS Directory Service for Single Sign-On (SSO) access?","AWS IAM Identity Center (Successor to AWS SSO)","Amazon S3","Amazon EC2","Amazon CloudWatch","AWS IAM Identity Center integrates with AWS Directory Service to provide SSO to AWS resources."
"When deploying AWS Directory Service 'AWS Managed Microsoft AD' in a multi-account AWS environment, how can you centralise directory management?","By creating a centralised AD directory in one account and establishing trust relationships with other accounts.","By deploying a separate AD directory in each account.","By using AWS Organizations to automatically synchronise AD directories across accounts.","By manually copying user accounts between accounts.","Using a centralised AD allows for easier management and consistent policies across the organisation."
"Which of the following is a key factor in determining the appropriate size and type of AWS Directory Service instance for 'AWS Managed Microsoft AD'?","The number of users and groups in your directory.","The amount of data stored in your S3 buckets.","The number of EC2 instances in your VPC.","The amount of network bandwidth available.","Directory size is determined by how many directory objects will be stored, in this case users and groups."
"Which AWS Directory Service offering is best suited for providing LDAP-compatible directory services to applications running on Amazon EC2 instances when a full Active Directory is not required?","Simple AD","AWS Managed Microsoft AD","AD Connector","Cloud Directory","Simple AD provides basic directory services using LDAP."
"What is the primary advantage of using AWS Directory Service with AWS IAM Identity Center?","It enables users to use their existing Active Directory credentials to access AWS applications and resources.","It automates the creation of IAM roles and policies.","It provides enhanced security monitoring of IAM user activity.","It eliminates the need for IAM users altogether.","IAM Identity Center leverages Directory Services for identity federation."
"Which of the following AWS services can you use to monitor the health and performance of your AWS Directory Service instances?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","CloudWatch provides metrics and logging for AWS services, including Directory Service."
"You are experiencing issues with authentication against your AWS Managed Microsoft AD directory. Which log should you consult first to troubleshoot the problem?","Directory Service event logs in CloudWatch Logs","EC2 instance system logs","VPC Flow Logs","S3 access logs","Directory Service writes events to CloudWatch Logs for auditing purposes."
"Which AWS Directory Service feature allows you to extend your on-premises Active Directory schema to include custom attributes?","Schema extensions in AWS Managed Microsoft AD","Custom attributes in AD Connector","Dynamic schemas in Simple AD","Cloud Directory's flexible schema","AWS Managed Microsoft AD is the closest to a full active directory and enables schema extensions."
"What is the best approach to ensure high availability for your AWS Managed Microsoft AD directory in the event of an Availability Zone failure?","Deploy the directory across multiple Availability Zones.","Regularly back up the directory to Amazon S3.","Use AWS CloudTrail to monitor directory changes.","Manually recreate the directory in another Availability Zone.","AWS Managed Microsoft AD can be deployed across multiple AZs for high availability."
"Which AWS Directory Service option allows you to enable multi-factor authentication (MFA) for users accessing AWS resources?","AWS Managed Microsoft AD with a RADIUS server","Simple AD with IAM roles","AD Connector with AWS IAM","Cloud Directory with AWS Cognito","AWS Managed Microsoft AD supports integration with RADIUS servers for MFA."
"What is the purpose of the AWS Directory Service 'Trust Relationship' feature?","To allow users in one Active Directory domain to access resources in another Active Directory domain.","To encrypt data in transit between AWS and your on-premises network.","To automatically scale your AWS Directory Service instances.","To replicate data between AWS and your on-premises Active Directory.","Trust relationships allow users in different domains to access each other's resources."
"Which of the following AWS services can be used to manage and automate tasks within your AWS Managed Microsoft AD directory?","AWS Systems Manager","Amazon EC2 Auto Scaling","AWS Lambda","Amazon CloudFront","AWS Systems Manager provides automation and configuration management capabilities."
"When configuring AWS Directory Service 'AWS Managed Microsoft AD', what is the recommended approach for DNS resolution?","Use the DNS servers provided by AWS Directory Service.","Configure your on-premises DNS servers to forward queries to the AWS Directory Service DNS servers.","Use a public DNS resolver like Google Public DNS.","Manually update the DNS records on each EC2 instance.","Using the DNS servers provided by AWS Directory Service ensures proper AD functioning."
"Which AWS Directory Service offers you the least administrative overhead?","Simple AD","AD Connector","AWS Managed Microsoft AD","Cloud Directory","Simple AD offers the least capabilities and therefor the least administrative overhead."
"You want to allow users in your on-premises Active Directory to authenticate to AWS resources without replicating passwords to AWS. Which AWS Directory Service offering would be most appropriate?","AD Connector","Simple AD","AWS Managed Microsoft AD","Cloud Directory","AD Connector acts as a proxy and does not replicate passwords."
"Which AWS Directory Service can be used to create a trust relationship with an on-premises domain?","AWS Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory","AWS Managed Microsoft AD supports trust relationships."
"What is the name of the tool used to manage a Microsoft Active Directory hosted on AWS?","Active Directory Users and Computers","AWS Console","AWS CLI","AWS Tools for Powershell","AWS Managed Microsoft AD utilizes standard Active Directory tools for administration."
"Your company wants to use the same usernames and passwords for local computers and for AWS hosted applications. Which AWS Directory Service helps enable this scenario?","AD Connector","Simple AD","Cloud Directory","AWS SSO","AD Connector allows federated authentication."
"What is the difference between AD Connector and Managed AD in terms of where the directory is hosted?","AD Connector connects to an on-premises AD, while Managed AD hosts a directory in AWS.","AD Connector hosts a directory in AWS, while Managed AD connects to an on-premises AD.","AD Connector requires a VPN connection, while Managed AD does not.","AD Connector is only for Linux, while Managed AD is only for Windows.","AD Connector simply forwards requests to your on-premises directory."
"What would you use to allow AWS applications to leverage existing on premise credentials?","AD Connector","Simple AD","Cloud Directory","AWS IAM","AD Connector allows federated authentication."
"Which AWS Directory Service does not cache any user information in the cloud?","AD Connector","AWS Managed Microsoft AD","Simple AD","Cloud Directory","AD Connector simply forwards requests to your on-premises directory."
"What should you configure in AWS in order for an EC2 instance that is connected to an on-premises Active Directory (AD) via AD Connector to find the AD domain?","Conditional forwarder in Route 53","IAM Role","Security group","Network ACL","A Conditional Forwarder is used to configure DNS resolution to an on-premise AD server."
"What is the authentication protocol used by AWS Managed Microsoft AD?","Kerberos","SAML","OAUTH","LDAP","AWS Managed Microsoft AD is a real instance of Active Directory, and thus leverages Kerberos for authentication."
"Which service requires you to maintain Active Directory, including patching, software updates, and backups?","AD Connector","AWS Managed Microsoft AD","Simple AD","Cloud Directory","When using AD Connector, you are responsible for maintaining the Active Directory on-premises."
"What is the AWS Directory Service's Cloud Directory primarily designed for?","Cloud-native applications","Traditional Active Directory replacement","Simple LDAP authentication","Windows-based environments","Cloud Directory is designed for cloud-native applications with hierarchical data and flexible schema needs."
"You are managing an AWS Managed Microsoft AD and need to extend the Active Directory schema. What capabilities does AWS Managed Microsoft AD offer in this regard?","Allows full schema extensions","Limited schema extensions","No schema extensions","Schema extensions are managed by AWS","AWS Managed Microsoft AD is the closest to a full active directory and enables schema extensions."
"You are configuring a multi-account AWS environment and want to centralise directory management. How can this be achieved with AWS Directory Service?","Centralise AD in one account and establish trust relationships.","Deploy separate AD in each account.","Use AWS Organisations to synchronize AD.","Manually copy users across accounts.","Using a centralised AD allows for easier management and consistent policies across the organisation."
"Which of the following AWS services can be integrated with AWS Directory Service to enable Single Sign-On (SSO)?","AWS IAM Identity Center","Amazon S3","Amazon EC2","Amazon CloudWatch","AWS IAM Identity Center integrates with AWS Directory Service to provide SSO to AWS resources."
"When configuring AWS Directory Service, what network setting is mandatory for 'AWS Managed Microsoft AD'?","A VPC with Private Subnets","A Public Subnet with an Internet Gateway","A Direct Connection to the Internet","A VPN connection","AWS Managed Microsoft AD requires private subnets for security reasons."
"What action should you take when encountering authentication issues with AWS Managed Microsoft AD?","Examine Directory Service event logs in CloudWatch Logs","Check EC2 instance system logs","Analyse VPC Flow Logs","Review S3 Access Logs","Directory Service writes events to CloudWatch Logs for auditing purposes."
"What is the main advantage of connecting on-premise AD to AWS Directory Services?","It allows existing users to authenticate to AWS resources","It encrypts data in transit between AWS and the on premise network","It creates daily backups of the on-premise AD","It automatically scales instances in AWS","AWS Directory Service allows you to manage user identities in one place and apply consistent access policies across AWS resources."
"If an organisation doesn't need the full feature set of Active Directory but still requires directory services such as LDAP, which AWS Directory Service offering is best suited?","Simple AD","AWS Managed Microsoft AD","AD Connector","Cloud Directory","Simple AD provides basic directory services using LDAP."
"What is the purpose of enabling deletion protection on AWS Directory Service for Microsoft Active Directory?","To prevent accidental deletion of the directory","To prevent unauthorized modifications to the directory schema","To protect the directory from malware attacks","To automatically create backups of the directory","Amazon RDS Deletion Protection prevents accidental deletion of your database instance, adding an extra layer of protection for critical databases."
"What is the primary purpose of AWS Directory Service?","To manage and connect to directories in the AWS Cloud","To monitor the health of EC2 instances","To provide a content delivery network","To manage S3 buckets","AWS Directory Service provides a way to connect your AWS resources with an existing on-premises Active Directory or to set up a new directory in the AWS Cloud."
"Which AWS Directory Service option allows you to connect your AWS resources to an existing on-premises Microsoft Active Directory?","AD Connector","Simple AD","Managed Microsoft AD","Cloud Directory","AD Connector creates a proxy to your existing on-premises AD, allowing AWS resources to use your existing user identities."
"Which AWS Directory Service option is a directory-aware application registry that enables you to describe, register, and consume shared services across AWS accounts?","Cloud Directory","AD Connector","Simple AD","Managed Microsoft AD","Cloud Directory is a highly scalable directory store with native hierarchical support, enabling application-specific directories."
"Which AWS Directory Service option offers a low-cost, basic Active Directory-compatible directory in the cloud?","Simple AD","Managed Microsoft AD","AD Connector","Cloud Directory","Simple AD is a basic, low-cost directory suitable for smaller organisations or development environments."
"Which AWS Directory Service option provides a fully managed Microsoft Active Directory in the AWS Cloud?","Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory","Managed Microsoft AD is a fully managed service that allows you to run actual Microsoft Active Directory in AWS."
"With AWS Directory Service, what is the benefit of using Managed Microsoft AD?","It allows you to use existing on-premises Active Directory users and groups with AWS applications","It offers the lowest possible cost for directory services","It's self-managed, giving you full control","It supports directory-aware applications only","Managed Microsoft AD eliminates the operational overhead of managing your own AD infrastructure."
"When using AD Connector, what is the critical network requirement?","Connectivity to your on-premises Active Directory domain controllers","Connectivity to all AWS Regions","Public internet access for all domain controllers","No network connectivity is required","AD Connector requires network connectivity to your on-premises Active Directory so that AWS resources can authenticate against it."
"Which AWS Directory Service option is most suitable for applications that require a flexible, hierarchical directory structure?","Cloud Directory","Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory is designed for applications that need flexible schemas and hierarchical data organisation."
"Which of the following is a typical use case for AWS Directory Service's AD Connector?","Extending your existing on-premises Active Directory to AWS","Creating a new, isolated Active Directory environment in AWS","Storing application configuration data","Managing DNS records","AD Connector allows you to extend your on-premises Active Directory to the cloud, enabling seamless authentication."
"What is the primary benefit of using AWS Directory Service for centralised user management?","Simplified access control and consistent user identities across AWS resources","Automated patching of EC2 instances","Real-time monitoring of application performance","Cost optimisation for S3 storage","Centralised user management simplifies security administration and ensures consistent access control across AWS resources."
"Which AWS Directory Service option supports multi-factor authentication (MFA) directly?","Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory","Managed Microsoft AD can be configured to use MFA for enhanced security."
"How does AWS Directory Service help in meeting compliance requirements?","By providing centralised audit logging and access control","By automatically encrypting all data at rest","By performing penetration testing on your infrastructure","By managing your AWS IAM roles","Centralised logging and access control through AWS Directory Service helps in meeting compliance requirements related to user management and security."
"What is one of the primary advantages of using AWS Managed Microsoft AD over self-managing Active Directory on EC2?","Reduced operational overhead and simplified management","Lower cost for small deployments","Greater control over the underlying operating system","Direct access to the AD database","Managed Microsoft AD offloads the operational burden of managing AD infrastructure to AWS."
"Which AWS service can you use to integrate your AWS Directory Service with applications?","AWS IAM","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS IAM can be used to integrate your Directory Service identities with AWS services and applications, allowing you to manage permissions and access."
"When using AWS Directory Service, how do you manage users and groups in Managed Microsoft AD?","Using standard Active Directory administration tools","Using AWS IAM roles and policies","Using AWS CloudFormation templates","Using the AWS CLI exclusively","Managed Microsoft AD uses standard Active Directory administration tools (e.g., Active Directory Users and Computers) for user and group management."
"What is the purpose of trust relationships in AWS Directory Service, specifically Managed Microsoft AD?","To allow users in one directory to access resources in another directory","To encrypt data in transit between directories","To control network access between directories","To automatically replicate data between directories","Trust relationships enable cross-directory access, allowing users in one directory to authenticate and access resources in another trusted directory."
"You have an existing on-premises Active Directory and want to allow users to authenticate to AWS resources using their existing credentials. Which AWS Directory Service option is the best choice?","AD Connector","Simple AD","Managed Microsoft AD","Cloud Directory","AD Connector is designed for connecting to an existing on-premises Active Directory, allowing users to use their existing credentials to access AWS resources."
"What is a key difference between Simple AD and Managed Microsoft AD in AWS Directory Service?","Managed Microsoft AD provides a fully managed Active Directory environment, while Simple AD is a basic, lower-cost option.","Simple AD supports multi-factor authentication, while Managed Microsoft AD does not.","Managed Microsoft AD integrates directly with AWS IAM, while Simple AD does not.","Simple AD allows you to extend your on-premises AD to AWS, while Managed Microsoft AD does not.","Managed Microsoft AD is a fully managed Active Directory service, while Simple AD provides basic AD-compatible features at a lower cost."
"What is the role of AWS Cloud Directory in relation to other AWS Directory Service options?","It is used for creating directory-based applications with custom schemas.","It is used for connecting to on-premises Active Directory.","It is used for providing a fully managed Active Directory environment.","It is used for providing a simple, low-cost Active Directory solution.","Cloud Directory is designed for developers who need to build directory-based applications with flexible schemas and hierarchical data."
"In AWS Directory Service, what is the significance of the 'Schema' in Cloud Directory?","It defines the structure and attributes of objects stored in the directory.","It defines the network configuration for the directory.","It defines the replication strategy for the directory.","It defines the backup policy for the directory.","The schema in Cloud Directory defines the structure and attributes of the data stored, allowing for flexible and custom directory structures."
"Which AWS service is commonly used to access resources within an AWS Directory Service domain after a user has authenticated?","AWS IAM","AWS CloudTrail","AWS CloudWatch","AWS Config","Once a user is authenticated by AWS Directory Service, AWS IAM is used to manage access permissions to AWS resources based on the user's identity and group memberships."
"What is the benefit of integrating AWS Directory Service with Amazon WorkSpaces?","Allows users to use their existing directory credentials to log in to WorkSpaces.","Automatically patches WorkSpaces instances.","Monitors the health of WorkSpaces instances.","Optimises the cost of WorkSpaces storage.","Integrating AWS Directory Service with Amazon WorkSpaces enables seamless authentication for users using their existing directory credentials."
"When choosing between Simple AD and Managed Microsoft AD, which factor should you consider if you need to support Group Policy Objects (GPOs)?","Managed Microsoft AD","Simple AD","Both support GPOs equally","Neither support GPOs","Managed Microsoft AD is a fully managed Microsoft Active Directory, and therefore supports Group Policy Objects (GPOs)."
"What is a key security advantage of using AD Connector over manually managing Active Directory on EC2 instances?","AD Connector does not store or cache passwords in the AWS Cloud.","AD Connector automatically encrypts all data at rest.","AD Connector automatically detects and prevents security threats.","AD Connector provides built-in intrusion detection and prevention.","AD Connector acts as a proxy to your on-premises AD, it does not store or cache passwords in the cloud, enhancing security."
"You are using AWS Directory Service AD Connector. What happens if the connection to your on-premises Active Directory is temporarily lost?","Users will not be able to authenticate to AWS resources until the connection is restored.","Users will be automatically switched to a backup directory service.","Users will be prompted to create new local AWS credentials.","Users will be granted temporary access based on cached credentials.","If the connection to on-premises AD is lost, users will not be able to authenticate until the connection is restored because AD Connector relies on the on-premises directory for authentication."
"How can you monitor the health and performance of your AWS Directory Service instances?","Using Amazon CloudWatch metrics and logs","Using AWS Trusted Advisor recommendations","Using AWS Config rules","Using AWS Systems Manager Automation","Amazon CloudWatch provides metrics and logs that can be used to monitor the health and performance of your AWS Directory Service instances."
"What is a common reason for choosing AWS Directory Service Managed Microsoft AD over setting up a Windows Server EC2 instance and configuring Active Directory yourself?","Reduced administrative overhead and automatic patching","Lower overall cost","Greater control over the underlying hardware","Direct access to the Active Directory database files","Managed Microsoft AD reduces administrative overhead by offloading management tasks, such as patching and backups, to AWS."
"Which of the following is NOT a benefit of using AWS Directory Service?","Providing a managed DNS service","Centralised user management","Simplified access control","Integration with AWS services","AWS Directory Service focuses on directory management and access control, not providing a managed DNS service (Route 53 does that)."
"What is the maximum number of objects that can be stored in a Simple AD directory?","5000","10000","25000","Unlimited","Simple AD has a limit of 5,000 objects (users, groups, computers, etc.)."
"Which AWS service can you use to create an identity provider (IdP) that you can then integrate with AWS Directory Service?","AWS IAM Identity Center (Successor to AWS SSO)","AWS Certificate Manager","AWS CloudHSM","AWS Artifact","AWS IAM Identity Center can be used to create an identity provider that can be integrated with AWS Directory Service to centralise identity management."
"What is the purpose of the AWS Directory Service 'Schema Extensions' feature in Managed Microsoft AD?","To add custom attributes to user and group objects","To increase the storage capacity of the directory","To improve the performance of directory queries","To enhance the security of directory communication","Schema Extensions allow you to add custom attributes to user and group objects, enabling you to store additional information relevant to your applications."
"What is a possible use case for AWS Directory Service when integrated with Amazon QuickSight?","Enabling users to log in to QuickSight using their directory credentials","Automatically generating QuickSight dashboards","Encrypting QuickSight data at rest","Scaling QuickSight capacity based on directory load","Integrating AWS Directory Service with Amazon QuickSight allows users to authenticate using their existing directory credentials, simplifying access."
"How does AWS Directory Service facilitate single sign-on (SSO) for users accessing AWS resources?","By integrating with identity providers (IdPs) using protocols like SAML","By automatically generating AWS IAM roles for each user","By providing a built-in password management system","By encrypting all communication between users and AWS services","AWS Directory Service integrates with IdPs using protocols like SAML to enable SSO, allowing users to access AWS resources with their existing credentials."
"What is a key benefit of using AWS Directory Service Managed Microsoft AD in a hybrid cloud environment?","It allows you to seamlessly extend your on-premises Active Directory to the AWS Cloud","It provides the lowest cost option for directory services in AWS","It allows you to completely replace your on-premises Active Directory","It eliminates the need for any network connectivity between AWS and your on-premises environment","Managed Microsoft AD enables seamless extension of your on-premises Active Directory to the AWS Cloud, supporting hybrid cloud deployments."
"What type of security measures should you implement when using AWS Directory Service AD Connector to connect to your on-premises Active Directory?","Secure network connectivity (e.g., VPN or Direct Connect) and strong passwords","Multi-factor authentication for all users","Regular penetration testing of your AWS environment","Physical security measures for AWS data centres","Secure network connectivity (VPN or Direct Connect) is crucial to protect the communication between AD Connector and your on-premises Active Directory."
"When configuring AWS Directory Service, what is the purpose of specifying a DNS server address?","To allow the directory to resolve domain names for AWS resources","To encrypt DNS traffic between the directory and AWS resources","To configure the directory to act as a DNS server for your VPC","To enable dynamic DNS updates for AWS resources","Specifying a DNS server address allows the directory to resolve domain names for AWS resources within your VPC."
"You are planning to migrate your on-premises applications to AWS. How can AWS Directory Service help simplify user management during the migration process?","By allowing you to use your existing Active Directory identities in AWS","By automatically migrating user data from your on-premises directory to AWS","By providing a tool to automatically rewrite application code to use AWS IAM","By managing the network configuration of your migrated applications","AWS Directory Service allows you to use your existing Active Directory identities in AWS, simplifying user management during migration."
"In AWS Directory Service, what is the primary function of a 'domain join' operation?","To associate an EC2 instance with a directory for authentication and authorisation","To create a trust relationship between two directories","To encrypt data in transit between directories","To configure network access control lists for the directory","A 'domain join' operation associates an EC2 instance with a directory, enabling it to authenticate users and enforce group policies."
"Which of the following is a key consideration when choosing the appropriate AWS Directory Service option for your organisation?","Your existing directory infrastructure and your application requirements","The geographical location of your users","The number of EC2 instances in your environment","The types of databases used by your applications","Your existing directory infrastructure and the specific requirements of your applications are crucial factors when choosing the right AWS Directory Service option."
"What is the main difference between AWS IAM and AWS Directory Service?","IAM manages AWS resource access, while Directory Service manages user identities and authentication.","IAM manages user identities, while Directory Service manages AWS resource access.","IAM is a free service, while Directory Service is a paid service.","IAM is used for on-premises resources, while Directory Service is used for AWS resources.","AWS IAM manages access to AWS resources, while AWS Directory Service focuses on user identities and authentication, often integrating with existing directory services."
"What is the pricing model for AWS Directory Service Managed Microsoft AD?","Based on the size of the directory and the number of directory objects","Based on the number of users in the directory","Based on the amount of data stored in the directory","Based on the number of API calls made to the directory","The pricing for Managed Microsoft AD is primarily based on the size of the directory instance (Small or Standard) and the number of directory objects."
"When using AWS Directory Service AD Connector, what is a common issue that can prevent successful connectivity to your on-premises Active Directory?","Firewall rules blocking communication between AWS and your on-premises network","Incorrectly configured AWS IAM roles","Outdated Active Directory schema","Insufficient CPU resources on the AD Connector instance","Firewall rules blocking communication between AWS and your on-premises network are a common cause of connectivity issues with AD Connector."
"You want to enable users to log in to AWS Management Console using their existing on-premises Active Directory credentials. Which AWS service can help you achieve this?","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","AWS Config","AWS Systems Manager","AWS IAM Identity Center (Successor to AWS SSO) allows you to connect your on-premises Active Directory to AWS and enable users to log in to the AWS Management Console using their existing credentials."
"What is the purpose of AWS Directory Service's 'Trust Relationship' feature?","To allow users in one domain to access resources in another domain","To encrypt data in transit between domains","To automatically replicate data between domains","To provide backup and disaster recovery for domains","The 'Trust Relationship' feature allows users in one domain to access resources in another trusted domain, enabling cross-domain authentication and authorisation."
"What is the maximum number of domain controllers that can be deployed in a Managed Microsoft AD directory?","Two","Three","Four","Five","Managed Microsoft AD automatically deploys two domain controllers for redundancy."
"What does LDAPS (Lightweight Directory Access Protocol Secure) provide in the context of AWS Directory Service?","Encrypted communication between clients and the directory server","Load balancing across multiple directory servers","Automatic failover in case of directory server failure","Enhanced logging and auditing of directory access","LDAPS provides encrypted communication between clients and the directory server, ensuring the confidentiality of directory traffic."
"What is the main purpose of the AWS Schema Conversion Tool (SCT) when migrating databases to AWS in conjunction with AWS Directory Service?","To convert database schemas to be compatible with the directory structure","To convert user credentials from the existing database to Active Directory","To automate the process of domain joining database servers to the directory","To encrypt the data being migrated from the on-premises database to AWS","The AWS Schema Conversion Tool is primarily used to convert database schemas for compatibility during database migration, not directly related to AWS Directory Service features, but can be used to migrate data which relies on directory service."
"Which AWS service can be used to control which AWS resources users can access after they have been authenticated by AWS Directory Service?","AWS Identity and Access Management (IAM)","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS IAM is used to manage access control policies and permissions, determining which AWS resources users can access after they have been authenticated by AWS Directory Service."
"What is a key consideration when implementing AWS Directory Service in a multi-account AWS environment?","Centralising directory management in a single AWS account","Duplicating the directory service in each AWS account","Using different directory service options in each AWS account","Allowing each team to manage their own directory service","Centralising directory management in a single AWS account simplifies administration and ensures consistent user identities across the entire multi-account environment."
"What is the primary purpose of AWS Directory Service?","To integrate your AWS resources with an existing on-premises Microsoft Active Directory or to set up a new directory in the AWS Cloud.","To manage AWS Identity and Access Management (IAM) users and roles.","To provide a content delivery network for distributing static content.","To automate infrastructure provisioning.","AWS Directory Service allows you to use your existing corporate credentials to access AWS resources and centrally manage identities."
"Which of the following is NOT a directory type offered by AWS Directory Service?","AWS Managed OpenLDAP","AD Connector","AWS Managed Microsoft AD","Simple AD","AWS Managed OpenLDAP is not directly a directory type offered by AWS Directory Service. Although, it can integrate with Active Directory via AD Connector."
"What is the function of AD Connector in AWS Directory Service?","It provides a proxy to your on-premises Active Directory without caching any information in the cloud.","It creates a fully managed Active Directory in the AWS Cloud.","It's used to manage users in AWS IAM.","It provides DNS services.","AD Connector allows you to connect your existing on-premises Active Directory to AWS without replicating the directory data."
"What is the benefit of using AWS Managed Microsoft AD?","You get a fully managed Active Directory running in the AWS Cloud, eliminating the need to manage your own AD infrastructure.","It allows you to manage AWS IAM roles directly from your on-premises Active Directory.","It automatically encrypts all data stored in Amazon S3.","It provides serverless compute capabilities.","AWS Managed Microsoft AD offers a fully managed Active Directory, reducing operational overhead."
"Which of the following is a key feature of AWS Managed Microsoft AD?","Trust relationships with on-premises AD domains","Simplified patching of EC2 instances","Centralised AWS account cost reporting","Automated creation of AWS IAM policies","AWS Managed Microsoft AD supports trust relationships, enabling users in your on-premises AD to access AWS resources."
"When using AWS Directory Service, what does a trust relationship between two Active Directory domains enable?","Users in one domain can authenticate and access resources in the other domain.","It automatically migrates data between the domains.","It merges the two domains into a single domain.","It provides a backup of one domain in the other.","Trust relationships allow users in one domain to authenticate and access resources in another domain without requiring separate credentials."
"What is the main use case for Simple AD in AWS Directory Service?","For basic directory functionality when you don't need advanced features like group policy.","For high-availability Active Directory deployments.","For connecting to multiple on-premises domains.","For managing Linux user accounts.","Simple AD is a basic directory service suitable for small organisations or test environments that don't require advanced AD features."
"You need to allow your on-premises users to access AWS resources using their existing credentials, but you don't want to replicate your entire Active Directory to the cloud. Which AWS Directory Service option is best?","AD Connector","AWS Managed Microsoft AD","Simple AD","AWS IAM","AD Connector provides a proxy to your on-premises AD without storing any directory data in the cloud."
"What is the purpose of a conditional forwarder when using AD Connector with AWS Directory Service?","To forward DNS queries for your on-premises domain to your on-premises DNS servers.","To forward all traffic to AWS CloudTrail.","To encrypt all communication between AD Connector and your on-premises domain.","To automatically create AWS IAM roles.","Conditional forwarders ensure that DNS queries for your on-premises domain are resolved by your on-premises DNS servers."
"Which of the following AWS services can be directly integrated with AWS Directory Service for authentication and authorisation?","Amazon RDS for SQL Server","Amazon S3","Amazon CloudFront","AWS Lambda","Amazon RDS for SQL Server can use Active Directory authentication, allowing users to use their AD credentials to access the database."
"What is the primary advantage of using AWS Directory Service for centralised identity management?","It allows you to use existing on-premises credentials for accessing AWS resources, simplifying user management and improving security.","It provides unlimited storage for user data.","It automatically optimises EC2 instance performance.","It allows you to host static websites.","Centralised identity management with AWS Directory Service reduces administrative overhead and improves security by leveraging existing credentials."
"Which AWS service would you use to extend your on-premises Active Directory to the AWS Cloud without creating a fully managed domain controller in AWS?","AD Connector","AWS Managed Microsoft AD","Simple AD","Amazon Inspector","AD Connector allows you to use your on-premises Active Directory as your directory store while using AWS resources."
"When setting up AWS Managed Microsoft AD, what is a critical requirement for your VPC?","It must have at least two subnets in different Availability Zones.","It must have an internet gateway attached.","It must have a single public subnet.","It must be configured with IPv6.","AWS Managed Microsoft AD requires at least two subnets in different Availability Zones to ensure high availability."
"What is the key difference between AWS Managed Microsoft AD and Simple AD?","AWS Managed Microsoft AD provides a fully managed Active Directory service, while Simple AD provides a basic directory service.","AWS Managed Microsoft AD supports only Linux-based applications, while Simple AD supports Windows-based applications.","AWS Managed Microsoft AD is free, while Simple AD requires a subscription.","AWS Managed Microsoft AD automatically scales compute resources, while Simple AD doesn't.","AWS Managed Microsoft AD offers a full Active Directory implementation, while Simple AD provides a more basic directory service with limited features."
"Which of the following is NOT a valid use case for AWS Directory Service?","Managing user authentication for web applications hosted on Amazon EC2.","Providing centralised password management for on-premises and AWS resources.","Hosting static websites.","Integrating with other AWS services for access control.","Hosting static websites is not a valid use case for AWS Directory Service."
"What type of protocol does AD Connector use to communicate with your on-premises Active Directory?","LDAPS","HTTPS","SMTP","SFTP","AD Connector uses LDAPS (LDAP over SSL) to securely communicate with your on-premises Active Directory."
"What is the purpose of the 'Schema extensions' feature in AWS Managed Microsoft AD?","To allow you to add custom attributes and classes to your Active Directory schema.","To allow you to manage DNS records in your Active Directory.","To allow you to create custom AWS IAM policies.","To allow you to extend the retention period of audit logs.","Schema extensions allow you to customise your Active Directory schema with custom attributes and classes."
"When using AWS Directory Service, what is the relationship between security groups and directory users?","You can control access to AWS resources by granting permissions to security groups containing your directory users.","Security groups are used to manage access to the Directory Service itself.","Security groups automatically sync with on-premises AD groups.","Security groups are not related to directory users.","AWS Security Groups can be used with users/groups managed in Directory Service to control access to AWS resources."
"You are planning to migrate your on-premises applications to AWS and want to minimise the effort required to manage user identities. Which AWS Directory Service option would provide the tightest integration with your existing Active Directory infrastructure?","AWS Managed Microsoft AD or AD Connector","Simple AD","AWS IAM","Amazon Cognito","AWS Managed Microsoft AD (for a fully managed AD) or AD Connector (to connect to an existing AD) provide the tightest integration with on-premises Active Directory."
"What is the primary function of AWS Single Sign-On (SSO) in relation to AWS Directory Service?","AWS SSO can be integrated with AWS Directory Service to provide users with seamless access to multiple AWS accounts and applications using their directory credentials.","AWS SSO replaces AWS Directory Service for user authentication.","AWS SSO provides a network firewall for AWS Directory Service.","AWS SSO is only used for granting access to Amazon S3 buckets.","AWS SSO works with AWS Directory Service to provide users with a single set of credentials for accessing multiple AWS accounts and applications."
"If you need to grant users access to AWS resources based on their existing on-premises Active Directory groups, which AWS Directory Service option would be most suitable?","AD Connector or AWS Managed Microsoft AD","Simple AD","AWS IAM","Amazon Cognito","AD Connector and AWS Managed Microsoft AD allow you to leverage your existing on-premises Active Directory groups for access control in AWS."
"What is a typical use case for using AD Connector along with AWS Managed Microsoft AD?","To create a trust relationship between your on-premises Active Directory and AWS Managed Microsoft AD.","To replicate data between Amazon S3 buckets.","To automatically create Amazon EC2 instances.","To provision AWS Lambda functions.","You can use AD Connector to create a trust relationship, enabling authentication and resource access across both environments."
"How does AWS Directory Service help with compliance requirements?","By providing a centralised audit trail of user authentication and access activities.","By automatically encrypting all data stored in AWS.","By automatically patching EC2 instances.","By providing unlimited storage for log files.","AWS Directory Service provides a centralised audit trail, which can be used for compliance purposes."
"What is a potential disadvantage of using AWS Managed Microsoft AD compared to AD Connector?","AWS Managed Microsoft AD involves managing a separate Active Directory domain in the AWS Cloud, whereas AD Connector connects to your existing on-premises AD.","AWS Managed Microsoft AD is more expensive than AD Connector.","AWS Managed Microsoft AD is less secure than AD Connector.","AWS Managed Microsoft AD is more difficult to configure than AD Connector.","AWS Managed Microsoft AD creates another AD forest to manage."
"Which of the following AWS services can be used to monitor the performance and health of your AWS Directory Service deployments?","Amazon CloudWatch","AWS CloudTrail","AWS Config","Amazon Inspector","Amazon CloudWatch can be used to monitor various metrics related to AWS Directory Service."
"How can you enable Multi-Factor Authentication (MFA) for users accessing AWS resources through AWS Directory Service?","By integrating AWS Directory Service with AWS IAM and enabling MFA for the IAM users or by integrating with a RADIUS server.","By directly configuring MFA within AWS Directory Service.","By using Amazon Cognito for MFA.","By using AWS CloudTrail for MFA.","MFA can be enabled either by integrating with IAM or a RADIUS server."
"What is the purpose of a two-way trust relationship in AWS Directory Service?","To allow users in both domains to access resources in either domain.","To only allow users in one domain to access resources in the other domain.","To only allow administrators to manage both domains.","To replicate data between the two domains.","Two-way trusts allow bidirectional access, users from each domain can access resources in the other domain."
"When using AD Connector, what happens if the connection to your on-premises Active Directory is temporarily lost?","Users will not be able to authenticate to AWS resources until the connection is restored.","Users can still authenticate using cached credentials.","Users will be automatically redirected to Amazon Cognito.","Users will be prompted to create new AWS IAM accounts.","If the connection to the on-premise Active Directory is lost then users can't authenticate to AWS resources until the connection is restored."
"You need to implement fine-grained access control for your AWS resources based on Active Directory group memberships. Which AWS service would you use in conjunction with AWS Directory Service?","AWS IAM","Amazon VPC","AWS CloudTrail","Amazon Inspector","AWS IAM can use information from AWS Directory Service to manage user access to AWS resources."
"What is the primary benefit of using AWS Directory Service over manually creating and managing your own Active Directory domain controllers on EC2 instances?","AWS Directory Service provides a fully managed service, reducing operational overhead and simplifying management.","AWS Directory Service provides unlimited storage for directory data.","AWS Directory Service automatically scales EC2 instance resources.","AWS Directory Service is free to use.","AWS Directory Service provides a fully managed service."
"Which of the following AWS services can you integrate with AWS Directory Service to provide a seamless single sign-on (SSO) experience for users?","AWS IAM Identity Center (Successor to AWS SSO)","Amazon Cognito","AWS CloudTrail","Amazon Inspector","AWS IAM Identity Center can be integrated with AWS Directory Service to provide seamless SSO for users."
"What is the role of DNS when configuring AWS Directory Service with AD Connector?","To resolve domain names for your on-premises Active Directory and AWS resources.","To encrypt network traffic between AWS and your on-premises network.","To automatically create EC2 instances.","To provide a content delivery network.","DNS is crucial for resolving domain names and ensuring proper communication between AD Connector, your on-premises AD, and AWS resources."
"What is the recommended way to manage Group Policy Objects (GPOs) in AWS Managed Microsoft AD?","Use the Remote Server Administration Tools (RSAT) provided by Microsoft.","Use the AWS Management Console.","Use the AWS CLI.","Use AWS CloudShell.","The standard way to manage GPOs in an Active Directory environment is to use RSAT."
"You are migrating an application to AWS that relies heavily on Kerberos authentication. Which AWS Directory Service option is most suitable?","AWS Managed Microsoft AD","Simple AD","AD Connector","AWS IAM","AWS Managed Microsoft AD supports Kerberos authentication."
"What is the best practice for securing your AWS Managed Microsoft AD domain controllers?","Ensure that the domain controllers are placed in private subnets with restricted network access.","Place the domain controllers in public subnets with open internet access.","Disable all inbound traffic to the domain controllers.","Use AWS Shield to protect the domain controllers.","Domain controllers should reside in private subnets that limit external access to them."
"You need to create a new Active Directory domain in AWS and want to minimise administrative overhead. Which AWS Directory Service option is the best choice?","AWS Managed Microsoft AD","AD Connector","Simple AD","AWS IAM","AWS Managed Microsoft AD is a fully managed service, which minimises the overhead of managing an Active Directory environment."
"What is the maximum number of objects that can be stored in a Simple AD directory?","500","5,000","50,000","Unlimited","Simple AD has a limit of 500 objects."
"Which AWS service can you use to centralise access management across multiple AWS accounts and on-premises applications, leveraging your existing AWS Directory Service deployment?","AWS IAM Identity Center (Successor to AWS SSO)","Amazon Cognito","AWS CloudTrail","Amazon Inspector","IAM Identity Center (Successor to AWS SSO) is designed for centralising access management."
"When using AWS Directory Service, how can you ensure high availability of your directory service?","By deploying AWS Managed Microsoft AD across multiple Availability Zones.","By using AWS CloudTrail to monitor directory service activity.","By using AWS Config to manage directory service configuration.","By enabling AWS Shield for directory service protection.","Deploying across Availability Zones is the typical means of ensuring high availability."
"What is the primary difference between AD Connector and AWS Managed Microsoft AD in terms of directory data storage?","AD Connector doesn't store any directory data in AWS, while AWS Managed Microsoft AD stores a fully managed copy of the directory in AWS.","AD Connector stores a full copy of the directory in AWS, while AWS Managed Microsoft AD stores only metadata.","AD Connector encrypts all directory data in transit, while AWS Managed Microsoft AD doesn't.","AD Connector is more expensive than AWS Managed Microsoft AD.","The key difference is where the directory data is stored."
"Which of the following is a critical requirement when setting up AD Connector?","You must have a properly configured and reachable DNS server in your on-premises network.","You must have a public IP address assigned to the AD Connector instance.","You must use a specific EC2 instance type for AD Connector.","You must create a separate AWS account for AD Connector.","AD Connector relies on DNS to function correctly."
"What is the recommended method for joining an EC2 instance to an AWS Managed Microsoft AD domain?","Use the AWS Systems Manager (SSM) Run Command with the 'AWS-JoinDirectoryServiceDomain' document.","Manually configure the instance to join the domain.","Use AWS CloudFormation to automate the domain join process.","Use AWS Config to automatically join the domain.","The SSM Run Command with the 'AWS-JoinDirectoryServiceDomain' is an automated means of joining a Windows Server to a domain."
"What is the maximum number of users supported by Simple AD?","1000","5000","10000","There is no limit","Simple AD supports a maximum of 5000 users."
"What is the purpose of Directory Federation with AWS IAM Identity Center (Successor to AWS SSO) when using AWS Directory Service?","To provide users with a single set of credentials to access AWS accounts and applications.","To encrypt data in transit between AWS services.","To automate infrastructure provisioning.","To manage AWS IAM roles.","Directory Federation with AWS IAM Identity Center allows centralised authentication."
"In an AWS Directory Service deployment, what is the purpose of establishing a trust relationship between an on-premises Active Directory and an AWS Managed Microsoft AD domain?","To enable users in the on-premises Active Directory to access resources in the AWS Managed Microsoft AD domain and vice versa.","To encrypt all data stored in both directories.","To replicate all data between the two directories.","To automatically migrate users and groups from the on-premises Active Directory to the AWS Managed Microsoft AD domain.","Trust relationships allow you to define which domains are able to communicate."
"You have an application running on AWS that requires LDAP authentication. Which AWS Directory Service option would be most appropriate if you don't require the full functionality of Active Directory?","Simple AD","AD Connector","AWS Managed Microsoft AD","AWS IAM","Simple AD is designed for basic LDAP authentication purposes."
"What is the function of the AWS Systems Manager (SSM) Agent in relation to managing EC2 instances joined to an AWS Managed Microsoft AD domain?","The SSM Agent allows you to manage and configure EC2 instances, including joining them to the domain, running commands, and applying patches.","The SSM Agent provides a network firewall for EC2 instances.","The SSM Agent automatically encrypts all data stored on EC2 instances.","The SSM Agent provides a content delivery network for EC2 instances.","The SSM Agent enables centralised management of EC2 instances."
"What is the best practice for backing up your AWS Managed Microsoft AD domain?","AWS automatically creates daily snapshots of your AWS Managed Microsoft AD domain.","You must manually create backups using the Remote Server Administration Tools (RSAT).","You must use AWS Backup to create backups of your domain controllers.","You must create a read replica of your AWS Managed Microsoft AD domain.","AWS automatically backs up your Microsoft AD domain."
"Which AWS service should you use if you want to provide your users with single sign-on (SSO) access to multiple AWS accounts and applications using their existing Active Directory credentials, without replicating the directory to the cloud?","AWS IAM Identity Center (Successor to AWS SSO) with AD Connector.","Amazon Cognito.","AWS CloudTrail.","AWS Config.","IAM Identity Center with AD Connector allows for SSO without replicating the directory to the cloud."
"What is the primary purpose of AWS Directory Service?","To connect your AWS resources to an existing on-premises Microsoft Active Directory or to set up a new directory in the AWS Cloud.","To provide object storage for directory data.","To manage IAM roles and permissions.","To provide a caching layer for database queries.","AWS Directory Service enables you to use your existing corporate credentials to access AWS applications, simplifying user management and authentication."
"Which AWS Directory Service offering allows you to use your existing on-premises Microsoft Active Directory to manage users and groups?","AD Connector","Simple AD","AWS Managed Microsoft AD","Cloud Directory","AD Connector allows you to proxy directory requests to your existing on-premises Active Directory without caching any information in the cloud."
"Which AWS Directory Service option is suitable for small businesses with fewer than 5000 users who need a basic Active Directory compatible directory?","Simple AD","AD Connector","AWS Managed Microsoft AD","Cloud Directory","Simple AD is a cost-effective directory service that is suitable for smaller organisations that need basic Active Directory features."
"When using AWS Managed Microsoft AD, what does AWS handle for you?","Patching and maintaining the domain controllers.","Managing the schema extensions.","Configuring Group Policy Objects (GPOs).","Deploying client applications to joined instances.","AWS manages the infrastructure and software for AWS Managed Microsoft AD, including patching and maintaining domain controllers."
"Which AWS Directory Service option requires you to have an existing on-premises Active Directory?","AD Connector","Simple AD","AWS Managed Microsoft AD","Cloud Directory","AD Connector requires you to have an existing on-premises Active Directory as it proxies requests to it."
"What is a key benefit of using AWS Directory Service for applications running on EC2 instances?","Centralised user management and authentication.","Automated scaling of EC2 instances.","Cost optimisation for EC2 storage.","Enhanced network security for EC2 traffic.","AWS Directory Service provides centralised user management and authentication for applications running on EC2 instances."
"Which AWS Directory Service type is NOT Active Directory compatible?","Cloud Directory","Simple AD","AWS Managed Microsoft AD","AD Connector","Cloud Directory is not Active Directory compatible, it's a cloud-native directory service."
"What type of trust relationship can be established between AWS Managed Microsoft AD and your on-premises Active Directory?","Two-way trust","One-way outgoing trust","One-way incoming trust","No trust relationship supported","AWS Managed Microsoft AD supports two-way trust relationships with your on-premises Active Directory."
"Which of the following best describes the AD Connector deployment model?","Proxy requests to an existing on-premises Active Directory.","Provides a fully managed Active Directory in the AWS Cloud.","Offers a simplified Active Directory-compatible service in the cloud.","Acts as an Identity Provider (IdP) for web applications.","AD Connector is a proxy service; it doesn't host the directory itself, but connects to your existing on-premises Active Directory."
"What is a primary use case for AWS Cloud Directory?","Building directory-based applications that are not dependent on Active Directory.","Replacing an existing on-premises Active Directory infrastructure.","Providing single sign-on (SSO) for SaaS applications.","Managing user permissions for S3 buckets.","Cloud Directory is designed for developers who need to create directory-based applications with flexible schemas."
"Which AWS Directory Service option would be most suitable for an organisation needing to join Linux instances to a domain?","AWS Managed Microsoft AD","AD Connector (with a pre-existing AD)","Simple AD","Both AWS Managed Microsoft AD and AD Connector (with a pre-existing AD)","Both AWS Managed Microsoft AD and AD Connector (with a pre-existing AD) can be used to join Linux instances to a domain."
"Which of the following is a key benefit of using AWS Directory Service compared to manually managing Active Directory on EC2 instances?","Reduced operational overhead and simplified management.","Improved network bandwidth.","Automated scaling of database servers.","Enhanced data encryption.","AWS Directory Service reduces operational overhead by managing the infrastructure and software for you."
"What is the function of the Schema in AWS Cloud Directory?","Defines the structure and attributes of the directory data.","Manages user permissions for directory objects.","Provides a replication mechanism for directory data.","Encrypts directory data at rest.","The Schema in Cloud Directory defines the structure and attributes of the data stored in the directory."
"What is the main advantage of using AWS Managed Microsoft AD over Simple AD for a large enterprise?","AWS Managed Microsoft AD provides a fully managed Active Directory environment with more features and scalability.","Simple AD is cheaper than AWS Managed Microsoft AD.","Simple AD is easier to configure than AWS Managed Microsoft AD.","Simple AD supports multi-factor authentication, while AWS Managed Microsoft AD does not.","AWS Managed Microsoft AD offers a full suite of Active Directory features and is designed for larger enterprises needing scalability and control."
"Which AWS service can be used to synchronise user passwords between AWS Directory Service and other AWS services?","AWS IAM","AWS Secrets Manager","AWS SSO (Single Sign-On)","AWS CloudTrail","AWS SSO can be used to provide a single sign-on experience for users accessing multiple AWS services and applications."
"When using AD Connector, where are user credentials stored?","On your on-premises Active Directory domain controllers","In the AD Connector instance in AWS","In AWS IAM","In Amazon S3","AD Connector acts as a proxy, user credentials remain on your on-premises Active Directory domain controllers."
"Which AWS Directory Service option provides the most comprehensive set of Active Directory features?","AWS Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory","AWS Managed Microsoft AD offers a full suite of Active Directory features and is the most comprehensive option."
"What is a primary consideration when choosing between Simple AD and AWS Managed Microsoft AD?","The size and complexity of your organisation's Active Directory needs.","The geographic location of your users.","The programming language used by your applications.","The type of storage used by your databases.","Simple AD is suitable for smaller organisations, while AWS Managed Microsoft AD is designed for larger, more complex environments."
"You are planning to migrate your on-premises Active Directory to AWS. Which AWS Directory Service option offers the most direct migration path?","AWS Managed Microsoft AD","Simple AD","AD Connector","Cloud Directory","AWS Managed Microsoft AD allows you to create a fully managed Active Directory in AWS, making it a direct migration path."
"What is the purpose of setting up a trust relationship between your on-premises Active Directory and AWS Managed Microsoft AD?","To allow users in your on-premises domain to access resources in the AWS domain, and vice versa.","To encrypt network traffic between the two domains.","To synchronise data between the two domains.","To provide disaster recovery for your on-premises Active Directory.","A trust relationship allows users and groups in one domain to access resources in another domain."
"Which AWS Directory Service offering is most suitable for a small startup that requires basic directory functionality for its AWS applications?","Simple AD","AWS Managed Microsoft AD","AD Connector","Cloud Directory","Simple AD is designed for small organisations needing basic Active Directory compatible functionality."
"What is a key security consideration when using AD Connector?","Securing the network connection between AD Connector and your on-premises Active Directory.","Enabling multi-factor authentication for all users.","Encrypting data at rest in AD Connector.","Auditing user activity in AD Connector.","Securing the network connection is critical to prevent unauthorised access to your on-premises Active Directory."
"Which AWS service can be integrated with AWS Directory Service to provide single sign-on (SSO) to AWS resources?","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","Amazon Cognito","AWS Config","AWS IAM Identity Center (Successor to AWS SSO) integrates with AWS Directory Service to provide SSO access."
"What type of network connectivity is required between your on-premises network and AWS when using AD Connector?","A Virtual Private Network (VPN) or AWS Direct Connect.","An internet connection.","A public IP address for your domain controller.","No network connectivity is required.","AD Connector requires a VPN or Direct Connect connection to communicate with your on-premises Active Directory."
"What is the recommended deployment model for AD Connector in terms of availability?","Deploy AD Connector in multiple Availability Zones for high availability.","Deploy AD Connector in a single Availability Zone for cost optimisation.","Deploy AD Connector in your on-premises data centre.","AD Connector does not require high availability.","Deploying AD Connector in multiple Availability Zones ensures high availability in case of an Availability Zone outage."
"Which of the following actions is AWS responsible for when you use AWS Managed Microsoft AD?","Patching the domain controllers.","Managing Group Policy Objects (GPOs).","Managing the Active Directory schema.","Creating user accounts.","AWS takes care of patching the domain controllers, reducing the operational burden on the customer."
"What is the purpose of Directory Federation when using AWS Directory Service?","To enable users to use their existing corporate credentials to access AWS resources.","To encrypt data stored in the directory.","To replicate directory data across multiple regions.","To automate the creation of user accounts.","Directory Federation enables users to use their existing credentials, simplifying access management."
"What is the cost structure of AWS Directory Service (Simple AD and AWS Managed Microsoft AD)?","Pay-as-you-go based on the size and type of the directory.","A one-time setup fee plus a monthly maintenance fee.","Free to use with certain AWS services.","Flat monthly fee regardless of usage.","AWS Directory Service charges based on the directory type and size, following a pay-as-you-go model."
"When extending your on-premises Active Directory to AWS using AD Connector, what is a best practice for network security?","Use security groups to restrict traffic to and from the AD Connector instances.","Disable firewall rules on your domain controllers.","Expose your domain controllers directly to the internet.","Use a single subnet for all AD Connector instances.","Using security groups is a fundamental network security practice to control traffic."
"Which AWS service can you use to monitor the health and performance of your AWS Directory Service?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and monitoring capabilities for AWS Directory Service."
"If you need to change the domain name of your AWS Managed Microsoft AD directory, what should you do?","You need to create a new directory with the desired domain name and migrate your resources.","You can rename the existing directory directly through the AWS console.","Contact AWS support to request a domain name change.","Modify the DNS settings in your VPC.","Changing the domain name of an AWS Managed Microsoft AD directory is not directly supported; you would need to create a new directory."
"What is the maximum number of objects supported by Simple AD?","5,000","10,000","25,000","Unlimited","Simple AD supports a maximum of 5,000 directory objects."
"What is the purpose of the 'Schema extensions' feature in AWS Managed Microsoft AD?","To customise the Active Directory schema to support specific application requirements.","To improve the performance of directory queries.","To encrypt data stored in Active Directory.","To manage user permissions in Active Directory.","Schema extensions allow you to add custom attributes to objects in Active Directory."
"Which AWS Directory Service option allows you to create a multi-region Active Directory environment?","AWS Managed Microsoft AD with multi-region replication.","Simple AD with cross-region replication.","AD Connector with cross-region proxying.","Cloud Directory with global replication.","AWS Managed Microsoft AD supports multi-region replication for improved availability and disaster recovery."
"What is the benefit of using AWS Directory Service integration with Amazon RDS for SQL Server?","Allows you to use your Active Directory credentials to authenticate users to your SQL Server database.","Automates the backup and restore process for your SQL Server database.","Encrypts the data stored in your SQL Server database.","Improves the performance of your SQL Server database queries.","Directory Service integration with RDS enables Active Directory authentication."
"What is a key difference between AWS Cloud Directory and traditional directory services like Active Directory?","Cloud Directory is designed for cloud-native applications and offers more flexible schemas.","Cloud Directory is cheaper than Active Directory.","Cloud Directory is easier to manage than Active Directory.","Cloud Directory supports a wider range of operating systems than Active Directory.","Cloud Directory is designed for cloud-native applications and offers more flexible schemas compared to Active Directory."
"You need to provide temporary access to AWS resources for users in your on-premises Active Directory without creating IAM users. Which approach is best?","Using AD Connector with SAML federation to AWS.","Creating temporary IAM users for each on-premises user.","Sharing the root account credentials with the on-premises users.","Granting all on-premises users full administrator access to AWS.","Using AD Connector with SAML federation allows users to use their existing credentials to access AWS resources without creating IAM users."
"Which AWS service should you use to centrally manage access to multiple AWS accounts and applications using your AWS Directory Service?","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","Amazon Cognito","AWS Config","AWS IAM Identity Center (Successor to AWS SSO) enables centralised access management across multiple accounts and applications."
"What is a key benefit of using AWS Managed Microsoft AD with native AWS services like Amazon WorkSpaces?","Simplified user management and single sign-on (SSO) experience.","Automated scaling of WorkSpaces instances.","Cost optimisation for WorkSpaces storage.","Enhanced network security for WorkSpaces traffic.","AWS Managed Microsoft AD simplifies user management and provides SSO for WorkSpaces and other AWS services."
"Which of the following is a potential limitation of using AD Connector compared to AWS Managed Microsoft AD?","AD Connector relies on the availability and performance of your on-premises Active Directory.","AD Connector is more expensive than AWS Managed Microsoft AD.","AD Connector is more complex to configure than AWS Managed Microsoft AD.","AD Connector does not support multi-factor authentication.","AD Connector's performance is tied to your on-premises Active Directory's availability and performance."
"What is the purpose of the 'Trust Password' when setting up a trust relationship between AWS Managed Microsoft AD and your on-premises Active Directory?","To authenticate the connection between the two domains.","To encrypt data transferred between the two domains.","To manage user permissions across the two domains.","To synchronise passwords between the two domains.","The Trust Password authenticates the connection and establishes a secure communication channel between the domains."
"When deploying AWS Directory Service, what is the recommended approach for DNS configuration?","Configure your VPC DNS settings to forward DNS queries to your directory service.","Use the default AWS DNS servers.","Configure a custom DNS server in your VPC.","Disable DNS resolution in your VPC.","Forwarding DNS queries ensures that your instances can resolve domain names within your directory service."
"You need to ensure that your AWS Directory Service is highly available. What steps should you take?","Deploy the directory service across multiple Availability Zones.","Use a single Availability Zone for cost optimisation.","Enable cross-region replication for your directory service.","Manually back up your directory service on a regular basis.","Deploying across multiple Availability Zones provides redundancy and high availability."
"What is the purpose of the 'Directory Size' setting when creating a Simple AD directory?","Specifies the maximum number of objects that can be stored in the directory.","Determines the amount of storage allocated to the directory.","Sets the replication frequency for the directory data.","Controls the network bandwidth available to the directory.","The Directory Size setting limits the number of directory objects."
"Which of the following is NOT a feature of AWS Cloud Directory?","Support for LDAP.","Flexible Schema.","Hierarchical Data Organisation.","Fine-Grained Access Control.","Cloud Directory does not support the Lightweight Directory Access Protocol (LDAP)."
"When using AWS Managed Microsoft AD, what is the maximum number of domain controllers deployed by default in each Availability Zone?","Two","One","Three","Four","AWS Managed Microsoft AD deploys two domain controllers in each Availability Zone by default for redundancy."
"What is the significance of the CIDR block you specify when creating an AWS Managed Microsoft AD directory?","It determines the IP address range used by the directory service within your VPC.","It specifies the public IP address range for the directory service.","It controls the network bandwidth allocated to the directory service.","It defines the DNS domain name for the directory service.","The CIDR block defines the IP address range that the directory service will use within your VPC."
"Which AWS service can you use to manage and provision user access to applications and resources based on their roles and group memberships in AWS Directory Service?","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","Amazon Cognito","AWS Config","AWS IAM Identity Center (Successor to AWS SSO) enables centralised access management based on roles and group memberships."
"What is a key advantage of using AWS Directory Service over hosting your own directory service on EC2?","Reduced operational overhead and simplified management.","Greater control over the underlying infrastructure.","Lower cost compared to EC2 hosting.","Improved network performance.","AWS Directory Service reduces the burden of managing the underlying infrastructure, operating system, and directory software."