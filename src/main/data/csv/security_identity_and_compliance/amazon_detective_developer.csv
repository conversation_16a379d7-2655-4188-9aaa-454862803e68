"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Detective, what is the primary purpose of an investigation?","To analyse and visualise security data to identify the root cause of security issues.","To automatically remediate security findings.","To act as a firewall to block suspicious traffic.","To encrypt data at rest and in transit.","Detective helps you to investigate and understand security issues by visualising and analysing security data."
"Which data source is NOT automatically ingested by Amazon Detective?","AWS Config data","VPC Flow Logs","CloudTrail Logs","GuardDuty Findings","AWS Config data is not automatically ingested into Detective."
"In Amazon Detective, what is a 'behaviour graph'?","A pre-built database that contains security-related data.","A customisable dashboard that displays security findings.","A visual representation of interactions between AWS resources and users.","A set of predefined rules for detecting security threats.","A behaviour graph visually represents the interactions between entities like users, roles, and resources, enabling the identification of anomalous behaviours."
"What is the primary benefit of using Amazon Detective over manually analysing logs?","Detective automates the collection, processing and analysis of logs, saving time and effort.","Detective offers real-time remediation of security threats.","Detective is cheaper than manually analysing logs.","Detective can replace your existing SIEM solution.","Detective automates log ingestion, processing, and analysis, providing a faster and more efficient way to investigate security incidents."
"Which AWS service needs to be enabled before you can use Amazon Detective?","Amazon Inspector","AWS IAM Identity Center (Successor to AWS SSO)","AWS Shield","No services are required, detective will ingest data as soon as it is enabled.","Detective needs at least one supported service (GuardDuty, VPC Flow Logs, CloudTrail) to be enabled to provide findings to analyse."
"Which AWS Region is Amazon Detective NOT currently available in?","Africa (Cape Town)","Asia Pacific (Tokyo)","Europe (Ireland)","US East (N. Virginia)","Detective has limited regional coverage."
"What is the 'scope' in an Amazon Detective investigation primarily focused on?","Identifying all resources impacted by a potential security issue.","Encrypting data within the environment.","Scaling compute resources based on demand.","Restricting network access based on location.","The scope of the investigation helps to focus on the involved entities and resources."
"Which of the following actions can you perform directly from the Amazon Detective console?","Quarantine an EC2 instance","Delete a CloudTrail log file","Revoke an IAM user's credentials","You cannot perform direct actions. Remediation is performed through other services.","Detective focuses on investigation and analysis, not direct remediation. Remediation is handled by other AWS services."
"What type of account can invite other accounts to become members in Amazon Detective?","The administrator account","The root account","Any IAM user with the necessary permissions","Only the AWS support account.","The administrator account is used to manage the Detective environment and invite other accounts."
"What is the maximum retention period for data in Amazon Detective?","12 months","3 months","6 months","36 months","Amazon Detective retains data for a maximum of 12 months."
"What is the 'Principal ID' in the context of Amazon Detective?","The unique identifier of an IAM user or role involved in an activity.","The IP address of a resource accessing the network.","The name of the security group associated with a resource.","The AWS account ID associated with an activity.","The Principal ID represents the unique identifier of an IAM user or role, indicating the actor performing an action."
"Which of the following is NOT a key component of the Amazon Detective data model?","Entities","Relationships","Findings","Dashboards","Detective's data model comprises Entities, Relationships and Findings, but does not have dashboards."
"Which of these capabilities does Amazon Detective provide to security teams?","Automated incident response","Predictive threat modelling","Root cause analysis of security findings","Real-time vulnerability scanning","Detective helps security teams with root cause analysis."
"What is the role of 'investigations' in Amazon Detective workflows?","To provide a guided process for analysing security findings and understanding root causes","To create new GuardDuty findings","To automate remediation of security vulnerabilities","To perform penetration testing on AWS resources","Investigations in Detective provide a guided analytical process."
"What is the benefit of Detective's pre-built visualisations?","They help analysts quickly understand relationships between entities and identify suspicious activities.","They allow users to customize data ingestion settings.","They enable real-time monitoring of network traffic.","They automate the creation of security policies.","Detective's pre-built visualisations are designed to help quickly spot suspicious activity."
"Which activity related to AWS Lambda functions can Detective analyse?","Lambda function invocations","Lambda function code","Lambda function environment variables","Lambda function layers","Detective monitors Lambda function invocations."
"What type of information does Detective provide about AWS API calls?","The volume and patterns of API calls, which can help identify anomalous activity.","The cost of each API call.","The success rate of each API call.","The latency of each API call.","Detective helps identify anomalous activity."
"In Amazon Detective, what is the significance of the 'Volume' insight?","It displays the amount of data transferred between entities.","It shows the number of security findings detected.","It indicates the frequency of interactions between entities.","It reflects the total cost of AWS resources used.","The 'Volume' insight indicates the frequency of interactions, highlighting patterns of communication between entities."
"Which of the following is NOT a typical use case for Amazon Detective?","Compliance reporting","Incident response","Threat hunting","Vulnerability management","Detective focuses on security investigation."
"What is the 'Administrator Account' in the context of Amazon Detective?","The AWS account that enables and manages Detective for the organisation.","The IAM user with the highest level of permissions.","The root account of the AWS organisation.","The AWS account that hosts the most critical workloads.","The Administrator Account is responsible for enabling and managing Detective."
"How does Amazon Detective use VPC Flow Logs?","To analyse network traffic patterns and identify suspicious communication.","To encrypt data in transit.","To control access to EC2 instances.","To monitor the performance of network devices.","Detective uses VPC Flow Logs to analyse network communication patterns."
"What kind of data can Detective ingest from AWS CloudTrail?","API calls made within your AWS environment","VPC flow logs","DNS queries","Network firewall logs","Detective analyses AWS API calls from CloudTrail."
"What type of security findings does Amazon Detective primarily help you investigate?","GuardDuty findings, Security Hub findings, and custom findings","Penetration testing results","Vulnerability scan results","Compliance audit reports","Detective is used for analysing findings from GuardDuty and Security Hub."
"In Amazon Detective, how are relationships between entities represented?","As connections in the behaviour graph, showing how entities interact with each other.","As a series of logs in CloudTrail.","As entries in a configuration file.","As a list of permissions in IAM.","Detective uses a behaviour graph to represent relationships."
"What is the purpose of the 'Finding Group' feature in Amazon Detective?","To group related findings together to streamline investigations.","To create custom alerts for specific security events.","To filter findings based on severity level.","To automatically resolve security findings.","The Finding Group feature helps streamline investigations by grouping related findings."
"How can you access the data collected by Amazon Detective for further analysis?","You cannot directly access the underlying data, as it is processed and stored within Detective's service.","You can export the data to an S3 bucket in CSV format.","You can query the data using SQL through Amazon Athena.","You can access the data through the AWS CLI.","Detective data is processed and stored within the service itself."
"What type of anomaly can Amazon Detective help you detect in your AWS environment?","Unusual API call patterns","Compromised credentials","Misconfigured security groups","Outdated software versions","Detective helps detect unusual API patterns."
"What is the advantage of using Detective's 'natural language' search capability?","It allows you to search for specific events and entities using simple, human-readable queries.","It translates findings into different languages.","It automatically generates documentation for security incidents.","It provides real-time translation of network traffic.","The natural language search capabilities is one of the main advantage of Detective."
"How does Amazon Detective handle personally identifiable information (PII)?","Detective does not store PII unless it is directly related to a security finding.","Detective automatically redacts all PII from the collected data.","Detective encrypts all PII at rest and in transit.","Detective requires explicit consent from users before collecting PII.","Detective stores PII only if related to a security finding."
"What is the relationship between Amazon Detective and AWS Security Hub?","Detective can ingest and analyse findings from Security Hub to help you investigate security issues.","Security Hub is a replacement for Detective.","Detective automatically remediates findings discovered by Security Hub.","Security Hub is only used for compliance and Detective is used for security analysis.","Detective complements Security Hub by providing tools for in-depth investigation."
"When configuring Amazon Detective, what is the significance of the 'Master Account'?","The Master Account manages the Detective configuration and can invite other accounts.","The Master Account is the AWS account with the highest security privileges.","The Master Account stores all the security findings.","The Master Account hosts the Detective service itself.","The Master Account manages the Detective environment."
"How does Amazon Detective help with threat hunting?","By providing a visual interface for exploring relationships between entities and identifying suspicious activities.","By automatically blocking malicious traffic.","By generating reports on known vulnerabilities.","By performing penetration testing on your environment.","Detective helps with threat hunting by providing a visual investigation interface."
"In Amazon Detective, what is a 'domain'?","A logical grouping of related AWS accounts and resources.","A pre-defined set of security policies.","A specific type of security finding.","A geographic region where Detective is deployed.","In Amazon Detective, a 'domain' is a logical grouping of related AWS accounts and resources for easier management and analysis."
"Which of the following is a key benefit of using Amazon Detective in a multi-account AWS environment?","Centralised visibility and analysis of security data across all accounts.","Automated deployment of security patches across all accounts.","Real-time monitoring of network traffic across all accounts.","Automated remediation of security findings across all accounts.","Detective provides centralised visibility across all accounts in a multi-account setup."
"What type of data source enables Amazon Detective to analyse IAM role activity?","AWS CloudTrail","VPC Flow Logs","AWS Config","Amazon S3 access logs","Detective analyses IAM role activity via CloudTrail logs."
"How does Amazon Detective use machine learning?","To automatically identify anomalous behaviours and potential security threats.","To encrypt data at rest and in transit.","To generate compliance reports.","To perform automated penetration testing.","Detective uses machine learning for anomaly detection."
"In Amazon Detective, what is a 'session'?","A period of interactive investigation within the Detective console.","A collection of security findings related to a specific incident.","A predefined set of security rules.","A snapshot of the behaviour graph at a specific point in time.","A 'session' in Detective is a period of interactive investigation."
"How does Amazon Detective help with understanding the scope of a security incident?","By identifying all resources and entities that were involved in the incident.","By automatically isolating affected resources.","By encrypting all data associated with the incident.","By generating a compliance report for the incident.","Detective helps understand the scope by identifying involved resources and entities."
"What type of AWS resource activity can be analysed by Amazon Detective for unusual behaviour related to S3?","Data exfiltration attempts","S3 bucket creation","S3 bucket policy changes","S3 replication configuration","Detective can analyse S3 activity related to data exfiltration attempts."
"How does Amazon Detective assist in understanding the 'blast radius' of a compromised EC2 instance?","By showing all network connections, API calls, and data access related to the instance.","By automatically isolating the instance from the network.","By encrypting all data on the instance.","By generating a vulnerability report for the instance.","Detective helps understand the blast radius by showing related activities."
"What is the typical workflow for using Amazon Detective after receiving a GuardDuty finding?","Use Detective to investigate the finding, understand its root cause, and identify affected resources.","Use Detective to automatically remediate the GuardDuty finding.","Use Detective to suppress the GuardDuty finding.","Use Detective to generate a compliance report based on the GuardDuty finding.","Detective is used to investigate and understand the root cause of GuardDuty findings."
"What type of information does Amazon Detective provide about failed login attempts?","The source IP addresses, user accounts, and timestamps of failed login attempts.","The location of the user who attempted to log in.","The encryption key used to protect the user's password.","The reason for the login failure.","Detective shows the source IP, user accounts and timestamps."
"How does Amazon Detective determine whether an activity is anomalous?","By comparing the activity to historical patterns and established baselines.","By comparing the activity to a list of known malicious IPs.","By comparing the activity to a list of known vulnerabilities.","By comparing the activity to a predefined set of security rules.","Detective uses historical patterns and baselines to detect anomalies."
"What is the cost model for Amazon Detective?","Detective is priced based on the volume of data ingested and analysed.","Detective is free to use for all AWS customers.","Detective is priced based on the number of investigations performed.","Detective is priced based on the number of accounts in your AWS environment.","Detective is priced based on the data ingested and analysed."
"Which of the following logs is not compatible with Amazon Detective?","Okta logs","VPC Flow Logs","CloudTrail Logs","GuardDuty Logs","Detective does not support Okta Logs."
"What action can you perform on a finding within Detective's interface to mark it for further attention or investigation?","Create a Note","Archive the Finding","Delete the Finding","Remediate the Finding","You can add notes for further attention."
"What is the maximum number of member accounts that can be part of a Detective behaviour graph?","1200","200","50","5000","Detective support 1200 member accounts."
"In Amazon Detective, what is an Entity?","A user, role, IP address, or resource being investigated","A collection of log data sources","A pre-defined investigation template","A security alert from another service","An Entity represents a subject of interest in Detective, like a user or resource."
"Which AWS service is required to be enabled before you can enable Amazon Detective?","AWS CloudTrail","AWS Config","Amazon GuardDuty","AWS IAM Access Analyzer","Amazon Detective requires AWS CloudTrail to be enabled to collect and analyse log data related to account activity."
"What type of data does Amazon Detective NOT ingest directly?","Network Flow Logs","EC2 Instance Metrics","AWS CloudTrail Logs","Amazon GuardDuty Findings","Detective does not ingest EC2 Instance Metrics directly. It focuses on logs and findings related to security investigations."
"In Amazon Detective, what is a Behaviour Graph?","A graphical representation of relationships between entities over time","A pre-defined set of rules for detecting anomalies","A list of all AWS resources in your account","A cost optimisation tool for AWS resources","A Behaviour Graph is the core of Detective, representing the relationships and interactions between entities derived from ingested data."
"What is the primary purpose of Amazon Detective's 'Scope' functionality?","To narrow down investigations to specific timeframes or entities","To broaden investigations across multiple AWS accounts","To configure data ingestion settings","To manage user access permissions","The Scope in Detective allows you to focus investigations on specific timeframes, entities, or resources."
"Which security standard compliance does Amazon Detective directly help you achieve?","It does not directly help with specific compliance standards","PCI DSS","HIPAA","GDPR","Amazon Detective helps with security investigations and doesn't directly map to individual compliance standards."
"What is the maximum data retention period for data ingested into Amazon Detective?","365 days","90 days","180 days","30 days","Amazon Detective retains data for up to 365 days, allowing for long-term trend analysis."
"Can Amazon Detective be used to investigate security events across multiple AWS accounts?","Yes, if you use cross-account detective","No, Detective only works within a single account","Only if the accounts are in the same region","Only if the accounts have the same IAM roles","Amazon Detective supports cross-account investigations when configured as a multi-account environment."
"What AWS service can be used to send findings to Amazon Detective?","Amazon GuardDuty","AWS CloudWatch","AWS Config","AWS Trusted Advisor","Amazon GuardDuty findings can be sent to Amazon Detective to provide context for security investigations."
"What is the pricing model for Amazon Detective?","Based on the volume of data ingested and analysed","Based on the number of users accessing the service","A flat monthly fee","Based on the number of investigations conducted","Amazon Detective charges based on the volume of data ingested from the supported data sources."
"When creating a new Behaviour Graph in Amazon Detective, what is a 'master account'?","The AWS account that manages the Detective service and associated member accounts","The AWS account with the highest security privileges","The first AWS account created in an organisation","The AWS account that owns the root S3 bucket","The master account manages the overall Detective service and can invite member accounts."
"What type of anomaly detection does Amazon Detective provide?","Correlation and relationship-based anomaly detection","Signature-based intrusion detection","Rule-based anomaly detection","Statistical anomaly detection only","Detective focuses on identifying anomalies based on relationships and correlations between entities and events."
"In Amazon Detective, what is the purpose of 'Finding Groups'?","To group related findings based on shared entities or characteristics","To group findings based on severity level","To group findings based on the data source they originated from","To group findings that require immediate attention","Finding Groups help to aggregate and correlate related findings, simplifying investigations."
"What role must be assumed by member accounts when integrating with an Amazon Detective master account?","DetectiveInvestigatorAccess","ReadOnlyAccess","AdministratorAccess","AWSSupportAccess","The DetectiveInvestigatorAccess role grants the necessary permissions for Detective to access and analyse data within the member account."
"Which of the following is NOT a supported data source for Amazon Detective?","Syslog","AWS CloudTrail logs","VPC Flow Logs","Amazon GuardDuty findings","Syslog is not directly supported as a data source for Amazon Detective."
"What is the purpose of the 'Summary' page within an Amazon Detective investigation?","To provide a high-level overview of the key entities and relationships involved","To display the raw log data for the investigation","To provide a list of recommended remediation steps","To show the cost breakdown for the investigation","The Summary page offers a concise overview of the most important aspects of the investigation."
"Can you use Amazon Detective to investigate historical security events prior to enabling the service?","No, Detective only analyses data collected after it's enabled","Yes, Detective automatically backfills historical data","Yes, if you manually upload historical logs","Yes, if you request it from AWS Support","Detective only analyses data collected after it's enabled. It doesn't retroactively analyse historical data."
"What is a 'principal' in the context of Amazon Detective?","A user, role, or service account that interacts with AWS resources","A security vulnerability detected by Detective","A pre-defined investigation workflow","A data source ingested by Detective","A principal is a user, role, or service account that performs actions within AWS, and can be an entity in Detective investigations."
"How does Amazon Detective assist with threat hunting?","By providing a unified view of security-related data and relationships","By automatically blocking malicious traffic","By providing pre-built intrusion detection rules","By automatically patching security vulnerabilities","Detective helps threat hunting by providing a centralised view of data and relationships to identify suspicious patterns."
"Which of the following actions can you perform directly from within the Amazon Detective console?","Query AWS CloudTrail logs","Terminate an EC2 instance","Update IAM policies","Change Security Group rules","You cannot perform these actions directly. The purpose is to provide a tool for investigation."
"If you disable Amazon Detective, what happens to the data that has been ingested?","The data is deleted after the retention period","The data is immediately deleted","The data is archived and can be restored later","The data remains accessible indefinitely","When you disable Amazon Detective, the data is deleted after the retention period."
"What is the best way to limit the amount of data ingested by Amazon Detective to control costs?","Filter events based on source IP addresses","Configure sampling rules in CloudTrail","There is no direct way to control the data ingested","Disable logging for certain resources","Detective data ingestion is controlled by the CloudTrail, VPC Flow Logs and GuardDuty configuration, but not directly configurable within Detective itself."
"What is the purpose of the 'Visualise' feature in Amazon Detective?","To display relationships between entities in a graphical format","To create custom dashboards","To generate reports","To view the raw log data in a table format","The Visualise feature provides a graphical representation of relationships between entities."
"What is the purpose of the 'IAM Access Analyzer' integration with Amazon Detective?","The detective integration does not use IAM Access Analyzer","To identify overly permissive IAM policies","To automate IAM policy creation","To enforce multi-factor authentication","Amazon Detective doesn't directly integrate with IAM Access Analyzer."
"How does Amazon Detective help with incident response?","By providing context and relationships to quickly understand the scope and impact of an incident","By automatically remediating security incidents","By automatically isolating compromised resources","By automatically generating incident reports","Detective helps with incident response by providing context and relationships between entities involved in an incident."
"When investigating a suspicious IP address in Amazon Detective, what kind of information might you find?","The AWS resources the IP address has interacted with","The geographic location of the IP address","The owner of the IP address","The ports the IP address scanned","Detective provides information about the AWS resources that a given IP address has interacted with."
"What is the benefit of using Amazon Detective compared to manually analysing CloudTrail logs?","Detective automatically correlates data from multiple sources and visualises relationships","Detective provides real-time alerts","Detective can automatically remediate security issues","Detective is more cost-effective than CloudTrail","Detective automates data correlation and provides visualisations, making analysis easier and faster than manual log analysis."
"In Amazon Detective, what does the term 'TTPs' refer to?","Tactics, Techniques, and Procedures used by attackers","Total Transactions Per Second","Tools, Tests, and Processes for security assessments","Top Threats and Priorities for remediation","TTPs are the Tactics, Techniques, and Procedures used by attackers."
"Can Amazon Detective be used to investigate potential insider threats?","Yes, by analysing user activity and resource access patterns","No, Detective only focuses on external threats","Yes, but only with additional data sources","Only with complex customisation","Detective can help investigate insider threats by analysing user activity and resource access patterns."
"What is a key benefit of using Amazon Detective's pre-built visualisations?","They simplify complex data and highlight key relationships quickly","They provide real-time alerts","They automatically generate compliance reports","They reduce the cost of data storage","Pre-built visualisations make it easier to understand complex data and spot relevant relationships."
"Which of the following actions can trigger the creation of a finding in Amazon Detective?","Suspicious API calls","An EC2 instance being launched in a new region","An S3 bucket being created","A change to a Security Group","Suspicious API calls can trigger a finding as they may indicate malicious activity."
"What is the purpose of the 'Investigation Notes' feature in Amazon Detective?","To allow investigators to add their own notes and observations to an investigation","To automatically generate reports","To provide recommendations for remediation","To track the cost of an investigation","Investigation Notes allow investigators to add context and insights to their findings."
"Can Amazon Detective investigate API calls made to AWS services outside of your AWS account?","No, Detective only analyses API calls within your account","Yes, if the other account shares its CloudTrail logs","Only if the other account is in the same region","Yes, but it requires manual configuration","Detective only analyses API calls within your account's CloudTrail logs."
"How does Amazon Detective help with identifying compromised AWS credentials?","By identifying unusual API activity associated with a specific IAM role or user","By automatically rotating credentials","By enforcing multi-factor authentication","By providing recommendations for strong passwords","Detective can identify compromised credentials by detecting unusual activity associated with specific users or roles."
"What is the significance of the 'First Seen' and 'Last Seen' timestamps in Amazon Detective?","They indicate the first and last time an entity was observed in the data","They indicate when an investigation was started and finished","They indicate when a resource was created and deleted","They indicate the first and last time data was ingested from a specific source","These timestamps help understand the duration and scope of an entity's activity."
"Which of the following is a valid reason to use Amazon Detective in a multi-account AWS environment?","Centralised security investigations across all accounts","Automated patching of vulnerabilities across all accounts","Cost optimisation across all accounts","Simplified IAM policy management across all accounts","Detective provides a centralised location for conducting security investigations across multiple AWS accounts."
"What type of information can be found in the 'Profile' page for an entity in Amazon Detective?","A summary of the entity's activity, relationships, and related findings","The entity's IP address and geographic location","The entity's IAM permissions","The entity's cost usage","The Profile page provides a summary of an entity's activity and relationships."
"Can you use Amazon Detective to investigate potential data exfiltration events?","Yes, by analysing network traffic patterns and data transfer activities","No, Detective only focuses on API activity","Only if you enable additional logging","Only if you integrate with a third-party SIEM","Detective can help identify data exfiltration by analysing network traffic patterns and data transfer activities."
"What type of questions can Amazon Detective help you answer during a security investigation?","Who accessed this resource and when?","What is the current CPU utilisation of this instance?","What is the cost of running this service?","What are the latest security patches available?","Detective helps answer questions about who accessed specific resources and when, to assist in security investigations."
"How does Amazon Detective leverage machine learning?","To identify patterns and anomalies in data","To automatically remediate security incidents","To predict future security threats","To optimise the cost of AWS resources","Detective uses machine learning to identify patterns and anomalies within the ingested data."
"Which AWS Region is Amazon Detective NOT available in?","Any AWS Region","eu-west-1 (Ireland)","us-east-1 (N. Virginia)","ap-southeast-2 (Sydney)","Amazon Detective is not available in any AWS Regions; it is designed to support investigations in any AWS Regions where AWS services are used."
"What is the purpose of the 'Search' function in Amazon Detective?","To find specific entities, events, or findings within the data","To search for files on an EC2 instance","To search for vulnerabilities in your application code","To search for security best practices","The Search function allows you to quickly locate specific entities, events, or findings."
"Which type of log provides information about network traffic in your VPC for Detective analysis?","VPC Flow Logs","CloudTrail Logs","S3 Access Logs","RDS Logs","VPC Flow Logs record network traffic within your VPC and are used by Detective to analyse network activity."
"How can you improve the performance of Amazon Detective?","By optimising the configuration of the ingested data sources","By increasing the Detective instance size","By adding more member accounts","By reducing the data retention period","Optimising the configuration of the ingested data sources, such as CloudTrail and VPC Flow Logs, is the best way to improve performance."
"What is the best way to give a security auditor temporary access to Amazon Detective data?","Create a new IAM user with the DetectiveInvestigatorAccess policy","Share your AWS account credentials","Grant the auditor access to your S3 buckets","Send the auditor a copy of your CloudTrail logs","Creating a new IAM user with the DetectiveInvestigatorAccess policy is the best way to grant temporary access to a security auditor."
"When using a master account for Amazon Detective, which of the following statements is true?","The master account pays for the data ingestion costs of all member accounts","Each member account pays for its own data ingestion costs","The data ingestion costs are split equally between the master and member accounts","The master account only pays for its own data ingestion costs","In a master account setup, the master account pays for all data ingestion costs from member accounts."
"In Amazon Detective, what data source is always ingested and cannot be disabled?","AWS CloudTrail logs","Amazon VPC Flow Logs","Amazon GuardDuty findings","AWS Config configuration items","AWS CloudTrail logs are a foundational data source for Detective and are always ingested to provide an audit trail of actions performed in the AWS environment."
"Which AWS service is NOT a direct data source integrated with Amazon Detective?","AWS Firewall Manager","AWS CloudTrail","Amazon VPC Flow Logs","Amazon GuardDuty","AWS Firewall Manager is a security management service, not a direct data source ingested by Detective."
"What is the primary purpose of the 'Finding Group' feature in Amazon Detective?","To group related findings based on common attributes or entities","To filter findings based on severity","To suppress findings from appearing in the console","To export findings to a SIEM solution","'Finding Groups' allow you to group related findings based on common attributes or entities, simplifying investigations."
"Within Amazon Detective's security graph, what is an 'Entity'?","A representation of an AWS resource, user, or IP address","A collection of related findings","A visualization of network traffic","A summary of security events","An 'Entity' in Detective represents an AWS resource, user, or IP address that is involved in security events."
"Which of the following is a typical use case for Amazon Detective?","Root cause analysis of security incidents","Real-time threat detection","Vulnerability scanning","Compliance reporting","Detective is primarily used for root cause analysis of security incidents by providing a historical view of activity."
"In Amazon Detective, what type of information is provided by analysing VPC Flow Logs?","Network traffic patterns and communication between resources","API calls made by IAM users","Changes to AWS resource configurations","Malicious code detected in EC2 instances","VPC Flow Logs provide insights into network traffic patterns and communication between resources within your VPC."
"What is the maximum retention period for data stored in Amazon Detective?","12 months","3 months","6 months","24 months","The maximum retention period for data in Amazon Detective is 12 months."
"How does Amazon Detective help with compliance requirements?","By providing a historical record of security-related activity","By automatically remediating security vulnerabilities","By generating compliance reports","By encrypting data at rest and in transit","Detective provides a historical record of security-related activity, which can be used for compliance auditing."
"Which statement is true regarding the pricing of Amazon Detective?","Pricing is based on the volume of data ingested from supported AWS services","Pricing is based on the number of security findings detected","Pricing is based on the number of Detective users","Pricing is a fixed monthly fee","Detective pricing is based on the volume of data ingested from supported AWS services."
"When setting up Amazon Detective, what is the 'Master' account responsible for?","Inviting and managing member accounts","Monitoring security events across all regions","Enforcing security policies on all AWS resources","Automating security incident response","The 'Master' account in Detective is responsible for inviting and managing member accounts."
"How does Amazon Detective use machine learning?","To identify suspicious patterns and anomalies in data","To automatically remediate security threats","To predict future security incidents","To encrypt data at rest","Detective uses machine learning to identify suspicious patterns and anomalies in the data it ingests."
"What is the primary benefit of using Amazon Detective over manually analysing logs?","Detective automates the analysis and correlation of security data","Detective provides real-time threat intelligence feeds","Detective allows you to block malicious IP addresses","Detective eliminates the need for security engineers","Detective automates the analysis and correlation of security data, saving time and effort."
"In Amazon Detective, what is the significance of the 'Principal ID' entity?","It represents the user or role that performed an action","It represents the AWS account ID","It represents the geographic region","It represents the VPC ID","The 'Principal ID' represents the user or role that performed an action within the AWS environment."
"What type of data does Amazon Detective ingest from Amazon GuardDuty?","Security findings related to threats and malicious activity","Configuration changes to EC2 instances","Network traffic logs","Database query logs","Detective ingests security findings from GuardDuty to provide context around potential threats and malicious activity."
"Which of the following is an advantage of using the pre-built visualisations in Amazon Detective?","They help to quickly identify and understand security issues","They allow you to customize the data sources used by Detective","They automate the remediation of security findings","They provide real-time alerts for security incidents","The pre-built visualisations in Detective help you quickly identify and understand security issues by presenting data in a clear and intuitive way."
"Which of the following AWS IAM permissions is required to enable Amazon Detective?","detective:EnableOrganizationAdminAccount","detective:StartInvestigation","detective:ViewFindings","detective:AcceptInvitation","The `detective:EnableOrganizationAdminAccount` permission is required to enable Detective as the administrator account for an organisation."
"What is a 'behaviour graph' in the context of Amazon Detective?","A linked set of data derived from AWS logs and events which allows you to perform security investigations","A graph showing the resource utilisation of AWS services","A diagram of your network infrastructure","A representation of user activity within your AWS environment","The 'behaviour graph' in Detective is a linked set of data derived from AWS logs and events, enabling security investigations."
"Which AWS service would you typically use alongside Amazon Detective to automatically respond to security incidents?","AWS Security Hub","AWS Lambda","Amazon CloudWatch Events","AWS Config","AWS Lambda can be used alongside Detective to automatically respond to security incidents based on findings."
"What type of information can you NOT directly obtain from Amazon Detective's analysis of CloudTrail logs?","The content of data stored in S3 buckets","API calls made by IAM users","The source IP address of API requests","The time of API requests","Detective analyses CloudTrail logs to show API calls, but it doesn't have access to the *content* of data stored in S3 buckets."
"In Amazon Detective, what does the term 'badge' refer to?","A visual indicator that highlights unusual or suspicious activity","A security certification earned by a user","A type of IAM permission granted to users","A third-party security tool integrated with Detective","In Detective, a 'badge' is a visual indicator that highlights unusual or suspicious activity within the behaviour graph."
"Which of the following activities can you perform directly within the Amazon Detective console?","Investigate security findings, analyse entities, and visualise relationships","Create new IAM users and roles","Configure AWS CloudTrail settings","Deploy new EC2 instances","The Detective console allows you to investigate security findings, analyse entities, and visualise relationships between them."
"How can you improve the effectiveness of Amazon Detective?","By ensuring all relevant AWS services are integrated and generating logs","By increasing the number of users with access to Detective","By reducing the retention period of data in Detective","By disabling CloudTrail logs","Ensuring all relevant AWS services are integrated and generating logs provides Detective with the data it needs to perform effective analysis."
"What is a key difference between Amazon Detective and Amazon GuardDuty?","Detective performs historical analysis, while GuardDuty focuses on real-time threat detection","Detective is used for compliance reporting, while GuardDuty is used for incident response","Detective is used for vulnerability scanning, while GuardDuty is used for malware detection","Detective is a free service, while GuardDuty is a paid service","Detective performs historical analysis and root cause investigations, while GuardDuty focuses on real-time threat detection."
"What level of access does a member account in Amazon Detective have to the data in the behaviour graph?","Access only to their own data","Full access to all data in the behaviour graph","No access to the data in the behaviour graph","Read-only access to all data in the behaviour graph","Member accounts in Detective only have access to their own data within the behaviour graph."
"Which statement is true about deleting Amazon Detective?","All ingested data is deleted and cannot be recovered","The service is paused, and data is retained for 30 days","Only the Detective configuration is deleted, the data remains","All integrations with other AWS services are disabled","When Detective is deleted, all ingested data is deleted and cannot be recovered."
"You want to investigate a spike in API calls from a specific IAM role. How can Amazon Detective assist you?","By showing the timeline of API calls made by the role and related entities","By automatically blocking the IAM role","By generating a compliance report for the IAM role","By encrypting the data accessed by the IAM role","Detective can show you the timeline of API calls made by the role and related entities, helping you understand the context of the spike."
"Which of the following features is NOT available in Amazon Detective?","Automated remediation of security incidents","Visualisation of security events","Analysis of network traffic","Investigation of user activity","Detective focuses on investigation and analysis; automated remediation is typically handled by other services."
"What role does AWS Security Hub play in relation to Amazon Detective?","Security Hub aggregates findings from multiple sources, including Detective, and provides a central view","Security Hub is a prerequisite for using Detective","Security Hub replaces Detective's functionality","Security Hub is used to configure Detective's data sources","Security Hub aggregates findings from multiple sources, including Detective, providing a central view of security posture."
"Which AWS region is NOT supported by Amazon Detective?","Africa (Cape Town)","US East (N. Virginia)","Europe (Ireland)","Asia Pacific (Tokyo)","Africa (Cape Town) is not yet a supported region for Amazon Detective."
"You suspect that an EC2 instance has been compromised. How can Amazon Detective help you investigate?","By showing the network traffic, API calls, and user activity associated with the instance","By automatically isolating the instance from the network","By patching the instance with the latest security updates","By performing a vulnerability scan of the instance","Detective can show you the network traffic, API calls, and user activity associated with the instance, helping you understand how it was compromised."
"What is the purpose of the 'Investigation' feature in Amazon Detective?","To focus your analysis on a specific security event or entity","To automate the analysis of all security findings","To create custom visualisations of security data","To generate compliance reports","The 'Investigation' feature in Detective allows you to focus your analysis on a specific security event or entity to understand its context and impact."
"Which type of data is NOT visualised directly in the Amazon Detective console?","CPU utilisation metrics of EC2 instances","Network connections between EC2 instances","API calls made by IAM users","GuardDuty findings","Detective focuses on security-related data, not general resource metrics like CPU utilisation."
"How does Amazon Detective help you identify insider threats?","By analysing user activity and identifying anomalous behaviour","By performing background checks on employees","By monitoring employee emails","By restricting access to sensitive data","Detective can analyse user activity and identify anomalous behaviour that may indicate an insider threat."
"What is the maximum number of member accounts that can be associated with a single Amazon Detective behaviour graph?","1200","50","100","Unlimited","The number of member accounts per behaviour graph has been increased over time and could vary. Please refer to the official documentation."
"Which AWS service does Amazon Detective utilise for data storage?","Amazon S3","Amazon EBS","Amazon RDS","Amazon DynamoDB","Detective uses Amazon S3 for storing the ingested data, ensuring scalability and durability."
"Which of the following is a benefit of using the 'Organization behaviour graph' feature in Amazon Detective?","Centralised security investigations across multiple AWS accounts","Automated remediation of security incidents across the organisation","Centralised management of IAM users and roles","Centralised configuration of AWS CloudTrail logs","The 'Organisation behaviour graph' feature enables centralised security investigations across multiple AWS accounts, making it easier to identify threats that span multiple environments."
"What is the purpose of the 'Entity Context' panel in Amazon Detective?","To provide detailed information about a specific entity, such as its activity and relationships","To filter the data displayed in the behaviour graph","To generate a report on the overall security posture","To configure the data sources used by Detective","The 'Entity Context' panel provides detailed information about a specific entity, such as its activity, relationships, and associated findings."
"How can you access the data ingested by Amazon Detective outside of the Detective console?","You cannot directly access the data outside of the Detective console","By exporting the data to Amazon S3","By querying the data using Amazon Athena","By accessing the data through the AWS CLI","Direct access to the underlying data is not provided; the primary way to interact with the data is through the Detective console."
"What type of user is typically responsible for managing and configuring Amazon Detective?","Security Engineer or Security Analyst","Database Administrator","Network Engineer","Application Developer","Amazon Detective is typically managed and configured by Security Engineers or Security Analysts."
"You receive a GuardDuty finding indicating a potential cryptocurrency mining operation on an EC2 instance. How can Amazon Detective help you investigate this further?","By showing the network connections, processes, and user activity associated with the instance","By automatically stopping the instance","By patching the instance with the latest security updates","By encrypting the instance's data","Detective can show you the network connections, processes, and user activity associated with the instance, helping you understand the scope and impact of the potential mining operation."
"Which of the following is NOT a supported data source for Amazon Detective?","AWS CloudWatch Logs","AWS CloudTrail logs","Amazon VPC Flow Logs","Amazon GuardDuty findings","AWS CloudWatch Logs are not directly ingested by Detective. Detective uses other sources such as CloudTrail, VPC Flow Logs and GuardDuty findings."
"In Amazon Detective, what does the term 'TTPs' refer to?","Tactics, Techniques, and Procedures used by attackers","Total Throughput Performance of network traffic","Trusted Third-Party security vendors","Technical Training Programs offered by AWS","'TTPs' refers to Tactics, Techniques, and Procedures used by attackers, which Detective can help identify and analyse."
"Which AWS service is commonly used to forward logs to Amazon S3 before Amazon Detective can ingest them?","AWS CloudTrail","Amazon Kinesis Data Firehose","Amazon SQS","AWS Lambda","Amazon Kinesis Data Firehose is commonly used to stream logs to S3, and as Amazon Detective ingests data from S3 it becomes available to Amazon Detective."
"How can you use tags in Amazon Detective?","To filter and organise entities and findings","To automatically remediate security incidents","To define access control policies","To encrypt data at rest","Tags can be used to filter and organise entities and findings in Detective, making it easier to focus on specific areas of interest."
"Which statement is correct regarding the enablement of Amazon Detective across multiple AWS accounts in an organisation?","Detective can be enabled centrally for the entire organisation using AWS Organizations","Each AWS account must enable Detective individually","Detective can only be enabled for a single AWS account","Detective requires a separate AWS subscription for each account","Detective can be enabled centrally for the entire organisation using AWS Organizations, simplifying management and ensuring consistent security coverage."
"Which analysis should be performed outside of Amazon Detective capabilities?","Analysing the root cause of a compromised EC2 instance","Identifying unusual API activity from a specific IAM user","Determining whether a specific S3 bucket has been accessed by an unauthorised user","Performing dynamic analysis of a suspicious executable file","Dynamic analysis of executable files requires dedicated tools; Detective focuses on analysing AWS logs and events."
"An analyst suspects a data exfiltration attempt. Which data source ingested by Amazon Detective will be most helpful to investigate?","Amazon VPC Flow Logs","AWS CloudTrail logs","Amazon GuardDuty findings","AWS Config configuration items","VPC Flow Logs record network traffic, making them ideal for investigating data exfiltration attempts."
"How does the 'Security best practice' recommendations from AWS Trusted Advisor help you get the most value out of Amazon Detective?","By ensuring that all required AWS services are properly configured and generating logs","By automatically remediating security vulnerabilities identified by Detective","By providing real-time threat intelligence feeds","By generating compliance reports for your AWS environment","Ensuring that all required AWS services are properly configured and generating logs provides Detective with the data it needs to perform effective analysis."
"In Amazon Detective, what is the primary purpose of an investigation?","To identify and analyse security issues","To enforce IAM policies","To automate patching of EC2 instances","To manage AWS budgets","Amazon Detective's primary purpose is to help security teams identify the root cause of security issues and suspicious activities by analysing log data."
"What type of data sources does Amazon Detective use for security investigations?","VPC Flow Logs, CloudTrail Logs, and GuardDuty findings","AWS Config Rules, S3 access logs, and Billing data","CloudWatch metrics, Lambda function logs, and CodeBuild logs","SNS notifications, SQS queue data, and Route 53 query logs","Amazon Detective ingests and analyses VPC Flow Logs, CloudTrail logs, and GuardDuty findings to provide a unified view of security-related events."
"Within Amazon Detective, what is a 'behaviour graph'?","A visual representation of entity interactions over time","A list of security vulnerabilities","A configuration file for log ingestion","A set of pre-defined security rules","The behaviour graph in Amazon Detective is a knowledge graph that shows the relationships and interactions between entities (users, roles, instances, etc.) over time."
"Which AWS service needs to be enabled to provide data to Amazon Detective?","GuardDuty","Inspector","Trusted Advisor","IAM Access Analyzer","GuardDuty findings are a key data source for Amazon Detective, so GuardDuty must be enabled to provide this information."
"What is the retention period for data in the Amazon Detective behaviour graph?","12 months","3 months","30 days","1 month","Amazon Detective retains data in the behaviour graph for 12 months to allow for long-term trend analysis and investigations."
"What security domain does Amazon Detective primarily address?","Threat detection and incident response","Data encryption and compliance","Identity and access management","Network security and firewall management","Amazon Detective focuses on helping security teams with threat detection, investigation and incident response by analysing log data."
"Which AWS account is required to create the Detective behaviour graph in a multi-account environment?","The Detective administrator account","The account with GuardDuty enabled","The master payer account","Any account within the organisation","The Detective behaviour graph is created in the Detective administrator account, which centralises the security investigation across all member accounts."
"What is the benefit of using Amazon Detective compared to manually analysing logs?","Automated data analysis and correlation","Lower infrastructure costs","Real-time application performance monitoring","Automated compliance reporting","Amazon Detective automates the analysis and correlation of log data, which is a significant time-saver compared to manual log analysis."
"How does Amazon Detective help with identifying the root cause of security findings?","By visually mapping entity interactions and behaviours","By automatically remediating security vulnerabilities","By providing a list of recommended security configurations","By generating compliance reports","Amazon Detective helps identify root causes by visually mapping entity interactions and behaviours, making it easier to understand the sequence of events leading to a security finding."
"When setting up Amazon Detective in a multi-account environment, what is a 'member account'?","An AWS account that contributes data to the behaviour graph","An AWS account with administrator privileges","An AWS account used for testing purposes","An AWS account that cannot be investigated","A member account is an AWS account within an organization that contributes data to the Detective behaviour graph, allowing the administrator account to investigate its activities."
"What type of investigation does Detective facilitate within AWS environments?","Security investigations","Cost optimisation analysis","Performance troubleshooting","Compliance auditing","Detective is specifically designed to facilitate security investigations by analysing data from various AWS services."
"What type of anomaly detection does Amazon Detective utilise to identify suspicious activity?","Behavioural analysis","Signature-based detection","Rule-based detection","Heuristic analysis","Amazon Detective uses behavioural analysis to identify anomalies and suspicious activity based on learned patterns of behaviour over time."
"Which log data is NOT directly ingested by Amazon Detective?","AWS Config logs","VPC Flow Logs","AWS CloudTrail logs","Amazon GuardDuty findings","AWS Config logs are not directly ingested by Detective. It uses VPC Flow Logs, CloudTrail Logs and GuardDuty findings."
"If a compromised AWS account is being used for malicious activity, how can Amazon Detective assist in identifying the scope of the compromise?","By mapping out network traffic and API calls associated with the account","By automatically terminating EC2 instances launched by the account","By generating a report of all IAM users in the account","By forcing a password reset for the account","Detective maps out the network traffic and API calls associated with the compromised account, providing a clear picture of the attacker's activity and the scope of the breach."
"What is the benefit of using Amazon Detective's pre-built data integrations?","Reduced configuration and faster time to insight","Improved data encryption","Automated compliance reporting","Real-time application monitoring","Detective's pre-built data integrations mean there's less manual configuration required, allowing security teams to quickly gain insights from the data."
"In Amazon Detective, what does an 'entity profile' provide?","A summary of activity associated with a specific entity","A list of IAM permissions for a user","A configuration file for an EC2 instance","A report of security vulnerabilities","An entity profile in Detective provides a summary of all activities and behaviours associated with a specific entity, such as a user, role, or IP address."
"How can Amazon Detective help with insider threat detection?","By identifying unusual access patterns and API calls","By automatically blocking suspicious users","By monitoring employee email communications","By tracking physical access to office buildings","Detective can help identify insider threats by analysing access patterns and API calls to detect unusual or unauthorised activity."
"What is the typical workflow for using Amazon Detective to investigate a security incident?","Review GuardDuty findings, investigate entities, analyse behaviour graph","Create a security group, configure VPC Flow Logs, analyse CloudTrail logs","Configure IAM roles, set up CloudWatch alarms, analyse CloudWatch metrics","Run vulnerability scans, perform penetration testing, analyse security reports","The typical workflow involves reviewing GuardDuty findings, investigating relevant entities in Detective, and analysing the behaviour graph to understand the scope and impact of the incident."
"What type of data visualisation is used in Amazon Detective to illustrate relationships between entities?","Interactive graph","Heat map","Bar chart","Pie chart","Amazon Detective uses an interactive graph visualisation to show the relationships and connections between entities, making it easier to understand the flow of activity and identify suspicious patterns."
"How does Amazon Detective assist with compliance requirements?","By providing audit trails of security-related events","By automatically generating compliance reports","By enforcing security policies","By encrypting all data at rest and in transit","Detective provides audit trails of security-related events, which can be used to demonstrate compliance with various regulations."
"Which activity is NOT a typical use case for Amazon Detective?","Identifying compromised EC2 instances","Troubleshooting application performance issues","Investigating IAM role abuse","Analysing suspicious API calls","Detective is primarily focused on security investigations and is not designed for application performance troubleshooting."
"How does Amazon Detective support collaborative investigations?","By allowing multiple users to access and annotate the behaviour graph","By automatically sharing security findings with other AWS services","By providing a chat interface for team communication","By generating a report of all user activity","Detective supports collaborative investigations by allowing multiple users to access and annotate the behaviour graph, making it easier for teams to share insights and work together."
"Which AWS Region supports Amazon Detective?","AWS regions where GuardDuty is available","All AWS Regions","Only US East (N. Virginia)","Only EU (Ireland)","Amazon Detective is generally available in most AWS regions where GuardDuty is also available, allowing for a consistent security posture across your AWS environment."
"In Amazon Detective, what does it mean when an entity is marked as 'unusual'?","Its behaviour deviates significantly from its baseline","It has been identified as a security vulnerability","It has been manually flagged by a security analyst","It is using an outdated version of software","When an entity is marked as 'unusual' in Detective, it means its behaviour deviates significantly from its established baseline, indicating potentially suspicious activity."
"What is the purpose of the 'PrincipalId' field in Amazon Detective's analysis?","To identify the IAM user or role making API calls","To identify the AWS account ID","To identify the source IP address","To identify the destination port","The 'PrincipalId' field in Detective is used to identify the IAM user or role that is making API calls, which is crucial for understanding who is responsible for specific actions."
"How does Amazon Detective ingest VPC Flow Logs?","Automatically, if VPC Flow Logs are enabled in the account","By configuring a CloudWatch Logs subscription","By configuring an S3 bucket to receive the logs","By manually uploading the log files","Detective automatically ingests VPC Flow Logs if they are enabled in the AWS account, without requiring additional configuration."
"What is the relationship between Amazon GuardDuty and Amazon Detective?","GuardDuty detects threats, and Detective helps investigate them","GuardDuty replaces Detective's functionality","Detective is required to use GuardDuty","GuardDuty provides historical data for Detective","GuardDuty detects potential threats, and Detective is used to investigate and analyse those findings to understand the root cause and scope of the incident."
"What is a core benefit of using Amazon Detective in conjunction with other AWS security services?","It provides a centralised view for security investigation across multiple AWS services","It automates the configuration of other security services","It reduces the cost of other security services","It eliminates the need for other security services","Detective provides a centralised view for security investigations, allowing security teams to correlate data from multiple AWS services (like GuardDuty and CloudTrail) in a single place."
"In Detective, what does the term 'finding' refer to?","A potential security issue identified by GuardDuty","A performance bottleneck identified by CloudWatch","A compliance violation identified by AWS Config","A cost anomaly identified by Cost Explorer","In Detective, a 'finding' typically refers to a potential security issue that has been identified by Amazon GuardDuty."
"How does Amazon Detective help with reducing the time to resolution for security incidents?","By providing contextual information and visualisations of security data","By automatically remediating security vulnerabilities","By providing pre-built security playbooks","By automatically escalating incidents to AWS support","Detective reduces the time to resolution by providing contextual information and visualisations of security data, making it easier to understand the scope and impact of security incidents."
"Which AWS service can be used to send alerts based on findings from Amazon Detective?","Amazon CloudWatch Events (EventBridge)","Amazon SNS","AWS Lambda","Amazon SQS","Amazon CloudWatch Events (EventBridge) can be used to send alerts based on findings generated by Amazon Detective, allowing for automated responses to security events."
"What is the main advantage of the graph database used by Amazon Detective?","Efficiently analysing relationships between entities","Storing large volumes of unstructured data","Performing complex analytical queries","Providing real-time data streaming","The graph database used by Amazon Detective is optimised for efficiently analysing the relationships and connections between different entities, making it ideal for security investigations."
"How can you add additional AWS accounts to an existing Amazon Detective behaviour graph?","By inviting the accounts from the Detective console","By creating a new behaviour graph for each account","By manually configuring IAM roles in each account","By using AWS Organizations to automatically add accounts","You can add additional AWS accounts to an existing Detective behaviour graph by inviting them from the Detective console, which simplifies the process of managing security investigations across multiple accounts."
"What is the purpose of the 'Data Source Packages' setting in Amazon Detective?","To specify which data sources are included in the behaviour graph","To configure data encryption","To configure data retention policies","To specify which AWS regions to collect data from","The 'Data Source Packages' setting in Detective allows you to specify which data sources (e.g., VPC Flow Logs, CloudTrail logs, GuardDuty findings) are included in the behaviour graph."
"When investigating a potentially compromised EC2 instance using Amazon Detective, what type of information can you typically find?","Network traffic patterns and API calls made by the instance","CPU utilisation and memory usage of the instance","Installed software and configuration files on the instance","Security group rules associated with the instance","When investigating a compromised EC2 instance, Detective can provide information about network traffic patterns, API calls made by the instance, and other security-related activities."
"Which of the following is a benefit of using Amazon Detective's security investigation capabilities in a cloud environment?","Improved visibility into user activity and resource interactions","Reduced infrastructure costs","Improved application performance","Automated compliance reporting","Detective provides improved visibility into user activity and resource interactions, which is essential for security investigations in a cloud environment."
"How can you access the data stored within the Amazon Detective behaviour graph programmatically?","There is no direct programmatic access to the data","Using AWS CLI commands","Using AWS SDKs and APIs","Using SQL queries","There is no direct programmatic access to the data of an amazon detective graph."
"What is the impact of disabling GuardDuty on Amazon Detective functionality?","Detective will have limited or no data for analysis","Detective will continue to function normally","Detective will switch to using AWS Security Hub findings","Detective will automatically enable AWS Config","If GuardDuty is disabled, Detective will have limited or no data for analysis, as GuardDuty findings are a key data source."
"How can you use tags to enhance investigations in Amazon Detective?","By filtering and grouping entities based on tag values","By automatically creating security groups based on tags","By automatically encrypting data based on tags","By assigning access control permissions based on tags","You can use tags to filter and group entities in Detective based on their tag values, making it easier to focus on specific resources or environments during an investigation."
"What is the primary goal of Amazon Detective's 'anomaly detection' feature?","To highlight deviations from established behavioural patterns","To identify known malware signatures","To prevent brute-force attacks","To enforce security policies","The primary goal of Detective's anomaly detection feature is to highlight deviations from established behavioural patterns, indicating potentially suspicious or malicious activity."
"When using Amazon Detective for incident response, what is the first step you should typically take?","Review findings from GuardDuty or other security services","Isolate the affected resources","Contain the compromised system","Eradicate the root cause","The first step when using Detective for incident response is typically to review findings from GuardDuty or other security services to understand the initial indicators of compromise."
"Which of the following actions can you perform directly from the Amazon Detective console?","View details about user activities and resource interactions","Remediate security vulnerabilities","Modify IAM permissions","Terminate EC2 instances","From the Detective console, you can view details about user activities and resource interactions to understand the context of a security incident, but you cannot directly remediate vulnerabilities or modify IAM permissions."
"What type of information can you find related to IAM roles within Amazon Detective?","API calls made using the role's credentials","The last time the role was used","All IAM policies attached to the role","The number of EC2 instances assumed by the role","Within Detective, you can find information about API calls made using a role's credentials, allowing you to understand what actions were performed with that role."
"How does Amazon Detective help security teams prioritise security alerts?","By providing contextual information and relationships between entities","By automatically assigning severity levels to alerts","By integrating with ticketing systems","By generating executive summary reports","Detective helps security teams prioritise alerts by providing contextual information and relationships between entities, making it easier to assess the potential impact of a security incident."
"When integrating Amazon Detective with AWS Organizations, what is the recommended best practice for enabling data collection?","Enable data collection in all member accounts","Enable data collection only in the management account","Enable data collection only in accounts with GuardDuty enabled","Enable data collection in a subset of member accounts based on risk","The recommended best practice is to enable data collection in all member accounts within AWS Organizations to ensure comprehensive visibility across your entire AWS environment."
"How does Amazon Detective handle data privacy?","By anonymizing sensitive data before ingestion","By providing tools for redacting sensitive data","By storing all data in encrypted format","It does not handle data privacy","Amazon Detective stores all data in encrypted format to protect data privacy."