"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Amazon GuardDuty?","Threat detection service that monitors for malicious activity and unauthorised behaviour to protect your AWS accounts and workloads.","A vulnerability scanning tool for EC2 instances.","A firewall service that blocks suspicious traffic.","A data loss prevention (DLP) solution.","GuardDuty continuously monitors your AWS environment for malicious activity and provides security findings."
"Which data sources does Amazon GuardDuty analyse to identify potential threats?","VPC Flow Logs, AWS CloudTrail event logs, and DNS logs","S3 bucket logs, CloudWatch metrics, and IAM access logs","RDS audit logs, EBS volume snapshots, and CloudFront access logs","ELB access logs, Route 53 query logs, and Config rules","GuardDuty analyses VPC Flow Logs, AWS CloudTrail event logs, and DNS logs to detect malicious activity."
"How does Amazon GuardDuty notify you of detected security threats?","Through Security Hub, CloudWatch Events, or directly through the GuardDuty console/API","Via email notifications to the root account holder.","By automatically isolating affected resources.","By sending SMS alerts to configured phone numbers.","GuardDuty integrates with Security Hub and CloudWatch Events to deliver findings, and you can view them in the GuardDuty console or retrieve them via the API."
"What type of machine learning is used by Amazon GuardDuty to improve threat detection accuracy?","Anomaly detection","Supervised learning","Reinforcement learning","Generative adversarial networks","GuardDuty uses machine learning, specifically anomaly detection, to identify unusual or unexpected activity."
"What is the purpose of Amazon GuardDuty's threat intelligence feeds?","To identify known malicious IP addresses, domains, and URLs","To analyse network traffic patterns.","To provide real-time operating system patches.","To manage AWS Identity and Access Management (IAM) roles.","GuardDuty uses threat intelligence feeds to identify activity originating from or directed towards known bad actors."
"Can Amazon GuardDuty be enabled in a single AWS account, or does it require organisation-wide activation?","It can be enabled in a single account or across an entire AWS organisation.","It requires activation at the AWS organisation level.","It can only be enabled for specific regions within an account.","It requires integration with a third-party security tool to function.","GuardDuty can be enabled for individual accounts or managed centrally across an entire AWS organisation using AWS Organizations."
"Which of the following AWS services can be used to automatically remediate findings detected by Amazon GuardDuty?","AWS Lambda and AWS Systems Manager","AWS Config and AWS CloudFormation","AWS Step Functions and Amazon SQS","Amazon SNS and Amazon SES","GuardDuty findings can trigger AWS Lambda functions to automatically remediate security issues, often in conjunction with AWS Systems Manager."
"What kind of security findings does Amazon GuardDuty generate related to compromised EC2 instances?","Instance is behaving as part of a botnet, or is participating in DDOS attacks.","Instance is missing security patches.","Instance has weak password policies.","Instance is running outdated software.","GuardDuty can detect when an EC2 instance is compromised and is behaving as part of a botnet or participating in DDOS attacks."
"What level of access is required to enable and manage Amazon GuardDuty in an AWS account?","IAM user or role with administrator privileges","IAM user with read-only access to CloudTrail.","IAM user with S3 bucket access.","IAM user with EC2 instance management permissions.","Enabling and managing GuardDuty requires IAM user or role with administrator privileges to grant the necessary permissions."
"How does Amazon GuardDuty help protect data stored in Amazon S3 buckets?","By detecting unusual access patterns and potential data exfiltration attempts","By encrypting data at rest and in transit.","By implementing access control lists (ACLs).","By providing versioning and lifecycle management.","GuardDuty can detect unusual access patterns to S3 buckets, helping to identify potential data exfiltration attempts."
"What is the typical pricing model for Amazon GuardDuty?","Pay-as-you-go based on the volume of data processed from CloudTrail, VPC Flow Logs, and DNS logs.","Fixed monthly fee per AWS account.","Free for the first year, then a fixed annual fee.","Pay-per-finding detected.","GuardDuty uses a pay-as-you-go model based on the volume of data processed from CloudTrail Events, VPC Flow Logs, and DNS queries."
"What is the purpose of the Amazon GuardDuty Malware Protection for EBS volumes?","To scan EBS volumes for malware infections.","To encrypt EBS volumes at rest.","To create backups of EBS volumes.","To manage EBS volume lifecycle.","The GuardDuty Malware Protection for EBS volumes scans EBS volumes attached to EC2 instances for potential malware infections."
"Which AWS service does Amazon GuardDuty use to monitor network traffic within your VPC?","VPC Flow Logs","AWS Network Firewall","AWS Shield","Amazon Inspector","GuardDuty uses VPC Flow Logs to monitor network traffic within your VPC for suspicious patterns."
"What is the purpose of the 'Suppressed' status for a GuardDuty finding?","To prevent the finding from generating further notifications.","To indicate that the finding has been resolved.","To mark the finding as a false positive.","To escalate the finding to a higher security level.","Suppressing a finding prevents it from generating further notifications, typically used for known or acceptable activity."
"Can Amazon GuardDuty detect malicious activity originating from within your own AWS environment (e.g., from a compromised EC2 instance)?","Yes, it can detect both external and internal threats.","No, it only detects threats originating from outside your AWS environment.","Yes, but only if you enable enhanced logging.","No, you need a third party tool.","GuardDuty monitors for both external threats and malicious activity originating from within your own AWS environment."
"What is the maximum retention period for GuardDuty findings?","90 days","30 days","60 days","120 days","GuardDuty findings are retained for 90 days."
"Which region(s) should you enable Amazon GuardDuty in for comprehensive coverage?","All AWS regions where your AWS resources are deployed","Only the AWS region where your primary data is stored.","Only the AWS region where your AWS account was created.","A single region of your choosing.","For comprehensive coverage, you should enable GuardDuty in all AWS regions where your AWS resources are deployed."
"What is the purpose of the 'Trust List' in Amazon GuardDuty?","To whitelist known trusted IP addresses, domains, and URLs","To blacklist known malicious IP addresses, domains, and URLs.","To manage user access permissions.","To define security policies for your AWS resources.","The Trust List allows you to whitelist trusted IP addresses, domains, and URLs to reduce false positives."
"What is the purpose of the 'Threat List' in Amazon GuardDuty?","To specify known malicious IP addresses, domains, and URLs that GuardDuty should actively monitor","To define the severity levels of different types of threats.","To prioritise security findings based on risk score.","To define custom remediation actions for specific threats.","The Threat List allows you to specify known malicious IP addresses, domains, and URLs that GuardDuty should actively monitor."
"How can you export Amazon GuardDuty findings for further analysis or integration with other security tools?","By configuring integration with Amazon S3, CloudWatch Logs, or Security Hub","By manually downloading findings in CSV format.","By using the GuardDuty API to retrieve findings programmatically.","By setting up a cross-account role.","GuardDuty findings can be exported to Amazon S3 or integrated with CloudWatch Logs or Security Hub for further analysis and integration."
"You have received a GuardDuty finding related to a potentially compromised EC2 instance. What should be your first step in the investigation?","Isolate the instance from the network to prevent further damage.","Immediately terminate the instance.","Change the instance's IAM role.","Disable the instance's monitoring.","Isolating the instance from the network is a critical first step to prevent further damage or data exfiltration."
"Can Amazon GuardDuty detect attempts to disable or tamper with CloudTrail logs?","Yes, GuardDuty monitors CloudTrail logs for suspicious activity.","No, GuardDuty does not monitor CloudTrail logs.","Yes, but only if you have enabled CloudTrail encryption.","Only for IAM changes.","GuardDuty monitors CloudTrail logs for suspicious activity, including attempts to disable or tamper with them."
"Which of the following is NOT a finding type generated by Amazon GuardDuty?","Suspicious login attempts","Compromised EC2 instances","Malicious network activity","Unencrypted S3 buckets","Unencrypted S3 buckets is not a threat detected by GuardDuty, its scope is threat detection not configuration issues."
"How can you centrally manage Amazon GuardDuty across multiple AWS accounts in an organisation?","By designating a master account in AWS Organizations.","By using AWS IAM to create cross-account roles.","By configuring a shared S3 bucket for log storage.","By using AWS Config to enforce security policies.","Using the master account in AWS Organizations, you can centrally manage GuardDuty across multiple member accounts."
"What is the role of AWS Security Hub in relation to Amazon GuardDuty?","Security Hub centralises security findings from GuardDuty and other AWS security services","Security Hub automates remediation actions for GuardDuty findings","Security Hub provides vulnerability scanning for EC2 instances","Security Hub provides compliance checks for AWS resources","Security Hub centralises security findings from various AWS services, including GuardDuty, providing a unified view of your security posture."
"Which of the following is a characteristic of Amazon GuardDuty?","It is a regional service.","It is a global service.","It requires installing agents on EC2 instances.","It only supports Linux instances.","GuardDuty is a regional service, meaning you need to enable it in each region you want to monitor."
"You need to investigate a GuardDuty finding that indicates a compromised EC2 instance. What information can you find in the finding details?","Instance ID, affected AWS account, severity level, and suggested remediation steps","The full operating system log of the instance.","The complete network traffic history for the instance.","A list of all installed software on the instance.","GuardDuty findings provide details such as the Instance ID, affected AWS account, severity level, and suggested remediation steps."
"What is the purpose of the 'Auto-Archive' feature in Amazon GuardDuty?","To automatically archive resolved findings after a specified period.","To automatically back up GuardDuty configuration settings.","To automatically delete old findings to save storage costs.","To automatically remediate security issues.","Auto-Archive automatically archives resolved findings after a specified period, helping to keep your finding list clean."
"Which of the following is a benefit of using Amazon GuardDuty over traditional security solutions?","No need to manage infrastructure, as it is a fully managed service.","Lower cost than traditional security solutions.","More comprehensive feature set than traditional security solutions.","Faster threat detection than traditional security solutions.","GuardDuty is fully managed, eliminating the need to manage infrastructure or software updates."
"What type of log does Amazon GuardDuty analyse for DNS queries?","DNS logs","CloudTrail logs","VPC Flow Logs","ELB access logs","GuardDuty analyses DNS logs to detect suspicious DNS queries, such as those related to known malicious domains."
"How can you customise Amazon GuardDuty to detect specific types of threats relevant to your environment?","By creating custom threat lists and trust lists.","By writing custom Lambda functions.","By configuring custom CloudWatch metrics.","By using third-party security tools.","You can customise GuardDuty by creating custom threat lists and trust lists to focus on specific threats relevant to your environment."
"What is the impact of disabling Amazon GuardDuty on your AWS environment?","You will no longer receive security findings from the service.","Your AWS resources will be automatically protected by AWS Shield.","Your AWS account will be automatically suspended.","All your EC2 instances will be terminated.","Disabling GuardDuty means you will no longer receive security findings, reducing your ability to detect and respond to threats."
"Which of the following AWS services can be used to visualise Amazon GuardDuty findings?","Amazon QuickSight","AWS CloudTrail","AWS Config","Amazon Inspector","GuardDuty findings can be visualised using Amazon QuickSight to provide insights and dashboards."
"What does a GuardDuty finding with a high severity level indicate?","A critical security threat that requires immediate attention.","A potential false positive that should be investigated.","A minor security issue that can be addressed later.","A performance issue that needs optimisation.","A high severity finding indicates a critical security threat that requires immediate attention."
"Which AWS service integrates with Amazon GuardDuty to provide automated remediation actions for security findings?","AWS Systems Manager","AWS CloudWatch","AWS IAM","AWS Config","AWS Systems Manager can be used to automate remediation actions for security findings detected by GuardDuty."
"What is the purpose of the GuardDuty Malware Protection's 'Runtime Monitoring' feature?","To detect malware executing in EC2 instances at runtime.","To scan EBS volumes before instances are launched.","To prevent malware from being uploaded to S3 buckets.","To encrypt network traffic between EC2 instances.","GuardDuty Malware Protection's Runtime Monitoring detects malware executing in EC2 instances at runtime by monitoring system calls and file system events."
"Which data source can be configured as a custom data source within Amazon GuardDuty?","S3 event logs","CloudWatch metrics","IAM access logs","RDS audit logs","S3 event logs can be configured as a custom data source."
"Can Amazon GuardDuty findings be used to trigger automated responses using AWS Lambda?","Yes, GuardDuty findings can be used to trigger AWS Lambda functions.","No, only Security Hub can trigger Lambda functions.","Yes, but only for high-severity findings.","No, GuardDuty can only send email notifications.","GuardDuty findings can be configured to trigger AWS Lambda functions, enabling automated responses and remediation actions."
"Which of the following findings could indicate a reconnaissance attempt by an attacker?","Unusual port scanning activity.","Excessive database queries.","High CPU utilisation on an EC2 instance.","Frequent changes to IAM roles.","Unusual port scanning activity is a sign of an attacker trying to discover open ports and running services, which is a common reconnaissance technique."
"What is the purpose of enabling GuardDuty across multiple regions in an AWS Organization?","To consolidate security findings from all regions into a single view.","To improve network performance across regions.","To reduce the cost of GuardDuty.","To enable cross-region replication of data.","Enabling GuardDuty across multiple regions consolidates security findings into a single view, simplifying security management across your entire AWS footprint."
"When should you consider enabling the GuardDuty S3 Protection?","When you have sensitive data stored in S3 buckets and need to monitor for unusual access patterns.","When you want to encrypt data stored in S3 buckets.","When you want to enforce compliance policies for S3 buckets.","When you want to optimize the cost of storing data in S3 buckets.","The S3 Protection feature monitors S3 buckets for unusual access patterns, such as brute-force attempts or data exfiltration, protecting sensitive data."
"How can you estimate the cost of running Amazon GuardDuty in your AWS environment?","By using the AWS Pricing Calculator.","By contacting AWS Support.","By monitoring your CloudWatch billing metrics.","By enabling the GuardDuty free trial.","The AWS Pricing Calculator allows you to estimate the cost of running GuardDuty based on your expected usage of CloudTrail Events, VPC Flow Logs, and DNS Queries."
"What type of security threats does Amazon GuardDuty help protect against regarding AWS Lambda functions?","Malicious code injection, unauthorised access attempts, and resource exhaustion.","DDOS attacks, SQL injection, and cross-site scripting.","Compromised EC2 instances, unencrypted S3 buckets, and vulnerable applications.","Data loss prevention, identity theft, and phishing attacks.","GuardDuty helps protect Lambda functions against threats such as malicious code injection, unauthorised access attempts, and resource exhaustion."
"You have integrated Amazon GuardDuty with Security Hub. Where will the findings from GuardDuty be displayed?","In the Security Hub console.","In the CloudWatch console.","In the GuardDuty console.","In the IAM console.","When integrated, GuardDuty findings are displayed in the Security Hub console, providing a centralised view of your security posture."
"What is the key difference between the 'Trust List' and the 'Threat List' in Amazon GuardDuty?","Trust List whitelists known safe entities, while Threat List blacklists known malicious entities.","Trust List defines user access permissions, while Threat List defines security policies.","Trust List prioritises security findings, while Threat List suppresses findings.","Trust List automates remediation actions, while Threat List generates alerts.","The Trust List whitelists known safe IP addresses, domains, and URLs, reducing false positives, while the Threat List specifies known malicious entities that GuardDuty should actively monitor."
"Which AWS resource can be used as a data source to generate custom insights when integrated with Amazon GuardDuty?","AWS CloudTrail logs from AWS KMS","AWS Trusted Advisor recommendations","AWS Config rules","AWS Service Health Dashboard","GuardDuty can use CloudTrail logs from AWS KMS to generate custom insights, detecting unusual or unauthorised KMS usage."
"Your organisation has a policy requiring that all security events be retained for at least one year for compliance purposes. How can you achieve this with Amazon GuardDuty?","By exporting GuardDuty findings to an S3 bucket and configuring a lifecycle policy to retain the data for one year.","By enabling GuardDuty's built-in long-term storage option.","By subscribing to AWS Security Hub's premium tier.","By enabling AWS CloudTrail's multi-region trail feature.","Exporting GuardDuty findings to an S3 bucket and configuring a lifecycle policy provides long-term storage for compliance purposes."
"You suspect a compromised EC2 instance is attempting to exfiltrate data to a known malicious IP address. How can you verify this using Amazon GuardDuty?","Check GuardDuty findings for alerts related to suspicious outbound traffic to the malicious IP address.","Review the EC2 instance's CloudWatch metrics for unusual network activity.","Examine the EC2 instance's system logs for evidence of data exfiltration.","Use AWS Network Firewall to block traffic to the malicious IP address.","GuardDuty will generate findings related to suspicious outbound traffic to known malicious IP addresses."
"What is the recommended approach for enabling Amazon GuardDuty in a new AWS account within your organisation?","Use AWS CloudFormation to automate the deployment of GuardDuty with pre-defined settings.","Manually configure GuardDuty settings in each new account.","Use AWS Config to enforce GuardDuty compliance.","Use AWS Organizations service control policies (SCPs) to enable GuardDuty.","Using CloudFormation automates deployment with pre-defined settings, ensuring consistency and reducing manual effort."
"Your team is investigating a GuardDuty finding that indicates a potential brute-force attack against an SSH server on an EC2 instance. What additional information should you gather to assess the scope and impact of the attack?","Review the EC2 instance's security group rules, network ACLs, and authentication logs.","Examine the EC2 instance's CPU utilisation, memory usage, and disk I/O.","Analyse the EC2 instance's CloudTrail events for IAM role changes.","Monitor the EC2 instance's network traffic for malicious patterns.","Reviewing security group rules, network ACLs, and authentication logs helps determine if the attacker gained access and what actions they performed."
"In Amazon GuardDuty, what is a finding?","A potential security issue detected in your AWS environment.","A report on AWS spending.","A list of trusted IP addresses.","A set of AWS security best practices.","GuardDuty findings represent potential security risks or malicious activity detected in your AWS environment, giving you visibility into possible threats."
"What type of data source does Amazon GuardDuty primarily analyse for threats?","VPC Flow Logs, CloudTrail Logs, and DNS Logs","S3 Bucket Contents, EC2 Instance Memory, and EBS Volume Snapshots","IAM Role Policies, Security Group Rules, and Route Tables","CloudWatch Metrics, CloudFront Access Logs, and Lambda Function Code","GuardDuty analyses VPC Flow Logs, CloudTrail Logs, and DNS Query Logs to identify malicious activity and potential security threats in your AWS environment."
"Which of the following AWS services is NOT directly integrated with Amazon GuardDuty for remediation actions?","AWS Lambda","AWS CloudWatch Events (now EventBridge)","AWS Systems Manager","AWS Config","GuardDuty directly integrates with Lambda and EventBridge for automated remediation. While Systems Manager can be part of a remediation workflow triggered by GuardDuty, it's not directly integrated in the same way. AWS Config is related to rules and compliance but not the event triggered remediation workflow directly."
"If GuardDuty identifies a potentially compromised EC2 instance, what is a typical remediation step you might take?","Isolate the instance by updating security group rules.","Terminate the instance immediately.","Increase the instance's CPU capacity.","Change the instance's IAM role.","Isolating the instance by updating its security group rules prevents further communication and limits the potential damage caused by the compromised instance."
"What does Amazon GuardDuty use to detect suspicious DNS activity?","DNS query logs","CloudTrail events","VPC Flow Logs","S3 access logs","GuardDuty analyses DNS query logs to detect suspicious patterns and identify potential threats, such as malware communication or data exfiltration attempts."
"Which of the following is a valid way to enable Amazon GuardDuty?","Using the AWS Management Console, AWS CLI, or AWS CloudFormation","Using only the AWS CLI","Using only AWS CloudFormation","Using only the AWS Management Console","Amazon GuardDuty can be enabled and managed using various methods, including the AWS Management Console, AWS CLI, and AWS CloudFormation."
"What is the purpose of a GuardDuty suppression rule?","To reduce the number of false positive findings.","To automatically remediate all findings.","To disable GuardDuty for a specific region.","To increase the severity of certain findings.","Suppression rules allow you to suppress findings that you've determined to be benign or irrelevant to your environment, reducing noise and focusing on genuine threats."
"Which AWS service can be used to automate remediation actions based on Amazon GuardDuty findings?","AWS Lambda","AWS IAM","AWS CloudWatch Logs","AWS S3","AWS Lambda functions can be triggered by CloudWatch Events (EventBridge) based on GuardDuty findings, enabling automated remediation actions such as isolating an instance or blocking an IP address."
"How does Amazon GuardDuty use machine learning?","To identify anomalous activity patterns.","To encrypt data at rest.","To manage IAM permissions.","To configure network settings.","GuardDuty uses machine learning algorithms to analyse data and identify unusual patterns that may indicate malicious activity or security threats."
"What is the primary benefit of using Amazon GuardDuty?","Provides continuous security monitoring and threat detection.","Provides a fully managed firewall.","Provides a centralised identity management system.","Provides automated vulnerability scanning.","The primary benefit of GuardDuty is its continuous security monitoring and threat detection capabilities, helping you identify and respond to potential security incidents in your AWS environment."
"You've received a GuardDuty finding indicating potential malicious activity from an IP address. Where can you find more details about the IP address?","Threat intelligence feeds integrated with GuardDuty.","AWS Config console.","AWS Trusted Advisor.","AWS Service Health Dashboard.","GuardDuty integrates with threat intelligence feeds to provide contextual information about IP addresses and other indicators of compromise, helping you understand the nature and severity of potential threats."
"What is the default data retention period for GuardDuty findings?","90 days","30 days","60 days","14 days","By default, GuardDuty retains findings for 90 days, allowing you to review historical security events and investigate potential incidents over time."
"What is the relationship between Amazon GuardDuty and AWS Security Hub?","GuardDuty findings can be ingested into Security Hub for centralised security management.","GuardDuty replaces Security Hub functionality.","Security Hub replaces GuardDuty functionality.","GuardDuty and Security Hub are completely independent services.","GuardDuty findings can be sent to Security Hub, providing a consolidated view of security alerts and compliance status across your AWS environment."
"What is a GuardDuty 'threat list'?","A list of known malicious IP addresses, domains, and other indicators of compromise.","A list of trusted IP addresses for whitelisting.","A list of AWS services protected by GuardDuty.","A list of users with access to GuardDuty findings.","GuardDuty uses threat lists, which contain known malicious IP addresses, domains, and other indicators of compromise, to identify potential threats in your AWS environment."
"Can GuardDuty be used to monitor multiple AWS accounts?","Yes, GuardDuty supports multi-account monitoring through AWS Organizations.","No, GuardDuty can only monitor a single AWS account.","Yes, but only if all accounts are in the same region.","Yes, but each account requires a separate GuardDuty subscription.","GuardDuty supports multi-account monitoring through AWS Organizations, allowing you to centrally manage security across your entire AWS environment."
"What does GuardDuty analyse regarding S3 buckets?","CloudTrail events related to S3 bucket access.","Contents of S3 buckets.","S3 bucket policies.","Encryption status of S3 buckets.","GuardDuty analyses CloudTrail events related to S3 bucket access to detect suspicious activity, such as unauthorised access or data exfiltration attempts."
"How can you export GuardDuty findings for further analysis?","By configuring a CloudWatch Events (EventBridge) rule to send findings to an S3 bucket or other destination.","By manually downloading findings from the GuardDuty console.","By enabling AWS Config to record GuardDuty findings.","By subscribing to an SNS topic for GuardDuty notifications.","GuardDuty findings can be exported by configuring a CloudWatch Events (EventBridge) rule to send them to an S3 bucket, Lambda function, or other destination for further analysis and processing."
"What is the purpose of the GuardDuty malware protection feature?","To scan EC2 instances and container images for malware at runtime.","To prevent malware from being uploaded to S3 buckets.","To encrypt EC2 volumes.","To restrict network traffic based on known malware signatures.","The malware protection feature allows GuardDuty to scan EC2 instances and container images to identify and remediate potential malware infections."
"What type of CloudTrail logs are analysed by GuardDuty?","Management events and data events","Only management events","Only data events","Only global service events","GuardDuty analyses both management events and data events in CloudTrail logs to detect a wide range of suspicious activity, including unauthorised API calls and data access attempts."
"Which AWS region is required to be enabled for centralised GuardDuty management across multiple accounts?","The AWS region designated as the GuardDuty administrator account's region.","The us-east-1 region.","The region with the most AWS resources.","Any region, as long as all accounts are in the same region.","The AWS region where the GuardDuty administrator account is configured is the one that must be enabled for centralised management across multiple accounts within AWS Organizations."
"What does Amazon GuardDuty NOT provide out-of-the-box?","Vulnerability scanning.","Threat detection.","Continuous security monitoring.","Integration with AWS security services.","GuardDuty provides threat detection and continuous security monitoring, and integrates with other AWS security services, but it doesn't offer vulnerability scanning as a built-in feature."
"In Amazon GuardDuty, what does the 'Severity' level of a finding indicate?","The potential impact and risk associated with the detected activity.","The time it took to detect the activity.","The number of resources affected by the activity.","The confidence level that the activity is malicious.","The 'Severity' level in a GuardDuty finding indicates the potential impact and risk associated with the detected activity, helping you prioritise remediation efforts."
"You want to automatically create a snapshot of an EC2 instance when GuardDuty detects suspicious activity. Which AWS service can facilitate this automation?","AWS Lambda","AWS CloudTrail","AWS CloudWatch Logs","AWS IAM","AWS Lambda functions can be triggered by CloudWatch Events (EventBridge) based on GuardDuty findings, allowing you to automate tasks such as creating a snapshot of an EC2 instance in response to suspicious activity."
"Which of the following is a key benefit of using GuardDuty with container environments (e.g., EKS, ECS)?","Detection of malicious container activity and vulnerabilities.","Automatic scaling of container resources.","Simplified container deployment.","Centralised container logging.","GuardDuty helps detect malicious activity and vulnerabilities within container environments, providing enhanced security for containerised applications."
"How does GuardDuty detect potentially compromised credentials?","By analysing CloudTrail logs for unusual API activity.","By scanning IAM role policies.","By monitoring network traffic for credential leakage.","By checking passwords against known compromised password lists.","GuardDuty analyses CloudTrail logs for unusual API activity that may indicate compromised credentials being used to access AWS resources."
"What is the maximum number of AWS accounts that can be managed by a single GuardDuty administrator account in AWS Organizations?","There is no limit.","100","500","1000","GuardDuty supports managing all accounts within your AWS Organizations structure, allowing you to centrally manage security across your entire organisation without a hard limit on the number of accounts."
"What does GuardDuty analyse to detect potential crypto currency mining activity?","DNS queries and network traffic patterns.","S3 bucket access logs.","EC2 instance CPU utilisation metrics.","IAM role permissions.","GuardDuty analyses DNS queries and network traffic patterns to identify communication with known crypto currency mining pools, indicating potential mining activity on your AWS resources."
"You want to receive real-time notifications when GuardDuty generates a new finding. Which AWS service should you integrate with GuardDuty?","Amazon SNS","AWS CloudWatch Logs","AWS Config","AWS Trusted Advisor","GuardDuty can be integrated with Amazon SNS to send real-time notifications whenever a new finding is generated, allowing you to respond quickly to potential security incidents."
"What type of information does the GuardDuty 'threat intelligence' provide about a finding?","Information about the source IP address, reputation, and potential malicious intent.","Information about the affected AWS resource, such as EC2 instance type or S3 bucket name.","Information about the remediation steps to take to resolve the finding.","Information about the AWS service that generated the finding.","The threat intelligence provided by GuardDuty offers details about the source IP address, its reputation, and the likelihood of malicious intent, helping you assess the severity of the finding."
"Which of the following network protocols is analysed by GuardDuty for suspicious activity?","DNS","HTTPS","SMTP","FTP","GuardDuty analyses DNS traffic, among other data sources, to detect suspicious activity, such as communication with known malicious domains or data exfiltration attempts."
"What is the purpose of the GuardDuty 'feedback' mechanism?","To provide feedback on the accuracy and relevance of findings.","To request new features for GuardDuty.","To report bugs in the GuardDuty service.","To provide suggestions for improving AWS security best practices.","The feedback mechanism allows you to provide feedback on the accuracy and relevance of findings, helping GuardDuty improve its detection capabilities and reduce false positives."
"If GuardDuty identifies an S3 bucket being accessed from an unusual location, what type of finding would it generate?","UnauthorizedAccess:S3/MaliciousIPCaller","Policy:IAMUser/S3Permissions","CloudTrail:APICalls/Unauthorized","CryptoCurrency:S3/Mining","GuardDuty generates a finding like 'UnauthorizedAccess:S3/MaliciousIPCaller' when it detects an S3 bucket being accessed from an IP address known for malicious activity."
"What is the primary purpose of the GuardDuty Security Health findings?","To identify potential configuration weaknesses in your AWS environment.","To track the overall security posture of your AWS account.","To identify active threats and malicious activity.","To recommend AWS security best practices.","The primary purpose of Security Health findings in GuardDuty is to pinpoint potential configuration weaknesses in your AWS environment, allowing you to address security gaps proactively."
"Which of the following AWS CloudTrail event types is used by GuardDuty to analyse potential reconnaissance activity?","ConsoleLogin","CreateSecurityGroup","ModifyNetworkInterfaceAttribute","RunInstances","GuardDuty uses events like 'ConsoleLogin' to analyse for reconnaissance activities that could precede other malicious actions."
"You need to ensure GuardDuty is enabled in all new AWS accounts created within your AWS Organization. How can you achieve this?","Enable GuardDuty as the delegated administrator account for your Organization.","Manually enable GuardDuty in each new account.","Create an AWS Config rule to automatically enable GuardDuty.","Use AWS CloudFormation StackSets to deploy GuardDuty.","By setting the delegated administrator account, you ensure that GuardDuty is enabled automatically in all accounts created within your organization. Manual configuration would be a lot of overhead."
"What is the benefit of using GuardDuty Runtime Monitoring?","It provides threat detection inside EC2 instances and container workloads.","It provides cost optimization recommendations for EC2 instances.","It provides compliance reports for EC2 instances.","It provides vulnerability scanning for EC2 instances.","Runtime Monitoring detects threats inside EC2 and containers, protecting against malicious activity and unauthorized access."
"You have a GuardDuty finding for a potential compromised instance. What does 'Instance is part of a known botnet' suggest?","The instance is communicating with known command-and-control servers.","The instance is running cryptomining software.","The instance is performing a large number of failed login attempts.","The instance has been infected with malware.","'Instance is part of a known botnet' means the instance is communicating with command-and-control servers, indicating its part of a botnet."
"How can you programmatically retrieve GuardDuty findings?","Using the AWS CLI or SDKs.","Using the AWS Management Console only.","Using AWS CloudWatch metrics only.","Using AWS Trusted Advisor only.","GuardDuty findings can be retrieved programmatically via the AWS CLI or SDKs, allowing for automation and integration with other systems."
"What type of data is ingested by GuardDuty to detect potentially malicious activity in Lambda functions?","CloudTrail logs of Lambda function invocations and API calls.","Lambda function code.","VPC Flow Logs from Lambda functions.","S3 access logs related to Lambda functions.","GuardDuty uses CloudTrail logs of Lambda function invocations and API calls to monitor for potentially malicious activity within Lambda functions."
"A GuardDuty finding indicates 'Backdoor:EC2/C&CActivity.B'. What does this likely mean?","The EC2 instance is communicating with a known command-and-control server.","The EC2 instance is running a vulnerable version of software.","The EC2 instance has an open port exposing a sensitive service.","The EC2 instance is acting as a DNS server for a malicious domain.","'Backdoor:EC2/C&CActivity.B' means the EC2 instance is communicating with a command-and-control server, suggesting it might be compromised."
"Which of the following actions will remove GuardDuty completely?","Disabling GuardDuty in all AWS regions.","Deleting the GuardDuty detector.","Suspending the AWS account.","Deleting all AWS resources.","Deleting the GuardDuty detector deletes the service and findings. Disabling it in the AWS region just stops GuardDuty from processing data, but you will be charged."
"What is the key benefit of using AWS Organizations with GuardDuty?","Centralised management of GuardDuty across multiple AWS accounts.","Automatic vulnerability scanning across all accounts.","Simplified IAM role management for all accounts.","Automated cost optimisation for all accounts.","AWS Organizations allows you to centrally manage GuardDuty, providing security visibility across your AWS accounts."
"You suspect a specific IP address is malicious. How can you proactively flag this in GuardDuty?","Upload a custom threat list containing the IP address.","Create a custom rule in AWS WAF.","Add the IP address to a security group.","Create a new IAM policy to block the IP address.","Uploading a custom threat list to GuardDuty will flag future malicious activity and allow you to proactively protect your network traffic."
"What is the purpose of the GuardDuty SNS topic?","To receive notifications about new GuardDuty findings.","To configure log delivery to S3.","To manage user permissions for GuardDuty.","To trigger automated remediation actions.","The SNS topic is used to send notifications about new GuardDuty findings, enabling you to receive real-time alerts."
"How does GuardDuty identify suspicious activity originating from within your VPC?","By analysing VPC Flow Logs.","By analysing S3 access logs.","By analysing CloudTrail logs.","By analysing DNS query logs.","GuardDuty uses VPC Flow Logs to analyse network traffic within your VPC for suspicious activity, such as unusual communication patterns or connections to known malicious IP addresses."
"If you suspect that a specific EC2 instance is generating false positive GuardDuty findings, what can you do?","Create a suppression rule to ignore findings related to the instance.","Terminate the instance immediately.","Increase the instance's CPU capacity.","Change the instance's IAM role.","Creating a suppression rule lets you ignore the finding if the instance if working normally, avoiding false alarms and allowing focusing on other potential malicious activities."
"Which of the following is NOT a valid data source for Amazon GuardDuty?","AWS Config Configuration Items","VPC Flow Logs","CloudTrail Logs","DNS Logs","GuardDuty analyses VPC Flow Logs, CloudTrail Logs, and DNS Query Logs to identify malicious activity and potential security threats in your AWS environment. It doesn't directly use AWS Config Configuration Items as a primary data source for threat detection."
"What type of vulnerability assessment does Amazon Inspector provide that Amazon GuardDuty does not?","Host-based vulnerability assessments of EC2 instances.","Network-based vulnerability assessments of VPCs.","Compliance-based assessments of IAM policies.","Threat intelligence-based assessments of IP addresses.","Amazon Inspector provides host-based vulnerability assessments, which look for security vulnerabilities in the operating system and applications running on EC2 instances, whereas GuardDuty focuses on threat detection based on network activity, DNS queries, and CloudTrail logs."
"What is the primary function of Amazon GuardDuty?","Threat detection","Vulnerability scanning","Compliance reporting","Cost optimisation","GuardDuty continuously monitors your AWS accounts and workloads for malicious activity and unauthorised behaviour."
"Which of the following data sources does Amazon GuardDuty analyse for threat detection?","VPC Flow Logs, AWS CloudTrail event logs, and DNS logs","S3 bucket data, RDS database logs, and EC2 instance metadata","CloudWatch metrics, IAM role policies, and Config rules","Lambda function code, API Gateway logs, and CloudFront access logs","GuardDuty ingests and analyses VPC Flow Logs, AWS CloudTrail event logs, and DNS logs to identify malicious activity."
"What type of finding does Amazon GuardDuty generate?","Informational, Low, Medium, and High severity","Critical, Major, Minor, and Trivial severity","Alert, Warning, Notice, and Debug severity","Error, Success, Pending, and Failed status","GuardDuty findings are categorised into four severity levels: Informational, Low, Medium, and High, indicating the potential impact of the detected threat."
"How can you enable Amazon GuardDuty for your AWS account?","Through the AWS Management Console, AWS CLI, or AWS SDKs","By creating a new IAM role with GuardDuty permissions","By installing the GuardDuty agent on all EC2 instances","By configuring VPC Flow Logs and CloudTrail to send data to GuardDuty","GuardDuty can be enabled easily through the AWS Management Console, AWS CLI, or AWS SDKs without requiring any agent installation."
"What is the purpose of the GuardDuty 'Master' account?","To centrally manage GuardDuty across multiple AWS accounts","To store all GuardDuty findings in a single S3 bucket","To provide administrative access to all GuardDuty features","To automatically remediate all GuardDuty findings","The GuardDuty 'Master' account is used to centrally manage GuardDuty across multiple AWS accounts in an organisation."
"Which AWS service can you use to automatically remediate GuardDuty findings?","AWS Lambda","Amazon CloudWatch Events (now EventBridge)","AWS Config","AWS Systems Manager","AWS Lambda, triggered by CloudWatch Events (now EventBridge), can be used to automatically remediate GuardDuty findings based on defined rules."
"What is the recommended method for receiving real-time notifications about new GuardDuty findings?","Configure Amazon CloudWatch Events (now EventBridge) to trigger an action","Check the GuardDuty console periodically","Subscribe to the GuardDuty SNS topic","Enable email notifications in the GuardDuty settings","Configuring Amazon CloudWatch Events (now EventBridge) to trigger an action (e.g., sending an email or invoking a Lambda function) provides real-time notifications for GuardDuty findings."
"Can GuardDuty detect malicious activity originating from compromised EC2 instances?","Yes, GuardDuty analyses network traffic and system logs to detect suspicious activity","No, GuardDuty only analyses CloudTrail event logs","Yes, if the EC2 instance has the GuardDuty agent installed","No, GuardDuty only detects threats against AWS services, not EC2 instances","GuardDuty can detect malicious activity originating from compromised EC2 instances by analysing network traffic patterns and unusual API calls."
"What is the relationship between VPC Flow Logs and Amazon GuardDuty?","GuardDuty uses VPC Flow Logs as a data source for threat detection","GuardDuty generates VPC Flow Logs","VPC Flow Logs are required to enable GuardDuty","GuardDuty encrypts VPC Flow Logs","GuardDuty leverages VPC Flow Logs, along with CloudTrail and DNS logs, as a crucial data source for identifying suspicious network activity."
"Does Amazon GuardDuty support custom threat intelligence feeds?","Yes, you can upload custom threat intelligence feeds to GuardDuty","No, GuardDuty only uses its built-in threat intelligence","Yes, but only for specific regions","No, custom threat feeds are not supported","GuardDuty allows you to upload custom threat intelligence feeds in STIX/TAXII format to enhance its threat detection capabilities."
"What type of log does GuardDuty use to detect malicious API usage?","AWS CloudTrail event logs","Amazon VPC Flow Logs","AWS Config logs","Amazon S3 access logs","GuardDuty uses AWS CloudTrail event logs to monitor API calls and detect suspicious or unauthorized activity."
"Which AWS service can you use to visualise GuardDuty findings?","Amazon Detective","AWS Security Hub","Amazon Inspector","AWS Trusted Advisor","Amazon Detective is purpose-built for investigating, analysing, and identifying the root cause of security findings from GuardDuty."
"What is the role of 'Threat Lists' in Amazon GuardDuty?","To provide known malicious IP addresses, domains, and URLs","To allowlist trusted IP addresses and AWS accounts","To categorise and prioritise GuardDuty findings","To configure notification settings for GuardDuty","Threat Lists in GuardDuty are lists of known malicious IP addresses, domains, and URLs that GuardDuty uses to identify potential threats."
"What is the purpose of 'Trusted IP Lists' in Amazon GuardDuty?","To whitelist known safe IP addresses that should not trigger findings","To blacklist known malicious IP addresses that should always trigger findings","To configure the regions where GuardDuty should operate","To define the data sources that GuardDuty should monitor","Trusted IP Lists are used to whitelist known safe IP addresses, preventing GuardDuty from generating findings for traffic originating from those sources."
"What is the recommended way to manage GuardDuty across multiple AWS accounts in an organisation?","Use the GuardDuty master account and invite member accounts","Create a separate GuardDuty detector in each AWS account","Consolidate all AWS accounts into a single AWS organisation","Manually configure GuardDuty settings in each AWS account","Using the GuardDuty master account and inviting member accounts simplifies the management of GuardDuty across multiple AWS accounts within an organisation."
"Can GuardDuty be enabled in all AWS regions?","Yes, GuardDuty is available in all AWS regions","No, GuardDuty is only available in specific AWS regions","Yes, but it requires additional configuration in some regions","No, GuardDuty is only available in regions with VPC Flow Logs enabled","GuardDuty is designed to be enabled in all AWS regions for comprehensive threat detection across your global infrastructure."
"What is the best practice for retaining GuardDuty findings?","Export findings to Amazon S3 or Amazon CloudWatch Logs","Delete findings after a certain period to reduce storage costs","Store findings only in the GuardDuty console","Disable finding generation to avoid storage costs","Exporting findings to Amazon S3 or Amazon CloudWatch Logs allows for long-term storage, analysis, and compliance reporting."
"Which type of attack can GuardDuty help detect by analysing DNS logs?","DNS data exfiltration attempts","Brute-force attacks on EC2 instances","SQL injection attacks against RDS databases","Denial-of-service (DoS) attacks against web applications","GuardDuty analyses DNS logs to detect DNS data exfiltration attempts, where attackers try to extract data by encoding it within DNS queries."
"What is the impact of enabling GuardDuty on the performance of your AWS resources?","Minimal, GuardDuty is designed to have a low performance impact","Significant, GuardDuty consumes a large amount of resources","Moderate, GuardDuty may cause some performance degradation during peak usage","None, GuardDuty does not impact the performance of AWS resources","GuardDuty is designed to have a minimal impact on the performance of your AWS resources by efficiently analysing log data."
"What is the cost model for Amazon GuardDuty?","Pay-as-you-go based on the volume of logs and events analysed","Fixed monthly fee per AWS account","Hourly rate per EC2 instance","One-time setup fee","GuardDuty uses a pay-as-you-go pricing model based on the volume of CloudTrail event logs, VPC Flow Logs, and DNS logs it analyses."
"How does GuardDuty detect potentially compromised EC2 instances?","By analysing network traffic for unusual patterns and known malicious IPs","By monitoring CPU utilisation and memory usage","By scanning the file system for malware signatures","By checking for vulnerabilities in installed software","GuardDuty detects compromised EC2 instances by analysing network traffic patterns, unusual API calls, and communication with known malicious IPs."
"Which AWS service does GuardDuty integrate with to provide centralised security management?","AWS Security Hub","AWS CloudTrail","AWS Config","AWS IAM","GuardDuty integrates with AWS Security Hub, which provides a centralised view of security alerts and compliance status across your AWS accounts."
"Can GuardDuty detect unusual behaviour in your AWS account based on IAM user activity?","Yes, GuardDuty analyses CloudTrail logs to identify suspicious IAM user behaviour","No, GuardDuty only monitors network traffic and DNS logs","Yes, if you enable IAM access analyser","No, you need to use AWS IAM Access Analyser","GuardDuty analyses CloudTrail logs to detect unusual IAM user activity, such as API calls from unfamiliar locations or attempts to escalate privileges."
"Which type of finding indicates that GuardDuty has detected suspicious network activity originating from an EC2 instance?","Trojan:EC2/MaliciousIPCaller.High","Policy:IAM/InvalidPermissions","CryptoCurrency:EC2/BitcoinTool.Tool","Recon:EC2/PortProbeTS","'Trojan:EC2/MaliciousIPCaller.High' indicates that an EC2 instance is communicating with a known malicious IP address, suggesting it may be compromised."
"What action should you take if GuardDuty generates a 'CryptoCurrency:EC2/BitcoinTool.Tool' finding?","Investigate the EC2 instance for cryptocurrency mining activity","Disable the EC2 instance immediately to prevent further damage","Isolate the EC2 instance from the network","Update the security group rules","A 'CryptoCurrency:EC2/BitcoinTool.Tool' finding indicates that an EC2 instance may be running cryptocurrency mining software, requiring investigation and remediation."
"How does GuardDuty detect potentially malicious DNS queries?","By comparing DNS queries against known malicious domains","By analysing the frequency of DNS queries","By checking the size of DNS responses","By monitoring DNS server CPU utilisation","GuardDuty detects malicious DNS queries by comparing them against a database of known malicious domains and detecting unusual patterns."
"What is the purpose of enabling GuardDuty in multiple AWS regions?","To provide comprehensive threat detection across your entire AWS infrastructure","To improve the performance of GuardDuty","To reduce the cost of GuardDuty","To comply with regional data residency requirements","Enabling GuardDuty in multiple AWS regions ensures that your entire AWS infrastructure is protected against threats, regardless of location."
"Which type of resource is most likely to be the source of a GuardDuty finding related to port scanning?","EC2 instance","S3 bucket","IAM role","Lambda function","EC2 instances are commonly used for port scanning activities, making them the most likely source of GuardDuty findings related to port scanning."
"What is the best way to integrate GuardDuty findings into your existing security information and event management (SIEM) system?","Export GuardDuty findings to Amazon S3 and configure your SIEM to ingest them","Forward GuardDuty findings directly to your SIEM using CloudWatch Events (now EventBridge)","Manually copy and paste findings from the GuardDuty console into your SIEM","Disable GuardDuty and rely on your SIEM for threat detection","Exporting GuardDuty findings to Amazon S3 and configuring your SIEM to ingest them allows for seamless integration and correlation of security events."
"What should you do to prevent GuardDuty findings related to misconfigured S3 buckets?","Implement strict S3 bucket policies and regularly audit access permissions","Disable public access to all S3 buckets","Encrypt all S3 bucket data","Enable S3 versioning","Implementing strict S3 bucket policies and regularly auditing access permissions helps prevent misconfigurations that can lead to GuardDuty findings."
"How does Amazon GuardDuty contribute to compliance efforts?","By providing automated threat detection and security monitoring","By generating compliance reports","By enforcing security policies","By automating security audits","GuardDuty contributes to compliance efforts by providing automated threat detection and security monitoring, helping organisations meet security requirements."
"Can GuardDuty detect attempts to access resources from Tor exit nodes?","Yes, GuardDuty uses threat intelligence feeds to identify Tor exit nodes","No, GuardDuty only detects threats from known malicious IP addresses","Yes, if you enable Tor exit node monitoring in the GuardDuty settings","No, Tor exit node detection is not supported","GuardDuty uses threat intelligence feeds to identify attempts to access resources from Tor exit nodes, which can indicate suspicious or malicious activity."
"What is the benefit of using the GuardDuty Malware Protection feature?","Detects malware on EC2 instances and container workloads","Encrypts data at rest","Automates security patching","Provides vulnerability scanning","GuardDuty Malware Protection helps detect malware on EC2 instances and container workloads, improving security posture."
"How does GuardDuty Malware Protection work?","Scans EBS volumes for malware","Monitors network traffic for malicious patterns","Uses machine learning to detect anomalies","Enforces security policies","GuardDuty Malware Protection scans EBS volumes attached to EC2 instances and container workloads for malware, providing enhanced threat detection."
"Which event source is used to trigger GuardDuty Malware Protection scans?","GuardDuty findings","CloudWatch alarms","Manual initiation","AWS Config rules","GuardDuty findings trigger Malware Protection scans when suspicious activity is detected, ensuring timely malware detection and remediation."
"What is the key difference between GuardDuty and Amazon Inspector?","GuardDuty focuses on threat detection, while Inspector focuses on vulnerability management","GuardDuty focuses on compliance reporting, while Inspector focuses on threat detection","GuardDuty is agent-based, while Inspector is agentless","GuardDuty is free, while Inspector is a paid service","GuardDuty primarily focuses on threat detection by analysing log data, while Amazon Inspector focuses on vulnerability management by assessing EC2 instances and container images for vulnerabilities."
"You have multiple AWS accounts and want to centralise GuardDuty management. What's the first step?","Designate one account as the GuardDuty master account","Enable GuardDuty in all accounts","Create a new IAM role for GuardDuty","Configure CloudTrail in all accounts","The first step is to designate one AWS account as the GuardDuty master account, which will be used to manage GuardDuty across all member accounts."
"What should you do with a GuardDuty finding indicating an EC2 instance is communicating with a known malicious IP address?","Isolate the EC2 instance and investigate the root cause","Ignore the finding if the EC2 instance is not critical","Increase the security group rules for the EC2 instance","Reboot the EC2 instance","Isolating the EC2 instance and investigating the root cause is crucial to prevent further damage and identify the source of the compromise."
"Which of the following best describes the relationship between GuardDuty and AWS CloudTrail?","GuardDuty analyzes CloudTrail logs to detect malicious activity","CloudTrail analyzes GuardDuty findings to identify security gaps","GuardDuty replaces CloudTrail for security monitoring","CloudTrail is not required for GuardDuty to function","GuardDuty leverages AWS CloudTrail logs as a key data source to detect malicious activity and unauthorized behavior within your AWS environment."
"Your organisation is concerned about data exfiltration attempts. How can GuardDuty help?","By detecting unusual DNS queries and network traffic patterns","By encrypting data in transit","By preventing unauthorised access to S3 buckets","By monitoring user activity","GuardDuty can help detect data exfiltration attempts by analysing DNS queries and network traffic patterns for suspicious activity."
"What is the benefit of integrating GuardDuty with AWS Security Hub?","Security Hub provides a centralised view of security findings from multiple AWS services","Security Hub automates remediation of GuardDuty findings","Security Hub improves GuardDuty's threat detection capabilities","Security Hub reduces the cost of GuardDuty","Integrating GuardDuty with AWS Security Hub provides a centralised view of security findings from multiple AWS services, allowing for a more comprehensive security posture."
"How can you customise GuardDuty's threat detection capabilities?","By uploading custom threat intelligence feeds and trusted IP lists","By modifying GuardDuty's detection algorithms","By creating custom IAM policies","By disabling specific finding types","Uploading custom threat intelligence feeds and trusted IP lists allows you to tailor GuardDuty's threat detection capabilities to your specific environment and security needs."
"Which of the following is a key consideration when deciding whether to enable GuardDuty in an AWS region?","Data residency requirements and potential threat landscape","Cost of enabling GuardDuty in that region","Performance impact of GuardDuty on resources in that region","Availability of GuardDuty in that region","Data residency requirements and the potential threat landscape are key considerations when deciding whether to enable GuardDuty in an AWS region."
"You want to receive immediate notifications when GuardDuty detects a high-severity finding. Which AWS service should you use to configure this?","Amazon CloudWatch Events (now EventBridge)","AWS Config","AWS Lambda","AWS SNS","Amazon CloudWatch Events (now EventBridge) can be used to trigger actions, such as sending notifications, based on GuardDuty findings, allowing for immediate responses to high-severity threats."
"What is the purpose of the GuardDuty Runtime Monitoring?","To detect threats within container workloads","To detect threats in EC2 instances","To detect threats in Lambda functions","To detect threats in S3 Buckets","GuardDuty Runtime Monitoring is used to detect threats within container workloads at runtime."
"Which of the following actions can you take based on GuardDuty findings?","Automated remediation using AWS Lambda, Manual investigation using Amazon Detective, Suppression of findings to reduce noise","Automated finding resolution, Automatic patching of instances, Automatic key rotation","Automatic vulnerability scanning, Automatic compliance assessment, Automatic network isolation","Enforce multi-factor authentication, Enable AWS Shield, Delete findings","Based on GuardDuty findings, you can perform automated remediation using AWS Lambda, conduct manual investigation using Amazon Detective, or suppress findings to reduce noise."
"Which of the following describes GuardDuty finding suppression?","Hiding or archiving findings that are not relevant or have been addressed","Preventing GuardDuty from generating any findings","Deleting findings after a certain period","Enabling more verbose logging in GuardDuty","GuardDuty finding suppression involves hiding or archiving findings that are not relevant or have been addressed, reducing noise and focusing on actionable threats."
"What is the relationship between GuardDuty and IAM Access Analyzer?","IAM Access Analyzer identifies unintended resource access, while GuardDuty detects malicious activity","GuardDuty automatically configures IAM Access Analyzer, IAM Access Analyzer automatically configures GuardDuty","IAM Access Analyzer replaces GuardDuty, GuardDuty replaces IAM Access Analyzer","IAM Access Analyzer enhances GuardDuty's threat detection capabilities","IAM Access Analyzer identifies unintended resource access by analyzing IAM policies, while GuardDuty detects malicious activity by analyzing logs and network traffic."
"Which AWS data source is primarily used by GuardDuty to detect unusual or unauthorized API calls?","CloudTrail logs","VPC Flow Logs","S3 Access Logs","RDS Audit Logs","GuardDuty primarily uses CloudTrail logs to detect unusual or unauthorized API calls, providing insight into potentially malicious activity within your AWS environment."
"How does Amazon GuardDuty help with compliance requirements related to data security?","By providing continuous threat detection and security monitoring","By automatically generating compliance reports","By enforcing security policies","By encrypting all data at rest","GuardDuty helps with compliance by providing continuous threat detection and security monitoring, aiding organisations in meeting data security requirements."