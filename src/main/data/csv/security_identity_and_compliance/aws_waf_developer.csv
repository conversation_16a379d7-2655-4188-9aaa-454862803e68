"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS WAF?","To protect web applications from common web exploits.","To provide a content delivery network.","To manage user identities.","To monitor network traffic.","AWS WAF is a web application firewall that helps protect your web applications from common web exploits that could affect application availability, compromise security, or consume excessive resources."
"Which AWS service is most commonly used in conjunction with AWS WAF to protect web applications?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","Amazon CloudFront is a content delivery network (CDN) that works seamlessly with AWS WAF to protect web applications by filtering malicious traffic before it reaches the origin."
"What are Web ACLs in AWS WAF?","Containers for rules that define how to inspect web requests.","A list of trusted IP addresses.","A collection of AWS Lambda functions.","A set of predefined security policies.","Web ACLs (Web Access Control Lists) are containers for rules that define how AWS WAF should inspect web requests and determine whether to allow, block, or count them."
"What are the three possible actions that an AWS WAF rule can take on a web request?","Allow, Block, Count","Allow, Redirect, Log","Block, Redirect, Audit","Count, Audit, Allow","AWS WAF rules can be configured to Allow requests to pass through, Block requests from reaching the web application, or Count requests to monitor traffic patterns without taking any action."
"Which type of rule in AWS WAF allows you to block requests based on the country of origin?","Geo Match rule","IP Reputation rule","Rate-Based rule","SQL injection rule","A Geo Match rule allows you to block or allow requests based on the country that the requests originate from, helping to protect against region-specific threats."
"What is a rate-based rule in AWS WAF designed to prevent?","Denial-of-service (DoS) attacks","SQL injection attacks","Cross-site scripting (XSS) attacks","Brute-force attacks","Rate-based rules are designed to protect against denial-of-service (DoS) attacks by limiting the number of requests allowed from a single IP address within a specified time period."
"How does AWS WAF integrate with AWS Shield?","AWS WAF provides application-layer protection, complementing AWS Shield's network-layer protection.","AWS WAF replaces AWS Shield's functionality.","AWS WAF is not related to AWS Shield.","AWS Shield manages the rules for AWS WAF.","AWS WAF provides application-layer protection against attacks like SQL injection and XSS, while AWS Shield provides network-layer protection against DDoS attacks, offering comprehensive protection together."
"What does the 'Inspect' option in an AWS WAF rule do?","Specifies the part of the web request to be examined.","Defines the action to take on a matching request.","Determines the priority of the rule.","Enables logging for the rule.","The 'Inspect' option in an AWS WAF rule specifies the part of the web request (e.g., headers, body, URI) that should be examined to determine if the rule's conditions are met."
"Which of the following is a typical use case for AWS WAF Bot Control?","Blocking malicious bots and crawlers.","Encrypting sensitive data in transit.","Scaling web application resources.","Monitoring web application performance.","AWS WAF Bot Control is used to identify and manage bots, allowing you to block malicious bots and crawlers that can negatively impact your web application."
"What is the purpose of AWS WAF's logging feature?","To provide detailed information about blocked and allowed requests.","To store web application code.","To manage user authentication.","To configure CDN settings.","AWS WAF's logging feature provides detailed information about the web requests that are inspected, blocked, or allowed, which can be used for security analysis and troubleshooting."
"Which AWS WAF feature allows you to test your rules before deploying them in production?","AWS WAF Simulation Mode","AWS WAF Shadow Mode","AWS WAF Dry Run Mode","AWS WAF Testing Console","AWS WAF's simulation mode allows you to test your rules against real-world traffic without actually blocking or allowing requests, helping you fine-tune your rules before deploying them in production."
"Which of the following can be used as a source for AWS WAF's IP address matching?","IP address lists.","AWS CloudTrail logs.","DNS server logs.","EC2 instance metadata.","AWS WAF can use IP address lists, such as those created in AWS Firewall Manager, to match against the source IP address of web requests and block or allow them based on their origin."
"How can you protect an API Gateway API using AWS WAF?","By associating a Web ACL with the API Gateway.","By enabling WAF directly in the API Gateway settings.","By configuring a separate WAF instance for the API.","It is not possible to protect API Gateway with WAF.","You can protect an API Gateway API by associating a Web ACL (Web Access Control List) with the API Gateway, which allows you to apply WAF rules to incoming API requests."
"What is the benefit of using managed rule groups in AWS WAF?","They provide pre-configured protection against common web threats.","They automatically scale web application resources.","They simplify user authentication.","They optimise database performance.","Managed rule groups in AWS WAF provide pre-configured protection against common web threats, such as OWASP Top 10 vulnerabilities, reducing the effort required to configure WAF rules from scratch."
"Which of the following AWS services can be used to analyse AWS WAF logs?","Amazon Athena","Amazon CloudWatch","Amazon Inspector","Amazon Config","Amazon Athena can be used to analyse AWS WAF logs stored in Amazon S3, allowing you to query and gain insights into web traffic patterns and security threats."
"Which AWS WAF component defines the criteria for inspecting web requests and the actions to take based on those criteria?","Rules","Conditions","Web ACLs","IP Sets","Rules in AWS WAF define the criteria for inspecting web requests, such as matching specific patterns in the URI or headers, and the actions to take based on those criteria, such as allowing or blocking the request."
"What is the purpose of the AWS WAF rule priority?","To determine the order in which rules are evaluated.","To assign different levels of protection to different rules.","To specify which rules should be logged.","To define which rules should be tested in simulation mode.","AWS WAF rule priority determines the order in which rules are evaluated, allowing you to control the order in which rules are applied to web requests and ensure that more specific rules are evaluated before more general ones."
"Which AWS WAF feature helps to mitigate brute force attacks?","Rate-based rules","Geo Match rules","IP Reputation rules","SQL injection rules","Rate-based rules can be configured to limit the number of requests allowed from a single IP address within a specified time period, helping to mitigate brute force attacks."
"How can you update AWS WAF rules in response to a new threat?","By modifying the Web ACL and redeploying it.","By updating the AWS WAF service directly.","By restarting the EC2 instances behind the load balancer.","By patching the web application code.","You can update AWS WAF rules in response to a new threat by modifying the Web ACL (Web Access Control List) and redeploying it, allowing you to quickly adapt your security posture to emerging threats."
"Which of the following is a valid scope for an AWS WAF Web ACL?","CloudFront distribution, Regional Application Load Balancer, or API Gateway.","EC2 instance, S3 bucket, or DynamoDB table.","IAM user, VPC, or Route 53 hosted zone.","Lambda function, SNS topic, or SQS queue.","An AWS WAF Web ACL can be scoped to protect resources such as a CloudFront distribution, a regional Application Load Balancer, or an API Gateway, allowing you to apply WAF rules to different types of web applications."
"What type of attack does the AWS WAF SQL injection rule group protect against?","Attacks that attempt to insert malicious SQL code into database queries.","Attacks that flood a web application with traffic.","Attacks that steal user credentials.","Attacks that exploit cross-site scripting vulnerabilities.","The AWS WAF SQL injection rule group protects against attacks that attempt to insert malicious SQL code into database queries, potentially allowing attackers to bypass security measures and gain unauthorised access to data."
"What does AWS WAF's 'visibility configuration' allow you to specify?","The metrics and logs to be sent to CloudWatch.","The regions where the WAF rules are applied.","The users who have access to the WAF console.","The billing preferences for the WAF service.","AWS WAF's 'visibility configuration' allows you to specify the metrics and logs that are sent to CloudWatch, providing visibility into web traffic patterns and security threats."
"Which AWS WAF feature allows you to create custom error pages for blocked requests?","Custom Response","Custom Error Pages","Block Response","HTTP Redirection","AWS WAF's 'Custom Response' feature allows you to create custom error pages for blocked requests, providing a more user-friendly experience for legitimate users who are accidentally blocked."
"What is the purpose of AWS WAF's 'Size Restriction' rule?","To block requests with excessively large bodies or headers.","To limit the number of requests per IP address.","To block requests from specific countries.","To protect against SQL injection attacks.","The 'Size Restriction' rule in AWS WAF is used to block requests with excessively large bodies or headers, helping to prevent denial-of-service attacks and other resource-intensive exploits."
"How can you automatically update AWS WAF rules in response to new vulnerabilities?","By using AWS Marketplace subscriptions for managed rule groups.","By enabling automatic updates in the AWS WAF console.","By creating custom AWS Lambda functions to update the rules.","By subscribing to AWS Security Hub notifications.","You can automatically update AWS WAF rules in response to new vulnerabilities by using AWS Marketplace subscriptions for managed rule groups, which are regularly updated by security experts."
"What is the difference between an 'IP Set' and a 'Geo Match' rule in AWS WAF?","An IP Set matches specific IP addresses or CIDR blocks, while a Geo Match rule matches requests based on the country of origin.","An IP Set matches requests based on the country of origin, while a Geo Match rule matches specific IP addresses or CIDR blocks.","An IP Set is used for rate-based rules, while a Geo Match rule is used for SQL injection protection.","An IP Set is used for logging, while a Geo Match rule is used for blocking requests.","An IP Set in AWS WAF is used to match specific IP addresses or CIDR blocks, allowing you to block or allow requests from known malicious or trusted sources, while a Geo Match rule matches requests based on the country of origin."
"Which AWS service can be used to automatically deploy and configure AWS WAF rules across multiple accounts and regions?","AWS Firewall Manager","AWS Config","AWS CloudFormation","AWS Systems Manager","AWS Firewall Manager can be used to automatically deploy and configure AWS WAF rules across multiple accounts and regions, providing centralised management and enforcement of security policies."
"Which AWS WAF rule type allows you to inspect the HTTP request body for malicious content?","Body match rule","Header match rule","URI match rule","Query string match rule","A 'Body match' rule in AWS WAF allows you to inspect the HTTP request body for malicious content, such as SQL injection or cross-site scripting payloads."
"What is the purpose of the 'CAPTCHA' action in AWS WAF?","To challenge users with a CAPTCHA to verify they are not bots.","To encrypt the web traffic.","To redirect users to a different page.","To log the request details.","The 'CAPTCHA' action in AWS WAF challenges users with a CAPTCHA (Completely Automated Public Turing test to tell Computers and Humans Apart) to verify they are not bots, helping to prevent automated attacks."
"Which of the following is a benefit of using AWS WAF with Amazon CloudFront?","WAF rules are applied closer to the user, reducing latency.","WAF rules are applied only at the origin server, simplifying management.","WAF rules are applied only to static content, improving performance.","WAF rules are applied only to dynamic content, enhancing security.","Using AWS WAF with Amazon CloudFront allows WAF rules to be applied closer to the user, reducing latency and improving the overall performance of the web application."
"What is the maximum number of rules that can be added to a single AWS WAF Web ACL?","100","50","10","Unlimited","An AWS WAF Web ACL has a limit of 100 rules. It is important to structure your rules efficiently to stay within this limit."
"What does the 'Associativity' setting in an AWS WAF rule group control?","Whether the rule group's rules are evaluated before or after other rules in the Web ACL.","Whether the rule group is enabled or disabled.","Whether the rule group's rules are logged or not.","Whether the rule group's rules are applied to all regions or only specific ones.","The 'Associativity' setting in an AWS WAF rule group controls whether the rule group's rules are evaluated before or after other rules in the Web ACL, allowing you to control the order of evaluation and ensure that specific rules are evaluated in the desired sequence."
"Which AWS WAF feature allows you to track the number of web requests matching a specific criteria over a period of time?","Metrics","Logging","Sampling","Monitoring","AWS WAF's metrics feature allows you to track the number of web requests matching a specific criteria over a period of time, providing insights into traffic patterns and security threats."
"What type of attack does the AWS WAF 'Cross-Site Scripting (XSS)' rule group protect against?","Attacks that inject malicious scripts into web pages viewed by other users.","Attacks that attempt to guess user passwords.","Attacks that exploit SQL injection vulnerabilities.","Attacks that flood a web application with traffic.","The AWS WAF 'Cross-Site Scripting (XSS)' rule group protects against attacks that inject malicious scripts into web pages viewed by other users, potentially allowing attackers to steal user credentials or perform other malicious actions."
"When should you consider using AWS WAF custom rules instead of managed rule groups?","When you have specific security requirements that are not covered by managed rule groups.","When you want to reduce the cost of using AWS WAF.","When you want to simplify the configuration of AWS WAF.","When you want to improve the performance of AWS WAF.","You should consider using AWS WAF custom rules instead of managed rule groups when you have specific security requirements that are not covered by the pre-configured protection provided by managed rule groups."
"Which AWS service provides threat intelligence feeds that can be used with AWS WAF?","AWS Marketplace","AWS Security Hub","Amazon GuardDuty","AWS CloudTrail","AWS Marketplace provides threat intelligence feeds that can be used with AWS WAF, allowing you to integrate threat intelligence data into your WAF rules and protect against known malicious actors and patterns."
"What is the relationship between AWS WAF rules and conditions?","Rules define the actions to take, while conditions define the criteria for matching web requests.","Rules define the criteria for matching web requests, while conditions define the actions to take.","Rules and conditions are interchangeable terms.","Rules are used for logging, while conditions are used for blocking requests.","Rules in AWS WAF define the actions to take, such as allowing or blocking a request, while conditions define the criteria for matching web requests, such as matching specific patterns in the URI or headers."
"Which of the following is a valid way to deploy AWS WAF rules?","Using the AWS Management Console, AWS CLI, or AWS CloudFormation.","Using only the AWS Management Console.","Using only the AWS CLI.","Using only AWS CloudFormation.","AWS WAF rules can be deployed and managed using the AWS Management Console, AWS CLI, or AWS CloudFormation, providing flexibility in how you manage your WAF configuration."
"What is the default action for an AWS WAF Web ACL?","Allow all requests that do not match any rules.","Block all requests that do not match any rules.","Log all requests that do not match any rules.","Count all requests that do not match any rules.","The default action for an AWS WAF Web ACL is to 'Allow' all requests that do not match any rules, ensuring that legitimate traffic is not blocked unless explicitly defined in a rule."
"What is a good strategy when initially deploying AWS WAF rules?","Start in count mode to monitor traffic before blocking.","Immediately block all suspicious traffic.","Start with a very strict set of rules and gradually relax them.","Deploy all rules simultaneously without testing.","A good strategy when initially deploying AWS WAF rules is to start in count mode to monitor traffic patterns and identify any false positives before blocking traffic, allowing you to fine-tune your rules and minimise disruptions."
"You have an AWS WAF rule blocking requests based on IP address. How can you temporarily allow a specific request without modifying the rule?","Add the IP address to an exclusion list in the Web ACL.","Create a new rule with a higher priority to allow the IP address.","Modify the existing rule to exclude the IP address.","It is not possible to temporarily allow a specific request without modifying the rule.","You can temporarily allow a specific request without modifying the existing rule by creating a new rule with a higher priority that allows the IP address, ensuring that it is evaluated before the blocking rule."
"Which of the following factors affects the cost of using AWS WAF?","The number of rules, the number of requests, and the AWS region.","The number of users accessing the web application.","The amount of data stored in S3.","The number of EC2 instances running.","The cost of using AWS WAF is affected by the number of rules you have, the number of requests that are processed by WAF, and the AWS region where WAF is deployed."
"When using AWS WAF to protect a CloudFront distribution, where are the WAF rules applied?","At the CloudFront edge locations.","At the origin server.","At the regional CloudFront cache.","At the user's browser.","When using AWS WAF to protect a CloudFront distribution, the WAF rules are applied at the CloudFront edge locations, providing protection closer to the user and reducing latency."
"How does AWS WAF help with compliance requirements such as PCI DSS?","By providing protection against common web application vulnerabilities.","By automatically encrypting data at rest.","By managing user access control.","By providing network-level protection.","AWS WAF helps with compliance requirements such as PCI DSS by providing protection against common web application vulnerabilities, such as SQL injection and cross-site scripting, which are often targeted by attackers."
"You are using AWS WAF to protect your web application. You notice a sudden spike in blocked requests. What should you do first?","Analyse the WAF logs to identify the source and nature of the blocked requests.","Immediately increase the capacity of your web application.","Disable all WAF rules to allow traffic to flow.","Contact AWS Support to investigate the issue.","The first step should be to analyse the AWS WAF logs to identify the source and nature of the blocked requests, allowing you to determine if the spike is due to a legitimate attack or a configuration issue."
"Which of the following is a common use case for the AWS WAF 'String Match' condition?","Matching specific strings in the URI, headers, or body of a web request.","Matching IP addresses or CIDR blocks.","Matching countries or regions.","Matching HTTP methods.","The AWS WAF 'String Match' condition is used to match specific strings in the URI, headers, or body of a web request, allowing you to identify and block requests that contain specific patterns or keywords."
"Which of the following is an advantage of using AWS WAF over traditional network firewalls?","AWS WAF provides application-layer protection tailored to web applications.","AWS WAF provides network-layer protection for all types of traffic.","AWS WAF automatically scales web application resources.","AWS WAF manages user identities and access control.","AWS WAF provides application-layer protection that is specifically tailored to web applications, allowing it to identify and block attacks that traditional network firewalls might miss."
"What does AWS WAF 'Challenge' action do?","It presents a CAPTCHA or other challenge to verify the user's legitimacy.","It permanently blocks the user's IP address.","It redirects the user to a different page.","It logs the request details.","The AWS WAF 'Challenge' action presents a CAPTCHA or other challenge to verify the user's legitimacy before allowing the request to proceed, helping to prevent bot-driven attacks."
"What is the primary purpose of AWS WAF?","Protect web applications from common web exploits.","Encrypt data at rest in S3.","Monitor network traffic for suspicious activity.","Manage user access to AWS resources.","AWS WAF's main function is to safeguard web applications from typical online vulnerabilities and attacks."
"In AWS WAF, what is a 'Web ACL'?","A container for rules that define how to inspect web requests.","A security group for your web application.","A type of firewall rule that allows or denies traffic based on IP address.","A tool for monitoring the performance of your web application.","A Web ACL in AWS WAF is used to define a set of rules that determine how web requests are inspected, which allows you to control access to your web application."
"What type of rule action in AWS WAF blocks a request?","Block","Allow","Count","Log","The 'Block' action in AWS WAF prevents the web request from reaching your application."
"Which AWS service is most commonly used with AWS WAF to protect web applications?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","AWS WAF is frequently deployed with Amazon CloudFront to protect content delivered through the CDN."
"What AWS WAF feature allows you to test rules before deploying them into production?","Count Mode","Block Mode","Allow Mode","Bypass Mode","Count mode lets you see how many requests would be blocked by the rule, without actually blocking them."
"What is the purpose of a 'Rate-Based Rule' in AWS WAF?","To block IP addresses that send excessive requests within a short period.","To allow requests only from specific geographical locations.","To filter requests based on the HTTP method.","To redirect traffic to a different endpoint.","Rate-based rules in AWS WAF are designed to identify and block IP addresses that are flooding your application with requests, preventing denial-of-service attacks."
"Which of the following is a common use case for AWS WAF's SQL injection protection?","Preventing malicious SQL code from being injected into database queries.","Encrypting SQL database connections.","Auditing SQL database activity.","Optimising SQL database performance.","AWS WAF's SQL injection protection is specifically designed to prevent attackers from injecting malicious SQL code into database queries, which could lead to data breaches or other security incidents."
"Which of the following is a common use case for AWS WAF's Cross-Site Scripting (XSS) protection?","Preventing malicious scripts from being injected into web pages.","Encrypting data in transit between the web server and the client.","Authenticating users with multi-factor authentication.","Monitoring web server performance.","XSS protection helps prevent attackers from injecting malicious scripts into web pages viewed by other users."
"Which AWS service can be used to get detailed logs of requests inspected by AWS WAF?","Amazon CloudWatch Logs","Amazon S3","AWS CloudTrail","Amazon VPC Flow Logs","Amazon CloudWatch Logs is the correct service to get detailed logs from AWS WAF."
"What is the purpose of 'AWS Managed Rules' in AWS WAF?","To provide pre-configured rulesets for common web application vulnerabilities.","To create custom rules based on your own specifications.","To automatically update AWS WAF with the latest security patches.","To provide real-time monitoring of web application traffic.","AWS Managed Rules are pre-configured rulesets offered by AWS or AWS Marketplace vendors to protect against common web application vulnerabilities."
"When you create a custom rule in AWS WAF, what is a 'Condition'?","A criteria that a web request must match for the rule to apply.","An action that AWS WAF takes when a web request matches the rule.","A log of all web requests processed by AWS WAF.","A schedule for when the rule should be active.","In AWS WAF, a 'Condition' is a specific criteria that a web request must meet for a rule to be triggered."
"What is the purpose of the AWS WAF 'Size restriction' option within a rule condition?","To filter requests based on the size of a specific part of the request (e.g., query string).","To limit the total number of requests allowed per second.","To compress large requests to reduce bandwidth consumption.","To encrypt requests that exceed a certain size.","The 'Size restriction' option in AWS WAF allows you to filter web requests based on the size of a specific part of the request, such as the query string or the request body."
"Which AWS WAF component lets you define reusable sets of rules?","Rule group","Web ACL","IP set","Geo match set","Rule groups in AWS WAF allow you to define and reuse sets of rules across multiple Web ACLs, simplifying rule management."
"What does AWS WAF inspect in a web request to determine if it matches a defined rule?","HTTP headers, URI, and body","Operating system","CPU utilisation","Memory usage","AWS WAF inspects various components of a web request, including the HTTP headers, URI, and body, to determine if it matches a defined rule."
"What is the difference between AWS WAF Classic and AWS WAF?","AWS WAF is the latest version and supports regional and global deployments.","AWS WAF Classic is the latest version.","AWS WAF is only for EC2 instances.","AWS WAF Classic supports more AWS services.","AWS WAF is the latest version and offers both regional and global deployments, while AWS WAF Classic is an older version with limited functionality and features."
"Which AWS service can you use to manage AWS WAF rules programmatically?","AWS SDK","AWS CloudTrail","Amazon Inspector","Amazon Macie","The AWS SDK is the proper way to manage AWS WAF programmatically."
"Which of the following is NOT a valid scope for AWS WAF?","AWS Lambda Function URL","Amazon EC2 instance","Amazon CloudFront distribution","Application Load Balancer","AWS WAF is not directly associated with Amazon EC2 instances."
"What is the purpose of the 'IP reputation list' feature in AWS WAF?","To block requests from IP addresses known to be associated with malicious activity.","To allow requests only from specific IP addresses.","To track the geographic location of IP addresses.","To encrypt traffic from specific IP addresses.","An IP reputation list in AWS WAF contains IP addresses known to be associated with malicious activity, which can be used to block requests from these addresses."
"Which of the following is a potential benefit of using AWS WAF?","Improved web application security.","Reduced server costs.","Increased website traffic.","Faster content delivery.","AWS WAF's primary benefit is to improve the security of web applications by protecting them from common web exploits and attacks."
"What is the primary purpose of the AWS WAF Bot Control feature?","To identify and manage bots that access your web application.","To block all automated traffic to your web application.","To improve the performance of your web application.","To encrypt bot traffic to protect user privacy.","AWS WAF Bot Control is designed to identify and manage bots that access your web application, allowing you to control which bots can access your resources."
"In AWS WAF, what is the function of the 'Challenge' action?","To present a CAPTCHA or other challenge to the user.","To automatically block requests from suspicious IP addresses.","To redirect the user to a different website.","To log all web requests for auditing purposes.","The 'Challenge' action in AWS WAF presents a CAPTCHA or other challenge to the user, helping to distinguish between legitimate users and automated bots or malicious actors."
"Which of the following can you use to automatically deploy and manage AWS WAF rules across multiple AWS accounts?","AWS Firewall Manager","AWS Systems Manager","AWS Config","AWS CloudFormation","AWS Firewall Manager allows you to centrally configure and manage AWS WAF rules across multiple AWS accounts."
"Which AWS service is commonly used to deliver static content alongside AWS WAF protecting your application?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","Amazon CloudFront is commonly used as a Content Delivery Network (CDN) to deliver static content, and it often works together with AWS WAF to protect the application from attacks."
"What happens to a request if it doesn't match any of the rules defined in a Web ACL?","It's handled based on the default action configured for the Web ACL.","It's automatically blocked.","It's automatically allowed.","It's sent to an administrator for manual review.","If a request doesn't match any defined rule, the Web ACL's default action (either allow or block) determines its fate."
"Which of the following AWS WAF logs provides the most detailed information about each request?","Full request logging to CloudWatch Logs","Summarised metrics in CloudWatch Metrics","Sampled logs in S3","Real-time alerts in SNS","Full request logging to CloudWatch Logs captures the most comprehensive details about each request processed by AWS WAF."
"How can you protect your API Gateway endpoints with AWS WAF?","By associating a Web ACL with the API Gateway.","By configuring IAM policies.","By using VPC endpoints.","By enabling API Gateway caching.","You can protect your API Gateway endpoints by associating a Web ACL with the API Gateway, allowing AWS WAF to filter and inspect traffic."
"When configuring a rule in AWS WAF, what is the purpose of the 'Priority' setting?","To determine the order in which rules are evaluated.","To specify the importance of the rule.","To set the rate at which the rule is applied.","To define the time period for which the rule is active.","The 'Priority' setting in AWS WAF determines the order in which rules are evaluated, with lower numbers having higher priority."
"Which AWS WAF rule type allows you to match requests based on specific strings within the request body?","String match condition","Regular expression match condition","Size constraint condition","Geo match condition","A string match condition allows you to match requests based on specific strings found within the request body or other parts of the request."
"What is the primary purpose of AWS Shield?","To protect against DDoS attacks.","To protect against SQL injection attacks.","To encrypt data in transit.","To manage user access to AWS resources.","AWS Shield is specifically designed to protect against Distributed Denial of Service (DDoS) attacks."
"You need to protect a web application hosted on EC2 instances behind an Application Load Balancer (ALB). How do you configure AWS WAF in this scenario?","Associate the WAF Web ACL with the ALB.","Install the WAF agent on the EC2 instances.","Configure the WAF rules directly in the EC2 security groups.","Use AWS Network Firewall instead of AWS WAF.","The correct way to configure AWS WAF for EC2 instances behind an ALB is to associate the Web ACL with the ALB."
"What type of rule action in AWS WAF allows the request but also logs it for analysis?","Count","Allow","Block","Challenge","The 'Count' action allows the request but also logs it, providing data for analysis without blocking traffic."
"Which AWS WAF feature lets you block requests based on the originating country?","Geo match condition","IP address match condition","Size constraint condition","Rate-based rule","Geo match condition allows you to block requests based on the country of origin."
"How does AWS WAF integrate with AWS Firewall Manager?","Firewall Manager provides centralised management of WAF rules across multiple accounts.","WAF automatically inherits rules from Firewall Manager.","Firewall Manager is a replacement for WAF.","WAF and Firewall Manager do not integrate.","AWS Firewall Manager enables centralised management of WAF rules across multiple AWS accounts, simplifying security administration."
"What is the purpose of the AWS WAF 'Regular expression match condition'?","To match requests based on a pattern using regular expressions.","To match requests based on exact string matches.","To match requests based on their size.","To match requests based on their geographic origin.","A regular expression match condition allows you to match requests based on a pattern defined using regular expressions, providing more flexible matching capabilities."
"You want to implement a custom rule in AWS WAF to block requests with a specific user-agent string. Which component of the rule do you configure to achieve this?","String match condition","Rate-based rule","Geo match condition","Size constraint condition","A string match condition is used to match requests based on specific strings, such as the user-agent header."
"What is a characteristic of the 'AWS WAF Bot Control' feature?","Protects against common bot activities","Encrypts bot traffic","Redirects bot traffic","Accelerates bot traffic","The 'AWS WAF Bot Control' feature helps protect against common bot activities like content scraping, credential stuffing, and vulnerability scanning."
"What is the key difference between AWS WAF and AWS Shield Advanced?","AWS Shield Advanced offers 24/7 DDoS response support and more advanced mitigation techniques.","AWS WAF offers 24/7 DDoS response support.","AWS WAF is for network traffic and AWS Shield Advanced is for web traffic.","AWS WAF and AWS Shield Advanced are the same.","AWS Shield Advanced offers 24/7 DDoS response support from AWS experts and more advanced mitigation techniques compared to standard AWS Shield and AWS WAF."
"Which of the following is a valid use case for AWS WAF Rate-Based Rules?","Mitigating HTTP floods","Blocking SQL injection attempts","Preventing Cross-Site Scripting (XSS) attacks","Filtering requests based on geographic location","Rate-Based Rules are designed to mitigate HTTP floods by limiting the number of requests from a specific IP address within a defined time period."
"When creating an AWS WAF rule, what does the 'Action' setting determine?","What AWS WAF does with requests that match the rule's conditions.","The order in which the rule is evaluated relative to other rules.","The CloudWatch metric used to monitor the rule's performance.","The frequency at which the rule is automatically updated.","The 'Action' setting in AWS WAF specifies what action WAF should take when a request matches the rule's conditions, such as allow, block, or count."
"What is the best practice for testing new AWS WAF rules before deploying them to production?","Use the 'Count' action to monitor the rule's behavior without blocking traffic.","Deploy the rule directly to production and monitor the impact.","Use AWS CloudTrail to simulate web traffic.","Disable all existing rules before enabling the new rule.","Using the 'Count' action allows you to observe the rule's behavior without disrupting production traffic, providing valuable insights before fully deploying the rule."
"Which AWS WAF feature helps protect against automated bots scraping your website's content?","AWS WAF Bot Control","AWS WAF Rate-Based Rules","AWS WAF Geo match rules","AWS WAF IP Reputation Lists","AWS WAF Bot Control is specifically designed to identify and manage bots, helping to protect against content scraping and other malicious bot activities."
"You have configured AWS WAF to protect your application. After enabling the WAF rules, you notice legitimate traffic is being blocked. What should you do first?","Review the WAF logs to identify the rules causing the false positives and adjust the rule conditions.","Disable all WAF rules and re-enable them one by one.","Increase the sensitivity of the WAF rules.","Contact AWS support to investigate the issue.","Reviewing the WAF logs will help you identify which rules are causing the false positives, allowing you to adjust the rule conditions and reduce the number of legitimate requests being blocked."
"In AWS WAF, what is the purpose of a Web ACL (Access Control List)?","To define a set of rules that inspect web requests","To manage AWS Identity and Access Management (IAM) users","To configure load balancing for EC2 instances","To monitor network traffic using VPC Flow Logs","A Web ACL in AWS WAF is a container for rules that inspect web requests. It allows you to define how WAF should handle different requests based on the rules configured."
"Which AWS service is typically used in conjunction with AWS WAF to protect web applications?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon DynamoDB","Amazon CloudFront is often used with AWS WAF to protect content delivered through CloudFront's content delivery network (CDN)."
"What type of rule action in AWS WAF allows a request to proceed to the backend web application?","Allow","Block","Count","Challenge","The 'Allow' action explicitly permits the web request to proceed to the backend web application without further inspection."
"What is the purpose of the AWS WAF 'Count' action when configuring a rule?","To count the number of requests that match the rule without blocking them","To block all requests matching the rule","To allow requests matching the rule after a CAPTCHA","To redirect requests matching the rule to a different URL","The 'Count' action is used to monitor and track the number of web requests that match a specific rule without taking any blocking action. This is useful for testing and monitoring rules before deploying them in 'Block' mode."
"Which AWS WAF component allows you to define a set of IP addresses that are allowed or blocked from accessing your web application?","IP Sets","Rule Groups","Web ACL","Managed Rules","IP Sets in AWS WAF are used to specify a list of IP addresses or CIDR blocks that you want to allow or block access from."
"What type of protection does AWS WAF provide against SQL injection attacks?","By inspecting the body and headers of web requests for malicious SQL code","By encrypting the database connection","By blocking all requests from unknown IP addresses","By limiting the size of web requests","AWS WAF can inspect the content of web requests, including the body and headers, for patterns that indicate SQL injection attempts."
"Which AWS WAF feature allows you to deploy pre-configured rulesets from AWS or third-party vendors?","Managed Rules","Custom Rules","Rate-Based Rules","Geo Match Rules","Managed Rules provide pre-configured rulesets from AWS or third-party vendors, making it easier to deploy common security protections."
"What is the purpose of Rate-Based Rules in AWS WAF?","To limit the number of requests from a single IP address within a specified time period","To block requests from specific countries","To allow requests only during certain hours of the day","To block requests based on the size of the request body","Rate-Based Rules in AWS WAF allow you to limit the number of requests from a single IP address within a defined time period, helping to mitigate denial-of-service (DoS) attacks."
"Which AWS WAF feature enables you to block requests originating from specific geographic locations?","Geo Match Rules","IP Sets","Rate-Based Rules","Regex Match Sets","Geo Match Rules allow you to filter web requests based on the country that the requests originate from."
"How does AWS WAF protect against Cross-Site Scripting (XSS) attacks?","By inspecting the body and headers of web requests for malicious JavaScript code","By automatically encoding all output from the web application","By requiring users to authenticate with multi-factor authentication","By using a Content Security Policy (CSP)","AWS WAF can inspect the content of web requests, including the body and headers, for patterns that indicate XSS attempts."
"When using AWS WAF with Amazon CloudFront, where are the WAF rules evaluated?","At the CloudFront edge locations","At the origin server","Within the user's browser","At the AWS WAF regional endpoint","When integrated with CloudFront, AWS WAF rules are evaluated at the CloudFront edge locations, providing protection close to the users."
"Which of the following best describes an AWS WAF Rule Group?","A collection of rules that can be managed as a single unit","A group of IAM users with specific permissions","A set of EC2 instances behind a load balancer","A collection of S3 buckets with a common prefix","An AWS WAF Rule Group is a reusable collection of rules, allowing you to manage and deploy rules more efficiently."
"What is the maximum number of rules you can add to an AWS WAF Web ACL?","100","10","Unlimited","50","AWS WAF has limits and they can evolve. As of today, the maximum number of rules that you can add to an AWS WAF Web ACL is 100."
"What is the purpose of the AWS WAF Bot Control feature?","To identify and manage common web bots","To encrypt traffic to the web application","To manage user authentication","To monitor web application performance","The AWS WAF Bot Control feature identifies and manages common web bots, allowing you to control their access to your application."
"Which of the following is NOT a valid match condition for an AWS WAF rule?","HTTP method","Originating IP address","Request size","Database type","AWS WAF doesn't directly inspect the type of database. It focuses on HTTP characteristics."
"What is the default action in AWS WAF if no rules match a web request?","Allow","Block","Challenge","Redirect","If no rules match, the default action of the Web ACL determines whether the request is allowed or blocked."
"What type of attack does AWS WAF Rate limiting primarily help protect against?","DDoS attacks","SQL injection attacks","Cross-site scripting attacks","Brute force attacks","Rate limiting is designed to mitigate DDoS attacks by limiting the number of requests from a single IP address."
"Which AWS service is commonly used to store AWS WAF logs?","Amazon S3","Amazon CloudWatch Logs","Amazon EC2","Amazon RDS","AWS WAF logs can be configured to be stored in Amazon S3 for analysis and auditing."
"When would you use AWS WAF Regex Match Sets?","When you need to match specific patterns in the request","When you want to block requests from specific countries","When you want to limit requests from a single IP address","When you need to match specific HTTP headers","Regex Match Sets are used to define regular expressions that can be used to match specific patterns within the request (e.g., in the URL or request body)."
"What is the purpose of CAPTCHA in AWS WAF's Challenge action?","To verify that the request is from a human user","To encrypt the request","To route the request to a different server","To reduce the load on the web server","The CAPTCHA (Completely Automated Public Turing test to tell Computers and Humans Apart) in AWS WAF's Challenge action is to distinguish between human users and bots."
"Which AWS service can you integrate with AWS WAF for centralised security management?","AWS Shield","AWS CloudTrail","AWS Config","AWS Identity and Access Management (IAM)","AWS Shield integrates with AWS WAF, providing enhanced protection against DDoS attacks."
"What is the primary difference between AWS WAF Classic and AWS WAF?","AWS WAF is the newer version with more features and broader integration","AWS WAF Classic only supports CloudFront, while AWS WAF supports other services","AWS WAF Classic is free, while AWS WAF has a cost","AWS WAF Classic is more secure than AWS WAF","AWS WAF is the newer version of the service with a broader feature set and deeper integration with other AWS services, offering more flexibility and advanced security capabilities compared to AWS WAF Classic."
"In AWS WAF, what is a 'Scope' referring to?","The AWS resources that the WAF rules apply to","The time period for which a WAF rule is active","The number of rules in a WAF ACL","The geographic region where the WAF is deployed","In AWS WAF, 'Scope' refers to the AWS resources that the WAF rules are intended to protect, such as CloudFront distributions, Application Load Balancers, or API Gateways."
"Which of the following is NOT a valid AWS WAF rule action?","Monitor","Block","Allow","Count","'Monitor' is not a valid AWS WAF rule action. The valid actions are 'Allow', 'Block', and 'Count'."
"You want to block requests containing a specific string in the URI. Which WAF component should you use?","String Match Condition","IP Set","Rate-Based Rule","Geo Match Rule","A String Match Condition allows you to define a string that AWS WAF will look for in the URI."
"When using AWS WAF with an Application Load Balancer (ALB), where does the WAF evaluation take place?","At the ALB before the request reaches the target instances","At the target instances after the ALB","In the user's browser","At the AWS WAF regional endpoint","When integrated with an ALB, AWS WAF rules are evaluated at the ALB before the request is routed to the target instances."
"What is the benefit of using AWS WAF with AWS Shield Advanced?","Enhanced DDoS protection and customised mitigation strategies","Automatic patching of web application vulnerabilities","Simplified IAM role management","Cost reduction for CloudFront data transfer","AWS Shield Advanced provides enhanced DDoS protection and allows for customised mitigation strategies when used in conjunction with AWS WAF."
"Which AWS WAF component would you use to create a rule that blocks requests with a specific User-Agent header?","Header Match Condition","Regex Match Set","Rate-Based Rule","Geo Match Rule","A Header Match Condition allows you to define a specific User-Agent header that AWS WAF will match against."
"What is the impact of enabling AWS WAF logging?","Increased cost due to storage of logs","Improved web application performance","Reduced security risk","Automatic backup of web application data","Enabling AWS WAF logging increases cost due to the storage and analysis of logs, but it is essential for auditing and identifying attack patterns."
"You need to protect your API Gateway endpoints from malicious requests. Can AWS WAF be used for this purpose?","Yes, AWS WAF can protect API Gateway endpoints","No, AWS WAF only protects CloudFront distributions","No, you must use AWS API Gateway's built-in security features","Yes, but only with a custom integration using Lambda functions","Yes, AWS WAF can be directly associated with API Gateway endpoints to protect them from common web exploits and malicious requests."
"What is the significance of the 'Priority' setting when defining rules in AWS WAF?","It determines the order in which rules are evaluated","It sets the level of security enforced by the rule","It defines the cost associated with the rule","It specifies the number of requests the rule applies to","The 'Priority' setting determines the order in which rules are evaluated within a Web ACL. Rules with lower priority numbers are evaluated first."
"What is the purpose of the AWS WAF Fraud Control account takeover prevention (ATP) feature?","To detect and prevent attempts to take over user accounts","To identify and block fraudulent financial transactions","To prevent unauthorised access to AWS accounts","To detect and block bots from scraping website content","The AWS WAF Fraud Control account takeover prevention (ATP) feature helps to detect and prevent attempts to take over user accounts by analysing web requests and identifying suspicious login patterns."
"Which of the following is a valid way to update an IP Set in AWS WAF?","Using the AWS WAF API or AWS CLI","Manually editing the AWS WAF configuration file","Importing the IP addresses from an S3 bucket","Modifying the application code to update the IP Set","IP Sets in AWS WAF can be updated programmatically using the AWS WAF API or AWS CLI, allowing for automated management of IP address lists."
"What is the primary use case for AWS WAF Bot Control's 'Tarpitting' action?","To slow down bots to increase the cost of attack","To redirect bots to a honeypot server","To automatically block all bots","To analyse bot behaviour","The 'Tarpitting' action in AWS WAF Bot Control is used to slow down bots, making it more costly and time-consuming for them to carry out attacks."
"You are using AWS WAF with CloudFront and want to block requests coming from known bad actors. What is the most efficient way to achieve this?","Subscribe to a threat intelligence feed and use Managed Rules","Create a custom rule to block each IP address individually","Use CloudFront geo-restriction features","Write a Lambda function to filter requests","Subscribing to a threat intelligence feed and using Managed Rules provides an efficient way to block requests from known bad actors, as the rules are automatically updated with the latest threat information."
"When would you use a regular expression (regex) in an AWS WAF rule?","To match patterns in request headers, bodies, or URLs","To block requests based on country of origin","To limit the number of requests from a single IP address","To allow requests only during specific hours of the day","Regular expressions are used to match specific patterns within request headers, bodies, or URLs, providing flexible and powerful matching capabilities."
"What is the relationship between AWS WAF and AWS Inspector?","AWS WAF protects web applications, while AWS Inspector assesses EC2 instance security","AWS WAF and AWS Inspector are the same service","AWS WAF is a subset of AWS Inspector","AWS Inspector protects web applications, while AWS WAF assesses EC2 instance security","AWS WAF protects web applications by filtering malicious requests, while AWS Inspector assesses the security of EC2 instances."
"Which of the following AWS services can you use to analyse AWS WAF logs stored in S3?","Amazon Athena","Amazon CloudWatch Logs Insights","Amazon GuardDuty","Amazon Macie","Amazon Athena allows you to query and analyse AWS WAF logs stored in S3 using standard SQL queries."
"You have deployed AWS WAF and are receiving too many false positives. What should you do first?","Review the WAF rules and adjust the match conditions","Increase the sensitivity of the WAF rules","Disable all WAF rules and enable them one by one","Contact AWS support","Reviewing the WAF rules and adjusting the match conditions is the first step to address false positives, ensuring that the rules are not too broad or aggressive."
"Which AWS WAF rule type allows you to specify the maximum size of the request body?","Size Restriction Rule","Rate-Based Rule","Geo Match Rule","String Match Rule","Size Restriction Rules in AWS WAF allow you to define the maximum size of the request body that is allowed, helping to prevent buffer overflow attacks."
"You are using AWS WAF with CloudFront and want to ensure that only requests from your origin server are allowed. How can you achieve this?","Configure a custom header in CloudFront and create a WAF rule to match it","Use CloudFront signed URLs","Enable CloudFront geo-restriction","Block all requests from CloudFront's IP addresses","Configuring a custom header in CloudFront and creating a WAF rule to match it ensures that only requests containing the expected header (and thus originating from your origin server) are allowed."
"What is the purpose of AWS WAF's 'Challenge' action with JavaScript integration?","To require clients to execute JavaScript to verify their browser","To encrypt all traffic between the client and the server","To cache static content at the edge","To redirect clients to a different URL","The 'Challenge' action with JavaScript integration requires clients to execute JavaScript to verify their browser's integrity and capabilities, helping to distinguish between legitimate users and bots."
"Which of the following AWS WAF features allows you to automatically update your rules based on emerging threat intelligence?","AWS Marketplace Managed Rules","Rate-Based Rules","Geo Match Rules","IP Reputation Lists","AWS Marketplace Managed Rules can provide automatic updates based on emerging threat intelligence, ensuring that your WAF rules are current and effective."
"You want to visualise the traffic patterns and WAF rule matches for your web application. Which AWS service can you use?","Amazon CloudWatch","Amazon S3","Amazon Inspector","Amazon GuardDuty","Amazon CloudWatch can be used to monitor and visualise the traffic patterns and WAF rule matches for your web application, providing insights into security events."
"Which AWS service would you use to receive real-time notifications about security events detected by AWS WAF?","Amazon CloudWatch Events","Amazon SNS","Amazon SQS","AWS Lambda","Amazon CloudWatch Events (now Amazon EventBridge) can be configured to receive real-time notifications about security events detected by AWS WAF, allowing for timely responses to potential threats."
"What is the maximum TTL (Time To Live) value for a cookie that can be set by AWS WAF's 'Challenge' action?","86400 seconds (24 hours)","3600 seconds (1 hour)","600 seconds (10 minutes)","300 seconds (5 minutes)","The maximum TTL for a cookie set by AWS WAF's Challenge action is 86400 seconds (24 hours), allowing for the challenge to be valid for a defined period."
"What is the purpose of setting a 'Forwarded IP address' configuration when using AWS WAF behind a load balancer or CDN?","To allow WAF to identify the original client IP address","To encrypt traffic between the client and the load balancer","To improve web application performance","To enable geo-location filtering","Setting the 'Forwarded IP address' configuration allows AWS WAF to identify the original client IP address, even when the request passes through a load balancer or CDN, ensuring accurate IP-based filtering."
"What is the effect of configuring AWS WAF to block all requests that don't match any defined rules?","Only requests that match the defined rules are allowed; all others are blocked","All requests are blocked","All requests are allowed","Only requests that don't match any defined rules are logged","Configuring AWS WAF to block all requests that don't match any defined rules creates a 'default deny' policy, where only explicitly allowed requests are permitted."
"Which of the following is a typical use case for AWS WAF's 'AssociateResource' API?","To link a Web ACL to a CloudFront distribution or Application Load Balancer","To create a new Web ACL","To update an existing WAF rule","To delete a WAF rule group","The 'AssociateResource' API is used to link an AWS WAF Web ACL to an AWS resource, such as a CloudFront distribution or an Application Load Balancer, enabling the WAF rules to protect that resource."
"What is the primary purpose of AWS WAF?","To protect web applications from common web exploits.","To monitor network traffic for security threats.","To encrypt data at rest in S3.","To manage user identities and access.","AWS WAF is a web application firewall that helps protect your web applications from common web exploits that could affect application availability, compromise security, or consume excessive resources."
"Which AWS service does AWS WAF commonly integrate with to protect web applications?","Amazon CloudFront","Amazon EC2","Amazon S3","Amazon EBS","AWS WAF typically integrates with Amazon CloudFront, Application Load Balancer (ALB), and API Gateway to filter malicious traffic before it reaches your web applications."
"What is a Web ACL in AWS WAF?","A container for rules that inspect web requests.","A service for monitoring web application performance.","A tool for creating and managing IAM users.","A database for storing web application logs.","A Web ACL (Web Access Control List) is a container for rules that define how to inspect web requests and what action to take (allow, block, count) based on the rule conditions."
"Which of the following is an action that AWS WAF can take when a request matches a rule?","Block","Notify","Encrypt","Redirect","AWS WAF can block a request that matches a rule, preventing it from reaching the web application. It can also allow requests and count the number of matches without blocking."
"What type of rules can you create in AWS WAF?","Rules that filter web requests based on conditions like IP addresses, HTTP headers, and body.","Rules that encrypt data in transit between clients and servers.","Rules that manage IAM permissions for web application users.","Rules that automatically scale web application resources based on traffic.","AWS WAF rules can be created to filter web requests based on various conditions, including IP addresses, HTTP headers, HTTP body, URI, and more."
"You want to block requests from a specific country in AWS WAF. Which type of rule condition would you use?","Geo match condition","IP match condition","String match condition","Size constraint condition","A Geo match condition allows you to filter requests based on the country they originate from, using IP address geolocation."
"What is an AWS WAF Managed Rule Group?","A pre-configured set of rules developed by AWS or AWS Marketplace sellers.","A custom set of rules created by a user.","A collection of IAM policies for web application users.","A service for managing web application logs.","AWS WAF Managed Rule Groups are pre-configured sets of rules developed by AWS or AWS Marketplace sellers, designed to protect against common web application vulnerabilities and threats."
"Which AWS WAF component allows you to view detailed information about the web requests that AWS WAF has inspected?","AWS WAF Logs","AWS CloudTrail","Amazon CloudWatch Logs","AWS Config","AWS WAF Logs provide detailed information about the web requests that AWS WAF has inspected, including the action taken (allow, block, count) and the reasons for the action."
"What is the purpose of the AWS WAF Bot Control feature?","To identify and manage traffic from bots.","To encrypt web application traffic.","To optimise web application performance.","To manage web application access control.","AWS WAF Bot Control is designed to identify and manage traffic from bots, allowing you to block malicious bots and allow legitimate bots."
"What is the difference between 'Count' and 'Block' actions in AWS WAF?","'Count' only counts the matching requests, while 'Block' prevents them from reaching the application.","'Count' encrypts the requests, while 'Block' decrypts them.","'Count' allows all requests, while 'Block' allows only authenticated requests.","'Count' logs the requests, while 'Block' deletes them.","The 'Count' action in AWS WAF simply counts the number of requests that match a rule, while the 'Block' action prevents those requests from reaching the web application."
"Which AWS WAF feature helps protect against SQL injection attacks?","SQL injection match condition","Cross-site scripting (XSS) match condition","IP address match condition","Size constraint condition","AWS WAF's SQL injection match condition helps protect against SQL injection attacks by inspecting web requests for malicious SQL code."
"You want to protect your web application against cross-site scripting (XSS) attacks using AWS WAF. Which type of rule condition would you use?","Cross-site scripting (XSS) match condition","IP address match condition","Regular expression match condition","Geo match condition","AWS WAF's Cross-site scripting (XSS) match condition is designed to protect against XSS attacks by inspecting web requests for malicious JavaScript code."
"What is the purpose of the AWS WAF rule priority?","To determine the order in which rules are evaluated.","To determine which rules are applied to specific web applications.","To determine the cost of each rule.","To determine the level of logging for each rule.","The AWS WAF rule priority determines the order in which rules are evaluated. Rules with lower priority numbers are evaluated first."
"How can you test AWS WAF rules before deploying them to production?","By using the 'Count' action to monitor matching requests.","By using the 'Block' action in a staging environment.","By disabling all other rules.","By deleting all existing logs.","Using the 'Count' action allows you to monitor matching requests without blocking them, which is a good way to test rules before deploying them to production."
"What is the maximum number of rules you can have in a single AWS WAF Web ACL?","100","50","1000","Unlimited","The default limit is 100 rules per Web ACL, but this can be increased by contacting AWS Support."
"Which of the following can be used as a source for AWS WAF IP address match conditions?","A list of IP addresses or CIDR blocks.","A list of domain names.","A list of AWS account IDs.","A list of IAM user names.","AWS WAF IP address match conditions can use a list of IP addresses or CIDR blocks to filter requests based on their source IP address."
"What is the purpose of the AWS WAF rate-based rule?","To block requests that exceed a specified rate limit.","To encrypt requests based on a defined rate.","To dynamically scale web application resources based on request rate.","To redirect requests to different origins based on request rate.","The AWS WAF rate-based rule is used to block requests that exceed a specified rate limit, protecting against denial-of-service (DoS) attacks."
"You want to use AWS WAF to protect an API deployed using Amazon API Gateway. Which AWS WAF deployment option should you choose?","Regional","Global (CloudFront)","Edge Optimized","Lambda Authorizer","AWS WAF Regional is designed for protecting resources like Application Load Balancers and API Gateway in a specific AWS Region."
"Which of the following is a valid use case for AWS WAF Regular expression (Regex) match condition?","Filtering requests based on specific patterns in the URI or HTTP headers.","Filtering requests based on their source IP address.","Filtering requests based on their geographic location.","Filtering requests based on their size.","AWS WAF Regular expression (Regex) match condition allows you to filter requests based on specific patterns in the URI, query string, or HTTP headers using regular expressions."
"What is the purpose of AWS Firewall Manager in relation to AWS WAF?","To centrally manage and deploy AWS WAF rules across multiple AWS accounts and resources.","To encrypt AWS WAF logs.","To analyse AWS WAF traffic patterns.","To automatically update AWS WAF rules based on threat intelligence.","AWS Firewall Manager allows you to centrally manage and deploy AWS WAF rules across multiple AWS accounts and resources, simplifying security administration."
"What type of attack does an AWS WAF Size constraint condition help prevent?","Attacks that use excessively large requests to overwhelm the server.","SQL injection attacks.","Cross-site scripting (XSS) attacks.","Denial of service attacks.","Size constraint conditions help prevent attacks that use excessively large requests to overwhelm the server or exploit vulnerabilities in handling large data inputs."
"When using AWS WAF, what is the primary benefit of using AWS Managed Rules?","They are automatically updated with the latest threat intelligence.","They are cheaper than custom rules.","They provide more granular control over traffic filtering.","They are easier to configure than custom rules.","AWS Managed Rules are automatically updated with the latest threat intelligence, providing ongoing protection against emerging threats without requiring manual updates."
"Which of the following AWS WAF features can help you identify and block malicious bots?","Bot Control","Rate-based rules","Geo match conditions","IP address match conditions","AWS WAF Bot Control provides advanced capabilities for identifying and managing traffic from bots, allowing you to block malicious bots and allow legitimate bots."
"You have deployed AWS WAF in front of an Application Load Balancer (ALB). Where can you view the AWS WAF logs?","Amazon CloudWatch Logs","Amazon S3","Amazon CloudTrail","Amazon EC2 Instance Store","AWS WAF logs can be sent to Amazon CloudWatch Logs, Amazon S3, or Amazon Kinesis Data Firehose for storage and analysis."
"What is the purpose of the 'Scope' setting when configuring an AWS WAF Web ACL?","To define whether the Web ACL protects CloudFront or a Regional resource.","To define the maximum number of rules in the Web ACL.","To define the region where the Web ACL is deployed.","To define the IAM permissions for the Web ACL.","The 'Scope' setting in an AWS WAF Web ACL defines whether the Web ACL protects CloudFront (Global) or a Regional resource like an Application Load Balancer or API Gateway."
"What is the purpose of the 'Default action' setting in an AWS WAF Web ACL?","To specify the action to take when a request doesn't match any of the rules.","To specify the action to take when a request matches all of the rules.","To specify the action to take when the AWS WAF service is unavailable.","To specify the action to take when the Web ACL is first created.","The 'Default action' setting in an AWS WAF Web ACL specifies the action to take (allow or block) when a request doesn't match any of the rules defined in the Web ACL."
"Which AWS service is required when using AWS WAF to protect resources served by Amazon CloudFront?","AWS Shield Advanced","Amazon Route 53","Amazon S3","Amazon EC2","When using AWS WAF to protect resources served by Amazon CloudFront, you must also subscribe to AWS Shield Advanced for DDoS protection."
"What is the recommended method for handling false positives in AWS WAF?","Use the 'Count' action to identify problematic rules and refine them.","Disable all rules and re-enable them one by one.","Increase the sensitivity of all rules.","Block all requests from unknown IP addresses.","The recommended method for handling false positives is to use the 'Count' action to identify problematic rules and then refine the rule conditions to reduce false positives."
"Which type of log provides information about the changes made to AWS WAF configurations?","AWS CloudTrail","Amazon CloudWatch Logs","AWS WAF Logs","Amazon S3 Access Logs","AWS CloudTrail provides logs of the API calls made to AWS WAF, including changes to Web ACLs, rules, and other configurations."
"Which AWS WAF feature can help you protect against common web application vulnerabilities like SQL injection and XSS with minimal configuration?","AWS Managed Rules","Custom rules","Rate-based rules","Geo match conditions","AWS Managed Rules provide pre-configured protection against common web application vulnerabilities like SQL injection and XSS, requiring minimal configuration."
"How does AWS WAF help improve the performance of a web application?","By filtering out malicious traffic and reducing the load on the application servers.","By compressing web application content.","By caching web application content.","By automatically scaling web application resources.","By filtering out malicious traffic and reducing the load on the application servers, AWS WAF can help improve the performance of a web application."
"What is the purpose of the 'Association' setting when configuring an AWS WAF Web ACL?","To associate the Web ACL with a CloudFront distribution, Application Load Balancer, or API Gateway.","To associate the Web ACL with an IAM user.","To associate the Web ACL with an S3 bucket.","To associate the Web ACL with an EC2 instance.","The 'Association' setting in an AWS WAF Web ACL specifies which resource (CloudFront distribution, Application Load Balancer, or API Gateway) the Web ACL will protect."
"What is the function of the 'IP Reputation List' in AWS WAF?","To block traffic from IP addresses known to be associated with malicious activity.","To encrypt traffic from trusted IP addresses.","To route traffic based on the source IP address.","To log traffic from specific IP addresses.","The 'IP Reputation List' in AWS WAF contains IP addresses known to be associated with malicious activity, allowing you to block traffic from these sources."
"Which type of AWS WAF rule condition allows you to inspect the request body for specific content?","Body match condition","Header match condition","Query string match condition","URI match condition","A Body match condition allows you to inspect the request body for specific content, such as keywords or patterns, to detect malicious payloads."
"You are using AWS WAF to protect a web application that receives a large amount of traffic from a specific country. You want to allow only a small percentage of this traffic. Which AWS WAF feature can you use?","Rate limiting with Geo match","IP address match with sampling","String match with rate limiting","Geo match with sampling","You can combine rate limiting with a Geo match condition to allow only a small percentage of traffic from a specific country, while blocking the rest."
"What is the purpose of the 'Challenge' action in AWS WAF?","To require the client to solve a CAPTCHA or other puzzle before allowing the request.","To encrypt the request before forwarding it to the application.","To redirect the request to a different origin.","To log the request and continue processing.","The 'Challenge' action in AWS WAF requires the client to solve a CAPTCHA or other puzzle before allowing the request, helping to mitigate bot attacks and other automated threats."
"Which AWS service can be used to visualise and analyse AWS WAF logs stored in Amazon S3?","Amazon Athena","Amazon CloudWatch Logs Insights","Amazon Inspector","AWS Config","Amazon Athena can be used to query and analyse AWS WAF logs stored in Amazon S3, allowing you to gain insights into traffic patterns and security threats."
"What is the purpose of the 'Custom response' feature in AWS WAF?","To return a custom HTTP response code and body when a request is blocked.","To encrypt the request with a custom key.","To redirect the request to a custom URL.","To log the request with a custom message.","The 'Custom response' feature in AWS WAF allows you to return a custom HTTP response code and body when a request is blocked, providing more informative feedback to the client."
"Which AWS WAF deployment option is best suited for protecting a global web application with users distributed around the world?","Global (CloudFront)","Regional","Edge Optimized","Local","The Global (CloudFront) deployment option is best suited for protecting a global web application with users distributed around the world, as it leverages CloudFront's global network of edge locations."
"You want to create a custom AWS WAF rule that blocks requests containing a specific HTTP header. Which type of rule condition would you use?","Header match condition","Body match condition","Query string match condition","URI match condition","A Header match condition allows you to inspect the HTTP headers of a request and block requests containing a specific header or header value."
"Which AWS WAF feature helps protect against account takeover attempts?","AWS WAF Fraud Control - Account Takeover Prevention (ATP)","AWS WAF Bot Control","AWS WAF Rate-Based Rules","AWS WAF IP Reputation Lists","AWS WAF Fraud Control - Account Takeover Prevention (ATP) provides dedicated protection against account takeover attempts by identifying and blocking malicious login requests."
"What type of condition allows you to filter requests based on the size of a specific part of the request, such as the URI or the query string?","Size constraint condition","String match condition","Regex match condition","Geo match condition","A size constraint condition lets you filter traffic based on the size of the request, or of parts of the request such as the URI or query string."
"You need to protect a set of APIs hosted using API Gateway against credential stuffing attacks. Which WAF feature would be most suitable?","AWS WAF Fraud Control - Account Takeover Prevention (ATP)","AWS WAF Bot Control","AWS WAF Rate-Based Rules","AWS WAF IP Reputation Lists","AWS WAF Fraud Control - Account Takeover Prevention (ATP) is specifically designed to identify and mitigate credential stuffing attacks against login endpoints."
"In AWS WAF, what does the term 'Capacity' refer to in the context of Web ACL rules?","The computational cost of evaluating the rule.","The amount of storage used by the rule's configuration.","The network bandwidth consumed by the rule.","The number of requests processed by the rule per second.","In AWS WAF, 'Capacity' refers to the computational cost of evaluating a rule. Each rule has a capacity unit (CU) cost, and a Web ACL has a maximum capacity."
"You want to allow only legitimate search engine crawlers to access your web application while blocking other bots. Which AWS WAF feature would be most effective?","AWS WAF Bot Control with defined allowlists for known good bots","AWS WAF Rate-Based Rules to limit requests from all bots","AWS WAF IP Reputation Lists to block known malicious IP addresses","AWS WAF Geo Match to filter traffic by location","AWS WAF Bot Control allows you to identify and manage different types of bots, including allowing known good bots like search engine crawlers and blocking others."
"How can you automate the deployment and management of AWS WAF configurations across multiple AWS accounts?","AWS Firewall Manager","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS Firewall Manager allows you to centrally manage and deploy AWS WAF configurations across multiple AWS accounts."
"You want to block HTTP requests that contain a specific user-agent string known to be used by malicious bots. Which AWS WAF feature is most suitable for this task?","String match condition on the user-agent header","IP address match condition","Geo match condition","Rate-based rule","Using a string match condition on the user-agent header allows you to identify and block requests based on the content of the user-agent string."
"What is the maximum request body size that AWS WAF can inspect?","8 KB","1 KB","32 KB","64 KB","AWS WAF can inspect the first 8 KB of the request body."