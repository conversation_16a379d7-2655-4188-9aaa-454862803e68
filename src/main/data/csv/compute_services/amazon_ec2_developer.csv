"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"Which Amazon EC2 instance purchasing option is best suited for flexible workloads with no commitment?","On-Demand Instances","Reserved Instances","Spot Instances","Savings Plans","On-Demand Instances allow you to pay for compute capacity by the hour or second with no long-term commitments."
"A developer needs to run a batch processing job on Amazon EC2 that can tolerate interruptions. Which instance purchasing option is the most cost-effective?","Spot Instances","On-Demand Instances","Reserved Instances","Dedicated Hosts","Spot Instances allow you to bid on unused EC2 capacity, offering significant cost savings for fault-tolerant workloads."
"Which Amazon EC2 feature allows you to launch instances into a private subnet and control inbound and outbound traffic?","Security Groups","Elastic IP Addresses","Placement Groups","VPC Endpoints","Security Groups act as a virtual firewall for your instance to control inbound and outbound traffic."
"You need to ensure that your Amazon EC2 instances are physically isolated from instances belonging to other AWS accounts. Which EC2 feature should you use?","Dedicated Hosts","Dedicated Instances","Placement Groups","Reserved Instances","Dedicated Hosts provide physical servers dedicated for your use, ensuring isolation at the host hardware level."
"Which Amazon EC2 placement strategy should you use to ensure your instances are spread across multiple underlying hardware to minimise correlated failures?","Spread Placement Group","Cluster Placement Group","Partition Placement Group","Availability Zone","A Spread Placement Group places instances on distinct underlying hardware to reduce the risk of simultaneous failures."
"A developer is deploying a high-performance computing (HPC) application on Amazon EC2 that requires low-latency network communication between instances. Which placement strategy is most suitable?","Cluster Placement Group","Spread Placement Group","Partition Placement Group","Availability Zone","A Cluster Placement Group is a logical grouping of instances within a single Availability Zone that are placed on the same underlying network hardware for high throughput and low latency."
"Which Amazon EC2 feature allows you to assign a static public IP address to your instance that remains associated with your account even if the instance is stopped or terminated?","Elastic IP Address","Public IP Address","Private IP Address","Secondary Private IP Address","An Elastic IP Address is a static IPv4 address designed for dynamic cloud computing, allowing you to mask instance failures by remapping the address to another instance in your account."
"You need to grant an Amazon EC2 instance permissions to access other AWS services without storing AWS credentials on the instance. Which AWS feature should you use?","IAM Role for EC2 Instance","IAM User with Access Keys","Security Group Rule","VPC Endpoint Policy","An IAM Role for EC2 Instance allows you to assign permissions to an instance, enabling it to access other AWS services securely without managing credentials."
"Which Amazon EC2 metric in CloudWatch should you monitor to determine the amount of network traffic going into your instance?","NetworkIn","NetworkOut","CPUUtilization","DiskReadBytes","The NetworkIn metric represents the number of bytes received on all network interfaces by the instance."
"A developer is troubleshooting performance issues on an Amazon EC2 instance and suspects high disk activity. Which CloudWatch metric should they examine?","DiskReadOps","CPUUtilization","NetworkIn","StatusCheckFailed","DiskReadOps indicates the number of completed read operations from all instance store volumes and EBS volumes attached to the instance."
"Which Amazon EC2 API call is used to stop an instance?","StopInstances","TerminateInstances","ShutDownInstances","PauseInstances","The `StopInstances` API call is used to stop one or more Amazon EC2 instances."
"You need to retrieve metadata about an Amazon EC2 instance, such as its instance ID, public keys, and network interfaces, from within the instance itself. Which service should you use?","Instance Metadata Service","CloudWatch Agent","AWS Config","AWS Systems Manager","The Instance Metadata Service allows an instance to access information about itself."
"Which Amazon EC2 feature allows you to create a copy of your EC2 instance's root volume and any attached data volumes?","Amazon Machine Image (AMI)","Snapshot","Volume","Instance Store","An Amazon Machine Image (AMI) provides the information required to launch an instance, including a template for the root volume."
"You need to create a custom Amazon Machine Image (AMI) from an existing EC2 instance. Which action should you take?","Create Image from Instance","Create Snapshot from Volume","Register AMI","Launch More Like This","The 'Create Image from Instance' action creates a new AMI from a running or stopped EC2 instance."
"Which Amazon EC2 storage option provides persistent block storage for use with EC2 instances?","Amazon Elastic Block Store (EBS)","Instance Store","Amazon S3","Amazon EFS","Amazon EBS provides durable, block-level storage volumes that can be attached to a running instance."
"You need a temporary block-level storage for your Amazon EC2 instance that is physically attached to the host computer. Which storage option should you choose?","Instance Store","Amazon EBS","Amazon S3","Amazon EFS","Instance store provides temporary block-level storage for your instance. The data on an instance store volume persists only during the life of the instance."
"Which Amazon EC2 feature allows you to automatically adjust the number of EC2 instances in your application based on demand?","Amazon EC2 Auto Scaling","Elastic Load Balancing","AWS Lambda","AWS Systems Manager","Amazon EC2 Auto Scaling helps you maintain application availability and allows you to automatically add or remove EC2 instances according to conditions you define."
"You are running a web application on Amazon EC2 and need to distribute incoming traffic across multiple instances to improve availability and fault tolerance. Which service should you use?","Elastic Load Balancing (ELB)","Amazon Route 53","AWS Global Accelerator","Amazon CloudFront","Elastic Load Balancing automatically distributes incoming application traffic across multiple targets, such as EC2 instances."
"Which type of Elastic Load Balancer is best suited for load balancing HTTP and HTTPS traffic?","Application Load Balancer (ALB)","Network Load Balancer (NLB)","Classic Load Balancer (CLB)","Gateway Load Balancer","An Application Load Balancer is best suited for load balancing of HTTP and HTTPS traffic and provides advanced request routing targeted at the delivery of modern application architectures."
"Which type of Elastic Load Balancer is best suited for load balancing TCP, UDP, and TLS traffic at the connection level?","Network Load Balancer (NLB)","Application Load Balancer (ALB)","Classic Load Balancer (CLB)","Gateway Load Balancer","A Network Load Balancer is best suited for load balancing TCP, UDP, and TLS traffic where extreme performance is required."
"You need to securely connect to your Amazon EC2 instance in a private subnet from your on-premises network. Which AWS service can facilitate this?","AWS Site-to-Site VPN","Internet Gateway","NAT Gateway","VPC Peering","AWS Site-to-Site VPN allows you to securely connect your on-premises network or branch office site to your Amazon VPC."
"Which Amazon EC2 feature allows you to control the network reachability of your instances and subnets?","Route Tables","Security Groups","Network ACLs","Internet Gateway","Route Tables contain a set of rules, called routes, that are used to determine where network traffic is directed."
"You need to allow inbound SSH access to your Amazon EC2 instance from a specific IP address range. Where should you configure this rule?","Security Group associated with the instance","Network ACL associated with the subnet","Route Table associated with the subnet","Internet Gateway","Security Groups act as a stateful firewall and are the primary way to control inbound and outbound traffic for your instances."
"Which Amazon EC2 feature allows you to create a logical isolation of your AWS resources, including EC2 instances, within a virtual network?","Amazon Virtual Private Cloud (VPC)","Availability Zone","Region","Edge Location","Amazon VPC enables you to launch AWS resources into a virtual network that you've defined."
"You are designing a highly available application on Amazon EC2. Across how many Availability Zones should you deploy your instances at a minimum?","Two","One","Three","Four","Deploying instances across at least two Availability Zones provides high availability by protecting against failures in a single data centre."
"Which Amazon EC2 feature allows you to encrypt your EBS volumes and snapshots at rest?","EBS Encryption","Instance Store Encryption","S3 Encryption","KMS Integration","EBS Encryption uses AWS Key Management Service (KMS) to encrypt your EBS volumes and snapshots."
"You need to share an encrypted Amazon Machine Image (AMI) with another AWS account. What is the correct process?","Copy the AMI and re-encrypt it with a KMS key shared with the target account, then share the copy.","Share the AMI directly","Share the underlying snapshot directly.","Decrypt the AMI and then share it.","To share an encrypted AMI, you must first copy it and re-encrypt it with a KMS key that you share with the target account. Then, you can share the re-encrypted copy of the AMI."
"Which Amazon EC2 feature allows you to monitor the status of your instances and receive notifications when they become impaired?","EC2 Status Checks","CloudWatch Alarms","AWS Personal Health Dashboard","AWS Config Rules","EC2 Status Checks (System Status Checks and Instance Status Checks) monitor the health of your instances."
"A developer needs to automate the process of patching and updating Amazon EC2 instances. Which AWS service can help with this?","AWS Systems Manager Patch Manager","AWS CloudFormation","AWS OpsWorks","AWS Elastic Beanstalk","AWS Systems Manager Patch Manager automates the process of patching managed instances with security-related and other types of updates."
"Which Amazon EC2 feature allows you to run commands on your instances remotely without needing to SSH into them?","AWS Systems Manager Run Command","AWS CloudShell","EC2 Instance Connect","Session Manager","AWS Systems Manager Run Command lets you remotely and securely manage the configuration of your managed instances."
"You need to grant an IAM user the ability to start and stop specific Amazon EC2 instances based on tags. Which IAM policy condition key should you use?","ec2:ResourceTag/tag-key","ec2:InstanceId","ec2:Region","aws:PrincipalArn","The `ec2:ResourceTag/tag-key` condition key allows you to control access to EC2 resources based on their tags."
"A developer is writing an application that needs to discover the private IP addresses of other EC2 instances in the same Auto Scaling group. Which service can they use?","AWS Cloud Map","Amazon Route 53","Elastic Load Balancing","Instance Metadata Service","AWS Cloud Map is a cloud resource discovery service that allows you to define custom names for your application resources, and it maintains the updated location of these dynamically changing resources."
"Which Amazon EC2 feature allows you to launch instances with a pre-defined set of configurations, including the AMI, instance type, and storage?","Launch Template","Launch Configuration","Auto Scaling Group","Placement Group","A Launch Template is a newer version of Launch Configurations and allows you to store launch parameters so you don't have to enter them every time you launch an instance."
"You need to migrate an existing on-premises virtual machine to Amazon EC2. Which AWS service can assist with this process?","AWS Server Migration Service (SMS)","AWS DataSync","AWS Snowball","AWS Transfer Family","AWS Server Migration Service (SMS) is an agentless service that makes it easier and faster for you to migrate thousands of on-premises workloads to AWS."
"Which Amazon EC2 feature allows you to create a consistent point-in-time backup of your EBS volumes?","EBS Snapshots","AMIs","Instance Store Volumes","EBS Volumes","EBS Snapshots are point-in-time backups of your EBS volumes."
"You need to share an unencrypted EBS snapshot with another AWS account. What is the correct process?","Modify the snapshot permissions to allow sharing with the target account ID.","Copy the snapshot and then share the copy.","Encrypt the snapshot and then share it.","Share the volume the snapshot was created from.","You can modify the permissions of an unencrypted EBS snapshot to share it with specific AWS accounts."
"Which Amazon EC2 feature allows you to attach a secondary network interface to your instance?","Elastic Network Interface (ENI)","Elastic IP Address","Public IP Address","Private IP Address","An Elastic Network Interface (ENI) is a logical networking component in a VPC that represents a virtual network card."
"You are designing a fault-tolerant application on Amazon EC2 and need to ensure that instances are automatically replaced if they fail. Which service should you use in conjunction with EC2?","Amazon EC2 Auto Scaling","Elastic Load Balancing","AWS Lambda","AWS Systems Manager","Amazon EC2 Auto Scaling can automatically replace unhealthy instances."
"Which Amazon EC2 feature allows you to control the maximum number of instances that can be launched in a region?","EC2 Service Quotas","Auto Scaling Group Limits","Placement Group Limits","Availability Zone Limits","EC2 Service Quotas (formerly limits) restrict the maximum number of instances, volumes, and other resources you can provision in a region."
"A developer needs to deploy an application to a fleet of EC2 instances and manage the deployment process. Which AWS service is designed for this?","AWS CodeDeploy","AWS CodePipeline","AWS CodeBuild","AWS Elastic Beanstalk","AWS CodeDeploy is a deployment service that automates application deployments to a variety of compute services, including Amazon EC2."
"Which Amazon EC2 feature allows you to run a script or provide configuration instructions when an instance is launched?","User Data","Instance Metadata","AMI Configuration","Launch Template","User data is data that you can configure at launch time to perform common automated configuration tasks and even run scripts after the instance starts."
"You need to monitor the performance of your Amazon EC2 instance at a more granular level than the default 5-minute interval. Which CloudWatch feature should you enable?","Detailed Monitoring","Basic Monitoring","Custom Metrics","CloudWatch Logs","Detailed Monitoring provides data in 1-minute periods, while Basic Monitoring provides data in 5-minute periods."
"Which Amazon EC2 feature allows you to assign a friendly name to your instance?","Tags","Instance ID","AMI ID","Public DNS","Tags are labels that you can assign to your AWS resources, including EC2 instances, to help you manage and identify them."
"You need to connect to your Amazon EC2 instance using a secure shell (SSH) without managing SSH keys. Which AWS service can facilitate this?","EC2 Instance Connect","AWS Systems Manager Session Manager","AWS CloudShell","VPC Peering","EC2 Instance Connect allows you to connect to your instances using SSH without requiring an SSH key pair."
"Which Amazon EC2 feature allows you to create a rule that automatically scales your Auto Scaling group based on a CloudWatch alarm?","Scaling Policy","Launch Configuration","Launch Template","Placement Group","A Scaling Policy in Amazon EC2 Auto Scaling determines when and how your Auto Scaling group scales."
"A developer is building a microservices architecture on Amazon EC2 and needs a way for services to discover each other. Which AWS service can help with this?","AWS Cloud Map","Elastic Load Balancing","Amazon Route 53","VPC Peering","AWS Cloud Map is a cloud resource discovery service that allows you to define custom names for your application resources."
"Which Amazon EC2 feature allows you to control the network traffic at the subnet level, acting as a stateless firewall?","Network ACLs (NACLs)","Security Groups","Route Tables","Internet Gateway","Network ACLs are stateless firewalls that control inbound and outbound traffic at the subnet level."
"You need to ensure that your Amazon EC2 instances are launched with the latest security updates. Which practice should you follow?","Regularly update your AMIs or use a service like EC2 Image Builder.","Manually update instances after launch.","Use a script in User Data to apply updates.","Rely on AWS to automatically update instances.","Regularly updating your AMIs or using a service like EC2 Image Builder ensures that new instances are launched with the latest security patches."
"Which Amazon EC2 feature allows you to reserve compute capacity for a specific instance type in a specific Availability Zone for a defined term?","Capacity Reservations","Reserved Instances","Savings Plans","Spot Instances","Capacity Reservations allow you to reserve compute capacity for your EC2 instances in a specific Availability Zone for any duration."
"A developer needs to troubleshoot why an Amazon EC2 instance is unreachable. Which EC2 status check should they examine first?","System Status Checks","Instance Status Checks","CloudWatch Alarms","Network ACLs","System Status Checks monitor the underlying AWS infrastructure, and a failed check indicates an issue with the host that requires AWS intervention."
"Which Amazon EC2 feature allows you to assign a secondary private IP address to your instance?","Elastic Network Interface (ENI)","Elastic IP Address","Public IP Address","Primary Private IP Address","An Elastic Network Interface (ENI) can have one or more secondary private IP addresses."
"You need to grant an IAM user the ability to view all Amazon EC2 instances in your account. Which IAM action should you include in their policy?","ec2:DescribeInstances","ec2:RunInstances","ec2:StartInstances","ec2:StopInstances","The `ec2:DescribeInstances` action allows an IAM user to view information about your EC2 instances."
"Which Amazon EC2 feature allows you to create a template for launching EC2 instances that includes parameters for the AMI, instance type, network settings, and storage?","Launch Template","Launch Configuration","Auto Scaling Group","Placement Group","A Launch Template is a template that contains the configuration information to launch an instance."
"What is the primary difference between EC2 On-Demand Instances and EC2 Spot Instances in terms of pricing and availability?","On-Demand Instances have predictable pricing and guaranteed availability, while Spot Instances offer discounted pricing for unused capacity with the risk of interruption.","On-Demand Instances are cheaper but require a long-term commitment, while Spot Instances are expensive and available only for short durations.","On-Demand Instances are suitable for fault-tolerant background tasks, while Spot Instances are ideal for critical workloads.","On-Demand Instances automatically scale based on demand, while Spot Instances require manual scaling.","On-Demand instances are billed at a fixed rate and guaranteed to be available, making them suitable for critical workloads, while Spot instances are offered at a discount but can be terminated if the spot price exceeds your bid, making them suitable for flexible, fault-tolerant workloads."
"An application running on an EC2 instance requires persistent storage that is accessible by other instances and survives instance termination. Which AWS storage option is MOST appropriate?","Elastic File System (EFS)","EC2 Instance Store","Elastic Block Storage (EBS)","S3 Glacier","EFS provides a scalable, elastic, and fully managed file system that can be mounted on multiple EC2 instances concurrently and persists independently of instance lifecycle, unlike EBS which is tied to a single instance, or Instance Store which is ephemeral."
"You need to configure an EC2 instance to automatically receive security updates and patches during a specific maintenance window. Which AWS service can BEST achieve this?","AWS Systems Manager Patch Manager","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS Systems Manager Patch Manager allows you to automate the process of patching managed instances, including EC2 instances, with security-related updates. You can schedule these updates during specific maintenance windows."
"Which of the following EC2 instance purchasing options is BEST suited for workloads with consistent, predictable usage patterns where you can commit to using the instance for a period of 1 or 3 years?","Reserved Instances","Spot Instances","Dedicated Hosts","On-Demand Instances","Reserved Instances provide a significant discount compared to On-Demand pricing, in exchange for a commitment to use the instance for a specified term."
"You have an EC2 instance running in a public subnet. You want to ensure that only traffic originating from a specific IP address range can access the instance via SSH (port 22). Which AWS service should you use to configure this restriction?","Security Groups","Network ACLs","IAM Roles","Route Tables","Security Groups act as a virtual firewall for your EC2 instances, allowing you to control inbound and outbound traffic based on IP address, protocol, and port. Network ACLs control traffic at the subnet level, while Security Groups control traffic at the instance level."