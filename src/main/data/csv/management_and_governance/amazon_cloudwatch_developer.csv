"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Amazon CloudWatch?","Monitoring and observability of AWS resources and applications","Providing a serverless compute environment","Managing network traffic","Storing large data sets","CloudWatch is designed for monitoring and observability, allowing you to collect and track metrics, collect and monitor log files, set alarms, and automatically react to changes in your AWS resources."
"Which Amazon CloudWatch feature allows you to set up automated actions based on metric thresholds?","Alarms","Events","Logs","Dashboards","CloudWatch Alarms allow you to monitor metrics and trigger actions when those metrics cross defined thresholds, automating responses to potential issues."
"What type of data can be stored and monitored in Amazon CloudWatch Logs?","Application logs, system logs, and custom logs","Database backups","EC2 instance images","S3 bucket contents","CloudWatch Logs is designed to collect and monitor logs from various sources, including applications, systems, and custom sources."
"Which Amazon CloudWatch feature enables you to visualise metrics and logs in a customisable format?","Dashboards","Events","Insights","Synthetics","CloudWatch Dashboards provide a visual interface for monitoring metrics and logs, allowing you to create custom views tailored to your specific needs."
"What is the purpose of Amazon CloudWatch Events (now EventBridge)?","To react to changes in the state of your AWS resources","To store and analyse log data","To visualise performance metrics","To provide a serverless compute environment","CloudWatch Events (now EventBridge) enables you to build event-driven applications by reacting to changes in the state of your AWS resources."
"Which metric namespace in Amazon CloudWatch is specifically used for monitoring Amazon EC2 instances?","AWS/EC2","AWS/RDS","AWS/S3","AWS/Lambda","The AWS/EC2 namespace contains metrics related to Amazon EC2 instances, such as CPU utilisation, network I/O, and disk I/O."
"Which Amazon CloudWatch feature can you use to monitor the availability and performance of your applications from different geographical locations?","Synthetics","Logs Insights","ServiceLens","Contributor Insights","CloudWatch Synthetics allows you to create canaries that monitor your application endpoints from different locations, providing insights into availability and performance."
"Which Amazon CloudWatch Logs feature allows you to search and analyse log data using a query language?","Logs Insights","Metric Filters","Subscription Filters","Log Groups","CloudWatch Logs Insights provides a powerful query language for searching and analysing log data, enabling you to quickly identify and troubleshoot issues."
"What is the purpose of Amazon CloudWatch Metric Filters?","To extract metric data from log events","To filter network traffic","To filter API calls","To filter S3 bucket objects","Metric Filters extract metric data from log events, allowing you to create metrics based on patterns found in your logs."
"What is the function of Amazon CloudWatch Agent?","Collect logs and metrics from EC2 instances and on-premises servers","Manage IAM roles","Orchestrate containers","Provision EC2 instances","The CloudWatch Agent is designed to collect logs and metrics from both EC2 instances and on-premises servers, providing comprehensive monitoring capabilities."
"Which of the following is NOT a feature of Amazon CloudWatch?","Code deployment","Alarms","Dashboards","Logs Insights","Code deployment is not a feature of CloudWatch. AWS CodeDeploy is used for code deployment."
"What is the retention period for detailed monitoring metrics (1-minute resolution) in Amazon CloudWatch?","15 months","3 months","1 month","6 months","Detailed monitoring metrics (1-minute resolution) are retained for 15 months in Amazon CloudWatch."
"How can you send custom metrics to Amazon CloudWatch?","Using the AWS CLI or SDK","Using the AWS Management Console only","Using CloudFront","Using AWS Config","Custom metrics can be sent to CloudWatch using the AWS CLI or SDK, allowing you to monitor application-specific data."
"What is the purpose of Amazon CloudWatch Anomaly Detection?","To automatically identify unusual metric behaviour","To detect network intrusions","To identify security vulnerabilities","To detect code errors","CloudWatch Anomaly Detection uses machine learning to automatically identify unusual metric behaviour, helping you proactively detect potential issues."
"Which service integrates with Amazon CloudWatch to provide distributed tracing capabilities for microservices?","X-Ray","CodeDeploy","CloudTrail","Config","AWS X-Ray integrates with CloudWatch to provide distributed tracing capabilities, enabling you to trace requests through your microservices architecture."
"What is the purpose of Amazon CloudWatch Contributor Insights?","To identify top contributors to system performance issues","To identify unused AWS resources","To identify security misconfigurations","To identify cost optimisation opportunities","CloudWatch Contributor Insights helps you identify the top contributors to system performance issues by analysing log data."
"Which of the following actions can be triggered by a CloudWatch Alarm?","Sending an SNS notification, Auto Scaling actions, EC2 actions","Creating an S3 bucket","Launching a Lambda function","Creating a VPC","CloudWatch Alarms can trigger actions such as sending SNS notifications, performing Auto Scaling actions, or taking EC2 actions."
"You want to monitor the number of errors occurring in your application logs. Which Amazon CloudWatch feature should you use?","Metric Filters","Alarms","Dashboards","Events","Metric Filters can be used to extract the number of errors from your application logs and create a metric, which can then be monitored with an alarm."
"What type of data is NOT typically stored in Amazon CloudWatch Logs?","Binary files","Application logs","System logs","Custom logs","Binary files are not typically stored in CloudWatch Logs, as it is primarily designed for text-based log data."
"Which of the following is a benefit of using Amazon CloudWatch Logs Insights?","Real-time log analysis","Automated security patching","Automatic database backups","Automatic code deployment","CloudWatch Logs Insights provides real-time log analysis, allowing you to quickly search and analyse log data to identify and troubleshoot issues."
"You need to monitor the CPU utilisation of all your EC2 instances in a specific AWS Region. How can you achieve this using Amazon CloudWatch?","Create a CloudWatch Dashboard with EC2 metrics","Use AWS Config rules","Create a CloudTrail trail","Use AWS Trusted Advisor","A CloudWatch Dashboard allows you to visualise EC2 metrics, providing a central view of CPU utilisation across all your instances."
"What is the minimum time resolution for custom metrics in Amazon CloudWatch?","1 second","1 minute","5 minutes","1 hour","The minimum time resolution for custom metrics in CloudWatch is 1 second, allowing for fine-grained monitoring."
"Which of the following AWS services is commonly used as a target for CloudWatch Events (EventBridge) rules?","Lambda","S3","EC2","IAM","Lambda functions are commonly used as targets for CloudWatch Events (EventBridge) rules, allowing you to trigger serverless code in response to events."
"You want to receive a notification when your EC2 instance CPU utilisation exceeds 80% for 5 consecutive minutes. How would you configure this in Amazon CloudWatch?","Create a CloudWatch Alarm with a threshold of 80% and an evaluation period of 5 minutes","Use AWS Config to monitor CPU utilisation","Create a CloudTrail trail to log CPU utilisation","Use AWS Trusted Advisor to monitor CPU utilisation","A CloudWatch Alarm allows you to set a threshold and evaluation period to trigger a notification when a metric exceeds the threshold for a specified duration."
"What is the purpose of Amazon CloudWatch EMF (Embedded Metric Format)?","To structure log events for easier analysis","To encrypt log data","To compress log data","To archive log data","EMF allows you to structure log events in a way that enables CloudWatch to automatically extract metrics from the logs."
"Which of the following is NOT a valid action that can be taken by a CloudWatch Alarm?","Deleting an EC2 instance","Sending an SNS notification","Performing an Auto Scaling action","Stopping an EC2 instance","Deleting an EC2 instance is not a valid action that can be triggered by a CloudWatch Alarm."
"You want to analyse the performance of your Lambda functions. Which Amazon CloudWatch feature can provide insights into function execution time and errors?","CloudWatch Logs Insights","CloudWatch Synthetics","CloudWatch ServiceLens","CloudWatch Anomaly Detection","CloudWatch Logs Insights can be used to analyse Lambda function logs, providing insights into execution time and errors."
"What is the purpose of Amazon CloudWatch ServiceLens?","To visualise and analyse the health and performance of your applications","To automate security patching","To manage network traffic","To optimise AWS costs","ServiceLens helps you visualise and analyse the health and performance of your applications, providing a unified view of your services."
"Which of the following is a typical use case for Amazon CloudWatch Logs Subscription Filters?","Sending log data to Elasticsearch or Splunk","Filtering network traffic","Filtering API calls","Filtering S3 bucket objects","Subscription Filters allow you to send log data to other AWS services like Elasticsearch or Splunk for further analysis."
"You need to monitor the disk space utilisation of your EC2 instances. Which Amazon CloudWatch metric should you use?","DiskSpaceUtilization","CPUUtilization","NetworkUtilization","MemoryUtilization","DiskSpaceUtilization is the appropriate metric to monitor disk space usage."
"Which service can you integrate with CloudWatch to receive notifications via email or SMS when an alarm is triggered?","SNS","SQS","SES","Lambda","SNS (Simple Notification Service) can be configured to send notifications when a CloudWatch Alarm is triggered."
"What is the default metric resolution for standard monitoring of EC2 instances in Amazon CloudWatch?","5 minutes","1 minute","10 minutes","15 minutes","Standard monitoring for EC2 instances provides metrics at a 5-minute resolution."
"Which of the following is NOT a valid dimension for an EC2 instance metric in Amazon CloudWatch?","InstanceType","ImageId","AvailabilityZone","RoleName","RoleName is not a standard dimension for EC2 instance metrics. The other options are valid dimensions."
"You want to create a CloudWatch Alarm that triggers when the average CPU utilisation of your Auto Scaling group exceeds 70% for 3 consecutive evaluation periods of 1 minute each. What should be the evaluation period and period settings for the alarm?","Evaluation Period: 3, Period: 1 minute","Evaluation Period: 1, Period: 3 minutes","Evaluation Period: 3, Period: 3 minutes","Evaluation Period: 1, Period: 1 minute","The evaluation period is the number of periods that the metric must be in the ALARM state before the alarm triggers, and the period is the length of each evaluation period."
"Which feature of CloudWatch allows you to group log streams from different sources into a single logical unit?","Log Groups","Metric Filters","Subscription Filters","Log Streams","Log Groups allow you to logically group log streams from different sources (e.g., multiple EC2 instances) for easier management and analysis."
"What is the main benefit of using CloudWatch Anomaly Detection for metrics?","Proactively identify unusual behaviour patterns","Automatically fix security vulnerabilities","Reduce compute costs","Improve network performance","Anomaly Detection uses machine learning to learn the normal behaviour of metrics and proactively identify deviations, allowing you to respond to potential issues before they impact users."
"Which AWS service is commonly used to store CloudWatch Logs archives for long-term retention?","S3","Glacier","EFS","RDS","S3 is a common and cost-effective service for storing CloudWatch Logs archives for long-term retention."
"What is the purpose of a CloudWatch Logs Metric Filter Pattern?","To define the criteria for extracting metrics from log events","To filter network traffic","To define IAM permissions","To compress log data","The Metric Filter Pattern specifies the criteria that CloudWatch uses to extract metrics from log events."
"You are using CloudWatch to monitor the health of your application and need to be alerted when the error rate exceeds a certain threshold. Which CloudWatch feature should you utilise?","CloudWatch Alarms based on a Metric Filter","CloudWatch Logs Insights","CloudWatch Events (EventBridge)","CloudWatch Dashboards","You can create a Metric Filter to extract the error rate from your logs and then create a CloudWatch Alarm based on this metric to trigger when the threshold is exceeded."
"Which of the following is NOT a valid target for a CloudWatch Events (EventBridge) rule?","SNS Topic","SQS Queue","CloudWatch Alarm","Lambda Function","CloudWatch Alarms cannot be targeted directly by CloudWatch Event rules."
"When using CloudWatch Agent, what is a 'stanza'?","A configuration section defining the data to be collected","A network security group rule","An IAM role","A CloudFormation template","In the context of the CloudWatch Agent, a stanza is a section in the configuration file that defines the logs or metrics you want to collect."
"Which CloudWatch feature provides a visual representation of your application's architecture, along with its performance and dependencies?","CloudWatch ServiceLens","CloudWatch Anomaly Detection","CloudWatch Logs Insights","CloudWatch Synthetics","ServiceLens integrates with other AWS services to provide a comprehensive view of your application's architecture and performance."
"What is the recommended method for collecting logs from a containerised application running on Amazon ECS?","Using the awslogs log driver","Using CloudWatch Agent on the host","Mounting a shared volume","Using a third-party logging tool","The `awslogs` log driver sends container logs directly to CloudWatch Logs."
"Which CloudWatch feature allows you to simulate user traffic to test the availability and performance of your web applications and APIs?","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch Logs Insights","CloudWatch Contributor Insights","CloudWatch Synthetics enables the creation of 'canaries' that simulate user traffic to proactively identify issues."
"You need to give a user the ability to view CloudWatch metrics and dashboards, but not to create or modify them. Which IAM permission would be most appropriate?","`cloudwatch:Get*` and `cloudwatch:List*`","`cloudwatch:*`","`cloudwatch:Put*`","`cloudwatch:Delete*`","The `cloudwatch:Get*` and `cloudwatch:List*` permissions allow a user to view existing CloudWatch resources without the ability to modify them."
"Which of the following is a valid unit of measure for a custom metric in CloudWatch?","Count, Seconds, Bytes","VPC ID","EC2 Instance ID","S3 Bucket Name","Custom metrics require a unit of measure to be specified, and 'Count', 'Seconds', and 'Bytes' are all valid options."
"When using the CloudWatch Agent, what is the purpose of the 'metrics_collected' section in the agent configuration file?","To specify which metrics to collect from the system","To define the AWS region","To specify the IAM role","To define which logs to collect","The 'metrics_collected' section allows you to specify which metrics you want the CloudWatch Agent to collect from the system, such as CPU utilisation or memory usage."
"You want to monitor the number of HTTP 500 errors returned by your application running on EC2. How can you best achieve this using CloudWatch?","Use a Metric Filter to extract the count of 500 errors from application logs","Enable detailed monitoring for the EC2 instance","Use CloudTrail to log API calls","Configure AWS Config rules","Using a Metric Filter to extract the error count from application logs allows you to create a CloudWatch metric and set an alarm based on the number of 500 errors."
"Which AWS CloudWatch feature allows you to visualise metrics and logs in interactive dashboards?","CloudWatch Dashboards","CloudWatch Insights","CloudWatch Events","CloudWatch Logs","CloudWatch Dashboards provides a customisable, visual representation of your metrics and logs."
"What is the primary purpose of AWS CloudWatch Alarms?","To trigger actions based on metric values","To store log data","To define infrastructure as code","To analyse network traffic","CloudWatch Alarms monitor metrics and trigger actions when the metrics exceed defined thresholds."
"Which AWS CloudWatch service is used to collect and monitor log files from your EC2 instances?","CloudWatch Logs","CloudWatch Metrics","CloudWatch Events","CloudWatch Synthetics","CloudWatch Logs is specifically designed for collecting and monitoring log data."
"What type of data does CloudWatch primarily collect to monitor your AWS resources?","Metrics and Logs","Network packets","User activity","Security vulnerabilities","CloudWatch collects metrics, which are numerical data points over time, and logs, which are records of events."
"Which CloudWatch feature allows you to monitor the availability and response time of your applications from multiple geographic locations?","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch ServiceLens","CloudWatch RUM","CloudWatch Synthetics creates canaries that test your application's availability and performance from different locations."
"What is the purpose of the AWS CloudWatch Metric Streams?","To stream metrics to other destinations for further analysis","To aggregate multiple logs into a single stream","To create alarms based on metric data","To visualise metrics in a dashboard","Metric Streams allow you to continuously stream metrics to destinations like Amazon S3, Datadog or Splunk."
"Which AWS service integrates with CloudWatch to allow you to automatically respond to changes in your resource utilisation?","Auto Scaling","Elastic Load Balancing","CloudTrail","AWS Config","Auto Scaling can use CloudWatch metrics to automatically scale your resources up or down based on demand."
"How can you access AWS CloudWatch?","AWS Management Console, AWS CLI, and AWS SDKs","Only through the AWS Management Console","Only through the AWS CLI","Only through AWS SDKs","CloudWatch can be accessed and managed through various interfaces, including the Console, CLI, and SDKs."
"What is the default retention period for CloudWatch Logs, if not explicitly configured?","Indefinite","7 days","30 days","90 days","CloudWatch Logs have an indefinite retention period by default unless you specify otherwise."
"What is the purpose of the AWS CloudWatch Logs Insights?","To query and analyse log data","To visualise metrics","To create alarms","To manage EC2 instances","CloudWatch Logs Insights provides a powerful query language to analyse log data and extract valuable insights."
"In CloudWatch, what is a 'metric filter' used for?","Extracting metric data from log events","Filtering which metrics are displayed on a dashboard","Filtering incoming requests to your application","Filtering which CloudWatch events are processed","Metric filters allow you to extract numeric values from log events and turn them into CloudWatch metrics."
"Which AWS CloudWatch feature can automatically detect unusual patterns in your metrics?","CloudWatch Anomaly Detection","CloudWatch Synthetics","CloudWatch Events","CloudWatch Alarms","CloudWatch Anomaly Detection uses machine learning algorithms to identify anomalies in your metrics."
"Which of the following is NOT a valid statistic that can be used in a CloudWatch alarm?","Average","Minimum","Maximum","Median","Median is not a valid statistic, the valid statics are Average, Minimum, Maximum, Sum, SampleCount, and percentiles."
"What is the purpose of AWS CloudWatch Events (now EventBridge)?","To react to changes in the state of your AWS resources","To store log data","To monitor network traffic","To perform code deployments","CloudWatch Events (now EventBridge) allows you to create rules that react to changes in the state of your AWS resources."
"How can you monitor the health of your web application using AWS CloudWatch?","By creating CloudWatch Synthetics canaries","By analysing CloudWatch Logs","By monitoring EC2 instance metrics","By using CloudWatch ServiceLens","CloudWatch Synthetics canaries simulate user interactions with your application to monitor its health and availability."
"Which of the following is a typical use case for AWS CloudWatch Logs?","Troubleshooting application errors","Managing database connections","Scaling EC2 instances","Configuring security groups","CloudWatch Logs is commonly used to collect and analyse application logs, aiding in troubleshooting."
"What is the relationship between CloudWatch Metrics and CloudWatch Logs?","Metrics are numerical data points, while logs are records of events","Metrics are used for logging, while logs are used for monitoring","Metrics and logs are the same thing","Metrics are stored in Logs","CloudWatch Metrics are numerical data points over time, whereas CloudWatch Logs are records of events."
"What is the purpose of CloudWatch 'Dimensions'?","To add metadata to metrics for more detailed analysis","To define the resolution of metrics","To set alarm thresholds","To visualise metrics on a dashboard","Dimensions are name-value pairs that you can add to metrics to further identify them."
"What is the maximum resolution (granularity) available for custom metrics in CloudWatch?","1 second","5 seconds","1 minute","5 minutes","You can publish custom metrics with a granularity as low as 1 second."
"Which AWS service can be integrated with CloudWatch to provide distributed tracing capabilities?","AWS X-Ray","AWS CloudTrail","AWS Config","AWS IAM","AWS X-Ray is a distributed tracing service that integrates with CloudWatch to provide insights into application performance."
"What is the purpose of 'Retention Policy' in CloudWatch Logs?","To define how long log events are stored","To control access to log data","To filter log events","To encrypt log data","Retention Policy defines the duration for which log events are stored in CloudWatch Logs."
"If your CloudWatch alarm is in the 'INSUFFICIENT_DATA' state, what does this typically mean?","The alarm does not have enough data to determine its state","The alarm is not configured correctly","The metric being monitored is not reporting data","The alarm threshold is set too low","The INSUFFICIENT_DATA state indicates that the alarm doesn't have enough data points to determine whether it is in ALARM or OK state."
"Which AWS service can be used to ship logs from on-premises servers to CloudWatch Logs?","CloudWatch Agent","CloudTrail","AWS Config","AWS Systems Manager","The CloudWatch Agent can be installed on on-premises servers to collect logs and metrics and send them to CloudWatch."
"What is the function of CloudWatch Embedded Metric Format (EMF)?","To efficiently publish complex application data as metrics","To encrypt log data","To visualise metrics in a dashboard","To create CloudWatch alarms","EMF allows you to publish complex application data as structured JSON blobs that CloudWatch automatically parses and indexes as metrics."
"Which of the following statements about CloudWatch pricing is correct?","You pay for the data ingested, stored, and retrieved","CloudWatch is a free service","You only pay for alarms","You only pay for dashboards","CloudWatch pricing is based on the amount of data ingested, stored, and retrieved, as well as the number of metrics and alarms."
"What is the purpose of AWS CloudWatch RUM (Real User Monitoring)?","To collect performance data from end-user browsers","To monitor server CPU utilisation","To analyse network traffic","To create CloudWatch alarms","CloudWatch RUM allows you to collect performance data from end-user browsers to understand user experience."
"Which CloudWatch feature is helpful for identifying the root cause of performance issues across multiple AWS services?","CloudWatch ServiceLens","CloudWatch Insights","CloudWatch Alarms","CloudWatch Dashboards","CloudWatch ServiceLens provides a unified view of your application's health and performance across multiple services, making it easier to identify root causes."
"What is the primary benefit of using CloudWatch Container Insights?","Automated monitoring and troubleshooting of containerised applications","Enhanced security for container workloads","Optimised resource allocation for containers","Simplified deployment of container images","Container Insights automates the discovery and collection of container metrics and logs, making it easier to monitor and troubleshoot containerised applications."
"You want to monitor the number of times a specific error message appears in your application logs. Which CloudWatch feature should you use?","Metric Filter","Alarm","Dashboard","Synthetics Canary","A Metric Filter can be configured to count the occurrences of a specific error message in your logs, turning that into a CloudWatch metric."
"Which AWS service can you use to forward CloudWatch Logs to a third-party analytics platform like Splunk?","CloudWatch Metric Streams","CloudWatch Events","CloudWatch Alarms","CloudWatch Dashboards","CloudWatch Metric Streams are designed to stream metrics to other destinations for further analysis."
"What is the maximum number of metrics you can display on a single CloudWatch dashboard?","100","50","25","10","A single CloudWatch dashboard can display up to 100 metrics."
"Which of these actions can NOT be performed by a CloudWatch Alarm?","Restart an EC2 instance","Send an SNS notification","Trigger an Auto Scaling policy","Execute an SSM document","CloudWatch alarms can send SNS notifications and trigger Auto Scaling policies, but they cannot directly restart an EC2 instance."
"What is the purpose of CloudWatch Contributor Insights?","To identify top contributors to application latency","To visualise metrics on a dashboard","To create alarms","To encrypt log data","Contributor Insights analyses log data to identify the top contributors impacting system performance."
"Which of the following is a valid CloudWatch metric dimension for an EC2 instance?","InstanceId","LogStreamName","FunctionName","TableName","InstanceId is a standard dimension for EC2 instance metrics, allowing you to filter metrics by a specific instance."
"Which of the following is NOT a function of AWS CloudWatch?","Infrastructure Provisioning","Monitoring AWS resources","Troubleshooting application errors","Analysing log data","Infrastructure Provisioning is NOT a function of AWS CloudWatch"
"You want to create a CloudWatch Alarm that triggers when the average CPU utilisation of your EC2 instance exceeds 70% for 5 consecutive minutes. Which evaluation period and evaluation periods must be set?","Evaluation Period: 1 minute, Evaluation Periods: 5","Evaluation Period: 5 minute, Evaluation Periods: 1","Evaluation Period: 1 minute, Evaluation Periods: 70","Evaluation Period: 5 minute, Evaluation Periods: 70","Setting Evaluation Period to 1 Minute and Evaluation Periods to 5, the Alarm is triggered when average CPU utilization exceeds 70% for 5 consecutive minutes."
"How can you monitor the custom metrics that are not directly provided by AWS services?","Use the PutMetricData API to publish custom metrics","Use CloudTrail to record custom events","Use VPC Flow Logs to capture network traffic","Use AWS Config to track resource changes","The PutMetricData API allows you to publish custom metrics to CloudWatch, providing flexibility to monitor application-specific data."
"What is the default aggregation period for metrics displayed on a CloudWatch dashboard if you don't specify one?","5 minutes","1 minute","1 hour","1 day","The default aggregation period for metrics displayed on a CloudWatch dashboard is 5 minutes."
"You have configured CloudWatch Logs Insights to query your application logs. What language is used to write the queries?","CloudWatch Logs Insights Query Language","SQL","Python","JSON","CloudWatch Logs Insights uses its own query language specifically designed for analysing log data."
"You need to ensure that all CloudWatch Logs from your AWS Lambda functions are encrypted. What is the easiest way to achieve this?","Use KMS encryption when creating the Lambda function's log group","Encrypt the log data at the application level","Enable encryption on the Lambda function","Enable encryption on the CloudWatch Logs dashboard","AWS Lambda automatically uses KMS encryption when you specify a KMS key during the creation of the log group."
"When setting up CloudWatch Alarms, what does the 'Treat missing data as' option allow you to configure?","How the alarm behaves when data is missing","How missing data is logged","How missing data is visualised","How missing data is stored","The 'Treat missing data as' option allows you to configure how the alarm behaves when data points are missing, such as treating them as 'notBreaching', 'breaching', 'ignore', or 'missing'."
"You want to receive notifications when a specific error occurs in your application. Which action should you configure on a CloudWatch Alarm?","Send an SNS notification","Trigger an Auto Scaling policy","Restart an EC2 instance","Create a CloudWatch Dashboard","SNS notifications are commonly used to alert you when an alarm's state changes, indicating that a threshold has been breached or a specific event has occurred."
"What is the main advantage of using CloudWatch over traditional monitoring tools?","Seamless integration with other AWS services","Lower cost","Increased security","Faster performance","CloudWatch integrates seamlessly with other AWS services, providing a unified monitoring solution across your AWS environment."
"Which of the following is NOT a valid statistic for a CloudWatch Metric Math expression?","SUM","AVG","STDDEV","MEDIAN","MEDIAN is not a supported statistic for CloudWatch Metric Math. Supported statistics include SUM, AVG, MIN, MAX, SAMPLECOUNT, STDDEV, and percentile functions."
"You are using CloudWatch to monitor your RDS database. Which metric is most useful for identifying potential performance bottlenecks related to database disk I/O?","DiskQueueDepth","CPUUtilization","FreeableMemory","DatabaseConnections","DiskQueueDepth provides insight into the number of I/O requests waiting to be serviced by the disk, indicating potential bottlenecks."
"What is the purpose of the CloudWatch cross-account observability?","Monitoring resources in multiple AWS accounts from a central account","Creating backups across multiple AWS accounts","Sharing logs across multiple AWS accounts","Managing security across multiple AWS accounts","CloudWatch cross-account observability enables monitoring resources and applications spread across different AWS accounts from a central management account."
"Which Amazon CloudWatch feature allows you to visualise, monitor, and manage metrics and logs in a unified view?","CloudWatch Dashboards","CloudWatch Events","CloudWatch Logs Insights","CloudWatch Alarms","CloudWatch Dashboards provide a customisable view of your CloudWatch data, allowing you to visualise metrics and logs in a single pane of glass."
"What is the purpose of the Amazon CloudWatch Logs agent?","To collect log data from EC2 instances and send it to CloudWatch Logs","To automatically scale EC2 instances","To manage IAM roles","To monitor network traffic","The CloudWatch Logs agent is designed to collect log data from your EC2 instances and stream it to CloudWatch Logs for centralised storage and monitoring."
"Which Amazon CloudWatch feature allows you to automatically react to changes in your AWS environment, such as triggering an AWS Lambda function?","CloudWatch Events (EventBridge)","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch ServiceLens","CloudWatch Events (now EventBridge) enables you to create rules that match incoming events and route them to one or more target actions, such as triggering a Lambda function."
"In Amazon CloudWatch, what is the purpose of a 'metric filter'?","To extract metric data from log events.","To filter CloudWatch alarms.","To create custom dashboards.","To manage access control to metrics.","Metric filters allow you to define patterns to search for within log data and extract numerical metric values based on those patterns."
"Which Amazon CloudWatch feature can be used to proactively monitor the availability and response time of your web applications?","CloudWatch Synthetics","CloudWatch RUM","CloudWatch Application Insights","CloudWatch Metrics","CloudWatch Synthetics enables you to create canaries, configurable scripts that run on a schedule, to monitor your application endpoints and APIs and alert you to issues."
"How does Amazon CloudWatch Anomaly Detection help in monitoring?","By identifying unexpected patterns in metrics.","By creating static thresholds for alarms.","By predicting future resource usage.","By automatically fixing performance issues.","CloudWatch Anomaly Detection uses machine learning algorithms to learn the typical patterns of your metrics and identify anomalies based on those patterns."
"What is the primary function of Amazon CloudWatch Logs Insights?","To query and analyse log data using a purpose-built query language.","To create static dashboards.","To manage EC2 instances.","To configure security groups.","CloudWatch Logs Insights allows you to interactively search and analyse your log data using a powerful query language, enabling you to quickly troubleshoot issues."
"Which metric in Amazon CloudWatch is useful for monitoring the CPU usage of an EC2 instance?","CPUUtilization","DiskSpaceUtilization","MemoryUtilization","NetworkUtilization","The CPUUtilization metric provides information about the percentage of CPU being used by an EC2 instance."
"When setting up an Amazon CloudWatch Alarm, what is a 'statistic'?","A function that aggregates metric data over a specified period.","A list of EC2 instance IDs.","A predefined threshold for the alarm.","A user-defined label for the alarm.","A statistic is a function (e.g., Average, Minimum, Maximum) that is applied to the metric data over a specified period to determine the alarm's state."
"What is the retention period for detailed (one-minute resolution) metrics in Amazon CloudWatch?","15 months","3 months","7 days","30 days","Amazon CloudWatch retains detailed (one-minute resolution) metrics for 15 months."
"In Amazon CloudWatch, what is the primary purpose of a 'Metric Filter'?","To extract metric data from log events.","To define alarms based on metric values.","To create dashboards from collected data.","To encrypt log data.","Metric filters allow you to define patterns to match in log events and extract numeric metric values from them, which can then be used for alarms or dashboards."
"Which Amazon CloudWatch feature allows you to visualise metrics and logs in a unified view?","CloudWatch Dashboards","CloudWatch Alarms","CloudWatch Logs Insights","CloudWatch Events (EventBridge)","CloudWatch Dashboards provide a customisable visual representation of your AWS resources, allowing you to monitor and troubleshoot applications and services in real-time."
"You want to receive notifications when the CPU utilisation of an EC2 instance exceeds 80%. Which CloudWatch feature should you use?","CloudWatch Alarms","CloudWatch Logs","CloudWatch Metrics","CloudWatch Events (EventBridge)","CloudWatch Alarms allow you to monitor metrics and receive notifications when specified thresholds are breached."
"What is the purpose of 'CloudWatch Logs Insights'?","To perform interactive log analytics on log data in CloudWatch Logs.","To automatically back up log data to S3.","To encrypt log data at rest.","To compress log data to reduce storage costs.","CloudWatch Logs Insights enables you to interactively search and analyse your log data in CloudWatch Logs, allowing you to identify patterns, troubleshoot issues, and gain insights into your applications."
"How can you send custom metrics to Amazon CloudWatch?","Using the AWS CLI or SDK to call the PutMetricData API.","By directly writing to CloudWatch Logs.","By configuring an EC2 instance to automatically send metrics.","By using CloudWatch Events (EventBridge) to capture metric data.","The PutMetricData API allows you to send custom metrics from your applications or services to CloudWatch, enabling you to monitor custom performance indicators."
"You need to monitor the number of errors occurring in your application logs. Which CloudWatch feature is most suitable for this?","CloudWatch Metric Filters on the logs.","CloudWatch Anomaly Detection.","CloudWatch Contributor Insights.","CloudWatch ServiceLens.","Metric filters can be configured to identify error patterns in your logs and create a corresponding metric, allowing you to monitor the error rate over time."
"What does the Amazon CloudWatch 'Namespace' represent?","A container for CloudWatch metrics, typically representing a service or application.","A specific configuration for CloudWatch Alarms.","A data retention policy for CloudWatch Logs.","A security group for controlling access to CloudWatch data.","A namespace is a container for CloudWatch metrics. Metrics in different namespaces are isolated from each other, so you can monitor related metrics for different applications or services without accidentally aggregating data."
"What is the function of 'CloudWatch Contributor Insights'?","To analyse log data and identify the top contributors impacting system performance.","To detect anomalies in metric data and provide alerts.","To collect and aggregate metrics from multiple AWS accounts.","To automatically scale AWS resources based on metric data.","Contributor Insights analyzes log data to create time-series data showing the top contributors influencing system performance, helping identify and isolate performance bottlenecks."
"Which action can you take to reduce the cost of storing logs in Amazon CloudWatch Logs?","Configure a shorter retention period for log groups.","Disable CloudWatch Logs entirely.","Increase the size of the CloudWatch Logs buffer.","Enable CloudWatch Alarms.","Reducing the retention period for log groups will cause older logs to be automatically deleted, reducing the overall storage costs."
"In Amazon CloudWatch, what is the use case for 'CloudWatch Anomaly Detection'?","To automatically detect unusual patterns in metrics and trigger alarms.","To automatically fix errors in log data.","To automatically encrypt log data.","To automatically back up metric data.","CloudWatch Anomaly Detection uses machine learning algorithms to learn the normal behavior of metrics and automatically detect anomalies, triggering alarms when unexpected deviations occur."
"Which CloudWatch feature allows you to visualise metrics and logs from your AWS resources in a single pane of glass?","CloudWatch Dashboards","CloudWatch Alarms","CloudWatch Events","CloudWatch Logs Insights","CloudWatch Dashboards provide a customisable visualisation of metrics and logs, allowing you to monitor the health and performance of your AWS resources."
"What is the primary function of CloudWatch Alarms?","To trigger actions based on metric thresholds","To collect and store log data","To define custom metrics","To discover and monitor AWS resources","CloudWatch Alarms monitor metrics and trigger actions, such as sending notifications or scaling resources, when specified thresholds are breached."
"What does CloudWatch Logs Insights allow you to do?","Analyse log data using a query language","Create real-time dashboards","Automate infrastructure provisioning","Manage IAM roles","CloudWatch Logs Insights allows you to interactively search and analyse your log data using a dedicated query language."
"Which CloudWatch feature can you use to automatically respond to state changes in your AWS resources?","CloudWatch Events (now EventBridge)","CloudWatch Metrics","CloudWatch Logs","CloudWatch Synthetics","CloudWatch Events (now EventBridge) allows you to create rules that trigger actions based on changes in the state of your AWS resources."
"What is the purpose of CloudWatch Metric Filters?","To extract specific information from log data to create metrics","To encrypt log data","To define retention policies for logs","To archive log data to S3","Metric Filters allow you to extract specific information from your log data and create CloudWatch metrics based on that information."
"You want to be notified when the CPU utilisation of your EC2 instance exceeds 80%. How can you achieve this using CloudWatch?","Create a CloudWatch Alarm","Create a CloudWatch Dashboard","Create a CloudWatch Metric Filter","Create a CloudWatch Log Group","CloudWatch Alarms are used to monitor metrics and trigger notifications when a specified threshold is breached."
"Which of the following is NOT a supported action that can be triggered by a CloudWatch Alarm?","Executing an AWS Lambda function","Sending an SNS notification","Automatically rebooting an EC2 instance","Modifying IAM policies","CloudWatch Alarms can trigger actions such as sending SNS notifications, executing Lambda functions, or performing Auto Scaling actions, but not modifying IAM policies."
"What is the retention period for CloudWatch Metrics data?","CloudWatch retains metric data at various resolutions (1-minute, 5-minute, 1-hour) indefinitely.","30 days","90 days","1 year","CloudWatch retains metric data at various resolutions indefinitely. Details can be found in AWS documentation."
"You want to monitor the latency of your application's API endpoints. Which CloudWatch feature can you use to proactively test and monitor these endpoints?","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch ServiceLens","CloudWatch Embedded Metric Format","CloudWatch Synthetics allows you to create canaries that simulate user traffic to proactively monitor the availability and latency of your API endpoints."
"In CloudWatch, what is the function of the 'namespace'?","To categorise metrics","To define alarm thresholds","To store log data","To define event patterns","A namespace is a container for CloudWatch metrics. Metrics in different namespaces are isolated from each other, even if they have the same metric names."
"In Amazon CloudWatch, what is the primary function of a metric?","To represent a time-ordered set of data points","To define an alarm threshold","To configure log filtering","To create a dashboard","A CloudWatch metric represents a time-ordered set of data points that are published to CloudWatch. Metrics are the fundamental concept in CloudWatch."
"What does the term 'namespace' refer to in the context of Amazon CloudWatch metrics?","A container for CloudWatch metrics","A grouping of CloudWatch alarms","A set of CloudWatch dashboards","A type of CloudWatch log group","A namespace is a container for CloudWatch metrics. Metrics in different namespaces are isolated from each other."
"Which Amazon CloudWatch feature allows you to automatically react to changes in the state of your AWS resources?","CloudWatch Alarms","CloudWatch Logs Insights","CloudWatch Events (EventBridge)","CloudWatch Dashboards","CloudWatch Alarms allow you to set thresholds on metrics and trigger actions when those thresholds are breached."
"You want to analyse the logs generated by your AWS Lambda function. Which Amazon CloudWatch service is best suited for this?","CloudWatch Logs","CloudWatch Metrics","CloudWatch Alarms","CloudWatch Synthetics","CloudWatch Logs is designed for collecting, monitoring, and analysing log data from various AWS resources, including Lambda functions."
"Which Amazon CloudWatch feature can be used to monitor the latency and availability of your application's endpoints?","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch Contributor Insights","CloudWatch ServiceLens","CloudWatch Synthetics allows you to create canaries to monitor your application's endpoints and APIs, providing insights into latency and availability."
"What is the purpose of CloudWatch Logs Insights?","To query and analyse log data in real-time","To visualise metrics data","To create custom dashboards","To configure alarm notifications","CloudWatch Logs Insights enables you to interactively search and analyse your log data in CloudWatch Logs."
"Which AWS service can be integrated with Amazon CloudWatch to send notifications when an alarm state changes?","Amazon SNS","Amazon SQS","AWS Lambda","Amazon CloudFront","Amazon SNS (Simple Notification Service) is commonly used with CloudWatch Alarms to send notifications via email, SMS, or other methods when an alarm state changes."
"In Amazon CloudWatch, what is the purpose of metric filters?","To extract metric data from log events","To filter out irrelevant log events","To create custom dashboards","To configure alarm thresholds","Metric filters are used to extract metric data from log events. They allow you to transform log data into numerical metrics that can be monitored."
"What type of monitoring can be performed using Amazon CloudWatch RUM (Real User Monitoring)?","End-user experience and performance","Server-side CPU utilization","Network bandwidth consumption","Database query performance","CloudWatch RUM focuses on collecting and analysing data from real users' interactions with your web applications, providing insights into their experience and performance."
"You need to visualise the CPU utilisation of multiple EC2 instances in a single graph. Which Amazon CloudWatch feature allows you to achieve this?","CloudWatch Dashboards","CloudWatch Logs Insights","CloudWatch Alarms","CloudWatch ServiceLens","CloudWatch Dashboards allow you to create customisable dashboards to visualise metrics and logs from various AWS resources, including multiple EC2 instances' CPU utilisation."
"What is the primary function of Amazon CloudWatch?","Monitoring and observability of AWS resources and applications","Data warehousing and analytics","Content delivery and caching","Serverless compute execution","CloudWatch provides monitoring and observability, collecting metrics, logs, and events to provide insights into resource and application behaviour."
"Which CloudWatch feature allows you to visualise metrics in a graphical format?","CloudWatch Dashboards","CloudWatch Events","CloudWatch Logs Insights","CloudWatch Alarms","CloudWatch Dashboards provide a customisable interface for visualising metrics from various AWS services and applications."
"What type of data can be stored and monitored using CloudWatch Logs?","Application logs, system logs, and custom logs","SQL databases, NoSQL databases, and data warehouses","Image files, video files, and audio files","Executable files, configuration files, and text files","CloudWatch Logs allows users to store and monitor various types of log data, including application, system, and custom logs."
"Which CloudWatch feature allows you to automatically respond to changes in your AWS environment?","CloudWatch Events (now EventBridge)","CloudWatch Synthetics","CloudWatch Anomaly Detection","CloudWatch ServiceLens","CloudWatch Events (now EventBridge) enables automated responses to state changes in AWS resources, triggering actions based on defined rules."
"How does CloudWatch Anomaly Detection help in monitoring metrics?","By automatically identifying unusual metric behaviour","By providing real-time virus scanning","By automatically patching operating systems","By performing automatic code reviews","CloudWatch Anomaly Detection automatically learns the typical behaviour of metrics and identifies deviations from the baseline, helping to detect anomalies."
"Which CloudWatch feature can be used to proactively monitor the availability and response time of your applications from different geographic locations?","CloudWatch Synthetics","CloudWatch Metric Streams","CloudWatch Embedded Metric Format","CloudWatch RUM","CloudWatch Synthetics allows you to create canaries that simulate user traffic to proactively monitor the availability and response time of applications."
"What is the purpose of CloudWatch Logs Insights?","To query and analyse log data using a purpose-built query language","To create static backups of log data","To encrypt log data at rest","To forward log data to external systems","CloudWatch Logs Insights provides a query language to efficiently analyse log data stored in CloudWatch Logs, enabling faster troubleshooting."
"What is the retention policy for custom metrics published to CloudWatch if you do not explicitly set one?","CloudWatch retains metric data indefinitely","CloudWatch retains metric data for 30 days","CloudWatch retains metric data for 90 days","CloudWatch retains metric data for 1 year","CloudWatch retains metric data indefinitely, even custom metrics."
"Which CloudWatch agent is used to collect metrics and logs from EC2 instances?","CloudWatch Agent","Systems Manager Agent","CodeDeploy Agent","Inspector Agent","The CloudWatch Agent is specifically designed for collecting metrics and logs from EC2 instances and on-premises servers."
"You want to send custom metrics to CloudWatch from your application. What API call should you use?","PutMetricData","GetMetricData","DescribeMetrics","ListMetrics","The `PutMetricData` API call allows you to send custom metrics to CloudWatch from your applications."