"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Organizations, what is the primary benefit of using Service Control Policies (SCPs)?","Centralized control over AWS services and actions allowed in member accounts.","Enhanced network security across all accounts.","Automated cost optimization recommendations for member accounts.","Simplified billing and cost allocation for individual users.","SCPs allow administrators to define guardrails, enforcing permissions across all member accounts within an organization. This provides centralized control and helps ensure compliance."
"Which of the following is a key function of the AWS Organizations service?","Consolidating billing and managing access across multiple AWS accounts.","Automating the deployment of EC2 instances.","Managing DNS records for your domain.","Creating and managing VPCs across regions.","AWS Organizations simplifies the management of multiple AWS accounts by allowing you to organize them into organizational units (OUs), consolidate billing, and apply policies across the entire organization."
"What is an Organizational Unit (OU) in AWS Organizations used for?","Grouping AWS accounts for management and policy application.","Defining the geographical region for resources.","Controlling access to individual S3 buckets.","Automating the deployment of CloudFormation templates.","OUs enable you to logically group AWS accounts together, making it easier to apply policies (like SCPs) and manage them as a unit."
"How does Consolidated Billing in AWS Organizations benefit your organization?","It simplifies billing and cost allocation across all member accounts.","It automatically provisions resources in all regions.","It provides real-time monitoring of all AWS services.","It ensures 100% uptime for all applications.","Consolidated billing combines the usage from all member accounts in the organization to determine volume discounts, making it easier to track and allocate costs across the organisation."
"Which AWS Organizations feature helps you manage and govern your AWS environment at scale?","Service Control Policies (SCPs)","AWS Config Rules","CloudTrail Logs","Trusted Advisor Recommendations","SCPs are a key feature for managing and governing an AWS environment at scale by defining the allowed and denied actions for AWS accounts within an organization."
"What is the root account in AWS Organizations?","The top-level account in an organisation, representing the management account.","The account used for deploying EC2 instances.","The account used for storing CloudTrail logs.","The account used for managing IAM users and roles.","The root account is the master or management account within an AWS organization. This is the starting point for managing all other member accounts."
"Which AWS service integrates with AWS Organizations to provide a centralized view of security findings across all member accounts?","AWS Security Hub","Amazon Inspector","AWS Shield","AWS WAF","AWS Security Hub integrates with Organizations to aggregate and prioritise security findings from multiple AWS services and member accounts, providing a single view of your security posture."
"What is the primary purpose of tagging AWS resources in an AWS Organizations environment?","Enabling cost allocation and resource management across accounts.","Improving application performance.","Enhancing network security.","Simplifying the deployment of new services.","Tagging resources with consistent tags makes it possible to track costs and manage resources across different accounts and OUs within an organization."
"When moving an existing AWS account into an AWS Organization, what must be considered?","The account must be invited and accept the invitation.","The account must be a newly created account.","The account must have no running resources.","The account must be in the same region as the management account.","Existing accounts need to be invited and must accept the invitation to join the AWS Organization. This ensures that the account owner is aware of and agrees to the account being managed by the organization."
"What is a common use case for delegating administrative control to member accounts within an AWS Organization?","Enabling independent resource management and service deployments.","Restricting access to all AWS services.","Automating security patching.","Centralising all billing and cost allocation.","Delegating administrative control allows member accounts to manage their own resources and services within the boundaries defined by SCPs, promoting agility and flexibility."
"In AWS Organizations, what is the effect of an SCP with a 'Deny' statement on an IAM user's permissions?","It explicitly denies the permission, overriding any 'Allow' statements.","It has no effect on the user's permissions.","It implicitly allows the permission.","It temporarily suspends the user's access.","A 'Deny' statement in an SCP explicitly prevents the specified action, regardless of any 'Allow' statements in the IAM policy. This is a powerful way to enforce restrictions."
"What is the purpose of the AWS Organizations feature 'Tag Policies'?","To enforce consistent tagging conventions across all AWS resources within an organization.","To automatically provision resources with pre-defined tags.","To restrict access to resources based on their tags.","To generate cost reports based on resource tags.","Tag Policies allow administrators to enforce tagging conventions across all AWS resources in the organization. This helps to ensure consistent tagging, which is important for cost allocation and resource management."
"Which statement is true about the relationship between AWS Organizations and IAM?","AWS Organizations manages accounts, while IAM manages users and roles within those accounts.","IAM manages accounts, while AWS Organizations manages users and roles.","AWS Organizations and IAM are interchangeable services.","AWS Organizations replaces IAM.","AWS Organizations deals with management of accounts and the policies that govern those accounts, while IAM deals with the identities (users, groups, roles) and permissions within an individual AWS account."
"When should you consider using AWS Organizations instead of managing individual AWS accounts independently?","When you need to centrally manage and govern multiple AWS accounts.","When you only have a single AWS account.","When you want to optimise the performance of a single application.","When you need to migrate a large number of on-premises servers.","Organizations is ideal when you have multiple accounts and need centralized control over them, for things like billing, security, and compliance. Independent management is suitable for simple scenarios with few accounts."
"What is the recommended best practice for enabling AWS CloudTrail in an AWS Organizations environment?","Enable CloudTrail in the management account and configure it to log events from all member accounts.","Enable CloudTrail in each member account separately.","Disable CloudTrail entirely for improved performance.","Enable CloudTrail only in the root account.","Enabling CloudTrail in the management account and configuring it to log events from all member accounts provides a centralized audit trail for the entire organization, improving security and compliance."
"In AWS Organizations, what happens to the resources in a member account if the account is removed from the organization?","The resources remain in the account, but the account is no longer managed by the organization.","The resources are automatically transferred to the management account.","The resources are automatically deleted.","The resources are moved to a new, unmanaged account.","When an account is removed, the resources remain in the account and operate as they did before, however the management account no longer has control over it."
"What role does the 'management account' play in an AWS Organization?","It is the central account used to create and manage the organisation.","It is the account that hosts all the applications.","It is the account used for storing all CloudTrail logs.","It is the account used for managing all IAM users and roles.","The management account is the central point for creating the organization, inviting other accounts, and applying policies to the member accounts."
"How can you use AWS Organizations to enforce PCI DSS compliance across your AWS accounts?","By creating SCPs that restrict actions that are not compliant with PCI DSS.","By automatically encrypting all data at rest.","By deploying a pre-configured PCI DSS compliant environment.","By providing a certification for PCI DSS compliance.","SCPs can be designed to restrict actions that would violate PCI DSS requirements, such as disabling public S3 buckets or enforcing encryption, thereby ensuring compliance across the organization."
"Which AWS Organizations feature can help you automate the process of creating new AWS accounts within your organization?","AWS Control Tower Account Factory","AWS CloudFormation StackSets","AWS Systems Manager Automation","AWS Lambda Function","AWS Control Tower's Account Factory is designed to automate the provisioning of new AWS accounts configured to meet your organisation's security and compliance requirements."
"What is the relationship between AWS Organizations and AWS Control Tower?","AWS Control Tower automates the setup of AWS Organizations and provides ongoing governance.","AWS Organizations replaces AWS Control Tower.","AWS Control Tower manages IAM users and roles within AWS Organizations.","AWS Organizations automates the deployment of resources in AWS Control Tower.","Control Tower builds upon Organizations by automating the creation of a landing zone, providing pre-configured guardrails, and automating account provisioning, simplifying the initial setup and ongoing management of a secure multi-account environment."
"In AWS Organizations, what is the purpose of an 'AWS Account Alias'?","To create a human-readable name for the AWS account ID.","To encrypt the AWS account ID.","To create a temporary password for the AWS account.","To create a duplicate account.","An account alias provides a user-friendly name for your AWS account, making it easier for users to remember and use when logging into the AWS Management Console."
"Which AWS service can be used to automatically remediate non-compliant resources detected by AWS Config in an AWS Organizations environment?","AWS Systems Manager Automation","AWS CloudFormation StackSets","AWS Trusted Advisor","AWS CloudWatch Events","AWS Systems Manager Automation, in conjunction with AWS Config, can be used to automatically remediate non-compliant resources based on Config rules. This helps ensure continuous compliance across the organization."
"What is a common use case for AWS Organizations in a development and testing environment?","Isolating development, testing, and production environments into separate AWS accounts.","Sharing resources between development and production environments.","Creating a single large AWS account for all environments.","Limiting access to AWS services.","Creating separate accounts for development, testing, and production environments allows for better isolation and prevents accidental changes in one environment from affecting others."
"In AWS Organizations, how can you ensure that all new AWS accounts created in your organization automatically inherit a specific set of IAM roles?","By using AWS CloudFormation StackSets to deploy the IAM roles to all accounts.","By manually creating the IAM roles in each account.","By using Service Control Policies (SCPs) to enforce the creation of the IAM roles.","By using AWS Trusted Advisor to recommend IAM roles.","CloudFormation StackSets are an effective way to deploy resources like IAM roles to multiple accounts within an organization, ensuring consistency across all accounts."
"What is a potential drawback of using overly restrictive SCPs in AWS Organizations?","They can unintentionally block legitimate actions required for application functionality.","They can increase the cost of using AWS services.","They can decrease the performance of applications.","They can make it easier to bypass security controls.","Overly restrictive SCPs can prevent users from performing necessary tasks, potentially disrupting application functionality. It is important to carefully design SCPs to balance security and usability."
"How does AWS Organizations help with meeting compliance requirements such as HIPAA or GDPR?","By providing a centralized platform for managing security controls and access across multiple accounts.","By automatically encrypting all data stored in AWS.","By generating compliance reports automatically.","By providing legal advice on compliance matters.","Organizations helps ensure compliance by providing centralised control over services, regions and configurations that can be managed with SCPs in accordance with governance and compliance requirements."
"In AWS Organizations, what is the purpose of a 'delegated administrator' account?","To allow a member account to manage specific AWS services or tasks on behalf of the organization.","To restrict access to all AWS services in a member account.","To automatically approve all AWS service requests.","To create new AWS accounts within the organization.","A delegated administrator account allows specific member accounts to manage certain AWS services or tasks, reducing the burden on the management account and promoting a more distributed management model."
"What is the maximum number of AWS accounts that can be part of an AWS Organization?","There is a soft limit that can be increased by contacting AWS Support.","20","100","Unlimited","The number of accounts is limited but is a soft limit that can be increased by contacting AWS Support."
"Which of the following is NOT a capability of AWS Organizations?","Automated patching of operating systems on EC2 instances.","Consolidated billing for all linked accounts.","Centralized management of security policies.","Hierarchical grouping of accounts into organizational units.","AWS Organizations focuses on account management and governance, not on operating system patching."
"You want to ensure that users in your member accounts cannot create EC2 instances in a specific region. How can you achieve this using AWS Organizations?","Create an SCP that denies the `ec2:RunInstances` action for the specified region.","Configure IAM policies in each member account to deny the action.","Use AWS Config to detect and remediate instances created in the region.","Enable AWS Trusted Advisor to provide recommendations for region selection.","SCPs are the best way to centrally enforce restrictions across all member accounts. Denying the `ec2:RunInstances` action for a specific region in an SCP will prevent users in those accounts from creating EC2 instances in that region."
"Which AWS service can you use to share resources such as VPCs and subnets across multiple AWS accounts within your AWS Organization?","AWS Resource Access Manager (RAM)","AWS CloudFormation StackSets","AWS Systems Manager Parameter Store","AWS Service Catalog","AWS Resource Access Manager (RAM) allows you to share resources such as VPCs, subnets, and transit gateways across accounts within your organization, simplifying resource management and promoting reusability."
"What is the primary benefit of using AWS Organizations with AWS Single Sign-On (SSO)?","Centralized management of user access to multiple AWS accounts.","Automated creation of IAM users and roles.","Simplified billing and cost allocation.","Enhanced network security.","SSO integrates with Organizations to provide a centralized way to manage user access to multiple AWS accounts, eliminating the need to create and manage IAM users in each individual account."
"How can you use AWS Organizations to implement a least privilege access strategy across your AWS accounts?","By creating SCPs that restrict permissions to only what is necessary for each account's function.","By granting full administrator access to all accounts.","By disabling IAM entirely.","By using AWS Trusted Advisor to recommend IAM policies.","SCPs allow you to define fine-grained permission restrictions, ensuring that users and roles in each account only have the access they need, adhering to the principle of least privilege."
"You need to generate a consolidated cost and usage report for all AWS accounts in your organization. Where can you find this report?","In the management account's AWS Cost Management console.","In each member account's AWS Cost Management console.","In the AWS CloudTrail logs.","In the AWS Trusted Advisor console.","The consolidated cost and usage report is available in the AWS Cost Management console of the management account, providing a single view of spending across the entire organization."
"What is a key difference between SCPs and IAM policies?","SCPs apply to all accounts within an organization or OU, while IAM policies apply to individual users, groups, or roles within an account.","IAM policies apply to all accounts within an organization or OU, while SCPs apply to individual users, groups, or roles within an account.","SCPs are used for authentication, while IAM policies are used for authorisation.","SCPs are used for cost optimisation, while IAM policies are used for security.","SCPs are organisation-level policies that affect all entities within the accounts or OUs to which they are applied, while IAM policies are account-level and apply to specific identities."
"How can you prevent users from deleting critical resources in your AWS Organization using SCPs?","By denying the `Delete` actions for those resources in the SCP.","By enabling deletion protection on each resource individually.","By requiring multi-factor authentication for all deletion requests.","By using AWS Trusted Advisor to monitor resource deletions.","SCPs can be used to deny specific actions, like `Delete`, preventing users from accidentally or maliciously deleting critical resources."
"Which AWS Organizations feature can help you ensure that all AWS accounts in your organization are created with a consistent set of baseline configurations?","AWS Control Tower Account Factory","AWS CloudFormation StackSets","AWS Systems Manager Automation","AWS Config Rules","AWS Control Tower's Account Factory automates the creation of new accounts with pre-defined baseline configurations, ensuring consistency across your organization."
"You have an existing AWS account that you want to move into your AWS Organization. What is the first step you need to take?","Send an invitation from the management account to the existing account.","Create a new IAM user in the existing account with administrator privileges.","Terminate all resources in the existing account.","Change the email address associated with the existing account.","To bring an account into an Organization, it must be invited by the management account."
"What is the benefit of using multiple OUs within your AWS Organization?","To create a hierarchical structure for organising accounts and applying policies.","To improve network performance.","To reduce the cost of AWS services.","To simplify IAM user management.","OUs allow you to group accounts together logically, enabling you to apply different policies to different groups of accounts based on their purpose or security requirements."
"How can you ensure that all new S3 buckets created in your AWS Organization are automatically encrypted at rest?","By creating an SCP that requires server-side encryption for all S3 bucket creation requests.","By enabling default encryption on the S3 service globally.","By using AWS CloudTrail to monitor S3 bucket creation and automatically encrypt them.","By using AWS Trusted Advisor to recommend encryption settings.","SCPs allow you to enforce encryption requirements for S3 bucket creation, ensuring that all new buckets are automatically encrypted at rest."
"You need to grant a third-party vendor access to specific resources in one of your AWS accounts within your AWS Organization. What is the recommended approach?","Create an IAM role in the account and grant the vendor access to that role using cross-account access.","Share your AWS account credentials with the vendor.","Add the vendor's IAM user to your AWS Organization.","Use AWS Organizations to create a separate AWS account for the vendor.","Using IAM roles with cross-account access is the most secure way to grant third-party vendors access to specific resources in your AWS account without sharing your credentials."
"What is the relationship between AWS Organizations and AWS IAM Identity Center (successor to AWS SSO)?","AWS IAM Identity Center (successor to AWS SSO) integrates with AWS Organizations to provide centralized access management across all accounts.","AWS Organizations replaces AWS IAM Identity Center (successor to AWS SSO).","AWS Organizations manages IAM users and roles, while AWS IAM Identity Center (successor to AWS SSO) manages accounts.","AWS Organizations is a prerequisite for using AWS IAM Identity Center (successor to AWS SSO).","AWS IAM Identity Center (successor to AWS SSO) integrates with Organizations to provide Single Sign-On access to all your accounts from a single place."
"What is the maximum depth of Organizational Units (OUs) allowed within an AWS Organization?","Five","Three","Ten","Unlimited","The maximum depth of OU nesting is five, allowing for flexibility while maintaining a manageable hierarchy."
"What is a typical use-case for SCPs involving region restriction?","Ensuring compliance with data residency requirements by restricting resource deployment to specific AWS Regions.","Accelerating application performance by forcing resources to be created closer to users.","Minimizing the cost of cloud resources by consolidating services in fewer regions.","Simplifying resource management by limiting the number of regions to monitor.","Region restriction via SCPs is a key method to enforce data residency compliance by controlling where data can be stored and processed."
"How does AWS Organizations facilitate multi-account strategy for security best practices?","By enabling centralised security policy enforcement, account isolation, and streamlined security auditing across all accounts.","By automatically installing security software on EC2 instances across the organization.","By providing a single point of contact for all security-related issues.","By completely automating all security tasks, eliminating the need for human intervention.","Organisations supports a robust multi-account security strategy by centralising policy and streamlining auditing."
"If a user has conflicting permissions granted by an IAM policy and an SCP, which policy takes precedence when the SCP has an explicit deny?","The SCP deny overrides any allow in IAM policy.","The IAM policy always takes precedence.","The policy that was created first takes precedence.","The policy with the most specific resource ARN takes precedence.","SCPs serve as guardrails that define the maximum permissions available, and explicit denies always take precedence, regardless of IAM."
"Which of the following is an advantage of using AWS Organizations to manage multiple AWS accounts for different environments (e.g., Development, Staging, Production)?","Enhanced isolation between environments, reducing the risk of accidental changes or security breaches impacting other environments.","Automatic resource scaling across all environments for optimal performance.","Simplified network configuration across all environments.","Reduced costs for AWS support services.","Isolating environments in separate accounts greatly reduces the risk of unintentional or malicious cross-environment impact."
"In AWS Organizations, what is the primary benefit of using Service Control Policies (SCPs)?","To centrally manage permissions across all accounts","To enforce multi-factor authentication on all accounts","To automate patching of EC2 instances","To manage billing across accounts","SCPs allow you to define guardrails and control the AWS services and actions that can be accessed by accounts within your organisation, helping to ensure compliance and security."
"What is the function of the AWS Organizations 'Management Account'?","It's the central point for billing and governance of the organisation","It automatically backs up data from all member accounts","It provides free AWS Support for all accounts in the organisation","It isolates all workloads for better security","The Management Account is used to create the AWS Organization and centrally manage billing, access, and governance across all member accounts."
"When moving an existing AWS account into an AWS Organization, what is a prerequisite?","The account must not have any IAM users or roles","The account must not have any active AWS services running","The account must be invited and accept the invitation","The account must be in the same AWS region as the Management Account","An existing AWS account must be invited to join the organization, and the invitation must be accepted by the account owner.  This ensures the owner consents to the transfer of control."
"What is the purpose of Organizational Units (OUs) in AWS Organizations?","To group accounts for simplified management and policy application","To automatically create IAM roles in member accounts","To isolate network traffic between accounts","To provide different levels of AWS Support to different accounts","OUs allow you to logically group accounts together, enabling you to apply policies and manage permissions at the OU level, which simplifies management across a large number of accounts."
"Which of the following is NOT a valid use case for AWS Organizations?","Centralised billing management","Cross-account resource sharing","Enforcing compliance policies","Automated deployment of EC2 instances","AWS Organizations provides centralised billing, cross-account resource sharing and enforcing compliance policies but does not directly automate EC2 deployments (though it can facilitate such deployments via other AWS services)."
"How can you grant cross-account access using AWS Organizations?","By creating IAM roles in each member account and trusting the Management Account","By sharing AWS IAM credentials between accounts","By creating SCPs that allow cross-account access","By enabling cross-account logging in CloudTrail","IAM roles with trust policies that allow other accounts to assume the role is the standard mechanism for granting cross-account access. This approach avoids sharing credentials."
"What is the effect of a Deny statement in an SCP?","It prevents the specified actions from being performed, even if allowed by IAM policies","It grants the specified actions only within the OU","It has no effect if an IAM policy allows the action","It allows the specified actions only if another SCP also allows them","A Deny statement in an SCP overrides any Allow statements in IAM policies attached to users or roles within the affected accounts, effectively restricting access."
"In AWS Organizations, what happens when a member account leaves the organisation?","The account reverts to a standalone AWS account","The account is automatically terminated","The account is suspended for 30 days","The account is moved to a separate OU","When a member account leaves an AWS Organization, it becomes a standalone AWS account.  It's no longer subject to the policies defined within the organization."
"What type of policy allows you to restrict the AWS Regions that member accounts can access within an AWS Organization?","Service Control Policy (SCP)","IAM Policy","Resource Policy","Trust Policy","SCPs can be used to restrict access to specific AWS Regions, helping to ensure compliance with data residency requirements or other regional restrictions."
"You want to ensure all accounts in your AWS Organization use a specific KMS key for encryption. How can you enforce this?","By using an SCP to restrict the use of other KMS keys","By configuring the KMS key policy to deny access from other accounts","By creating a CloudTrail rule to monitor KMS usage","By using AWS Config to check for compliance","SCPs can be used to restrict the use of any KMS keys except the specified one, effectively enforcing the use of the desired KMS key across the organization."
"What is the purpose of the AWS Organizations feature 'Consolidated Billing'?","To combine billing for all accounts in the organisation into a single bill","To provide discounts based on the aggregate usage of all accounts","To automatically allocate costs to individual accounts","To encrypt billing data for all accounts","Consolidated Billing combines the AWS usage from all accounts in the organization into a single bill, making it easier to track and manage spending."
"Which statement is true regarding applying IAM policies and SCPs in AWS Organizations?","IAM policies grant permissions, while SCPs limit permissions","IAM policies limit permissions, while SCPs grant permissions","Both IAM policies and SCPs grant permissions","Both IAM policies and SCPs limit permissions","IAM policies grant permissions to users, groups, and roles within an account, while SCPs limit the permissions that can be granted within an account or OU."
"If an IAM policy and an SCP both apply to a user and the SCP denies an action that the IAM policy allows, what is the result?","The action is denied","The action is allowed","The SCP is ignored","The IAM policy is ignored","SCPs act as guardrails, and a Deny in an SCP always overrides an Allow in an IAM policy, resulting in the action being denied."
"Which AWS service integrates directly with AWS Organizations to help you track and manage your AWS resource inventory across all accounts?","AWS Config","AWS CloudTrail","AWS CloudWatch","AWS Trusted Advisor","AWS Config can be used to track resource inventory and configuration across all accounts in an organization, allowing you to centrally manage and audit your AWS resources."
"What happens to the AWS Support level of a member account in an AWS Organization?","It inherits the support level of the management account","It retains its original AWS Support level","It is automatically downgraded to the Basic support level","It is automatically upgraded to the Enterprise support level","A member account retains its original AWS Support level.  It does not inherit from or affect the management account's support plan."
"You need to apply a specific security policy to a subset of accounts within your AWS Organization. What is the recommended approach?","Create an OU for the accounts and apply the SCP to the OU","Apply the SCP directly to each individual account","Create an IAM role in the Management Account and assume it from each member account","Use AWS Config rules to enforce the policy","Grouping the accounts into an OU and applying the SCP to the OU is the most efficient and scalable approach for applying policies to a subset of accounts."
"What is the maximum number of accounts that can be part of an AWS Organization?","Unlimited","Limited by AWS support tier","Depends on the number of OUs","5000","The maximum number of accounts that can be part of an AWS Organization is 5000."
"Which of the following actions can only be performed by the Management Account of an AWS Organization?","Creating new member accounts","Launching EC2 instances","Creating IAM users","Creating S3 buckets","Creating new member accounts can only be done by the Management Account, ensuring centralized control over the organization's structure."
"Which AWS service provides a detailed view of costs across all accounts in an AWS Organization?","AWS Cost Explorer","AWS CloudWatch","AWS CloudTrail","AWS Trusted Advisor","AWS Cost Explorer allows you to analyse your AWS costs across all accounts in your organization, helping you to identify cost-saving opportunities."
"What is the function of the 'aws:PrincipalOrgID' condition key in an IAM policy?","To restrict access based on the AWS Organization ID","To grant access to resources in the Management Account","To delegate permissions to a specific OU","To identify the root user of an AWS account","The `aws:PrincipalOrgID` condition key in an IAM policy allows you to grant or restrict access based on the AWS Organization ID, enabling you to control access across your organization."
"What is the effect of enabling AWS CloudTrail Organisation Trail?","Centralised logging of events across all accounts in the organisation","Automated compliance checks across all accounts","Real-time monitoring of security threats across all accounts","Automatic backup of data in all member accounts","An organization trail creates a single point for logging all API calls made across all accounts in the organization, simplifying auditing and security monitoring."
"Which AWS service can you use to automate security checks and compliance validation across your AWS Organization?","AWS Security Hub","AWS CloudTrail","AWS CloudWatch","AWS Trusted Advisor","AWS Security Hub provides a central view of your security posture across all accounts in your organization, and automates security checks and compliance validation."
"What is the difference between a 'Root' and an 'Organizational Unit (OU)' in AWS Organizations?","The Root is the top-level container, OUs are nested within it","The Root is used for billing, OUs are used for security","The Root represents a single AWS account, OUs represent multiple accounts","The Root is used for global services, OUs are used for regional services","The Root is the top-level container for all accounts in an organization, while OUs are nested within the Root and used to group accounts for easier management."
"How can you prevent users in a specific AWS account within your organization from terminating EC2 instances?","Apply an SCP to the account denying the 'ec2:TerminateInstances' action","Disable EC2 service access in the account's IAM settings","Remove the 'ec2:TerminateInstances' permission from all IAM roles in the account","Enable termination protection on all EC2 instances in the account","Applying an SCP to the account denying the 'ec2:TerminateInstances' action will prevent users from terminating EC2 instances, even if they have IAM policies that allow it."
"Which of the following is NOT a feature provided by AWS Organizations?","Centralised policy management","Consolidated billing","Cross-account resource sharing","Automated deployment of applications","Automated deployment of applications is not a feature directly provided by AWS Organizations; other services like CodeDeploy or CloudFormation are used for that."
"What is the purpose of AWS Control Tower when used in conjunction with AWS Organizations?","To automate the setup and governance of a multi-account AWS environment","To provide real-time monitoring of AWS resources","To optimise AWS spending across all accounts","To encrypt data stored in S3 buckets","AWS Control Tower provides a pre-configured, opinionated way to set up and govern a multi-account AWS environment based on best practices, leveraging AWS Organizations."
"You want to share a custom AMI across all accounts in your AWS Organization. What is the recommended approach?","Share the AMI and grant permission to the Organization ID","Copy the AMI to each individual account","Create a public AMI and share the URL","Create a snapshot of the AMI and share it","Sharing the AMI and granting permission to the Organization ID allows all accounts in the organization to access the AMI without the need for copying or manual sharing."
"What is the primary benefit of using tags with AWS Organizations?","To improve resource organisation and cost allocation","To encrypt data at rest","To automate security patching","To enable multi-factor authentication","Tags allow you to organise and categorise your AWS resources, including those managed through AWS Organizations, making it easier to track costs and manage resources."
"When creating an AWS Organization, what is the first step you should take?","Create the Management Account","Invite existing AWS accounts to join","Define your organizational structure (OUs)","Create Service Control Policies (SCPs)","The first step is to create the Management Account, as this account will be used to manage the organization and its member accounts."
"What is the purpose of the 'AllowedServicePrincipals' condition in an SCP?","To restrict which services can assume IAM roles in member accounts","To restrict which services can be used in member accounts","To restrict which users can access services in member accounts","To restrict which roles can be used in member accounts","The 'AllowedServicePrincipals' condition in an SCP can be used to restrict which AWS services are allowed to assume IAM roles in member accounts, helping to prevent privilege escalation."
"How can you ensure that all new AWS accounts created within your Organization automatically have a specific IAM role created?","Use AWS CloudFormation StackSets targeting the Organization","Create a CloudWatch Events rule to trigger a Lambda function","Configure AWS Config to create the role automatically","Manually create the role in each new account","CloudFormation StackSets, when used with Organizations, can deploy CloudFormation templates to all accounts in the organization, including new ones, to ensure consistent configuration."
"You want to receive a notification whenever a new AWS account is created within your Organization. What service can you use?","AWS CloudTrail","AWS CloudWatch Events (EventBridge)","AWS Config","AWS Trusted Advisor","CloudWatch Events (now EventBridge) can be configured to detect the 'CreateAccount' event in CloudTrail and trigger a notification or other automated action."
"What is the recommended method for migrating an existing AWS account into an Organization without causing downtime?","Invite the account to the Organization; no downtime is required","Stop all resources in the account before inviting it","Create a snapshot of all resources and restore them in a new account","Schedule a maintenance window and migrate the resources manually","Inviting the account to the Organization does not require any downtime.  The account simply becomes part of the organization's hierarchy and billing structure."
"What is the best practice for managing access keys in AWS Organizations?","Disable access keys for IAM users and use IAM roles instead","Share access keys between accounts to simplify management","Store access keys in a central location for easy access","Rotate access keys manually on a regular basis","Disabling access keys and using IAM roles is the best practice for managing access in AWS, as it avoids the security risks associated with managing long-term credentials."
"You need to implement a policy that requires all S3 buckets in your Organization to be encrypted. Which AWS service can help you achieve this?","AWS Config","AWS CloudTrail","AWS CloudWatch","AWS Trusted Advisor","AWS Config allows you to create rules to check whether S3 buckets are encrypted and can automatically remediate non-compliant buckets."
"What is the 'delegated administrator' account in AWS Organizations used for?","To manage specific AWS services on behalf of the Management Account","To provide temporary access to AWS resources","To bypass SCPs for emergency access","To store backup data from all accounts","A delegated administrator account allows you to delegate the management of specific AWS services to a member account, reducing the burden on the Management Account."
"In the context of AWS Organizations, what does the term 'feature set' refer to?","The set of features enabled for the Organization (e.g., Consolidated Billing, All Features)","The list of AWS services available in each account","The collection of IAM policies applied across the Organization","The group of security policies enforced by SCPs","The 'feature set' refers to the capabilities enabled for the organization, such as Consolidated Billing or All Features, which determines the available functionality and management options."
"How can you ensure that all AWS accounts created within your Organization adhere to a specific naming convention?","By using Tag Policies to enforce the naming convention","By using SCPs to restrict account creation","By using AWS Config rules to check for compliance","By manually reviewing account names after creation","Tag Policies allow you to enforce naming conventions for resources, including AWS accounts, ensuring consistency and compliance."
"Which of the following is a benefit of using AWS Organizations with AWS IAM Identity Center (successor to AWS Single Sign-On)?","Simplified user access management across multiple AWS accounts","Automated patching of EC2 instances","Real-time monitoring of security threats","Improved database performance","AWS IAM Identity Center simplifies user access management by providing a central place to manage users and their access to multiple AWS accounts within the organization."
"You need to audit all IAM actions performed within your AWS Organization. Which service provides the most comprehensive logging for this purpose?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS CloudTrail captures all API calls made within your AWS environment, including IAM actions, providing a detailed audit trail for security and compliance purposes."
"What is the role of AWS Resource Access Manager (RAM) in the context of AWS Organizations?","To share AWS resources across accounts within the organization","To manage IAM roles and permissions","To automate the creation of AWS resources","To monitor the health of AWS resources","AWS RAM allows you to share AWS resources, such as VPCs and subnets, across accounts within your organization, enabling resource sharing and collaboration."
"How does AWS Organizations help with compliance management?","By providing a central place to define and enforce policies across all accounts","By automatically encrypting all data stored in AWS","By preventing all security breaches","By providing free AWS Support for all accounts","AWS Organizations allows you to define and enforce compliance policies using SCPs, ensuring that all accounts within the organization adhere to the defined standards."
"You are tasked with setting up a multi-account AWS environment using AWS Organizations. Which approach is generally recommended?","Start with a small number of OUs and gradually expand as needed","Create a large number of OUs upfront to anticipate future needs","Use a flat structure with all accounts directly under the Root","Avoid using OUs altogether for simplicity","Starting with a small number of OUs and gradually expanding as needed allows you to refine your organizational structure as your needs evolve, avoiding unnecessary complexity."
"In AWS Organizations, what is the purpose of a Service Control Policy (SCP)?","To manage permissions for AWS accounts within an organisation","To monitor resource usage across all accounts","To automate the creation of new AWS accounts","To manage billing for linked accounts","SCPs define the maximum permissions available to accounts in an organisation. They act as a guardrail, limiting what IAM users and roles can do."
"Which AWS Organizations feature allows you to centrally manage access to AWS services and resources across multiple AWS accounts?","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS IAM Identity Center (Successor to AWS SSO) provides a central place to manage users and their access to multiple AWS accounts and applications."
"What is the primary benefit of using AWS Organizations' consolidated billing feature?","Simplified billing and cost management across all accounts","Improved security posture of individual accounts","Automatic resource scaling based on demand","Enhanced network performance across accounts","Consolidated billing allows you to receive a single bill for all AWS accounts in your organisation, making it easier to track and manage costs."
"You want to prevent users in a specific AWS account within your organisation from launching EC2 instances. How can you achieve this using AWS Organizations?","Apply a Service Control Policy (SCP) that denies EC2 instance launch permissions to that account","Configure an IAM policy that denies EC2 instance launch permissions to all users in the organisation","Enable AWS CloudTrail logging for EC2 instance launches in that account","Use AWS Config to monitor EC2 instance launches and trigger alerts","SCPs are the mechanism in AWS Organizations to restrict what actions can be performed by IAM users and roles within member accounts."
"What is the role of the management account in an AWS Organization?","It's the root account used to create and manage the organisation","It is used to host shared services such as Active Directory","It provides a central location for monitoring resource usage across all accounts","It is used to store backups of all data in the organisation","The management account is the master account that creates the AWS Organization and has control over all member accounts."
"When creating an AWS Organization, what is the first step you should take?","Create an AWS account to act as the management account","Invite existing AWS accounts to join the organisation","Create Organizational Units (OUs) to structure your accounts","Define Service Control Policies (SCPs) to restrict permissions","The first step is to create an AWS account to serve as the management account for the organisation."
"What is the purpose of Organizational Units (OUs) in AWS Organizations?","To logically group AWS accounts for easier management and policy application","To create separate billing groups within the organisation","To isolate resources between different AWS accounts","To automatically scale resources based on demand","OUs provide a hierarchical structure for organising accounts, enabling you to apply policies and manage permissions at the OU level."
"Which statement about moving an existing AWS account into an AWS Organization is correct?","The existing account must accept an invitation from the management account to join the organisation","The existing account is automatically moved into the organisation without any action required","The existing account must be deleted and recreated within the organisation","The existing account's IAM users and roles must be migrated to the management account","Existing accounts need to accept an invitation to join. This is a key security measure."
"How can you delegate administrative control over specific AWS accounts within your Organization?","By creating IAM roles in the management account and granting them access to the member accounts","By creating IAM users in the management account and granting them access to the member accounts","By delegating permissions to IAM users in the member accounts","By assigning different AWS Support plans to different OUs","You delegate by creating IAM roles in the management account that are assumable by users in the member accounts."
"What happens when an SCP denies a permission that is also granted by an IAM policy in a member account?","The SCP takes precedence and the permission is denied","The IAM policy takes precedence and the permission is granted","The permission is granted only if both the SCP and the IAM policy explicitly allow it","The behaviour is undefined and depends on the order in which the policies are evaluated","SCPs act as a guardrail. A deny in an SCP will always override an allow in an IAM policy."
"What is the effect of enabling AWS Organizations on an existing AWS account?","It allows you to centrally manage and govern multiple AWS accounts","It automatically migrates all resources to the management account","It restricts access to all AWS services in the account","It reduces the cost of running AWS services in the account","AWS Organizations provides centralised management and governance across multiple accounts."
"Which AWS service provides detailed information about the costs associated with different AWS accounts within an AWS Organization?","AWS Cost Explorer","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Cost Explorer allows you to analyse your AWS costs, including breaking them down by account, service, and region."
"You need to ensure that all new AWS accounts created within your Organization automatically have a specific IAM role. How can you achieve this?","Use AWS CloudFormation StackSets to deploy the IAM role to all new accounts","Create an IAM role in the management account and grant it access to all member accounts","Configure AWS Config to automatically create the IAM role in new accounts","Manually create the IAM role in each new account","StackSets allows you to deploy resources, including IAM roles, to multiple AWS accounts in your organisation."
"What is the purpose of the 'aws:PrincipalOrgID' condition key in an IAM policy when using AWS Organizations?","To restrict access to resources based on the organisation ID","To restrict access to resources based on the account ID","To restrict access to resources based on the user ID","To restrict access to resources based on the role ID","The aws:PrincipalOrgID condition key is used to control access based on the organisation ID, ensuring that only accounts within the specified organisation can access the resource."
"Which of the following is NOT a benefit of using AWS Organizations?","Centralised management of multiple AWS accounts","Simplified billing and cost management","Improved security posture through SCPs","Automatic resource provisioning across all accounts","Automatic resource provisioning is NOT directly a feature of Organizations. That requires other services."
"What is the maximum number of AWS accounts that can be included in an AWS Organization?","There is no fixed limit, it depends on your requirements and AWS support","100","1000","50","The service limits are subject to change but in practice there is no hard limit and depends on your requirements"
"You want to implement a least privilege strategy across your AWS Organization. How can SCPs help you achieve this?","By defining the maximum permissions that IAM users and roles can have in each account","By granting all permissions to IAM users and roles by default","By monitoring resource usage and identifying over-permissioned users and roles","By automatically removing unused IAM users and roles","SCPs define the maximum permissions that can be granted, ensuring that users and roles only have the necessary permissions."
"What happens to the resources in an AWS account when it is removed from an AWS Organization?","The resources remain in the account and are accessible as before","The resources are automatically moved to the management account","The resources are deleted","The resources become inaccessible until the account is added to another organisation","The resources remain in the account. Removing it simply disconnects it from the Organisation."
"Which AWS service can be used to automate the creation and configuration of AWS accounts within an AWS Organization?","AWS Control Tower Account Factory","AWS CloudFormation","AWS Config","AWS Systems Manager","AWS Control Tower Account Factory helps to automate account creation, including setting up guardrails and configurations."
"What is the main purpose of the AWS Organizations feature 'tag policies'?","To enforce consistent tagging across resources in different AWS accounts","To automatically apply tags to new resources","To monitor resource usage based on tags","To automatically delete resources that are not tagged","Tag policies ensure consistent tagging practices across your organisation, which aids in cost allocation, resource management, and automation."
"You want to ensure that all AWS accounts in your organization have a common set of security configurations. Which AWS Organizations feature can help with this?","AWS CloudFormation StackSets","AWS Config","AWS CloudTrail","AWS Systems Manager","StackSets allows you to deploy templates to multiple accounts, ensuring a consistent baseline configuration."
"What is the difference between a root user and an IAM user in the context of AWS Organizations?","The root user has full access to the account, while an IAM user has specific permissions assigned","The root user can only manage billing information, while an IAM user can manage resources","The root user can only access the AWS Management Console, while an IAM user can access the AWS CLI","The root user is automatically created when an account joins an organization, while an IAM user must be created manually","The root user has unrestricted access, while IAM users are granted specific permissions."
"Which condition key would you use in a resource-based policy to allow access only from accounts within a specific AWS Organization?","aws:PrincipalOrgPaths","aws:PrincipalAccount","aws:SourceIp","aws:userid","`aws:PrincipalOrgPaths` allows you to grant permissions to principals in accounts that are members of your organization."
"In AWS Organizations, what is the relationship between OUs and SCPs?","SCPs are applied to OUs to manage permissions for accounts within those OUs","OUs are applied to SCPs to define the scope of the policies","OUs and SCPs are independent and have no relationship","SCPs are used to create OUs automatically","SCPs are applied to OUs (or directly to accounts) to control the maximum available permissions."
"Which of the following is a valid use case for AWS Organizations?","Enforcing security standards across multiple AWS accounts","Migrating on-premises servers to AWS","Monitoring the health of EC2 instances","Load balancing traffic across multiple regions","AWS Organizations helps manage and govern multiple accounts which contributes to enforcing security standards."
"How can you ensure that all S3 buckets created in your AWS Organization are encrypted at rest?","By implementing an SCP that denies S3 bucket creation without encryption enabled","By enabling default encryption on the S3 service in the management account","By configuring AWS Config to monitor S3 buckets and enforce encryption","By creating an IAM policy that requires encryption for all S3 bucket creation","SCPs are the best way to prevent actions if they are not compliant with your policies."
"What is the benefit of using AWS Organizations for resource sharing?","It allows you to easily share resources such as VPCs and subnets across multiple AWS accounts","It allows you to share IAM roles across multiple AWS accounts","It allows you to share data between different regions","It allows you to share databases between different AWS accounts","Resource sharing is a key advantage, reducing duplication and improving efficiency."
"You need to audit the actions taken by users in all AWS accounts within your organization. Which AWS service can you use in conjunction with AWS Organizations?","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS Systems Manager","CloudTrail logs API calls and other events, providing an audit trail for all accounts."
"What happens when you suspend a member account in AWS Organizations?","The account is temporarily disabled and cannot be accessed until it is restored","The account is permanently deleted","The resources in the account are moved to the management account","The account is isolated but still incurs charges","Suspending the account disables access but doesn't delete it. You can reactivate it."
"Which AWS Organizations feature allows you to define guardrails that prevent users from creating non-compliant resources?","Service Control Policies (SCPs)","AWS CloudTrail","AWS Config Rules","AWS IAM","SCPs act as guardrails, limiting the actions that can be taken by users and roles in member accounts."
"What is the purpose of the AWS Organizations 'delegated administrator' feature?","To allow a member account to manage certain aspects of the organization on behalf of the management account","To allow a member account to manage billing for the entire organization","To allow a member account to manage IAM policies for the entire organization","To allow a member account to create new AWS accounts within the organization","Delegated administrators relieve the burden on the management account by allowing member accounts to manage specific services."
"Which of the following is NOT a valid target for an SCP?","A specific IAM user","The organization root","An individual AWS account","An organizational unit (OU)","SCPs cannot be directly applied to IAM users."
"How can you centrally manage and deploy software patches across all EC2 instances in your AWS Organization?","Using AWS Systems Manager Patch Manager","Using AWS Config","Using AWS CloudTrail","Using AWS IAM","AWS Systems Manager Patch Manager allows you to automate the patching process across multiple accounts."
"You want to ensure that all data stored in S3 buckets in your AWS Organization is protected from accidental deletion. What can you implement at the Organizations level?","An SCP that prevents deletion of S3 buckets or objects without specific approval","Enable versioning on all S3 buckets","Require multi-factor authentication for all S3 bucket access","Implement cross-region replication for all S3 buckets","SCPs can prevent deletion by denying access to the delete actions if specific conditions aren't met."
"What is the recommended approach for managing IAM roles and permissions in an AWS Organization?","Centralise IAM management in the management account and grant access to member accounts","Delegate IAM management to the individual member accounts","Use a hybrid approach, with some IAM roles managed centrally and others managed locally","Automatically generate IAM roles based on resource usage patterns","Delegated IAM management promotes autonomy and avoids overburdening the management account."
"Which AWS service can be used to centrally manage secrets and credentials across all AWS accounts in your Organization?","AWS Secrets Manager","AWS IAM","AWS CloudHSM","AWS KMS","AWS Secrets Manager is designed for securely storing and managing secrets, and can be used across multiple accounts."
"What is the purpose of the 'Account Factory' feature within AWS Control Tower, when used with AWS Organizations?","To automate the creation and configuration of new AWS accounts","To automatically migrate existing AWS accounts into the organization","To automatically tag resources in new AWS accounts","To automatically create IAM users and roles in new AWS accounts","The Account Factory automates the process of provisioning new AWS accounts with a consistent baseline configuration."
"How can you use AWS Organizations to implement a cost allocation strategy based on different departments within your company?","By creating OUs for each department and tagging resources accordingly","By assigning different billing groups to each department","By using AWS Cost Explorer to filter costs by department","By creating separate AWS accounts for each department","OUs and resource tags allow you to allocate costs to different departments based on their resource usage."
"Which AWS Organizations feature can help you identify and remediate security vulnerabilities across all AWS accounts in your organization?","AWS Security Hub","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Security Hub provides a centralised view of your security posture across your AWS environment, including findings from other security services."
"What is the impact of an SCP that restricts access to a specific AWS Region?","Users in affected accounts will not be able to create resources in that Region","Users in affected accounts will not be able to access resources in any Region","The SCP will have no effect on resource access","The SCP will automatically move all resources to a different Region","Restricting access to a region limits the location of resources that can be created."
"You want to enforce a policy that all AWS accounts in your organization must use multi-factor authentication (MFA) for IAM users. How can you achieve this?","Create an SCP that denies access to AWS services without MFA enabled","Enable MFA for the root user of each account","Configure AWS Config to monitor IAM users and enforce MFA","Create an IAM policy that requires MFA for all users","The SCP effectively restricts all services unless multi-factor authentication is enabled."
"Which of the following is NOT a key consideration when planning your AWS Organizations structure?","The size and complexity of your organization","The number of AWS accounts you need to manage","Your security and compliance requirements","The geographical location of your resources","Geographical location is more related to infrastructure and not to the account structure."
"In AWS Organizations, how can you centrally manage the AWS Support plans for all member accounts?","You manage the support plan at the organisation level, which applies to all accounts","Each member account is responsible for managing its own support plan","The management account is responsible for paying for all support plans, but each account manages its own level","AWS automatically assigns a support plan based on the size of the account","Support plans are managed individually at the account level, although the management account pays for them centrally."
"What is the best practice for granting cross-account access in AWS Organizations?","Use IAM roles with trust relationships between accounts","Create IAM users in the management account and grant them access to member accounts","Share the root user credentials of the management account with trusted users","Grant full administrative access to all accounts for all users","IAM roles are the recommended method for securely granting cross-account access."
"You want to ensure that all AWS accounts within your organization comply with a specific PCI DSS standard. Which AWS service can help you achieve this?","AWS Audit Manager","AWS CloudTrail","AWS Config","AWS Security Hub","AWS Audit Manager helps you automate the process of assessing and auditing your compliance with various standards, including PCI DSS."
"Which of the following is NOT a feature of AWS Organizations?","Account consolidation","Automated security auditing","Centralized policy management","Hierarchical grouping of accounts","Automated security auditing is not a built in feature."
"Which condition key in an IAM policy would you use to ensure that an IAM role can only be assumed by accounts within your AWS Organization?","sts:ExternalId","aws:PrincipalOrgID","aws:SourceAccount","aws:userid","aws:PrincipalOrgID restricts access to roles within the organisation"
"In AWS Organizations, what is the purpose of a Service Control Policy (SCP)?","To limit the permissions of AWS accounts within the organisation.","To configure network settings across all accounts.","To manage billing preferences for all accounts.","To automate the deployment of resources across accounts.","SCPs define the maximum permissions available to accounts within an AWS Organization. They act as guardrails, limiting what actions can be performed."
"Which AWS Organizations feature allows you to consolidate billing for all accounts in your organisation?","Consolidated Billing","Shared VPC","Resource Access Manager","CloudTrail Organisation Integration","Consolidated Billing enables you to receive a single bill for all AWS accounts in your organisation."
"In AWS Organizations, what is a management account?","The account that centrally manages all other accounts in the organisation.","A read-only account used for monitoring.","An account with unlimited permissions.","A temporary account for testing purposes.","The management account is the root account of the organisation and has control over all other member accounts."
"What is the purpose of Organizational Units (OUs) in AWS Organizations?","To group AWS accounts into a hierarchy for easier management.","To separate network traffic between accounts.","To isolate resources in different regions.","To automatically provision resources to new accounts.","OUs provide a hierarchical structure for organizing AWS accounts, allowing you to apply policies and manage them more effectively."
"Which of the following is a benefit of using AWS Organizations?","Centralised management of multiple AWS accounts.","Automatic scaling of EC2 instances.","Free data transfer between regions.","Unlimited storage in S3.","AWS Organizations allows you to centrally manage multiple AWS accounts, apply policies, and consolidate billing."
"What is the relationship between AWS Organizations and AWS IAM?","AWS Organizations manages accounts, while IAM manages users and roles within those accounts.","AWS Organizations and IAM provide identical functionality.","IAM manages accounts, while AWS Organizations manages users and roles.","AWS Organizations requires you to disable IAM in member accounts.","AWS Organizations focuses on account management, while IAM is used for managing users, roles, and permissions within individual accounts."
"In AWS Organizations, how can you ensure that certain AWS services are not used in specific accounts?","Apply a Service Control Policy (SCP) that denies access to those services.","Disable the services at the AWS region level.","Remove the services from the IAM role associated with the account.","Use AWS Config to prevent the services from being provisioned.","SCPs can be used to explicitly deny access to specific AWS services or actions within an account."
"Which statement is true regarding Service Control Policies (SCPs) in AWS Organizations?","SCPs affect all users and roles in the targeted account(s), including the root user.","SCPs only affect IAM users, not IAM roles.","SCPs only apply to the management account.","SCPs can only deny access to AWS services, not individual API actions.","SCPs act as guardrails and can affect all IAM users and roles, including the root user, within the accounts to which they are applied."
"What is the best practice for managing access to the AWS Organizations management account?","Enable Multi-Factor Authentication (MFA) for the root user and limit its usage.","Share the root user credentials with trusted administrators.","Disable MFA to simplify access.","Use the root user for all administrative tasks.","Enabling MFA and limiting the root user's usage is a best practice to protect the highly privileged management account."
"How does AWS Organizations help with compliance?","By enabling central control and auditing of AWS resources across all accounts.","By automatically encrypting all data in all accounts.","By providing free access to compliance certifications.","By eliminating the need for compliance audits.","AWS Organizations helps with compliance by providing centralized control and auditing, making it easier to enforce policies and track resource usage across all accounts."
"Which type of AWS account is used to create an AWS Organization?","Management Account","Member Account","Standalone Account","Linked Account","The Management Account is the root account of the organisation and has control over all other member accounts."
"What is the maximum number of AWS accounts that can be invited to join an AWS Organisation if using only invitations?","Unlimited","10","20","5","AWS Organizations limits the number of pending invitations to an organisation to 20. This prevents abuse."
"What is required before you can move an AWS account from one AWS Organisation to another?","The account must be removed from the original organization first.","The account must have all of its resources terminated first.","The account must have the same IAM settings in both organisations.","The account must have an active support plan.","An account must be removed from the original organization before it can be invited to join another one."
"Which AWS Organizations feature helps you gain visibility into cost and usage patterns across your entire organization?","AWS Cost Explorer","AWS Trusted Advisor","AWS CloudTrail","AWS Config","AWS Cost Explorer, when integrated with AWS Organizations, provides visibility into cost and usage patterns across all accounts in the organization."
"How does AWS Organizations integrate with AWS Identity and Access Management (IAM)?","AWS Organizations allows you to centrally manage IAM policies across multiple accounts.","AWS Organizations replaces IAM.","IAM policies can be applied to organizational units (OUs) in AWS Organizations.","IAM policies can be applied to multiple accounts using AWS Organizations' delegated administrator feature.","AWS Organizations allows you to centrally manage IAM policies across multiple accounts. You can use Service Control Policies (SCPs) to set permissions guardrails for all accounts in an OU or your entire organization."
"What is the purpose of enabling AWS CloudTrail integration with AWS Organizations?","To centralise logging and auditing across all accounts in the organization.","To automatically encrypt data in all accounts.","To provide free access to AWS support.","To simplify the process of creating new AWS accounts.","By integrating CloudTrail with AWS Organizations, you can centralize logging and auditing, providing a single source of truth for security and compliance purposes."
"Which AWS service can be used to automate the creation of AWS accounts within an organization?","AWS Control Tower Account Factory","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS Control Tower Account Factory is designed to automate the creation of AWS accounts within an organization, ensuring that they are compliant with predefined policies and configurations."
"What is a 'delegated administrator' in the context of AWS Organizations?","An AWS account within the organisation that is granted permissions to manage certain AWS services on behalf of the management account.","The default administrator role for all AWS accounts.","An IAM user with full administrative privileges.","A service that automatically manages administrator roles.","Delegated administrators allow you to assign specific AWS services management tasks to member accounts, reducing the load on the management account."
"What are the implications of removing an AWS account from an organization?","The account becomes a standalone AWS account, and policies inherited from the organisation are removed.","The account is permanently deleted.","The account is automatically migrated to another organisation.","The account is suspended for 24 hours.","When an account is removed from an organization, it becomes a standalone account, and all SCPs and other policies inherited from the organization are removed."
"How can you ensure that all new AWS accounts created within your organization automatically inherit specific configurations, such as IAM roles and network settings?","Use AWS Control Tower Account Factory with account customisations.","Manually configure each new account.","Use AWS Config to enforce desired configurations.","Use AWS CloudTrail to track changes and remediate misconfigurations.","AWS Control Tower Account Factory, combined with account customisations, enables you to automate the configuration of new accounts, ensuring they meet your organisation's standards from the start."
"What is the main benefit of using AWS Resource Access Manager (RAM) in conjunction with AWS Organizations?","You can share AWS resources across accounts within your organization.","You can automatically back up AWS resources in all accounts.","You can encrypt data at rest in all AWS accounts.","You can monitor resource utilization across all accounts.","AWS RAM allows you to securely share AWS resources, such as subnets, transit gateways, and license manager configurations, across accounts within your organization."
"In AWS Organizations, what is the purpose of a Trusted Access relationship with other AWS services?","To allow other AWS services to perform actions in your organization on your behalf.","To restrict access to specific AWS services.","To automatically create backups of AWS resources.","To encrypt data at rest in all AWS accounts.","Trusted access grants permissions to other AWS services to perform actions in your organization, typically for management or integration purposes."
"Which of the following is NOT a key benefit of using AWS Organizations?","Simplified cross-account resource sharing.","Automated resource provisioning across all accounts.","Centralized billing and cost management.","Consistent policy enforcement across multiple AWS accounts.","While AWS Organizations helps with resource sharing through RAM, it does not inherently automate resource provisioning. AWS Control Tower or other automation tools are needed for provisioning."
"You need to ensure that all AWS accounts within your organization are compliant with a specific set of security standards. Which AWS service can help you achieve this?","AWS Security Hub","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS Security Hub provides a central view of security alerts and compliance status across all AWS accounts in your organization, making it easier to identify and remediate security issues."
"How does AWS Organizations facilitate cross-account access for developers?","By using IAM roles and trusting relationships between accounts.","By automatically sharing IAM user credentials across accounts.","By disabling IAM in member accounts.","By providing a single set of credentials for all accounts.","IAM roles and trust relationships allow developers to securely access resources in other AWS accounts without sharing user credentials."
"What is the recommended approach for managing a large number of AWS accounts in an organization, using AWS Organizations?","Organise accounts into OUs and apply policies at the OU level.","Manage each account individually.","Create a single OU for all accounts and apply policies globally.","Use IAM groups to manage access to multiple accounts.","Organising accounts into OUs allows you to apply policies and permissions at a higher level, making it easier to manage a large number of accounts."
"How can you delegate administrative control for specific AWS services to a member account within your AWS Organization?","By using the delegated administrator feature.","By sharing the management account's credentials.","By creating a new management account for each service.","By granting full access to all member accounts.","The delegated administrator feature in AWS Organizations allows you to grant permissions to member accounts to manage specific AWS services on behalf of the management account."
"What is the benefit of using AWS Organizations in conjunction with AWS Control Tower?","AWS Control Tower automates the setup and governance of a multi-account AWS environment, leveraging AWS Organizations.","AWS Control Tower replaces AWS Organizations.","AWS Organizations provides all the functionality of AWS Control Tower.","AWS Control Tower is not compatible with AWS Organizations.","AWS Control Tower builds on top of AWS Organizations to automate the setup and governance of a secure and compliant multi-account AWS environment."
"Which of the following statements is true regarding the maximum number of OUs within an AWS Organization?","There is a limit to the number of OUs you can create.","There is no limit to the number of OUs you can create.","OUs are automatically created based on the number of accounts.","You can only have one OU per AWS account.","There is a limit to the number of OUs you can create."
"What is the primary function of AWS Organizations tagging policies?","To enforce a consistent tagging strategy across all resources in your AWS Organization.","To automatically provision resources in member accounts.","To monitor resource utilization across all accounts.","To encrypt data at rest in all AWS accounts.","AWS Organizations tagging policies enforce a consistent tagging strategy across resources in all accounts, ensuring compliance and improving resource management."
"How can you centrally manage access to AWS resources across multiple accounts within your organization?","Using IAM Identity Center (successor to AWS SSO) with AWS Organizations.","Creating individual IAM users in each account.","Sharing the root user credentials of the management account.","Disabling IAM in member accounts.","IAM Identity Center (successor to AWS SSO) allows you to centrally manage access to AWS resources across multiple accounts in your organization, providing a single sign-on experience for users."
"What happens to an AWS account when it is disassociated from an AWS Organization?","It becomes a standalone AWS account and no longer inherits policies from the organization.","It is automatically deleted.","It is automatically moved to a new organization.","It is suspended for a period of 24 hours.","When an AWS account is disassociated from an AWS Organization, it becomes a standalone AWS account and loses all policies and configurations inherited from the organization."
"Which AWS service can be used to automate the deployment of resources across multiple AWS accounts in your organization, based on a predefined template?","AWS CloudFormation StackSets","AWS CodeDeploy","AWS Lambda","AWS Systems Manager","AWS CloudFormation StackSets enable you to deploy and manage CloudFormation stacks across multiple accounts and regions, simplifying the deployment of resources across your organization."
"How can you ensure that all new AWS accounts created within your organisation automatically have AWS Config enabled?","Use AWS Control Tower Account Factory with customisations.","Enable AWS Config in the management account and apply it to all member accounts.","Manually enable AWS Config in each new account.","AWS Config is automatically enabled for all AWS accounts in an organization.","AWS Control Tower Account Factory, combined with account customisations, can ensure that all new accounts automatically have AWS Config enabled from the start."
"Which of the following can be used to generate reports on the security posture of AWS accounts within an AWS Organization?","AWS Security Hub","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS Security Hub aggregates security findings from various sources and provides reports on the security posture of AWS accounts within an AWS Organization."
"What is the difference between Service Control Policies (SCPs) and IAM policies within AWS Organizations?","SCPs define the maximum permissions for accounts, while IAM policies grant specific permissions to users and roles within those accounts.","SCPs grant specific permissions to users and roles, while IAM policies define the maximum permissions for accounts.","SCPs and IAM policies provide identical functionality.","SCPs apply only to the management account, while IAM policies apply to member accounts.","SCPs act as guardrails, defining the maximum permissions for accounts, while IAM policies grant specific permissions to users and roles within those accounts."
"How can you audit changes made to Service Control Policies (SCPs) within your AWS Organization?","By using AWS CloudTrail to track changes to SCPs.","By using AWS Config to monitor SCP configurations.","By manually reviewing SCP configurations.","SCPs cannot be audited.","AWS CloudTrail records API calls made to manage SCPs, allowing you to track changes and identify who made them."
"Which AWS service allows you to share AWS resources across accounts in your organization without requiring manual configuration in each account?","AWS Resource Access Manager (RAM)","AWS CloudFormation StackSets","AWS Systems Manager","AWS Config","AWS Resource Access Manager (RAM) allows you to securely share AWS resources, such as subnets and transit gateways, across accounts within your organization without needing to configure each account individually."
"What is the purpose of the AWS Organizations 'tagging' policy type?","To enforce a consistent tagging strategy across all resources in your AWS Organization.","To automatically encrypt all data in all member accounts.","To automatically provision resources in member accounts.","To control access to specific tags.","Tagging policies within AWS Organizations enforce a consistent tagging strategy across all resources, making it easier to manage and organize your AWS resources."
"In AWS Organizations, how do you prevent IAM users or roles in member accounts from disabling AWS CloudTrail?","By using a Service Control Policy (SCP) to deny the 'cloudtrail:StopLogging' action.","By disabling CloudTrail in the management account.","By manually disabling CloudTrail in each member account.","CloudTrail cannot be disabled by member accounts.","An SCP can be used to deny the `cloudtrail:StopLogging` action, preventing IAM users or roles in member accounts from disabling AWS CloudTrail."
"You need to move an AWS account from one Organizational Unit (OU) to another within your AWS Organization. What should you consider before doing this?","The account will inherit the policies of the new OU and lose the policies of the old OU.","The account will retain the policies of both OUs.","The account will be automatically deleted.","The account cannot be moved between OUs.","When moving an account between OUs, the account will inherit the policies of the new OU and lose the policies of the old OU. This can impact permissions and access."
"Which of the following is a valid use case for AWS Organizations?","Managing multiple AWS accounts as a single entity.","Automating the deployment of applications to EC2 instances.","Managing network configurations for VPCs.","Scaling DynamoDB tables based on demand.","AWS Organizations enables you to manage multiple AWS accounts as a single entity, simplifying billing, security, and compliance."
"In AWS Organizations, what is the purpose of the 'member' account status?","It indicates that the account is part of an organization but is not the management account.","It indicates that the account is suspended.","It indicates that the account is used for testing purposes.","It indicates that the account is the management account.","The 'member' account status signifies that the account is part of an organization but is not the management account."
"How does AWS Organizations simplify compliance management across multiple AWS accounts?","By providing centralised policy enforcement and auditing capabilities.","By automatically encrypting all data in all accounts.","By providing free access to AWS support.","By eliminating the need for compliance audits.","AWS Organizations facilitates compliance by providing centralised policy enforcement and auditing capabilities, making it easier to maintain consistent security and compliance standards across all accounts."
"What is a key difference between AWS Organizations and AWS IAM (Identity and Access Management)?","AWS Organizations manages AWS accounts, while IAM manages access within those accounts.","AWS Organizations manages access within AWS accounts, while IAM manages AWS accounts.","AWS Organizations and IAM are interchangeable.","IAM is a feature of AWS Organizations.","AWS Organizations is about managing multiple AWS accounts, while IAM is about managing users, roles, and permissions within individual accounts."
"How can you ensure that all AWS resources deployed in your AWS Organizations are properly tagged for cost allocation purposes?","By using AWS Organizations Tagging Policies.","By manually tagging resources in each account.","By using AWS CloudTrail to track untagged resources.","By using AWS Config to enforce tagging rules.","AWS Organizations Tagging Policies allow you to enforce a consistent tagging strategy across your AWS Organization, ensuring that resources are properly tagged for cost allocation."
"Which AWS service integrates with AWS Organizations to provide a centralised view of security alerts and compliance status across all AWS accounts?","AWS Security Hub","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS Security Hub integrates with AWS Organizations to provide a centralised view of security alerts and compliance status across all AWS accounts, simplifying security management."
"What is the main benefit of using AWS Organizations together with AWS Control Tower?","Automated setup and governance of a secure, multi-account AWS environment.","Centralised billing and cost management across all AWS accounts.","Simplified management of IAM users and roles across all AWS accounts.","Automated scaling of AWS resources across all AWS accounts.","AWS Control Tower leverages AWS Organizations to automate the setup and governance of a secure, compliant multi-account AWS environment, reducing the manual effort required to manage multiple accounts."
"In AWS Organizations, what is the purpose of a Service Control Policy (SCP)?","To limit the actions that users and roles can perform within member accounts","To centrally manage billing and cost allocation across all accounts","To define security groups for EC2 instances across multiple accounts","To automatically provision resources across all AWS accounts","SCPs are used to establish guardrails and limit the actions that users and roles can perform within member accounts of an AWS Organization. They help enforce compliance and security policies."
"What is the primary benefit of using AWS Organizations for managing multiple AWS accounts?","Centralised governance and management","Increased EC2 instance limits","Automatic application deployment","Simplified database migrations","AWS Organizations provides a way to centrally manage and govern multiple AWS accounts, simplifying policy enforcement, access control, and billing."
"Within AWS Organizations, what is the function of an Organization Unit (OU)?","To group accounts for hierarchical management and policy application","To define regions for resource deployment","To specify IP address ranges for VPCs","To store IAM users and roles","OUs allow you to group AWS accounts into a hierarchy to simplify management and apply policies consistently across related accounts."
"When creating a new AWS account using AWS Organizations, what is the default IAM role created in the new account?","There is no default IAM role created","OrganizationAccountAccessRole","Administrator","Root","When creating a new AWS account using AWS Organizations, a default IAM role named OrganizationAccountAccessRole is automatically created to allow the management account to access the new account."
"You need to ensure that only specific AWS regions are allowed for resource deployment across all accounts in your AWS Organization. How can you achieve this?","By applying a Service Control Policy (SCP) that restricts region access","By configuring IAM policies in each individual account","By using AWS Config rules to detect non-compliant resources","By enabling AWS Trusted Advisor recommendations","SCPs can be used to restrict access to specific AWS regions for all accounts within an AWS Organization, enforcing a consistent regional deployment strategy."
"What is the role of the management account in AWS Organizations?","It centrally manages and pays for all member accounts","It provides compute resources for member accounts","It stores data for all member accounts","It monitors the security of all member accounts","The management account is the central point of control for an AWS Organization, responsible for managing member accounts, setting policies, and handling consolidated billing."
"How does consolidated billing work within AWS Organizations?","All charges from member accounts are aggregated and billed to the management account","Each member account receives a separate bill based on its individual usage","Billing is randomly distributed across member accounts","Charges are split evenly between all accounts in the organisation","Consolidated billing in AWS Organizations aggregates the charges from all member accounts and bills them to the management account, allowing for volume discounts and simplified payment."
"What is the impact of enabling 'all features' in AWS Organizations compared to 'consolidated billing' feature set?","Enabling 'all features' allows full control and management across all organizational accounts, including the use of SCPs","Enabling 'all features' allows billing information sharing only","Enabling 'all features' increases compute costs significantly","Enabling 'all features' only allows cost explorer access","Enabling 'all features' gives full control of the Organization, the use of SCPs and other governance features, it requires that all previously created member accounts approve this change."
"What action is required to move an existing AWS account into an AWS Organization?","An invitation must be sent from the organisation and accepted by the account","The account is moved automatically when the organisation is created","The account must be deleted and recreated within the organisation","The root user's password must be reset","Existing accounts can be moved into an AWS Organization by sending an invitation from the organisation and accepting it from the account."
"You want to apply a policy to all accounts within a specific OU in AWS Organizations. Where should you attach the policy?","Attach the policy to the OU","Attach the policy to the management account","Attach the policy to each individual account in the OU","Attach the policy to the AWS Region","Policies attached to an OU will apply to all accounts contained within that OU."
"In AWS Organizations, what is the primary purpose of a Service Control Policy (SCP)?","To restrict the AWS services and actions that can be used by member accounts.","To define the billing structure for the organisation.","To manage IAM roles across all accounts.","To monitor resource utilisation in member accounts.","SCPs are used to centrally control the AWS services and actions available to member accounts within an organisation, enforcing guardrails and compliance."
"What is the benefit of using Consolidated Billing in AWS Organizations?","Volume discounts and simplified payment.","Increased compute capacity.","Enhanced security features.","Automated disaster recovery.","Consolidated Billing enables you to combine usage across all accounts in your organisation to qualify for volume discounts and simplifies payment management."
"Which statement best describes an Organizational Unit (OU) in AWS Organizations?","A container for grouping AWS accounts together.","A policy that defines network configurations.","A tool for monitoring resource costs.","A service that automates instance patching.","An OU is a container for grouping AWS accounts together to administer them as a single unit, allowing you to apply policies and manage resources collectively."
"When moving an AWS account between Organizational Units (OUs) in AWS Organizations, what is a key consideration?","Inherited policies from the new OU will apply.","The account's IAM roles are automatically updated.","The account's billing information is reset.","The account's security groups are automatically modified.","When moving an account, it inherits the policies (SCPs) of the new OU, which can affect the services and actions available to the account."
"What is the root in AWS Organizations?","The parent container for all AWS accounts in the organisation.","A pre-configured IAM role for administrative access.","A feature that automatically backs up all AWS resources.","A tool for managing network traffic.","The root is the top-level container in AWS Organizations, serving as the parent for all accounts and OUs within the organisation."
"What is the AWS Organizations feature that can help enforce governance across multiple accounts?","Service Control Policies (SCPs).","AWS Config Rules.","CloudTrail Logs.","Trusted Advisor Checks.","Service Control Policies (SCPs) enable you to centrally control the AWS services and actions that can be performed by member accounts, helping to enforce governance."
"How does AWS Organizations assist with compliance requirements?","By allowing central control over AWS services and actions.","By automatically generating compliance reports.","By encrypting all data stored in member accounts.","By providing automated security vulnerability scans.","AWS Organizations assists with compliance by providing central control over AWS services and actions, enabling you to enforce compliance policies across your organization."
"Which AWS service is commonly used with AWS Organizations to manage user access across multiple accounts?","IAM Identity Center (Successor to AWS SSO).","AWS Directory Service.","AWS Certificate Manager.","Amazon Cognito.","IAM Identity Center (Successor to AWS SSO) integrates with AWS Organizations to centrally manage user access and permissions across multiple AWS accounts."
"What is the best way to ensure that a newly created AWS account automatically becomes part of an existing AWS Organization?","Invite the account to join the organisation.","Manually create the account within the AWS Organizations console.","Configure an IAM role to automatically add accounts.","Enable automatic account discovery in AWS Organizations.","The account must be invited to join the organisation and accept the invitation in order to become a member."
"In AWS Organizations, what happens if an SCP denies a user permission to perform an action, but their IAM policy grants them that permission?","The SCP takes precedence and the action is denied.","The IAM policy takes precedence and the action is allowed.","The AWS account administrator is notified to resolve the conflict.","The action is allowed, but a warning is logged.","SCPs act as a guardrail and always take precedence over IAM policies. If an SCP denies an action, it will be denied, even if the IAM policy grants permission."