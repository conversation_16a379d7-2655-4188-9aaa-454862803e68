"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Config?","To assess, audit, and evaluate the configurations of your AWS resources.","To monitor CPU utilisation of EC2 instances.","To manage IAM roles and permissions.","To deploy applications using CodeDeploy.","AWS Config continuously monitors and records your AWS resource configurations and allows you to automate the evaluation of recorded configurations against desired configurations."
"Which type of rule in AWS Config allows you to evaluate the configuration of your AWS resources?","Config Rule","IAM Rule","CloudWatch Rule","Security Group Rule","Config Rules allow you to define the desired configuration state of your AWS resources and evaluate their compliance. They're the core of AWS Config's functionality."
"What is the purpose of a remediation action in AWS Config?","To automatically correct non-compliant resource configurations.","To generate alerts for non-compliant resources.","To prevent resource creation that violates rules.","To provide cost optimisation recommendations.","Remediation actions allow you to automatically fix non-compliant resources, bringing them back into a desired configuration state. This automates compliance management."
"How does AWS Config help with compliance management?","By providing a historical record of resource configurations and changes.","By encrypting all data at rest.","By providing DDoS protection.","By optimising network latency.","AWS Config maintains a historical record of resource configurations, allowing you to track changes over time and demonstrate compliance to auditors. It's about historical tracking for auditability."
"What is the role of the AWS Config aggregator?","To collect configuration and compliance data from multiple AWS accounts and regions.","To encrypt configuration data at rest.","To automatically provision AWS resources.","To manage IAM policies across accounts.","The aggregator provides a centralised view of configuration and compliance data across multiple accounts and regions, making it easier to manage compliance at scale."
"What is the AWS Config 'Configuration Recorder' used for?","To record the configuration changes of supported AWS resources.","To encrypt configuration data in transit.","To manage access to configuration data.","To generate cost reports based on resource configurations.","The Configuration Recorder tracks the configuration changes of your resources and makes them available for evaluation by Config Rules."
"Which of the following is a managed rule available in AWS Config?","`ec2-volume-inuse-check`","`s3-bucket-policy-check`","`rds-instance-public-access-check`","`lambda-function-version-check`","`ec2-volume-inuse-check` is a managed Config rule that checks if all Amazon EBS volumes are attached to an EC2 instance."
"What is the scope of an AWS Config rule?","An AWS Config rule is limited to evaluating resources within a single AWS region.","An AWS Config rule can be used to manage costs.","An AWS Config rule can prevent resources to be created.","An AWS Config rule is global and applies to all AWS accounts.","AWS Config rules are regional in scope, meaning they evaluate resources within a single AWS Region. To monitor globally you need aggregation."
"How does AWS Config integrate with AWS CloudTrail?","AWS Config uses CloudTrail logs to detect configuration changes.","AWS Config uses CloudTrail to manage IAM policies.","AWS Config stores its data in CloudTrail.","AWS Config automatically creates CloudTrail trails.","AWS Config uses CloudTrail logs to detect changes made to AWS resources. This allows Config to accurately track configuration changes and evaluate compliance."
"What is the purpose of the AWS Config console?","To visualise resource configurations and compliance status.","To manage EC2 instances.","To configure VPC settings.","To monitor application performance.","The AWS Config console provides a user-friendly interface for viewing resource configurations, compliance status, and other relevant information."
"Which of the following is NOT a benefit of using AWS Config?","Enhanced security auditing","Automated compliance checks","Simplified resource provisioning","Historical configuration tracking","AWS Config is designed for security, auditing, compliance, and historical tracking - not for simplifying resource provisioning."
"You need to demonstrate compliance with a specific security standard using AWS Config. What should you use?","Conformance Packs","GuardDuty","Inspector","Trusted Advisor","Conformance Packs are collections of Config rules and remediation actions that you can use to quickly deploy a compliance baseline."
"Which AWS service can be integrated with AWS Config to receive notifications about compliance changes?","SNS","CloudWatch","CloudTrail","SQS","Amazon SNS (Simple Notification Service) can be configured to receive notifications when the compliance status of your resources changes, allowing for real-time alerting."
"What type of data does AWS Config store about your resources?","Configuration items","CloudWatch metrics","CloudTrail logs","Billing data","AWS Config stores configuration items, which represent the configuration of a resource at a specific point in time."
"When evaluating resources, what is the possible compliance status in AWS Config?","Compliant or Non-compliant","Healthy or Unhealthy","Active or Inactive","Enabled or Disabled","AWS Config rules evaluate resources and determine whether they are Compliant (meeting the defined criteria) or Non-compliant."
"What is an advantage of using managed Config rules over custom rules?","Managed rules are pre-built and maintained by AWS.","Managed rules can be modified to suit specific needs.","Managed rules can evaluate resources in other AWS accounts.","Managed rules can be written in any programming language.","Managed rules are pre-built and maintained by AWS, saving you the time and effort of creating and maintaining your own rules."
"What is the function of the `aws configservice get-resource-config-history` command?","To retrieve the configuration history of a specific resource.","To create a new Config rule.","To delete a Config rule.","To start the Configuration Recorder.","The `get-resource-config-history` command retrieves the configuration changes for a specific resource over time, allowing you to track changes and troubleshoot issues."
"You want to ensure that all your S3 buckets are encrypted at rest. How can AWS Config help with this?","By using a Config rule that checks for S3 bucket encryption.","By automatically encrypting all new S3 buckets.","By preventing the creation of unencrypted S3 buckets.","By reporting the cost of S3 storage.","You can create a Config rule that evaluates whether your S3 buckets have encryption enabled. If any are found to be unencrypted, the rule will mark them as non-compliant."
"Which AWS service can you use to create custom remediation actions for AWS Config?","Systems Manager Automation","Lambda","CloudWatch Events","CloudFormation","Systems Manager Automation allows you to define and execute remediation actions in response to AWS Config rule violations."
"Which statement is true regarding the pricing of AWS Config?","AWS Config is priced based on the number of configuration items recorded.","AWS Config is a free service.","AWS Config is priced based on the number of Config rules you create.","AWS Config is priced based on the number of AWS accounts you monitor.","AWS Config pricing is based on the number of configuration items recorded. You pay for the continuous monitoring and recording of resource configurations."
"What does AWS Config use to define the desired state of a resource?","Configuration Items","CloudTrail logs","CloudWatch alarms","IAM policies","Configuration items represents the desired configuration state of resources being monitored by AWS Config."
"How can you ensure that all new EC2 instances are tagged with specific tags using AWS Config?","By using a Config rule to check for the required tags.","By creating a CloudWatch Event rule.","By using an IAM policy.","By enabling tagging on the EC2 service.","A Config rule can check if newly created EC2 instances have specific tags and mark them as non-compliant if they are missing."
"What is a 'configuration item relationship' in AWS Config?","A connection between two related AWS resources","A mapping between IAM roles and AWS resources","A record of changes made to a resource's configuration","A definition of the desired state of a resource","Configuration item relationships help you understand how different resources are related to each other, providing a more holistic view of your infrastructure."
"Which of these AWS services can AWS Config NOT monitor?","On-premise servers","EC2 Instances","S3 Buckets","VPC resources","AWS Config is designed to monitor AWS resources, so it does not have the ability to directly monitor resources hosted on-premise."
"You need to centrally manage AWS Config rules across multiple AWS accounts. Which feature should you use?","Organisations","IAM Roles","CloudWatch Events","AWS Trusted Advisor","AWS Organisations allows you to centrally manage AWS Config across multiple accounts."
"You want to receive real-time alerts when an AWS Config rule changes its compliance status. Which AWS service should you use?","SNS","CloudWatch Logs","CloudTrail","Inspector","SNS can be used to receive real-time notifications about changes in compliance status."
"What is the use of the AWS Config service role?","To grant AWS Config permissions to access and record configurations.","To grant users access to AWS Config.","To encrypt Config data.","To manage the Configuration Recorder.","The AWS Config service role provides Config with the necessary permissions to access and record the configurations of your AWS resources."
"How can you use AWS Config to ensure that all your EC2 instances are of a specific instance type?","Create a Config rule that checks the instance type.","Use CloudWatch to monitor instance types.","Use IAM policies to restrict instance types.","Use EC2 Instance Metadata to check the instance type.","You can create an AWS Config rule that validates that all EC2 instances are using a particular instance type."
"What is the relationship between AWS Config and AWS CloudFormation?","AWS Config can track changes made by CloudFormation.","CloudFormation can be used to create Config rules.","They are not related.","AWS Config is replaced by CloudFormation.","AWS Config can track changes made to resources deployed and managed by CloudFormation."
"How does AWS Config help with change management?","By providing a historical record of resource configuration changes.","By automatically deploying application updates.","By preventing unauthorised changes to resources.","By scheduling resource configuration changes.","AWS Config keeps a history of resource configuration changes, making it easier to track what changed, when, and by whom."
"What is the purpose of 'scope of changes' in an AWS Config rule definition?","To limit which resources the rule applies to.","To define how the rule will remediate non-compliant resources.","To define the alert message for non-compliance.","To specify the order in which rules are evaluated.","The 'scope of changes' defines the types of resources that the rule will evaluate. This allows you to apply the rule to specific resources."
"How can AWS Config help you reduce costs?","By identifying over-provisioned or unused resources.","By automatically scaling resources based on demand.","By optimising your network configuration.","By providing cost allocation tags.","By identifying over-provisioned resources or finding resources that do not comply with cost optimization rules, AWS Config helps with cost reduction."
"What type of input parameters can you define for custom AWS Config rules?","AWS CloudFormation Parameters","AWS Lambda Event Parameters","AWS Systems Manager Parameters","AWS CloudWatch Alarm Parameters","You can define input parameters for custom Config rules to make them more flexible and reusable."
"You want to use AWS Config to evaluate resources in a different AWS account. What is the first step?","Configure cross-account access using IAM roles.","Enable AWS Config in both accounts.","Create a VPC peering connection.","Set up a cross-region replication.","To evaluate resources in another account you must first set up cross-account access with IAM Roles to allow the Config service in the aggregator account to access the config data of the target account."
"What is the difference between a trigger type of 'Configuration Changes' and 'Periodic' in AWS Config?","'Configuration Changes' triggers evaluation when a resource changes; 'Periodic' triggers evaluation on a schedule.","'Configuration Changes' triggers evaluation only for S3 buckets; 'Periodic' triggers evaluation for all resources.","'Configuration Changes' triggers evaluation only for new resources; 'Periodic' triggers evaluation for existing resources.","'Configuration Changes' triggers evaluation daily; 'Periodic' triggers evaluation hourly.","'Configuration Changes' triggers evaluation whenever a configuration change happens, while 'Periodic' runs evaluation regularly based on a time period."
"Which is NOT a valid trigger type for AWS Config rules?","Periodic","Configuration Items","Configuration Changes","Scheduled","AWS Config does not have a 'Scheduled' trigger type. Config rules can be triggered by periodic evaluations or by configuration changes."
"What is the key difference between AWS Config and AWS Trusted Advisor?","AWS Config provides continuous monitoring, while Trusted Advisor provides on-demand checks.","AWS Config optimises infrastructure costs, while Trusted Advisor manages security.","AWS Config manages security groups, while Trusted Advisor manages EC2 instances.","AWS Config manages compliance, while Trusted Advisor optimises performance.","AWS Config offers continuous monitoring for configuration changes, while Trusted Advisor provides periodic checks to assist with cost optimisations and best practices."
"How can you integrate AWS Config with a third-party compliance tool?","By exporting configuration data to the third-party tool.","By creating a custom Config rule using the third-party tool's API.","By using CloudWatch Events to trigger actions in the third-party tool.","By granting the third-party tool access to your AWS account.","The configuration data can be exported from AWS Config and consumed by third-party tools."
"What is the primary benefit of using AWS Config with AWS Organisations?","Centralised management of configuration and compliance across multiple accounts.","Automated creation of AWS accounts.","Simplified billing for AWS resources.","Increased security for AWS resources.","AWS Organisations allows you to centrally manage AWS Config across multiple accounts."
"What is the format of the configuration data that AWS Config stores?","JSON","XML","CSV","YAML","AWS Config stores data in JSON (JavaScript Object Notation) format."
"Which AWS service is typically used alongside AWS Config to automate responses to non-compliant resources?","AWS Systems Manager Automation","Amazon CloudWatch","AWS CloudTrail","AWS IAM","AWS Systems Manager Automation allows you to automate remediation of non-compliant resource configurations."
"What is the primary purpose of AWS Config conformance packs?","To deploy a pre-defined set of Config rules and remediation actions.","To visualise compliance data in a dashboard.","To encrypt configuration data at rest.","To automate the creation of AWS Config rules.","Conformance packs are pre-defined sets of Config rules and remediation actions that can be deployed together to quickly establish a compliance baseline."
"Which is NOT a valid AWS Config resource type?","AWS::EC2::Instance","AWS::S3::Bucket","AWS::IAM::Role","AWS::SNS::Topic","All listed are valid resources supported by AWS config."
"What happens when a resource is deleted in AWS, but AWS Config is still configured to monitor it?","AWS Config marks the resource as 'deleted' and retains the configuration history.","AWS Config automatically deletes the Config rule associated with the resource.","AWS Config creates a new resource with the same configuration.","AWS Config stops recording configuration changes until the resource is recreated.","AWS Config marks the resource as 'deleted' but keeps the configuration history, allowing you to track past configurations even for deleted resources."
"What is a potential consequence of configuring AWS Config to record all resource types in a large AWS environment?","Increased storage costs.","Improved security posture.","Reduced network latency.","Simplified resource provisioning.","Recording all resource types increases the number of configuration items stored, leading to higher storage costs."
"In AWS Config, what is a 'Configuration Recorder'?","A service that detects changes in your AWS environment.","A tool for creating AWS CloudFormation templates.","A mechanism for encrypting your configuration data.","A system for managing IAM policies across multiple accounts.","The Configuration Recorder detects changes in your AWS environment by periodically examining the configuration of your resources."
"What type of resource changes can AWS Config track?","Creation, modification, and deletion.","Only creation and deletion.","Only modification.","Only creation.","AWS Config tracks all changes including the creation, modification and deletion of resources."
"What is the primary purpose of AWS Config Rules?","To evaluate whether your AWS resources comply with your desired configurations.","To automatically create and manage AWS resources.","To monitor the performance of your AWS applications.","To provide a centralized logging solution for your AWS account.","Config Rules allow you to define desired configurations and continuously evaluate resource compliance."
"Which of the following is a benefit of using AWS Config?","Improved security and compliance.","Reduced compute costs.","Automated application deployments.","Simplified network management.","AWS Config helps improve security and compliance by continuously monitoring and assessing resource configurations."
"What is the function of an AWS Config conformance pack?","A collection of AWS Config rules and remediation actions that can be deployed as a single entity.","A tool for generating AWS CloudFormation templates from existing infrastructure.","A service for automatically patching EC2 instances.","A mechanism for encrypting data at rest in S3.","Conformance packs bundle together rules and remediation actions, making it easier to manage compliance across your organisation."
"How does AWS Config help with auditing?","By providing a detailed history of resource configurations.","By automatically creating security groups.","By providing real-time monitoring of network traffic.","By automatically backing up your data to S3.","AWS Config helps with auditing by keeping a historical record of resource configurations, allowing you to track changes over time."
"What is the purpose of AWS Config Aggregator?","To collect configuration and compliance data from multiple AWS accounts and Regions.","To automatically remediate non-compliant resources.","To create custom AWS Config rules.","To encrypt configuration data at rest.","The Config Aggregator centralises configuration data from multiple accounts and Regions, simplifying compliance monitoring."
"What is an example of a managed AWS Config Rule?","`s3-bucket-public-read-prohibited`","`ec2-instance-no-public-ip`","`iam-password-policy`","`elb-listener-https-only`","Managed rules are pre-defined rules provided by AWS, such as `s3-bucket-public-read-prohibited`."
"Which AWS service can you integrate with AWS Config to automatically remediate non-compliant resources?","AWS Systems Manager Automation.","AWS CloudTrail.","AWS CloudWatch.","Amazon Inspector.","AWS Systems Manager Automation can be integrated to automatically remediate resources that are found to be non-compliant by AWS Config rules."
"What type of AWS Config Rule can be triggered by changes in resource configurations?","Configuration change-triggered rule.","Periodic rule.","Manual rule.","Event-driven rule.","Configuration change-triggered rules are triggered by changes in resource configurations, allowing for real-time compliance checks."
"What is the minimum required permission for AWS Config to record the configuration of AWS resources in your account?","`AWSConfigRole`","`AWSConfigFullAccess`","`ReadOnlyAccess`","`AdministratorAccess`","`AWSConfigRole` provides the necessary permissions for AWS Config to record configurations."
"How does AWS Config store the configuration information for your AWS resources?","As configuration items (CIs) in an S3 bucket.","As encrypted data in DynamoDB.","As JSON files in EBS volumes.","As snapshots in Glacier.","AWS Config stores the configuration information as configuration items (CIs) in an S3 bucket."
"Which of the following is NOT a benefit of using AWS Config?","Cost estimation for infrastructure changes.","Compliance monitoring.","Security assessment.","Troubleshooting and auditing.","AWS Config is not used for cost estimations, its main focus is compliance, security, auditing and troubleshooting using the existing configuration."
"When evaluating a custom AWS Config Rule, what type of input is provided to the AWS Lambda function?","Configuration item.","CloudWatch Logs.","CloudTrail events.","VPC Flow Logs.","The Lambda function receives a configuration item as input, which contains the resource's configuration details."
"What is the purpose of the `EvaluationMode` parameter when creating an AWS Config Rule?","To specify whether the rule should evaluate resources proactively or reactively.","To specify the priority of the rule.","To specify the Region where the rule should be applied.","To specify the maximum number of resources the rule can evaluate.","The `EvaluationMode` parameter determines whether the rule is evaluated proactively (DETECTIVE) or reactively (REACTIVE)."
"In AWS Config, what is the difference between a managed rule and a custom rule?","Managed rules are pre-defined by AWS, while custom rules are created by the user.","Managed rules are automatically remediated, while custom rules require manual remediation.","Managed rules are free, while custom rules incur additional charges.","Managed rules can only be applied to specific resource types, while custom rules can be applied to any resource type.","Managed rules are pre-defined by AWS and are ready to use, custom rules are created by the user and are typically backed by a Lambda function."
"Which AWS service is commonly used to write custom AWS Config Rules?","AWS Lambda.","AWS CloudFormation.","AWS CloudWatch.","Amazon SQS.","Custom AWS Config Rules are typically written using AWS Lambda functions."
"You want to use AWS Config to track changes to your VPC security groups. What do you need to ensure is configured?","Configuration recorder is configured to record security groups.","AWS CloudTrail is enabled.","VPC Flow Logs are enabled.","Network ACLs are configured.","The configuration recorder needs to be configured to record the resource types you want to track, including security groups."
"What is the purpose of a 'Remediation Action' in AWS Config?","To automatically fix non-compliant resources.","To generate compliance reports.","To notify administrators of non-compliant resources.","To encrypt configuration data at rest.","Remediation actions are used to automatically fix resources that are found to be non-compliant by AWS Config rules."
"Which AWS Config feature allows you to assess the overall compliance status of your AWS environment against a set of standards?","Conformance Packs.","Configuration Recorders.","Config Aggregators.","Config Rules.","Conformance Packs are designed to help assess the overall compliance status against pre-defined standards."
"Which of the following is NOT a supported trigger type for AWS Config Rules?","Schedule-based.","Configuration change-based.","SNS notification-based.","Manual invocation.","AWS Config Rules are not triggered by SNS notifications; they are triggered by configuration changes, schedules, or manual invocation."
"What is the role of the IAM service role associated with the AWS Config service?","To grant AWS Config permissions to access and record configurations of AWS resources.","To grant users access to AWS Config data.","To encrypt AWS Config data at rest.","To configure AWS Config settings.","The IAM service role grants AWS Config the necessary permissions to access and record the configurations of AWS resources."
"What is the purpose of the `DescribeConfigRules` API call in AWS Config?","To retrieve information about existing Config Rules.","To create a new Config Rule.","To delete a Config Rule.","To update a Config Rule.","The `DescribeConfigRules` API call is used to retrieve information about existing Config Rules."
"How can you use AWS Config to track changes made to IAM policies?","Configure the Configuration Recorder to track IAM policies.","Enable AWS CloudTrail and correlate events.","Enable VPC Flow Logs.","Configure Amazon Inspector.","The Configuration Recorder needs to be configured to track IAM policies in order for Config to record and track changes."
"You want to deploy a set of AWS Config Rules across multiple AWS accounts in your organisation. What is the recommended approach?","Use Conformance Packs with AWS Organizations.","Manually deploy the rules in each account.","Use AWS CloudFormation StackSets.","Use AWS Lambda to deploy the rules.","Conformance Packs, combined with AWS Organizations, provides a way to centrally manage and deploy rules across multiple accounts."
"Which AWS service can be used to visualise the configuration changes tracked by AWS Config?","AWS CloudTrail Insights.","AWS CloudWatch Dashboards.","AWS X-Ray.","AWS Trusted Advisor.","AWS Config data can be visualised in CloudWatch Dashboards using the output of config rules."
"What is the maximum number of AWS Config Rules that can be associated with a single AWS resource?","There is no limit.","10.","50.","100.","There is no explicit limit, but practicality and performance may impose constraints."
"What is the purpose of the AWS Config rule parameter `MaximumExecutionFrequency`?","To control how often AWS Config evaluates the rule.","To control the maximum cost of executing the rule.","To control the maximum number of resources the rule evaluates.","To control the maximum duration of the rule execution.","`MaximumExecutionFrequency` limits how often the rule runs."
"What does the AWS Config timeline view show?","A chronological history of configuration changes for a specific resource.","Real-time CPU usage for all EC2 instances.","A list of all AWS services used in your account.","A summary of all security findings in your account.","The timeline view shows a chronological history of changes to a specific resource's configuration."
"Which data feed can be used for AWS Config to deliver configuration changes?","Amazon Simple Notification Service (SNS) topic.","Amazon Simple Queue Service (SQS) queue.","Amazon Kinesis Data Stream.","AWS CloudTrail log file.","AWS Config uses SNS topics to deliver configuration change notifications."
"Which AWS service is used for logging API calls made to AWS Config?","AWS CloudTrail.","AWS CloudWatch Logs.","Amazon S3 Access Logs.","AWS Config itself.","CloudTrail logs API calls made to AWS Config, providing an audit trail."
"What is the purpose of the AWS Config `RecorderStatus`?","To indicate whether the Configuration Recorder is running or stopped.","To indicate the compliance status of resources.","To indicate the health of the AWS Config service.","To indicate the number of resources being recorded.","The `RecorderStatus` indicates the operational state of the Configuration Recorder."
"You want to ensure that all new EC2 instances launched in your AWS account are tagged with a specific key. How can you achieve this using AWS Config?","Create a custom Config Rule that checks for the required tag.","Use AWS CloudTrail to monitor EC2 instance launches.","Enable mandatory tagging through IAM policies.","Use AWS Systems Manager Automation to apply the tag.","A custom Config Rule can be created to check that each launched EC2 Instance has the correct tags."
"Which of the following is a use case for AWS Config Conformance Packs?","Deploying a pre-defined set of compliance rules across an organisation.","Creating a single compliance report for all AWS resources.","Automatically remediating non-compliant resources.","Encrypting data at rest in S3.","Conformance Packs are ideal for deploying pre-defined compliance rules consistently across an organisation."
"You need to evaluate the configuration of resources that existed before you enabled AWS Config. What feature can you use?","AWS Config Historical Inventory.","AWS CloudTrail Event History.","AWS Trusted Advisor.","Amazon Inspector.","AWS Config can evaluate resources that existed before it was enabled using its Historical Inventory."
"What type of changes does AWS Config not record by default?","Operating system changes within an EC2 instance.","Security group rule changes.","S3 bucket policy changes.","IAM role policy changes.","AWS Config does not record operating system changes within an EC2 instance by default."
"You are using AWS Config to monitor the compliance of your S3 buckets. Which compliance status will Config report if a bucket's policy allows public read access?","Non-compliant.","Compliant.","Indeterminate.","Pending Evaluation.","If a bucket allows public read access and the corresponding config rule forbids it, it will be Non-compliant."
"What is the benefit of using AWS Config with AWS Organizations?","Centralised management of compliance across multiple AWS accounts.","Automated patching of EC2 instances in all accounts.","Simplified network configuration across all accounts.","Centralised billing for all AWS services.","AWS Config and AWS Organizations allows for a central point of managing compliance across multiple AWS accounts."
"What is the role of the `Scope` parameter in an AWS Config Rule?","To specify the resources that the rule applies to.","To specify the Region where the rule should be evaluated.","To specify the AWS account where the rule should be applied.","To specify the IAM role that the rule should use.","The `Scope` parameter defines the resources to which the rule is applied. It is one of the key defining aspects of any config rule."
"You want to receive notifications when an AWS Config rule detects a non-compliant resource. What service can you integrate with AWS Config for this purpose?","Amazon Simple Notification Service (SNS).","AWS CloudWatch Alarms.","Amazon Simple Queue Service (SQS).","AWS Lambda.","AWS Config can send compliance notifications to SNS topics."
"Which of the following actions can you take to reduce the cost of using AWS Config?","Reduce the frequency of configuration recording.","Disable CloudTrail logging.","Use a lower-cost storage class for S3.","Delete unused IAM roles.","Reducing the frequency of Configuration recording reduces the number of snapshots being created and reduces cost."
"How can you ensure that AWS Config records the configuration of newly created resources automatically?","Ensure the Configuration Recorder is configured to record all supported resource types.","Enable AWS CloudTrail.","Configure Amazon CloudWatch Events.","Enable Enhanced Monitoring on EC2 instances.","Ensure the Configuration Recorder is configured to record all supported resource types. Without this config is unable to detect newly created resources."
"What AWS service can be used to create a dashboard showing the compliance status of your AWS resources based on AWS Config data?","AWS CloudWatch.","AWS Trusted Advisor.","AWS CloudTrail.","Amazon Inspector.","CloudWatch can create dashboards based on AWS Config data using the results of Config Rules."
"What is the purpose of the `RevaluationStatus` parameter in AWS Config?","To indicate whether a rule needs to be re-evaluated.","To indicate the last time the rule was successfully evaluated.","To indicate the compliance status of the resources evaluated by the rule.","To indicate the number of resources that were evaluated by the rule.","RevaluationStatus indicates if a rule needs to be re-evaluated. You would likely encounter this when you modify the config rule."
"How does AWS Config support continuous compliance?","By continuously monitoring and evaluating resource configurations against defined rules.","By automatically backing up your data to S3.","By providing real-time monitoring of network traffic.","By automatically creating security groups.","The primary function of AWS Config is to provide continuous monitoring and evaluation of resource configurations."
"Which of the following is a valid use case for AWS Config?","Enforcing tagging standards across all resources in your AWS account.","Automatically scaling your EC2 instances based on CPU utilisation.","Monitoring the performance of your database queries.","Backing up your data to S3.","AWS Config can monitor for missing tags in AWS Resources."
"What is the primary function of AWS Config?","Continuous assessment and auditing of AWS resource configurations","Real-time threat detection and response","Application performance monitoring and optimisation","Centralised log management and analysis","AWS Config continuously monitors and records the configuration of your AWS resources, allowing for compliance auditing, security analysis, and change management."
"Which of the following AWS Config components defines the desired configuration state for a resource?","Configuration Rule","Configuration Item","Configuration Recorder","Delivery Channel","A Configuration Rule defines the desired configuration state for a resource and evaluates whether the resource complies with that configuration."
"Which AWS service can be integrated with AWS Config to automatically remediate non-compliant resources?","AWS Systems Manager","AWS CloudWatch","AWS CloudTrail","Amazon S3","AWS Systems Manager Automation documents can be used in conjunction with AWS Config to automatically remediate non-compliant resources, bringing them back into compliance."
"Which statement about AWS Config Rules is true?","They can be triggered by configuration changes or on a periodic basis","They can only be triggered by user actions","They are limited to monitoring only EC2 instances","They cannot be customised","AWS Config Rules can be triggered by configuration changes detected by AWS Config or on a periodic schedule, allowing for both reactive and proactive compliance monitoring."
"What is the purpose of the AWS Config Configuration Recorder?","To record the configuration history of your AWS resources","To encrypt configuration data","To visualise resource relationships","To manage access control for configuration data","The Configuration Recorder is responsible for tracking the configuration changes of your AWS resources over time, providing a history of configurations for auditing and compliance purposes."
"Which AWS Config component is used to receive notifications about configuration changes?","Delivery Channel","Configuration Recorder","Configuration Rule","Aggregator","The Delivery Channel specifies where AWS Config sends configuration snapshots and configuration change notifications, typically an S3 bucket and optionally an SNS topic."
"Which of the following is a benefit of using AWS Config?","Improved security posture through continuous compliance monitoring","Reduced cost of compute resources","Faster deployment of applications","Automated scaling of databases","AWS Config helps improve your security posture by continuously monitoring the configuration of your AWS resources and identifying any deviations from your desired state."
"Which type of rule is an AWS Config Managed Rule?","A pre-defined, ready-to-use rule provided by AWS","A custom rule created and managed by the user","A rule that automatically remediates non-compliant resources","A rule that integrates with third-party security tools","AWS Config Managed Rules are pre-defined rules provided by AWS that address common compliance and security best practices, making it easier to get started with AWS Config."
"What is the function of the AWS Config Aggregator?","To collect configuration data from multiple AWS accounts and regions","To encrypt configuration data at rest","To generate compliance reports","To automatically remediate non-compliant resources","The Aggregator allows you to centrally collect configuration data from multiple AWS accounts and regions into a single AWS Config instance, providing a consolidated view of your resource configurations."
"Which AWS service is commonly used to store AWS Config configuration snapshots?","Amazon S3","Amazon EBS","Amazon RDS","Amazon EC2","AWS Config configuration snapshots, which contain the detailed configuration of your resources, are typically stored in an Amazon S3 bucket."
"You want to use AWS Config to assess the compliance of your EC2 instances against a specific security standard. Which AWS Config feature should you use?","Config Rules","Config Aggregator","Config Recorder","Delivery Channel","AWS Config Rules allow you to define and evaluate the compliance of your resources against specific standards or policies, providing a way to continuously monitor your security posture."
"How does AWS Config contribute to change management?","By providing a history of resource configuration changes","By automating the deployment of new resources","By preventing unauthorised changes to resources","By optimising resource utilisation","AWS Config provides a history of resource configuration changes, enabling you to track changes over time, identify the root cause of issues, and manage configuration drift."
"Which AWS service does AWS Config rely on to deliver configuration change notifications?","Amazon SNS","Amazon SQS","Amazon CloudWatch Events","AWS CloudTrail","AWS Config uses Amazon SNS (Simple Notification Service) to deliver notifications about configuration changes to subscribed endpoints, allowing you to react to changes in real-time."
"What is a Configuration Item in AWS Config?","A record of the configuration state of a specific AWS resource at a point in time","A rule that defines the desired configuration state for a resource","A channel for delivering configuration change notifications","A summary report of compliance status","A Configuration Item (CI) represents the configuration state of a specific AWS resource at a particular point in time, capturing its attributes and relationships."
"Which of the following is a use case for AWS Config Rules?","Enforcing tagging standards for EC2 instances","Automatically scaling DynamoDB tables","Optimising network traffic flow","Managing IAM policies","AWS Config Rules can be used to enforce tagging standards, ensuring that all EC2 instances are properly tagged for cost allocation, compliance, or other organisational purposes."
"What is the relationship between AWS Config and AWS CloudTrail?","AWS Config records resource configuration, while CloudTrail logs API activity","AWS Config logs API activity, while CloudTrail records resource configuration","Both services perform the same function","AWS Config replaces CloudTrail","AWS Config records the configuration of your AWS resources, while CloudTrail logs API calls made to your AWS account, providing complementary information for security and auditing."
"Which of the following is NOT a resource type that AWS Config can track?","On-premises servers","EC2 instances","S3 buckets","IAM roles","AWS Config is designed to track the configuration of AWS resources. It cannot directly track on-premises servers unless they are managed as AWS resources via services like AWS Systems Manager."
"What IAM permission is required for an AWS Config role to record configuration changes?","config:DeliverConfigSnapshot","config:PutConfigRecorder","config:StartConfigurationRecorder","config:GetResourceConfigHistory","The IAM role used by AWS Config needs permissions to record the configuration of resources, including `config:StartConfigurationRecorder` to initiate the recording process."
"You have configured AWS Config to record changes to your S3 buckets. What type of information will be captured?","Bucket name, access policies, and encryption settings","Data stored in the buckets","Logs of access to the buckets","Number of objects in the buckets","AWS Config will capture details about the bucket's configuration, such as its name, access policies, encryption settings, and other metadata, but not the actual data stored within the buckets."
"Which AWS Config component allows you to define which resource types will be recorded?","Configuration Recorder","Delivery Channel","Configuration Rule","Aggregator","The Configuration Recorder is responsible for specifying which resource types AWS Config should monitor and record configuration changes for."
"What is the purpose of the `aws configservice get-resource-config-history` CLI command?","To retrieve the configuration history for a specific resource","To start the configuration recorder","To create a new configuration rule","To deliver a configuration snapshot","The `aws configservice get-resource-config-history` command is used to retrieve the historical configuration data for a particular AWS resource tracked by AWS Config."
"How can you use AWS Config to determine which security groups allow unrestricted access (0.0.0.0/0) to SSH (port 22)?","By creating a custom Config Rule that checks security group rules","By using AWS Trusted Advisor","By manually reviewing each security group","By analysing CloudTrail logs","You can create a custom AWS Config Rule that evaluates each security group's ingress rules and flags those that allow unrestricted access to SSH or other sensitive ports."
"What is the purpose of AWS Config conformance packs?","To deploy a collection of AWS Config rules and remediation actions as a single unit","To encrypt configuration data at rest","To automate the creation of AWS Config recorders","To visualise resource dependencies","Conformance packs allow you to package and deploy a collection of AWS Config rules and related remediation actions, simplifying the deployment of compliance frameworks across your AWS environment."
"Which AWS service can be used to visualise the relationships between resources recorded by AWS Config?","AWS X-Ray","AWS CloudFormation","AWS Trusted Advisor","AWS Resource Groups","AWS Config provides a visualisation of resource relationships, but it doesn't directly integrate with another service to visualise these dependencies. AWS X-Ray is used for tracing requests through microservices."
"Which of the following statements about AWS Config custom rules is true?","They are written in Lambda functions","They can only be triggered by periodic evaluations","They are limited to a predefined set of evaluation criteria","They cannot be used to evaluate resources in other AWS accounts","AWS Config custom rules are implemented as Lambda functions, allowing you to define custom evaluation logic for assessing the compliance of your AWS resources."
"What happens if an AWS Config rule evaluates a resource as non-compliant?","AWS Config flags the resource as non-compliant","The resource is automatically deleted","The resource is automatically remediated","The rule is automatically disabled","When an AWS Config rule evaluates a resource as non-compliant, AWS Config simply flags the resource as non-compliant, providing visibility into potential compliance issues. Remediation may require manual intervention or the use of automated remediation actions."
"You need to ensure that all new EC2 instances launched in your account are encrypted. How can AWS Config help with this?","By creating a Config Rule that checks if EBS volumes are encrypted","By preventing the launch of unencrypted instances","By automatically encrypting existing instances","By monitoring CloudTrail logs for instance launch events","You can create an AWS Config Rule that checks whether the EBS volumes attached to newly launched EC2 instances are encrypted, providing ongoing monitoring of encryption status."
"Which action is NOT typically performed by AWS Config?","Enforcing resource tagging standards","Monitoring CPU utilisation of EC2 instances","Detecting changes to security group rules","Tracking the configuration of S3 bucket policies","AWS Config primarily focuses on tracking and evaluating resource configurations and their compliance. Monitoring real-time CPU utilisation is typically done with CloudWatch."
"What is the primary benefit of using AWS Config with AWS Organizations?","Centralised compliance management across multiple AWS accounts","Automated remediation of non-compliant resources","Reduced cost of AWS Config usage","Increased performance of AWS resources","Integrating AWS Config with AWS Organizations allows you to centrally manage compliance across all accounts within your organisation, providing a consolidated view of your security posture."
"You want to receive an alert whenever an AWS Config rule detects a non-compliant resource. How can you achieve this?","By configuring an SNS topic for the Config rule's delivery channel","By creating a CloudWatch alarm based on Config rule evaluation results","By enabling CloudTrail logging for Config rule evaluations","By subscribing to the AWS Config console","You can configure an SNS topic in the delivery channel to receive notifications about configuration changes and rule evaluations. Then, you can create a CloudWatch alarm based on Config rule evaluation results to trigger alerts for non-compliant resources."
"Which of the following is a key limitation of AWS Config?","It cannot directly monitor on-premises resources without integrations","It cannot track changes to IAM policies","It cannot evaluate resources in other AWS regions","It cannot integrate with third-party security tools","AWS Config's primary focus is on AWS resources and it cannot directly monitor on-premises infrastructure without the aid of AWS partner solutions."
"Which AWS service complements AWS Config by providing detailed API call history?","AWS CloudTrail","AWS CloudWatch","AWS IAM","AWS Trusted Advisor","AWS CloudTrail records API calls made to your AWS account, which provides a record of who did what and when, complementing AWS Config's focus on resource configuration."
"What type of action can be triggered by an AWS Config remediation?","Automated changes to resource configurations","Automatic deletion of non-compliant resources","Blocking access to non-compliant resources","Sending notifications to security personnel","AWS Config remediations, when integrated with services like AWS Systems Manager Automation, can automatically adjust resource configurations to bring them back into compliance."
"You need to generate a report showing the compliance status of your AWS resources against a specific standard (e.g., PCI DSS). How can AWS Config assist you?","By using conformance packs and generating compliance reports","By exporting configuration data to a third-party reporting tool","By manually reviewing the results of Config rule evaluations","By integrating with AWS Trusted Advisor","AWS Config conformance packs are designed to simplify the deployment of common compliance frameworks and generate reports showing the compliance status of your resources against those standards."
"Which of the following best describes the purpose of the 'aws configservice describe-config-rules' CLI command?","To retrieve information about existing AWS Config rules","To create a new AWS Config rule","To delete an existing AWS Config rule","To evaluate AWS Config rules against a resource","The `aws configservice describe-config-rules` CLI command is used to retrieve detailed information about the AWS Config rules defined in your AWS account, including their configuration and status."
"What is the role of the AWS Config service-linked role?","To grant AWS Config permissions to access and manage your resources","To control user access to AWS Config","To encrypt configuration data","To automate the deployment of AWS Config rules","The AWS Config service-linked role provides AWS Config with the necessary permissions to access and manage your AWS resources on your behalf, enabling it to record configuration changes and evaluate compliance."
"Which AWS Config feature is best suited for identifying resources that are missing required tags?","A custom Config Rule with a Lambda function","A managed Config Rule focused on cost optimisation","AWS Trusted Advisor's cost optimisation checks","AWS CloudWatch alarms based on tagging metrics","A custom Config Rule with a Lambda function is the most flexible and precise way to identify resources that are missing required tags, allowing you to enforce tagging policies."
"How does AWS Config relate to the principle of Infrastructure as Code (IaC)?","It provides visibility and auditability for configuration changes made by IaC tools","It replaces the need for IaC tools","It automates the deployment of IaC templates","It encrypts IaC templates at rest","AWS Config enhances IaC by providing a continuous view of resource configurations and detecting any deviations from the desired state defined in your IaC templates."
"You want to use AWS Config to monitor the configuration of your VPCs. Which resource type should you specify in the Configuration Recorder?","AWS::EC2::VPC","AWS::EC2::Subnet","AWS::EC2::SecurityGroup","AWS::EC2::InternetGateway","To monitor the configuration of your VPCs, you need to specify the `AWS::EC2::VPC` resource type in the Configuration Recorder, which tells AWS Config to track changes to your VPCs."
"Which AWS Config component provides information on relationships between AWS resources?","Relationships within the Configuration Item","Delivery Channel","Configuration Recorder","Configuration Rule","The Relationships section in the Configuration Item indicates how a resource is related to other AWS resources."
"Which of the following is NOT a valid evaluation mode for AWS Config rules?","On change","On schedule","On demand","On creation","AWS Config rules typically evaluate resources based on changes to their configuration ('on change') or on a periodic schedule ('on schedule'). 'On demand' and 'on creation' are not recognised evaluation modes."
"How can you centrally manage AWS Config rules across multiple accounts in your organisation?","Using AWS Organizations and a centralised Config Aggregator","Using AWS CloudFormation StackSets to deploy rules to each account","By manually configuring each account separately","By using AWS IAM roles to delegate access","Using AWS Organizations and a Config Aggregator allows for the central collection and management of configuration data and rules across multiple AWS accounts."
"What is the main benefit of using AWS Config remediation actions?","Automated correction of non-compliant resources","Real-time threat detection","Faster deployment of new resources","Reduced cost of compute resources","The primary benefit of remediation actions is the automated correction of non-compliant resources, ensuring that your environment remains in a desired state."
"Which AWS service can be used to create and manage custom AWS Config rules?","AWS Lambda","AWS CloudWatch","AWS CloudTrail","AWS IAM","AWS Lambda is used to create the custom logic for evaluating the compliance of resources within custom AWS Config rules."
"Which AWS service is NOT directly integrated with AWS Config for standard functionality?","AWS Systems Manager","AWS CloudTrail","AWS CloudWatch","AWS IAM","While AWS Config uses AWS IAM for permissions, it doesn't directly integrate with it for standard functionality like configuration tracking or remediation."
"What type of data does AWS Config NOT capture about your AWS resources?","Network traffic flowing through EC2 instances","Configuration settings and attributes","Relationships with other AWS resources","Creation and modification timestamps","AWS Config focuses on capturing the configuration settings and attributes of your resources, but it does not directly capture network traffic data."
"What is the primary purpose of AWS Config?","To assess, audit, and evaluate the configurations of your AWS resources","To monitor network traffic in your VPC","To manage user access and permissions","To automatically scale compute resources","AWS Config provides a detailed view of the configuration of AWS resources in your account and how they relate to each other. It can be used to assess, audit, and evaluate configurations."
"Which type of resource is NOT supported by AWS Config?","On-premises servers","EC2 instances","S3 buckets","RDS databases","AWS Config primarily supports AWS resources. It cannot directly track the configuration of on-premises servers without additional integration."
"What is an AWS Config rule?","A pre-defined or custom rule that evaluates the configuration of your AWS resources","A script that automates the creation of AWS resources","A network security policy","A tool for monitoring CPU utilisation","AWS Config rules represent the desired configuration state of resources and evaluate resources to determine whether they comply with the rule."
"What is the function of an AWS Config aggregator?","Collects configuration and compliance data from multiple AWS accounts and regions","Creates automated backups of AWS Config data","Encrypts AWS Config data at rest","Sends notifications for configuration changes","An aggregator collects configuration and compliance data from multiple AWS accounts and regions, providing a central view."
"Which AWS service can be integrated with AWS Config to automatically remediate non-compliant resources?","AWS Systems Manager","Amazon CloudWatch","AWS CloudTrail","AWS IAM","AWS Systems Manager can be used with AWS Config to automatically remediate non-compliant resources, bringing them back into compliance."
"In AWS Config, what is a 'configuration item'?","A record of the configuration of a specific AWS resource at a point in time","A list of users and their permissions","A log of network traffic","A snapshot of EBS volume data","A configuration item represents the configuration of a specific AWS resource at a point in time. It includes details like resource attributes, relationships, and current configuration."
"What is the purpose of the AWS Config service-linked role?","To grant AWS Config permissions to access resources in your account","To grant users access to AWS Config","To restrict AWS Config's access to certain resources","To encrypt data in AWS Config","A service-linked role is a special type of IAM role that is pre-defined by AWS and grants AWS Config the necessary permissions to access resources in your account and perform actions on your behalf."
"How does AWS Config help with compliance?","By continuously monitoring and recording resource configurations against defined rules","By automatically patching EC2 instances","By preventing DDoS attacks","By providing encryption keys","AWS Config helps with compliance by providing a detailed view of resource configurations and automatically evaluating those configurations against defined rules and standards."
"Which of the following is a benefit of using AWS Config?","Improved security posture through continuous monitoring","Reduced cost of EC2 instances","Increased network bandwidth","Faster database performance","AWS Config helps improve the security posture of your AWS environment by providing continuous monitoring and alerting on configuration changes that deviate from established standards."
"What is a 'Conformance Pack' in AWS Config?","A collection of AWS Config rules and remediation actions that can be deployed as a single entity","A set of pre-configured CloudWatch alarms","A collection of IAM policies","A pre-built network configuration","A Conformance Pack is a collection of AWS Config rules and remediation actions that can be deployed as a single entity to ensure consistent governance across your AWS environment."
"How does AWS Config relate to AWS CloudTrail?","AWS Config records configuration changes, while CloudTrail records API calls","AWS Config records API calls, while CloudTrail records configuration changes","They both record network traffic","They both manage IAM permissions","AWS Config focuses on recording the configuration of AWS resources, while CloudTrail focuses on recording API calls made to AWS services."
"Which AWS service allows you to view the historical configurations recorded by AWS Config?","AWS Config console","Amazon CloudWatch Logs","AWS CloudTrail console","AWS Systems Manager Inventory","The AWS Config console provides a user interface to view the historical configurations recorded by the service."
"Can you use AWS Config to track changes to IAM policies?","Yes, IAM policies are supported resources in AWS Config","No, IAM policies are not supported","Only default IAM policies are supported","Only IAM policies attached to EC2 instances are supported","AWS Config supports tracking changes to IAM policies, allowing you to monitor who has access to what resources and when those permissions change."
"What happens when an AWS Config rule evaluates a resource as non-compliant?","AWS Config generates a compliance event and can trigger remediation actions","The resource is automatically terminated","The user who created the resource is notified","The resource is moved to a quarantine zone","When a resource is non-compliant, AWS Config generates a compliance event that can be used to trigger notifications, alerts, or automated remediation actions."
"Which of the following is a custom rule type in AWS Config?","Lambda function-backed rule","Managed rule","AWS Marketplace rule","AWS Trusted Advisor rule","A custom rule in AWS Config is backed by a Lambda function, which allows you to define your own custom logic to evaluate resource configurations."
"Which of the following is a managed rule type in AWS Config?","A pre-defined rule provided by AWS","A rule that runs only on a schedule","A rule that is automatically created for all resources","A rule defined using CloudFormation","A managed rule is a pre-defined rule provided by AWS that can be used to evaluate common configuration requirements."
"What action does AWS Config take when a change occurs to a monitored resource?","It records the new configuration of the resource and evaluates it against applicable rules","It automatically reverts the resource to its previous configuration","It sends an email notification to the AWS account owner","It pauses the resource","When a change occurs, AWS Config records the new configuration of the resource as a configuration item and then evaluates the resource against any applicable Config rules to determine compliance."
"What is the cost model for AWS Config?","Pay-per-configuration-item-recorded and per rule evaluation","Pay-per-CPU-utilisation","Pay-per-network-bandwidth","Free for all AWS accounts","AWS Config is priced based on the number of configuration items recorded and the number of rule evaluations performed."
"What kind of changes can be tracked using AWS Config?","Changes to resource configurations and relationships","Changes to network latency","Changes to user login attempts","Changes to the weather","AWS Config tracks changes to the configuration of supported AWS resources, as well as the relationships between them."
"If you need to track the configuration of resources in multiple AWS accounts, how can you achieve this with AWS Config?","Use AWS Config aggregators to centralise the data","Create separate AWS Config rules for each account","Use AWS CloudTrail to track the configurations","Manually copy the configurations from each account","AWS Config aggregators allow you to collect configuration and compliance data from multiple AWS accounts and regions into a central location, simplifying governance and compliance management."
"You want to be notified when an AWS Config rule detects a non-compliant resource. Which AWS service can you integrate with AWS Config to achieve this?","Amazon SNS","AWS CloudWatch Logs","AWS IAM","Amazon SQS","Amazon SNS (Simple Notification Service) can be integrated with AWS Config to send notifications when a Config rule detects a non-compliant resource."
"Which data retention period is offered by AWS Config to store your resource configuration history?","AWS Config allows you to specify a retention period between 7 days and 7 years","AWS Config only retains data for 24 hours","AWS Config retains data indefinitely","AWS Config retains data for 30 days","AWS Config lets you store your resource configuration history for a period of your choosing, from 7 days to 7 years."
"When evaluating compliance with AWS Config rules, what are the possible compliance types?","Compliant, Non-compliant, or Insufficient Data","Healthy, Unhealthy, or Warning","Enabled, Disabled, or Paused","Active, Inactive, or Pending","The possible compliance types when evaluating against AWS Config rules are Compliant, Non-compliant, or Insufficient Data."
"What type of rule triggers AWS Config evaluation based on changes to a resource configuration?","Configuration change-triggered rule","Periodic rule","Scheduled rule","Manual rule","Configuration change-triggered rules initiate evaluation whenever there are changes to a resource configuration. This allows AWS Config to maintain a real-time view of compliance."
"What does AWS Config provide that enables you to see how your resources are related to one another?","Resource relationships","Network diagrams","CloudTrail logs","Compliance summaries","AWS Config captures relationships between resources, such as an EC2 instance belonging to a specific VPC or an EBS volume attached to an EC2 instance. This enables you to understand dependencies and the impact of changes."
"When using AWS Config, which of the following is a potential use case for custom rules?","Enforcing custom naming conventions for resources","Automatically scaling EC2 instances","Encrypting EBS volumes","Monitoring network bandwidth usage","Custom rules allow you to define your own logic to evaluate resource configurations, enabling you to enforce custom naming conventions, security policies, or other requirements."
"How can you use AWS Config to ensure that all your S3 buckets are encrypted?","Create an AWS Config rule to check if S3 buckets have encryption enabled","Use S3 bucket policies to enforce encryption","Enable default encryption on all S3 buckets","Use IAM policies to restrict access to unencrypted buckets","You can create an AWS Config rule that evaluates whether S3 buckets have encryption enabled. If a bucket is not encrypted, the rule will flag it as non-compliant."
"What is the benefit of using AWS Config managed rules over custom rules?","Managed rules are pre-defined by AWS and are easier to implement","Managed rules provide more flexibility than custom rules","Managed rules are free to use","Managed rules are automatically applied to all resources","Managed rules are pre-defined by AWS, which simplifies implementation and reduces the need to write custom code for common compliance requirements."
"Which AWS service can be integrated with AWS Config to allow you to visualise your resource configurations and relationships?","AWS X-Ray","AWS CloudWatch","AWS Trusted Advisor","AWS Service Catalog","AWS Config can't be directly integrated with any AWS visualisation tool. However, you can visualise with third party tools."
"If you want to ensure that all your EC2 instances are of a specific instance type, how can AWS Config assist you?","Create an AWS Config rule to check the instance type of EC2 instances","Use IAM policies to restrict the instance types that can be launched","Use Auto Scaling groups to enforce instance types","Use EC2 Instance Connect to monitor instance types","You can create an AWS Config rule that evaluates the instance type of EC2 instances and flags those that do not match the required type as non-compliant."
"What level of detail does AWS Config record about resource configurations?","It records all attributes and relationships of supported resources","It only records the resource type and ARN","It only records changes to specific attributes","It only records the creation and deletion of resources","AWS Config records all attributes and relationships of supported resources, providing a detailed view of their configuration."
"Which of the following is a use case for AWS Config aggregators?","Centralised compliance monitoring across multiple AWS accounts","Automated remediation of non-compliant resources","Real-time threat detection","Automated backup and recovery of resources","AWS Config aggregators are primarily used for centralised compliance monitoring across multiple AWS accounts, allowing you to get a single view of compliance across your organisation."
"What is the role of an AWS Config Recorder?","To specify which resources AWS Config should record","To specify the frequency of AWS Config recording","To specify the region where AWS Config data is stored","To encrypt AWS Config data","The AWS Config Recorder is used to specify which resources AWS Config should record in your AWS account."
"If you need to export the configuration data recorded by AWS Config, which AWS service can you use?","Amazon S3","Amazon CloudWatch Logs","AWS CloudTrail","AWS Lambda","AWS Config can deliver its configuration data to an Amazon S3 bucket, allowing you to export and analyse the data."
"How can you use AWS Config to track changes to security group rules?","AWS Config records changes to security group rules as configuration items","Use AWS Network Firewall to track security group rule changes","Use AWS CloudTrail to track security group rule changes","Security group rule changes cannot be tracked","AWS Config records changes to security group rules as configuration items, allowing you to monitor who is making changes to your security posture."
"What is the advantage of using AWS Config with Conformance Packs?","Conformance Packs provide a standardised way to deploy and manage compliance policies","Conformance Packs are free to use","Conformance Packs automatically remediate non-compliant resources","Conformance Packs provide real-time threat detection","Conformance Packs offer a standardised and streamlined way to deploy and manage compliance policies, ensuring consistent governance across your AWS environment."
"How does AWS Config assist in auditing your AWS environment?","By providing a historical record of resource configurations and compliance status","By automatically scanning for vulnerabilities","By providing real-time threat intelligence","By automatically patching EC2 instances","AWS Config helps with auditing by providing a historical record of resource configurations and compliance status, allowing you to track changes and identify potential issues."
"Can AWS Config track resources created outside of AWS?","No, AWS Config can only track AWS resources","Yes, with the use of the AWS Config agent","Yes, through integration with AWS CloudTrail","Yes, through a custom-built API","AWS Config is designed to track the configuration of AWS resources. While you can integrate with other tools, it primarily focuses on resources within the AWS ecosystem."
"Which of the following is NOT a valid use case for AWS Config?","Monitoring application performance","Ensuring compliance with industry regulations","Tracking changes to resource configurations","Automating remediation of non-compliant resources","AWS Config is designed for configuration management and compliance, not for monitoring application performance."
"You want to ensure that all new EC2 instances have a specific tag. How can you achieve this using AWS Config?","Create an AWS Config rule that checks for the presence of the tag","Use IAM policies to enforce the tag requirement","Use EC2 Instance Connect to add the tag","Use CloudFormation to automatically add the tag","You can create an AWS Config rule that evaluates whether EC2 instances have the required tag and flags those that do not as non-compliant."
"What happens to the data in AWS Config when a resource is deleted?","The configuration item for the resource remains in AWS Config with a status of 'deleted'","The data is automatically purged","The data is archived to Glacier","The data is moved to a separate historical database","When a resource is deleted, the configuration item for the resource remains in AWS Config with a status of 'deleted', allowing you to track the deletion event."
"Which of the following actions can you take to minimise the cost of using AWS Config?","Limit the scope of the AWS Config Recorder to only record necessary resource types","Disable AWS Config during off-peak hours","Reduce the frequency of rule evaluations","Disable all AWS Config rules","Limiting the scope of the AWS Config Recorder to only record necessary resource types is a way to minimise the cost of using AWS Config as it reduces the number of configuration items recorded."
"Which of the following is a benefit of using AWS Config in a multi-account environment?","Centralised visibility and compliance monitoring across all accounts","Automated cost optimisation across all accounts","Real-time threat detection across all accounts","Simplified IAM management across all accounts","AWS Config allows centralised visibility and compliance monitoring across all accounts, providing a unified view of your organisation's security posture and compliance status."
"You need to prove to an auditor that your AWS resources were compliant with a specific security policy at a specific point in time. How can AWS Config help you achieve this?","AWS Config provides a historical record of resource configurations and compliance status","AWS Config automatically generates audit reports","AWS Config provides real-time threat intelligence","AWS Config automatically remediates non-compliant resources","AWS Config provides a historical record of resource configurations and compliance status, allowing you to demonstrate compliance with security policies over time."
"What is the relationship between AWS Config and AWS Trusted Advisor?","AWS Trusted Advisor provides best practice recommendations, while AWS Config tracks compliance with those recommendations","AWS Config provides best practice recommendations, while AWS Trusted Advisor tracks compliance","They both provide the same functionality","They are completely unrelated services","AWS Trusted Advisor provides best practice recommendations, while AWS Config tracks the configuration of resources and evaluates them against those recommendations, allowing you to assess compliance."
"If you need to track changes to AWS CloudFormation stacks, can AWS Config do this?","Yes, AWS Config supports tracking AWS CloudFormation stacks as resources","No, AWS Config does not support CloudFormation stacks","Only specific CloudFormation resources can be tracked","Only changes to stack outputs can be tracked","AWS Config supports tracking AWS CloudFormation stacks as resources, allowing you to monitor changes to your infrastructure-as-code deployments."
"What is the purpose of the 'aws:cloudtrail:eventSource' attribute in an AWS Config rule?","To trigger the rule evaluation based on events from AWS CloudTrail","To specify the AWS region where the rule should be evaluated","To specify the IAM role that the rule should use","To specify the resource types that the rule should evaluate","The 'aws:cloudtrail:eventSource' attribute is used to trigger the rule evaluation based on events from AWS CloudTrail, allowing you to react to specific API calls."
"How can you use AWS Config to detect and prevent drift in your infrastructure configuration?","By continuously monitoring resource configurations and alerting on deviations from the desired state","By automatically rolling back configuration changes","By automatically patching EC2 instances","By automatically scaling resources","AWS Config helps detect and prevent drift by continuously monitoring resource configurations and alerting on deviations from the desired state, allowing you to quickly identify and correct any unintended changes."
"What is the primary purpose of a 'Scope' in AWS Config rule configuration?","To define which resources are evaluated by the rule","To define the region where the rule is evaluated","To define the users who can modify the rule","To define the schedule for the rule evaluation","The Scope in AWS Config rule configuration defines which resources are evaluated by the rule, allowing you to target specific resource types, IDs, or tags."
"What is the primary function of AWS Config?","To assess, audit, and evaluate the configurations of your AWS resources","To monitor network traffic","To manage user access","To deploy applications","AWS Config provides a detailed view of the configuration of AWS resources in your AWS account and how they relate to one another. This allows you to assess, audit, and evaluate configurations for compliance."
"Which of the following is a component of AWS Config that defines the desired configuration state of your AWS resources?","Config Rule","Config Pack","Configuration Recorder","Configuration Item","A Config Rule defines the desired configuration state for an AWS resource and evaluates whether a resource complies with the rule."
"How does AWS Config discover the AWS resources in your account?","By using the Configuration Recorder","By using AWS CloudTrail","By using AWS CloudWatch","By using AWS IAM","The Configuration Recorder continuously monitors and records the configuration changes of your AWS resources."
"What type of AWS Config rule evaluates the configuration of your AWS resources based on a schedule?","Periodic rule","Change-triggered rule","Remediation rule","Compliance rule","A periodic rule evaluates resources on a schedule, such as every 24 hours."
"What can you use AWS Config for?","Automating remediation actions for non-compliant resources","Monitoring CPU utilisation of EC2 instances","Creating VPCs","Managing IAM roles","AWS Config, through Config Rules and Remediation Actions, allows for automated remediation of non-compliant resources."
"Which of the following AWS services is required for AWS Config to function correctly?","AWS IAM","AWS CloudFront","AWS SQS","AWS Lambda","AWS IAM is required so AWS Config can assume a role with permissions to describe AWS resources."
"What is the term for the historical record of configuration changes captured by AWS Config?","Configuration History","Configuration Snapshot","Configuration Stream","Configuration Log","AWS Config captures a Configuration History of all resources in a supported region and account."
"How can you centrally manage AWS Config rules across multiple AWS accounts?","By using AWS Organizations","By using AWS IAM","By using AWS CloudFormation","By using AWS Trusted Advisor","AWS Organizations allows you to centrally manage Config rules and other governance policies across multiple accounts."
"Which of the following is NOT a use case for AWS Config?","Detecting unauthorised changes to security group rules","Monitoring database performance","Auditing IAM role assignments","Validating S3 bucket policies","While AWS Config can track changes to security groups, IAM roles and S3 bucket policies, it doesn't directly monitor database performance (CloudWatch is a better choice for that)."
"What is the purpose of a remediation action in AWS Config?","To automatically correct non-compliant resources","To generate compliance reports","To define the scope of AWS Config rules","To trigger alerts for non-compliant resources","Remediation actions allow you to automatically correct resources that are found to be non-compliant with your Config rules."
"What is the primary purpose of AWS Config?","To assess, audit, and evaluate the configurations of your AWS resources","To monitor the CPU utilisation of your EC2 instances","To manage user access to AWS services","To store and retrieve configuration files","AWS Config continuously monitors and records the configurations of your AWS resources and allows you to automate the evaluation of recorded configurations against desired configurations."
"Which AWS Config component captures the configuration state of a resource at a specific point in time?","Configuration Item","Configuration Recorder","Configuration Rule","Delivery Channel","A Configuration Item represents a point-in-time view of the configuration of a supported AWS resource."
"What is the function of AWS Config Rules?","To evaluate whether your AWS resources comply with specific configuration standards","To encrypt data stored in S3 buckets","To automatically scale EC2 instances","To define network security groups","Config Rules allow you to define the desired configuration state of your AWS resources and automatically evaluate whether they comply with those standards."
"How does AWS Config deliver configuration changes to a specified destination?","Through a Delivery Channel","Through an SNS Topic","Through a CloudWatch Alarm","Through a VPC Endpoint","A Delivery Channel defines where AWS Config sends configuration snapshots and configuration history files."
"Which of the following is a managed Config Rule?","`s3-bucket-public-read-prohibited`","A custom rule created with Lambda","A rule defined using CloudFormation","A rule written in JSON","Managed Config Rules are pre-defined rules provided by AWS, such as `s3-bucket-public-read-prohibited`, to quickly assess compliance with common best practices."
"What type of rule is best suited for checking custom compliance requirements unique to an organisation using AWS Config?","Custom Rule","Managed Rule","Service Rule","Default Rule","Custom rules allow you to implement your own evaluation logic, making them appropriate for checking custom compliance requirements."
"Which AWS service is typically used to create custom Config Rules?","AWS Lambda","AWS CloudTrail","Amazon CloudWatch","Amazon SQS","Custom Config Rules are commonly created using AWS Lambda functions, which provide the code to evaluate the resource configurations."
"Which of the following is NOT a typical use case for AWS Config?","Forecasting future AWS costs","Compliance auditing","Security analysis","Change management","AWS Config is primarily focused on resource configuration management, compliance, security analysis, and change management. Forecasting future AWS costs would typically be handled by other tools, such as AWS Cost Explorer."
"Which resource can AWS Config NOT track configuration changes for?","On-premises servers","EC2 instances","S3 buckets","IAM roles","AWS Config primarily tracks the configuration of supported AWS resources. It does not directly track on-premises servers, unless they are registered as hybrid resources via other AWS services."
"Which of the following is a benefit of using AWS Config?","Continuous monitoring of resource configurations","Automated patching of EC2 instances","Automatic backup of DynamoDB tables","Predictive scaling of application load balancers","AWS Config provides continuous monitoring of resource configurations, enabling you to assess, audit, and evaluate those configurations."