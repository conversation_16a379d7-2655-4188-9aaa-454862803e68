"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of the AWS Health Dashboard?","To provide a personalised view of the health of AWS services affecting your resources","To monitor the CPU usage of your EC2 instances","To manage your AWS billing and costs","To configure network settings for your VPC","The AWS Health Dashboard provides a personalised view of the health of AWS services that power your AWS resources, along with proactive notifications to help you plan for scheduled activities."
"Which type of events are typically displayed in the AWS Health Dashboard?","Service health events and planned maintenance events","Real-time stock market data","Global weather patterns","Social media trends","The AWS Health Dashboard displays service health events affecting AWS services, and planned maintenance events that may impact your resources."
"Where can you access the AWS Health Dashboard?","AWS Management Console","AWS Marketplace","AWS Support Centre only","AWS CloudShell","The AWS Health Dashboard is directly accessible through the AWS Management Console."
"How does the AWS Health Dashboard provide personalised information?","By displaying events specific to the AWS services and regions you are using","By showing events affecting all AWS customers globally","By sending weekly email summaries of AWS service health","By providing a forum for AWS users to discuss service issues","The AWS Health Dashboard filters events to show only those affecting the specific AWS services and regions that you are using."
"What kind of information does the 'Planned Maintenance' section of the AWS Health Dashboard provide?","Details about upcoming maintenance activities that may affect your resources","Information about past security breaches","Suggestions for cost optimisation","Predictions about future AWS service outages","The 'Planned Maintenance' section informs you about upcoming scheduled maintenance activities that could potentially impact your AWS resources."
"Which AWS service integrates with the AWS Health Dashboard to automate responses to events?","AWS CloudWatch Events (EventBridge)","AWS Lambda","AWS S3","AWS IAM","AWS CloudWatch Events (now known as EventBridge) can be used to automatically trigger actions based on events reported by the AWS Health Dashboard, such as initiating failover procedures or sending notifications."
"How does the AWS Health Dashboard differ from the AWS Service Health Dashboard?","AWS Health Dashboard provides a personalised view, while Service Health Dashboard provides a general view","AWS Health Dashboard shows only past events, while Service Health Dashboard shows only future events","AWS Health Dashboard requires a paid subscription, while Service Health Dashboard is free","AWS Health Dashboard focuses on security events, while Service Health Dashboard focuses on performance events","The AWS Health Dashboard provides a personalised view, showing events specific to your AWS resources, while the Service Health Dashboard shows the overall health of AWS services globally."
"What type of actions can you take based on the information provided in the AWS Health Dashboard?","Reschedule maintenance windows, update instance configurations, or adjust scaling policies","Increase your AWS spending limits","Change your AWS account password","Delete unused AWS resources only","The information from the AWS Health Dashboard can guide actions such as rescheduling maintenance windows, updating instance configurations, or adjusting scaling policies to minimise the impact of events."
"Can the AWS Health Dashboard be used to identify security vulnerabilities in your AWS environment?","It can highlight known service-level vulnerabilities that may affect your resources","It performs automatic penetration testing","It automatically patches security vulnerabilities","It provides a detailed report of all security flaws","The AWS Health Dashboard may highlight known service-level vulnerabilities that could affect your resources, prompting you to take appropriate actions."
"What is the AWS Health API used for?","Programmatically accessing AWS Health information","Managing AWS IAM roles","Creating AWS Lambda functions","Monitoring network traffic","The AWS Health API allows you to programmatically access information from the AWS Health Dashboard, enabling integration with your existing monitoring and management tools."
"Which information is NOT displayed in the AWS Health Dashboard?","Historical pricing data for AWS services","Scheduled changes affecting your resources","AWS service outages impacting your resources","Potential issues related to your account","The AWS Health Dashboard does not display historical pricing data for AWS services. It focuses on the health and availability of services and resources."
"What type of support cases can be created directly from the AWS Health Dashboard?","Technical support cases related to reported events","Billing support cases","Feature request cases","Training support cases","You can create technical support cases directly from the AWS Health Dashboard related to the events that are being reported. This streamlines the process of getting help for issues affecting your resources."
"In the context of AWS Health Dashboard, what does 'affected resource' refer to?","An AWS resource impacted by a service event or planned maintenance","A virtual server running on a local computer","A file stored in Amazon S3","A user account with administrator privileges","An 'affected resource' in the AWS Health Dashboard is an AWS resource (e.g., an EC2 instance, an RDS database) that is being impacted by a service event or planned maintenance activity."
"How can you receive proactive notifications about AWS Health Dashboard events?","By configuring AWS CloudWatch Events (EventBridge) rules","By subscribing to an AWS SNS topic for billing alerts","By setting up an AWS Config rule","By enabling AWS CloudTrail logging","You can configure AWS CloudWatch Events (EventBridge) rules to receive proactive notifications about AWS Health Dashboard events. These notifications can be sent via email, SMS, or other channels."
"What is the relationship between AWS Trusted Advisor and AWS Health Dashboard?","AWS Trusted Advisor provides recommendations for optimising your AWS resources, while AWS Health Dashboard provides information on service health and planned maintenance","AWS Trusted Advisor directly monitors AWS service health","AWS Health Dashboard replaces AWS Trusted Advisor","AWS Trusted Advisor is a component of AWS Health Dashboard","AWS Trusted Advisor provides recommendations for optimising your AWS resources based on cost, security, performance, and fault tolerance, while the AWS Health Dashboard focuses on service health and planned maintenance events affecting your resources."
"You want to automate the process of backing up your EC2 instances before a scheduled maintenance event reported by the AWS Health Dashboard. Which AWS service can help you achieve this?","AWS CloudWatch Events (EventBridge)","AWS Config","AWS CloudTrail","AWS Systems Manager","AWS CloudWatch Events (now EventBridge) can trigger an AWS Lambda function that automates the backup process before the scheduled maintenance event."
"Which statement is true regarding the AWS Health Dashboard's data retention policy?","AWS Health Dashboard retains data for at least 12 months","AWS Health Dashboard retains data indefinitely","AWS Health Dashboard only shows current events","AWS Health Dashboard does not store any historical data","The AWS Health Dashboard retains data for at least 12 months, allowing you to review past events and identify trends."
"How can you share AWS Health Dashboard events with users who do not have direct access to your AWS account?","By creating a support case and including the relevant information","By granting them temporary access to the AWS Management Console","By exporting the events as a PDF document","It is not possible to share Health Dashboard events with external users","The most appropriate way is to create a support case and share the relevant information. Sharing AWS account access is generally not recommended for security reasons."
"What is the scope of the AWS Health Dashboard?","It provides insights into the health of AWS services affecting your account and region","It monitors the health of all cloud providers worldwide","It provides global economic data","It monitors on-premises infrastructure","The scope of the AWS Health Dashboard is limited to providing insights into the health of AWS services and resources specifically affecting your AWS account and region."
"How can you identify the root cause of an issue reported in the AWS Health Dashboard?","The AWS Health Dashboard provides direct links to relevant documentation and support resources","The AWS Health Dashboard provides automated root cause analysis","The AWS Health Dashboard offers a built-in troubleshooting tool","The AWS Health Dashboard does not provide root cause analysis information","The AWS Health Dashboard does not provide automated root cause analysis. However, it can provide links to relevant documentation and support resources."
"Which of the following can trigger an event to be displayed on the AWS Health Dashboard?","An issue impacting an AWS service used by your resources","A change to your AWS billing settings","A new AWS service release","A new feature added to the AWS Management Console","An issue impacting an AWS service used by your resources can trigger an event to be displayed on the AWS Health Dashboard."
"If an AWS service is experiencing a global outage, where would you find information about it?","AWS Service Health Dashboard","AWS Billing Console","AWS CloudTrail logs","AWS IAM console","Information about global outages is typically found on the AWS Service Health Dashboard, which provides a broader view of the health of all AWS services."
"You receive a notification from the AWS Health Dashboard about a 'Degraded' status for an EC2 instance. What does this typically indicate?","The instance is experiencing performance issues but is still running","The instance has been terminated","The instance is running at optimal performance","The instance is being upgraded","A 'Degraded' status usually indicates that the EC2 instance is experiencing performance issues but is still running. This might be due to underlying hardware problems or other factors."
"How can you filter events in the AWS Health Dashboard to focus on specific AWS services?","By using the 'Filter' option and selecting the relevant service names","By sorting the events by severity","By grouping the events by region","You cannot filter events in the AWS Health Dashboard","You can use the 'Filter' option in the AWS Health Dashboard to select specific AWS services you're interested in, allowing you to focus on events related to those services."
"What type of IAM permissions are required to access the AWS Health Dashboard?","Permissions to view AWS Health events and resources","Permissions to manage AWS billing","Permissions to create IAM users","Permissions to manage VPCs","To access the AWS Health Dashboard, you need IAM permissions that allow you to view AWS Health events and the resources affected by those events."
"Can you customise the notifications you receive from the AWS Health Dashboard?","Yes, by configuring AWS CloudWatch Events (EventBridge) rules","No, notifications are pre-defined and cannot be changed","Yes, through the AWS Billing console","Yes, by contacting AWS support","You can customise the notifications you receive from the AWS Health Dashboard by configuring AWS CloudWatch Events (now EventBridge) rules. This allows you to define specific criteria for triggering notifications."
"Which action is NOT typically recommended based on information from the AWS Health Dashboard?","Ignoring the event if it does not directly impact your current operations","Checking the affected resources","Reviewing the provided documentation","Contacting AWS support if needed","Ignoring events, even if they don't immediately impact your operations, is not recommended as they might indicate potential future issues."
"What is the difference between AWS Health and AWS Personal Health Dashboard?","AWS Health is the underlying service, while Personal Health Dashboard is the personalised view for users","AWS Health is for monitoring EC2 instances, while Personal Health Dashboard is for RDS instances","AWS Health is for managing IAM users, while Personal Health Dashboard is for managing VPCs","AWS Health is a paid service, while Personal Health Dashboard is free","AWS Health is the underlying service that provides health information, while the Personal Health Dashboard is the personalised view of that information for individual AWS users."
"You are seeing an increased number of API throttling errors. Can the AWS Health Dashboard help identify if this is related to a broader service issue?","Yes, it can indicate if there's a service-wide issue causing increased throttling","No, it only shows hardware-related problems","No, it only shows security vulnerabilities","No, it only shows planned maintenance events","The AWS Health Dashboard can indicate if there's a service-wide issue that might be causing increased API throttling, even if it's not directly related to your specific resources."
"What is the benefit of using the AWS Health Dashboard API?","Allows for programmatic access to health information, enabling automated monitoring and response","Provides a graphical user interface for managing AWS resources","Offers a real-time feed of financial news","Allows direct manipulation of AWS hardware","The AWS Health Dashboard API allows for programmatic access to health information, enabling automated monitoring and response through custom scripts or third-party tools."
"Which AWS service should be used to create custom dashboards with health event data?","Amazon CloudWatch","AWS Config","AWS CloudTrail","AWS IAM","Amazon CloudWatch can be used to create custom dashboards with metrics and logs related to AWS resources, enabling you to visualise and monitor the health of your environment."
"What kind of resources can be marked as 'affected' by an event in the AWS Health Dashboard?","EC2 instances, RDS databases, S3 buckets","Only EC2 instances","Only RDS databases","Only S3 buckets","Resources such as EC2 instances, RDS databases, and S3 buckets can be marked as 'affected' by an event in the AWS Health Dashboard."
"What is the best practice for responding to a 'Planned Maintenance' event in the AWS Health Dashboard?","Review the event details and plan any necessary downtime or adjustments to your resources","Immediately terminate all affected resources","Ignore the event until it becomes a 'Service Impairment'","Immediately increase the capacity of all affected resources","The best practice is to review the event details and plan any necessary downtime or adjustments to your resources to minimise impact."
"If the AWS Health Dashboard indicates a 'Service Impairment' event, what should you do first?","Check the affected resources and review any recommended actions","Immediately contact AWS support","Immediately terminate all affected resources","Immediately migrate all resources to another region","The first step should be to check the affected resources and review any recommended actions provided in the AWS Health Dashboard."
"Which of the following actions can be automated using AWS CloudWatch Events (EventBridge) in response to an AWS Health Dashboard event?","Automating the failover of an EC2 instance","Manually creating a new AWS account","Manually configuring a security group","Manually updating IAM permissions","AWS CloudWatch Events (now EventBridge) can be configured to automatically trigger a failover of an EC2 instance in response to an AWS Health Dashboard event."
"When viewing the AWS Health Dashboard, what does the 'Region' filter refer to?","The AWS Region where the affected resources are located","The geographical location of the AWS Health Dashboard server","The preferred language setting for the dashboard","The time zone for displaying events","The 'Region' filter refers to the AWS Region where the affected resources are located. This allows you to focus on events impacting resources in specific regions."
"How can you determine the potential impact of a 'Planned Maintenance' event on your application?","By reviewing the 'Affected Resources' list and the event description","By checking your AWS billing statement","By monitoring the AWS service quotas","By running a penetration test","Reviewing the 'Affected Resources' list and the event description will help you understand which resources might be impacted and how to prepare for the maintenance."
"What is the maximum amount of historical data you can typically view in the AWS Health Dashboard?","Up to 12 months of historical data","Only the current week's data","Only the current month's data","Data is not retained in the AWS Health Dashboard","The AWS Health Dashboard typically retains up to 12 months of historical data, allowing you to review past events."
"If you suspect an issue with an AWS service but don't see it listed on the AWS Health Dashboard, what should you do?","Check the AWS Service Health Dashboard and consider opening a support case","Immediately restart all your EC2 instances","Assume the issue is with your application code","Immediately increase your AWS spending limits","If you suspect an issue with an AWS service but don't see it listed on the AWS Health Dashboard, check the AWS Service Health Dashboard (which provides a global view) and consider opening a support case to report the issue."
"What is the role of AWS Support in relation to the AWS Health Dashboard?","AWS Support can provide assistance and guidance on resolving issues identified in the dashboard","AWS Support directly manages the AWS Health Dashboard","AWS Support is not related to the AWS Health Dashboard","AWS Support only handles billing issues","AWS Support can provide assistance and guidance on resolving issues identified in the AWS Health Dashboard, helping you to understand the impact and take appropriate action."
"Which of the following metrics is NOT directly monitored by the AWS Health Dashboard?","CPU utilisation of individual EC2 instances","Service availability","Impacted resources","Planned maintenance","The AWS Health Dashboard focuses on service availability and impacted resources, not the detailed CPU utilisation of individual EC2 instances. CloudWatch is used for monitoring instance-level metrics."
"What does the status 'Informational' signify in the AWS Health Dashboard?","The event is providing general information and does not require immediate action","The event indicates a critical service outage","The event requires immediate security patching","The event requires immediate billing attention","A status of 'Informational' in the AWS Health Dashboard indicates that the event is providing general information and does not require immediate action."
"How can you programmatically acknowledge or dismiss events displayed in the AWS Health Dashboard?","Via the AWS Health API","Via the AWS CLI","Via the AWS CloudShell","This is not possible","You can programmatically acknowledge or dismiss events displayed in the AWS Health Dashboard through the AWS Health API."
"You need to provide evidence that a specific AWS service was impaired during a critical period. Where can you find this information?","AWS Health Dashboard","AWS CloudTrail","AWS Config","AWS Trusted Advisor","You can find evidence of service impairment events in the AWS Health Dashboard, which provides a historical record of service health."
"Which statement accurately describes the relationship between the AWS Health Dashboard and AWS CloudTrail?","AWS Health Dashboard reports on service events, while CloudTrail logs API calls","AWS Health Dashboard logs all API calls, while CloudTrail reports on service events","AWS Health Dashboard replaces CloudTrail","AWS Health Dashboard is a subset of CloudTrail","AWS Health Dashboard reports on service events and planned maintenance, while AWS CloudTrail logs API calls made to AWS services."
"How can you determine if a service event reported in the AWS Health Dashboard is affecting your Auto Scaling group?","Check the list of affected resources in the event details","Manually monitor the Auto Scaling group metrics","Use AWS Config to analyse the resources","The Auto Scaling group will be automatically terminated","You can check the list of affected resources in the event details to see if your Auto Scaling group or its associated resources are impacted."
"Which of the following is a typical use case for the AWS Health Dashboard?","Proactively planning for scheduled maintenance affecting your resources","Managing IAM permissions","Optimising AWS costs","Monitoring application performance","Proactively planning for scheduled maintenance affecting your resources is a typical use case for the AWS Health Dashboard."
"What is the first step you should take if you see a 'Potential Impact' event in the AWS Health Dashboard?","Assess if it is likely to impact any of your applications or resources","Immediately scale up your EC2 instances","Immediately change your security group rules","Immediately terminate all affected instances","The first step is to assess whether the event is likely to impact any of your applications or resources. Check affected resources and event descriptions."
"What is the main advantage of a centralised AWS Health Dashboard view?","It provides a single pane of glass for monitoring the health of all your AWS resources and services","It allows you to directly manage your IAM roles","It eliminates the need for AWS support","It automatically fixes any issues detected","A centralised AWS Health Dashboard view provides a single pane of glass for monitoring the health of all your AWS resources and services."
"Regarding AWS Health Dashboard, what is the primary function of the Personal Health Dashboard (PHD)?","To provide a personalised view of events affecting your AWS resources","To display the overall health of all AWS services globally","To offer cost optimisation recommendations","To manage AWS Identity and Access Management (IAM) roles","The Personal Health Dashboard provides a tailored view of events that may impact your specific AWS resources."
"In the context of AWS Health Dashboard, what type of information does the Service Health Dashboard (SHD) provide?","Information about the overall health of AWS services","Detailed logs of your individual resource performance","Configuration recommendations for your AWS account","Real-time pricing data for AWS services","The Service Health Dashboard offers a global view of the health of AWS services, providing information about any widespread issues."
"When using the AWS Health Dashboard, how can you be proactively notified of potential issues affecting your resources?","By configuring CloudWatch alarms based on Health Dashboard events","By subscribing to the AWS Security Bulletin","By enabling AWS Trusted Advisor checks","By reviewing the AWS billing dashboard daily","CloudWatch alarms can be configured to trigger notifications based on events reported in the AWS Health Dashboard, allowing for proactive responses."
"You need to identify which AWS resources are affected by a specific AWS Health event. Where would you find this information?","Within the detailed event information on the Personal Health Dashboard","In the AWS CloudTrail logs","In the AWS Config console","Within the AWS Service Quotas dashboard","The Personal Health Dashboard provides details about each event, including a list of the AWS resources that are impacted."
"What is the significance of the 'Affected Resources' section within an AWS Health Dashboard event?","It lists the specific AWS resources that are experiencing the issue","It provides cost estimates for resolving the issue","It offers links to AWS documentation related to the impacted services","It displays the geographical location of the affected services","The 'Affected Resources' section clearly identifies which of your AWS resources are impacted by a specific health event."
"When an AWS service is experiencing a general outage, where would you typically find the most up-to-date information?","Service Health Dashboard (SHD)","AWS Support Centre","Personal Health Dashboard (PHD)","AWS Trusted Advisor","For widespread AWS service outages, the Service Health Dashboard provides the most current and comprehensive information."
"How does the AWS Health Dashboard differ from AWS CloudWatch?","AWS Health focuses on AWS service health, while CloudWatch monitors your resources","AWS Health monitors network latency, while CloudWatch focuses on compute resources","AWS Health provides security alerts, while CloudWatch provides cost optimisation","AWS Health visualizes data, while CloudWatch manages user access","AWS Health provides information on the health of AWS services impacting your resources while CloudWatch monitors the performance and operational health of your specific AWS resources."
"Which AWS service can you integrate with the AWS Health Dashboard to receive automated notifications about events affecting your resources?","Amazon CloudWatch Events (EventBridge)","AWS Config","AWS CloudTrail","AWS IAM","Amazon CloudWatch Events (now Amazon EventBridge) allows you to create rules that trigger actions based on events reported by the AWS Health Dashboard, providing automated notifications."
"In the context of the AWS Health Dashboard, what does the term 'Operational Issue' typically refer to?","A problem with an AWS service that impacts your resources","A misconfiguration in your AWS account","A potential security vulnerability","An upcoming scheduled maintenance","'Operational Issue' generally refers to problems with AWS services that could be affecting your resources."
"If you are not seeing expected events in your Personal Health Dashboard (PHD), what should you check first?","Ensure you have the correct AWS Region selected","Check your AWS billing preferences","Verify your AWS Identity and Access Management (IAM) permissions","Review your Amazon S3 bucket policies","The Personal Health Dashboard displays events specific to the AWS Region you are viewing, so ensure the correct Region is selected."
"What is the primary purpose of the AWS Health API?","To programmatically access and retrieve health events and status","To create and manage AWS IAM roles","To provision and configure AWS EC2 instances","To monitor network performance metrics","The AWS Health API enables developers to programmatically access and retrieve information about AWS health events, enabling automated responses."
"Which AWS Health Dashboard feature allows you to filter events based on specific AWS services?","Event filtering","Resource tagging","Account grouping","Role-based access control","The event filtering feature within the AWS Health Dashboard allows you to narrow down events based on specific AWS services of interest."
"You suspect an issue with an AWS service but don't see anything reported on the AWS Health Dashboard. What should you do first?","Check the Service Health Dashboard (SHD) for general AWS service status","Immediately contact AWS Support","Reboot your EC2 instances","Assume your application code is the problem","Even if the PHD doesn't show events, the SHD provides a broader view of the overall status of AWS services which may reveal a wider issue."
"What is the best way to determine the potential impact of a scheduled maintenance event on your AWS resources, using the AWS Health Dashboard?","Review the 'Affected Resources' section of the maintenance event","Analyse CloudWatch metrics for the impacted resources","Contact AWS Support for a detailed impact assessment","Estimate the impact based on historical data","The 'Affected Resources' section of a scheduled maintenance event details which resources may be affected, allowing you to plan accordingly."
"When should you typically consult the AWS Health Dashboard?","When experiencing unexpected issues with your AWS resources","When planning infrastructure upgrades","When conducting security audits","When optimising AWS costs","The AWS Health Dashboard is most useful when you are experiencing unexpected issues with your AWS resources as it provides information on AWS service events."
"Which AWS Health Dashboard component provides information specific to your AWS account?","Personal Health Dashboard (PHD)","Service Health Dashboard (SHD)","AWS Trusted Advisor","AWS Support Centre","The Personal Health Dashboard is tailored to your AWS account, displaying events that directly affect your resources."
"If you need to understand if a specific AWS Region is experiencing widespread issues, where should you look within the AWS Health Dashboard?","Service Health Dashboard (SHD)","Personal Health Dashboard (PHD)","AWS Cost Explorer","AWS Management Console","The Service Health Dashboard provides the overall status of AWS services, including regional availability and performance."
"How can the AWS Health Dashboard assist with troubleshooting application issues?","By providing information about underlying AWS service problems","By providing application debugging tools","By suggesting code fixes","By automatically scaling resources","The AWS Health Dashboard can reveal underlying AWS service issues that may be impacting your application, aiding in diagnosis."
"What type of information would you expect to find in the AWS Health Dashboard regarding an EC2 instance retirement?","Notification of the scheduled retirement and affected instance ID","Recommendations for instance optimisation","Real-time CPU usage data","Details of security vulnerabilities","The AWS Health Dashboard will display notifications of scheduled EC2 instance retirements, including the impacted instance ID."
"What can you configure using Amazon EventBridge (formerly CloudWatch Events) and the AWS Health Dashboard?","Automated responses to AWS Health events","Enhanced security measures for AWS accounts","Cost optimisation recommendations based on usage patterns","Automated backups of EC2 instances","Amazon EventBridge can be configured to trigger automated responses, such as sending notifications or initiating workflows, based on events reported in the AWS Health Dashboard."
"Which of the following statements best describes the AWS Health Dashboard's role in managing AWS infrastructure?","It provides visibility into events that can impact your AWS infrastructure.","It automates the deployment of AWS resources.","It optimises the cost of your AWS services.","It secures your AWS environment.","The AWS Health Dashboard provides a clear view into events and issues that may affect your AWS infrastructure and applications."
"If your AWS resources are experiencing performance degradation, what is the first place you should check within the AWS Management Console?","AWS Health Dashboard","AWS CloudTrail","AWS Config","AWS Trusted Advisor","The AWS Health Dashboard will highlight potential issues with underlying AWS services that may be causing performance degradation."
"Which AWS tool is best suited for historical auditing of AWS Health events?","AWS Health API integrated with a logging solution","AWS Config","AWS CloudWatch Logs","AWS Trusted Advisor","The AWS Health API, when integrated with a logging solution, allows for historical auditing of AWS Health events over time."
"What level of access do you typically need to view the AWS Health Dashboard?","Read-only access to AWS Health service","Full administrative access to the AWS account","No specific IAM permissions are needed","Access to AWS Billing dashboard","Typically, you need read-only access to the AWS Health service via IAM to view the AWS Health Dashboard."
"In the context of the AWS Health Dashboard, what is a 'planned event'?","A scheduled maintenance or upgrade activity","An unexpected outage due to a service failure","A potential security threat","A cost optimisation opportunity","A 'planned event' refers to scheduled maintenance or upgrade activities that AWS is performing, which may temporarily affect your resources."
"You want to track all events related to a specific AWS service within your AWS account. What is the most efficient way to do this using the AWS Health Dashboard?","Use the filtering options to view events for the specific service","Manually review all events in the dashboard","Contact AWS Support for a custom report","Export the entire dashboard data to a spreadsheet","The most efficient way is to use the filtering options available within the AWS Health Dashboard to focus on events for the specific service."
"What does the AWS Health Dashboard NOT provide?","Detailed code-level debugging information for your applications","Information about planned maintenance","Notifications about AWS service events","A list of your affected resources","The AWS Health Dashboard does not provide detailed code-level debugging information, focusing instead on the health of the AWS infrastructure."
"Which of the following is a use case for integrating the AWS Health API with your existing monitoring systems?","Automating incident response based on AWS Health events","Creating AWS IAM roles","Configuring AWS networking settings","Managing AWS billing preferences","Integrating the AWS Health API allows you to automate incident response by triggering actions based on AWS Health events."
"How does the AWS Health Dashboard help in maintaining compliance with regulatory requirements?","By providing a record of AWS service availability and incidents","By automating security patching","By generating compliance reports","By managing user access control","The AWS Health Dashboard provides a record of AWS service availability and any incidents, which can be useful for demonstrating compliance."
"What is the relationship between AWS Trusted Advisor and the AWS Health Dashboard?","AWS Trusted Advisor provides best practices, while AWS Health reports on service health","AWS Trusted Advisor manages security, while AWS Health manages performance","AWS Trusted Advisor monitors cost, while AWS Health manages access control","AWS Trusted Advisor automates deployment, while AWS Health monitors security","AWS Trusted Advisor offers recommendations on best practices related to cost optimisation, security, fault tolerance, service limits, and performance, while the AWS Health Dashboard reports on the actual health of AWS services."
"What is the key advantage of using the AWS Health Dashboard over relying solely on CloudWatch metrics for identifying issues?","AWS Health provides a broader view of AWS service health","CloudWatch provides real-time data, while AWS Health is delayed","AWS Health is more cost-effective than CloudWatch","CloudWatch provides more detailed information about your resources","The AWS Health Dashboard gives you a broader understanding of whether issues stem from your resources or underlying AWS service problems."
"Which AWS Health Dashboard view is most useful for understanding the overall operational status of AWS regions?","Service Health Dashboard","Personal Health Dashboard","Account settings page","Billing dashboard","The Service Health Dashboard provides a global view of the operational status of AWS services across all regions."
"What type of events would be shown in the Personal Health Dashboard?","Events that affect your specific AWS resources","Events that affect all AWS customers","Events related to AWS billing","Events related to AWS security compliance","The Personal Health Dashboard provides a personalised view of events that impact your specific AWS resources."
"If you receive a notification from AWS Health about a potential security vulnerability, what action should you take?","Follow the remediation steps outlined in the Health event details","Immediately shut down all your EC2 instances","Change your AWS account password","Ignore the notification if your resources are not affected","Follow the remediation steps outlined in the event details in AWS Health. These often include specific instructions for patching or mitigating the vulnerability."
"What is the best way to stay informed about scheduled maintenance activities impacting your AWS resources?","Regularly check the Personal Health Dashboard for upcoming events","Subscribe to the AWS Security Bulletin","Monitor CloudWatch metrics for unusual activity","Contact AWS Support weekly","The Personal Health Dashboard is the best way to stay informed about maintenance activities scheduled for your resources."
"Which AWS service can be used to automate actions based on events reported in the AWS Health Dashboard?","Amazon EventBridge (formerly CloudWatch Events)","AWS CloudTrail","AWS Config","AWS Systems Manager","Amazon EventBridge (formerly CloudWatch Events) can trigger automated actions, such as sending notifications or executing Lambda functions, when specific events are reported in the AWS Health Dashboard."
"When diagnosing an issue, where would you find details on the specific AWS services affected by an event listed on the AWS Health Dashboard?","In the event details section","On the AWS Trusted Advisor dashboard","In the AWS Config console","On the AWS Billing dashboard","The event details section within the AWS Health Dashboard provides granular information on the specific AWS services impacted by an event."
"You need to create a custom dashboard displaying only AWS Health events related to your EC2 instances. How can you achieve this?","By using the filtering options in the AWS Health Dashboard","By creating a custom CloudWatch dashboard and configuring EventBridge rules","By exporting the AWS Health data to a third-party tool","By contacting AWS Support","You can use the filtering options within the AWS Health Dashboard to create a view that shows only EC2-related events."
"What is the primary difference between a planned and unplanned event in the context of AWS Health?","Planned events are scheduled maintenance, while unplanned events are unexpected issues","Planned events affect all AWS customers, while unplanned events affect specific resources","Planned events are cost-optimised, while unplanned events involve security incidents","Planned events are related to billing, while unplanned events are related to performance","Planned events refer to scheduled maintenance activities, while unplanned events refer to unexpected issues or outages."
"How can the AWS Health Dashboard help you prepare for scheduled maintenance events?","By providing information about affected resources and estimated downtime","By automatically migrating your resources to unaffected regions","By providing cost estimates for the maintenance period","By automatically updating your applications","The AWS Health Dashboard provides details about affected resources and estimated downtime, allowing you to plan for the potential impact of scheduled maintenance."
"You are experiencing an issue with your application, and you suspect it might be related to an AWS service. Where can you quickly check the health status of that service?","AWS Health Dashboard","AWS CloudTrail","AWS Config","AWS Trusted Advisor","The AWS Health Dashboard offers a quick way to check the status of AWS services, helping you determine if an issue is related to a service outage or another problem."
"What is the benefit of using the AWS Health API compared to manually checking the AWS Health Dashboard?","Automation and programmatic access to health information","Faster access to AWS support","Improved security measures","Cost optimisation","The AWS Health API enables automation by allowing you to programmatically access and process health information, which is not possible with manual checks."
"In the AWS Health Dashboard, what is indicated when an event's status changes from 'Open' to 'Closed'?","The underlying issue has been resolved","The affected resources have been deleted","The event has been manually archived","The customer has acknowledged the event","A status of 'Closed' generally indicates that the underlying issue causing the event has been resolved."
"What is the primary benefit of integrating the AWS Health Dashboard with a third-party monitoring tool?","Centralised monitoring of AWS service health and application performance","Automated resource scaling based on AWS Health events","Improved security compliance","Cost optimisation","Integrating the AWS Health Dashboard with a third-party monitoring tool provides a centralised view of both AWS service health and your application performance, enabling holistic monitoring."
"What can be achieved by integrating AWS Chatbot with AWS Health Dashboard?","Receive notifications about health events directly in chat channels","Automate security patching across your infrastructure","Optimise costs based on health event analysis","Deploy new EC2 instances from your chat channel","Integrating AWS Chatbot allows you to receive real-time notifications about health events directly within your chat channels, facilitating faster response and collaboration."
"You receive a notification from the AWS Health Dashboard about an upcoming EC2 instance retirement. What should be your first step?","Identify the impacted instances and plan for migration or replacement","Immediately terminate and relaunch the instances","Contact AWS Support for assistance","Ignore the notification if the instances are not critical","Your first step should be to identify the impacted instances and plan for either migrating your data to a new instance or replacing the retiring instance, depending on your application's requirements."
"When should you proactively check the AWS Health Dashboard for potential issues?","During periods of high application traffic or before significant deployments","After a security incident","When planning for a disaster recovery exercise","Before shutting down your AWS account","Proactively checking the AWS Health Dashboard during periods of high application traffic or before significant deployments allows you to identify potential AWS service issues that could impact your operations."
"What is the primary purpose of the AWS Health Dashboard?","To provide visibility into the operational health of AWS services","To monitor individual EC2 instance CPU utilisation","To manage AWS IAM roles and permissions","To configure AWS network settings","The AWS Health Dashboard provides a personalised view of the health of AWS services, along with recommended steps to take if there are issues."
"Which two components make up the AWS Health Dashboard?","Personal Health Dashboard and Service Health Dashboard","CloudWatch Dashboard and Trusted Advisor","AWS Config and AWS CloudTrail","AWS IAM and AWS Organizations","The AWS Health Dashboard consists of the Personal Health Dashboard (PHD) and the Service Health Dashboard (SHD)."
"What does the Service Health Dashboard (SHD) within the AWS Health Dashboard display?","The overall health of AWS services across all regions","The health of individual EC2 instances","The status of your AWS support cases","The billing information for your AWS account","The Service Health Dashboard provides a general overview of the health of AWS services across all AWS Regions."
"What does the Personal Health Dashboard (PHD) within the AWS Health Dashboard display?","Issues that might affect your AWS resources","The overall health of AWS services across all regions","AWS security best practices","Estimated AWS costs for the month","The Personal Health Dashboard provides a personalised view of AWS service health, showing issues that directly impact your resources."
"How can you access the AWS Health Dashboard?","Through the AWS Management Console","Via a command-line interface (CLI) only","By subscribing to an SNS topic","Only through the AWS Support Center","The AWS Health Dashboard is accessible through the AWS Management Console."
"What type of events are typically reported on the AWS Health Dashboard?","Planned maintenance, service impacting events, and security vulnerabilities","Real-time stock market data","Social media trends","Local weather forecasts","The AWS Health Dashboard reports on planned maintenance activities, service-impacting events, and security vulnerabilities that might affect your AWS resources."
"If the AWS Health Dashboard reports an issue with an AWS service in a specific region, what does this typically indicate?","An issue that may affect resources you have in that region","A global issue affecting all AWS services","An issue isolated to a single AWS account","An issue caused by user error","The AWS Health Dashboard reports on issues that may affect your resources in the region where the service issue is occurring."
"Can you use the AWS Health Dashboard to track the progress of scheduled maintenance activities?","Yes, the dashboard provides updates on planned maintenance","No, it only reports on unexpected service disruptions","Only if you have a premium support plan","Only for EC2 instances","The AWS Health Dashboard provides visibility into planned maintenance activities that may affect your resources, along with updates on their progress."
"How does the AWS Health Dashboard differ from Amazon CloudWatch?","The Health Dashboard provides service health information, while CloudWatch provides performance monitoring.","CloudWatch provides service health information, while the Health Dashboard provides performance monitoring.","They both provide the same information.","The Health Dashboard is only for billing information, while CloudWatch is for performance monitoring.","The Health Dashboard focuses on the health of AWS services and potential impact to your resources, while CloudWatch focuses on monitoring the performance and operational health of your own infrastructure and applications."
"Which of the following is NOT a feature of the AWS Health Dashboard?","Real-time monitoring of EC2 CPU utilisation","Proactive notifications of potential issues","Visibility into planned maintenance activities","Personalised view of service health affecting your resources","The AWS Health Dashboard provides service health information, not real-time resource utilisation metrics."
"Can you receive notifications from the AWS Health Dashboard?","Yes, you can configure event-driven notifications via CloudWatch Events.","No, the dashboard only provides a visual interface.","Only via email.","Only if you have an Enterprise support plan.","You can configure event-driven notifications from the AWS Health Dashboard using CloudWatch Events, allowing you to automate actions based on service health events."
"What level of AWS Support plan is required to access the AWS Health Dashboard?","All AWS Support plans have access to the Health Dashboard.","Business and Enterprise support plans only.","Enterprise support plan only.","Developer support plan only.","All AWS Support plans, including Basic, can access the AWS Health Dashboard."
"What type of information can you find in the AWS Health Dashboard regarding security vulnerabilities?","Notification of vulnerabilities affecting your AWS resources","Password reset instructions","Credit card information","A list of AWS employees","The AWS Health Dashboard provides notifications about security vulnerabilities that may affect your AWS resources, allowing you to take appropriate action."
"If the AWS Health Dashboard reports an impending retirement of an EC2 instance type, what does this mean?","The EC2 instance type will no longer be supported, and you need to migrate your workloads.","Your EC2 instance is infected with malware.","Your EC2 instance is being upgraded automatically.","Your EC2 instance is being moved to a different region.","The retirement of an EC2 instance type means that AWS will no longer support that instance type, and you need to migrate your workloads to a supported instance type."
"How frequently is the information on the AWS Health Dashboard updated?","In near real-time","Once per day","Once per week","Once per month","The AWS Health Dashboard is updated in near real-time, providing you with the most current information about the health of AWS services."
"What action should you take if the AWS Health Dashboard indicates a degraded AWS service that is impacting your application?","Follow the recommended steps provided in the dashboard","Immediately terminate all your EC2 instances","Contact AWS support even if the issue is already reported","Ignore the issue and hope it resolves itself","The AWS Health Dashboard often provides recommended steps to mitigate the impact of a service degradation, such as switching to a different region or instance type."
"Can you use the AWS Health Dashboard to determine the root cause of an issue affecting your application?","The Health Dashboard provides information about the AWS service impacting your resources, but does not provide the root cause of application errors.","Yes, the Health Dashboard provides detailed root cause analysis.","Only if you have an Enterprise support plan.","Only for EC2 instances.","The AWS Health Dashboard primarily informs you of AWS service issues that might be impacting your application. Root cause analysis for the application itself requires further investigation."
"What should you do if you believe your application is being affected by an issue that is not reported on the AWS Health Dashboard?","Contact AWS Support to report the potential issue","Assume it is a problem with your application and troubleshoot accordingly","Ignore the issue and wait for the Health Dashboard to update","Immediately migrate your application to a different region","If you suspect an issue not reported on the dashboard, contacting AWS Support allows them to investigate and potentially identify a broader service impact."
"The AWS Health Dashboard provides information about events that affect your AWS infrastructure. Does it provide information about the health of third-party services integrated with your AWS environment?","No, the dashboard only provides information about AWS services.","Yes, the dashboard provides comprehensive health information for all services.","Only if the third-party service is an AWS Marketplace offering.","Only if you have an Enterprise support plan.","The AWS Health Dashboard focuses on the health of AWS services and does not provide information about the health of third-party services."
"When reviewing the AWS Health Dashboard, you see a notification stating 'EC2 instance scheduled for retirement'. What action should you take?","Plan to migrate the affected EC2 instance to a new instance","Immediately terminate the EC2 instance","Increase the size of the EC2 instance","Downgrade the EC2 instance type","A scheduled retirement means the instance will be unavailable soon, so planning a migration to a new instance is necessary."
"How does the AWS Health Dashboard help with disaster recovery planning?","By providing advance notice of planned maintenance and potential service disruptions","By automatically failing over your resources to a different region","By backing up your data to S3","By providing security vulnerability alerts","Advance notice of planned maintenance and potential disruptions allows you to proactively plan for disaster recovery scenarios."
"What is the relationship between AWS Trusted Advisor and the AWS Health Dashboard?","Trusted Advisor provides recommendations for optimising your AWS environment, while the Health Dashboard informs you about service health events.","The Health Dashboard provides recommendations for optimising your AWS environment, while Trusted Advisor informs you about service health events.","They both provide the same information.","Trusted Advisor is only available with Enterprise Support.","Trusted Advisor provides best practice recommendations across cost, security, performance, and fault tolerance, while the Health Dashboard focuses specifically on the health of AWS services."
"If the AWS Health Dashboard indicates that a specific AWS region is experiencing increased latency, what impact might this have on your application?","Increased response times and potential errors for resources in that region","No impact, as AWS services are always globally available","Decreased CPU utilization for EC2 instances","Improved security posture for your AWS account","Increased latency in a region can lead to slower response times and potential errors for resources running in that region."
"Which of the following is NOT a valid way to access the AWS Health Dashboard?","Using the AWS Mobile App","Using the AWS CLI","Using the AWS Management Console","Programmatically via the AWS SDK","While the Health API exists to pull data programmatically, the dashboard itself is accessed via the console, SDK and Mobile App."
"Which AWS service can be integrated with the AWS Health Dashboard to automate responses to service health events?","AWS CloudWatch Events","AWS CloudTrail","AWS Config","AWS IAM","AWS CloudWatch Events (now Amazon EventBridge) can be configured to trigger automated actions based on events reported in the AWS Health Dashboard."
"What is the scope of the AWS Health Dashboard notifications?","Account-specific and region-specific","Global and account-specific","Region-specific and service-specific","Global and service-specific","The AWS Health Dashboard provides notifications that are specific to your AWS account and the AWS Regions where you have resources."
"If you receive a notification in the AWS Health Dashboard about an 'impaired EC2 instance', what does this generally indicate?","There is a problem with the underlying hardware or software of the EC2 instance","The EC2 instance is running out of disk space","The EC2 instance is being attacked","The EC2 instance is being migrated","An 'impaired' EC2 instance typically indicates an issue with the underlying hardware or software that is preventing the instance from functioning correctly."
"You notice a planned maintenance event for Amazon RDS in the AWS Health Dashboard. What should you do to minimise the impact on your application?","Review the maintenance schedule and plan accordingly, potentially using Multi-AZ deployments","Immediately migrate your database to a different region","Ignore the maintenance event, as it will not affect your application","Delete the RDS instance and recreate it after the maintenance","Reviewing the maintenance schedule and taking appropriate steps, such as leveraging Multi-AZ deployments, helps minimise the impact of planned maintenance on your application."
"Which section of the AWS Health Dashboard provides information on issues that are affecting AWS services globally?","Service Health Dashboard (SHD)","Personal Health Dashboard (PHD)","AWS Trusted Advisor","AWS Support Center","The Service Health Dashboard (SHD) provides a broad overview of the health of AWS services across all AWS Regions."
"Which section of the AWS Health Dashboard provides information on issues specifically affecting your AWS resources?","Personal Health Dashboard (PHD)","Service Health Dashboard (SHD)","AWS Trusted Advisor","AWS Support Center","The Personal Health Dashboard (PHD) provides a personalised view of AWS service health, focusing on issues directly impacting your resources."
"What type of proactive information does the AWS Health Dashboard provide related to AWS service limits?","Notifications when you are approaching or exceeding service limits","Automatic increases to your service limits","Instructions for optimising your resource usage","Denial of Service attack alerts","The AWS Health Dashboard can notify you when you are nearing or have exceeded service limits, allowing you to request increases or optimise resource usage."
"Which of the following is NOT a common issue reported by the AWS Health Dashboard?","High CPU utilisation on your EC2 instances","Planned maintenance activities","Service impacting events","Security vulnerabilities affecting your resources","High CPU utilisation is a metric monitored by CloudWatch, not typically reported on the Health Dashboard."
"Can you view historical data on the AWS Health Dashboard to analyse past service disruptions?","Yes, the dashboard retains historical data for a limited period","No, the dashboard only shows current events","Only if you have an Enterprise support plan","Only for EC2 instances","The AWS Health Dashboard retains historical data for a limited period, allowing you to review past service disruptions."
"What is the purpose of the 'Affected Resources' tab within the Personal Health Dashboard?","To list the specific AWS resources impacted by a reported event","To display a list of all your AWS resources","To view the cost associated with impacted resources","To manage access control for impacted resources","The 'Affected Resources' tab lists the specific AWS resources in your account that are affected by a reported event, helping you quickly identify and address the impact."
"How can you use the AWS Health Dashboard to improve your application's resilience?","By proactively addressing planned maintenance events and potential service disruptions","By automatically scaling your resources during peak traffic","By encrypting your data at rest","By implementing multi-factor authentication","Proactively addressing planned maintenance and potential service disruptions helps improve your application's resilience."
"Which AWS service is commonly used to automate actions based on events reported in the AWS Health Dashboard?","Amazon EventBridge (formerly CloudWatch Events)","AWS CloudTrail","AWS Config","AWS IAM","Amazon EventBridge can be configured to trigger automated actions in response to events reported in the AWS Health Dashboard."
"You are investigating a performance issue with your application. Where should you primarily look for AWS infrastructure level issues?","AWS Health Dashboard","AWS Cost Explorer","AWS IAM console","AWS Marketplace","The AWS Health Dashboard would be the primary tool to review for infrastructure level issues impacting performance."
"Can you use the AWS Health Dashboard to report a service outage to AWS?","No, the Health Dashboard is for AWS to report to you, not the other way around","Yes, if you have an enterprise support plan","Yes, but only for EC2 instances","Yes, using the 'Report Issue' button","The AWS Health Dashboard is a one-way communication tool from AWS to you."
"What is a common use case for integrating the AWS Health Dashboard with a third-party monitoring tool?","To consolidate AWS service health information with other monitoring data in a central location","To bypass AWS support","To automatically deploy new AWS resources","To track AWS spending","Integrating the AWS Health Dashboard with third-party monitoring tools allows you to consolidate AWS service health information with other monitoring data in a central location."
"If you receive a notification from the AWS Health Dashboard regarding a scheduled maintenance window for Amazon RDS, what does this typically mean?","AWS will be performing maintenance on the underlying infrastructure that may impact your RDS instance","Your RDS instance is experiencing high CPU utilization","Your RDS instance is being migrated to a different region","Your RDS instance is running an outdated version of the database engine","A scheduled maintenance window indicates that AWS will be performing maintenance on the underlying infrastructure that may impact your RDS instance."
"Which of the following is NOT a benefit of using the AWS Health Dashboard?","Provides visibility into the health of AWS services impacting your resources","Offers proactive notifications about potential issues and planned maintenance","Allows you to directly manage your AWS resources from the dashboard","Helps you plan for disaster recovery scenarios","The AWS Health Dashboard provides visibility into service health and helps with planning, but it does not allow you to directly manage your AWS resources from the dashboard."
"What is the difference between a 'public' and a 'private' AWS Health Dashboard event?","Public events affect all AWS customers, while private events only affect your account","Public events are visible to everyone, while private events are only visible to AWS support","Public events are related to security vulnerabilities, while private events are related to performance issues","There is no such distinction, all events are displayed equally","Public events are general service issues affecting many customers. Private events are specific to your account."
"If the AWS Health Dashboard shows a region as having 'informational' status, what does this indicate?","AWS is providing information about an event that may or may not affect your resources","The region is experiencing a major outage","The region is undergoing routine maintenance","The region is being upgraded","'Informational' status indicates that AWS is providing information about an event that may or may not affect your resources. It is neither a major outage or maintenance, but a general information update"
"What is the primary purpose of the AWS Health Dashboard?","To provide personalised visibility into the health of AWS services and resources.","To monitor the CPU utilisation of EC2 instances.","To manage user access to AWS resources.","To configure network security groups.","The AWS Health Dashboard provides a personalised view of the health of AWS services and resources, highlighting events that may impact your applications."
"Which of the following components is NOT a feature of the AWS Health Dashboard?","Cost optimisation recommendations","Service health status","Personalised health alerts","Scheduled changes","The AWS Health Dashboard focuses on service health and events impacting your resources; cost optimisation isn't a core feature."
"In the context of the AWS Health Dashboard, what does a 'Service Health' event typically indicate?","A general issue affecting an AWS service in a specific region.","An issue specifically impacting your AWS account.","A security vulnerability in your application code.","A billing error related to your AWS usage.","Service Health events signal broader issues affecting AWS services that might impact multiple customers in a region."
"If the AWS Health Dashboard shows a 'Planned Maintenance' event, what does this mean?","AWS is planning to perform maintenance on a service that might affect your resources.","Your EC2 instances need immediate patching.","There is a security breach in your AWS account.","Your S3 bucket is nearing its storage limit.","'Planned Maintenance' events indicate that AWS is planning maintenance activities that could temporarily impact your resources."
"Which AWS service integrates directly with the AWS Health Dashboard to provide proactive alerts?","AWS Personal Health Dashboard","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Personal Health Dashboard (PHD) offers more personalized communication on events that impact your AWS infrastructure."
"How does the AWS Health Dashboard differ from Amazon CloudWatch?","The AWS Health Dashboard provides information about the health of AWS services, while CloudWatch monitors the performance of your AWS resources.","The AWS Health Dashboard monitors the performance of your AWS resources, while CloudWatch provides information about the health of AWS services.","The AWS Health Dashboard is a free service, while CloudWatch is a paid service.","The AWS Health Dashboard is only accessible through the AWS Management Console, while CloudWatch is accessible through the AWS CLI.","The Health Dashboard focuses on AWS service health impacting your resources, while CloudWatch is for monitoring resource performance and metrics."
"What information is provided in the 'Account Health' section of the AWS Health Dashboard?","Events that specifically affect your AWS account and resources.","Overall health status of all AWS services.","Cost optimisation recommendations for your AWS usage.","Security best practices for your AWS account.","Account Health gives you insight into events that are impacting your own AWS account."
"Which type of events are typically displayed in the AWS Health Dashboard?","AWS service events, planned maintenance, and security advisories.","CPU utilisation of EC2 instances, network traffic, and disk I/O.","Billing alerts, cost optimisation recommendations, and budget forecasts.","User login attempts, API calls, and security group changes.","The AWS Health Dashboard focuses on service health, planned maintenance, and potential security advisories."
"If you are experiencing issues with an AWS service, where should you FIRST check for relevant information?","The AWS Health Dashboard.","Amazon CloudWatch metrics.","AWS Trusted Advisor.","AWS Support Forums.","The AWS Health Dashboard provides real-time visibility into the health of AWS services, so this should always be your first port of call when troubleshooting issues."
"What is the best way to stay informed about critical AWS Health Dashboard events that may impact your applications?","Configure AWS Health Dashboard alerts through Amazon CloudWatch Events/EventBridge.","Manually check the AWS Health Dashboard every hour.","Subscribe to the AWS Support Forums.","Contact AWS Support directly.","Configuring alerts through CloudWatch Events/EventBridge allows you to automatically receive notifications about critical Health Dashboard events."
"Which of the following actions can you perform directly from the AWS Health Dashboard?","View affected resources and potential mitigation steps.","Terminate EC2 instances.","Modify security group rules.","Adjust billing preferences.","The AWS Health Dashboard shows affected resources and often includes guidance or mitigation steps."
"How frequently is the information on the AWS Health Dashboard updated?","In near real-time.","Every 24 hours.","Every week.","Once a month.","The information on the AWS Health Dashboard is kept current and is updated in near real-time."
"What does the AWS Health Dashboard NOT provide information about?","Third-party software installed on your EC2 instances.","AWS service outages.","Planned maintenance activities.","Security advisories affecting AWS services.","The AWS Health Dashboard is focused on the health of AWS services, not the software you install."
"Which AWS service can be used to automate actions in response to events reported in the AWS Health Dashboard?","Amazon EventBridge (formerly CloudWatch Events)","AWS CloudTrail","AWS Config","AWS IAM","Amazon EventBridge (formerly CloudWatch Events) can trigger automated actions based on events from the AWS Health Dashboard."
"If you see an 'Informational' event in the AWS Health Dashboard, what does this typically indicate?","General information about an AWS service that may not directly affect your resources.","A critical issue that requires immediate action.","A security vulnerability that needs to be patched.","A scheduled maintenance activity that will cause a brief outage.","'Informational' events provide general information and may not indicate a direct impact on your resources."
"Which level of AWS Support plan is required to access the AWS Health Dashboard?","All AWS Support plans include access to the AWS Health Dashboard.","Business Support or higher.","Enterprise Support only.","Developer Support or higher.","The AWS Health Dashboard is available to all AWS customers, regardless of their support plan."
"What type of information would you expect to find under the 'Resources' tab in the AWS Health Dashboard for an event?","A list of your AWS resources that are potentially affected by the event.","Links to AWS documentation about the impacted service.","A summary of the event and its potential impact.","Contact information for AWS Support.","The Resources tab lists the specific AWS resources in your account that the event may be affecting."
"Which of the following statements about the AWS Personal Health Dashboard (PHD) is true?","It provides personalised communication about events that impact your specific AWS infrastructure.","It provides general information about the health of AWS services globally.","It provides cost optimisation recommendations for your AWS account.","It provides security best practices for your AWS account.","The Personal Health Dashboard (PHD) is your personalised view of events affecting *your* AWS environment."
"What is the main difference between the AWS Service Health Dashboard and the AWS Personal Health Dashboard?","The Service Health Dashboard shows the general health of AWS services, while the Personal Health Dashboard shows events impacting your resources.","The Service Health Dashboard shows events impacting your resources, while the Personal Health Dashboard shows the general health of AWS services.","The Service Health Dashboard is accessible to all AWS users, while the Personal Health Dashboard requires a paid support plan.","The Service Health Dashboard provides real-time updates, while the Personal Health Dashboard is updated less frequently.","The Service Health Dashboard is a general overview; the Personal Health Dashboard is specific to your account."
"You receive a notification from the AWS Health Dashboard about a degraded AWS Region. What is the recommended first step?","Assess the impact on your applications and services running in that Region.","Immediately migrate your applications to another Region.","Contact AWS Support to report the issue.","Restart all your EC2 instances in the affected Region.","You must understand the impact before taking any actions."
"Where can you find information about scheduled maintenance activities that may impact your AWS resources?","AWS Health Dashboard","AWS Trusted Advisor","Amazon CloudWatch","AWS Cost Explorer","Scheduled maintenance is provided in the Health Dashboard."
"How can you proactively monitor the AWS Health Dashboard for events that affect your production environment?","Set up Amazon EventBridge rules to trigger notifications based on AWS Health Dashboard events.","Manually check the AWS Health Dashboard multiple times a day.","Subscribe to the AWS RSS feed for general AWS announcements.","Configure AWS Config rules to monitor the health of your AWS resources.","Automated notifications are key for fast responses."
"What does the AWS Health Dashboard rely on to identify events and provide personalised information?","AWS internal monitoring systems and data analysis.","Customer-provided feedback and reports.","Third-party monitoring tools.","Open-source community contributions.","The AWS Health Dashboard uses AWS internal monitoring to gather information."
"If an AWS service is experiencing widespread issues, where would you typically find the most up-to-date information?","AWS Service Health Dashboard.","AWS Personal Health Dashboard.","AWS Trusted Advisor.","AWS CloudTrail.","The Service Health Dashboard is the place to find information about global service outages."
"What is NOT a typical use case for the AWS Health Dashboard?","Monitoring CPU utilisation on individual EC2 instances.","Identifying service outages impacting your applications.","Receiving notifications about scheduled maintenance.","Understanding the potential impact of security advisories.","CPU utilisation is best monitored through CloudWatch."
"How does the AWS Health Dashboard help in disaster recovery planning?","By providing information about service availability and potential impacts during an event.","By automatically failing over your applications to a backup region.","By providing cost estimates for disaster recovery scenarios.","By providing a checklist of steps to take during a disaster.","The Health Dashboard can provide critical information during a disaster."
"What is the advantage of using the AWS Personal Health Dashboard over manually monitoring individual AWS services?","It provides a consolidated and personalised view of events affecting your specific resources.","It provides more detailed performance metrics than individual service dashboards.","It allows you to remotely manage your AWS resources.","It automatically resolves issues affecting your AWS services.","The Personal Health Dashboard (PHD) is your personalised view of events affecting *your* AWS environment, therefore is the best single view."
"If you receive a notification about a security vulnerability from the AWS Health Dashboard, what should you do?","Assess the potential impact on your environment and follow any recommended remediation steps.","Immediately shut down all your EC2 instances.","Change your AWS account password.","Ignore the notification, as AWS automatically patches all vulnerabilities.","Assessing the impact on the environment is an important step to see if the vulnerability applies, and recommended steps should be followed."
"How does the AWS Health Dashboard contribute to improving the reliability of your AWS applications?","By providing proactive notifications about potential issues and planned maintenance.","By automatically scaling your AWS resources to handle increased traffic.","By providing recommendations for optimising the cost of your AWS infrastructure.","By automatically backing up your data to prevent data loss.","The AWS Health Dashboard proactively notifies any potential issues and upcoming maintenance which helps improve AWS Application reliability."
"What kind of events would you typically find in the 'Open Issues' section of the AWS Health Dashboard?","Events that are currently impacting your AWS resources and require attention.","Events that have been resolved but may have had a past impact.","Scheduled maintenance activities that will occur in the future.","Security advisories that do not directly affect your resources.","'Open Issues' are the events that are actively impacting resources right now."
"You want to receive automated alerts for critical events reported by the AWS Health Dashboard. Which AWS service should you integrate with?","Amazon EventBridge","AWS CloudTrail","AWS Config","AWS IAM","Amazon EventBridge integrates with AWS Health Dashboard to send automated alerts for critical events."
"What is the best way to determine the scope of impact when you receive an AWS Health Dashboard notification?","Examine the resources listed in the 'Affected Resources' tab.","Contact AWS Support for a detailed assessment.","Review your AWS CloudTrail logs for suspicious activity.","Assume that all your resources are affected.","The affected resources tab lists the exact resources affected by the particular notification."
"You are investigating a performance issue with your application. Besides the AWS Health Dashboard, which other AWS service would be most helpful for diagnosing the root cause?","Amazon CloudWatch","AWS Trusted Advisor","AWS Config","AWS CloudFormation","CloudWatch provides performance metrics of your application and the resources your application utilises."
"How does the AWS Health Dashboard help you prioritise your incident response efforts?","By highlighting the most critical events that have the greatest potential impact on your business.","By providing automated solutions to resolve incidents.","By automatically escalating incidents to AWS Support.","By providing detailed documentation on how to troubleshoot each type of incident.","The AWS Health Dashboard will highlight the critical events that would affect your business the most."
"What is a key advantage of using the AWS Health Dashboard compared to relying solely on email notifications from AWS Support?","The AWS Health Dashboard provides a centralized and personalized view of all events affecting your environment.","Email notifications from AWS Support are generally more timely and detailed.","The AWS Health Dashboard is only available to customers with Enterprise Support plans.","Email notifications from AWS Support are more reliable.","The Health Dashboard centralises and personalises all events affecting an AWS environment."
"What action can you take directly from the AWS Health Dashboard to address an identified issue?","View recommended actions and resources to help mitigate the impact.","Terminate all EC2 instances.","Modify IAM roles.","Update security groups.","The AWS Health Dashboard recommends actions to resolve the identified issue."
"When would you typically use the AWS Service Health Dashboard instead of the AWS Personal Health Dashboard?","To check the overall health of AWS services in a specific region.","To diagnose a problem with your specific AWS resources.","To receive notifications about upcoming maintenance activities.","To monitor the cost of your AWS infrastructure.","Service Health Dashboard is used for when you need to check the health of a service within a region."
"How does the AWS Health Dashboard support compliance efforts?","By providing a record of AWS service availability and any impacting events.","By automatically auditing your AWS infrastructure for compliance violations.","By generating compliance reports for various regulatory standards.","By encrypting your data to protect it from unauthorized access.","AWS Health Dashboard stores a record of AWS Service availability which helps with compliance."
"What is the difference between 'Account-specific issues' and 'Service Health issues' in the AWS Health Dashboard?","Account-specific issues directly impact your resources, while Service Health issues affect a broader AWS service.","Account-specific issues affect a broader AWS service, while Service Health issues directly impact your resources.","Account-specific issues are displayed in the AWS Service Health Dashboard, while Service Health issues are displayed in the AWS Personal Health Dashboard.","Account-specific issues are resolved automatically by AWS Support, while Service Health issues require manual intervention.","'Account-specific issues' only affect your resources, and 'Service Health issues' affect a broader service."
"How does AWS decide to send notifications when there is an issue raised in the AWS Health Dashboard","Based on the resources affected in your account and your notification preferences","Notifications are only sent to the AWS Account owner","AWS support will contact you directly via phone","When there is a widespread AWS outage impacting all customers","Notifications are set based on the resources you own and your notification preferences. "
"What type of information is NOT typically included in an AWS Health Dashboard event description?","Estimated time to resolution for the issue.","Potential impact on your applications.","List of affected AWS regions.","Root cause analysis of the issue.","Usually root cause analysis is not included in event descriptions in the AWS Health Dashboard"
"What is the first action a Cloud Engineer should take when the AWS Health Dashboard reports an issue with an EC2 Instance","Identify if the EC2 instance is business critical or not and follow incident management processes accordingly","Immediately terminate the instance and launch a new one","Reboot the Instance without checking the health dashboard first","Contact AWS support to understand the issue better","The first action is to identify if the instance is business critical or not and then act accordingly."
"What should you check in the AWS Health Dashboard when you see an alarm in CloudWatch related to one of your EBS volumes?","The 'Account Health' and 'Service Health' sections to see if there are related events.","The Amazon EC2 console to view the status of the EBS volume.","The Amazon S3 console to check if the EBS volume is backed up.","The AWS Trusted Advisor console for cost optimisation recommendations.","It's recommended to always check the Account and Service health sections in the Health Dashboard as this will give you more context."
"Where in the AWS Health Dashboard, would you find more information on how AWS are communicating to you regarding events that can impact your AWS Environment","Notifications","Planned Maintenance","Open Issues","Resources","The Notifications section is the area in the AWS Health Dashboard that AWS uses to communicate with you on any events that can impact your AWS Environment."
"How do you ensure critical AWS Health Dashboard notifications are not missed?","Set up notifications using Amazon EventBridge and configure appropriate alerting mechanisms.","Rely solely on email notifications from AWS Support.","Manually check the AWS Health Dashboard every few minutes.","Subscribe to the AWS RSS feed for general AWS announcements.","Setting up notifications with EventBridge will ensure that critical notifications are not missed"
"If the AWS Health Dashboard displays an 'upcoming scheduled event', what action should you take?","Review the event details and assess the potential impact on your AWS resources and plan accordingly.","Ignore the event, as AWS will automatically handle any potential issues.","Immediately migrate all your AWS resources to a different region.","Contact AWS Support to request a change in the scheduled maintenance window.","Scheduled Maintenance events require you to review the resources that are impacted and prepare for it."
"What is the purpose of viewing ‘Affected Resources’ on the AWS Health Dashboard regarding an Open issue?","To quickly identify which of your resources are impacted so you can triage correctly.","To display the cost of the resources affected.","To display potential next steps on resolving the issue.","To quickly start and stop resources that may be impacted.","The Affected resources will identify what you need to triage or if you are seeing an issue with a resource."
"What is the difference between AWS Health and AWS Trusted Advisor?","AWS Health provides personalized visibility into the health of AWS services and resources while AWS Trusted Advisor provides best practice recommendations.","AWS Health provides best practice recommendations while AWS Trusted Advisor provides visibility into the health of AWS services and resources.","AWS Health helps you manage compliance, while AWS Trusted Advisor helps you maintain security.","AWS Health is a paid service, while AWS Trusted Advisor is a free service.","AWS Health provides visibility into the health of AWS services and resources, while AWS Trusted Advisor provides best practice recommendations."
"Which category of AWS Health Dashboard events informs you about planned activities that might affect your resources?","Scheduled Change Event","Open Issue Event","Resolved Issue Event","Security Event","Scheduled Change Events are planned activities by AWS that may require your attention."
"In the context of the AWS Health Dashboard, what does an 'Account Health Event' signify?","Issues specific to your AWS account that might require action","General AWS service disruptions affecting all customers","Notifications about billing and cost management","Information about new AWS features and services","Account Health Events relate to issues impacting your specific AWS account, such as security vulnerabilities or policy violations."
"How can you access the AWS Health Dashboard?","Through the AWS Management Console","Via the AWS CLI using specific commands","By subscribing to an SNS topic","By enabling CloudTrail logging","The AWS Health Dashboard is directly accessible through the AWS Management Console."
"If the AWS Health Dashboard shows a 'Service Health Event' impacting your EC2 instances, what is the best initial action?","Review the event details and assess the potential impact on your applications","Immediately reboot all affected EC2 instances","Migrate all EC2 instances to a different region","Change the instance type of all affected EC2 instances","Reviewing the event details allows you to understand the nature of the issue and determine the potential impact on your applications, guiding your next steps."
"Which AWS service can be integrated with the AWS Health Dashboard to receive proactive alerts about events affecting your resources?","Amazon CloudWatch Events (EventBridge)","AWS Config","AWS CloudTrail","AWS Trusted Advisor","Amazon CloudWatch Events (EventBridge) can be used to create rules that trigger actions based on Health Dashboard events, enabling proactive alerting."
"Can the AWS Health Dashboard be used to determine if an issue is caused by a problem on the user's side, such as incorrect configuration?","It can provide indications if the issue is related to AWS, helping to rule out user-side problems","It provides detailed diagnostics for user configuration issues","It provides a root cause analysis for every issue","It automatically fixes user configuration errors","While not directly diagnosing user configurations, the Health Dashboard can help determine if an issue stems from AWS infrastructure, thus indirectly helping to rule out user-side problems."
"Which AWS service provides detailed historical records of AWS Health Dashboard events for auditing and analysis?","AWS CloudTrail","Amazon CloudWatch Logs","AWS Config","AWS X-Ray","AWS CloudTrail logs API calls made to the AWS Health Dashboard, allowing for auditing and analysis of events over time."
"Besides the AWS Management Console, how else can you programmatically retrieve AWS Health Dashboard information?","Using the AWS Health API","Through Amazon SQS","Via Amazon SNS","By directly querying the AWS Support database","The AWS Health API provides programmatic access to the Health Dashboard, allowing you to retrieve event information and integrate it into your own systems."
"Which AWS service can be integrated with the AWS Health Dashboard to automate remediation actions for certain types of events?","AWS Systems Manager","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Systems Manager can be integrated with the AWS Health Dashboard to automatically run pre-defined actions when specific events occur, automating remediation."
"You are responsible for a production application. How can you use the AWS Health Dashboard to be proactive about potential issues?","By subscribing to receive notifications about upcoming scheduled events","By monitoring CPU utilisation of EC2 instances","By reviewing monthly cost reports","By setting up read replicas for your database","The AWS Health Dashboard provides information about upcoming scheduled events that may affect your resources, allowing you to plan proactively."
"Which type of event would you typically NOT find in the AWS Health Dashboard?","Planned maintenance for an EC2 instance","Service degradation affecting multiple AWS regions","Security vulnerabilities in third-party software","Individual instance failures not related to a service event","The AWS Health Dashboard focuses on events that impact AWS services and resources, not specific third-party software vulnerabilities."
"How does the AWS Health Dashboard differ from the Amazon CloudWatch dashboard?","The AWS Health Dashboard shows the health of AWS services, while CloudWatch monitors resource metrics.","The AWS Health Dashboard shows only cost information, while CloudWatch shows performance metrics.","The AWS Health Dashboard is only for enterprise customers, while CloudWatch is for all customers.","The AWS Health Dashboard shows aggregated data across all AWS accounts, while CloudWatch is account-specific.","The AWS Health Dashboard is focused on the health of AWS services, providing a high-level overview of issues, while CloudWatch monitors the performance and health of individual resources within your account."
"You are experiencing connectivity issues with your application and suspect an AWS service outage. What should be your first step using the AWS Health Dashboard?","Check the AWS Health Dashboard for any reported service events in your region","Review your CloudWatch metrics for increased latency","Contact AWS Support directly","Restart your EC2 instances","The AWS Health Dashboard provides information on any AWS service events which can affect your services, and will immediately show whether or not the problem is on AWS side."
"What is the difference between 'Public Health Dashboard' and 'Personal Health Dashboard'?","The Public Health Dashboard shows general AWS service health, while the Personal Health Dashboard shows events affecting your specific resources.","The Public Health Dashboard requires a paid subscription, while the Personal Health Dashboard is free.","The Public Health Dashboard shows past events, while the Personal Health Dashboard shows current events.","The Public Health Dashboard is for AWS employees, while the Personal Health Dashboard is for customers.","The Public Health Dashboard displays the overall health of AWS services, whereas the Personal Health Dashboard focuses on events impacting your particular AWS resources."
"How does the AWS Health Dashboard help with compliance and auditing?","By providing a record of service events and their impact on your resources","By automatically generating compliance reports","By encrypting your data at rest","By managing your IAM policies","The AWS Health Dashboard helps with compliance and auditing by providing a historical record of service events that may have affected your resources, allowing you to demonstrate transparency and accountability."
"You are using AWS Organizations. Where can you view the consolidated health of AWS services across all accounts in your organisation?","In the master account's AWS Health Dashboard","In each individual account's CloudWatch dashboard","In AWS CloudTrail logs","In AWS Config rules","The AWS Health Dashboard in the master account of AWS Organizations provides a consolidated view of service health across all member accounts."