"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS resources","To monitor the health of EC2 instances","To automate the deployment of applications","To manage AWS IAM roles and permissions","AWS Compute Optimizer analyses the configuration and utilisation metrics of your AWS resources and recommends optimal AWS resources to reduce costs and improve performance."
"Which AWS resource types are supported by AWS Compute Optimizer for generating recommendations?","EC2 instances, EBS volumes, Lambda functions and Auto Scaling groups","S3 buckets, DynamoDB tables and CloudFront distributions","SNS topics, SQS queues and API Gateway endpoints","VPC networks, Route 53 domains and CloudWatch dashboards","Compute Optimizer supports EC2 instances, EBS volumes, Lambda functions and Auto Scaling groups offering compute recommendations."
"What type of data does AWS Compute Optimizer use to generate resource optimisation recommendations?","Historical utilisation metrics","Real-time network traffic data","User feedback surveys","Application code analysis","Compute Optimizer uses historical utilisation metrics of your AWS resources, such as CPU utilisation, memory utilisation, network I/O, and disk I/O to generate recommendations."
"What is a potential benefit of implementing AWS Compute Optimizer's recommendations?","Reduced AWS costs","Improved network latency","Increased storage capacity","Enhanced security posture","Implementing Compute Optimizer's recommendations can lead to significant cost savings by optimising resource utilisation and reducing unnecessary spending."
"What does AWS Compute Optimizer's 'Over-provisioned' finding indicate for an EC2 instance?","The instance is larger than required for its workload","The instance is experiencing network congestion","The instance is running out of storage space","The instance has too many security vulnerabilities","An 'Over-provisioned' finding means that the EC2 instance is larger than necessary to handle its workload, leading to wasted resources and higher costs."
"What is the purpose of the AWS Compute Optimizer API?","To programmatically access and manage Compute Optimizer's recommendations","To create and manage EC2 instances","To configure AWS security groups","To monitor AWS billing and usage","The Compute Optimizer API allows developers to programmatically access and manage recommendations, enabling integration with automation tools and custom workflows."
"Which AWS service does AWS Compute Optimizer integrate with to provide recommendations for Auto Scaling groups?","Amazon EC2 Auto Scaling","Amazon CloudWatch","AWS CloudFormation","AWS IAM","Compute Optimizer analyses the performance of Auto Scaling groups based on data collected by CloudWatch to provide optimisation advice."
"Which of the following is NOT a metric considered by AWS Compute Optimizer when generating recommendations for EC2 instances?","Page faults","CPU utilization","Network I/O","Disk I/O","Page faults is not a directly considered metric in AWS Compute Optimizer's EC2 instance recommendations."
"In AWS Compute Optimizer, what does a 'Low Risk' recommendation typically indicate?","The suggested instance type is highly likely to meet the workload's performance requirements","The suggested instance type may result in performance degradation","The suggested instance type will significantly reduce costs but may impact performance","The suggested instance type requires extensive testing before implementation","A 'Low Risk' recommendation means that the suggested instance type is highly likely to meet the workload's performance requirements with minimal risk of performance degradation."
"How often does AWS Compute Optimizer typically update its resource optimisation recommendations?","Daily","Weekly","Monthly","Yearly","AWS Compute Optimizer updates its recommendations daily to reflect the latest performance data and usage patterns."
"What is the role of the 'Enhance Infrastructure Metrics' option in AWS Compute Optimizer?","Collects additional performance data to improve recommendation accuracy","Increases the size of the recommended instance type","Reduces the number of recommendations generated","Disables the collection of utilisation metrics","Enabling 'Enhance Infrastructure Metrics' allows Compute Optimizer to gather more detailed performance data, leading to more accurate and tailored recommendations."
"Which AWS service needs to be enabled for Compute Optimizer to make recommendations for Lambda functions?","AWS Lambda","AWS X-Ray","Amazon CloudWatch","AWS CloudTrail","Compute Optimizer needs AWS Lambda to be enabled to provide optimisation advice."
"What is the meaning of 'Rightsizing' in the context of AWS Compute Optimizer?","Selecting the appropriate instance type to match the workload's resource requirements","Migrating an application to a different AWS region","Optimising the storage configuration for an EC2 instance","Improving the security posture of an AWS environment","Rightsizing refers to selecting the optimal instance type to meet the performance needs of a workload while minimising costs."
"Which of the following statements about AWS Compute Optimizer is TRUE?","It can provide recommendations for on-premises infrastructure.","It requires manual configuration of resource utilisation thresholds.","It automatically adjusts the size of EC2 instances based on real-time usage.","It can provide recommendations for EBS volume types and sizes.","Compute Optimizer provides recommendations for EBS volume types and sizes to improve performance and reduce costs."
"How can you access AWS Compute Optimizer recommendations?","AWS Management Console, AWS CLI, and AWS SDKs","Only through the AWS Management Console","Only through the AWS CLI","Only through AWS Partner Network","AWS Compute Optimizer recommendations can be accessed through the AWS Management Console, AWS CLI, and AWS SDKs for flexible integration."
"What IAM permission is required to allow AWS Compute Optimizer to access utilisation metrics for EC2 instances?","ReadOnlyAccess","ComputeOptimizerServiceRolePolicy","EC2FullAccess","IAMFullAccess","The ComputeOptimizerServiceRolePolicy IAM permission is required to allow Compute Optimizer to access the necessary utilisation metrics."
"If AWS Compute Optimizer recommends a smaller EC2 instance type, what should you do before implementing the change in production?","Perform thorough testing to ensure the application performance is not negatively impacted","Immediately implement the change to reduce costs as quickly as possible","Disable monitoring on the EC2 instance to avoid false alerts","Ignore the recommendation if the current instance type has been stable for a long time","Before implementing any downsizing recommendations, thorough testing is crucial to ensure the application's performance isn't negatively affected."
"Which of the following is NOT a benefit of using AWS Compute Optimizer for EBS volumes?","Identifying underutilised volumes","Rightsizing volumes to reduce costs","Improving EBS volume throughput","Automating EBS volume backups","Compute Optimizer helps identify underutilised volumes and rightsizes them to reduce costs. It also supports improving EBS volume throughput, but it doesn't automate backups."
"What action does AWS Compute Optimizer take when you opt-in to enhanced infrastructure metrics?","It leverages CloudWatch Agent to collect more detailed metrics","It automatically migrates your instances to a different Availability Zone","It increases the storage capacity of your EBS volumes","It disables detailed monitoring to reduce CloudWatch costs","When you opt-in to enhanced infrastructure metrics, Compute Optimizer uses the CloudWatch Agent to collect more granular data, resulting in more accurate recommendations."
"Which AWS cost management tool complements AWS Compute Optimizer by providing detailed cost breakdowns?","AWS Cost Explorer","AWS Budgets","AWS Trusted Advisor","AWS Billing and Cost Management","AWS Cost Explorer helps to breakdown your overall AWS costs."
"What is the impact of opting-in to AWS Compute Optimizer's cross-account analysis?","Compute Optimizer can generate recommendations for resources across multiple AWS accounts within your organisation","Compute Optimizer can generate recommendations for resources in multiple AWS regions","Compute Optimizer will begin generating recommendations for resources managed by other AWS services","Compute Optimizer can now provide recommendations for on-premise servers","Opting-in to cross-account analysis allows Compute Optimizer to provide optimisation recommendations across multiple AWS accounts within your organisation, enhancing resource utilisation."
"Which EC2 pricing model is most suitable for resources recommended by AWS Compute Optimizer for steady-state workloads?","Reserved Instances","Spot Instances","On-Demand Instances","Dedicated Hosts","Reserved Instances are most suitable for steady-state workloads because they offer significant cost savings compared to On-Demand instances, making them ideal for resources recommended by Compute Optimizer for consistent usage."
"You notice AWS Compute Optimizer is consistently recommending instance types from a specific instance family that you want to avoid. How can you prevent this?","Create a preference in AWS Compute Optimizer to exclude that instance family","Manually override the recommendations for each individual resource","Disable AWS Compute Optimizer for all resources","Contact AWS support to blacklist the instance family","You can create a preference in AWS Compute Optimizer to exclude specific instance families, ensuring that future recommendations align with your architectural constraints and preferences."
"What is the key difference between Compute Optimizer recommendations for EC2 instances and Auto Scaling groups?","EC2 instance recommendations focus on individual instances, while Auto Scaling group recommendations consider the group's overall performance and scalability","EC2 instance recommendations are based on real-time metrics, while Auto Scaling group recommendations are based on historical data","EC2 instance recommendations provide specific instance types, while Auto Scaling group recommendations provide a range of instance types","EC2 instance recommendations are free, while Auto Scaling group recommendations require a paid subscription","EC2 instance recommendations focus on individual instances, while Auto Scaling group recommendations consider the group's overall performance, scaling policies, and workload patterns."
"What does the utilisation percentage in AWS Compute Optimizer recommendations represent?","The average resource utilization of the instance or volume during the observation period","The peak resource utilization of the instance or volume during the observation period","The percentage of time the instance or volume was actively used","The percentage of available resources that were allocated to the instance or volume","The utilisation percentage indicates the average resource utilization during the observation period, providing insights into how efficiently the resource is being used."
"Which factor is LEAST important when evaluating AWS Compute Optimizer recommendations?","The reputation of the software installed on the instance","Cost savings","Performance impact","Risk level","The reputation of the software installed on the instance is not a factor, whereas cost savings, performance impact, and risk level are very important."
"Which of the following AWS resources is NOT directly optimised by AWS Compute Optimizer?","AWS ECS clusters","Amazon EC2 instances","Amazon EBS volumes","AWS Lambda functions","AWS Compute Optimizer does not directly optimise Amazon ECS clusters, which require different optimisation approaches focused on container resource allocation and scheduling."
"If AWS Compute Optimizer provides recommendations but you disagree with them, what should you do?","Analyse the recommendations in detail, considering your specific workload requirements and constraints, and adjust your resource configuration accordingly","Ignore the recommendations, as they are only suggestions and may not be relevant to your environment","Implement the recommendations immediately to benefit from the potential cost savings","Contact AWS support to request a manual review of your resource configuration","You should carefully analyse the recommendations, considering your specific workload, performance requirements, and constraints, to make informed decisions about resource configuration."
"What is the purpose of the AWS Compute Optimizer dashboard?","To provide a centralised view of resource optimisation opportunities across your AWS environment","To configure AWS Compute Optimizer settings and preferences","To monitor the real-time performance of your EC2 instances","To generate detailed cost reports for your AWS usage","The dashboard provides a centralised view of potential resource optimisation opportunities, allowing you to quickly identify and address underutilised or over-provisioned resources."
"What is a potential drawback of aggressively rightsizing EC2 instances based solely on AWS Compute Optimizer recommendations?","Potential performance degradation if the workload's resource demands increase unexpectedly","Increased complexity in managing a larger number of smaller instances","Higher costs associated with migrating to different instance types","Reduced visibility into resource utilisation patterns","Aggressively rightsizing without considering potential future growth or unexpected workload changes can lead to performance degradation, impacting application availability and user experience."
"Which AWS CloudWatch metric is most relevant for determining whether an EC2 instance is CPU-bound, and thus a candidate for rightsizing?","CPUUtilization","NetworkIn","DiskWriteOps","CPUCreditBalance","CPUUtilization directly measures the percentage of CPU resources being used, helping you identify CPU-bound instances that may benefit from rightsizing."
"How does AWS Compute Optimizer help with choosing the right EBS volume type?","By recommending the most cost-effective volume type based on IOPS and throughput requirements","By automatically migrating EBS volumes to different storage tiers","By providing detailed performance metrics for each EBS volume type","By identifying unused EBS volumes that can be deleted","Compute Optimizer analyses the IOPS and throughput patterns of EBS volumes and recommends the most cost-effective volume type to meet those requirements, potentially saving you money without sacrificing performance."
"When evaluating AWS Compute Optimizer recommendations for Lambda functions, what is the most important metric to consider?","Duration","Invocations","Errors","Throttles","Duration is the most important metric for evaluating Lambda functions, as it directly reflects the execution time and resource consumption of the function."
"Which AWS feature can you use to automate the process of applying AWS Compute Optimizer recommendations?","AWS Systems Manager Automation","AWS CloudFormation","AWS Config","AWS Lambda","AWS Systems Manager Automation can be used to automate the process of applying Compute Optimizer recommendations, such as resizing EC2 instances or modifying EBS volume types."
"What does the term 'Savings Opportunity' refer to within the context of AWS Compute Optimizer?","The potential cost savings that can be achieved by implementing the recommended resource optimisations","The amount of unused resources in your AWS environment","The percentage of your AWS bill that can be reduced through Compute Optimizer","The number of underutilised resources identified by Compute Optimizer","The 'Savings Opportunity' represents the potential cost savings that can be realised by implementing the recommended resource optimisations, providing a clear indication of the financial benefits of using Compute Optimizer."
"How can you ensure that AWS Compute Optimizer recommendations align with your organisation's security policies?","By reviewing and validating the recommendations against your security requirements before implementation","By disabling AWS Compute Optimizer for sensitive resources","By automatically accepting all Compute Optimizer recommendations without manual review","By configuring AWS Compute Optimizer to only recommend resources that meet specific security standards","You should always review and validate Compute Optimizer recommendations against your security requirements to ensure that any changes align with your organisation's policies and compliance standards."
"What happens to historical data used by AWS Compute Optimizer when a resource is terminated?","The historical data is retained for a limited period to support trend analysis and future recommendations","The historical data is immediately deleted to protect user privacy","The historical data is archived for compliance purposes","The historical data is anonymised and used for aggregate statistics","The historical data is retained for a limited period to support trend analysis and future recommendations, allowing Compute Optimizer to learn from past usage patterns."
"Which AWS service can you integrate with AWS Compute Optimizer to receive notifications about new recommendations?","Amazon Simple Notification Service (SNS)","Amazon CloudWatch Events","AWS CloudTrail","AWS Config","Amazon Simple Notification Service (SNS) can be used to receive notifications about new Compute Optimizer recommendations, enabling you to proactively address resource optimisation opportunities."
"What is the maximum number of days of historical data used by AWS Compute Optimizer to generate recommendations?","93 days","30 days","60 days","180 days","AWS Compute Optimizer utilises a maximum of 93 days of historical data to give an accurate recommendation."
"How does AWS Compute Optimizer help improve the performance of Lambda functions?","By recommending optimal memory allocation settings","By suggesting alternative programming languages","By automatically optimising code execution","By migrating Lambda functions to different AWS regions","Compute Optimizer recommends memory allocation sizes to improve execution performance."
"Which AWS service can be used to monitor the actual cost savings achieved by implementing AWS Compute Optimizer recommendations?","AWS Cost Explorer","AWS Trusted Advisor","AWS Budgets","AWS CloudWatch","AWS Cost Explorer is a very suitable option as it will allow you to dig into the savings achieved when implementing AWS Compute Optimizer recommendations."
"Which of the following is a key benefit of using AWS Compute Optimizer for Auto Scaling groups?","Ensuring that the group has the correct instance types and scaling policies to meet demand","Automatically adjusting the scaling policies based on real-time traffic patterns","Optimising the network configuration for the Auto Scaling group","Reducing the cost of data transfer between instances in the group","AWS Compute Optimizer helps ensure the group has the correct instance types and scaling policies to meet demand, potentially reducing over-provisioning and improving performance."
"What is the relationship between AWS Trusted Advisor and AWS Compute Optimizer?","Compute Optimizer provides more detailed and specific resource optimisation recommendations than Trusted Advisor","Trusted Advisor provides more detailed and specific resource optimisation recommendations than Compute Optimizer","Compute Optimizer replaces Trusted Advisor as the primary tool for resource optimisation","Trusted Advisor and Compute Optimizer are completely independent and do not interact","Compute Optimizer provides more detailed and specific resource optimisation recommendations, while Trusted Advisor provides general best-practice recommendations across various categories."
"Which of the following EC2 pricing models is best suited for workloads with variable or unpredictable resource demands, according to AWS Compute Optimizer?","On-Demand Instances","Reserved Instances","Dedicated Hosts","Spot Instances","On-Demand instances are typically best suited for these types of use cases."
"Which of the following is an example of a 'medium risk' recommendation from AWS Compute Optimizer?","A recommendation to change to an instance type that is slightly smaller than the current instance, but has a similar performance profile","A recommendation to change to an instance type that is significantly smaller than the current instance, and may result in some performance degradation","A recommendation to change to an instance type that is larger than the current instance, to improve performance","A recommendation to change to an instance type that is from a completely different instance family, with unknown performance characteristics","The key part of the definition of medium risk is around 'significantly smaller' and 'may result in some performance degradation'"
"What is a potential disadvantage of using AWS Compute Optimizer for long-running, CPU-intensive workloads?","The recommendations may not accurately reflect the sustained CPU utilisation patterns over extended periods","The recommendations may be too conservative, resulting in over-provisioned resources","The recommendations may be too aggressive, leading to performance bottlenecks","The recommendations are only applicable to burstable workloads, not sustained workloads","The recommendations may not accurately reflect the sustained CPU utilisation patterns over extended periods, especially if the workload has significant variations in CPU usage over time."
"Which of the following factors is most likely to influence the accuracy of AWS Compute Optimizer recommendations?","The amount and quality of historical utilisation data available","The number of AWS regions in which the resources are deployed","The complexity of the application architecture","The level of security applied to the AWS environment","The amount and quality of historical utilisation data available is one of the most important factors."
"In AWS Compute Optimizer, what is the primary goal of providing recommendations?","To identify optimal AWS resource configurations for cost and performance.","To manage AWS IAM permissions.","To monitor network traffic.","To automate security patching.","Compute Optimizer analyzes resource utilization and configuration data to suggest right-sizing recommendations that balance cost and performance."
"Which AWS service does Compute Optimizer primarily use to collect resource utilisation data?","CloudWatch","CloudTrail","Config","X-Ray","Compute Optimizer integrates with CloudWatch to retrieve metrics like CPU utilization, memory utilization, and network I/O, which are essential for analysis."
"What type of AWS resource does Compute Optimizer support for recommendations?","EC2 instances and EBS volumes","S3 buckets and Glacier vaults","IAM roles and policies","CloudFront distributions and Lambda functions","Currently, Compute Optimizer focuses on providing recommendations for EC2 instances to optimise compute, and EBS volumes to optimise storage."
"What does the 'under-provisioned' finding in Compute Optimizer typically indicate?","The resource is not meeting the performance requirements of the workload.","The resource is running at maximum capacity.","The resource is being billed incorrectly.","The resource is properly sized.","An 'under-provisioned' finding suggests that the current resource configuration is insufficient to meet the workload's performance demands, leading to potential performance bottlenecks."
"Which of the following factors does AWS Compute Optimizer consider when making resource optimisation recommendations?","CPU utilisation, memory utilisation, network I/O","Number of IAM users, security group rules, VPC configuration","Cost of AWS support plans, AWS Region, instance family","The number of S3 buckets, Lambda invocations, CloudFront distributions","Compute Optimizer primarily uses resource utilisation metrics like CPU, memory, and network to assess the current configuration and make optimisation recommendations."
"If Compute Optimizer recommends a smaller instance type, what is it trying to achieve?","Reduce costs without significantly impacting performance.","Increase the availability of the application.","Improve the security posture of the environment.","Migrate the application to a different AWS Region.","Right-sizing by recommending a smaller instance aims to lower costs associated with over-provisioning while ensuring the workload's performance remains acceptable."
"In AWS Compute Optimizer, what is the purpose of the 'performance risk' metric?","To quantify the likelihood of performance degradation with the recommended instance type.","To measure the security vulnerabilities of the current infrastructure.","To estimate the cost savings from implementing the recommendations.","To track the network latency between different AWS services.","The performance risk metric helps you understand the potential impact of implementing Compute Optimizer's recommendations on your workload's performance."
"What is the first step to start using AWS Compute Optimizer?","Opt-in through the AWS Management Console or API.","Install the Compute Optimizer agent on each EC2 instance.","Create a new IAM role with full administrator privileges.","Configure a CloudWatch dashboard with custom metrics.","To begin using Compute Optimizer, you must opt-in to allow the service to access and analyse your resource utilization data."
"Can AWS Compute Optimizer provide recommendations for EC2 instances in Auto Scaling groups?","Yes, Compute Optimizer provides recommendations for EC2 instances in Auto Scaling groups.","No, Compute Optimizer only supports standalone EC2 instances.","Only if the Auto Scaling group has a fixed size.","Only if the Auto Scaling group is using on-demand instances.","Compute Optimizer can analyse and provide recommendations for EC2 instances that are part of Auto Scaling groups, which helps optimise dynamically scaling workloads."
"Which pricing model for EC2 instances does AWS Compute Optimizer support for recommendations?","On-Demand, Reserved, and Spot Instances","Only On-Demand Instances","Only Reserved Instances","Only Spot Instances","Compute Optimizer can provide recommendations regardless of the pricing model you are using for your EC2 instances, including On-Demand, Reserved, and Spot Instances."
"What is the benefit of using AWS Compute Optimizer's enhanced infrastructure metrics?","It provides more granular data for memory utilisation and disk I/O, leading to more accurate recommendations.","It automates patching of the operating system.","It automatically backups EC2 instances.","It enables centralised logging of all AWS services.","Enhanced infrastructure metrics, specifically memory utilization and disk I/O, are crucial for understanding a workload's resource demands and providing more informed right-sizing suggestions."
"When viewing AWS Compute Optimizer recommendations, what does a 'current configuration' refer to?","The current EC2 instance type and EBS volume configuration being analysed.","The total cost of all AWS resources in the account.","The number of security groups attached to the EC2 instance.","The average network latency for the EC2 instance.","The 'current configuration' refers to the existing resource setup (EC2 instance type, EBS volume size, etc.) that Compute Optimizer is evaluating for potential improvements."
"How frequently does AWS Compute Optimizer typically update its recommendations?","Every 24 hours","Every hour","Every week","Every month","Compute Optimizer regularly updates its recommendations, approximately every 24 hours, to reflect changes in resource utilisation and provide the most current optimisation opportunities."
"What is the purpose of the AWS Compute Optimizer API?","To programmatically access and manage Compute Optimizer recommendations.","To configure IAM roles and permissions.","To manage CloudWatch alarms.","To deploy EC2 instances.","The Compute Optimizer API enables developers to integrate the service's functionality into their automation workflows and programmatically retrieve and manage recommendations."
"Which AWS region does AWS Compute Optimizer need to be enabled in to receive recommendations across an entire AWS Account?","Each AWS region that contains AWS resources","The AWS Account's root region","The AWS global region","Any AWS region","AWS Compute Optimizer needs to be enabled in EACH region that contains resources that you want to receive recommendations for."
"Which of these metrics IS NOT used by Compute Optimizer to generate recommendations?","Memory utilisation","Network Packets Out","CPU utilisation","Disk I/O","Compute Optimizer does not use network packets out when generating recommendations"
"Which of the following is a primary benefit of using AWS Compute Optimizer?","Improved resource utilisation and cost savings","Enhanced security and compliance","Automated deployment and scaling","Simplified network configuration","The primary benefit is the ability to improve resource utilisation and reduce costs by identifying right-sizing opportunities."
"Which factor does AWS Compute Optimizer use to classify workload performance?","Performance risk","Security risk","Availability risk","Compliance risk","Performance risk is a key factor in classifying workload performance, indicating the potential for performance degradation with recommended configurations."
"What data sources are used by AWS Compute Optimizer to assess workload performance?","CloudWatch metrics and resource configuration data","AWS CloudTrail logs and VPC flow logs","AWS Config rules and security group configurations","AWS X-Ray traces and Lambda function metrics","Compute Optimizer uses CloudWatch metrics (e.g., CPU utilization, memory utilization) and resource configuration data to understand workload demands."
"If AWS Compute Optimizer recommends migrating an EC2 instance to a different instance family, what is the likely reason?","The recommended instance family offers better performance or cost characteristics for the workload.","The current instance family is being deprecated by AWS.","The recommended instance family has better security features.","The current instance family is not supported in the current AWS region.","Migrating to a different instance family is typically recommended when the new family offers a more efficient or cost-effective option for the workload."
"In AWS Compute Optimizer, what does a 'recommendation options' refer to?","Alternative EC2 instance types or EBS volume sizes that Compute Optimizer suggests.","Different IAM roles that can be assigned to the EC2 instance.","Various security groups that can be applied to the EC2 instance.","Different AWS regions where the EC2 instance can be deployed.","'Recommendation options' are the specific instance types or volume sizes that Compute Optimizer proposes as alternatives to the current configuration."
"How can you evaluate the impact of implementing AWS Compute Optimizer's recommendations before applying them in production?","By reviewing the 'performance risk' metric and historical performance data.","By conducting a full security audit of the infrastructure.","By running a cost analysis using the AWS Pricing Calculator.","By simulating the workload in a separate environment.","The 'performance risk' metric provides insights into the potential performance impact of the recommendations, while historical data helps validate their suitability."
"Which AWS service does AWS Compute Optimizer directly integrate with to provide enhanced infrastructure metrics for memory utilisation?","Amazon CloudWatch Agent","AWS CloudTrail","AWS Config","AWS X-Ray","The Amazon CloudWatch Agent collects enhanced metrics, including memory utilisation, which are then used by Compute Optimizer."
"If AWS Compute Optimizer identifies an EC2 instance as 'over-provisioned', what does this mean?","The EC2 instance has more resources than the workload requires, leading to unnecessary costs.","The EC2 instance is running close to its maximum capacity.","The EC2 instance is not properly configured for the workload.","The EC2 instance is experiencing performance bottlenecks.","'Over-provisioned' indicates that the instance has more resources (CPU, memory) than necessary, resulting in wasted spending."
"What is the AWS Compute Optimizer dashboard used for?","Viewing recommendations for EC2 instances and EBS volumes.","Managing IAM users and roles.","Configuring VPC settings.","Monitoring network traffic.","The dashboard provides a centralised view of Compute Optimizer's recommendations, enabling users to identify and prioritise optimisation opportunities."
"How can you exclude certain EC2 instances from AWS Compute Optimizer analysis?","By creating an exclusion list in the Compute Optimizer settings.","By removing the instances from the Auto Scaling group.","By disabling CloudWatch metrics for the instances.","By terminating the instances.","You can exclude specific instances from analysis by adding them to an exclusion list within Compute Optimizer's settings."
"Which AWS service provides the underlying infrastructure to run AWS Compute Optimizer?","It's a fully managed service that doesn't require any underlying infrastructure management.","Amazon EC2","Amazon ECS","AWS Lambda","Compute Optimizer is a fully managed service, meaning AWS handles the underlying infrastructure and management."
"In AWS Compute Optimizer, what does the term 'finding' refer to?","A recommendation for optimising a resource.","An error message indicating a problem with the service.","A security vulnerability detected in the infrastructure.","A notification about a new feature in Compute Optimizer.","A 'finding' is a recommendation generated by Compute Optimizer to improve the configuration of your resources."
"What type of EBS volumes does AWS Compute Optimizer provide recommendations for?","All EBS volume types (e.g., gp2, gp3, io1)","Only gp2 volumes","Only io1 volumes","Only st1 volumes","Compute Optimizer supports recommendations for all EBS volume types, helping optimise storage performance and cost."
"Which of the following is NOT a valid recommendation type from AWS Compute Optimizer?","Instance type change","Storage volume type change","Network configuration change","Instance family change","Compute Optimizer's recommendations focus on instance types, volume types, and instance families, not network configurations."
"How can you access AWS Compute Optimizer?","AWS Management Console, AWS CLI, and AWS SDKs","Only AWS Management Console","Only AWS CLI","Only AWS SDKs","You can access Compute Optimizer through the AWS Management Console, AWS CLI, and AWS SDKs, providing flexibility in how you interact with the service."
"What does AWS Compute Optimizer use to determine the current utilisation of an EBS volume?","CloudWatch metrics such as VolumeReadBytes and VolumeWriteBytes","EC2 instance CPU utilisation","VPC Flow Logs","S3 access logs","Compute Optimizer uses CloudWatch metrics like VolumeReadBytes and VolumeWriteBytes to assess the performance and utilisation of EBS volumes."
"If Compute Optimizer recommends 'No improvement opportunity found', what does this indicate?","The resource is already optimally configured for the workload.","Compute Optimizer is unable to collect data for the resource.","The resource is running at maximum capacity.","The resource is not supported by Compute Optimizer.","'No improvement opportunity found' suggests that the resource is currently configured optimally based on the available data."
"What is the maximum historical data that Compute Optimizer uses when creating a recommendation?","14 months","7 days","30 days","3 months","Compute Optimizer uses up to the past 14 months of metric data to generate recommendations"
"How does AWS Compute Optimizer help reduce the carbon footprint of AWS workloads?","By recommending more efficient resource configurations.","By optimising data transfer between AWS services.","By enabling automatic shutdown of unused resources.","By reducing the amount of data stored in S3.","By recommending right-sized resources, Compute Optimizer helps reduce energy consumption and the carbon footprint of AWS workloads."
"What is a limitation of AWS Compute Optimizer?","It does not support all AWS regions.","It requires manual installation of an agent on EC2 instances.","It cannot provide recommendations for databases.","It can only provide recommendations for EC2 and EBS resources","While Compute Optimizer covers common AWS resources, it doesn't support all services (e.g., databases).  It is important to consider this when deciding to use the service."
"What is one of the advantages of using Compute Optimizer?","Increased CPU performance for EC2 instances.","Reduced time spent on resource optimisation analysis.","Automated security patching of EC2 instances.","Improved network bandwidth between EC2 instances.","Compute Optimizer automates the resource analysis process, saving time and effort compared to manual analysis."
"How can AWS Compute Optimizer help with capacity planning?","By providing insights into resource utilisation trends and future resource needs.","By automatically scaling resources based on demand.","By predicting the cost of future AWS resources.","By optimising network bandwidth between AWS regions.","By analysing resource utilization trends, Compute Optimizer can inform capacity planning decisions by highlighting potential future resource needs."
"How often is AWS Compute Optimizer free to use?","AWS Compute Optimizer is free to use","AWS Compute Optimizer is free to use for a limited time only","AWS Compute Optimizer requires a monthly subscription","AWS Compute Optimizer is free to use with limited functionality","AWS Compute Optimizer is free to use and can generate recommendations immediately. However the advanced usage of the services is charged for. "
"How are the recommendations provided by AWS Compute Optimizer to be considered?","As recommendations only","As a must have change","As a starting point to identify possible problems","As a final advice","AWS Compute Optimizer does not have complete knowledge about your workload and so its advise should be considered as a recommendation to investigate further."
"Which of these is a supported Resource Type for Compute Optimizer?","AWS Lambda functions","AWS RDS clusters","EC2 instances","S3 Buckets","AWS Lambda functions, AWS RDS clusters and S3 Buckets are not supported Resource Type for Compute Optimizer. Only EC2 instances is a supported Resource Type for Compute Optimizer."
"What is the purpose of the AWS Compute Optimizer?","To reduce costs and improve performance of your AWS resources.","To manage your AWS security settings.","To monitor your AWS network traffic.","To automate your AWS deployments.","The purpose of the AWS Compute Optimizer is to reduce costs and improve performance of your AWS resources by identifying and recommending optimal resource configurations."
"Which type of metric is used by AWS Compute Optimizer to determine the suitability of an EBS volume?","Disk I/O","Memory utilisation","CPU utilisation","Network traffic","Disk I/O is the only metric listed that's used by AWS Compute Optimizer to determine the suitability of an EBS volume."
"How can you prevent AWS Compute Optimizer from analysing specific EC2 instances?","Tag the EC2 instances with a specific tag.","Remove the IAM role from the EC2 instances.","Disable the CloudWatch metrics for the EC2 instances.","Remove the EC2 instances from the VPC.","You can prevent AWS Compute Optimizer from analysing specific EC2 instances by tagging the instances with a specific tag. This allows you to exclude specific EC2 instances from the analysis."
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS resources","To monitor real-time application performance","To manage AWS Identity and Access Management (IAM) roles","To create and manage AWS Lambda functions","Compute Optimizer analyses the configuration and utilisation metrics of your AWS resources and recommends optimal AWS resources to reduce costs and improve performance."
"Which AWS resource types can AWS Compute Optimizer provide recommendations for?","EC2 instances, Auto Scaling groups, EBS volumes, Lambda functions","S3 Buckets, DynamoDB tables, CloudFront distributions, API Gateway","VPC networking, Route 53 DNS, CloudWatch alarms, SNS topics","IAM users, Security groups, KMS keys, CloudTrail logs","Compute Optimizer supports EC2 instances, Auto Scaling groups, EBS volumes and Lambda functions to provide rightsizing recommendations."
"What type of data does AWS Compute Optimizer primarily analyse to generate recommendations?","Historical utilisation metrics","Real-time network traffic data","Application log files","User activity logs","Compute Optimizer uses historical utilisation data such as CPU utilisation, memory utilisation, network I/O, and disk I/O to generate recommendations."
"How does AWS Compute Optimizer help reduce costs?","By identifying over-provisioned resources","By automatically scaling resources up and down","By optimising database queries","By enabling data compression","Compute Optimizer identifies over-provisioned resources that are not being fully utilised and recommends smaller, more cost-effective resource types."
"What is a key benefit of using AWS Compute Optimizer besides cost reduction?","Improved application performance","Enhanced security posture","Simplified resource deployment","Automated compliance reporting","By rightsizing resources, Compute Optimizer helps ensure that applications have the necessary resources to perform optimally, leading to improved performance."
"In AWS Compute Optimizer, what is an 'EC2 instance type recommendation'?","A suggestion to change the instance type to better match the workload","A recommendation to upgrade the operating system","A suggestion to change the security group configuration","A recommendation to change the IAM role","An EC2 instance type recommendation suggests changing the instance type to a more appropriate size or family based on the workload's performance characteristics."
"Which of the following is NOT a metric considered by AWS Compute Optimizer for EC2 instance recommendations?","Instance Store utilisation","CPU utilisation","Memory utilisation","Network I/O","Instance Store utilisation is not directly considered. Compute Optimizer focuses on CPU, memory, network, and disk I/O."
"Can AWS Compute Optimizer provide recommendations for Auto Scaling groups?","Yes, it can provide recommendations for Auto Scaling groups","No, it only supports EC2 instances","Only for Auto Scaling groups using specific launch templates","Only for Auto Scaling groups with a fixed size","Compute Optimizer analyses the performance of instances within Auto Scaling groups and provides recommendations to optimise the group's instance types."
"If AWS Compute Optimizer recommends a smaller EC2 instance type, what should you do before implementing the change?","Test the change in a non-production environment","Immediately apply the change to all instances","Deactivate Compute Optimizer for that instance","Ignore the recommendation if the application is critical","Testing the recommended change in a non-production environment helps to ensure that the application continues to perform as expected."
"How can you access AWS Compute Optimizer?","Through the AWS Management Console, AWS CLI, and AWS SDKs","Only through the AWS Management Console","Only through the AWS CLI","Only through AWS SDKs","Compute Optimizer is accessible through the AWS Management Console, AWS CLI, and AWS SDKs, allowing for flexible integration and management."
"What is the purpose of the 'Savings Opportunity' metric in AWS Compute Optimizer?","To estimate the potential cost savings from implementing recommendations","To measure the performance improvement after implementing recommendations","To track the number of recommendations generated","To assess the complexity of implementing recommendations","The 'Savings Opportunity' metric estimates the potential cost savings that can be achieved by implementing the resource optimisation recommendations provided by Compute Optimizer."
"Can AWS Compute Optimizer provide recommendations for EBS volumes?","Yes, it can provide recommendations to optimise EBS volume types and sizes","No, it only supports EC2 instances and Auto Scaling groups","Only for EBS volumes attached to specific EC2 instance types","Only for EBS volumes using specific encryption types","Compute Optimizer analyses the I/O characteristics of EBS volumes and provides recommendations to optimise their types and sizes for cost and performance."
"Which AWS service does AWS Compute Optimizer integrate with to retrieve resource utilisation data?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Compute Optimizer relies on Amazon CloudWatch metrics to gather resource utilisation data, which it then analyses to generate recommendations."
"What is the meaning of 'Over-provisioned' in the context of AWS Compute Optimizer?","The resource has more capacity than the workload requires","The resource has insufficient capacity for the workload","The resource is running at maximum utilisation","The resource is not properly configured","'Over-provisioned' means that the resource has more capacity than the workload currently requires, leading to wasted resources and higher costs."
"What is a limitation of AWS Compute Optimizer?","It does not provide recommendations for all AWS resource types","It cannot analyse historical utilisation data","It cannot integrate with other AWS services","It requires manual configuration of resource utilisation metrics","Compute Optimizer does not provide recommendations for all AWS resource types, such as databases or networking resources, although its scope is constantly increasing."
"How often does AWS Compute Optimizer typically refresh its recommendations?","Approximately every 24 hours","Continuously in real-time","Once per week","Once per month","Compute Optimizer typically refreshes its recommendations approximately every 24 hours, providing up-to-date suggestions based on recent resource utilisation."
"Which of the following is a key factor considered by AWS Compute Optimizer when recommending EBS volume changes?","IOPS and throughput","Volume encryption type","Volume snapshot history","Volume location","Compute Optimizer analyses the IOPS and throughput of EBS volumes to recommend more appropriate volume types and sizes for the workload."
"What is the benefit of integrating AWS Compute Optimizer with AWS Organizations?","To centralise recommendations across multiple AWS accounts","To automatically implement recommendations","To restrict access to recommendations","To generate compliance reports","Integrating with AWS Organizations allows you to centralise recommendations across multiple AWS accounts, providing a consolidated view of optimisation opportunities."
"Which of the following is NOT a supported recommendation by AWS Compute Optimizer for Lambda functions?","Memory allocation","Provisioned concurrency","Timeout settings","Runtime version","Compute Optimizer focuses on memory allocation to optimise Lambda function performance and cost. It does not advise on Timeout settings."
"What happens if AWS Compute Optimizer does not have sufficient data to generate a recommendation?","It will indicate that there is insufficient data","It will provide a default recommendation based on common patterns","It will automatically increase the resource size","It will assume the resource is optimally provisioned","Compute Optimizer needs sufficient historical data to provide accurate recommendations. If there's not enough data, it will indicate that."
"In AWS Compute Optimizer, what does 'Under-provisioned' indicate?","The resource does not have enough capacity to meet the workload demand","The resource is utilising 100% of its capacity","The resource is properly provisioned","The resource is idle","'Under-provisioned' indicates that the resource does not have enough capacity to meet the workload's demand, leading to potential performance bottlenecks."
"Which of the following AWS services is NOT directly integrated with AWS Compute Optimizer for taking action on recommendations?","AWS Systems Manager","AWS CloudFormation","AWS CloudWatch","AWS Auto Scaling","While Compute Optimizer uses CloudWatch for metrics and can inform Auto Scaling decisions, it does not directly integrate with CloudFormation or Systems Manager for automated remediation."
"How can you exclude specific EC2 instances from AWS Compute Optimizer analysis?","By opting out of Compute Optimizer for the specific instance","By deactivating CloudWatch metrics for the instance","By removing the instance from the Auto Scaling group","By terminating the instance","You can opt-out of Compute Optimizer for a specific instance so that it is not analysed."
"What is the primary reason to use AWS Compute Optimizer for Lambda functions?","To optimise memory allocation for cost savings","To optimise execution time for faster performance","To optimise code size for reduced deployment time","To optimise network connectivity for lower latency","The primary goal is to optimise memory allocation, which directly impacts the cost of running Lambda functions. Function execution time is also improved as a secondary effect of optimal memory allocation."
"Which of the following is a key consideration when evaluating AWS Compute Optimizer recommendations for EBS volumes?","The application's I/O latency requirements","The EBS volume's encryption status","The EC2 instance type the EBS volume is attached to","The availability zone of the EBS volume","The application's I/O latency requirements are a key consideration as choosing a smaller or different type of EBS volume can impact performance."
"Which feature of AWS Compute Optimizer allows you to view aggregated recommendations across your AWS environment?","Dashboard view","Recommendation export","Automated reporting","Resource summary","The dashboard view provides an aggregated view of recommendations across all supported resources and accounts in your AWS environment."
"How does AWS Compute Optimizer handle recommendations for EC2 instances in Auto Scaling groups?","It provides recommendations based on the entire group's performance","It provides recommendations for each instance individually","It does not provide recommendations for instances in Auto Scaling groups","It only provides recommendations for the Auto Scaling group's scaling policies","Compute Optimizer considers the performance of the entire Auto Scaling group when providing recommendations, aiming to optimise the group as a whole."
"What is the impact of implementing an AWS Compute Optimizer recommendation to reduce EC2 instance size?","Potential cost savings and reduced resource consumption","Increased application performance","Improved security posture","Simplified deployment process","Reducing instance size generally leads to cost savings and reduced resource consumption."
"How can you provide feedback on AWS Compute Optimizer recommendations?","By using the feedback mechanism within the Compute Optimizer console","By contacting AWS Support","By posting on the AWS Forums","By filing a feature request","Compute Optimizer provides a feedback mechanism within the console to allow users to rate the accuracy and usefulness of recommendations."
"What is the primary benefit of using AWS Compute Optimizer with AWS Lambda functions?","Cost optimisation through memory allocation right-sizing","Enhanced security posture through permission boundary recommendations","Improved deployment efficiency through packaged code analysis","Reduced latency via global accelerator integration","Compute Optimizer's primary benefit is cost optimisation for Lambda functions by right-sizing memory allocation."
"What is the scope of recommendations provided by AWS Compute Optimizer for EBS volumes?","Volume type, size, and IOPS","Volume encryption, snapshot schedules, and placement group","Volume tagging, lifecycle policies, and access control","Volume performance monitoring, throttling alerts, and usage patterns","Compute Optimizer primarily focuses on volume type, size and IOPS when providing recommendations for EBS volumes."
"How does AWS Compute Optimizer support multi-account environments?","By integrating with AWS Organizations","By creating separate Compute Optimizer configurations for each account","By using AWS CloudFormation templates to deploy Compute Optimizer across accounts","By enabling cross-account access through IAM roles","AWS Compute Optimizer integrates with AWS Organizations to support multi-account environments and provide consolidated recommendations."
"If AWS Compute Optimizer recommends an instance type with fewer vCPUs, what potential impact should you consider?","Potential CPU contention and performance degradation","Potential memory exhaustion and application crashes","Potential network bottlenecks and reduced throughput","Potential disk I/O limitations and slower response times","CPU contention and performance degradation are possible impacts if an instance type with fewer vCPUs is selected, especially for CPU-intensive workloads."
"Which type of recommendation does AWS Compute Optimizer provide for Amazon EC2 Auto Scaling groups?","Instance type recommendations","EBS volume type recommendations","Scaling policy recommendations","Networking recommendations","AWS Compute Optimizer primarily provides instance type recommendations to optimise the Amazon EC2 Auto Scaling groups."
"How does AWS Compute Optimizer use Amazon CloudWatch metrics to generate recommendations?","It analyzes historical resource utilisation data from CloudWatch","It configures CloudWatch alarms based on resource utilisation","It streams real-time metrics to CloudWatch for monitoring purposes","It uses CloudWatch Logs to collect application log data","Compute Optimizer analyzes the historical resource utilisation data from CloudWatch to generate recommendations."
"What is the main benefit of enabling enhanced infrastructure metrics in AWS Compute Optimizer?","Increased accuracy of recommendations","Reduced latency in recommendation generation","Enhanced visibility into resource utilization patterns","Improved security posture of AWS resources","Enhanced infrastructure metrics in AWS Compute Optimizer increase the accuracy of recommendations."
"Which of the following best describes the term 'right-sizing' in the context of AWS Compute Optimizer?","Matching resource capacity to workload requirements","Maximizing resource utilization for cost optimization","Minimizing resource allocation to reduce operational overhead","Balancing resource performance with security requirements","Right-sizing matches resource capacity to workload requirements, ensuring optimal performance and cost efficiency."
"Which AWS service can be used to automate the implementation of AWS Compute Optimizer recommendations?","AWS Systems Manager Automation","AWS CloudFormation","AWS Config","AWS CloudWatch Events","AWS Systems Manager Automation can be used to automate the implementation of AWS Compute Optimizer recommendations."
"What type of information can be found in the AWS Compute Optimizer dashboard?","Summary of resource optimization opportunities","Real-time performance metrics of AWS resources","Security vulnerabilities in AWS infrastructure","Compliance status of AWS resources","The AWS Compute Optimizer dashboard provides a summary of resource optimization opportunities."
"Which of the following is a key consideration when evaluating AWS Compute Optimizer recommendations for AWS Lambda functions?","Memory utilization patterns","Network latency metrics","Execution timeout thresholds","Function code size","When evaluating recommendations for Lambda functions, memory utilization patterns are a key consideration."
"What is the primary goal of using AWS Compute Optimizer for Amazon EBS volumes?","Optimizing storage costs and performance","Improving data durability and availability","Enhancing data security and compliance","Simplifying storage management and administration","The primary goal of using AWS Compute Optimizer for Amazon EBS volumes is optimizing storage costs and performance."
"How does AWS Compute Optimizer help improve the performance of AWS resources?","By recommending resource configurations that match workload requirements","By automatically scaling resources up or down based on demand","By identifying and resolving performance bottlenecks in real-time","By optimizing network traffic and reducing latency","AWS Compute Optimizer helps improve the performance of AWS resources by recommending configurations that match workload requirements."
"What type of data does AWS Compute Optimizer use to create performance improvement recommendations?","CPU utilization and memory usage","Network traffic and disk I/O","Application logs and error messages","Security audit logs and compliance reports","AWS Compute Optimizer uses CPU utilization and memory usage data to create performance improvement recommendations."
"What is the maximum look-back period of historical utilisation data used by Compute Optimizer to produce recommendations?","14 months","3 months","1 month","7 days","Compute Optimizer analyses the configuration and utilisation metrics of your AWS resources and recommends optimal AWS resources to reduce costs and improve performance using a 14 month look-back period."
"Can Compute Optimizer provide recommendations for resources in different AWS Regions?","Yes, but it must be configured for each region separately","No, it only provides recommendations for resources in the current region","Yes, it automatically aggregates data from all regions","Only if the regions are linked through a VPC peering connection","Compute Optimizer must be configured for each region separately in order to provide recommendations for AWS resources within that region."
"When does Compute Optimizer need to be enabled in an account?","Compute Optimizer must be enabled per account and region to generate the recommendations","Enabled only when performance issues arise","It is automatically enabled when the account is created","It does not need to be enabled if the resources are monitored by CloudWatch","Compute Optimizer must be enabled in each individual account and AWS region in order to generate the resource recommendations."
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS compute services","To manage user access to compute resources","To monitor network traffic within a VPC","To automate the deployment of EC2 instances","AWS Compute Optimizer analyses the configuration and utilisation metrics of your AWS compute resources to provide recommendations for optimal resource allocation and cost reduction."
"Which AWS services does Compute Optimizer provide recommendations for?","EC2 instances, EBS volumes, and Lambda functions","S3 buckets, DynamoDB tables, and CloudFront distributions","IAM roles, VPCs, and Security Groups","CloudWatch alarms, SNS topics, and SQS queues","Compute Optimizer focuses on compute resources, specifically EC2 instances, EBS volumes attached to those instances, and Lambda functions."
"What type of data does Compute Optimizer analyse to generate its recommendations?","Resource utilisation metrics from CloudWatch","Network logs from VPC Flow Logs","Security logs from CloudTrail","Cost data from Cost Explorer","Compute Optimizer relies on resource utilisation metrics, such as CPU utilisation, memory utilisation, and I/O operations, which are sourced from CloudWatch."
"What is a key benefit of using AWS Compute Optimizer?","Reducing compute costs by identifying over-provisioned resources","Improving network latency between EC2 instances","Enhancing the security posture of your AWS environment","Simplifying the deployment of containerised applications","Compute Optimizer helps identify resources that are over-provisioned, allowing you to downsize them and reduce your overall compute costs."
"What does the 'Finding Summary' section in Compute Optimizer provide?","An overview of the potential cost savings and performance improvements","A detailed list of security vulnerabilities in your EC2 instances","A summary of network configuration issues","A comprehensive inventory of all your AWS resources","The 'Finding Summary' gives you a high-level overview of the potential impact of implementing Compute Optimizer's recommendations, including cost savings and performance enhancements."
"How often does AWS Compute Optimizer refresh its recommendations?","Periodically, based on historical data","In real-time, as resources are utilised","On a daily basis","Only when manually triggered","Compute Optimizer generates recommendations periodically, using the historical resource utilisation data to provide relevant suggestions."
"Which AWS account permissions are required to enable Compute Optimizer?","Permissions to read CloudWatch metrics and modify EC2 instance configurations","Permissions to create IAM roles and policies","Permissions to manage billing and cost data","Permissions to access S3 buckets and DynamoDB tables","Compute Optimizer needs permissions to access CloudWatch metrics and to make changes to the configuration of EC2 instances or Lambda functions in order to implement recommendations."
"What is the purpose of the 'Enhanced Infrastructure Metrics' option in Compute Optimizer?","To provide more granular and accurate resource utilisation data","To encrypt all data transmitted between EC2 instances","To automatically back up your EC2 instances","To integrate with third-party monitoring tools","Enabling 'Enhanced Infrastructure Metrics' allows Compute Optimizer to access more detailed resource utilisation data, leading to more precise and effective recommendations."
"If Compute Optimizer recommends a different EC2 instance type, what should you consider before implementing the change?","Testing the new instance type with your workload to ensure compatibility and performance","Upgrading your operating system to the latest version","Changing your VPC configuration","Deleting any unused EBS volumes","It's crucial to test the recommended instance type to ensure that it can handle your workload's demands and is compatible with your applications before making the change in production."
"What is the 'Savings Opportunity' metric in AWS Compute Optimizer?","The estimated cost reduction that can be achieved by implementing the recommendations","The total cost of all your AWS compute resources","The percentage of underutilised resources","The historical cost of running your workloads","The 'Savings Opportunity' metric represents the potential cost savings that you could realise by adopting the Compute Optimizer's recommendations."
"How does AWS Compute Optimizer handle EC2 instances with consistent high utilisation?","It recommends scaling up the instance type to provide more resources","It recommends stopping the instance to reduce costs","It recommends migrating the instance to a different AWS region","It ignores the instance as it's already optimally utilised","If an instance is consistently highly utilised, Compute Optimizer may recommend scaling up to a larger instance type to avoid performance bottlenecks."
"What is the relationship between AWS Trusted Advisor and AWS Compute Optimizer?","Compute Optimizer provides resource optimisation recommendations based on Trusted Advisor findings","Trusted Advisor provides general cost optimisation advice, while Compute Optimizer focuses on compute resources","Compute Optimizer replaces Trusted Advisor's cost optimisation checks","Trusted Advisor is a prerequisite for using Compute Optimizer","Trusted Advisor provides general cost optimisation recommendations, while Compute Optimizer focuses specifically on compute resources and provides more detailed recommendations."
"Can AWS Compute Optimizer provide recommendations for Auto Scaling groups?","Yes, by analysing the EC2 instances within the Auto Scaling group","No, it only supports individual EC2 instances","Only if the Auto Scaling group uses a fixed instance type","Only if the Auto Scaling group has scaling policies configured","Compute Optimizer analyses the EC2 instances within an Auto Scaling group and can provide recommendations based on their collective resource utilisation."
"How does AWS Compute Optimizer help with right-sizing AWS Lambda functions?","By analysing the function's memory configuration and invocation patterns","By optimising the function's code for performance","By automatically deploying the function to different AWS regions","By suggesting alternative programming languages","Compute Optimizer can analyse Lambda functions and recommend adjusting their memory allocation based on their actual usage, which can help reduce costs."
"What is a 'Performance Risk' in AWS Compute Optimizer?","The potential for performance degradation if a recommendation is implemented","A security vulnerability that could impact your EC2 instances","A network configuration issue that could lead to connectivity problems","The risk of losing data if an EC2 instance fails","'Performance Risk' indicates the potential for your workload to experience performance issues if you implement a recommendation, so it's important to carefully evaluate these before making changes."
"You disable Compute Optimizer. What happens to your recommendation history?","It is retained for a limited time according to AWS data retention policies","It is immediately deleted","It is archived indefinitely","It is transferred to AWS Trusted Advisor","Your recommendation history is kept for a limited time, in accordance to AWS data retention policies."
"What does Compute Optimizer consider when providing recommendations for EBS volumes?","Volume type, IOPS, and throughput","Volume size, encryption status, and backup frequency","Volume snapshot schedule, attached instance type, and AWS region","Volume tags, access permissions, and lifecycle policies","Compute Optimizer considers EBS volume type, IOPS, and throughput to give recommendations that match the workload demands."
"What is the impact of under-provisioning an EC2 instance, according to Compute Optimizer?","Increased Performance Risk","Increased Savings Opportunity","Reduced Cost","Reduced Carbon Footprint","Under-provisioning an instance increases the Performance Risk."
"What is the impact of over-provisioning an EC2 instance, according to Compute Optimizer?","Increased Savings Opportunity","Increased Performance Risk","Reduced Downtime","Reduced Network Latency","Over-provisioning an instance increases the Savings Opportunity."
"Does Compute Optimizer provide recommendations for ECS clusters?","No, Compute Optimizer does not provide recommendations for ECS clusters directly","Yes, by analysing underlying instances","Only if Fargate is enabled","Only if EC2 launch type is enabled","Compute Optimizer does not provide recommendations for ECS clusters directly, however, it does analyse underlying EC2 instances."
"Which of the following is NOT a factor considered when sizing a Lambda Function with Compute Optimizer?","Disk I/O","Memory","CPU","Network usage","Compute Optimizer does not consider Disk I/O when recommending memory allocation for Lambda."
"Which of the following AWS services integrates directly with AWS Compute Optimizer to implement recommendations?","AWS Auto Scaling","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS Auto Scaling can use Compute Optimizer recommendations to dynamically adjust the size of EC2 instances."
"Which of the following is a performance indicator that would cause Compute Optimizer to recommend instance type upgrade?","CPU throttling","Low network bandwidth","Low storage capacity","High memory consumption","CPU throttling is a performance indicator that would cause Compute Optimizer to recommend an instance type upgrade."
"Which of the following metrics is NOT directly considered by Compute Optimizer for EC2 instance recommendations?","Network Packet Loss","CPU Utilization","Memory Utilization","Disk I/O","Compute Optimizer looks at resource utilisation metrics, and Packet Loss is not one of them."
"What type of EBS volume performance issue can AWS Compute Optimizer help identify and resolve?","Under-utilised provisioned IOPS","Malware infection","Fragmentation","Incorrect encryption settings","Compute Optimizer can assist with identifiying under-utilised provisioned IOPS and help reduce costs."
"In AWS Compute Optimizer, what does a recommendation to change an EBS volume type from gp2 to gp3 indicate?","Potential cost savings due to lower gp3 pricing and similar performance","A need for higher IOPS performance than gp2 can provide","Improved data durability and availability","Enhanced security features","Changing from gp2 to gp3 indicates cost savings."
"What is the purpose of the 'Current Instance Type' column in AWS Compute Optimizer's EC2 instance recommendations?","To display the instance type currently running the workload","To show the recommended instance type based on Compute Optimizer's analysis","To indicate the optimal instance type for cost savings","To list all available instance types in the AWS region","The 'Current Instance Type' column indicates the current instance."
"What is the purpose of enabling Enhanced Infrastructure Metrics on Compute Optimizer?","To provide deeper insights into EBS Volume throughput and latency","To enable Compute Optimizer recommendations for AWS Lambda functions","To improve the security of your AWS infrastructure","To enable real-time monitoring of resource utilisation","The purpose of Enhanced Infrastructure Metrics is to provide deeper insights into EBS volume throughput and latency."
"What is one way AWS Compute Optimizer helps to improve sustainability within an AWS environment?","By identifying and reducing over-provisioned resources, leading to lower energy consumption","By optimising network traffic to reduce data transfer costs","By automating the deletion of unused EBS snapshots","By recommending the use of renewable energy sources for EC2 instances","Reducing over-provisioned resources also leads to lower energy consumption, contributing to sustainability."
"Which AWS service can be used to automatically implement recommendations generated by AWS Compute Optimizer?","AWS Auto Scaling","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS Auto Scaling can implement Compute Optimizer recommendations."
"A company is using a large number of gp2 EBS volumes. What potential benefit could they gain by running AWS Compute Optimizer?","Recommendations to migrate to gp3 volumes for cost savings and potentially better performance","Automatic encryption of all EBS volumes","Improved data replication across multiple Availability Zones","Real-time monitoring of EBS volume health","Compute Optimizer can recommend migrating to gp3."
"What data source is NOT used by AWS Compute Optimizer when generating EC2 instance recommendations?","AWS CloudTrail logs","Amazon CloudWatch metrics","EC2 instance configuration","EBS volume metrics","Compute Optimizer does not use CloudTrail logs."
"Which of the following factors can prevent AWS Compute Optimizer from providing recommendations for an EC2 instance?","Insufficient historical data","The instance is already running the optimal instance type","The instance is running on a reserved instance","The instance is part of a large Auto Scaling group","Insufficient historical data prevents Compute Optimizer from providing accurate recommendations."
"Which of the following is a key benefit of using AWS Compute Optimizer for AWS Lambda functions?","Identifying opportunities to reduce Lambda function memory allocation and costs","Optimising Lambda function code for faster execution times","Automating the deployment of Lambda functions across multiple regions","Enhancing the security of Lambda function execution environments","Compute Optimizer helps reduce memory allocation and costs."
"You want to ensure that AWS Compute Optimizer recommendations are automatically applied to your EC2 instances. What is the BEST way to achieve this?","Configure AWS Auto Scaling to use Compute Optimizer recommendations","Use AWS Config to automatically remediate non-compliant EC2 instances","Manually implement recommendations on a regular basis","Create a custom script to monitor Compute Optimizer and apply changes","Configure AWS Auto Scaling to use Compute Optimizer recommendations."
"When reviewing AWS Compute Optimizer recommendations for EBS volumes, what does 'IOPS' stand for?","Input/Output Operations Per Second","Internet Operations Protocol Standard","Integrated Output Processing System","Instance Optimisation Performance Score","IOPS stands for Input/Output Operations Per Second."
"AWS Compute Optimizer provides recommendations for EC2 instances based on observed resource utilisation. What happens if an instance's workload suddenly changes significantly after the recommendations are made?","The user should manually trigger Compute Optimizer to re-analyze the instance","Compute Optimizer automatically adjusts its recommendations in real-time","The initial recommendations remain valid until Compute Optimizer runs again on its scheduled refresh cycle","Compute Optimizer sends an alert notifying the user of the workload change","The initial recommendations remain valid until Compute Optimizer runs again on its scheduled refresh cycle, but the user can trigger a new analysis."
"What is the 'look-back period' when it comes to Compute Optimizer analysis?","It refers to the number of days that Compute Optimizer analyses","It refers to the time Compute Optimizer takes to create recommendations","It refers to the length of time it takes to implement the recommendations","It refers to the cost saving period of the new configuration","Look-back period is the number of days that Compute Optimizer uses in its analysis."
"Which statement about AWS Compute Optimizer is FALSE?","It provides recommendations for both EC2 and Auto Scaling Groups","It requires manual configuration of CloudWatch metrics","It requires permissions to access CloudWatch metrics","It can reduce costs for Lambda Functions","It requires access to CloudWatch but not configuration of CloudWatch metrics, which happens out of the box."
"Which of these AWS services would Compute Optimizer recommendations NOT help you to right-size?","RDS Instance","EC2 Instance","Lambda Function","EBS Volume","Compute Optimizer does not offer recommendations for RDS Instances."
"What feature allows you to view consolidated Compute Optimizer recommendations across multiple AWS accounts in your organisation?","AWS Organizations integration","AWS Trusted Advisor integration","AWS Cost Explorer integration","AWS Systems Manager integration","AWS Organizations integration allows consolidated view."
"Which of these is NOT a Performance Risk that Compute Optimizer is designed to help you detect?","CPU Bottleneck","Network Congestion","Memory Pressure","Disk I/O starvation","Network Congestion is not a performance risk that Compute Optimizer is designed to detect."
"If an EC2 instance is consistently utilising 95% of its CPU, what recommendation is Compute Optimizer most likely to make?","Upgrade to a larger instance type","Downgrade to a smaller instance type","Enable Elastic Load Balancing","Purchase Reserved Instances","Upgrade to a larger instance type."
"You have a development environment with EC2 instances that are only used during business hours. How can Compute Optimizer help optimise costs in this scenario?","By recommending smaller instance types that are sufficient for the limited usage","By suggesting the use of Spot Instances","By identifying underutilised instances that can be stopped when not in use","By enabling enhanced networking on the instances","By identifying underutilised instances."
"Which AWS service does AWS Compute Optimizer primarily rely on for collecting resource utilisation data?","CloudWatch","CloudTrail","Config","Trusted Advisor","Compute Optimizer uses CloudWatch metrics to analyse the utilisation of your AWS resources."
"What is the main goal of AWS Compute Optimizer?","To provide right-sizing recommendations for AWS resources","To manage AWS IAM roles and policies","To monitor network traffic","To automate infrastructure deployments","Compute Optimizer's main goal is to analyse and provide recommendations for optimizing the size of your AWS resources to improve performance and reduce costs."
"Which resource types are supported by AWS Compute Optimizer for generating recommendations?","EC2 instances, EBS volumes, Lambda functions","S3 buckets, DynamoDB tables, CloudFront distributions","VPC networks, Route 53 hosted zones, API Gateway endpoints","SQS queues, SNS topics, Step Functions state machines","Compute Optimizer supports EC2 instances, EBS volumes, and Lambda functions for resource optimisation recommendations."
"What does AWS Compute Optimizer use to analyse Lambda function performance?","Duration, Memory Utilisation, and Errors","Number of Invocations, Data Transfer, and Cost","Code Size, Timeout, and Concurrency","Function Name, Runtime, and Role","Compute Optimizer analyses Lambda function performance based on duration, memory utilisation, and errors to suggest optimal memory configurations."
"What is the primary benefit of implementing AWS Compute Optimizer recommendations for EC2 instances?","Cost savings and improved performance","Enhanced security and compliance","Simplified resource management","Automated software updates","Implementing Compute Optimizer recommendations can lead to significant cost savings by reducing over-provisioning and improving performance by right-sizing instances."
"If AWS Compute Optimizer recommends a smaller EC2 instance type, what is it trying to achieve?","Reduce costs by eliminating over-provisioning","Increase network bandwidth","Improve storage capacity","Enhance security posture","Compute Optimizer aims to reduce costs by identifying instances that are over-provisioned and recommending smaller, more cost-effective instance types."
"What does AWS Compute Optimizer consider when recommending EBS volume size adjustments?","IOPS and Throughput","Storage Class and Encryption","Volume Type and Tags","Snapshot Policy and Region","Compute Optimizer considers IOPS and throughput when making recommendations for EBS volume size adjustments, aiming to optimize performance and cost."
"What is the meaning of the 'Savings Opportunity' metric in AWS Compute Optimizer?","Estimated cost reduction if recommendations are implemented","Potential performance improvement if recommendations are implemented","Predicted increase in resource utilisation","Total spend on compute resources","'Savings Opportunity' represents the estimated cost reduction that can be achieved by implementing Compute Optimizer's resource right-sizing recommendations."
"How often does AWS Compute Optimizer typically refresh its recommendations?","Every 24 hours","Every 5 minutes","Every week","Every month","AWS Compute Optimizer typically refreshes its recommendations on a daily basis to reflect changes in resource utilisation patterns."
"Can AWS Compute Optimizer provide recommendations for EC2 Auto Scaling groups?","Yes, based on the performance of the instances within the group","No, it only supports standalone EC2 instances","Yes, but only for spot instances","No, it requires manual configuration of scaling policies","Compute Optimizer can provide recommendations for EC2 Auto Scaling groups based on the performance of the instances within the group, helping to optimize scaling configurations."
"In AWS Compute Optimizer, what does the 'Performance Risk' indicate?","The potential risk of performance degradation if the recommendations are implemented","The security risk associated with the current resource configuration","The risk of data loss due to incorrect storage settings","The compliance risk of the current infrastructure","'Performance Risk' indicates the potential risk of performance degradation if the recommendations are implemented, helping users make informed decisions about resource sizing."
"Which AWS Region is AWS Compute Optimizer not available in?","All AWS Regions are supported","AWS GovCloud (US)","China (Beijing)","China (Ningxia)","Compute Optimizer is available in most AWS Regions, including both commercial and government regions, but its availability in China Regions may vary."
"Which AWS service can be integrated with AWS Compute Optimizer to automate the implementation of recommendations?","AWS Auto Scaling","AWS Systems Manager","AWS CloudFormation","AWS Config","AWS Auto Scaling can be integrated with Compute Optimizer to automate the implementation of scaling policy recommendations based on Compute Optimizer's analysis."
"What is the default look-back period for AWS Compute Optimizer to analyse resource utilisation?","14 days","7 days","30 days","90 days","The default look-back period for Compute Optimizer is 14 days, providing a historical view of resource utilisation patterns."
"Can AWS Compute Optimizer generate recommendations for resources in different AWS accounts?","Yes, if the accounts are linked through AWS Organizations","No, it only supports resources within a single account","Yes, but only for certain resource types","No, it requires manual data aggregation","Compute Optimizer supports generating recommendations for resources in different AWS accounts if the accounts are linked through AWS Organizations."
"What is the significance of the 'Finding Reason Codes' in AWS Compute Optimizer?","They explain why a specific recommendation was generated","They indicate the error codes associated with resource deployments","They list the permissions required to implement recommendations","They describe the security vulnerabilities detected by the service","'Finding Reason Codes' provide explanations for why a specific recommendation was generated, offering insights into the analysis process."
"If AWS Compute Optimizer recommends increasing the memory of a Lambda function, what is the likely reason?","The function is experiencing memory-related errors or timeouts","The function is not being invoked frequently enough","The function is exceeding its execution time limit","The function is using too much network bandwidth","Compute Optimizer recommends increasing Lambda function memory if the function is experiencing memory-related errors or timeouts, indicating it needs more memory to execute efficiently."
"What is the AWS Compute Optimizer dashboard used for?","To visualise resource utilisation and recommendations","To configure IAM roles and policies","To manage billing and cost allocation","To monitor network traffic and security events","The Compute Optimizer dashboard provides a centralised view for visualising resource utilisation data and the recommendations generated by the service."
"How does AWS Compute Optimizer handle resources with intermittent or spiky workloads?","It uses a longer look-back period to capture the average utilisation","It provides separate recommendations for peak and off-peak periods","It ignores resources with highly variable utilisation","It only recommends instances with burstable performance","Compute Optimizer uses a longer look-back period to capture the average utilisation of resources with intermittent or spiky workloads, ensuring recommendations are based on representative data."
"What is the role of AWS Organizations in AWS Compute Optimizer?","To enable cross-account recommendations","To manage user access control","To configure billing and cost allocation","To monitor network traffic","AWS Organizations enables Compute Optimizer to generate recommendations for resources across multiple accounts within the organisation."
"Which of the following EC2 instance purchasing options can AWS Compute Optimizer recommend?","On-Demand, Reserved, and Savings Plans","Spot Instances only","Reserved Instances only","Dedicated Hosts only","Compute Optimizer can recommend various purchasing options, including On-Demand, Reserved Instances, and Savings Plans, to help optimise EC2 instance costs."
"How can you export AWS Compute Optimizer recommendations for further analysis?","Using the AWS CLI or API to retrieve data","Downloading a CSV file from the console","Integrating with AWS Cost Explorer","Using AWS CloudTrail","Compute Optimizer recommendations can be exported using the AWS CLI or API to retrieve data, enabling further analysis and integration with other tools."
"What does AWS Compute Optimizer consider when making recommendations for EC2 instance families?","Workload characteristics, price performance and available features","Operating system, installed software and compliance standards","Network configuration, security groups and IAM roles","Storage type, backup frequency and disaster recovery plan","Compute Optimizer considers workload characteristics, price performance, and available features when recommending different EC2 instance families."
"If AWS Compute Optimizer indicates that an EBS volume is 'under-provisioned', what does this mean?","The volume's IOPS or throughput is insufficient for the workload","The volume has insufficient storage capacity","The volume is using an outdated storage class","The volume is not encrypted","An 'under-provisioned' EBS volume indicates that its IOPS or throughput is insufficient to meet the demands of the workload, leading to performance bottlenecks."
"What is the purpose of the AWS Compute Optimizer API?","To programmatically access and manage recommendations","To configure IAM roles and policies","To monitor network traffic","To automate infrastructure deployments","The Compute Optimizer API allows users to programmatically access and manage recommendations, enabling automation and integration with other systems."
"Which of the following is NOT a metric considered by AWS Compute Optimizer for EC2 instance recommendations?","Disk Queue Length","CPU Utilization","Network In/Out","Memory Utilization","Disk Queue Length is not a metric that AWS Compute Optimizer considers when making recommendations for EC2 instance right sizing."
"How can you provide feedback to AWS Compute Optimizer about its recommendations?","Using the 'thumbs up' or 'thumbs down' feedback option","By submitting a support ticket","By posting on the AWS forums","By contacting your AWS account manager","Compute Optimizer allows users to provide feedback about its recommendations using the 'thumbs up' or 'thumbs down' option, helping to improve the accuracy of the service."
"If you have a very consistent workload, what is AWS Compute Optimizer likely to recommend?","A Reserved Instance or Savings Plan","A Spot Instance","A larger On-Demand Instance","An Instance with a very high clock speed","For a consistent workload, Compute Optimizer is likely to recommend a Reserved Instance or Savings Plan to optimise costs over the long term."
"What level of access do you need to grant AWS Compute Optimizer to start receiving recommendations?","Read-only access to CloudWatch metrics and resource configurations","Full administrative access to your AWS account","Write access to modify EC2 instances and EBS volumes","No access is required, as it automatically discovers resources","Compute Optimizer requires read-only access to CloudWatch metrics and resource configurations to analyse resource utilisation and generate recommendations."
"You have implemented an AWS Compute Optimizer recommendation but are not seeing the expected cost savings. What should you investigate first?","Check if the workload has changed since the recommendation","Check if the recommendation was applied correctly","Check if there are any unexpected charges on your AWS bill","Check the health of the EC2 instance","First, check if the workload has changed since the recommendation was made. Workload changes can affect utilisation and alter the expected cost savings."
"What is the main benefit of integrating AWS Compute Optimizer with Savings Plans?","To optimize Savings Plans commitment based on Compute Optimizer recommendations","To purchase Savings Plans directly from Compute Optimizer","To track Savings Plans utilization","To generate Savings Plans cost reports","Integrating with Savings Plans allows Compute Optimizer to provide more informed recommendations based on existing Savings Plans commitments and helps optimize future Savings Plans purchases."
"When evaluating AWS Compute Optimizer recommendations, what is an important factor to consider in addition to cost savings?","The performance impact on the application","The security implications of the proposed changes","The compliance requirements of the workload","The level of support available for the recommended instance type","When evaluating Compute Optimizer recommendations, it's crucial to consider the performance impact on the application to ensure the changes don't negatively affect the user experience or overall system functionality."
"Which of the following actions is NOT directly supported by AWS Compute Optimizer?","Migrating a workload to a different AWS Region","Right-sizing EC2 instances","Optimizing EBS volumes","Optimizing Lambda function memory","Compute Optimizer focuses on right-sizing resources within the same AWS Region; it does not directly support migrating workloads to different AWS Regions."
"What type of recommendation would AWS Compute Optimizer provide if an EBS volume is consistently experiencing high latency?","Increase the IOPS of the EBS volume","Reduce the size of the EBS volume","Change the volume type to a cheaper option","Change the volume type to gp2","If an EBS volume is consistently experiencing high latency, Compute Optimizer would recommend increasing the IOPS to improve performance."
"What happens if AWS Compute Optimizer doesn't have enough data to provide a recommendation?","It displays a 'Insufficient Data' message","It provides a default recommendation","It provides a recommendation based on similar resources","It provides a recommendation based on the maximum possible configuration","If Compute Optimizer lacks sufficient data, it displays an 'Insufficient Data' message, indicating that it cannot provide an accurate recommendation at this time."
"Which tool can be used to monitor the performance impact of AWS Compute Optimizer recommendations?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch is the primary tool for monitoring the performance impact of Compute Optimizer recommendations, allowing you to track key metrics and ensure that changes are having the desired effect."
"If AWS Compute Optimizer recommends switching an EC2 instance to a burstable performance instance (e.g., t3.micro), what is a key consideration?","Whether the workload can tolerate performance variations during burst periods","Whether the instance supports the required operating system","Whether the instance has enough network bandwidth","Whether the instance has enough local storage","When switching to a burstable performance instance, it's essential to consider whether the workload can tolerate performance variations during burst periods. If the workload consistently requires high CPU, a burstable instance may not be suitable."
"What AWS Compute Optimizer feature helps you understand the historical cost savings from previous recommendations?","Recommendation history","Cost explorer integration","Savings opportunity dashboard","Usage reports","AWS Compute Optimizer doesn't have any feature that specifically displays historic cost savings due to previous recommendations, although cost explorer provides similar cost tracking."
"You have an AWS Lambda function that occasionally runs out of memory. What would AWS Compute Optimizer likely recommend?","Increasing the memory allocation for the function","Reducing the function's timeout","Increasing the function's concurrency","Reducing the size of the deployment package","If a Lambda function occasionally runs out of memory, Compute Optimizer would likely recommend increasing the memory allocation to prevent memory-related errors and timeouts."
"What information does AWS Compute Optimizer use from Amazon CloudWatch to determine Lambda function right sizing?","Lambda function duration, memory usage and errors","Lambda function invocation count, concurrent executions and dead-letter queue errors","Lambda function log data, metrics and X-Ray traces","Lambda function code size, layers and dependencies","Compute Optimizer uses Lambda function duration, memory usage and errors from CloudWatch to determine optimal memory allocation."
"Can AWS Compute Optimizer make recommendations for EC2 instances running within an AWS ECS cluster?","Yes, by analysing the performance of the underlying EC2 instances","No, it only supports standalone EC2 instances","Yes, but only for EC2 instances in Fargate","No, it requires manual configuration of ECS scaling policies","Compute Optimizer can make recommendations for EC2 instances running within an ECS cluster by analysing the performance of the underlying EC2 instances. However, it does not provide recommendations for Fargate clusters."
"What happens to AWS Compute Optimizer recommendations when an EC2 instance is terminated?","The recommendations are automatically removed","The recommendations remain until manually deleted","The recommendations are archived for historical purposes","The recommendations are flagged as 'inactive'","When an EC2 instance is terminated, the recommendations are automatically removed from Compute Optimizer, as they are no longer relevant."
"You've implemented an AWS Compute Optimizer recommendation for an EC2 instance, and you're still seeing high CPU utilisation. What should you do?","Investigate the specific processes consuming CPU","Revert the recommendation and use a larger instance","Ignore the recommendation and rely on auto-scaling","Contact AWS Support","If you're still seeing high CPU utilisation after implementing a recommendation, investigate the specific processes consuming CPU to identify the root cause. It may be that the recommendation was correct, but the application itself needs optimisation."
"What is one way to exclude specific AWS resources from AWS Compute Optimizer analysis?","Using resource tags","Using IAM policies","Using CloudWatch alarms","Using AWS Config rules","Using resource tags is one way to exclude specific AWS resources from Compute Optimizer analysis. Compute Optimizer will not generate recommendations for resources with these tags."
"What is a potential drawback of aggressively implementing all AWS Compute Optimizer recommendations?","Potential performance degradation if the workload changes unexpectedly","Increased management overhead from frequently changing resource sizes","Inaccurate cost savings due to fluctuating resource prices","Increased security vulnerabilities","A potential drawback of aggressively implementing all Compute Optimizer recommendations is the risk of performance degradation if the workload changes unexpectedly. It's important to monitor performance after implementing recommendations to ensure they remain appropriate."
"What AWS service is the primary source of information for AWS Compute Optimizer to derive utilization metrics?","Amazon CloudWatch","AWS CloudTrail","Amazon Inspector","AWS X-Ray","Amazon CloudWatch is the primary source of information for AWS Compute Optimizer to derive utilization metrics."
"Which AWS service can AWS Compute Optimizer integrate with to help automate applying EC2 instance sizing recommendations?","AWS Auto Scaling","AWS Systems Manager","AWS Config","AWS CloudFormation","AWS Auto Scaling can integrate with AWS Compute Optimizer to automate applying EC2 instance sizing recommendations."
"What is the default setting for AWS Compute Optimizer's resource type coverage?","It automatically discovers and analyses supported resource types","It requires manual configuration of resource types to analyse","It only analyses resources tagged with specific tags","It only analyses resources in specific AWS Regions","By default, AWS Compute Optimizer automatically discovers and analyses all supported resource types in your AWS account."
"Which of the following factors contributes to the recommendation confidence level provided by AWS Compute Optimizer?","Amount of historical data available for analysis","User feedback on previous recommendations","Compliance status of the resource","Number of security vulnerabilities detected on the resource","The amount of historical data available for analysis is a key factor contributing to the recommendation confidence level provided by Compute Optimizer. More data typically leads to higher confidence."
"What is the primary function of AWS Compute Optimizer?","Recommending optimal AWS resource configurations","Monitoring network latency","Managing security groups","Automatically deploying applications","Compute Optimizer analyses resource utilisation to recommend optimal configurations (e.g., instance types) for improved performance and cost savings."
"Which AWS service does Compute Optimizer primarily use to gather resource utilisation data?","CloudWatch","CloudTrail","Config","Trusted Advisor","Compute Optimizer leverages CloudWatch metrics to analyse resource utilisation patterns."
"Which type of resource can AWS Compute Optimizer NOT provide recommendations for?","On-Premise virtual machines","EC2 instances","EBS volumes","Lambda functions","Compute Optimizer focuses on AWS resources, and on-premise virtual machines are not within its scope."
"What is the benefit of implementing Compute Optimizer's recommendations?","Improved performance and reduced costs","Enhanced security posture","Automated disaster recovery","Simplified application deployment","Implementing Compute Optimizer recommendations can lead to more efficient resource usage, resulting in both performance improvements and cost reductions."
"How frequently does AWS Compute Optimizer typically refresh its recommendations?","Daily","Hourly","Weekly","Monthly","Compute Optimizer typically refreshes its recommendations daily to reflect changes in resource utilisation."
"Which pricing model does Compute Optimizer use?","Free to use, generates recommendations at no cost","Pay-as-you-go, based on the number of resources analysed","Subscription-based, with a fixed monthly fee","Reserved Instance based, according to the plan.","Compute Optimizer is free to use. It generates recommendations at no cost to the user."
"What is the purpose of the 'Savings Opportunity' metric in AWS Compute Optimizer?","Estimates the potential cost savings from implementing recommendations","Measures the current CPU utilisation of a resource","Indicates the number of security vulnerabilities detected","Tracks the number of failed API calls","The 'Savings Opportunity' metric provides an estimate of the potential cost savings that can be achieved by implementing Compute Optimizer's recommendations."
"Which AWS Compute Optimizer feature allows you to view recommendations for multiple accounts within an organisation?","Cross-Account Analysis","Consolidated Billing Integration","Multi-Region Support","Global Accelerator Integration","Compute Optimizer allows you to analyse and view recommendations for multiple accounts within an organisation using cross-account analysis."
"What type of recommendation does AWS Compute Optimizer provide for EC2 instances?","Instance type and size","Storage volume type","Network bandwidth allocation","Security group configuration","Compute Optimizer recommends changes to the EC2 instance type and size to better match the workload's resource requirements."
"What is the main advantage of using Compute Optimizer's EBS volume recommendations?","Optimised storage performance and reduced storage costs","Automated data backup and recovery","Enhanced data encryption","Simplified volume management","Compute Optimizer EBS volume recommendations focus on optimising storage performance and reducing storage costs by suggesting appropriate volume types."
"What is the purpose of AWS Compute Optimizer's Lambda function recommendations?","Suggest optimal memory allocation and concurrency settings","Identify code vulnerabilities","Automate function deployment","Monitor function execution time","Compute Optimizer provides recommendations for Lambda functions to optimise memory allocation and concurrency settings for cost efficiency and performance."
"Which factor does AWS Compute Optimizer consider when recommending EC2 instance types?","CPU utilisation, memory utilisation, and network I/O","Instance launch time, storage capacity, and operating system","Security group rules, IAM role permissions, and network ACLs","Database connection limits, disk space, and firewall settings","Compute Optimizer uses metrics like CPU utilisation, memory utilisation, and network I/O to determine the optimal EC2 instance types for a given workload."
"If AWS Compute Optimizer recommends downsizing an EC2 instance, what does this typically indicate?","The instance is over-provisioned and underutilised","The instance is experiencing high network latency","The instance is running out of storage space","The instance is experiencing CPU throttling","Downsizing recommendations from Compute Optimizer usually indicate that the instance is over-provisioned and not fully utilising its resources."
"Which AWS service can be used to automatically implement Compute Optimizer's recommendations?","AWS Auto Scaling","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS Auto Scaling can be configured to automatically adjust resources based on Compute Optimizer recommendations, ensuring optimal resource allocation."
"What is the role of Compute Optimizer in a cost optimisation strategy?","Identifies and eliminates wasted resources","Encrypts data at rest","Automates security patching","Manages user access","Compute Optimizer plays a crucial role in cost optimisation by identifying and helping to eliminate wasted resources through right-sizing and configuration recommendations."
"What type of data does AWS Compute Optimizer analyse to provide recommendations for EC2 Auto Scaling groups?","CPU utilisation, memory consumption, and network I/O across the group's instances","Number of requests served, error rates, and latency metrics","Security group rules, IAM role permissions, and network ACLs","Database connection limits, disk space, and firewall settings","Compute Optimizer analyses metrics like CPU utilisation, memory consumption, and network I/O across the instances within an EC2 Auto Scaling group to provide optimal recommendations."
"Which of these is NOT a benefit of using AWS Compute Optimizer?","Automated security compliance reporting","Cost optimisation","Performance improvement","Workload right-sizing","AWS Compute Optimizer focuses on optimising resource configurations for cost and performance and is not involved in security compliance reporting."
"In AWS Compute Optimizer, what does the term 'resource utilisation' refer to?","The degree to which a resource is actively used and consuming resources","The amount of free storage space on a resource","The number of users accessing a resource","The geographic location of a resource","Resource utilisation refers to the level at which a resource, such as CPU, memory, or network, is being actively used."
"When Compute Optimizer recommends an instance type with fewer vCPUs, what might be a likely reason?","The workload is CPU-bound and does not benefit from additional cores","The workload is I/O-bound","The workload requires more memory","The instance has reached its maximum network bandwidth","When Compute Optimizer recommends fewer vCPUs, it suggests that the workload is not effectively utilising the available CPU cores, and a smaller instance type would be more cost-effective."
"You want to receive proactive notifications when Compute Optimizer generates new recommendations. Which AWS service can you integrate with?","Amazon EventBridge","AWS CloudTrail","AWS Config","AWS IAM","Amazon EventBridge can be integrated with Compute Optimizer to receive notifications about new recommendations, allowing for timely action."
"Which type of Amazon EC2 instance would Compute Optimizer most likely recommend for a memory-intensive application?","An R-family instance","A T-family instance","A C-family instance","A G-family instance","For memory-intensive applications, Compute Optimizer would typically recommend an R-family instance, as these instances are optimised for memory performance."
"A company is using AWS Compute Optimizer for cost optimisation. After implementing recommendations, how should they monitor the impact of the changes?","By tracking CloudWatch metrics and cost reports","By reviewing CloudTrail logs","By analysing VPC Flow Logs","By checking AWS Trusted Advisor","To monitor the impact of Compute Optimizer's recommendations, companies should track relevant CloudWatch metrics and cost reports to assess performance and cost savings."
"What action should you take if AWS Compute Optimizer recommends a different EBS volume type for your EC2 instance?","Evaluate the performance characteristics and cost implications of the recommended volume type","Immediately switch to the recommended volume type without further analysis","Ignore the recommendation as it is only a suggestion","Delete the existing volume and create a new one of the recommended type","It's important to evaluate the performance characteristics and cost implications of the recommended volume type before making a change to ensure it meets your application's needs."
"If AWS Compute Optimizer shows a low utilisation rate for a particular Lambda function, what does this suggest?","The function is over-provisioned and could use less memory","The function is under-provisioned and needs more memory","The function is experiencing execution errors","The function is not being invoked frequently enough","A low utilisation rate for a Lambda function typically suggests that it's over-provisioned and could use less memory, reducing costs."
"Which AWS Compute Optimizer setting allows you to exclude specific resources from analysis and recommendations?","Opt-Out Preference","Exclusion Filter","Resource Tagging","Blacklist Configuration","The 'Opt-Out Preference' setting in Compute Optimizer allows you to exclude specific resources from analysis and recommendations."
"What is the best approach to deploying AWS Compute Optimizer recommendations in a production environment?","Implement changes gradually and monitor performance closely","Immediately implement all recommendations without testing","Implement changes only during off-peak hours","Ignore the recommendations if the system works fine","The best approach is to implement changes gradually and monitor performance closely to ensure that the changes have the desired effect and do not negatively impact the application."
"You are analysing AWS Compute Optimizer's findings. What does it mean if an EC2 instance is labelled as 'under-provisioned'?","The instance is not sized appropriately for the workload and is likely experiencing performance bottlenecks","The instance is oversized and wasting resources","The instance is nearing its storage capacity limit","The instance is running an outdated operating system","An 'under-provisioned' label indicates that the instance is not sized appropriately for the workload and is likely experiencing performance bottlenecks due to insufficient resources."
"Which factor is NOT a key consideration when deciding whether to implement an AWS Compute Optimizer recommendation?","The colour of the recommended instance type","The potential impact on application performance","The estimated cost savings","The level of business criticality of the application","The colour of an instance type has no bearing on whether to implement a Compute Optimizer recommendation, so this is not a key consideration."
"What is the first step you should take before implementing an AWS Compute Optimizer recommendation for an EC2 instance?","Backup the existing instance or create a snapshot","Update the operating system on the instance","Reboot the instance","Disable CloudWatch monitoring","Before implementing any significant change to an EC2 instance, it's crucial to backup the existing instance or create a snapshot to ensure you can revert to the previous state if necessary."
"Which scenario would benefit most from using AWS Compute Optimizer's EBS volume recommendations?","A database server with high I/O requirements","A web server with mostly static content","A development environment with minimal storage needs","A disaster recovery environment with infrequent usage","A database server with high I/O requirements would benefit most from Compute Optimizer's EBS volume recommendations, as it can help optimise storage performance and cost."
"What does AWS Compute Optimizer use as the primary identifier for resources when providing recommendations?","Amazon Resource Name (ARN)","Instance ID","Resource Name","IP Address","Compute Optimizer uses the Amazon Resource Name (ARN) as the primary identifier for resources."
"How can you verify the accuracy of AWS Compute Optimizer's recommendations before implementing them?","By conducting thorough testing and monitoring","By consulting with AWS support","By ignoring the current monitoring metrics","By assuming that the recommendations are always correct","It's essential to verify the accuracy of Compute Optimizer's recommendations by conducting thorough testing and monitoring before fully implementing them in a production environment."
"Which AWS service would you use to centrally manage AWS Compute Optimizer across multiple AWS accounts within an organisation?","AWS Organizations","AWS IAM","AWS CloudTrail","AWS Systems Manager","AWS Organizations allows you to centrally manage services like Compute Optimizer across multiple AWS accounts within an organisation."
"You want to analyse AWS Compute Optimizer recommendations programmatically. Which AWS service would you use?","AWS SDK","AWS Management Console","AWS CloudShell","AWS CLI","The AWS SDK provides a set of APIs that enable programmatic access to Compute Optimizer recommendations."
"Which of the following is an example of a 'workload characteristic' that AWS Compute Optimizer considers when generating recommendations?","CPU utilization","Number of employees","Company revenue","Office location","CPU utilization is a workload characteristic considered by AWS Compute Optimizer."
"What is the purpose of AWS Compute Optimizer's 'external metrics ingestion' feature?","To integrate with third-party monitoring tools for enhanced data analysis","To automatically configure EC2 instances","To improve cloud security","To manage cloud billing","The external metrics ingestion feature allows AWS Compute Optimizer to integrate with third-party monitoring tools for enhanced data analysis, providing more accurate recommendations."
"If you see a Compute Optimizer recommendation to switch from gp2 to gp3 EBS volumes, what is the primary benefit you can expect?","Potentially lower storage costs and improved performance","Increased data security","Reduced network latency","Automated backups","Switching from gp2 to gp3 EBS volumes can potentially lead to lower storage costs and improved performance due to the way gp3 volumes are priced and perform."
"How does AWS Compute Optimizer contribute to sustainability efforts?","By optimising resource utilisation and reducing energy consumption","By automatically recycling old hardware","By offsetting carbon emissions","By promoting cloud adoption","Compute Optimizer contributes to sustainability by optimising resource utilisation, reducing energy consumption and helping customers to reduce their carbon footprint in the cloud."
"What is the key difference between AWS Trusted Advisor and AWS Compute Optimizer?","Trusted Advisor provides general best practice recommendations, while Compute Optimizer focuses on resource right-sizing.","Trusted Advisor automatically fixes identified issues, while Compute Optimizer only provides recommendations.","Trusted Advisor is a paid service, while Compute Optimizer is free.","Trusted Advisor focuses on security, while Compute Optimizer focuses on cost.","Trusted Advisor provides general best practice recommendations across multiple areas, while Compute Optimizer specifically focuses on right-sizing compute resources for cost and performance."
"Which of the following AWS services does NOT provide metrics that can be directly used by AWS Compute Optimizer?","Amazon S3","Amazon EC2","AWS Lambda","Amazon EBS","Amazon S3 metrics are not directly used by Compute Optimizer. It analyses compute resources such as EC2, Lambda and EBS."
"What should you do if you disagree with an AWS Compute Optimizer recommendation?","Investigate the underlying metrics and resource utilisation patterns","Ignore the recommendation without further analysis","Immediately implement the recommendation anyway","Contact AWS support to dispute the recommendation","If you disagree with a recommendation, the best course of action is to investigate the underlying metrics and resource utilisation patterns to understand why Compute Optimizer made that recommendation and whether it aligns with your workload requirements."
"Which of the following is NOT a compute service that AWS Compute Optimizer analyses?","Amazon ECS","Amazon EKS","Amazon SageMaker","Amazon EC2","Compute Optimizer does not directly analyse SageMaker. It focuses on compute such as EC2 and container services such as ECS and EKS."
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS resources","To automatically scale AWS resources based on demand","To monitor the health and performance of AWS resources","To manage the cost of AWS resources","AWS Compute Optimizer analyses the configuration and utilisation metrics of your AWS resources and recommends optimal AWS resource configurations to reduce costs and improve performance."
"Which AWS resources are supported by AWS Compute Optimizer for optimisation recommendations?","EC2 instances, EBS volumes, and Lambda functions","S3 buckets, DynamoDB tables, and CloudFront distributions","IAM roles, VPCs, and Security Groups","Route 53 records, SNS topics, and SQS queues","Compute Optimizer currently supports EC2 instances, EBS volumes, and Lambda functions for optimisation recommendations."
"What type of data does AWS Compute Optimizer use to generate optimisation recommendations?","Historical utilisation metrics of AWS resources","Real-time network traffic data","Configuration details of AWS security groups","User-defined cost allocation tags","Compute Optimizer analyses the historical utilisation metrics of your AWS resources, such as CPU utilisation, memory utilisation, and I/O operations, to generate recommendations."
"How does AWS Compute Optimizer help reduce costs?","By identifying under-utilised resources and recommending right-sizing options","By automatically purchasing Reserved Instances","By optimising data transfer costs between regions","By automatically deleting unused snapshots","Compute Optimizer helps reduce costs by identifying under-utilised resources and recommending right-sizing options, such as choosing a smaller instance type or reducing the provisioned IOPS for an EBS volume."
"What is the 'Performance risk' in AWS Compute Optimizer recommendations?","It indicates the likelihood that a workload might experience performance degradation if the recommended configuration is applied","It indicates the risk of data loss if the recommended configuration is applied","It indicates the risk of security vulnerabilities in the recommended configuration","It indicates the risk of increased costs in the recommended configuration","The 'Performance risk' metric indicates the likelihood that a workload might experience performance degradation if the recommended configuration is applied. A lower performance risk indicates a higher confidence in the recommendation."
"What is the benefit of enabling enhanced infrastructure metrics in AWS Compute Optimizer?","It provides more granular utilisation data for more accurate recommendations","It enables automatic scaling of AWS resources","It provides real-time performance monitoring dashboards","It automatically generates cost reports","Enhanced infrastructure metrics provide more granular utilisation data (e.g., memory utilisation for EC2 instances), leading to more accurate and reliable recommendations."
"How does AWS Compute Optimizer integrate with AWS Cost Explorer?","Compute Optimizer insights can be viewed directly within the Cost Explorer interface to correlate cost savings with optimisation recommendations","Compute Optimizer automatically generates cost reports that can be accessed from Cost Explorer","Compute Optimizer uses Cost Explorer data to determine the optimal pricing model for your resources","Compute Optimizer uses Cost Explorer data to automatically adjust your budget alerts","Compute Optimizer insights can be viewed directly within the Cost Explorer interface. This allows you to easily correlate cost savings with Compute Optimizer's optimisation recommendations."
"What is the primary difference between AWS Compute Optimizer and AWS Trusted Advisor?","Compute Optimizer provides resource optimisation recommendations, while Trusted Advisor provides best practice checks","Compute Optimizer is a paid service, while Trusted Advisor is a free service","Compute Optimizer optimises compute resources, while Trusted Advisor optimises storage resources","Compute Optimizer is a real-time monitoring tool, while Trusted Advisor is a static analysis tool","Compute Optimizer provides resource optimisation recommendations based on utilisation metrics, while Trusted Advisor provides best practice checks based on configuration and security aspects."
"Which metric is NOT considered by AWS Compute Optimizer when providing recommendations for EC2 instances?","Network bandwidth utilisation","CPU utilisation","Memory utilisation","Disk I/O operations","While Compute Optimizer considers network throughput, it mainly focuses on CPU, memory, and disk I/O to determine the optimal instance type. Network bandwidth is considered to a lesser extent, and more difficult to accurately assess within an instance."
"You have applied an AWS Compute Optimizer recommendation to reduce the size of an EC2 instance. How can you monitor the impact of this change?","Monitor the CloudWatch metrics for the EC2 instance and the Compute Optimizer dashboard","Use AWS Config to track configuration changes","Use AWS CloudTrail to audit API calls","View the AWS Service Health Dashboard","By monitoring the CloudWatch metrics for the EC2 instance and the Compute Optimizer dashboard you can ensure that the performance of the application isn't being negatively impacted."
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS resources","To monitor network traffic in AWS","To manage user permissions for AWS resources","To automate the deployment of AWS resources","AWS Compute Optimizer analyses the configuration and utilisation metrics of your AWS resources and recommends optimal AWS resources to reduce costs and improve performance."
"Which AWS resources can AWS Compute Optimizer provide recommendations for?","EC2 instances, EBS volumes, and Lambda functions","S3 buckets, DynamoDB tables, and VPCs","IAM roles, CloudFront distributions, and Route 53 records","CloudWatch dashboards, CloudTrail logs, and Config rules","AWS Compute Optimizer currently supports providing recommendations for EC2 instances, EBS volumes, and Lambda functions. The other resource types are not supported."
"In AWS Compute Optimizer, what does the 'findings' section display?","Resource optimisation recommendations","Security vulnerabilities detected","Network latency metrics","Cost breakdown by service","The 'findings' section in AWS Compute Optimizer presents the resource optimisation recommendations based on the analysis of resource utilisation."
"What type of data does AWS Compute Optimizer primarily analyse to generate recommendations?","Historical utilisation metrics","Real-time network traffic","User access logs","Application performance traces","AWS Compute Optimizer analyses historical utilisation metrics, such as CPU utilisation, memory utilisation, and I/O operations, to identify optimisation opportunities."
"Which of the following is NOT a benefit of using AWS Compute Optimizer?","Automated security patching","Cost reduction","Performance improvement","Rightsizing resources","AWS Compute Optimizer focuses on cost reduction, performance improvement, and rightsizing resources but does not provide automated security patching."
"How does AWS Compute Optimizer determine if an EC2 instance is over-provisioned?","By analysing CPU, memory, and network utilisation","By monitoring security group rules","By tracking API calls","By checking the instance's operating system version","AWS Compute Optimizer analyses CPU, memory, and network utilisation metrics to determine if an EC2 instance is over-provisioned. It identifies instances that are consistently underutilised."
"If AWS Compute Optimizer recommends a smaller EC2 instance size, what is the primary reason?","The current instance is underutilised, leading to wasted resources","The current instance is experiencing network congestion","The current instance has a high security risk","The current instance is running an outdated operating system","If AWS Compute Optimizer recommends a smaller instance size, it's because the current instance is underutilised, meaning it's consuming more resources than necessary for its workload."
"Which of the following factors does AWS Compute Optimizer consider when recommending EBS volume types?","IOPS and throughput","Encryption status","Backup frequency","Geographic location","AWS Compute Optimizer considers the Input/Output Operations Per Second (IOPS) and throughput of EBS volumes when recommending different volume types to optimise performance and cost."
"How can you access the recommendations provided by AWS Compute Optimizer?","Through the AWS Management Console, AWS CLI, and AWS SDKs","Only through the AWS Management Console","By directly querying the Compute Optimizer database","By submitting a support ticket to AWS","AWS Compute Optimizer recommendations can be accessed through the AWS Management Console, AWS CLI, and AWS SDKs, allowing for flexible integration with existing infrastructure management workflows."
"What is the primary goal of AWS Compute Optimizer's Lambda function recommendations?","To reduce the cost of Lambda function execution","To improve the security of Lambda functions","To decrease Lambda function deployment time","To increase the memory available to Lambda functions","AWS Compute Optimizer aims to reduce the cost of Lambda function execution by recommending optimal memory configurations based on the function's actual resource utilisation."
"What is the primary function of AWS Compute Optimizer?","Recommending optimal AWS resource configurations based on workload analysis.","Managing AWS Identity and Access Management (IAM) roles.","Monitoring network traffic between AWS resources.","Providing cost estimates for AWS services.","Compute Optimizer analyses the historical utilisation of resources like EC2 instances and EBS volumes to provide recommendations for right-sizing them, saving costs and improving performance."
"Which AWS Compute Optimizer feature helps identify idle or underutilised resources?","Resource Efficiency Metrics","Workload Analysis Reports","Performance Monitoring Dashboards","Cost Optimisation Alerts","Compute Optimizer provides Resource Efficiency Metrics that clearly indicate whether resources are being underutilised or over-provisioned."
"Which of the following resources can AWS Compute Optimizer provide recommendations for?","EC2 Instances, EBS Volumes, and Lambda functions","S3 Buckets, DynamoDB Tables, and VPCs","CloudFront Distributions, Route 53 Hosted Zones, and SNS Topics","IAM Roles, CloudWatch Alarms, and CloudTrail Trails","Compute Optimizer currently supports EC2 instances, EBS volumes and Lambda functions for resource optimisation recommendations."
"What data source does AWS Compute Optimizer primarily use to generate its recommendations?","Amazon CloudWatch metrics","AWS Config rules","VPC Flow Logs","AWS Trusted Advisor checks","Compute Optimizer relies on historical utilisation data obtained from Amazon CloudWatch metrics to analyse the performance of resources and generate appropriate recommendations."
"How does AWS Compute Optimizer help improve workload performance?","By recommending optimal resource configurations to meet workload demands.","By automatically migrating workloads to different AWS regions.","By encrypting data at rest and in transit.","By providing detailed security vulnerability reports.","Compute Optimizer identifies bottlenecks and recommends instance types or volume sizes that better match workload demands, resulting in improved performance."
"Which pricing model does AWS Compute Optimizer use?","It is a free service.","Pay-as-you-go","Reserved Instance based","Spot Instance based","AWS Compute Optimizer is a free service. You only pay for the resources you eventually choose to change based on its recommendations."
"After implementing a recommendation from AWS Compute Optimizer, what should you do to ensure continued optimisation?","Regularly review Compute Optimizer recommendations as workload patterns change.","Disable Compute Optimizer to avoid further recommendations.","Manually adjust resource configurations without using Compute Optimizer.","Ignore future Compute Optimizer recommendations.","Workloads change over time. Continuously reviewing Compute Optimizer recommendations will help ensure that your resources remain optimised."
"What is the benefit of enabling enhanced infrastructure metrics in AWS Compute Optimizer?","Enhanced infrastructure metrics provides more granular and detailed resource utilisation data for more accurate recommendations.","Enhanced infrastructure metrics reduces the amount of data collected by Compute Optimizer.","Enhanced infrastructure metrics automatically migrates resources to more cost-effective regions.","Enhanced infrastructure metrics encrypts all data collected by Compute Optimizer.","Enhanced infrastructure metrics, by leveraging things like memory utilisation, helps Compute Optimizer provide more specific and accurate optimisation recommendations."
"You want to automatically implement Compute Optimizer recommendations. What is the best approach?","Currently, Compute Optimizer provides recommendations which must be implemented manually.","Create CloudWatch alarms to automatically resize resources.","Use AWS Config to automatically adjust resource configurations.","Use AWS Systems Manager Automation to apply recommendations.","Compute Optimizer provides recommendations but does not have built-in automation for direct implementation. You must implement the recommendations manually based on your assessment."
"What is the recommended action to take if AWS Compute Optimizer suggests downsizing an EC2 instance?","Monitor the instance after downsizing to ensure performance remains acceptable.","Immediately terminate the original EC2 instance.","Increase the EBS volume size to compensate for the smaller instance.","Migrate the instance to a different AWS region.","After downsizing an instance, it's crucial to closely monitor its performance to confirm that the new configuration adequately handles the workload without degradation."
"What primary function does AWS Compute Optimizer perform?","Recommending optimal AWS resource configurations","Providing real-time monitoring of resource utilisation","Automating the deployment of AWS resources","Managing AWS cost allocation tags","Compute Optimizer analyses the utilisation metrics of your AWS resources and recommends optimal configurations (instance types, EBS volume types, etc.) to reduce costs and improve performance."
"Which AWS service is required as a data source for AWS Compute Optimizer to generate recommendations?","CloudWatch","CloudTrail","Config","Trusted Advisor","Compute Optimizer relies on CloudWatch metrics to analyse the utilisation of your resources and generate right-sizing recommendations."
"Which of the following AWS resources can AWS Compute Optimizer provide recommendations for?","EC2 instances, EBS volumes, and Lambda functions","S3 buckets, DynamoDB tables, and CloudFront distributions","IAM roles, VPCs, and Security Groups","Route 53 hosted zones, SNS topics, and SQS queues","Compute Optimizer supports EC2 instances, EBS volumes, and Lambda functions for providing optimisation recommendations based on workload performance characteristics."
"What is the main benefit of using Enhanced Infrastructure Metrics with AWS Compute Optimizer?","Provides more granular and accurate resource utilisation data","Enables integration with third-party monitoring tools","Reduces the cost of CloudWatch metrics","Automatically adjusts resource capacity in real-time","Enhanced Infrastructure Metrics provide more detailed insights into resource utilisation, leading to more accurate and relevant recommendations from Compute Optimizer."
"What type of rightsizing recommendations does AWS Compute Optimizer provide for EC2 instances?","Instance type and instance family","Region and Availability Zone","Storage type and size","Security Group and IAM Role","Compute Optimizer suggests optimal instance types and families based on the historical workload characteristics and resource utilisation of your EC2 instances."
"How does AWS Compute Optimizer help in reducing costs for AWS Lambda functions?","By recommending optimal memory allocation settings","By automatically scaling Lambda functions based on traffic","By identifying unused Lambda functions","By optimising Lambda function code","Compute Optimizer analyses Lambda function execution and provides recommendations for the optimal memory allocation to improve performance and reduce costs."
"What is the 'Savings Opportunity' presented by AWS Compute Optimizer?","An estimate of potential cost savings from implementing recommendations","A discount offered on AWS services for using Compute Optimizer","A feature that automatically shuts down underutilised resources","A measure of improved application performance","The Savings Opportunity provides an estimate of the potential cost savings you can achieve by implementing Compute Optimizer's recommendations for right-sizing your resources."
"How frequently does AWS Compute Optimizer typically refresh its recommendations?","Recommendations are typically refreshed on a daily basis","Recommendations are only generated when you manually request them","Recommendations are refreshed every hour","Recommendations are refreshed once a month","Compute Optimizer continuously analyses resource utilisation and typically refreshes its recommendations on a daily basis to reflect changes in workload patterns."
"Which pricing model does AWS Compute Optimizer use?","Compute Optimizer is generally offered at no additional charge","Compute Optimizer charges based on the number of resources analysed","Compute Optimizer charges based on the number of recommendations implemented","Compute Optimizer requires a subscription fee","AWS Compute Optimizer is generally offered at no additional charge. It leverages CloudWatch metrics which incur their own costs."
"Can AWS Compute Optimizer provide recommendations across multiple AWS accounts?","Yes, through cross-account access configuration","No, Compute Optimizer only operates within a single AWS account","Yes, automatically across all accounts in an AWS Organization","Yes, but only for accounts in the same AWS Region","Compute Optimizer can provide recommendations across multiple AWS accounts by configuring cross-account access through IAM roles, enabling centralised optimisation management."
"What primary function does AWS Compute Optimizer serve?","Recommending optimal AWS resources for workloads","Detecting security vulnerabilities in EC2 instances","Managing AWS cost allocation tags","Automating infrastructure deployments","Compute Optimizer analyses your workloads and recommends the most efficient AWS resources (e.g., EC2 instance types, EBS volume types) to reduce costs and improve performance."
"Which of the following AWS services integrates directly with AWS Compute Optimizer for resource optimisation?","EC2 Auto Scaling","AWS CloudTrail","Amazon S3","AWS Lambda","Compute Optimizer provides recommendations for EC2 Auto Scaling groups, enabling you to optimise the group's configuration for cost and performance."
"What is a key factor that AWS Compute Optimizer uses to generate its recommendations?","Historical utilisation data of AWS resources","Current stock prices of EC2 instances","Number of AWS support cases opened","Size of the AWS account's bill","Compute Optimizer analyses the historical utilisation data of your AWS resources, such as CPU utilisation, memory utilisation, and network I/O, to generate its recommendations."
"What is the main benefit of implementing AWS Compute Optimizer's recommendations?","Reduced AWS costs and improved application performance","Increased AWS support response times","Enhanced security posture of AWS resources","Simplified AWS account management","Implementing Compute Optimizer's recommendations typically leads to reduced AWS costs by using more efficient resources and improved application performance by ensuring adequate resource allocation."
"Which of these AWS Compute Optimizer recommendation types is specifically related to storage optimisation?","EBS volume type recommendations","CPU architecture recommendations","Memory optimisation recommendations","Networking bandwidth recommendations","Compute Optimizer offers recommendations on the optimal EBS volume type to use for your EC2 instances, based on I/O performance characteristics and cost."
"How does AWS Compute Optimizer help in rightsizing AWS resources?","By analysing resource utilisation patterns and recommending optimal resource configurations","By automatically scaling resources based on real-time traffic demands","By providing a cost breakdown of each AWS service","By predicting future AWS infrastructure needs","Compute Optimizer analyses historical utilisation data to identify under- or over-provisioned resources and recommends optimal configurations (e.g., smaller instance sizes) to match workload requirements."
"What type of workloads does AWS Compute Optimizer support?","EC2 instances, EBS volumes and Lambda functions","Only EC2 instances","Only EC2 instances and S3 buckets","Only Lambda functions","AWS Compute Optimizer supports EC2 instances, EBS volumes, and Lambda functions."
"How frequently does AWS Compute Optimizer typically update its resource recommendations?","Recommendations are updated periodically, typically every 24 hours.","Recommendations are updated in real-time.","Recommendations are generated once and remain static.","Recommendations are updated based on AWS support ticket creation.","Compute Optimizer continuously analyses resource utilisation and typically updates its recommendations periodically (usually around every 24 hours) as workloads change."
"What information is NOT considered by AWS Compute Optimizer when creating recommendations?","Data residency requirements","CPU utilization","Memory utilization","Network I/O","Compute Optimizer focuses on resource utilization metrics like CPU, memory, and network. Data residency and other compliance requirements are not considered."
"Which of the following is a key performance indicator (KPI) tracked by AWS Compute Optimizer to generate recommendations?","CPU utilisation","Number of concurrent users","Average database query latency","Number of login attempts","Compute Optimizer uses CPU utilisation as a key metric to assess resource usage and identify potential optimisation opportunities."
"What is the primary function of AWS Compute Optimizer?","To provide resource optimisation recommendations for AWS compute services.","To monitor the security of AWS compute resources.","To manage the billing and cost allocation for AWS compute resources.","To automate the deployment of AWS compute resources.","Compute Optimizer analyses the configuration and utilisation metrics of your AWS compute resources and recommends optimal AWS resources to reduce costs and improve performance."
"Which AWS compute services does AWS Compute Optimizer provide recommendations for?","EC2 instances, EBS volumes, and Lambda functions","S3 buckets, Glacier archives and Redshift clusters","DynamoDB tables, SQS queues, and SNS topics","CloudFront distributions, Route 53 zones and VPCs","Compute Optimizer provides recommendations for EC2 instances, EBS volumes, and Lambda functions, suggesting right-sizing and optimisation opportunities."
"What type of recommendations does AWS Compute Optimizer provide for EC2 instances?","Instance type recommendations","Security group recommendations","Network configuration recommendations","AMI selection recommendations","Compute Optimizer primarily focuses on providing instance type recommendations, suggesting more suitable instance types based on workload patterns."
"What data sources does AWS Compute Optimizer use to generate recommendations?","CloudWatch metrics","CloudTrail logs","VPC Flow Logs","Trusted Advisor checks","Compute Optimizer analyses CloudWatch metrics, such as CPU utilisation, memory utilisation, and network I/O, to generate its recommendations."
"What is the benefit of implementing AWS Compute Optimizer's recommendations?","Reduced costs and improved performance","Enhanced security and compliance","Simplified deployment and management","Automated backup and recovery","Implementing Compute Optimizer's recommendations typically leads to reduced costs by right-sizing resources and improved performance by allocating appropriate resources to workloads."
"How can you access AWS Compute Optimizer?","Through the AWS Management Console, CLI, or SDK","By using a third-party monitoring tool","Through a custom API","By contacting AWS Support","Compute Optimizer is natively integrated into AWS and accessible through the AWS Management Console, Command Line Interface (CLI), or Software Development Kit (SDK)."
"What is the significance of the 'savings opportunity' metric in AWS Compute Optimizer?","It estimates the potential cost savings from implementing the recommendations.","It indicates the level of security risk associated with the current configuration.","It shows the performance improvement achieved after implementing the recommendations.","It measures the total resource utilisation across all AWS services.","The 'savings opportunity' metric provides an estimate of the potential cost savings that can be realised by implementing the recommended resource optimisations."
"How does AWS Compute Optimizer help with rightsizing Lambda functions?","By recommending optimal memory allocation","By suggesting optimal timeout settings","By optimising the deployment package size","By automating function code updates","Compute Optimizer helps rightsizing Lambda functions by analysing function execution metrics and recommending the optimal memory allocation to balance performance and cost."
"Which of the following is NOT a factor considered by AWS Compute Optimizer when generating EC2 instance recommendations?","Network bandwidth requirements","Operating system version","Storage I/O performance","CPU utilisation","While Compute Optimizer considers CPU utilisation and storage I/O, it does not directly consider the operating system version when making EC2 instance recommendations. It's more about performance characteristics."
"Can AWS Compute Optimizer provide recommendations across different AWS Regions?","Yes, it can provide recommendations for resources in multiple regions.","No, it only provides recommendations for resources in the current region.","It provides consolidated recommendations for all regions in a single report.","Recommendations are only visible in the region where the resources were created.","Compute Optimizer can provide recommendations for resources across different AWS Regions, but you need to enable the service in each individual region."
"What is the primary function of AWS Compute Optimizer?","To provide recommendations to optimise the performance and cost of your AWS compute resources","To automate the deployment of AWS compute resources","To monitor the real-time health of AWS compute resources","To provide detailed security reports for AWS compute resources","AWS Compute Optimizer analyses the configuration and utilisation metrics of your AWS compute resources and recommends optimal AWS resources for them, reducing costs and improving performance."
"Which AWS compute resources are supported by AWS Compute Optimizer?","EC2 instances, EBS volumes and Lambda functions","S3 buckets, DynamoDB tables and CloudFront distributions","VPC networks, Route 53 domains and IAM roles","CloudWatch dashboards, CloudTrail logs and CloudFormation stacks","Compute Optimizer supports EC2 instances, EBS volumes, and Lambda functions, providing recommendations to right-size these resources."
"What data does AWS Compute Optimizer use to generate recommendations?","Historical utilisation data from CloudWatch metrics","Real-time network traffic data from VPC Flow Logs","Security vulnerability data from AWS Security Hub","Customer satisfaction survey data from AWS Support","Compute Optimizer uses historical utilisation data from CloudWatch metrics, such as CPU utilisation, memory utilisation, and network I/O, to generate recommendations."
"How does AWS Compute Optimizer help reduce costs?","By identifying underutilised resources and recommending right-sized instances","By automatically applying reserved instances","By optimizing database query performance","By compressing data stored in S3","Compute Optimizer identifies underutilised resources and recommends smaller instance sizes or different instance types, which can reduce costs."
"Which pricing model is used by AWS Compute Optimizer?","Compute Optimizer is a free service.","Pay-as-you-go pricing based on the number of resources analysed.","Subscription-based pricing with monthly fees.","One-time fee for unlimited access.","AWS Compute Optimizer is a free service that analyses your AWS resources and provides optimisation recommendations without any charges."
"What is a 'finding' in the context of AWS Compute Optimizer?","A recommendation for improving the performance or cost of a resource","An error message indicating a problem with the service","A list of all AWS resources currently in use","A summary of the user's account activity","A finding in Compute Optimizer represents a specific recommendation for optimising a resource, such as changing the instance type or storage configuration."
"What is the 'Performance Risk' metric in AWS Compute Optimizer?","An assessment of the likelihood that a resource will experience performance issues due to under-provisioning","A measure of the resource's vulnerability to security threats","An indication of the resource's compliance with regulatory standards","A calculation of the total cost savings potential","The Performance Risk metric indicates the likelihood that a resource will experience performance issues due to under-provisioning, helping users understand the potential impact of a recommendation."
"How can you access AWS Compute Optimizer recommendations?","Through the AWS Management Console, AWS CLI, or AWS SDKs","Only through the AWS Management Console","By contacting AWS Support directly","Only through third-party monitoring tools","You can access Compute Optimizer recommendations through the AWS Management Console, AWS CLI, and AWS SDKs, providing flexibility in how you interact with the service."
"What is the 'Savings Opportunity' shown by AWS Compute Optimizer?","An estimate of the potential cost savings from implementing the recommendations","A guaranteed refund from AWS if recommendations are not effective","A bonus credit applied to your AWS account","A discount on future AWS services","The Savings Opportunity is an estimate of the potential cost savings that can be achieved by implementing the recommendations provided by Compute Optimizer."
"Can AWS Compute Optimizer automatically apply the recommended changes to your resources?","No, Compute Optimizer provides recommendations, but the user must manually implement the changes.","Yes, Compute Optimizer can automatically apply all recommendations.","Yes, but only for EC2 instance type changes.","Yes, but only with explicit permission from AWS Support.","Compute Optimizer provides recommendations, but the user must manually implement the changes. It does not automatically apply the recommendations."