"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Control Tower?","Automating the setup and governance of a multi-account AWS environment","Monitoring individual EC2 instance performance","Managing AWS Lambda function deployments","Optimising S3 bucket storage costs","Control Tower automates the setup of a landing zone and provides ongoing governance for multi-account AWS environments, ensuring consistency and compliance."
"In AWS Control Tower, what is a Landing Zone?","A well-architected, multi-account AWS environment","A disaster recovery site","A dedicated VPC for security appliances","A central repository for AWS CloudFormation templates","The Landing Zone represents the foundational, multi-account architecture built and managed by Control Tower, providing a secure and compliant environment."
"Which AWS service does Control Tower primarily rely on to provision and manage accounts?","AWS Organizations","AWS IAM","AWS CloudTrail","AWS Config","Control Tower heavily relies on AWS Organizations to manage accounts, create organisational units (OUs), and apply service control policies (SCPs)."
"What is a Guardrail in AWS Control Tower?","A preventative or detective control that enforces compliance","A custom IAM role","An Amazon CloudWatch alarm","A network access control list (ACL)","Guardrails are policies, implemented through SCPs and AWS Config rules, that provide ongoing governance and enforce compliance best practices within the AWS environment."
"What type of accounts are typically included in an AWS Control Tower Landing Zone?","Management account and member accounts","Only member accounts","Only the management account","A single root account","A Control Tower Landing Zone typically includes a management account and multiple member accounts organised under OUs."
"How does AWS Control Tower help with compliance?","By enforcing pre-defined guardrails and providing visibility into compliance status","By automatically encrypting all data at rest","By replacing existing security tools","By providing a real-time intrusion detection system","Control Tower helps with compliance by enforcing guardrails and providing a centralised view of compliance status across the AWS environment."
"What is the function of the AWS Control Tower Account Factory?","To automate the creation of new AWS accounts within the Landing Zone","To manage user access to AWS resources","To monitor the cost of AWS services","To automatically patch EC2 instances","The Account Factory automates the process of creating new AWS accounts that are pre-configured with the Landing Zone's security and compliance settings."
"Which of the following is NOT a core component of an AWS Control Tower Landing Zone?","AWS CloudFormation","AWS Organizations","AWS IAM","Amazon SQS","AWS CloudFormation, AWS Organizations, and AWS IAM are all core components that Control Tower uses to automate account provisioning and governance. Amazon SQS is not a core component."
"How can you centrally manage AWS service access across multiple accounts in AWS Control Tower?","Using Service Control Policies (SCPs)","Using IAM role delegation","Using AWS Firewall Manager","Using VPC peering","Service Control Policies (SCPs), applied at the OU level in AWS Organizations, are the primary mechanism for centrally managing AWS service access across multiple accounts."
"What is the purpose of organisational units (OUs) in AWS Control Tower?","To logically group AWS accounts and apply policies consistently","To manage individual IAM users","To create virtual private clouds (VPCs)","To store data in S3 buckets","Organisational units (OUs) allow you to logically group AWS accounts based on business function or security requirements, and then apply policies consistently across those accounts."
"What happens when a resource in a member account violates a mandatory guardrail in AWS Control Tower?","Control Tower remediates the violation automatically or alerts the administrator","The resource is automatically terminated","The account is suspended","The user is blocked from accessing the resource","Control Tower automatically remediates the violation or alerts the administrator. Remediation can be automatic in some cases, or manual in others. It depends on the type of the guardrail."
"What is the recommended approach for customising an AWS Control Tower Landing Zone?","Use lifecycle events, customisations for Control Tower (CfCT), and Customizations to AWS Control Tower","Directly modify the CloudFormation templates","Manually configure each account","Disable Control Tower and build a custom solution","The recommended approach for customizing an AWS Control Tower landing zone is to leverage Lifecycle Events, Customizations for Control Tower (CfCT) and Customizations to AWS Control Tower (which replaced CfCT) which offer a safe and maintainable way to add custom configurations and solutions to your landing zone. Avoid direct modifications of the CloudFormation templates."
"What is the benefit of using AWS Control Tower for cost management?","Centralised visibility of costs across all accounts within the Landing Zone","Automatic cost optimisation of EC2 instances","Real-time cost forecasting","Automated budget alerts for individual users","Control Tower provides centralised visibility of costs across all accounts within the Landing Zone, allowing for better cost tracking and management."
"How does AWS Control Tower ensure consistency across accounts?","By enforcing pre-defined guardrails and automatically provisioning resources using standardised templates","By requiring manual configuration of each account","By preventing users from making any changes","By regularly auditing user activity","Control Tower ensures consistency by enforcing pre-defined guardrails, and automatically provisioning resources using standardised templates."
"Which AWS service is used to implement preventative guardrails in AWS Control Tower?","Service Control Policies (SCPs)","AWS Config","AWS CloudTrail","AWS IAM","Preventative guardrails in Control Tower are typically implemented using Service Control Policies (SCPs), which restrict the actions that users can take within the accounts governed by the Landing Zone."
"How do you onboard an existing AWS account into an AWS Control Tower Landing Zone?","Through the AWS Control Tower console, by selecting the account and attaching it to an organisational unit (OU)","By manually migrating resources to a new account","By deleting the existing account and recreating it through the Account Factory","By running a script to automatically transfer data","AWS Control Tower provides a guided process to onboard existing accounts. From the AWS Control Tower console, select the account and attach it to an organisational unit (OU)."
"Which of the following is NOT a benefit of using AWS Control Tower?","Simplified compliance management","Increased operational efficiency","Centralised security control","Elimination of the need for any AWS expertise","While Control Tower simplifies compliance and improves operational efficiency, it still requires some level of AWS expertise for setup, customisation, and ongoing management."
"What is the role of the AWS Control Tower audit account?","To provide a central repository for audit logs and security findings","To manage user access to AWS resources","To store backups of all data","To run performance tests","The audit account in Control Tower serves as a central repository for audit logs and security findings, providing a single place to monitor compliance and security across the environment."
"Which AWS service does Control Tower use to log API calls across all accounts in the Landing Zone?","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS X-Ray","Control Tower relies on AWS CloudTrail to log API calls across all accounts, providing an audit trail of all actions taken within the Landing Zone."
"How does AWS Control Tower integrate with AWS Security Hub?","It automatically feeds security findings from Security Hub into the Control Tower dashboard","It replaces Security Hub","It disables Security Hub","It prevents Security Hub from detecting threats","AWS Control Tower integrates with AWS Security Hub. It automatically feeds security findings from Security Hub into the Control Tower dashboard, providing a unified view of security posture."
"In AWS Control Tower, what does the term 'drift' refer to?","A deviation from the baseline configuration established by the Landing Zone","Slow network performance","High cost of AWS services","Unexpected user behaviour","Drift in Control Tower refers to a deviation from the baseline configuration established by the Landing Zone. This can include changes to IAM policies, security group rules, or other configuration settings that violate the established guardrails."
"How does AWS Control Tower simplify the creation of new VPCs?","By providing pre-configured VPC templates within the Account Factory","By automatically configuring VPC peering connections","By allowing users to create VPCs directly in the management account","By eliminating the need for VPCs","Control Tower simplifies VPC creation by providing pre-configured VPC templates within the Account Factory, ensuring that new VPCs are compliant with the Landing Zone's security and governance policies."
"What type of guardrail in AWS Control Tower prevents users from taking actions that violate compliance requirements?","Preventative guardrail","Detective guardrail","Reactive guardrail","Proactive guardrail","Preventative guardrails stop users from taking actions that violate compliance requirements."
"What is the function of the AWS Control Tower management account?","To centrally manage the Landing Zone and all member accounts","To store data for all member accounts","To run applications for all member accounts","To provide network connectivity for all member accounts","The management account is used to centrally manage the Landing Zone, including configuring guardrails, managing accounts, and viewing compliance reports."
"How can you identify which AWS accounts are not compliant with the established guardrails in AWS Control Tower?","Using the Control Tower dashboard, which provides a summary of compliance status","By manually reviewing the configuration of each account","By analysing CloudTrail logs","By contacting AWS support","The Control Tower dashboard provides a centralised view of compliance status across all accounts within the Landing Zone, making it easy to identify non-compliant accounts."
"What is the purpose of AWS Control Tower lifecycle events?","To trigger custom actions when Landing Zone events occur, such as account creation or OU creation","To schedule regular backups of AWS resources","To automatically scale EC2 instances based on demand","To send email notifications when errors occur","Lifecycle events in Control Tower allow you to trigger custom actions when Landing Zone events occur, such as account creation or OU creation. This enables you to automate custom configurations and integrations."
"Which of the following actions is NOT typically performed by AWS Control Tower?","Provisioning AWS accounts","Enforcing compliance through guardrails","Managing individual EC2 instances","Centralising logging and auditing","While Control Tower provisions accounts, enforces compliance, and centralises logging and auditing, it doesn't manage individual EC2 instances directly. EC2 instance management is typically handled by other services like EC2 Auto Scaling and AWS Systems Manager."
"What is the recommended way to deploy applications in an AWS Control Tower environment?","Using a CI/CD pipeline that leverages AWS CodePipeline and other DevOps tools","Manually deploying applications to each account","Deploying applications to the management account and sharing them with other accounts","Using AWS CloudFormation directly in the management account","The recommended approach is to use a CI/CD pipeline that integrates with AWS CodePipeline and other DevOps tools to automate application deployments across the accounts in the Landing Zone."
"How does AWS Control Tower help to enforce security best practices?","By implementing mandatory and strongly recommended guardrails that align with industry standards","By automatically patching all EC2 instances","By replacing existing security tools","By providing a real-time intrusion detection system","AWS Control Tower helps enforce security best practices by implementing mandatory and strongly recommended guardrails that align with industry standards. These guardrails help to prevent common security misconfigurations and ensure a consistent security posture across the environment."
"What is the primary benefit of using AWS Control Tower with multiple AWS accounts?","Isolation of resources, improved security, and simplified cost management","Faster deployment of applications","Reduced complexity of AWS infrastructure","Elimination of the need for security tools","Using multiple AWS accounts provides isolation of resources, improved security, and simplified cost management. It allows you to separate workloads, apply different security policies, and track costs for different business units or projects."
"How can you ensure that all new AWS accounts created through AWS Control Tower meet specific security requirements?","By customising the Account Factory with pre-defined security configurations","By manually configuring each new account after it is created","By creating a custom IAM role for each new account","By disabling the Account Factory and creating accounts manually","Customising the Account Factory with pre-defined security configurations ensures that all new AWS accounts created through Control Tower automatically meet the specified security requirements. This ensures consistency and reduces the risk of misconfigurations."
"What is the relationship between AWS Control Tower and AWS Organizations?","AWS Control Tower uses AWS Organizations to manage AWS accounts in the Landing Zone","AWS Organizations replaces AWS Control Tower","AWS Control Tower is a feature of AWS Organizations","AWS Organizations is not required to use AWS Control Tower","AWS Control Tower uses AWS Organizations to manage AWS accounts in the Landing Zone. AWS Organizations provides the foundation for creating and managing a multi-account AWS environment."
"What is the key difference between a preventative and a detective guardrail in AWS Control Tower?","Preventative guardrails prevent actions from being taken, while detective guardrails identify actions that violate compliance","Preventative guardrails are more effective than detective guardrails","Preventative guardrails are cheaper than detective guardrails","Preventative guardrails are only available in the management account","Preventative guardrails prevent actions from being taken that violate compliance requirements, while detective guardrails identify actions that violate compliance after they have occurred."
"Which type of AWS Control Tower guardrail allows for automatic remediation of non-compliant resources?","Detective guardrail","Preventative guardrail","Mandatory guardrail","Strongly recommended guardrail","Detective guardrails can trigger automatic remediation of non-compliant resources. When a detective guardrail detects a violation, it can automatically take corrective action to bring the resource back into compliance."
"How can you track changes made to AWS resources within an AWS Control Tower Landing Zone?","By analysing AWS CloudTrail logs in the audit account","By monitoring Amazon CloudWatch metrics","By using AWS Config rules","By enabling AWS Trusted Advisor","CloudTrail logs provide a detailed record of all API calls made within the AWS environment. By analysing these logs in the audit account, you can track changes made to AWS resources."
"Which of the following is a key consideration when planning an AWS Control Tower implementation?","Defining your organisational structure and compliance requirements","Choosing the cheapest AWS region","Migrating all existing resources to a single AWS account","Disabling all existing security tools","Defining your organisational structure (OUs) and compliance requirements is a critical first step in planning a Control Tower implementation. This helps you to configure the Landing Zone and guardrails to meet your specific needs."
"What is the purpose of the 'AWS Control Tower Account Vending Machine' (AVM) functionality?","This is now known as the Account Factory; it automates account creation within the Landing Zone","To manage IAM user credentials","To monitor AWS service usage","To automate software deployments","The AWS Control Tower Account Vending Machine (AVM) is now called the Account Factory. It automates the process of creating new AWS accounts that are pre-configured with the Landing Zone's security and compliance settings."
"How does AWS Control Tower help with enforcing data residency requirements?","By using preventative guardrails to restrict resource deployment to specific AWS regions","By automatically encrypting all data at rest","By providing a compliance dashboard that shows data residency status","By replacing existing data residency tools","Preventative guardrails can be used to restrict resource deployment to specific AWS regions, ensuring that data is stored within the required geographic boundaries to meet data residency requirements."
"What is a common use case for integrating AWS Control Tower with a third-party security information and event management (SIEM) system?","To centralise security event logging and analysis across multiple AWS accounts","To automate patching of EC2 instances","To manage user access to AWS resources","To monitor the cost of AWS services","Integrating Control Tower with a SIEM system allows you to centralise security event logging and analysis across multiple AWS accounts, providing a comprehensive view of your security posture and enabling faster threat detection and response."
"Which AWS service can be used to automate the process of updating the AWS Control Tower Landing Zone?","Customizations for AWS Control Tower (CfCT) (Now Customisations to AWS Control Tower)","AWS CloudFormation","AWS Systems Manager","AWS CodeDeploy","Customisations to AWS Control Tower allows you to automate the process of updating the AWS Control Tower Landing Zone with new features and security updates. This helps to ensure that your Landing Zone is always up-to-date and secure."
"How does AWS Control Tower help to reduce the risk of misconfigurations in AWS accounts?","By enforcing pre-defined guardrails that prevent common misconfigurations","By automatically patching all EC2 instances","By replacing existing configuration management tools","By monitoring user activity and blocking suspicious actions","Control Tower enforces pre-defined guardrails that prevent common misconfigurations, such as leaving S3 buckets publicly accessible or creating overly permissive IAM policies. This helps to reduce the risk of security vulnerabilities and compliance violations."
"What is the recommended approach for managing changes to AWS Control Tower guardrails?","Using a version control system and a deployment pipeline","Manually updating the guardrails in the Control Tower console","Disabling the guardrails and implementing custom policies","Allowing users to modify the guardrails directly","Using a version control system, such as Git, and a deployment pipeline allows you to track changes to the guardrails, automate the deployment process, and ensure that changes are properly tested and approved before being implemented."
"Which AWS service can be used to create custom guardrails for AWS Control Tower?","AWS Config","AWS CloudTrail","AWS IAM","Amazon CloudWatch","AWS Config can be used to create custom guardrails for Control Tower. AWS Config allows you to define custom rules that check the configuration of your AWS resources and identify non-compliant resources."
"How can you delegate administrative access to a specific OU within an AWS Control Tower Landing Zone?","By creating an IAM role in the management account and assigning it to a user in the OU","By granting the user direct access to the management account","By creating a separate AWS account for the OU administrator","By disabling access to the management account for all users in the OU","The most secure and recommended practice to delegate permissions is to create an IAM role in the management account, and then assign the permissions to a user within the required OU."
"What is the purpose of the AWS Control Tower 'Baseline' in the context of infrastructure management?","It defines the standard, compliant state of the AWS environment","It represents the cost of running the AWS environment","It specifies the minimum performance requirements for AWS resources","It describes the network topology of the AWS environment","The Baseline in AWS Control Tower defines the standard, compliant state of the AWS environment. It includes the configuration of AWS resources, security settings, and compliance policies that must be enforced across all accounts in the Landing Zone."
"Which action should you take if an AWS Control Tower guardrail is too restrictive for a specific workload?","Request an exception for the workload, documenting the justification and alternative controls","Disable the guardrail for the entire Landing Zone","Modify the guardrail directly in the Control Tower console","Migrate the workload to a different AWS account outside of the Landing Zone","You should request an exception for the workload, documenting the justification and alternative controls, so that you can track and address specific concerns that come from disabling the guardrail."
"How does AWS Control Tower support the principle of least privilege?","By allowing you to define granular IAM policies that restrict user access to only the resources they need","By automatically granting users full administrative access to all AWS resources","By preventing users from accessing any AWS resources","By requiring multi-factor authentication for all AWS users","AWS Control Tower supports the principle of least privilege by allowing you to define granular IAM policies that restrict user access to only the resources they need to perform their job functions. This helps to reduce the risk of security breaches and compliance violations."
"When should you consider using AWS Control Tower?","When you need to manage multiple AWS accounts in a secure and compliant manner","When you only have a single AWS account","When you need to optimise the cost of your AWS infrastructure","When you need to migrate on-premises workloads to AWS","Control Tower is a good fit when you have multiple AWS accounts and need to automate their management in a secure and compliant manner."
"In AWS Control Tower, what is the significance of the 'Strongly Recommended' guardrail status?","It indicates a best practice that should be implemented, but isn't strictly enforced by default","It indicates a mandatory requirement that must be met","It indicates a deprecated policy that should be removed","It indicates a custom policy that has been defined by the user","Strongly recommended guardrails indicate best practices that should be implemented to improve security and compliance, but they aren't strictly enforced by default. You can choose to enable these guardrails based on your specific requirements."
"What is the recommended way to manage user identities in an AWS Control Tower environment?","Using a centralised identity provider that integrates with AWS IAM Identity Center (successor to AWS Single Sign-On)","By creating separate IAM users in each AWS account","By using the root user credentials for all AWS accounts","By disabling user authentication and relying on service accounts","Using a centralised identity provider that integrates with AWS IAM Identity Center (successor to AWS Single Sign-On) allows you to manage user identities and access centrally, providing a consistent authentication experience across all AWS accounts in the Landing Zone."
"In AWS Control Tower, what is the purpose of a Landing Zone?","To automatically set up a multi-account AWS environment following best practices","To manually configure each AWS account individually","To manage only the networking components of AWS","To monitor AWS spending across different regions","The Landing Zone is a pre-configured, multi-account AWS environment that follows AWS best practices, making it easy to set up and govern your cloud environment."
"Which AWS service is used by Control Tower to manage identity and access across all accounts in the Landing Zone?","AWS IAM Identity Center (successor to AWS Single Sign-On)","AWS IAM","AWS Directory Service","AWS Organizations","Control Tower uses AWS IAM Identity Center (successor to AWS Single Sign-On) to provide centralised identity and access management across all accounts within the Landing Zone."
"What is the primary function of an AWS Control Tower Account Factory?","To automate the creation of new AWS accounts within the Landing Zone","To manually create AWS accounts using the AWS Management Console","To migrate existing AWS accounts into the Landing Zone","To monitor the cost of existing AWS accounts","The Account Factory automates the process of creating new AWS accounts that are compliant with the Control Tower governance policies."
"In AWS Control Tower, what is a Guardrail?","A rule or policy that enforces compliance within your AWS environment","A physical security measure to protect your AWS infrastructure","A monitoring tool to detect security threats","A firewall rule to control network traffic","Guardrails are rules or policies that enforce compliance with security, operational, and cost management best practices in your AWS environment."
"Which type of Guardrail in AWS Control Tower prevents actions from being performed that violate a policy?","Preventative Guardrail","Detective Guardrail","Reactive Guardrail","Informative Guardrail","Preventative Guardrails actively prevent actions that violate a policy, ensuring compliance before a non-compliant state is reached."
"Which type of Guardrail in AWS Control Tower detects non-compliance after it has occurred?","Detective Guardrail","Preventative Guardrail","Reactive Guardrail","Informative Guardrail","Detective Guardrails detect non-compliance after it has occurred, allowing you to take corrective action to remediate the issue."
"What is the purpose of AWS Organizations in the context of AWS Control Tower?","To provide centralised management and governance across multiple AWS accounts","To provide a secure VPN connection to your on-premises network","To store and manage your application code","To provide a content delivery network (CDN) service","AWS Organizations provides the foundation for Control Tower by enabling centralised management and governance across multiple AWS accounts, organised into organisational units (OUs)."
"In AWS Control Tower, what is an Organisational Unit (OU)?","A logical grouping of AWS accounts within your AWS Organization","A physical location where your AWS resources are hosted","A security group that controls access to your AWS resources","A billing unit for tracking AWS costs","Organisational Units (OUs) are logical groupings of AWS accounts that allow you to apply policies and governance controls to multiple accounts simultaneously."
"What is the benefit of using AWS Control Tower over manually configuring a multi-account AWS environment?","Automation and consistent enforcement of best practices","Lower cost of AWS resources","Faster network performance","Ability to use unsupported AWS services","Control Tower automates the setup and configuration of a multi-account AWS environment and provides consistent enforcement of security and compliance best practices."
"What is the 'StackSet' functionality used for within the AWS Control Tower context?","Deploying resources across multiple accounts and regions in a consistent manner","To monitor the CPU utilisation of EC2 instances","To manage user permissions in IAM","To create and manage S3 buckets","StackSets enable the deployment of resources across multiple AWS accounts and regions, ensuring a consistent configuration across your Landing Zone."
"Which AWS service does Control Tower leverage for logging and monitoring across all managed accounts?","AWS CloudTrail and AWS CloudWatch","AWS Config","AWS Lambda","Amazon SQS","Control Tower leverages CloudTrail for audit logging and CloudWatch for monitoring, providing visibility into activities and resource utilisation across all managed accounts."
"Can you customise the AWS Control Tower Landing Zone after initial setup?","Yes, but customisations should be carefully planned and implemented using infrastructure-as-code to maintain consistency","No, the Landing Zone is a fixed configuration and cannot be changed","Only the AWS Support team can make changes to the Landing Zone","Only cosmetic changes are allowed","While you can customise the Landing Zone, it is crucial to plan and implement customisations using infrastructure-as-code to maintain consistency and avoid disrupting the automated governance features."
"What is the AWS Control Tower dashboard used for?","To provide a centralised view of the compliance status of your AWS environment","To configure network settings for your AWS accounts","To deploy new applications to your AWS accounts","To manage user access to your AWS accounts","The Control Tower dashboard provides a centralised view of the compliance status of your AWS accounts and OUs, allowing you to quickly identify and address any non-compliance issues."
"When launching a new AWS account using the Control Tower Account Factory, what information is typically required?","Account name, email address, and desired OU","AWS account ID of an existing account","Credit card information","List of IAM users to create","The Account Factory requires information such as the account name, email address, and the desired organisational unit (OU) to provision a new compliant AWS account."
"What is the relationship between AWS Control Tower and AWS Config?","Control Tower uses Config to assess and audit the configuration of AWS resources","Control Tower replaces the functionality of Config","Config replaces the functionality of Control Tower","Control Tower and Config are completely independent services","Control Tower integrates with AWS Config to assess and audit the configuration of your AWS resources against defined compliance rules."
"What is the purpose of AWS Service Catalog in the context of AWS Control Tower?","To provide a catalogue of pre-approved and compliant AWS resources for users to provision","To manage user access to AWS services","To monitor the performance of AWS services","To manage billing and cost allocation for AWS services","Service Catalog allows you to create and manage a catalogue of pre-approved and compliant AWS resources that users can provision, ensuring consistency and adherence to governance policies."
"If an AWS account drifts from the configured guardrails in Control Tower, what should you do?","Remediate the non-compliant account using Control Tower's remediation features or manual intervention","Ignore the non-compliance as it will automatically be corrected","Delete the non-compliant account","Create a new guardrail to match the drifted configuration","When an account drifts from the configured guardrails, you should remediate it using Control Tower's remediation features or by manually correcting the configuration to bring it back into compliance."
"Which AWS Control Tower feature helps enforce a consistent network configuration across multiple AWS accounts?","Network Factory","VPC Sharing","Transit Gateway","Direct Connect Gateway","Although Control Tower integrates with networking components, it doesn't provide a dedicated 'Network Factory' feature for networking configurations.  Configuration management via StackSets is the right approach."
"What is the purpose of the AWS Control Tower Lifecycle Events?","To trigger actions based on changes to the state of Control Tower resources","To schedule automatic backups of your AWS accounts","To monitor the health of your AWS resources","To send notifications about billing changes","Lifecycle Events can be used to trigger actions based on changes to the state of Control Tower resources, such as account creation or guardrail updates."
"How does AWS Control Tower help with cost management?","By providing visibility into AWS spending across all accounts and enforcing cost control policies","By automatically reducing the cost of AWS resources","By providing free AWS credits to new accounts","By completely removing the need for cost optimisation","Control Tower helps with cost management by providing visibility into AWS spending across all accounts and enabling the enforcement of cost control policies through guardrails."
"Which AWS service can be used to integrate AWS Control Tower with existing DevOps pipelines?","AWS CodePipeline","AWS CloudFormation","AWS OpsWorks","AWS Systems Manager","AWS CodePipeline can be used to integrate Control Tower with existing DevOps pipelines, enabling automated deployment and management of compliant AWS environments."
"What is the recommended approach for managing infrastructure-as-code templates within an AWS Control Tower environment?","Using AWS CloudFormation StackSets to deploy templates across multiple accounts","Using AWS Management Console to manually configure each account","Using AWS CLI to deploy templates to individual accounts","Using AWS CodeCommit to store the templates","Using CloudFormation StackSets is the recommended approach for deploying infrastructure-as-code templates across multiple accounts in a consistent and governed manner."
"Can you integrate AWS Control Tower with on-premises Active Directory?","Yes, using AWS IAM Identity Center (successor to AWS Single Sign-On) and AWS Directory Service","No, Control Tower only supports AWS native identity providers","Only with a custom-built solution","Only if you migrate your Active Directory to AWS","AWS IAM Identity Center (successor to AWS Single Sign-On) and AWS Directory Service can be used to integrate Control Tower with on-premises Active Directory, providing a unified identity and access management experience."
"What is the purpose of the AWS Control Tower Customizations for AWS Control Tower (CfCT) framework?","To extend and customise the functionality of Control Tower beyond the standard Landing Zone","To replace the default Landing Zone with a completely custom implementation","To simplify the initial setup of the Landing Zone","To automatically update Control Tower to the latest version","Customizations for AWS Control Tower (CfCT) allows you to extend and customise the functionality of Control Tower beyond the standard Landing Zone, enabling you to tailor the environment to your specific business needs."
"Which AWS Control Tower feature helps to enforce a standardised naming convention for AWS resources across all accounts?","Guardrails that check resource names against a defined pattern","Automated resource tagging","Resource locking","Custom resource creation scripts","Guardrails can be configured to check resource names against a defined pattern, ensuring that all resources adhere to a standardised naming convention across all accounts."
"What is the purpose of the AWS Control Tower Governance Summary report?","To provide a detailed overview of the compliance status of your AWS environment","To list all AWS resources in your environment","To provide a summary of your AWS spending","To list all IAM users and their permissions","The Governance Summary report provides a detailed overview of the compliance status of your AWS environment, including information on non-compliant resources and the guardrails that are being violated."
"How does AWS Control Tower integrate with AWS Security Hub?","Security Hub receives security findings from Control Tower's detective guardrails","Control Tower replaces the functionality of Security Hub","Security Hub configures Control Tower's guardrails","Control Tower and Security Hub are completely independent services","Security Hub receives security findings from Control Tower's detective guardrails, providing a centralised view of security alerts across your entire AWS environment."
"What is the recommended way to update the AWS Control Tower Landing Zone to the latest version?","Use the Control Tower console to initiate the Landing Zone update process","Manually update each AWS account in the Landing Zone","Recreate the entire Landing Zone from scratch","Ignore the update and continue using the older version","The Control Tower console provides a streamlined process for updating the Landing Zone to the latest version, ensuring that you benefit from the latest features and security patches."
"What is the purpose of the AWS Control Tower 'Baseline' configuration?","To define the minimum security and compliance standards for all AWS accounts in the Landing Zone","To define the maximum resource limits for AWS accounts","To define the naming conventions for AWS resources","To define the network topology for the Landing Zone","The Baseline configuration defines the minimum security and compliance standards that all AWS accounts in the Landing Zone must adhere to, ensuring a consistent and secure foundation."
"Can you use AWS Control Tower to manage existing AWS accounts that were not created through the Account Factory?","Yes, you can enrol existing accounts into Control Tower's management scope","No, Control Tower can only manage accounts created through the Account Factory","Only if the accounts are in the same AWS region as the Landing Zone","Only if the accounts are completely empty","You can enrol existing AWS accounts into Control Tower's management scope, allowing you to bring them under the centralised governance and compliance controls."
"In AWS Control Tower, what is the purpose of the 'Mandatory' guardrail category?","To enforce essential security and compliance requirements that cannot be disabled","To provide optional recommendations for improving security","To provide informational alerts about potential issues","To track the cost of AWS resources","Mandatory guardrails enforce essential security and compliance requirements that cannot be disabled, ensuring a minimum level of protection for all AWS accounts."
"What happens when an AWS account violates a 'Preventative' guardrail in Control Tower?","The action that violates the guardrail is blocked, and the account remains compliant","The action is allowed, but a notification is sent to the administrator","The account is automatically suspended","The resource that violates the guardrail is automatically deleted","Preventative guardrails block actions that violate the policy, preventing the account from entering a non-compliant state."
"What is the difference between 'Preventative' and 'Detective' guardrails in terms of their impact on AWS resources?","Preventative guardrails block actions, while Detective guardrails only detect violations","Preventative guardrails automatically fix violations, while Detective guardrails require manual remediation","Preventative guardrails are more expensive than Detective guardrails","Preventative guardrails are only used for security, while Detective guardrails are used for cost management","Preventative guardrails actively prevent actions that violate a policy, while Detective guardrails detect non-compliance after it has occurred."
"What is the typical workflow for adding a new AWS account to an existing AWS Control Tower Landing Zone?","Use the Account Factory to create a new account within the desired OU","Manually create the account in the AWS Management Console and then enrol it in Control Tower","Migrate an existing AWS account from another AWS Organization","Request the AWS Support team to create the account","The Account Factory provides an automated and governed way to create new AWS accounts that are compliant with the Control Tower policies and configurations."
"Which AWS service is responsible for enforcing identity federation in AWS Control Tower?","AWS IAM Identity Center (successor to AWS Single Sign-On)","AWS IAM","AWS Directory Service","Amazon Cognito","AWS IAM Identity Center (successor to AWS Single Sign-On) provides centralised identity and access management, enabling identity federation with existing identity providers."
"What is the purpose of 'Remediation' actions in AWS Control Tower guardrails?","To automatically correct non-compliant configurations and bring resources back into compliance","To provide recommendations for fixing non-compliant configurations","To automatically delete non-compliant resources","To generate reports on non-compliant resources","Remediation actions automatically correct non-compliant configurations and bring resources back into compliance, reducing the manual effort required to maintain a compliant environment."
"In AWS Control Tower, can you apply different sets of guardrails to different Organisational Units (OUs)?","Yes, you can apply different sets of guardrails to different OUs to tailor governance policies to specific needs","No, all OUs must have the same set of guardrails applied","Only if the OUs are in different AWS regions","Only for mandatory guardrails","You can apply different sets of guardrails to different OUs, allowing you to tailor the governance policies to the specific needs and requirements of each OU."
"What is the AWS Control Tower 'Audit Manager' primarily used for?","Continuously auditing and assessing the compliance of your AWS environment","Managing user access and permissions","Monitoring the performance of AWS resources","Managing billing and cost allocation","AWS Audit Manager helps you continuously audit and assess the compliance of your AWS environment, providing evidence to support your audits and compliance requirements."
"How does AWS Control Tower support compliance with industry regulations like HIPAA or PCI DSS?","By providing pre-configured guardrails and compliance reports that align with these regulations","By automatically encrypting all data stored in AWS","By providing a dedicated compliance team to manage your AWS environment","By guaranteeing compliance with all industry regulations","Control Tower provides pre-configured guardrails and compliance reports that align with industry regulations like HIPAA or PCI DSS, helping you to meet your compliance obligations."
"What is the significance of the 'Shared Responsibility Model' in the context of AWS Control Tower?","It defines the shared responsibilities between AWS and the customer for security and compliance","It defines the shared responsibilities between different teams within an organisation","It defines the shared responsibilities between Control Tower and other AWS services","It defines the shared responsibilities between AWS and third-party auditors","The Shared Responsibility Model defines the shared responsibilities between AWS and the customer for security and compliance. AWS is responsible for the security of the cloud, while the customer is responsible for the security in the cloud."
"What is the recommended approach for implementing custom security controls that are not covered by the standard AWS Control Tower guardrails?","Use Customizations for AWS Control Tower (CfCT) or integrate with third-party security tools","Modify the existing Control Tower guardrails directly","Disable the standard guardrails and implement completely custom controls","Ignore the missing controls and rely solely on the standard guardrails","Customizations for AWS Control Tower (CfCT) and integration with third-party security tools provide a flexible way to implement custom security controls that are not covered by the standard guardrails."
"What is the impact of enabling AWS CloudTrail in AWS Control Tower?","It provides a centralised audit trail of all API activity across all accounts in the Landing Zone","It improves the performance of AWS resources","It reduces the cost of AWS resources","It encrypts all data stored in AWS","Enabling CloudTrail provides a centralised audit trail of all API activity across all accounts in the Landing Zone, enabling you to track changes and identify potential security issues."
"What is the purpose of AWS Control Tower's 'Service Quotas' feature?","To manage the maximum resource limits for each AWS account in the Landing Zone","To manage the cost of AWS resources","To manage user access to AWS services","To manage the network configuration of AWS resources","Service Quotas allows you to manage the maximum resource limits for each AWS account in the Landing Zone, preventing resource exhaustion and ensuring that resources are used efficiently."
"How does AWS Control Tower help in automating the process of creating and configuring AWS accounts?","By providing the Account Factory, which automates the creation and configuration of new accounts","By automatically migrating existing accounts to AWS","By providing a command-line interface for managing AWS accounts","By providing a graphical interface for monitoring AWS accounts","The Account Factory automates the creation and configuration of new AWS accounts, ensuring that they are compliant with the Control Tower policies and configurations."
"What are the key components of a well-architected AWS Control Tower Landing Zone?","AWS Organizations, AWS IAM Identity Center (successor to AWS Single Sign-On), AWS CloudTrail, AWS Config, and Account Factory","Amazon S3, Amazon EC2, Amazon RDS, Amazon VPC, and Amazon CloudFront","AWS Lambda, Amazon DynamoDB, Amazon SQS, Amazon SNS, and Amazon API Gateway","AWS CodeCommit, AWS CodeBuild, AWS CodeDeploy, AWS CodePipeline, and AWS Cloud9","The key components of a well-architected Landing Zone include AWS Organizations for centralised management, AWS IAM Identity Center (successor to AWS Single Sign-On) for identity management, CloudTrail for auditing, Config for compliance, and Account Factory for account provisioning."
"What is one of the main advantages of using AWS Control Tower for managing a multi-account AWS environment compared to building your own solution?","Reduced operational overhead and faster time to value through automation","Lower cost of AWS resources","Increased network bandwidth","Greater customisation options","Control Tower reduces operational overhead and accelerates time to value by automating the setup, configuration, and governance of a multi-account AWS environment."
"What is the primary purpose of AWS Control Tower?","To automate the setup and governance of a multi-account AWS environment","To manage EC2 instances across multiple regions","To monitor network traffic in your VPC","To analyse S3 bucket costs","Control Tower automates the setup of a well-architected, multi-account AWS environment based on best practices, ensuring consistent governance."
"In AWS Control Tower, what is a Landing Zone?","A pre-configured, secure, multi-account AWS environment","A virtual private cloud (VPC)","A region where you launch resources","A security group","The Landing Zone is a fully configured, secure environment that adheres to established best practices and acts as the foundation for your multi-account AWS environment."
"What is an AWS Control Tower Account Factory?","A tool for automating the creation of new AWS accounts within your organisation","A service for managing IAM users and roles","A feature for cost allocation and reporting","A mechanism for encrypting data at rest","The Account Factory automates the process of creating new accounts that are compliant with your Control Tower governance policies."
"Which of the following AWS services is NOT directly integrated with AWS Control Tower?","AWS CloudTrail","Amazon S3","AWS Config","AWS Lambda","While Control Tower integrates with many services, it doesn't directly integrate with Lambda in the same way it does with core governance services like CloudTrail and Config."
"What is a preventative guardrail in AWS Control Tower?","A control that prevents resource deployments that violate compliance rules","A control that detects compliance violations after they occur","A tool for cost optimisation","A dashboard for monitoring security alerts","Preventative guardrails proactively prevent actions that could lead to non-compliance, stopping violations before they happen."
"What is a detective guardrail in AWS Control Tower?","A control that detects non-compliant resources or configurations","A control that enforces specific IAM policies","A tool for automating security patching","A mechanism for managing access keys","Detective guardrails identify non-compliant resources or configurations, enabling you to take corrective action."
"How does AWS Control Tower enforce compliance across AWS accounts?","Through the use of Service Control Policies (SCPs)","By restricting access to the AWS Management Console","By automatically patching EC2 instances","By forcing all traffic through a single VPC","Control Tower uses SCPs to define organisation-wide permissions and enforce compliance requirements across all accounts in the organisation."
"Which AWS service is used by AWS Control Tower to log all API calls made in the managed accounts?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS IAM","AWS CloudTrail records all API calls made in the AWS environment, providing an audit trail of activities."
"What is the purpose of the AWS Control Tower dashboard?","To provide a centralised view of compliance status and account health","To manage EC2 instances","To configure networking settings","To deploy applications","The Control Tower dashboard offers a single pane of glass for monitoring the overall health and compliance of your AWS environment."
"What type of AWS account is used to manage billing and consolidate costs across all accounts in an AWS Control Tower environment?","Management Account","Shared Services Account","Workload Account","Audit Account","The Management Account, created during initial setup, is used for billing and organisation-level management."
"In AWS Control Tower, what is the purpose of the Shared Services account?","To host shared resources and services that are used by multiple AWS accounts","To store backups of all AWS accounts","To act as a disaster recovery site","To manage user identities","The Shared Services account hosts common resources like networking infrastructure, security tools, and centralised logging services."
"Which of the following is a core benefit of using AWS Control Tower?","Enforced governance and compliance","Reduced EC2 instance costs","Simplified S3 bucket management","Automated application deployment","Control Tower ensures consistent governance, security, and compliance across your AWS environment, following best practices."
"What is the first step in setting up AWS Control Tower?","Creating a Landing Zone","Creating new AWS accounts","Configuring IAM roles","Deploying applications","Creating a Landing Zone is the very first step, and will prepare your account for setting up all the other pieces of infrastructure."
"Which of the following actions CANNOT be performed directly from the AWS Control Tower console?","Modifying SCPs","Creating VPCs","Viewing compliance status","Creating new accounts","While you can view compliance status and create accounts, you can't directly modify SCPs from the Control Tower console. You would typically do this in the AWS Organisations console."
"How does AWS Control Tower help with cost management?","By providing cost allocation tags and reporting","By automatically reducing EC2 instance sizes","By optimising S3 storage classes","By eliminating unused EBS volumes","Control Tower helps with cost management by providing cost allocation tags, detailed reports, and insights into resource usage across your organisation."
"What is the AWS Control Tower 'Region deny' guardrail used for?","To prevent the deployment of resources in specific AWS Regions","To limit access to certain AWS services","To restrict network traffic between VPCs","To enforce encryption at rest","The 'Region deny' guardrail prevents resource deployment in specified regions, helping to enforce geographic compliance or cost control policies."
"When using AWS Control Tower, which AWS service provides centralised logging across all accounts?","AWS CloudTrail and Amazon S3","AWS CloudWatch Logs","AWS Config","AWS IAM","CloudTrail logs are centralised to a dedicated S3 bucket within the Audit account, providing a single location for audit data."
"What is the purpose of the AWS Control Tower Audit account?","To store logs and audit information from all AWS accounts","To host shared services","To manage user identities","To act as a disaster recovery site","The Audit account is dedicated to storing logs and audit information, providing a secure and centralised location for security and compliance data."
"Which AWS service does Control Tower use to manage user identities centrally?","AWS IAM through Federated Access","AWS Directory Service","AWS Cognito","AWS Single Sign-On (SSO)","Control Tower integrates with AWS IAM through Federated Access and AWS Single Sign-On (SSO) to manage user identities and provide centralised access to AWS accounts."
"You need to ensure that all new S3 buckets created in your AWS environment are encrypted by default. How can you achieve this using AWS Control Tower?","By enabling the 'Encryption for S3 Buckets' preventative guardrail","By creating a custom IAM policy","By configuring S3 bucket policies","By using AWS KMS directly","The preventative guardrail 'Encryption for S3 Buckets' ensures that all new S3 buckets are created with encryption enabled."
"What is the scope of AWS Control Tower's governance policies?","AWS Organisation","Individual AWS accounts","Specific EC2 instances","Individual S3 buckets","Control Tower's governance policies are applied at the AWS Organisation level, impacting all accounts within the organisation."
"Which of the following actions would require updating the AWS Control Tower Landing Zone?","Adding a new AWS Region to your organisation","Creating a new IAM user","Deploying a new EC2 instance","Modifying an S3 bucket policy","Adding a new Region to your organisation requires updating the Landing Zone configuration to ensure proper governance and security in the new Region."
"What is the purpose of AWS Control Tower's integration with AWS Organisations?","To manage and group AWS accounts","To monitor EC2 instance performance","To analyse S3 bucket costs","To configure VPC settings","AWS Organisations is used to manage and group AWS accounts into organisational units (OUs), which Control Tower leverages for applying governance policies."
"How does AWS Control Tower simplify the process of creating AWS accounts?","By providing a pre-configured Account Factory","By automating IAM role creation","By managing VPC configurations","By optimising EC2 instance sizes","The Account Factory provides a streamlined, automated process for creating new AWS accounts that are compliant with Control Tower's governance policies."
"You want to receive notifications when a non-compliant resource is detected in your AWS Control Tower environment. Which AWS service can you integrate with Control Tower to achieve this?","AWS CloudWatch Events","AWS Lambda","AWS SQS","AWS SNS","AWS CloudWatch Events (now known as Amazon EventBridge) can be configured to trigger notifications when non-compliant resources are detected, allowing for timely remediation."
"In AWS Control Tower, what is an organisational unit (OU)?","A logical grouping of AWS accounts","A virtual private cloud (VPC)","A security group","A region where you launch resources","An OU is a container for grouping accounts, enabling you to apply different sets of governance policies to different groups of accounts."
"Which of the following is NOT a typical component of an AWS Control Tower Landing Zone?","Shared Services account","Workload account","Management account","Individual IAM user accounts","Individual IAM user accounts are not a core component of the Landing Zone itself; users are managed within the individual accounts or through SSO."
"What type of customisations can be applied to the AWS Control Tower Landing Zone using Customisations for Control Tower (CfCT)?","Automated deployment of resources and services","Custom branding of the AWS Management Console","Manual configuration of IAM policies","Direct modification of SCPs","CfCT allows you to automate the deployment of additional resources and services to complement the standard Landing Zone setup."
"What is the primary benefit of using AWS Control Tower's 'Guardrails' feature?","Enforcement of compliance and security best practices","Reduction of EC2 instance costs","Simplified S3 bucket management","Automated application deployment","Guardrails ensure that your AWS environment adheres to security and compliance best practices, preventing violations and detecting non-compliant resources."
"Which type of control in AWS Control Tower helps you meet regulatory requirements, such as HIPAA or PCI DSS?","Mandatory Controls","Preventative Controls","Detective Controls","Remediating Controls","AWS Control Tower's mandatory controls help enforce requirements from security or compliance mandates."
"What is the purpose of the AWS Control Tower Account Factory Customization (AFC)?","To allow pre-configuring accounts during creation","To customize SCPs","To create Custom Guardrails","To change landing zone","AFC allows you to pre-configure the accounts during the creation process with specific requirements that are needed by your workload."
"You need to enforce a policy that prevents users from creating EC2 instances in a specific region. What type of AWS Control Tower guardrail should you use?","Preventative guardrail","Detective guardrail","Proactive guardrail","Reactive guardrail","A preventative guardrail is the correct choice because it will actively prevent users from creating EC2 instances in the specified region before the action is completed."
"What is the purpose of enabling AWS SSO (Single Sign-On) in AWS Control Tower?","To provide a centralized identity management system for accessing all AWS accounts","To enable multi-factor authentication for all AWS users","To automatically generate IAM roles for each AWS account","To encrypt data in transit between AWS services","Enabling AWS SSO provides a centralized way for users to access multiple AWS accounts and applications with a single set of credentials."
"Which AWS service does AWS Control Tower leverage to detect and report on configuration changes in your AWS environment?","AWS Config","AWS CloudWatch","AWS CloudTrail","AWS Trusted Advisor","AWS Config is used to continuously monitor and record the configuration of your AWS resources, allowing Control Tower to detect and report on configuration changes."
"What is the recommended approach for deploying applications in an AWS Control Tower environment?","Using dedicated workload accounts","Deploying all applications in the Management account","Deploying all applications in the Shared Services account","Creating separate AWS Regions for each application","The recommended approach is to use dedicated workload accounts for each application or set of related applications to maintain isolation and security."
"How does AWS Control Tower help to improve security posture across an organisation?","By enforcing security guardrails and providing centralized logging","By automatically patching EC2 instances","By managing VPC configurations","By optimising S3 storage classes","Control Tower enforces security guardrails, provides centralized logging, and automates security-related tasks to improve the overall security posture of the organisation."
"Which of the following AWS Control Tower concepts ensures consistency across all AWS accounts in the organisation?","Landing Zone","Account Factory","Guardrails","Organisational Units","The Landing Zone pre-configures a multi-account environment to be secure and well-architected according to AWS best practices. It ensures consistent configuration across all accounts in the environment."
"You have a requirement to prevent data from being stored in specific AWS regions for compliance reasons. How can AWS Control Tower help meet this requirement?","By implementing a Region Deny preventative guardrail","By creating a custom IAM policy","By configuring S3 bucket policies","By enabling encryption at rest","Implementing a Region Deny preventative guardrail prevents resources from being created in the specified regions."
"What role does AWS Organisations play within the AWS Control Tower architecture?","It provides the hierarchical structure for managing multiple AWS accounts.","It provides real-time metrics for monitoring AWS resource utilization.","It automates the deployment of infrastructure-as-code templates.","It centrally manages identity and access management (IAM) roles and policies.","AWS Organisations provides the hierarchical structure for managing and governing multiple AWS accounts, allowing Control Tower to enforce policies and controls across the entire organisation."
"What is the recommended method for granting access to AWS resources across multiple accounts in an AWS Control Tower environment?","Using IAM roles with cross-account access","Creating individual IAM users in each AWS account","Sharing IAM user credentials across multiple accounts","Granting direct access to the Management account","Using IAM roles with cross-account access is the recommended method because it allows users in one account to assume roles in other accounts, providing secure and auditable access."
"You need to ensure that all AWS accounts created through the Account Factory have a specific set of IAM roles pre-configured. How can you achieve this with AWS Control Tower?","By using Account Factory Customizations (AFC)","By creating a custom Landing Zone template","By manually configuring each account after creation","By modifying the default Account Factory settings","Account Factory Customizations (AFC) allows you to customize the account creation process, including pre-configuring IAM roles and other settings."
"Which AWS service is used by AWS Control Tower to provide a single sign-on (SSO) experience for users accessing multiple AWS accounts?","AWS IAM Identity Center (Successor to AWS SSO)","AWS IAM","AWS Cognito","AWS Directory Service","AWS IAM Identity Center (Successor to AWS SSO) enables users to access multiple AWS accounts and applications with a single set of credentials, simplifying the sign-on experience."
"How does AWS Control Tower help in maintaining a consistent security baseline across all AWS accounts?","By enforcing pre-defined guardrails based on security best practices","By automatically patching operating systems on EC2 instances","By providing a centralized dashboard for monitoring security events","By encrypting all data at rest and in transit","Control Tower enforces security guardrails that are based on AWS security best practices, ensuring that all accounts adhere to a consistent security baseline."
"What is the purpose of the 'mandatory' guardrail category in AWS Control Tower?","To enforce controls that cannot be disabled by users, ensuring baseline compliance.","To provide recommendations for security best practices.","To detect non-compliant resources and configurations.","To remediate non-compliant resources automatically.","'Mandatory' guardrails are designed to enforce controls that cannot be disabled, ensuring a minimum level of compliance across all accounts."
"Which of the following is NOT a feature provided by the AWS Control Tower dashboard?","Centralized view of compliance status","Automated remediation of non-compliant resources","Visibility into account inventory and health","Drill-down into individual account details","Automated remediation of non-compliant resources is not a feature of the Control Tower dashboard. It provides visibility and alerting but requires manual intervention for remediation."
"How can you extend AWS Control Tower functionality to integrate with third-party security and governance tools?","Through Customizations for Control Tower (CfCT)","By creating custom guardrails using AWS Lambda functions","By directly modifying the Landing Zone configuration","By using AWS CloudFormation templates","Customizations for Control Tower (CfCT) enables you to extend Control Tower functionality by automating the deployment of additional resources, including integrations with third-party tools."
"What is the AWS Control Tower-recommended approach to manage networking configurations across multiple AWS accounts?","Centralizing network resources in a shared services account","Creating individual VPCs in each account","Using AWS Transit Gateway to connect VPCs across accounts","Enforcing strict network isolation between all AWS accounts","Centralizing network resources in a shared services account is the recommended approach, allowing for consistent network policies and easier management."
"Which of the following AWS services is commonly used with AWS Control Tower to manage and automate infrastructure deployments?","AWS CloudFormation","AWS CodeDeploy","AWS OpsWorks","AWS Systems Manager","AWS CloudFormation is commonly used with Control Tower to manage and automate infrastructure deployments."
"You have identified a non-compliant resource in one of your AWS accounts managed by Control Tower. What is the typical first step in remediating this issue?","Reviewing the details of the non-compliance in the Control Tower dashboard","Automatically terminating the non-compliant resource","Modifying the AWS Control Tower Landing Zone configuration","Deleting the AWS account containing the non-compliant resource","The first step is typically to review the details of the non-compliance in the Control Tower dashboard to understand the issue and determine the appropriate remediation steps."
"Which of the following is an advantage of using AWS Control Tower over manually setting up a multi-account AWS environment?","Reduced operational overhead and faster time to value","Greater flexibility in customizing security policies","Lower overall cost of infrastructure","Simplified deployment of custom applications","Control Tower automates many of the tasks involved in setting up and managing a multi-account environment, leading to reduced operational overhead and faster time to value."
"How can you ensure that all future AWS accounts created within your AWS Control Tower environment are compliant with your organisation's security policies?","By using the Account Factory and applying preventative guardrails","By manually configuring each new account","By creating custom IAM policies","By modifying the Landing Zone configuration","The Account Factory ensures that new accounts are created with the appropriate configuration, and preventative guardrails prevent actions that could lead to non-compliance."
"In AWS Control Tower, what is the purpose of a landing zone?","To provision and manage a secure, multi-account AWS environment","To monitor AWS service usage costs","To automatically scale EC2 instances","To configure network routing tables","A landing zone in AWS Control Tower automates the setup of a secure, well-architected, multi-account AWS environment, providing a starting point for your cloud journey."
"Which AWS service is commonly used to manage identities and access within an AWS Control Tower environment?","AWS IAM Identity Center (Successor to AWS Single Sign-On)","Amazon Cognito","AWS Directory Service","AWS Key Management Service (KMS)","AWS IAM Identity Center (Successor to AWS Single Sign-On) provides central management of identities and access to multiple AWS accounts within the Control Tower environment."
"What is the primary purpose of AWS Control Tower's guardrails?","To enforce preventative or detective controls to maintain compliance","To optimise EC2 instance pricing","To automate database backups","To manage VPC configurations","Guardrails in AWS Control Tower enforce policies to maintain security, compliance, and operational best practices within the AWS environment."
"In AWS Control Tower, what is an AWS account that is managed under Control Tower called?","Managed Account","Security Account","Member Account","Master Account","Accounts managed by AWS Control Tower are referred to as managed accounts or member accounts."
"Which AWS Control Tower feature allows you to centrally view compliance status across all managed accounts?","Dashboard","AWS Config","AWS CloudTrail","AWS Security Hub","The Control Tower Dashboard provides a centralised view of compliance status and overall governance across managed accounts."
"What is the function of a 'Mandatory' guardrail in AWS Control Tower?","It cannot be disabled and is always enforced","It can be disabled at any time","It is only a recommendation and not enforced","It applies only to specific accounts","Mandatory guardrails are non-negotiable policies that must be enforced to maintain the overall security and compliance of the Control Tower environment."
"How does AWS Control Tower enable centralised logging and auditing?","By configuring AWS CloudTrail across all accounts","By using Amazon CloudWatch Logs Insights","By integrating with third-party SIEM tools","By enabling S3 access logging","AWS Control Tower automates the configuration of AWS CloudTrail across all accounts, ensuring centralised logging and auditing of all AWS API activity."
"When setting up AWS Control Tower, what is the recommended AWS Region to choose for the home region?","The region closest to the majority of your users","Any region with sufficient AWS services","The region with the lowest cost","A region that meets regulatory requirements","The home region is where AWS Control Tower is deployed and where the majority of management operations are performed. Choosing a region close to your users minimises latency for administrative tasks."
"Which statement best describes the use of AWS Organisations in AWS Control Tower?","AWS Organisations provides the underlying account structure for Control Tower","AWS Organisations is an optional component","AWS Organisations is only used for billing purposes","AWS Organisations is used for cost optimisation","AWS Organisations is fundamental to AWS Control Tower, providing the underlying account structure and enabling centralised policy enforcement."
"In AWS Control Tower, what is the role of the AWS Control Tower account factory?","To automate the creation of new AWS accounts within the landing zone","To manage existing AWS accounts","To monitor AWS service usage","To deploy applications to AWS accounts","The Control Tower account factory streamlines and automates the creation of new, pre-configured AWS accounts within the Control Tower environment."
"What type of control in AWS Control Tower prevents actions that violate security policies?","Preventative control","Detective control","Corrective control","Proactive control","Preventative controls prevent actions from being taken that violate security policies."
"What does a detective control in AWS Control Tower do?","Identifies security policy violations that have already occurred","Prevents security policy violations","Automatically corrects security policy violations","Predicts potential security policy violations","Detective controls identify security policy violations that have already occurred, allowing for remediation."
"If an AWS Control Tower guardrail is non-compliant, how is this typically indicated in the Control Tower dashboard?","The guardrail will be marked as 'Non-compliant' or flagged","The guardrail will be automatically disabled","The associated AWS account will be suspended","No indication will be provided","The Control Tower dashboard provides a visual indication of guardrail compliance status, highlighting any non-compliant guardrails."
"How does AWS Control Tower assist with compliance reporting?","By providing a central dashboard of compliance status across accounts","By automatically generating compliance reports","By integrating with third-party compliance tools","By replacing the need for compliance reports","Control Tower's dashboard offers a centralised view of compliance status, making it easier to track and report on compliance across the AWS environment."
"Which AWS service is used to manage secrets within an AWS Control Tower managed account?","AWS Secrets Manager","AWS IAM","AWS KMS","AWS Certificate Manager","AWS Secrets Manager is designed for securely storing and managing secrets like database credentials, API keys, and other sensitive information."
"What is the relationship between AWS Control Tower and AWS Config?","AWS Control Tower uses AWS Config to assess compliance against guardrails","AWS Control Tower replaces AWS Config","AWS Control Tower uses AWS Config to manage networking","AWS Config is not used by AWS Control Tower","Control Tower leverages AWS Config to evaluate whether AWS resources comply with defined guardrails."
"Which AWS service is typically used to manage network connectivity between VPCs in a multi-account AWS Control Tower environment?","AWS Transit Gateway","AWS VPN","AWS Direct Connect","AWS Network Firewall","AWS Transit Gateway simplifies network management by providing a central hub for connecting multiple VPCs and on-premises networks."
"What is the primary benefit of using AWS Control Tower’s automated account provisioning?","Consistency and standardisation of AWS account configurations","Reduced EC2 instance costs","Improved database performance","Simplified serverless deployment","Automated account provisioning ensures consistent and standardised configurations across all new AWS accounts, adhering to best practices and organisational policies."
"Which of the following is NOT a key component of an AWS Control Tower landing zone?","Shared Services Account","Audit Account","Management Account","Development Account","A development account is not a key component of a Control Tower landing zone. The other components are the core structure."
"Which AWS service allows you to centrally manage user access and permissions across multiple AWS accounts managed by AWS Control Tower?","AWS IAM Identity Center (Successor to AWS Single Sign-On)","AWS IAM","AWS Directory Service","AWS STS","AWS IAM Identity Center (Successor to AWS Single Sign-On) enables centralised management of user access and permissions across multiple AWS accounts within the Control Tower environment."
"What type of customisations can be implemented in an AWS Control Tower environment using Customisations for AWS Control Tower (CfCT)?","Automated deployment of resources and configurations","Automated backup and recovery processes","Automated security patching","Automated cost optimisation recommendations","Customisations for AWS Control Tower (CfCT) allow you to automate the deployment of resources and configurations, tailoring the Control Tower environment to meet specific business requirements."
"How does AWS Control Tower help in enforcing organisational policies across multiple AWS accounts?","By applying Service Control Policies (SCPs) through AWS Organisations","By deploying custom IAM roles","By using AWS CloudFormation templates","By managing network ACLs","AWS Control Tower uses Service Control Policies (SCPs) within AWS Organisations to enforce organisational policies and restrictions across all managed accounts."
"What is the recommended way to update the AWS Control Tower landing zone to the latest version?","Using the AWS Control Tower console to initiate the update process","Manually updating each AWS account","Redeploying the entire landing zone","Creating a new AWS Control Tower environment","The AWS Control Tower console provides a managed process for updating the landing zone to the latest version, ensuring compatibility and minimising disruption."
"What is the purpose of the 'Audit' account in an AWS Control Tower landing zone?","To store and analyse logs from all other accounts","To manage AWS IAM users and roles","To host shared services like DNS and NTP","To run security vulnerability scans","The Audit account is designed to securely store and analyse logs from all other accounts in the Control Tower environment, providing a centralised location for security auditing and compliance analysis."
"Which AWS service is used to automate infrastructure provisioning in an AWS Control Tower environment?","AWS CloudFormation","AWS OpsWorks","AWS CodeDeploy","AWS Elastic Beanstalk","AWS CloudFormation is a service that allows you to define and provision AWS infrastructure as code, enabling automated and repeatable deployments."
"In AWS Control Tower, what is the function of the 'Security' account?","To host security tools and resources for the entire organisation","To store AWS KMS keys","To manage network firewalls","To manage access to S3 buckets","The Security account hosts security tools and resources that provide centralised security monitoring and incident response capabilities for the entire organisation."
"Which of the following is NOT a best practice when setting up AWS Control Tower?","Enabling multi-factor authentication (MFA) for all AWS accounts","Granting broad administrator access to all users","Implementing a least privilege access model","Regularly reviewing and updating guardrails","Granting broad administrator access to all users is contrary to the principle of least privilege and is not a recommended best practice."
"How does AWS Control Tower simplify the management of AWS environments for organisations?","By automating the setup and governance of a secure, multi-account environment","By optimising EC2 instance pricing","By providing detailed cost allocation reports","By managing application deployments","AWS Control Tower automates the setup and governance of a secure, well-architected, multi-account AWS environment, simplifying AWS environment management for organisations."
"Which AWS service is used to detect and respond to security threats in an AWS Control Tower environment?","AWS Security Hub","Amazon GuardDuty","AWS Shield","AWS Inspector","AWS Security Hub provides a central place to manage security alerts and compliance status across your AWS accounts, offering a consolidated view of your security posture."
"What is the recommended approach for deploying applications in an AWS Control Tower environment?","Using a CI/CD pipeline to deploy applications to individual AWS accounts","Deploying applications directly from the Management account","Using a single AWS account for all application deployments","Manually deploying applications to each AWS account","Using a CI/CD pipeline allows for automated and controlled deployments, ensuring consistency and best practices across all AWS accounts."
"What is the benefit of using AWS Control Tower's landing zone over manually configuring a multi-account AWS environment?","The landing zone provides a pre-configured, secure, and compliant foundation","Manual configuration is always cheaper","Manual configuration offers more flexibility","The landing zone is not customisable","The Control Tower landing zone offers a well-architected and secure foundation, saving significant time and effort compared to manually configuring a multi-account environment."
"Which AWS service is used to implement network segmentation and access control in an AWS Control Tower environment?","Amazon Virtual Private Cloud (VPC)","AWS Direct Connect","AWS VPN","AWS Route 53","Amazon VPC enables you to create isolated networks within the AWS cloud, allowing you to implement network segmentation and control access to your resources."
"How does AWS Control Tower contribute to cost governance in a multi-account AWS environment?","By providing cost allocation tags and reporting","By automatically optimising EC2 instance sizes","By automatically stopping unused EC2 instances","By providing discount recommendations","AWS Control Tower encourages the use of cost allocation tags, which enables detailed cost tracking and reporting across all AWS accounts."
"Which AWS service is used to manage and enforce identity federation in an AWS Control Tower environment?","AWS IAM Identity Center (Successor to AWS Single Sign-On)","AWS IAM","AWS Directory Service","AWS Certificate Manager","AWS IAM Identity Center (Successor to AWS Single Sign-On) allows you to connect your existing identity provider to AWS, enabling users to access AWS resources using their existing credentials."
"What is the relationship between AWS Control Tower and AWS Service Catalog?","AWS Service Catalog can be used to provision approved products in Control Tower accounts","AWS Service Catalog is not compatible with Control Tower","AWS Service Catalog replaces the AWS Marketplace","Control Tower is a service catalog","AWS Service Catalog can be used to deploy approved resources within Control Tower accounts, ensuring that only compliant and pre-approved services are provisioned."
"In AWS Control Tower, which account is responsible for the overall management and governance of the environment?","Management Account","Security Account","Audit Account","Shared Services Account","The Management account is the central point of control for the Control Tower environment, responsible for overall management and governance."
"How does AWS Control Tower support compliance with industry regulations such as GDPR or HIPAA?","By providing a framework for implementing and enforcing security controls","By automatically achieving compliance with all regulations","By generating compliance reports","By replacing the need for compliance audits","AWS Control Tower provides a framework for implementing and enforcing security controls and policies that can help organisations meet compliance requirements for various industry regulations."
"What is the purpose of using Custom Control Actions in AWS Control Tower?","To remediate non-compliant resources automatically","To create custom dashboards","To deploy custom guardrails","To manage AWS accounts","Custom Control Actions provide an automated way to remediate resources that do not comply with implemented guardrails."
"How does AWS Control Tower integrate with AWS CloudTrail?","It automatically configures CloudTrail across all managed accounts for audit logging","It replaces AWS CloudTrail","It only uses CloudTrail for the management account","It does not integrate with AWS CloudTrail","Control Tower automatically configures CloudTrail across all managed accounts, providing a centralised logging solution for auditing and compliance purposes."
"In AWS Control Tower, what is the primary use case for the Shared Services account?","To host resources that are shared across multiple accounts, such as networking infrastructure","To store security logs","To manage IAM users","To host development environments","The Shared Services account provides a location for hosting shared resources like networking infrastructure, DNS, and other services used by multiple accounts."
"Which action is generally recommended when a drift is detected in an AWS Control Tower environment?","Remediate the drifted resource back to the defined configuration","Delete the drifted resource","Ignore the drift","Disable the associated guardrail","Drift should be remediated to ensure continued compliance and security within the AWS environment."
"Which of the following is NOT a feature provided by AWS Control Tower?","Automated account provisioning","Automated compliance monitoring","Cost optimisation recommendations","Centralised identity management","Cost optimisation is not a core feature of AWS Control Tower. While it promotes cost awareness, it doesn't directly optimise costs."
"Which AWS service is often integrated with AWS Control Tower to visualize security findings and automate security workflows?","AWS Security Hub","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Security Hub aggregates security findings from various AWS services and provides a centralized view of security posture, enabling automated workflows."
"In an AWS Control Tower environment, how can you ensure that all new AWS accounts adhere to your organisation's security policies from the start?","Using the Account Factory to provision accounts with pre-defined security configurations","Manually configuring each new account","Disabling security features for faster provisioning","Using a shared AWS account for all resources","The Account Factory automates the creation of new AWS accounts with predefined security configurations, ensuring consistent security posture from the beginning."
"What type of control is implemented when an AWS Control Tower guardrail prevents the creation of an S3 bucket without encryption?","Preventative","Detective","Corrective","Informative","A preventative control stops an action from happening if it violates the guardrail; hence, preventing the creation of an unencrypted S3 bucket."
"You need to onboard an existing AWS account to your AWS Control Tower landing zone. What is the recommended approach?","Use the AWS Control Tower console to enrol the existing account","Manually migrate the account","Create a new account and migrate all resources","Delete the existing account and recreate it in Control Tower","The AWS Control Tower console provides a mechanism to enrol existing accounts into the landing zone, aligning them with the established governance and security policies."
"What is the benefit of using AWS Control Tower over manually configuring an AWS Organisation for multi-account management?","Control Tower provides automated landing zone setup and guardrail enforcement","Manually configuring AWS Organisation is cheaper","Manually configuring AWS Organisation offers more flexibility","Control Tower offers no advantage over manually configuring AWS Organisation","Control Tower automates many of the complex tasks involved in setting up and managing a secure and compliant multi-account AWS environment, saving time and effort."
"What is the primary purpose of AWS Control Tower?","Automating the setup and governance of a multi-account AWS environment","Monitoring individual EC2 instance performance","Managing AWS billing and cost optimisation across a single account","Providing a fully managed database service","AWS Control Tower automates the setup and ongoing governance of a secure, compliant, multi-account AWS environment based on best practices."
"In AWS Control Tower, what is a Landing Zone?","A pre-configured, secure, multi-account AWS environment","A designated area for storing application logs","A service for managing network access control lists","A tool for visualising AWS resource utilisation","A Landing Zone is a well-architected, multi-account AWS environment that's based on security and compliance best practices. It's the foundation for running workloads securely in the cloud."
"Which of the following is a key benefit of using AWS Control Tower?","Enforced compliance with security and compliance policies","Automated application deployment","Real-time threat detection and response","Simplified content delivery network configuration","Control Tower enforces compliance with security and compliance policies across the entire AWS environment, ensuring consistency and reducing risk."
"In AWS Control Tower, what is the purpose of Guardrails?","To enforce policies and best practices across AWS accounts","To manage AWS Identity and Access Management (IAM) roles","To monitor the health of AWS services","To optimise the cost of AWS resources","Guardrails are policies that enforce best practices and compliance standards across your AWS environment, helping to prevent misconfigurations and security vulnerabilities."
"Which AWS service is primarily used by AWS Control Tower to manage identity and access across multiple accounts?","AWS IAM Identity Center (Successor to AWS SSO)","AWS Directory Service","AWS Certificate Manager","AWS Cloud Directory","AWS IAM Identity Center (Successor to AWS SSO) is used to centrally manage users and their access to multiple AWS accounts within the Control Tower environment."
"When creating a new account through AWS Control Tower, which of the following is automatically provisioned?","A pre-configured VPC and associated network resources","A fully configured CI/CD pipeline","A dedicated security operations centre","A detailed cost allocation report","When creating an account through Control Tower, a pre-configured VPC and associated network resources are automatically provisioned based on the landing zone configuration."
"What is the role of the AWS Control Tower Account Factory?","To automate the creation of new AWS accounts within the Control Tower environment","To manage and monitor AWS account spending","To provide a central repository for AWS account credentials","To simplify the process of creating AWS Identity and Access Management (IAM) roles","The Account Factory automates the process of creating new AWS accounts, ensuring that they are configured according to the defined security and compliance policies."
"Which of the following is NOT a capability of AWS Control Tower?","Continuous compliance monitoring","Centralised logging and auditing","Automated patch management for EC2 instances","Automated account provisioning","AWS Control Tower primarily focuses on setting up and governing the environment. Automated patch management for EC2 instances is generally handled by other services like AWS Systems Manager."
"What type of policies can be implemented using AWS Control Tower Guardrails?","Preventative and Detective","Only Preventative","Only Detective","Corrective and reactive","AWS Control Tower Guardrails can be either preventative (preventing actions that violate policies) or detective (detecting policy violations after they occur)."
"You have existing AWS accounts that you want to bring under AWS Control Tower governance. What is the process for doing this?","Enrolling the existing accounts into AWS Control Tower","Deleting and recreating the accounts within Control Tower","Migrating data to new accounts created by Control Tower","Creating read replicas of the existing accounts in the Control Tower managed region","You can enrol existing AWS accounts into AWS Control Tower, bringing them under the governance of the landing zone and its guardrails."
"What is the primary purpose of AWS Control Tower?","Automating the setup and governance of a multi-account AWS environment","Providing a single pane of glass for all AWS services","Optimising the cost of individual AWS resources","Creating custom security policies","AWS Control Tower's main goal is to simplify and automate the management of a multi-account AWS environment, enforcing consistent policies and best practices."
"In AWS Control Tower, what is a Landing Zone?","A pre-configured, secure, multi-account AWS environment","A tool for visualising AWS costs","A service for managing IAM permissions","A type of virtual private cloud (VPC)","A Landing Zone is the foundation of your Control Tower environment, providing a secure and compliant multi-account structure from the start."
"Which AWS service does Control Tower primarily leverage for identity and access management across accounts?","AWS IAM Identity Center (Successor to AWS SSO)","AWS IAM","AWS Directory Service","AWS Cognito","Control Tower integrates with AWS IAM Identity Center (successor to AWS SSO) to provide centralised identity and access management across all accounts within the Landing Zone."
"What is the purpose of Guardrails in AWS Control Tower?","To enforce policies and compliance across accounts","To monitor resource utilisation","To automate software deployments","To provide cost recommendations","Guardrails in Control Tower enforce policies and compliance rules, ensuring that your AWS environment adheres to security and operational best practices."
"When setting up AWS Control Tower, what is the purpose of the 'AWS Control Tower Account Factory'?","To automate the creation of new AWS accounts within the Landing Zone","To manage existing AWS accounts","To migrate on-premises servers to AWS","To create IAM roles and policies","The Account Factory automates the process of creating new, compliant AWS accounts within the Control Tower-managed environment."
"In AWS Control Tower, what is the function of the 'AWS Control Tower Dashboard'?","To provide a centralised view of the compliance status and health of the environment","To configure Guardrails","To create new AWS accounts","To manage AWS budgets","The Control Tower Dashboard offers a single point of access for monitoring the overall health, compliance status, and performance of your managed AWS environment."
"What is a key benefit of using AWS Control Tower's preventative Guardrails?","They proactively block actions that violate policies, preventing non-compliance","They detect violations after they occur, allowing for remediation","They provide recommendations for cost optimisation","They automate the deployment of infrastructure as code","Preventative Guardrails stop non-compliant actions from happening in the first place, enhancing security and compliance posture."
"Which AWS service is NOT directly integrated and managed by AWS Control Tower?","Amazon S3","AWS IAM Identity Center (Successor to AWS SSO)","AWS CloudTrail","AWS Config","While Control Tower leverages S3 for storage and logging, it doesn't directly manage the S3 service itself. It focuses on services that enable multi-account governance and security."
"When establishing AWS Control Tower, what does the term 'Organisation Unit' (OU) refer to?","A logical grouping of AWS accounts based on business function or security requirements","A virtual network within AWS","A billing construct for aggregating costs","A collection of IAM users and groups","Organisation Units in Control Tower allow you to group AWS accounts based on your organisational structure, enabling you to apply specific policies and governance controls to different groups of accounts."
"In AWS Control Tower, what is the relationship between a 'Mandatory' guardrail and an 'Optional' guardrail?","Mandatory guardrails are always enforced, while optional guardrails can be enabled or disabled","Mandatory guardrails apply to the management account only, while optional guardrails apply to all accounts","Mandatory guardrails are free, while optional guardrails incur additional costs","Mandatory guardrails cannot be modified, while optional guardrails can be customised","Mandatory guardrails are always enabled and enforced, providing a baseline level of security and compliance, while optional guardrails offer greater flexibility to tailor policies based on specific needs."