"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Systems Manager, what is the purpose of Parameter Store?","Provides secure, hierarchical storage for configuration data and secrets management.","Automates the execution of tasks on EC2 instances.","Monitors the health of EC2 instances.","Manages user access to AWS resources.","Parameter Store offers secure, centralised storage and management of configuration data, passwords, and other sensitive information."
"What Systems Manager capability allows you to define a desired state for your AWS infrastructure and maintain it over time?","State Manager","Session Manager","Patch Manager","Inventory Manager","State Manager allows you to define and enforce a desired configuration state across your AWS resources."
"Which AWS Systems Manager feature enables you to remotely and securely manage the operating systems of your EC2 instances?","Session Manager","Automation","Run Command","Maintenance Windows","Session Manager provides secure and auditable remote access to your instances without the need to open inbound ports or manage SSH keys."
"With AWS Systems Manager Automation, what is a document?","A workflow defining a series of actions to be performed on your AWS resources.","A backup of your EC2 instance.","A script used to monitor your systems.","A container image.","Automation documents define the sequence of steps and actions that Systems Manager Automation will execute."
"What is the function of AWS Systems Manager Patch Manager?","Automates the process of patching operating systems and applications on your instances.","Manages AWS Identity and Access Management (IAM) roles.","Monitors CPU utilisation on your EC2 instances.","Backs up your databases.","Patch Manager streamlines and automates the process of applying patches to operating systems and applications, improving security and compliance."
"Which AWS Systems Manager tool can be used to collect software and hardware inventory information from your managed instances?","Inventory Manager","Compliance Manager","Distributor","Explorer","Inventory Manager gathers detailed information about the software, hardware, and configuration of your instances."
"You want to execute a shell script on a large number of EC2 instances simultaneously using AWS Systems Manager. Which feature should you use?","Run Command","Automation","Session Manager","Fleet Manager","Run Command allows you to remotely and securely execute commands or scripts on a fleet of instances."
"What AWS Systems Manager feature allows you to schedule recurring tasks on your instances, such as running scripts or installing software updates?","Maintenance Windows","State Manager","Patch Manager","Run Command","Maintenance Windows allows you to define a schedule for performing tasks on your instances, minimising disruptions to your environment."
"Which Systems Manager feature is designed to help you identify and remediate compliance issues across your AWS resources?","Compliance Manager","Inventory Manager","Patch Manager","Explorer","Compliance Manager allows you to assess and track the compliance status of your instances against defined policies and standards."
"What type of document is primarily used with AWS Systems Manager Automation?","YAML or JSON","XML","CSV","Text","Automation documents are typically defined in YAML or JSON format."
"Which AWS Systems Manager capability helps you manage and distribute software packages across your managed instances?","Distributor","Inventory Manager","Compliance Manager","Automation","Distributor enables you to securely store, version, and distribute software packages, such as agents and applications, to your instances."
"What AWS Systems Manager tool is used to centrally view and manage your hybrid environment (on-premises servers and EC2 instances)?","Fleet Manager","Explorer","Inventory Manager","Compliance Manager","Fleet Manager provides a unified interface for managing your hybrid environment, simplifying administration tasks."
"Which Systems Manager feature lets you track changes to your EC2 instance configurations over time?","Configuration Compliance","Run Command","Patch Manager","Automation","Configuration Compliance tracks changes to EC2 instance configurations over time."
"You need to ensure that a specific version of a software package is installed on all your Linux EC2 instances. What Systems Manager feature can help you achieve this?","State Manager","Session Manager","Patch Manager","Distributor","State Manager lets you enforce a desired state, ensuring a software package of a specific version is installed on all your Linux EC2 instances."
"Which AWS Systems Manager service enables you to view operational data from multiple AWS services in a single pane of glass?","Explorer","Incident Manager","Parameter Store","Fleet Manager","Explorer provides a central view of operational data, making it easier to identify and resolve issues."
"What is the primary benefit of using AWS Systems Manager Parameter Store to store sensitive information like database passwords?","Secure encryption and access control","Faster database queries","Simplified EC2 instance launch process","Automated scaling of EC2 instances","Parameter Store offers encryption and access control features to protect sensitive data."
"Which Systems Manager feature lets you create a central repository for storing and retrieving application configuration?","Parameter Store","Session Manager","Run Command","Maintenance Windows","Parameter Store is a central repository to store and retrieve application configuration."
"You need to grant temporary access to a system administrator to remotely access a Windows EC2 instance. Which AWS Systems Manager feature provides the most secure way to do this?","Session Manager","Run Command","Patch Manager","Automation","Session Manager provides secure, audited and controlled access without needing to open RDP ports."
"What is the main purpose of using AWS Systems Manager Quick Setup?","Automating the configuration of Systems Manager on your accounts.","Managing AWS IAM Roles and Policies.","Creating CloudWatch Dashboards.","Configuring VPC settings.","Quick Setup automates the configuration and deployment of Systems Manager across your AWS accounts and regions."
"Which AWS Systems Manager tool allows you to analyse operational data across your AWS environment to identify potential issues and improve operational efficiency?","OpsCenter","Run Command","Patch Manager","Session Manager","OpsCenter aggregates and standardises operational issues, allowing you to quickly diagnose and remediate problems."
"Which AWS Systems Manager feature could you use to automatically start and stop EC2 instances based on a schedule?","Automation","Run Command","Patch Manager","Session Manager","Automation can be used to define workflows that start and stop EC2 instances based on a schedule, reducing costs."
"You want to ensure that all your EC2 instances are compliant with a specific security policy. What Systems Manager capability can help you achieve this goal?","Compliance Manager","Inventory Manager","Patch Manager","Explorer","Compliance Manager helps you assess and enforce compliance policies across your AWS resources."
"With AWS Systems Manager, what are runbooks in the context of OpsCenter?","Predefined procedures for addressing operational issues.","Scripts used to start and stop EC2 instances.","Configuration files for your servers.","Backups of your databases.","Runbooks are predefined procedures that guide you through the process of resolving operational issues identified in OpsCenter."
"Which AWS Systems Manager feature enables you to manage operating system updates and patches for instances in hybrid environments (both AWS and on-premises)?","Patch Manager","State Manager","Automation","Run Command","Patch Manager provides a unified way to manage operating system updates and patches across both AWS and on-premises environments."
"You need to quickly diagnose and resolve an operational issue on a malfunctioning EC2 instance. Which Systems Manager feature would be most helpful in this scenario?","OpsCenter","Inventory Manager","Parameter Store","Maintenance Windows","OpsCenter provides a central location to view, investigate, and resolve operational issues."
"Which AWS Systems Manager capability can be used to automate the process of creating Amazon Machine Images (AMIs)?","Automation","Run Command","Patch Manager","Session Manager","Automation lets you define and execute workflows to automate the creation of AMIs, ensuring consistency and efficiency."
"Which Systems Manager feature helps you to visualise and analyse operational data, such as CPU utilisation and memory usage, across your fleet of managed instances?","Explorer","Run Command","Patch Manager","Session Manager","Explorer helps visualise and analyse operational data."
"What is the purpose of creating a maintenance window target in AWS Systems Manager?","To specify which instances a Maintenance Window will apply to.","To define the start and end times of the Maintenance Window.","To set up alerts for Maintenance Window events.","To define the actions to be performed during a Maintenance Window.","The target specifies which instances will be affected by the maintenance window tasks."
"You want to store and retrieve database connection strings in a secure and centralised manner. Which AWS Systems Manager feature is best suited for this purpose?","Parameter Store","Automation","Run Command","Fleet Manager","Parameter Store is designed for storing and retrieving configuration data, including sensitive information like database connection strings, securely."
"What is the primary purpose of the `AWS-RunShellScript` document in AWS Systems Manager Run Command?","To execute a shell script on a target instance.","To create a backup of a target instance.","To monitor the health of a target instance.","To manage user access control on a target instance.","`AWS-RunShellScript` allows you to execute shell scripts remotely on managed instances."
"Which AWS Systems Manager feature can assist with troubleshooting issues by allowing you to examine system logs, application logs, and other relevant data from a central location?","CloudWatch Integration with Systems Manager","Patch Manager","Session Manager","Inventory Manager","CloudWatch integration provides centralised log access for troubleshooting."
"You need to ensure that a custom security baseline is applied to all newly launched EC2 instances. What Systems Manager feature could you use to automate this process?","State Manager","Run Command","Patch Manager","Session Manager","State Manager can automatically apply configuration baselines to newly launched instances."
"Which AWS Systems Manager feature helps you track and manage changes to your infrastructure as code configurations stored in AWS CodeCommit?","Configuration Compliance","Session Manager","Run Command","Patch Manager","Configuration Compliance tracks changes to infrastructure as code configurations."
"You need to audit all user activity within your AWS environment, including actions performed using AWS Systems Manager. Which AWS service provides this capability?","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS IAM","AWS CloudTrail records API calls made to your AWS environment, including those made through Systems Manager."
"Which AWS Systems Manager feature can be used to deploy applications and software packages to your EC2 instances in a controlled and automated manner?","Distributor and Run Command","Patch Manager","Inventory Manager","Session Manager","Distributor and Run Command can be used to deploy applications and software packages."
"Which AWS Systems Manager tool allows you to interactively troubleshoot issues on a fleet of instances through a command line interface?","Session Manager","Run Command","Patch Manager","Automation","Session Manager provides interactive terminal access for troubleshooting."
"What is the purpose of AWS Systems Manager Maintenance Window's 'cutoff' parameter?","Specifies the amount of time after the scheduled end time that the system can continue to run tasks.","Specifies the minimum number of instances that must be available during the Maintenance Window.","Specifies the frequency at which tasks are executed during the Maintenance Window.","Specifies the maximum duration of a single task during the Maintenance Window.","The Cutoff dictates how long after the specified maintenance window that task execution can be extended to."
"You need to collect detailed performance metrics from your EC2 instances and store them in a time-series database for analysis. Which AWS service should you integrate with AWS Systems Manager?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch is the primary service for collecting and monitoring metrics from your AWS resources."
"What is the main benefit of using the 'AWS-UpdateSSMAgent' document in AWS Systems Manager Run Command?","Keeps the SSM Agent on your instances up to date.","Creates a backup of your instances.","Optimises the performance of your instances.","Manages user access control on your instances.","`AWS-UpdateSSMAgent` automates the process of updating the SSM Agent, ensuring it has the latest features and security patches."
"Which AWS Systems Manager feature would you use to automatically apply operating system security patches to your EC2 instances during a defined maintenance window?","Patch Manager","State Manager","Automation","Run Command","Patch Manager enables automated patching during scheduled maintenance windows."
"You want to create a centralised dashboard to monitor the status of your patch deployments across all your AWS regions. Which AWS Systems Manager feature should you use?","Compliance Summary within Patch Manager","Inventory Manager","Session Manager","Automation","The compliance summary within Patch Manager gives you a centralised dashboard."
"What is the purpose of defining a 'service role' when configuring AWS Systems Manager Automation?","Provides permissions for Automation to interact with other AWS services.","Defines the IAM role that users assume to access Automation.","Specifies the AWS region where Automation will run.","Defines the network configuration for Automation.","The service role grants Automation the necessary permissions to perform actions on your AWS resources."
"You want to distribute custom software packages to your managed instances without using the public internet. Which AWS Systems Manager feature allows you to achieve this?","Distributor with a private S3 bucket.","Patch Manager with a private repository.","Session Manager with a private endpoint.","Run Command with a local file.","Distributor, used with a private S3 bucket, enables distribution of software packages without internet access."
"Which AWS Systems Manager feature allows you to securely connect to your Linux and Windows EC2 instances without opening inbound ports in your security groups?","Session Manager","Run Command","Patch Manager","Automation","Session Manager removes the need to open inbound ports."
"What is the purpose of using 'Target IDs' when running an AWS Systems Manager Run Command?","To specify which instances the command should be executed on.","To define the IAM roles that have access to the command.","To set the execution timeout for the command.","To specify the AWS region where the command should be executed.","Target IDs allow you to specify the particular EC2 instances you are aiming the Systems Manager command for."
"You are implementing an approval workflow for patching your EC2 instances in AWS Systems Manager. Which Patch Manager feature allows you to control when patches are deployed based on their severity level?","Approval rules","Baseline","Patch groups","Global filters","Approval rules allow you to configure approval workflows based on patch severity and other criteria."
"Which AWS Systems Manager feature can you use to schedule a script to run on a specific instance every day at midnight?","Maintenance Windows","State Manager","Run Command with a scheduled task","Automation with a CloudWatch Event","Maintenance windows let you schedule scripts or actions."
"What is the advantage of using AWS Systems Manager Automation over manually performing tasks on your AWS resources?","Automation reduces human error and improves consistency.","Automation provides real-time monitoring of your infrastructure.","Automation automatically scales your AWS resources.","Automation encrypts your data at rest.","Automation reduces the risk of human error and ensures consistent execution of tasks."
"You need to create a snapshot of an EBS volume and copy it to another AWS region using AWS Systems Manager. Which Systems Manager feature allows you to orchestrate this process?","Automation","Run Command","Patch Manager","Session Manager","Automation allows you to orchestrate a multi-step process."
"Which AWS Systems Manager capability allows you to automate operational tasks across your AWS resources?","Automation","Inventory","Session Manager","Parameter Store","Automation is the Systems Manager capability designed for automating operational tasks, such as patching, configuration changes, and instance restarts."
"Which AWS Systems Manager feature provides a centralised repository for storing and managing configuration data, such as passwords and database connection strings?","Parameter Store","OpsCenter","State Manager","Patch Manager","Parameter Store provides secure, hierarchical storage for configuration data and secrets."
"What AWS Systems Manager feature enables you to remotely and securely manage your EC2 instances without opening inbound ports?","Session Manager","Distributor","Compliance","Change Manager","Session Manager allows secure remote access to EC2 instances through the AWS Management Console, AWS CLI, or SSH/SCP, without needing to open inbound ports."
"Which AWS Systems Manager service helps you to collect software inventory, operating system details, and other metadata from your managed instances?","Inventory","Run Command","Maintenance Windows","Incident Manager","Inventory collects detailed information about your managed instances, allowing you to track software, hardware, and configurations."
"Which AWS Systems Manager service allows you to schedule recurring tasks on your managed instances?","Maintenance Windows","Patch Manager","Explorer","Quick Setup","Maintenance Windows allows you to define a schedule for recurring tasks, such as patching or software updates, to minimise disruptions."
"Which AWS Systems Manager feature helps you track and manage changes to your infrastructure configuration over time?","Change Manager","Compliance","Automation","Distributor","Change Manager helps you track, review, approve, and implement operational changes to your AWS resources."
"Which AWS Systems Manager service enables you to package and distribute software to your managed instances?","Distributor","Run Command","Inventory","Automation","Distributor allows you to package and distribute software packages, such as installers and applications, to your managed instances."
"Which AWS Systems Manager capability helps you assess the compliance status of your managed instances against defined standards and policies?","Compliance","Parameter Store","Session Manager","Change Manager","Compliance helps you evaluate the compliance status of your managed instances against your organisation's security and configuration policies."
"What is the primary purpose of AWS Systems Manager OpsCenter?","Centralise operational issues and provide resolution tools","Manage EC2 instance types","Manage user IAM policies","Manage DNS records","OpsCenter provides a central location where operations engineers and IT professionals can view, investigate, and resolve operational issues related to their AWS resources."
"Which AWS Systems Manager feature enables you to execute commands on multiple instances simultaneously?","Run Command","Session Manager","Parameter Store","Change Manager","Run Command allows you to execute commands on one or more managed instances at the same time."
"Which AWS Systems Manager service is used to manage operating system and application patching for managed instances?","Patch Manager","Run Command","Automation","Session Manager","Patch Manager automates the process of patching operating systems and applications on your managed instances."
"What is the purpose of the AWS Systems Manager State Manager feature?","Enforce desired configurations on managed instances","Store secret key values","Manage user access","Monitor CPU utilisation","State Manager allows you to define and maintain a desired state for your managed instances."
"Which AWS Systems Manager feature provides a dashboard for viewing operational data across your AWS environment?","Explorer","OpsCenter","Change Manager","Parameter Store","Explorer provides a customisable dashboard to view operational data, such as inventory, compliance, and patch status."
"Which AWS Systems Manager capability is used to collect and analyse operational data, such as logs and metrics, from your managed instances?","CloudWatch Integration","Automation","Session Manager","Parameter Store","CloudWatch Integration allows you to forward Systems Manager data, such as Run Command logs and Inventory data, to CloudWatch for analysis and monitoring."
"Which of the following AWS Systems Manager services is designed to provide secure and auditable remote access to your EC2 instances?","Session Manager","Run Command","Parameter Store","Automation","Session Manager provides secure remote access to EC2 instances without exposing inbound ports, and all actions are audited."
"Which AWS Systems Manager feature enables you to create a Quick Setup configuration?","Quick Setup","Automation","Run Command","Parameter Store","Quick Setup allows you to quickly configure common Systems Manager features across your AWS account."
"Which AWS Systems Manager service helps you manage and automate the process of updating your Amazon Machine Images (AMIs)?","Image Builder","Distributor","Session Manager","Parameter Store","Image Builder allows you to automate the creation and customisation of AMIs."
"Which AWS Systems Manager feature allows you to define approval workflows for changes to your infrastructure?","Change Manager","Compliance","Automation","Distributor","Change Manager integrates with change request systems to manage and automate change approvals."
"Which AWS Systems Manager service provides a central place to view, investigate, and resolve operational issues?","OpsCenter","Run Command","Automation","Parameter Store","OpsCenter provides a centralised view of operational issues and integrates with other Systems Manager features for remediation."
"What is the purpose of the AWS Systems Manager Document (SSM Document)?","Defines the actions that Systems Manager performs on managed instances","Stores instance metadata","Manages IAM policies","Manages CloudWatch alarms","SSM Documents define the actions that Systems Manager takes on managed instances, such as executing commands, installing software, or applying patches."
"You need to grant an IAM role to Systems Manager to manage an EC2 instance. What IAM policy should be attached to the role?","AmazonSSMManagedInstanceCore","AdministratorAccess","ReadOnlyAccess","IAMFullAccess","AmazonSSMManagedInstanceCore allows Systems Manager to perform core management tasks on the instance."
"Which AWS Systems Manager service can be used to manage hybrid environments including on-premises servers?","Systems Manager Agent","EC2Config","CloudWatch Agent","Route 53 Agent","The Systems Manager Agent can be installed on on-premises servers to manage them through Systems Manager."
"You want to execute a script on a fleet of EC2 instances. Which Systems Manager feature should you use?","Run Command","Inventory","Patch Manager","Parameter Store","Run Command enables you to remotely and securely execute commands and scripts on managed instances."
"Which AWS Systems Manager feature can you use to centrally manage the configuration of your EC2 instances, ensuring they are in the desired state?","State Manager","Run Command","Patch Manager","Parameter Store","State Manager allows you to define, schedule, and maintain a desired state configuration for your managed instances."
"Which AWS Systems Manager feature helps you maintain consistent operating system and application configurations on your instances by automating configuration tasks?","Configuration Compliance","Inventory","Session Manager","Parameter Store","Configuration Compliance helps ensure your instances adhere to your desired configuration state and policies."
"What type of document is used in AWS Systems Manager Automation to define a sequence of actions to perform?","Automation document","JSON document","YAML document","XML document","Automation documents are written in YAML or JSON and define the steps of an automation workflow."
"Which AWS Systems Manager service allows you to create, maintain, and distribute software packages for managed instances?","Distributor","Run Command","Automation","Parameter Store","Distributor simplifies the process of finding, installing, and managing software packages on your managed instances."
"You need to store and retrieve sensitive information, such as database passwords, in a secure manner. Which AWS Systems Manager service should you use?","Parameter Store (Secure String)","Run Command","Inventory","Session Manager","Parameter Store, specifically using the Secure String data type, allows you to store and retrieve sensitive data securely."
"Which AWS Systems Manager service can you use to automate the process of creating golden images for your EC2 instances?","Image Builder","Run Command","Automation","Parameter Store","Image Builder simplifies and automates the process of building and customising golden images for your EC2 instances."
"You want to ensure that your EC2 instances are regularly scanned for missing patches and vulnerabilities. Which AWS Systems Manager service should you use?","Patch Manager","Run Command","Automation","Parameter Store","Patch Manager enables you to automate the process of patching your managed instances and checking for missing patches."
"Which AWS Systems Manager service can be used to provide remote access to instances behind a firewall, without needing to open inbound ports?","Session Manager","Run Command","Automation","Parameter Store","Session Manager allows you to securely access your EC2 instances without the need to open inbound ports, even if they are behind a firewall."
"You need to identify the software installed on your EC2 instances. Which AWS Systems Manager service should you use?","Inventory","Run Command","Automation","Parameter Store","Inventory collects information about the software, hardware, and configurations of your managed instances."
"You want to define and enforce a specific configuration baseline for your EC2 instances. Which AWS Systems Manager feature should you use?","Configuration Compliance","Inventory","Patch Manager","Parameter Store","Configuration Compliance helps you ensure that your instances adhere to a defined configuration baseline."
"Which AWS Systems Manager feature allows you to schedule and manage maintenance windows for your EC2 instances?","Maintenance Windows","Run Command","Automation","Parameter Store","Maintenance Windows allows you to define time windows for performing maintenance tasks on your instances."
"You need to ensure that all your EC2 instances have the latest version of the AWS Systems Manager Agent installed. Which AWS Systems Manager service can help you achieve this?","Quick Setup","Run Command","Automation","Parameter Store","Quick Setup provides a simple way to configure and update the AWS Systems Manager Agent across your instances."
"Which of the following is a key benefit of using AWS Systems Manager Parameter Store?","Centralised configuration management","Automated patching","Remote access to instances","Infrastructure monitoring","Parameter Store provides a centralised location to store and manage configuration data, including secrets."
"You want to create a workflow that automatically restarts a service on your EC2 instances if it becomes unresponsive. Which AWS Systems Manager service should you use?","Automation","Run Command","Inventory","Parameter Store","Automation allows you to create workflows that automate tasks, such as restarting services, based on certain conditions."
"Which of the following AWS Systems Manager capabilities helps you to discover and act upon operational issues in your AWS environment?","OpsCenter","Run Command","Automation","Parameter Store","OpsCenter provides a central view of operational issues and allows you to take action to resolve them."
"You need to distribute a custom script to all your EC2 instances and execute it. Which AWS Systems Manager feature is the most suitable for this task?","Run Command","Inventory","Automation","Parameter Store","Run Command allows you to run commands and scripts on managed instances."
"Which AWS Systems Manager service enables you to manage your on-premises servers as if they were EC2 instances?","Systems Manager Agent","EC2Config","CloudWatch Agent","Route 53 Agent","The Systems Manager Agent must be installed on the on-premises servers to be managed by Systems Manager."
"Which AWS Systems Manager feature helps you to identify and remediate non-compliant instances in your environment?","Compliance","Run Command","Automation","Parameter Store","Compliance allows you to assess the compliance status of your instances against defined standards."
"What is the purpose of AWS Systems Manager Inventory?","Collect metadata about your instances","Schedule maintenance windows","Automate patching","Store sensitive data","Inventory collects information about the software, hardware, and configurations of your managed instances."
"Which AWS Systems Manager service allows you to create AMIs based on a custom recipe?","Image Builder","Run Command","Automation","Parameter Store","Image Builder allows you to automate the creation of customised AMIs."
"What is the main function of AWS Systems Manager Distributor?","Software package distribution","Remote command execution","Parameter management","Inventory collection","Distributor allows you to distribute software packages to your managed instances."
"Which AWS Systems Manager service is ideal for securely accessing EC2 instances without the need for SSH keys?","Session Manager","Run Command","Automation","Parameter Store","Session Manager provides secure and auditable instance access without exposing inbound ports or managing SSH keys."
"You want to ensure that all EC2 instances in a specific AWS account have a certain configuration setting. Which AWS Systems Manager feature can help you achieve this?","State Manager","Run Command","Automation","Parameter Store","State Manager allows you to define and enforce a desired configuration state for your instances."
"What AWS Systems Manager feature allows you to create a centralised dashboard to monitor operational data?","Explorer","Run Command","Automation","Parameter Store","Explorer provides a customisable dashboard for viewing operational data across your AWS environment."
"Which AWS Systems Manager service provides the capability to execute PowerShell commands on Windows EC2 instances?","Run Command","Inventory","Automation","Parameter Store","Run Command supports executing PowerShell commands on Windows instances."
"What is the purpose of the AWS Systems Manager SSM Agent?","Allows Systems Manager to interact with instances","Manages IAM roles","Stores parameters","Manages CloudWatch Metrics","The SSM Agent must be installed on instances to enable Systems Manager to manage them."
"What is the primary function of AWS Systems Manager Parameter Store?","Secure, hierarchical storage for configuration data management and secrets management.","Monitoring CPU usage of EC2 instances.","Managing user access to S3 buckets.","Deploying application code to Lambda functions.","Systems Manager Parameter Store provides a centralised location to store and manage configuration data and secrets."
"Which AWS Systems Manager capability allows you to automate operational tasks across your AWS resources?","Automation","Inventory","Session Manager","Patch Manager","Systems Manager Automation allows you to define and execute automated workflows to manage and configure your AWS resources."
"Which AWS Systems Manager feature allows you to securely manage EC2 instances without opening inbound ports?","Session Manager","Patch Manager","Run Command","State Manager","Session Manager provides secure shell access to your EC2 instances without requiring SSH or opening inbound ports."
"What AWS Systems Manager component collects software inventory, OS configuration, and application details from your managed instances?","Inventory","Compliance","Maintenance Windows","Distributor","Inventory collects detailed information about the software and configurations on your managed instances, providing visibility into your environment."
"Which AWS Systems Manager capability is used to automate the process of applying patches to your managed instances?","Patch Manager","Run Command","Configuration Compliance","Change Manager","Patch Manager automates the process of patching operating systems and applications on your managed instances to maintain security and compliance."
"What is the purpose of AWS Systems Manager State Manager?","To enforce desired configurations on your instances over time.","To manage user access to AWS resources.","To monitor network traffic between EC2 instances.","To back up EC2 instance volumes.","State Manager allows you to define and maintain consistent configurations on your instances by applying policies over time."
"You need to execute a command on a large number of EC2 instances simultaneously. Which AWS Systems Manager feature should you use?","Run Command","Maintenance Windows","Distributor","Change Manager","Run Command allows you to execute commands on a group of managed instances concurrently."
"What is the function of AWS Systems Manager Maintenance Windows?","To schedule periods of time to perform potentially disruptive tasks on your instances.","To create backups of EC2 instances.","To manage IAM roles.","To monitor application performance.","Maintenance Windows allow you to schedule specific time periods for maintenance activities like patching and software updates to minimise impact."
"Which AWS Systems Manager feature can be used to distribute software packages to your managed instances?","Distributor","Session Manager","Automation","Explorer","Distributor allows you to package and distribute software to your managed instances in a controlled manner."
"You want to track the compliance status of your instances against a defined security baseline. Which AWS Systems Manager feature should you use?","Compliance","Inventory","Patch Manager","Automation","Compliance allows you to evaluate your instances against defined security standards and track their compliance status."
"What is the purpose of AWS Systems Manager Explorer?","To provide a dashboard view of operational data across your AWS environment.","To manage encryption keys.","To monitor database performance.","To configure load balancers.","Explorer provides a centralised dashboard to view operational data and insights across your AWS resources."
"Which AWS Systems Manager capability provides a centralised service to manage changes to your application and infrastructure securely?","Change Manager","Automation","Run Command","Session Manager","Change Manager provides a framework for requesting, approving, implementing, and reporting on operational changes."
"You need to grant a user permission to access AWS Systems Manager Session Manager. Which AWS IAM policy should you use?","AmazonSSMManagedInstanceCore","AmazonEC2FullAccess","IAMUserChangePassword","S3FullAccess","The AmazonSSMManagedInstanceCore policy provides the necessary permissions for Session Manager access."
"What type of data can be stored in AWS Systems Manager Parameter Store?","Passwords, database connection strings, API keys.","EC2 instance IDs.","CloudWatch metrics.","S3 bucket names.","Parameter Store can securely store sensitive data like passwords, connection strings, and API keys."
"What is the maximum size of a parameter value that can be stored in AWS Systems Manager Parameter Store with the Standard tier?","4KB","1KB","8KB","10KB","The Standard tier of Parameter Store allows you to store parameter values up to 4KB in size."
"Which AWS Systems Manager Parameter Store parameter type is used to store sensitive data in encrypted form?","SecureString","StringList","String","Text","SecureString is used to store sensitive data, encrypting the parameter value for added security."
"You want to organise your parameters in AWS Systems Manager Parameter Store for easier management. How can you achieve this?","Using hierarchical paths","Using tags","Using aliases","Using versions","Parameter Store allows you to organise parameters using hierarchical paths, making them easier to manage and navigate."
"Which AWS Systems Manager feature can be used to trigger an action when a specific parameter value changes in Parameter Store?","CloudWatch Events","CloudTrail","Config","Trusted Advisor","CloudWatch Events can be configured to trigger an action when a parameter value changes in Parameter Store, enabling automated responses."
"Which AWS Systems Manager feature helps you to create, manage, and deploy operating system images to EC2 instances?","Image Builder","Patch Manager","Automation","Run Command","Image Builder automates the process of creating and customising golden images for your EC2 instances."
"You want to create a custom AWS Systems Manager document (SSM document). What format is typically used for these documents?","YAML or JSON","XML","CSV","HTML","SSM documents are typically written in YAML or JSON format to define the steps and parameters for automation tasks."
"Which AWS Systems Manager feature can be used to create a single package containing all dependencies and configuration required to deploy applications across your hybrid environment?","Package Manager","Configuration Manager","Service Manager","Dependency Manager","Distributor helps you create a single package which contains all the dependencies and configuration to deploy the applications in the hybrid environment."
"A user needs to restart a service on a group of EC2 instances using AWS Systems Manager. Which SSM document type is most suitable?","AWS::EC2::Instance","AWS::SSM::Document","AWS::CloudFormation::Stack","AWS::S3::Bucket","AWS::SSM::Document is the document type to be used to manage the managed instances."
"Which AWS Systems Manager capability allows you to centrally manage hybrid environments consisting of on-premises servers and virtual machines?","Hybrid Activations","Cloud Directory","Cognito Federated Identities","Direct Connect Gateway","Hybrid Activations is the Systems Manager feature that helps in managing hybrid environments."
"You are using AWS Systems Manager Automation and need to pass data between different steps in your automation workflow. How can you achieve this?","Using SSM Parameters","Using Environment Variables","Using Lambda Functions","Using SNS Topics","You can pass data between different steps using SSM Parameters"
"Which AWS Systems Manager capability can be used to discover and manage vulnerabilities in your EC2 instances?","Inspector","Shield","GuardDuty","Detective","Inspector can be integrated with AWS System Manager to discover vulnerabilities in your EC2 instances."
"You want to ensure that your AWS Systems Manager Automation workflows are executed with the appropriate permissions. What IAM role should you use?","Automation Assume Role","Instance Profile Role","Service Linked Role","Admin Role","The Automation Assume Role is the role that should be used with appropriate permissions."
"Which AWS Systems Manager feature provides pre-built automation workflows for common operational tasks?","Automation Runbooks","Quick Starts","Solutions","CloudFormation Templates","Automation Runbooks provide pre-built Automation Workflows for common Operational Tasks."
"You need to rotate API keys stored in AWS Systems Manager Parameter Store on a regular basis. Which AWS service can be used to automate this process?","Lambda Functions","IAM","CloudTrail","CloudWatch Logs","You can use Lambda Functions to rotate your API Keys, it's the best practice to do so."
"You are using AWS Systems Manager Patch Manager and want to exclude a specific patch from being installed on your instances. How can you achieve this?","Using Patch Baselines","Using Maintenance Windows","Using Compliance Reports","Using Inventory","You can exclude a specific patch using Patch Baselines."
"Which AWS Systems Manager feature allows you to define and maintain desired configuration states for your AWS resources using infrastructure as code?","AWS CloudFormation","AWS Config","AWS Trusted Advisor","AWS Control Tower","AWS CloudFormation is used to define and maintain desired configuration states."
"You need to configure a custom CloudWatch metric filter to monitor specific events in AWS Systems Manager. Where can you find the relevant event logs?","CloudTrail Logs","VPC Flow Logs","S3 Access Logs","Route 53 Query Logs","You can find the relevant event logs for the Cloudwatch metric filters in Cloudtrail logs."
"What is the purpose of the AWS Systems Manager Inventory Association feature?","To automatically update instance inventory at scheduled intervals.","To associate instances with specific IAM roles.","To track changes to CloudFormation stacks.","To manage tags on EC2 instances.","Inventory Association automatically updates the inventory at the scheduled intervals."
"Which AWS Systems Manager capability allows you to remotely access and troubleshoot EC2 instances running in a private subnet without exposing them to the internet?","Session Manager","Run Command","Patch Manager","State Manager","Session Manager is used to remotely access and troubleshoot instances in private subnets."
"You are using AWS Systems Manager Automation and need to debug a failed automation execution. Which AWS service can be used to view the execution logs?","CloudWatch Logs","CloudTrail","VPC Flow Logs","S3 Access Logs","CloudWatch Logs will provide you with the execution logs, which will help you debug."
"Which AWS Systems Manager feature provides a centralised view of security alerts and findings across your AWS environment?","Security Hub","Inspector","GuardDuty","CloudTrail","Security Hub gives you centralised view of Security Alerts"
"You want to automate the process of updating your Amazon Machine Images (AMIs) with the latest security patches. Which AWS service should you use in conjunction with AWS Systems Manager?","AWS Image Builder","AWS Lambda","AWS CloudFormation","AWS Auto Scaling","AWS Image Builder automates the customisation of images"
"Which AWS Systems Manager feature allows you to define and manage reusable configurations and policies for your AWS resources?","AWS CloudFormation StackSets","AWS Config Rules","AWS Service Catalog","AWS Control Tower","AWS CloudFormation StackSets enables you to manage the reusable configurations"
"Which AWS Systems Manager feature allows you to implement a rollback strategy in case of a failed deployment?","Automation Rollback","Patch Manager Undo","Run Command Revert","Session Manager Termination","Automation Rollback lets you implement a rollback strategy in case of a failed deployment."
"You are using AWS Systems Manager Patch Manager and need to apply patches to instances based on their operating system type. How can you achieve this?","Using Patch Baselines with filters","Using Maintenance Windows with targets","Using Compliance Reports with rules","Using Inventory with groups","You can use Patch Baselines with filters to apply patches based on OS"
"Which AWS Systems Manager capability allows you to manage and monitor the lifecycle of software packages, including versioning and deployment?","AWS CodePipeline","AWS CodeDeploy","AWS OpsWorks","AWS Lambda","Distributor helps to manage and monitor the lifecycle of software packages"
"You need to create a custom AWS Systems Manager Automation document to automate the process of backing up your Amazon RDS databases. Which AWS service should you use within the automation document?","AWS Backup","AWS Data Pipeline","AWS DMS","AWS Storage Gateway","AWS Backup would be the best choice to back up your RDS databases."
"Which AWS Systems Manager feature provides a centralised repository for storing and managing your Infrastructure as Code (IaC) templates?","AWS CodeCommit","AWS CodeArtifact","AWS Service Catalog","AWS Systems Manager Explorer","AWS CodeCommit provides repository for Infrastructure as Code templates."
"You are using AWS Systems Manager Change Manager and need to integrate it with your existing IT service management (ITSM) system. Which AWS service can be used to facilitate this integration?","AWS Service Catalog","AWS Chatbot","AWS SNS","AWS SQS","AWS Service Catalog facilitates the integration with the IT system."
"What is the main benefit of integrating AWS Systems Manager with AWS Organizations?","Centralised management of resources across multiple AWS accounts.","Automated cost allocation across AWS accounts.","Enhanced security auditing across AWS accounts.","Simplified access control management across AWS accounts.","Organizations provides centralised management of resources across multiple accounts"
"You need to troubleshoot a network connectivity issue on an EC2 instance using AWS Systems Manager. Which SSM document type would be most appropriate?","AWS::EC2::Vpc","AWS::EC2::SecurityGroup","AWS::EC2::RouteTable","AWS::EC2::Instance","AWS::EC2::Instance would be most appropriate to resolve network issues."
"Which of the following is the MOST secure way to store database passwords using AWS Systems Manager?","Using SecureString parameters in Parameter Store with KMS encryption.","Storing them in plaintext in Parameter Store.","Storing them in environment variables on EC2 instances.","Hardcoding them in application configuration files.","SecureString is the most secure way to store DB Passwords"
"A company wants to automate the process of starting and stopping EC2 instances on a schedule using AWS Systems Manager. Which feature should they use?","Automation using Scheduled Events.","Patch Manager with Scheduled Tasks.","Run Command with Cron Expressions.","Inventory with Scheduled Scans.","Automation using scheduled events helps automate stopping and starting of EC2 instances"
"You want to create a custom AWS Systems Manager Quick Setup configuration to automate the deployment of common services and features. Which AWS service is used to define the configuration?","AWS CloudFormation","AWS Config","AWS OpsWorks","AWS CodeDeploy","AWS CloudFormation helps create automation for common deployments."
"Which AWS Systems Manager capability provides a centralised dashboard for monitoring the operational health and performance of your AWS resources, including EC2 instances, RDS databases, and Lambda functions?","AWS CloudWatch Dashboards","AWS Trusted Advisor","AWS Personal Health Dashboard","AWS Systems Manager Explorer","Systems Manager Explorer provides a central dashboard for monitoring the operational health."
"In AWS Systems Manager, what is the primary function of Parameter Store?","Securely store configuration data and secrets","Manage EC2 instance patching","Automate infrastructure provisioning","Monitor application performance","Parameter Store provides secure, centralised storage and management of configuration data and secrets. You can store values as plain text or encrypted data."
"What AWS Systems Manager capability allows you to automate the execution of tasks on a schedule?","Maintenance Windows","State Manager","Automation","Session Manager","Maintenance Windows allow you to define recurring schedules for executing tasks, such as patching or updating software, on your instances."
"Which AWS Systems Manager feature can be used to establish secure shell (SSH) or Remote Desktop Protocol (RDP) connections to your EC2 instances without opening inbound ports?","Session Manager","Patch Manager","Run Command","Inventory","Session Manager provides secure and auditable instance management without the need to open inbound ports, reducing the attack surface."
"What is the purpose of the AWS Systems Manager State Manager?","To enforce and maintain a desired state for your instances","To manage user access permissions","To collect inventory data","To automate the creation of EC2 instances","State Manager helps you maintain a consistent configuration state across your instances by defining and applying configuration policies."
"Which AWS Systems Manager feature is used to collect software inventory, operating system details, and other metadata from your instances?","Inventory","Compliance","Distributor","Explorer","Inventory collects information about your instances, including installed software, operating systems, and other metadata, providing a central view of your infrastructure."
"Which AWS Systems Manager feature would you use to remotely execute commands on a large group of EC2 instances simultaneously?","Run Command","Automation","Parameter Store","Change Manager","Run Command enables you to execute commands on one or more instances simultaneously, simplifying administration tasks."
"What is the main benefit of using AWS Systems Manager Patch Manager?","Automating the process of patching operating systems and applications","Managing AWS Identity and Access Management (IAM) roles","Creating custom CloudWatch dashboards","Automatically scaling EC2 instances","Patch Manager automates the process of patching operating systems and applications on your instances, ensuring they are up to date with the latest security patches."
"Which AWS Systems Manager feature helps you define and automate the steps required to perform tasks such as creating AMIs or patching instances?","Automation","Run Command","Session Manager","Compliance","Automation allows you to define workflows and automate tasks, such as creating AMIs or patching instances, using pre-defined or custom documents."
"How does AWS Systems Manager Parameter Store enhance security when storing sensitive information like passwords?","By offering encryption at rest and in transit using KMS","By automatically rotating passwords","By limiting access to specific IP addresses","By storing passwords in plain text within a secure VPC","Parameter Store offers encryption at rest and in transit using KMS, allowing you to securely store sensitive information like passwords."
"Which AWS Systems Manager feature provides a centralised dashboard for viewing operational data from various AWS services and resources?","Explorer","Compliance","Automation","Distributor","Explorer provides a centralised dashboard for viewing operational data from various AWS services and resources, giving you a comprehensive view of your environment."
"What is the purpose of AWS Systems Manager Distributor?","To securely store and distribute software packages","To manage EC2 instance patching schedules","To automate infrastructure deployments","To monitor application performance","Distributor enables you to securely store and distribute software packages to your instances."
"Which AWS Systems Manager feature allows you to track compliance status of your instances against defined security policies and standards?","Compliance","Inventory","Patch Manager","Automation","Compliance allows you to track the compliance status of your instances against defined security policies and standards, ensuring they meet your security requirements."
"You need to grant a user permission to access AWS Systems Manager Parameter Store. Which AWS Identity and Access Management (IAM) action would you typically include in the user's policy?","ssm:GetParameter","ec2:DescribeInstances","s3:GetObject","cloudwatch:GetMetricData","The `ssm:GetParameter` action is required to grant a user permission to retrieve parameters from Parameter Store."
"What is the purpose of the AWS Systems Manager Change Manager?","To manage and approve changes to your infrastructure","To automate EC2 instance scaling","To monitor application logs in real time","To manage user access policies across all AWS services","Change Manager enables you to manage and approve changes to your infrastructure, providing a centralised workflow for change management."
"How can you use AWS Systems Manager to ensure that all your EC2 instances have a specific configuration file?","Using State Manager to define and enforce the desired configuration","Using Patch Manager to install the configuration file","Using Run Command to manually copy the file to each instance","Using Inventory to verify the presence of the file","State Manager can be used to define and enforce the desired configuration state, ensuring all instances have the specific configuration file."
"Which AWS Systems Manager service can be used to automate the creation, update, and deletion of AWS resources in a repeatable and predictable way?","Automation","Run Command","Session Manager","Inventory","Automation allows you to define workflows and automate the creation, update, and deletion of AWS resources, providing a repeatable and predictable process."
"You want to use AWS Systems Manager to automatically update the Amazon CloudWatch agent on your EC2 instances. Which feature should you use?","Patch Manager","Inventory","Session Manager","Run Command","Patch Manager can be used to manage and update software, including the Amazon CloudWatch agent, on your EC2 instances."
"What is the primary use case for AWS Systems Manager Automation Documents?","Defining and executing automation workflows","Storing sensitive configuration data","Managing EC2 instance patching","Monitoring application performance","Automation Documents define the steps and actions to be executed as part of an automation workflow."
"Which of the following AWS Systems Manager features provides a secure and auditable way to access your EC2 instances without using SSH keys?","Session Manager","Run Command","Patch Manager","Inventory","Session Manager allows you to securely connect to your instances without the need for SSH keys, enhancing security and auditability."
"How does AWS Systems Manager help with compliance in an AWS environment?","By providing features to track compliance status against defined policies","By automatically encrypting all data at rest and in transit","By managing user access permissions across all AWS services","By automatically scaling EC2 instances based on demand","Systems Manager provides features like Compliance to track the compliance status of your instances against defined policies and standards."
"You need to store a database password securely in AWS and retrieve it from an application running on an EC2 instance. Which AWS Systems Manager feature is best suited for this?","Parameter Store with KMS encryption","Run Command with encrypted parameters","Patch Manager with secure parameters","Inventory with secret storage","Parameter Store with KMS encryption allows you to securely store and retrieve sensitive information like database passwords."
"Which AWS Systems Manager feature would you use to collect information about the software installed on your EC2 instances?","Inventory","Patch Manager","Compliance","Automation","Inventory collects information about the software, operating system, and other metadata installed on your instances."
"How can you use AWS Systems Manager Run Command to troubleshoot an issue on an EC2 instance?","By executing commands remotely to diagnose the problem","By automatically rebooting the instance","By creating a snapshot of the instance's disk","By monitoring the instance's CPU utilisation","Run Command allows you to remotely execute commands on the instance to diagnose and troubleshoot issues."
"What is the purpose of AWS Systems Manager Maintenance Windows in the context of patching?","To schedule patching activities during specific time windows","To automatically approve all patches","To prevent patching from occurring","To encrypt patches before deployment","Maintenance Windows allow you to schedule patching activities during specific time windows to minimise disruption to your applications."
"Which AWS Systems Manager feature helps you distribute and install software packages on your EC2 instances without requiring direct access to the instances?","Distributor","Automation","Run Command","Session Manager","Distributor allows you to securely store and distribute software packages, making it easier to deploy and manage software on your instances."
"You want to ensure that all your EC2 instances are running the latest version of a specific software package. Which AWS Systems Manager feature can help you achieve this?","Patch Manager","Inventory","Session Manager","Run Command","Patch Manager can be used to automatically patch and update software packages on your instances, ensuring they are running the latest versions."
"How does AWS Systems Manager contribute to improving the operational efficiency of managing EC2 instances?","By providing centralised management and automation capabilities","By automatically scaling EC2 instances based on demand","By automatically backing up EC2 instances","By providing real-time monitoring of EC2 instances","Systems Manager offers centralised management and automation capabilities, making it easier to manage and maintain your EC2 instances at scale."
"Which AWS Systems Manager feature provides a way to manage changes to your AWS infrastructure in a controlled and auditable manner?","Change Manager","Automation","Run Command","Inventory","Change Manager provides a workflow for requesting, reviewing, approving, and implementing changes to your AWS infrastructure."
"You are using AWS Systems Manager Parameter Store to store sensitive data. What is the recommended practice for granting access to these parameters?","Grant access to specific parameters using IAM policies","Store all parameters in a public S3 bucket","Grant full Systems Manager access to all users","Embed the parameters directly in your application code","Grant access to specific parameters using IAM policies to follow the principle of least privilege and enhance security."
"Which AWS Systems Manager feature can be used to centrally manage and deploy AWS Systems Manager Agent (SSM Agent) updates?","Patch Manager","Distributor","Automation","Run Command","Distributor simplifies the process of updating the SSM Agent on your instances, ensuring they have the latest features and security updates."
"What is the role of the AWS Systems Manager Compliance feature in maintaining a secure environment?","It assesses the compliance status of your instances against defined policies","It automatically encrypts all data stored on your instances","It prevents unauthorised access to your EC2 instances","It automatically patches your EC2 instances","The Compliance feature helps you assess and track the compliance status of your instances against defined policies and standards."
"Which of the following is a key benefit of using AWS Systems Manager Session Manager over traditional SSH?","Session Manager provides secure and auditable access without open inbound ports","Session Manager automatically patches your EC2 instances","Session Manager offers real-time monitoring of your applications","Session Manager provides centralised management of your AWS costs","Session Manager eliminates the need for open inbound ports, reducing the attack surface and improving security."
"You are automating the deployment of a new application using AWS Systems Manager Automation. What is the first step in this process?","Creating an Automation Document to define the workflow","Creating an IAM role with appropriate permissions","Configuring Parameter Store to store application settings","Launching the EC2 instances for the application","The first step is to create an Automation Document, which defines the steps and actions to be performed as part of the deployment process."
"Which AWS Systems Manager feature would you use to create a report showing the number of EC2 instances running a specific operating system?","Inventory","Patch Manager","Compliance","Automation","Inventory collects information about the operating system on your instances, which can be used to generate a report."
"How can you integrate AWS Systems Manager with other AWS services to enhance your operational workflows?","By integrating with services like CloudWatch, CloudTrail, and IAM","By replacing all other AWS management tools","By running Systems Manager on-premises","By limiting access to other AWS services","Systems Manager integrates with various AWS services, such as CloudWatch, CloudTrail, and IAM, to provide a more comprehensive management and monitoring solution."
"You need to ensure that your EC2 instances have the latest version of the AWS CLI installed. Which AWS Systems Manager feature can help you automate this task?","Patch Manager","Distributor","Automation","Run Command","Distributor allows you to store and distribute software packages, including the AWS CLI, to your instances."
"What is the purpose of using AWS Systems Manager Parameters with the 'SecureString' data type?","To encrypt sensitive data, such as passwords, using KMS","To store large binary files in Parameter Store","To store plain text configuration data","To automatically rotate passwords stored in Parameter Store","The 'SecureString' data type encrypts sensitive data using KMS, protecting it from unauthorised access."
"How can you use AWS Systems Manager Explorer to gain insights into your AWS environment?","By visualising operational data from various AWS services in a single dashboard","By automatically patching your EC2 instances","By managing user access permissions across all AWS services","By automatically scaling EC2 instances based on demand","Explorer provides a centralised dashboard for visualising operational data, enabling you to gain insights into the overall health and performance of your AWS environment."
"Which AWS Systems Manager feature would be most useful for centrally managing a fleet of EC2 instances that are spread across multiple AWS accounts?","Systems Manager supports cross-account management through delegated access","Run Command","Session Manager","Inventory","Systems Manager supports cross-account management through delegated access, allowing you to centrally manage instances across multiple AWS accounts."
"You want to automate the process of creating a backup of your EC2 instances before applying a software update. Which AWS Systems Manager feature is best suited for this?","Automation","Run Command","Patch Manager","Inventory","Automation allows you to define a workflow that includes creating a backup of your instances before applying a software update."
"Which AWS Systems Manager feature enables you to create and manage a centralised repository of software packages that can be deployed to your instances?","Distributor","Patch Manager","Inventory","Run Command","Distributor enables you to create and manage a centralised repository of software packages that can be deployed to your instances."
"What is the primary purpose of AWS Systems Manager Incident Manager?","To automate incident response and escalation processes","To automatically patch EC2 instances","To monitor application performance in real time","To manage user access policies across all AWS services","Incident Manager automates incident response and escalation processes, helping you quickly resolve issues and minimise downtime."
"Which AWS Systems Manager feature allows you to create predefined documents that can be used to perform common operational tasks on your instances?","Automation","Run Command","Session Manager","Inventory","Automation provides a library of predefined documents and allows you to create custom documents to perform operational tasks."
"You are responsible for ensuring that all EC2 instances in your organisation are configured according to a specific security baseline. Which AWS Systems Manager feature can help you enforce this baseline?","State Manager","Patch Manager","Inventory","Run Command","State Manager allows you to define and enforce a desired configuration state, ensuring that all instances meet your security baseline."
"Which AWS Systems Manager feature can be used to provide visibility into the changes made to your infrastructure, such as configuration changes or software updates?","Change Manager","Automation","Run Command","Inventory","Change Manager tracks changes made to your infrastructure, providing visibility and auditability."
"You need to share a secret value, such as an API key, with an application running on an EC2 instance. What is the recommended way to securely manage this secret using AWS Systems Manager?","Store the secret in Parameter Store using the SecureString data type","Store the secret in an environment variable","Store the secret in a plain text file on the instance","Store the secret in an S3 bucket without encryption","Parameter Store with the SecureString data type provides a secure and encrypted way to store and manage secrets."
"Which AWS Systems Manager capability can be used to centrally manage operating system configurations, ensuring consistency across a fleet of EC2 instances?","State Manager","Automation","Patch Manager","Run Command","State Manager lets you maintain a consistent configuration state across your instances by defining and applying configuration policies."
"What is the purpose of using AWS Systems Manager Quick Setup?","To quickly configure essential Systems Manager features","To quickly launch EC2 instances","To quickly create IAM roles","To quickly configure VPCs","Quick Setup helps you rapidly configure essential Systems Manager features, such as Inventory, Patch Manager, and State Manager."
"Which of the following is NOT a capability of AWS Systems Manager?","Automated database backup","Automated patching","Remote session management","Configuration management","AWS Systems Manager is not directly used for automated database backups, but instead provides capabilities like automated patching, remote session management, and configuration management."
"You are required to ensure all newly launched EC2 instances are automatically enrolled in AWS Systems Manager. Which service helps achieve this?","Systems Manager Automation in conjunction with CloudWatch Events","CloudTrail","AWS Config","IAM","Systems Manager Automation triggered by CloudWatch Events can automatically enrol new instances. The event detects the launch and the automation configures the instance."
"Which AWS Systems Manager feature lets you view and act upon operational issues related to AWS resources from a central place?","OpsCenter","Session Manager","Automation","Inventory","OpsCenter consolidates operational issues, allowing you to view, investigate and remediate problems."
"What is the purpose of AWS Systems Manager Parameter Store?","Provides secure, hierarchical storage for configuration data management and secrets management.","Provides a fully managed configuration management service for application code.","Provides a central repository for storing Amazon Machine Images (AMIs).","Provides a tool for monitoring CPU utilisation across EC2 instances.","Parameter Store provides centralised and secure storage for managing configuration data and secrets, such as passwords, database strings, and license codes."
"Which AWS Systems Manager capability allows you to remotely and securely manage the configuration of your managed instances?","State Manager","Session Manager","Patch Manager","Automation","State Manager allows you to define and maintain a consistent configuration state for your instances. It automatically applies configurations and ensures they are maintained over time."
"You need to automate patching of your EC2 instances using AWS Systems Manager. Which Systems Manager feature should you use?","Patch Manager","Inventory Manager","Run Command","Maintenance Windows","Patch Manager automates the process of patching your EC2 instances and other managed instances with security updates and other types of updates."
"Which AWS Systems Manager feature helps you collect software inventory, operating system information, and other metadata from your managed instances?","Inventory","Compliance","Distributor","Explorer","Inventory gathers detailed information about your instances, which can be used for compliance reporting, troubleshooting, and other management tasks."
"What is the primary function of AWS Systems Manager Automation?","Automating operational tasks across AWS resources.","Managing user access to EC2 instances.","Backing up EC2 instance data.","Monitoring network traffic in VPCs.","Automation allows you to define and execute automated tasks to manage AWS resources, such as creating snapshots, restarting instances, and applying patches."
"Which AWS Systems Manager feature allows you to establish secure shell (SSH) or Remote Desktop Protocol (RDP) sessions with your EC2 instances without needing to open inbound ports or manage SSH keys?","Session Manager","Run Command","Systems Manager Explorer","OpsCenter","Session Manager provides a secure and auditable way to connect to your instances without exposing them to the public internet or managing SSH keys."
"You want to ensure that your EC2 instances are compliant with your organisation's security policies. Which AWS Systems Manager feature can assist you with this?","Compliance","Explorer","Change Manager","Quick Setup","Compliance scans your managed instances to check for patch compliance, configuration compliance, and custom compliance checks, providing insights into the state of your infrastructure."
"Which AWS Systems Manager feature allows you to centrally distribute software packages to your managed instances?","Distributor","Package Manager","Application Manager","Software Central","Distributor enables you to securely store and distribute software packages, such as agents and applications, to your managed instances."
"A company needs a centralised dashboard to view operational data from multiple AWS Regions and accounts. Which AWS Systems Manager feature can provide this capability?","Explorer","CloudWatch Dashboards","OpsData","Resource Groups","Explorer provides a central view of your operational data across AWS Regions and accounts, enabling you to quickly identify and address issues."
"How does AWS Systems Manager Change Manager facilitate safe operational changes?","By providing a workflow to request, review, approve, and implement operational changes.","By automatically rolling back changes upon detection of errors.","By automatically encrypting all data during change operations.","By automatically scaling resources during change windows.","Change Manager facilitates operational changes by providing a controlled workflow for requesting, reviewing, approving, and implementing changes to AWS resources, reducing the risk of errors and disruptions."
"Which AWS Systems Manager capability allows you to automate operational tasks and workflows across your AWS resources?","Automation","Inventory","Parameter Store","Session Manager","Systems Manager Automation enables you to define and execute automated tasks for managing and configuring your AWS resources."
"Which AWS Systems Manager feature allows you to securely manage and rotate secrets, such as passwords and API keys?","Parameter Store","Session Manager","Patch Manager","Inventory","Parameter Store provides secure, centralised storage and management of secrets, allowing you to rotate and encrypt sensitive data."
"What is the primary purpose of AWS Systems Manager Inventory?","To collect information about your managed instances","To automate patching of managed instances","To manage user access to managed instances","To provide a secure shell access to managed instances","Systems Manager Inventory automatically collects hardware and software inventory from your managed instances, making it easier to track and manage your infrastructure."
"Which AWS Systems Manager capability enables you to establish secure shell (SSH) or RDP connections to your instances without exposing them to the public internet?","Session Manager","Patch Manager","Compliance","Distributor","Session Manager provides a secure and auditable way to connect to your instances without the need for bastion hosts or open inbound ports."
"You need to ensure that your EC2 instances are compliant with your organisation's security policies using AWS Systems Manager. Which feature should you use?","Compliance","Automation","Run Command","Maintenance Windows","Systems Manager Compliance allows you to scan your instances against predefined or custom baselines and generate compliance reports."
"Which AWS Systems Manager feature allows you to schedule and orchestrate patching of your managed instances?","Patch Manager","Inventory","Run Command","Distributor","Systems Manager Patch Manager automates the process of patching operating systems and applications on your instances, allowing you to schedule patches during maintenance windows."
"You want to quickly execute commands on a group of managed instances using AWS Systems Manager. Which feature should you use?","Run Command","Automation","Patch Manager","Inventory","Systems Manager Run Command allows you to remotely and securely execute commands on one or more managed instances."
"Which AWS Systems Manager capability allows you to distribute and install software packages on your managed instances?","Distributor","Automation","Compliance","Session Manager","Systems Manager Distributor enables you to package, distribute, and install software on your managed instances."
"What is the purpose of AWS Systems Manager Maintenance Windows?","To schedule maintenance activities on your managed instances","To monitor the health of your managed instances","To manage access control to your managed instances","To store configuration data for your managed instances","Maintenance Windows allow you to define recurring schedules for executing operational tasks, such as patching, software installations, and reboots, on your instances."
"You are using AWS Systems Manager Parameter Store to store sensitive information. Which type of parameter should you use to encrypt the value?","Secure String","String","Integer","StringList","Secure String parameters are encrypted using AWS KMS, providing enhanced security for sensitive data stored in Parameter Store."