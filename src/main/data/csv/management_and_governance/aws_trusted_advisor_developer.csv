"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Trusted Advisor?","To provide best practice recommendations for cost optimisation, security, fault tolerance, and performance improvement.","To monitor the real-time performance of EC2 instances.","To manage user access and permissions for AWS resources.","To automatically patch operating systems on EC2 instances.","Trusted Advisor analyses your AWS environment and provides recommendations based on AWS best practices."
"Which of the following AWS Trusted Advisor categories helps you reduce your AWS costs?","Cost Optimisation","Security","Fault Tolerance","Performance","The Cost Optimisation category specifically identifies opportunities to reduce your AWS spending."
"How often does AWS Trusted Advisor automatically refresh its checks by default?","Once per week","Once per day","Every hour","In real-time","Trusted Advisor automatically refreshes its checks on a weekly basis to provide updated recommendations."
"Which of the following is NOT a category in AWS Trusted Advisor?","Compliance","Cost Optimisation","Performance","Security","Compliance is not a top level category. Security encompasses compliance related checks."
"What action is required to see AWS Trusted Advisor recommendations for all AWS accounts in an AWS Organisation?","Enable Trusted Advisor in the management account and share the results with member accounts.","Install the Trusted Advisor agent on all EC2 instances.","Configure a dedicated IAM role for Trusted Advisor in each account.","Upgrade all accounts to the Business Support plan.","You enable Trusted Advisor at the organisation level, starting with the management account. It then shares the information."
"Which AWS Support plan provides access to all AWS Trusted Advisor checks?","Business and Enterprise","Basic","Developer","Enterprise Only","Business and Enterprise support plans provide access to the full suite of Trusted Advisor checks."
"What type of data does AWS Trusted Advisor primarily analyse to provide recommendations?","AWS CloudTrail logs and AWS Config rules","EC2 instance CPU utilisation and memory usage","RDS database performance metrics","Your AWS account configuration and usage data","Trusted Advisor analyses your AWS account configuration and usage to identify areas for improvement."
"What is the meaning of a 'red' status indicator in AWS Trusted Advisor?","Indicates that an action is recommended immediately.","Indicates that the resource is being used efficiently.","Indicates that the issue has been resolved.","Indicates that the resource is operating normally.","A red status indicator signifies a high-risk issue that requires immediate attention."
"What is the meaning of a 'yellow' status indicator in AWS Trusted Advisor?","Indicates that investigation is recommended.","Indicates that an action is recommended immediately.","Indicates that the issue has been resolved.","Indicates that the resource is operating normally.","A yellow status indicator suggests that you should investigate the highlighted issue."
"You want to programmatically access AWS Trusted Advisor recommendations. Which AWS service can you use?","AWS Support API","AWS CloudWatch Events","AWS Config","AWS Lambda","The AWS Support API allows programmatic access to Trusted Advisor recommendations and checks."
"What is the maximum number of AWS accounts that can be associated with an AWS Organisation to receive AWS Trusted Advisor recommendations?","There is no limit","100","1000","5000","There is no hard limit on the number of accounts that can be associated with an AWS Organisation and receive Trusted Advisor recommendations."
"Which AWS Trusted Advisor check helps you identify EC2 security groups with unrestricted access?","Security Groups - Specific Ports Unrestricted","IAM Use","S3 Bucket Permissions","RDS Public Access","The 'Security Groups - Specific Ports Unrestricted' check identifies security groups that allow unrestricted access to specific ports, posing a security risk."
"You have configured AWS Trusted Advisor to send weekly email summaries. What information will these emails contain?","A summary of all identified issues and recommended actions.","Detailed performance metrics for your EC2 instances.","A list of new AWS services and features.","A full audit log of all AWS account activity.","Trusted Advisor sends email summaries containing a high-level overview of the identified issues and recommendations."
"What does the AWS Trusted Advisor 'Service Limits' check evaluate?","Whether you are approaching or exceeding your AWS service limits.","Whether your services are configured to meet compliance requirements.","Whether you are using the latest versions of AWS services.","Whether your services are configured for optimal performance.","The 'Service Limits' check alerts you when you are approaching or exceeding your AWS service limits, which can impact your ability to provision resources."
"Which AWS Trusted Advisor check can help you identify underutilised EC2 instances?","Underutilised Amazon EC2 Instances","High Utilisation Amazon EC2 Instances","Idle Load Balancers","Unassociated Elastic IP Addresses","The 'Underutilised Amazon EC2 Instances' check identifies EC2 instances with low CPU utilisation and network I/O, suggesting they may be oversized."
"Which is an advantage of using AWS Trusted Advisor over manually reviewing your AWS resources?","Trusted Advisor automates the process and provides proactive recommendations.","Trusted Advisor is free for all AWS users.","Trusted Advisor provides real-time monitoring of resource utilisation.","Trusted Advisor can automatically remediate identified issues.","Trusted Advisor automates the process of analysing your AWS environment and proactively provides recommendations based on best practices."
"How does AWS Trusted Advisor contribute to improving the fault tolerance of your applications?","By identifying single points of failure and recommending solutions.","By automatically replicating data across multiple Availability Zones.","By automatically scaling your resources based on demand.","By providing real-time monitoring of application performance.","Trusted Advisor contributes to fault tolerance by identifying single points of failure and providing recommendations to mitigate them."
"You need to exempt a specific resource from an AWS Trusted Advisor check. What can you do?","Suppress the check for that resource.","Delete the resource.","Modify the Trusted Advisor configuration file.","Disable the Trusted Advisor service.","You can suppress a specific check for a specific resource, preventing Trusted Advisor from reporting on it."
"Which AWS Trusted Advisor check assists in identifying idle load balancers?","Idle Load Balancers","Underutilised Amazon EC2 Instances","Security Groups - Specific Ports Unrestricted","High Utilisation Amazon EC2 Instances","The 'Idle Load Balancers' check identifies load balancers that are not actively routing traffic, indicating potential cost savings."
"Which statement about AWS Trusted Advisor is true?","It provides cost optimization, security, fault tolerance, and performance recommendations.","It automatically fixes security vulnerabilities.","It replaces AWS Config.","It requires installing an agent on EC2 instances.","Trusted Advisor is designed to assist with cost optimisation, security, fault tolerance and performance improvments."
"What action can you take to ensure AWS Trusted Advisor checks are performed regularly?","Schedule a weekly refresh of the checks.","Manually run the checks daily.","Install a monitoring agent on all your AWS resources.","AWS Trusted Advisor checks are always performed automatically.","AWS Trusted Advisor checks are performed automatically on a weekly schedule by default. You can also manually refresh the checks."
"You want to identify unused Elastic IP addresses in your AWS account. Which AWS Trusted Advisor check can help with this?","Unassociated Elastic IP Addresses","Security Groups - Specific Ports Unrestricted","Idle Load Balancers","Underutilised Amazon EC2 Instances","Unassociated Elastic IP Addresses highlights IP addresses that are allocated but not attached to any instance. These can incur charges."
"Which of the following AWS services integrates with AWS Trusted Advisor to provide ongoing assessment and analysis of your AWS infrastructure?","AWS Organizations","AWS CloudTrail","AWS CloudWatch","AWS IAM","AWS Trusted Advisor integrates with AWS Organizations to provide ongoing assessment and analysis of your AWS infrastructure."
"Which of the following describes the function of Trusted Advisor's 'S3 Bucket Permissions' check?","It identifies S3 buckets with open access.","It monitors data transfer costs for S3 buckets.","It checks the encryption status of S3 buckets.","It identifies unused S3 buckets.","Trusted Advisor's 'S3 Bucket Permissions' check identifies S3 buckets that have open access permissions, which can pose a security risk."
"What is a benefit of using AWS Trusted Advisor for security assessments?","It automates security best practice checks.","It provides real-time threat detection.","It replaces the need for security groups.","It automatically encrypts data at rest.","Trusted Advisor automates the process of checking your AWS configuration against security best practices."
"Your company uses AWS Organizations. How can you use AWS Trusted Advisor to view recommendations for all member accounts?","Enable Trusted Advisor in the management account and share the results with member accounts.","Install Trusted Advisor in each member account.","Configure a centralised logging solution for Trusted Advisor.","Upgrade all member accounts to the Enterprise support plan.","You can manage and view Trusted Advisor recommendations for all member accounts from the management account."
"What action can you take based on AWS Trusted Advisor's recommendations for underutilised EC2 instances?","Resize the instances to a smaller instance type.","Terminate the instances.","Add more storage to the instances.","Upgrade the instances to the latest generation.","If Trusted Advisor identifies underutilised EC2 instances, you can resize them to a smaller instance type to reduce costs."
"Which AWS Trusted Advisor check helps you identify RDS DB instances that are not backed up?","RDS Backups","RDS Security Groups","RDS Instance Types","RDS Performance","The 'RDS Backups' check identifies RDS instances that do not have backups enabled, which is important for data recovery."
"You have an AWS Enterprise Support plan. How often can you request a Well-Architected Framework Review from AWS?","As needed","Once per quarter","Once per year","Once per month","Customers with Enterprise Support receive unlimited Well-Architected Framework reviews."
"Which AWS Trusted Advisor check can help you improve the performance of your application by identifying high-latency connections?","High Utilisation Amazon EC2 Instances","Amazon EBS Volume Configuration","Network Configuration","Security Groups - Specific Ports Unrestricted","Trusted Advisor does not have checks that can identify high-latency connections."
"If AWS Trusted Advisor identifies a security risk, what should you do first?","Review the recommended actions and implement them.","Immediately terminate the affected resources.","Contact AWS Support for assistance.","Disable the affected services.","The first step is to review the recommendations provided by Trusted Advisor and implement the appropriate actions to mitigate the risk."
"Which AWS Trusted Advisor category focuses on ensuring your AWS resources are resilient and available?","Fault Tolerance","Cost Optimisation","Security","Performance","The Fault Tolerance category focuses on identifying potential points of failure and recommending solutions to improve the resilience and availability of your applications."
"You are receiving excessive alerts from AWS Trusted Advisor regarding a resource that you know is configured correctly. What should you do?","Suppress the check for that resource.","Disable Trusted Advisor notifications.","Delete the resource.","Contact AWS Support.","Suppressing the check will prevent future alerts for that specific resource without affecting other resources."
"Which of the following is a benefit of using AWS Trusted Advisor for cost optimisation?","It identifies opportunities to reduce your AWS spending.","It automatically reduces your AWS bill.","It replaces the need for AWS Cost Explorer.","It provides real-time pricing data for AWS services.","Trusted Advisor identifies areas where you can optimize your AWS usage to reduce costs."
"What is the purpose of the AWS Trusted Advisor 'IAM Use' check?","To verify that IAM users are using multi-factor authentication (MFA).","To identify unused IAM roles.","To enforce password policies for IAM users.","To monitor IAM policy changes.","The 'IAM Use' check verifies that IAM users are using multi-factor authentication (MFA) to enhance security."
"Which AWS Trusted Advisor check helps you identify underutilised AWS Lambda functions?","There is no specific check for Lambda function utilisation.","Underutilised Amazon EC2 Instances","Idle Load Balancers","Unassociated Elastic IP Addresses","Trusted Advisor does not currently have a specific check for Lambda function utilisation. You need to monitor this through CloudWatch."
"You have enabled AWS Trusted Advisor in your AWS account. Where can you access the Trusted Advisor dashboard?","AWS Management Console","AWS CLI","AWS SDK","AWS Marketplace","The Trusted Advisor dashboard is accessed through the AWS Management Console."
"Which AWS Trusted Advisor check can help you optimise your Amazon EBS volume configuration?","Amazon EBS Volume Configuration","Underutilised Amazon EC2 Instances","Security Groups - Specific Ports Unrestricted","Idle Load Balancers","The 'Amazon EBS Volume Configuration' check identifies potential issues with your EBS volume configuration, such as provisioned IOPS that are not being fully utilised."
"What is a limitation of the AWS Trusted Advisor Basic support plan?","Limited number of checks.","No email notifications.","No access to the AWS Support API.","No access to the AWS Management Console.","The Basic support plan has a limited number of checks available compared to Business and Enterprise support."
"Which AWS Trusted Advisor category can help you identify potential security vulnerabilities in your AWS environment?","Security","Cost Optimisation","Performance","Fault Tolerance","The Security category specifically focuses on identifying potential security vulnerabilities and misconfigurations."
"How can you ensure that your AWS Trusted Advisor checks are up-to-date?","Manually refresh the checks in the AWS Management Console.","Automatically refresh the checks daily.","Schedule a weekly refresh of the checks.","Trusted Advisor checks are always up-to-date.","You can manually refresh the checks in the AWS Management Console to ensure you have the latest recommendations."
"Which of the following AWS services can be used to automate the remediation of issues identified by AWS Trusted Advisor?","AWS Systems Manager","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS Systems Manager (SSM) can be used to automate the remediation of issues identified by Trusted Advisor."
"You need to generate a report of all AWS Trusted Advisor recommendations for your AWS account. Which format is NOT supported?","PDF","CSV","JSON","HTML","Trusted Advisor does not directly generate PDF reports."
"Which statement is true about AWS Trusted Advisor and its use with AWS Regions?","Trusted Advisor provides recommendations for all supported AWS Regions.","Trusted Advisor only provides recommendations for the AWS Region in which it is enabled.","Trusted Advisor is not available in all AWS Regions.","Trusted Advisor can only be used with the US East (N. Virginia) Region.","Trusted Advisor provides recommendations based on the AWS Regions in which you are using AWS resources."
"Which AWS Trusted Advisor check helps you to determine if your S3 buckets are configured for logging?","S3 Bucket Logging","S3 Bucket Permissions","S3 Versioning","S3 Encryption","The S3 Bucket Logging check helps to determine if your S3 buckets are configured for logging which is an important security and auditability practice."
"You are troubleshooting a performance issue on your website and suspect that the database is the bottleneck. Can AWS Trusted Advisor help identify if your RDS instance is correctly configured?","Yes, it has checks relating to RDS configuration.","No, AWS Trusted Advisor only checks for cost optimisation.","No, that would require AWS Support, not Trusted Advisor.","No, AWS Trusted Advisor does not provide performance recommendations.","Trusted Advisor includes checks that look at RDS instance configuration."
"How does AWS Trusted Advisor help with increasing the performance of your applications on AWS?","By identifying underutilised resources that can be optimised to save costs.","By automatically scaling your EC2 instances based on demand.","By suggesting the latest generation of EC2 instances to enhance speed.","By automatically optimising database queries for faster performance.","Trusted Advisor provides suggestions for identifying underutilised resources that can be optimised to save costs and improve performance by freeing up unused resources."
"Which statement about AWS Trusted Advisor is correct?","It analyses your AWS environment using AWS best practices.","It automatically fixes all issues it identifies.","It is a replacement for AWS CloudTrail.","It only works with EC2 instances.","Trusted Advisor analyses your AWS environment and makes recommendations based on AWS best practices, such as cost optimisation, security, fault tolerance, and performance improvement."
"In AWS Trusted Advisor, what does the 'Performance' category focus on?","Identifying ways to improve the speed and responsiveness of your AWS services.","Identifying security vulnerabilities in your AWS environment.","Identifying cost optimisation opportunities.","Identifying issues with service limits.","The Performance category in Trusted Advisor highlights opportunities to improve the speed and responsiveness of your AWS services, such as optimising EC2 instance sizes or EBS volume configurations."
"Which AWS Trusted Advisor check falls under the 'Cost Optimisation' category?","Idle Load Balancers","Security Groups - Specific Ports Unrestricted","Low Utilisation EC2 Instances","EBS Public Snapshots","Idle Load Balancers identifies load balancers that are not actively serving traffic and can be removed to reduce costs."
"Which AWS support plan is required to access all AWS Trusted Advisor checks?","Business or Enterprise Support","Basic Support","Developer Support","Professional Support","All Trusted Advisor checks are available with Business and Enterprise Support plans. Basic and Developer Support plans have limited checks."
"What type of recommendations does AWS Trusted Advisor provide?","Best practice recommendations","Contractual and legal recommendations","Tax advice","Business strategy recommendations","Trusted Advisor is designed to help follow AWS best practices in the areas of cost optimisation, security, fault tolerance, performance, and service limits."
"In AWS Trusted Advisor, what does a 'red' status indicator typically signify?","A critical issue that needs immediate attention","An informational message","A recommendation for cost savings","A service limit increase request","A red status indicates a critical issue that requires immediate attention, such as a security vulnerability or a service limit being exceeded."
"If AWS Trusted Advisor flags an EC2 instance as underutilised, what action might it recommend?","Downsize the instance to a smaller size.","Upgrade the instance to a larger size.","Terminate the instance.","Change the instance type to a GPU instance.","Trusted Advisor's recommendation for an underutilised EC2 instance is typically to downsize it to a smaller, more cost-effective size."
"Which AWS service does Trusted Advisor directly integrate with to display recommendations?","AWS Management Console","AWS CloudTrail","Amazon CloudWatch","AWS Config","Trusted Advisor integrates directly with the AWS Management Console to display its recommendations in a user-friendly format."
"Which AWS Trusted Advisor check helps identify EBS volumes that might be costing money unnecessarily?","Unassociated Elastic IP Addresses","Idle Load Balancers","Idle EC2 Instances","Idle EBS Volumes","Idle EBS Volumes identifies EBS volumes that are not attached to any EC2 instances and are therefore potentially costing money without providing value."
"Which of the following is NOT a core category of checks performed by AWS Trusted Advisor?","Compliance","Fault Tolerance","Security","Cost Optimisation","Compliance is not one of the core categories of checks performed by AWS Trusted Advisor. The core categories are Cost Optimisation, Security, Fault Tolerance, Performance, and Service Limits."
"How often does AWS Trusted Advisor automatically refresh its checks by default?","Every 24 hours","Every hour","Every 7 days","Every 30 days","Trusted Advisor automatically refreshes its checks every 24 hours to ensure that you have the most up-to-date recommendations."
"What is the purpose of the 'Exclude Items' feature in AWS Trusted Advisor?","To hide specific recommendations that are not relevant to your environment.","To disable Trusted Advisor entirely.","To automatically fix issues identified by Trusted Advisor.","To generate detailed reports on identified issues.","The 'Exclude Items' feature allows you to hide specific recommendations that are not relevant or applicable to your environment, decluttering the Trusted Advisor dashboard."
"Which AWS Trusted Advisor check helps identify security risks associated with S3 buckets?","S3 Bucket Permissions","IAM Use","Security Groups - Specific Ports Unrestricted","EBS Public Snapshots","S3 Bucket Permissions identifies S3 buckets with open access permissions, which can pose a security risk."
"Can you customise the checks that AWS Trusted Advisor performs?","No, the checks are pre-defined.","Yes, you can create custom checks using AWS Lambda.","Yes, you can modify the existing checks.","Yes, you can schedule different checks to run at different times.","No, the checks are pre-defined by AWS and cannot be customised."
"Which AWS Trusted Advisor check helps identify unused or underutilised resources that can be terminated to save costs?","Idle Load Balancers","Security Groups - Specific Ports Unrestricted","High Utilisation EC2 Instances","EBS Public Snapshots","Idle Load Balancers identifies load balancers that are not actively serving traffic and can be removed to reduce costs."
"What happens when an AWS service limit is reached, as identified by Trusted Advisor?","Your application may experience performance issues or failures.","Your AWS account will be suspended.","AWS will automatically increase the limit.","You will be charged a higher rate for exceeding the limit.","When a service limit is reached, your application may experience performance issues or failures because you are unable to provision more resources."
"Which AWS service can be used to automatically remediate issues identified by AWS Trusted Advisor?","AWS Systems Manager Automation","AWS CloudTrail","Amazon CloudWatch","AWS Config","AWS Systems Manager Automation can be used to automatically remediate issues identified by AWS Trusted Advisor by creating automation documents that execute predefined actions."
"Which AWS Trusted Advisor check helps you identify EC2 instances that are running on older generation instance types?","EC2 Instance Types","Security Groups - Specific Ports Unrestricted","High Utilisation EC2 Instances","EBS Public Snapshots","EC2 Instance Types identifies EC2 instances that are running on older generation instance types, which might not be as efficient or cost-effective as newer generations."
"What is the main benefit of using AWS Trusted Advisor's 'Service Limits' checks?","To proactively identify potential bottlenecks and prevent service disruptions.","To automatically increase service limits.","To reduce the cost of AWS services.","To improve the security of your AWS environment.","The main benefit of the 'Service Limits' checks is to proactively identify potential bottlenecks and prevent service disruptions by ensuring that you are not approaching your service limits."
"Which AWS Trusted Advisor category helps in reducing your monthly AWS bill?","Cost Optimisation","Fault Tolerance","Security","Performance","The Cost Optimisation category provides recommendations to reduce your monthly AWS bill by identifying unused resources, underutilised resources, and other cost-saving opportunities."
"Which AWS Trusted Advisor check is most helpful in identifying potential weaknesses in your network configuration?","Security Groups - Specific Ports Unrestricted","S3 Bucket Permissions","Idle Load Balancers","Underutilised RDS Instances","Security Groups - Specific Ports Unrestricted helps identify security groups that have overly permissive rules, which can expose your resources to potential security threats."
"How does AWS Trusted Advisor help improve the fault tolerance of your AWS infrastructure?","By identifying single points of failure and recommending redundant configurations.","By automatically backing up your data.","By automatically scaling your resources.","By encrypting your data at rest and in transit.","Trusted Advisor helps improve fault tolerance by identifying single points of failure, such as single EC2 instances or EBS volumes, and recommending redundant configurations to improve the resilience of your infrastructure."
"Which of these is NOT a category covered by AWS Trusted Advisor?","Data residency","Cost Optimisation","Security","Performance","Data residency is not a core category covered by AWS Trusted Advisor. The core categories are Cost Optimisation, Security, Fault Tolerance, Performance, and Service Limits."
"You have an AWS Business Support plan. How can you access AWS Trusted Advisor?","Through the AWS Management Console.","By contacting AWS Support directly.","Through the AWS Marketplace.","By using the AWS CLI.","Trusted Advisor is accessible through the AWS Management Console under the 'Trusted Advisor' service."
"What action should you take when Trusted Advisor flags a security group with unrestricted ports?","Restrict access to specific IP addresses or CIDR blocks.","Delete the security group.","Disable inbound traffic entirely.","Enable outbound traffic only.","The correct action is to restrict access to specific IP addresses or CIDR blocks to minimise the attack surface."
"Trusted Advisor flags an EBS volume as 'Idle'. What does this mean?","The volume is not attached to any EC2 instance.","The volume is running at maximum capacity.","The volume is encrypted.","The volume is about to expire.","An 'Idle' EBS volume is not attached to any EC2 instance and is therefore likely costing money without providing value."
"How can you suppress a specific recommendation from AWS Trusted Advisor?","By excluding the item.","By deleting the item.","By archiving the item.","By ignoring the item.","You can suppress a specific recommendation by excluding the item, which hides it from the Trusted Advisor dashboard."
"What is the primary benefit of running AWS Trusted Advisor checks regularly?","To continuously improve the cost efficiency, security, and performance of your AWS environment.","To automatically patch security vulnerabilities.","To automatically scale your resources based on demand.","To generate compliance reports for regulatory audits.","The primary benefit of running Trusted Advisor checks regularly is to continuously improve the cost efficiency, security, and performance of your AWS environment by identifying and addressing potential issues."
"Which Trusted Advisor check identifies potential security risks associated with publicly accessible RDS snapshots?","RDS Public Snapshots","Security Groups - Specific Ports Unrestricted","S3 Bucket Permissions","IAM Use","RDS Public Snapshots identifies RDS snapshots that are publicly accessible, which can expose your data to unauthorised access."
"Which feature of AWS Trusted Advisor allows you to receive notifications about changes to your check results?","Weekly Email Notifications","AWS CloudTrail","Amazon CloudWatch Events","AWS Config","Trusted Advisor can send you Weekly Email Notifications summarizing the changes to your check results."
"What is the impact of ignoring AWS Trusted Advisor's recommendations?","Increased costs, security vulnerabilities, and potential performance issues.","Automatic termination of unused resources.","Automatic scaling of resources based on demand.","Improved compliance with regulatory standards.","Ignoring Trusted Advisor's recommendations can lead to increased costs, security vulnerabilities, and potential performance issues, as you might be missing opportunities to optimise your environment."
"Which AWS Trusted Advisor check is essential for ensuring business continuity and disaster recovery?","Fault Tolerance checks","Cost Optimisation checks","Security checks","Performance checks","Fault Tolerance checks help identify single points of failure and recommend redundant configurations, which are essential for ensuring business continuity and disaster recovery."
"Your company is planning a major product launch. How can AWS Trusted Advisor assist in ensuring a smooth launch?","By identifying potential service limit issues and performance bottlenecks.","By automatically scaling your resources based on demand.","By automatically encrypting your data at rest and in transit.","By generating compliance reports for regulatory audits.","Trusted Advisor can identify potential service limit issues and performance bottlenecks, allowing you to address them proactively and ensure a smooth product launch."
"Which AWS Trusted Advisor check helps you identify unused or underutilised resources that can be terminated to save costs?","Idle EC2 Instances","Security Groups - Specific Ports Unrestricted","High Utilisation EC2 Instances","EBS Public Snapshots","Idle EC2 Instances identifies EC2 instances that are not actively serving traffic and can be removed to reduce costs."
"What is the advantage of using AWS Trusted Advisor over manually reviewing your AWS infrastructure?","Trusted Advisor provides automated, continuous monitoring and recommendations based on AWS best practices.","Trusted Advisor offers lower prices compared to manual review.","Trusted Advisor is more accurate than manual review.","Trusted Advisor guarantees 100% security compliance.","Trusted Advisor provides automated, continuous monitoring and recommendations based on AWS best practices, saving time and effort compared to manual reviews."
"You've made changes to your AWS infrastructure. How long does it typically take for Trusted Advisor to reflect these changes in its checks?","Up to 24 hours.","Immediately.","Up to 1 hour.","Up to 7 days.","Trusted Advisor automatically refreshes its checks every 24 hours, so it typically takes up to 24 hours to reflect changes in your infrastructure."
"When evaluating Trusted Advisor recommendations, what's an important factor to consider?","The specific requirements and constraints of your application and environment.","The average cost savings reported by other AWS customers.","The compliance regulations applicable to your industry.","The default settings recommended by AWS.","It's important to consider the specific requirements and constraints of your application and environment when evaluating Trusted Advisor recommendations, as not all recommendations are universally applicable."
"Which Trusted Advisor check alerts you to S3 buckets with open access permissions?","S3 Bucket Permissions","IAM Use","Security Groups - Specific Ports Unrestricted","EBS Public Snapshots","S3 Bucket Permissions identifies S3 buckets with open access permissions, which can pose a security risk."
"Which of the following is a benefit of using AWS Trusted Advisor to manage service limits?","Proactively identify potential issues and request limit increases before they impact your applications.","Automatically increase service limits without manual intervention.","Reduce the cost of exceeding service limits.","Eliminate the need for service limits entirely.","Trusted Advisor helps proactively identify potential issues related to service limits and allows you to request limit increases before they impact your applications."
"Which of the following best describes the purpose of the 'Exclusions' feature in AWS Trusted Advisor?","To suppress or hide specific recommendations that are not applicable or relevant to your environment.","To prevent Trusted Advisor from running certain checks.","To automatically fix issues identified by Trusted Advisor.","To generate detailed reports on specific check results.","The 'Exclusions' feature allows you to suppress or hide specific recommendations that are not applicable or relevant to your environment, decluttering the Trusted Advisor dashboard."
"What is the most efficient way to view the recommendations provided by AWS Trusted Advisor?","Through the AWS Management Console's Trusted Advisor dashboard.","By reviewing the AWS CloudTrail logs.","By contacting AWS Support directly.","By using the AWS Cost Explorer.","The most efficient way to view Trusted Advisor's recommendations is through the AWS Management Console's Trusted Advisor dashboard, which provides a user-friendly interface for reviewing and managing the recommendations."
"When should you consider implementing a recommendation from AWS Trusted Advisor?","When the recommendation aligns with your organisation's specific needs, priorities, and risk tolerance.","When the recommendation is marked as 'High Risk' by Trusted Advisor.","When the recommendation is universally applicable to all AWS environments.","When the recommendation is the easiest and quickest to implement.","You should consider implementing a recommendation when it aligns with your organisation's specific needs, priorities, and risk tolerance, as not all recommendations are suitable for every environment."
"Which AWS Trusted Advisor check relates to the configuration of AWS Identity and Access Management (IAM)?","IAM Use","Security Groups - Specific Ports Unrestricted","S3 Bucket Permissions","EBS Public Snapshots","IAM Use check helps identify potential security risks related to IAM configurations, such as overly permissive IAM roles or unused IAM users."
"What AWS Trusted Advisor category would help you find EC2 instances that are running but are not actively used?","Cost Optimisation","Fault Tolerance","Security","Performance","Cost Optimisation provides recommendations to reduce your monthly AWS bill by identifying unused resources, underutilised resources, including Idle EC2 instances."
"Which action will NOT improve your AWS security posture based on AWS Trusted Advisor's recommendations?","Excluding specific Trusted Advisor checks.","Restricting access to specific IP addresses or CIDR blocks on a security group.","Removing public access from S3 buckets.","Utilising IAM roles to delegate permissions.","Excluding Trusted Advisor checks will remove those security recommendations so that action would NOT improve your AWS security posture."
"You need to ensure that your AWS resources are configured for high availability. Which AWS Trusted Advisor category will be most helpful?","Fault Tolerance","Cost Optimisation","Security","Performance","Fault Tolerance checks help identify single points of failure and recommend redundant configurations, which are essential for ensuring high availability."
"What is the function of the 'Refresh All Checks' button in AWS Trusted Advisor?","To manually trigger an immediate refresh of all Trusted Advisor checks.","To automatically fix issues identified by Trusted Advisor.","To download a report of all Trusted Advisor checks.","To reset Trusted Advisor to its default configuration.","The 'Refresh All Checks' button allows you to manually trigger an immediate refresh of all Trusted Advisor checks, ensuring that you have the most up-to-date recommendations."
"What is the purpose of the AWS Trusted Advisor 'Service Limits' dashboard?","To display your current usage against AWS service limits and identify potential bottlenecks.","To automatically increase your service limits.","To reduce the cost of exceeding service limits.","To eliminate the need for service limits entirely.","The 'Service Limits' dashboard displays your current usage against AWS service limits and identifies potential bottlenecks, allowing you to proactively request limit increases."
"Which of the following AWS Support plans provides access to the full set of AWS Trusted Advisor checks and features?","Enterprise Support","Basic Support","Developer Support","Business Support","The Enterprise support plan provides access to the full set of AWS Trusted Advisor checks and features"
"What does the AWS Trusted Advisor 'Virtual Private Cloud (VPC)' check primarily focus on?","Identifying potential security misconfigurations within VPCs.","Optimising the cost of VPC resources.","Improving the performance of VPC networks.","Identifying unused VPC resources.","The AWS Trusted Advisor 'Virtual Private Cloud (VPC)' check primarily focuses on identifying potential security misconfigurations within VPCs, such as open security groups or misconfigured network ACLs."
"Which AWS service provides real-time guidance to help you provision your resources following AWS best practices?","AWS Trusted Advisor","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS Trusted Advisor provides real-time recommendations to help you follow AWS best practices."
"What are the five categories of checks performed by AWS Trusted Advisor?","Cost Optimisation, Security, Fault Tolerance, Performance, Service Limits","Compliance, Monitoring, Networking, Storage, Compute","Identity, Access Management, Encryption, Logging, Auditing","Disaster Recovery, Business Continuity, DevOps, Automation, Migration","AWS Trusted Advisor provides recommendations in five categories: Cost Optimisation, Security, Fault Tolerance, Performance, and Service Limits."
"Regarding AWS Trusted Advisor, what does the 'Service Limits' check category monitor?","Your usage of AWS resources relative to service quotas.","Security vulnerabilities in your AWS account.","The cost-effectiveness of your AWS resources.","The performance of your AWS resources.","The Service Limits category monitors your AWS resource usage against service quotas to help prevent hitting limits."
"What does the 'Fault Tolerance' category of AWS Trusted Advisor checks focus on?","Identifying ways to improve the resilience of your AWS infrastructure.","Identifying unused resources.","Identifying security misconfigurations.","Identifying performance bottlenecks.","The Fault Tolerance category helps you identify ways to improve the resilience of your AWS infrastructure by highlighting potential single points of failure or configuration issues that could lead to outages."
"How often does AWS Trusted Advisor refresh its checks?","The frequency varies depending on the check; some are refreshed daily, weekly, or on-demand.","Checks are only refreshed manually.","All checks are refreshed hourly.","All checks are refreshed monthly.","The refresh frequency varies by check. Some checks are refreshed daily, weekly, or on-demand, depending on the nature of the check and the data it relies on."
"Which AWS support plan includes access to all AWS Trusted Advisor checks?","Business and Enterprise","Basic","Developer","Professional","The Business and Enterprise support plans provide access to all Trusted Advisor checks, including both core and full checks."
"Which is an AWS Trusted Advisor best practice for 'Security'?","Check security group rules to ensure they are not overly permissive.","Use Reserved Instances to reduce costs.","Monitor CPU utilisation to optimise performance.","Enable multi-factor authentication for all IAM users.","While enabling MFA for IAM users is a security best practice, the direct output of AWS Trusted Advisor's security checks focus on issues like security group rules."
"How can you programmatically access AWS Trusted Advisor recommendations?","AWS Support API","AWS CLI and AWS SDK","AWS Config Rules","AWS CloudFormation","The AWS Support API provides programmatic access to Trusted Advisor recommendations."
"You want to suppress a specific AWS Trusted Advisor recommendation because you have already addressed it. How can you do this?","Use the 'Exclude Items' feature within the Trusted Advisor console.","Delete the resource identified in the recommendation.","Disable the Trusted Advisor service.","Delete the AWS Account.","The 'Exclude Items' feature allows you to suppress specific recommendations that you have already addressed or determined are not applicable to your environment."
"What is the AWS Trusted Advisor 'Cost Optimisation' check primarily focused on?","Identifying opportunities to reduce AWS spending.","Ensuring your infrastructure is highly available.","Identifying security vulnerabilities.","Improving application performance.","The Cost Optimisation checks are focused on identifying opportunities to reduce your AWS spending by highlighting underutilised resources, potential savings with Reserved Instances, and other cost-saving measures."
"Which of the following AWS services does AWS Trusted Advisor primarily focus on providing recommendations for?","EC2, S3, RDS","Lambda, CloudFront, API Gateway","DynamoDB, SQS, SNS","IAM, KMS, CloudHSM","Trusted Advisor checks a wide array of services with a focus on cost, security, performance, and service limits. EC2, S3, and RDS are common services that Trusted Advisor provides recommendations for."
"A customer is approaching their service limit for EC2 instances. How can AWS Trusted Advisor help?","It will provide a warning and suggest requesting a service limit increase.","It will automatically increase the service limit.","It will terminate unused instances.","It will automatically purchase Reserved Instances.","Trusted Advisor monitors service limits and will provide a warning if you are approaching a limit, suggesting that you request a service limit increase."
"Which of the following actions can be performed directly from the AWS Trusted Advisor console?","Request a service limit increase.","Launch an EC2 instance.","Create an S3 bucket.","Modify an IAM role.","You can directly request a service limit increase from the Trusted Advisor console."
"What is the primary benefit of using AWS Trusted Advisor to check for open security groups?","To reduce the risk of unauthorised access to your resources.","To improve the performance of your applications.","To reduce your AWS costs.","To simplify your AWS deployments.","Open security groups can pose a security risk by allowing unauthorised access to your resources. Trusted Advisor helps you identify and remediate these vulnerabilities."
"You've implemented a solution based on an AWS Trusted Advisor recommendation. How do you verify the issue is resolved?","Refresh the Trusted Advisor check to see if the recommendation is no longer listed.","Receive an email notification from AWS.","Check the AWS CloudTrail logs.","Manually test the solution.","After implementing a solution, you should refresh the Trusted Advisor check. If the issue is resolved, the recommendation should no longer be listed."
"What type of IAM permissions are required to view all AWS Trusted Advisor recommendations?","TrustedAdvisorFullAccess","ReadOnlyAccess","AdministratorAccess","IAMFullAccess","The `TrustedAdvisorFullAccess` IAM policy provides the necessary permissions to view all Trusted Advisor recommendations."
"What is a key difference between AWS Trusted Advisor and AWS Config?","Trusted Advisor provides recommendations, while AWS Config tracks configuration changes.","Trusted Advisor is a free service, while AWS Config is a paid service.","Trusted Advisor is used for security only, while AWS Config is used for compliance only.","Trusted Advisor provides recommendations, while AWS Config enforces them.","Trusted Advisor provides recommendations based on best practices. AWS Config tracks configuration changes and enables you to assess, audit, and evaluate the configurations of your AWS resources."
"What is the scope of AWS Trusted Advisor checks?","Checks are performed at the account level, across all regions.","Checks are performed at the region level, within a specific account.","Checks are performed at the VPC level, within a specific region.","Checks are performed at the instance level.","Trusted Advisor checks are performed at the account level, across all regions, providing a global view of your AWS environment."
"What action should you take if AWS Trusted Advisor flags an underutilised EC2 instance?","Consider resizing the instance to a smaller instance type or terminating it.","Increase the instance size to improve performance.","Ignore the recommendation, as instance utilisation fluctuates.","Purchase additional Reserved Instances.","Underutilised EC2 instances represent a potential cost saving. Consider resizing the instance to a smaller type or terminating it if it is not needed."
"What is the purpose of the AWS Trusted Advisor 'Performance' check category?","To identify opportunities to improve the speed and responsiveness of your AWS resources.","To identify security vulnerabilities in your AWS account.","To identify cost optimisation opportunities.","To identify fault tolerance issues in your AWS infrastructure.","The Performance check category aims to identify potential performance bottlenecks and opportunities to improve the speed and responsiveness of your resources."
"What are the differences between AWS Trusted Advisor Core Checks and Full Checks?","Core Checks are available to all AWS customers while Full Checks are available for Business and Enterprise Support customers.","Core Checks focus on cost while Full Checks focus on security.","Core Checks are manual while Full Checks are automatic.","Core Checks are limited to one region while Full Checks are global.","Core Checks are a subset of checks available to all AWS users. Full Checks are only available to users with Business or Enterprise support plans."
"Which of the following is a benefit of regularly reviewing AWS Trusted Advisor recommendations?","Helps you optimise your AWS infrastructure for cost, performance, and security.","Automates the deployment of new AWS resources.","Automatically enforces compliance policies.","Replaces the need for manual security audits.","Regularly reviewing and acting on Trusted Advisor recommendations helps you optimise your AWS infrastructure for cost, performance, and security, ensuring you are following best practices."
"A customer needs to identify unused EBS volumes. Which AWS Trusted Advisor check helps with this?","Idle Load Balancers","Underutilised EC2 Instances","Unassociated Elastic IP Addresses","Idle EBS Volumes","The Idle EBS Volumes check within the Cost Optimisation category identifies volumes that are not attached to any instances and have had minimal I/O activity, indicating they may be unused."
"What does AWS Trusted Advisor recommend regarding Route 53 DNS records?","Check for high TTL values.","Check for low TTL values.","Check for SPF and DKIM Records","Check for high domain registration costs.","Trusted Advisor makes recommendations for improving Route 53 DNS records, including verifying SPF and DKIM records for better email deliverability and security."
"What is the best approach for remediating a security recommendation from AWS Trusted Advisor regarding overly permissive security groups?","Modify the security group rules to restrict access to only necessary ports and IP addresses.","Delete the security group and create a new one with default settings.","Ignore the recommendation if you believe the security group configuration is necessary.","Disable the affected EC2 instance.","The correct approach is to modify the existing security group rules to restrict access to only the necessary ports and IP addresses, following the principle of least privilege."
"You want to automate the process of checking AWS Trusted Advisor recommendations and taking action on them. Which AWS service or combination of services can help with this?","AWS Support API, AWS Lambda, and Amazon CloudWatch Events.","AWS CloudTrail and AWS Config.","AWS Systems Manager and AWS CloudFormation.","Amazon S3 and Amazon EC2.","Using the AWS Support API, you can programmatically access Trusted Advisor recommendations. AWS Lambda can be used to execute code in response to CloudWatch Events triggered by changes in the Trusted Advisor results, enabling automated remediation."
"How does AWS Trusted Advisor contribute to improving the resilience of an application?","By identifying single points of failure and recommending improvements to fault tolerance.","By automatically scaling resources based on demand.","By providing real-time monitoring of application performance.","By automating the deployment of new application versions.","Trusted Advisor's Fault Tolerance checks help identify single points of failure, such as single EC2 instances or databases without replication, and recommends improvements to increase the resilience of your application."
"What is the primary benefit of using AWS Trusted Advisor to identify unassociated Elastic IP addresses?","Reduce costs by releasing unused IP addresses.","Improve the performance of your applications.","Enhance the security of your AWS infrastructure.","Simplify your AWS deployments.","Unassociated Elastic IP addresses incur charges even when they are not being used. Trusted Advisor helps you identify and release these addresses to reduce costs."
"Which of the following AWS services can be integrated with AWS Trusted Advisor to provide additional insights or automate actions?","AWS CloudWatch Events, AWS Lambda, and AWS Systems Manager.","AWS Glue, Amazon EMR, and AWS Athena.","AWS CodePipeline, AWS CodeBuild, and AWS CodeDeploy.","Amazon SageMaker, Amazon Comprehend, and Amazon Rekognition.","AWS CloudWatch Events can trigger AWS Lambda functions based on changes in Trusted Advisor results, and AWS Systems Manager can be used to automate remediation tasks based on Trusted Advisor recommendations."
"What kind of actions can you take once you have an AWS Trusted Advisor security warning?","Review and update security group rules, IAM policies, or other security configurations.","Terminate EC2 instances that are flagged as insecure.","Disable the AWS Trusted Advisor service.","Delete the AWS account.","Once Trusted Advisor flags a security issue, you need to review the underlying configurations, such as security group rules or IAM policies, and update them to address the vulnerability."
"How does AWS Trusted Advisor contribute to the security pillar of the AWS Well-Architected Framework?","By providing recommendations on IAM, security groups, and other security-related configurations.","By automatically encrypting data at rest and in transit.","By providing real-time threat detection and prevention.","By automatically patching operating systems and applications.","Trusted Advisor provides specific security recommendations related to IAM, security groups, open ports and services, S3 bucket permissions and other security-related configurations, which directly contribute to the security pillar."
"You are managing multiple AWS accounts within an AWS Organisation. How can you use AWS Trusted Advisor to get a consolidated view of recommendations across all accounts?","Enable Trusted Advisor Organisational View and consolidate all trusted advisor data into the management account.","You must log in to each individual account to view Trusted Advisor recommendations.","Trusted Advisor does not support AWS Organisations.","Create a custom dashboard that aggregates data from all accounts.","Trusted Advisor Organisational View is a feature that allows you to view a consolidated summary of Trusted Advisor check results across all accounts within your AWS Organisation from the management account."
"Which of the following are valid recommendations from AWS Trusted Advisor about your AWS infrastructure?","Check for underutilised instances.","Remove all public access from S3 buckets.","Run applications on the latest generation hardware.","Disable root account access.","Trusted Advisor has cost optimisation checks for underutilised EC2 instances as they can lead to wasted resources and costs."
"What impact does an AWS account having a 'red flag' in AWS Trusted Advisor imply?","Urgent action is required due to a high-risk issue.","No impact is applied, the account is working normally.","The account is temporarily blocked.","The account is migrated to a new region.","A 'red flag' indicates a high-risk issue that requires immediate attention to mitigate potential risks or vulnerabilities."
"If AWS Trusted Advisor recommends that you resize an EC2 instance, what factors should you consider before taking action?","Current CPU utilisation, memory usage, and network I/O.","The cost of resizing the instance.","The instance's uptime.","The region the instance is running in.","When Trusted Advisor recommends resizing an EC2 instance, consider the instance's resource utilisation metrics, such as CPU, memory and network I/O, to ensure the new instance type meets the application's needs."
"What are the potential benefits of following AWS Trusted Advisor's performance recommendations?","Reduced latency, increased throughput, and improved user experience.","Lower AWS costs, enhanced security, and simplified management.","Increased data durability, improved compliance, and automated deployments.","Reduced storage costs, enhanced networking, and automated backups.","Following performance recommendations can result in reduced latency, increased throughput, and an overall improved user experience for your applications."
"If you exclude an AWS Trusted Advisor recommendation, can you later re-enable it?","Yes, excluded items can be re-enabled at any time.","No, excluded items are permanently hidden.","Excluded items are automatically re-enabled after 30 days.","Excluded items are re-enabled when you upgrade your AWS Support plan.","Yes, excluded items can be re-enabled through the Trusted Advisor console, allowing you to revisit previously addressed issues."
"What is the main advantage of using AWS Trusted Advisor for security checks compared to manually auditing your AWS environment?","Automated and continuous monitoring, providing real-time insights and recommendations.","Faster deployment of resources.","Lower AWS support costs.","More granular control over security configurations.","Trusted Advisor provides automated and continuous monitoring, offering real-time insights and recommendations, which are difficult to replicate with manual audits."
"How does AWS Trusted Advisor help with managing service limits?","By identifying services that are approaching their limits and providing guidance on requesting increases.","By automatically increasing service limits when they are reached.","By providing detailed documentation on all AWS service limits.","By enforcing service limits and preventing users from exceeding them.","Trusted Advisor monitors service limits and alerts you when you are approaching a limit, providing guidance on how to request a service limit increase, preventing unexpected issues."
"You receive an AWS Trusted Advisor notification that an S3 bucket has 'Everyone' access. What action should you take?","Restrict access to the bucket by using IAM policies and bucket policies.","Ignore the notification if the data in the bucket is not sensitive.","Delete the bucket and recreate it with default settings.","Encrypt the data in the bucket using server-side encryption.","When an S3 bucket has 'Everyone' access, it's crucial to restrict access using IAM policies and bucket policies to ensure only authorised users can access the data."
"You are using AWS Trusted Advisor to monitor your RDS instances. What specific security checks are performed?","Security Group access.","CPU utilisation.","Storage capacity.","Backup schedule.","Trusted Advisor performs security checks for RDS instances, focusing on security group rules to ensure the database is not publicly accessible."
"How can you use AWS Trusted Advisor to reduce your AWS costs associated with underutilised resources?","Identify idle or underutilised EC2 instances, EBS volumes, and load balancers, then resize or terminate them.","Automatically purchase Reserved Instances for all EC2 instances.","Automatically move infrequently accessed data to Glacier storage.","Reduce the size of all S3 buckets.","Trusted Advisor identifies idle and underutilised resources. You can then resize or terminate these resources to reduce costs, ensuring you are not paying for unused capacity."
"Which AWS Trusted Advisor category focuses on improving the design of your workload to withstand failures?","Fault Tolerance","Security","Cost Optimisation","Performance","The Fault Tolerance category in AWS Trusted Advisor helps you improve the resilience of your workload by identifying single points of failure and recommending improvements to withstand failures."
"What is the best way to ensure that your AWS Trusted Advisor checks are up-to-date?","Refresh the checks manually on a regular basis.","Schedule automatic daily check refreshes.","The checks are automatically refreshed hourly.","The checks are only refreshed when you change your AWS Support plan.","AWS Trusted Advisor checks can be refreshed manually. It is not automatically refreshed hourly."
"Which AWS Trusted Advisor category focuses on reducing AWS spending?","Cost Optimisation","Security","Performance","Fault Tolerance","The Cost Optimisation category provides recommendations to reduce AWS spending."
"What is the primary purpose of AWS Trusted Advisor?","To provide best practice recommendations for your AWS environment","To monitor network traffic","To manage IAM users and roles","To deploy applications to EC2 instances","Trusted Advisor helps you follow AWS best practices by inspecting your AWS environment."
"Which AWS service is most directly integrated with AWS Trusted Advisor to provide cost insights?","AWS Cost Explorer","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Cost Explorer provides detailed cost insights that complement Trusted Advisor's recommendations."
"In AWS Trusted Advisor, what does a 'red' status indicator signify for a check?","An issue requires immediate attention","The check is disabled","The check is running","The check is informational only","A red status indicates a high-priority issue that should be addressed immediately."
"Which AWS Trusted Advisor check identifies EC2 instances with low utilisation?","Underutilised Amazon EC2 Instances","Idle Load Balancers","Unassociated Elastic IP Addresses","Large RDS Instance Sizes","The 'Underutilised Amazon EC2 Instances' check specifically identifies EC2 instances that may be oversized for their current workload."
"What type of data does the AWS Trusted Advisor 'Security Groups - Specific Ports Unrestricted' check examine?","Inbound security group rules","Outbound security group rules","Network ACLs","VPC Peering configurations","This check specifically looks at inbound security group rules that allow unrestricted access to specific ports, posing a security risk."
"How frequently does AWS Trusted Advisor refresh its checks?","Checks refresh periodically based on the check type","Checks refresh only when manually triggered","Checks refresh daily","Checks refresh hourly","Trusted Advisor checks are refreshed automatically. The refresh interval depends on the check."
"Which AWS Trusted Advisor check is related to potential savings on EBS volumes?","Idle EBS Volumes","Underutilised Amazon EC2 Instances","Unassociated Elastic IP Addresses","Low Utilisation RDS Instances","Idle EBS Volumes are EBS volumes that are not attached to an EC2 instance and incurring costs needlessly."
"Which AWS Trusted Advisor check category helps you improve the speed and responsiveness of your applications?","Performance","Security","Cost Optimisation","Fault Tolerance","The Performance category provides recommendations to improve the speed and responsiveness of your AWS applications."
"If you have AWS Business or Enterprise Support, what level of access do you have to Trusted Advisor checks?","Full access to all checks","Access to only the core security checks","Access to a limited set of checks","No access to any checks","Business and Enterprise Support plans receive full access to all Trusted Advisor checks."
"What AWS service allows you to automate remediation actions based on AWS Trusted Advisor recommendations?","AWS Systems Manager","AWS CloudTrail","AWS IAM","AWS Config","AWS Systems Manager can be used to automate remediation actions based on Trusted Advisor recommendations, improving operational efficiency."
"Which AWS Trusted Advisor check can help identify RDS instances that are running on a previous generation engine version?","RDS DB Instances Running Previous Generation Engine Versions","Idle Load Balancers","Unassociated Elastic IP Addresses","Underutilised Amazon EC2 Instances","This check specifically identifies RDS instances that should be upgraded to newer engine versions for performance and security reasons."
"Which AWS Trusted Advisor check can help you find Elastic Load Balancers that are not actively routing traffic?","Idle Load Balancers","Public EC2 Instances","Unassociated Elastic IP Addresses","Underutilised Amazon EC2 Instances","Idle Load Balancers incur costs without providing any value, which this check identifies."
"What is a characteristic of the 'Fault Tolerance' category in AWS Trusted Advisor?","It focuses on ensuring that your applications remain available and resilient","It focuses on reducing your AWS spending","It focuses on improving the performance of your applications","It focuses on enhancing the security of your AWS resources","The Fault Tolerance category provides recommendations to help you build more resilient and highly available applications."
"What is the purpose of the AWS Trusted Advisor Preference settings?","To customise which checks are displayed and how they are reported","To change the refresh frequency of checks","To configure IAM permissions for Trusted Advisor","To modify the AWS Region in which checks are performed","Preference settings allow you to customise the Trusted Advisor dashboard and the types of checks that are displayed."
"Which AWS Trusted Advisor check identifies public EC2 instances that should ideally be in a private subnet?","Public EC2 Instances","High Utilisation Amazon EC2 Instances","Idle Load Balancers","Unassociated Elastic IP Addresses","This check helps identify and mitigate security risks associated with exposing EC2 instances directly to the public internet."
"Which statement is true about the 'Security' category in AWS Trusted Advisor?","It focuses on identifying security vulnerabilities and misconfigurations","It focuses on improving the performance of your applications","It focuses on reducing your AWS spending","It focuses on ensuring that your applications remain available and resilient","The Security category helps you identify potential security risks and misconfigurations in your AWS environment."
"Which AWS Trusted Advisor check identifies S3 buckets that have open access permissions?","S3 Bucket Permissions","Idle Load Balancers","Unassociated Elastic IP Addresses","Underutilised Amazon EC2 Instances","S3 Bucket Permissions check is an important security control to prevent unintended data exposure."
"What does the AWS Trusted Advisor 'Service Limits' check monitor?","Your usage of AWS services against your account limits","Network latency between AWS Regions","The amount of free storage on your EC2 instances","The number of failed API calls","The 'Service Limits' check helps you avoid being throttled or encountering unexpected issues due to exceeding service limits."
"What is the best way to access AWS Trusted Advisor?","Through the AWS Management Console","Through the AWS CLI only","Through the AWS SDK only","Through the AWS Marketplace","Trusted Advisor can be accessed through the AWS Management Console, providing a user-friendly interface for reviewing recommendations."
"Which AWS Trusted Advisor feature allows you to exclude specific resources from certain checks?","Suppressed Items","Excluded Accounts","Disabled Checks","Ignored Warnings","The 'Suppressed Items' feature allows you to exclude resources from specific checks if you have a valid reason."
"Which AWS Trusted Advisor check relates to EBS volumes that are not utilising Provisioned IOPS?","Optimised EBS Volumes","Idle Load Balancers","Unassociated Elastic IP Addresses","Underutilised Amazon EC2 Instances","Optimised EBS Volumes identifies volumes that could benefit from Provisioned IOPS or could be optimised based on usage patterns."
"What is the relationship between AWS Organisations and AWS Trusted Advisor?","Trusted Advisor can aggregate checks across multiple accounts in an organisation","Trusted Advisor is not compatible with AWS Organizations","Trusted Advisor can only run checks on the management account","Trusted Advisor checks are only visible at the individual account level","With AWS Organizations, Trusted Advisor can aggregate checks across multiple accounts, providing a centralised view of best practice adherence."
"Which AWS Trusted Advisor check helps identify security groups that allow unrestricted access to port 22?","Security Groups - Specific Ports Unrestricted","Security Groups - All Ports Open","Security Groups - Redundant Rules","Security Groups - Excessive Rules","Allowing unrestricted access to port 22 (SSH) poses a significant security risk."
"Which AWS Trusted Advisor check helps you discover Load Balancers configured with listeners that are not actively processing requests?","Idle Load Balancers","Public EC2 Instances","Unassociated Elastic IP Addresses","Underutilised Amazon EC2 Instances","Idle Load Balancers incur costs without providing any value, which this check identifies."
"You have recently enabled MFA on your root account, How quickly will AWS Trusted Advisor pick this up?","Dependent on the check, it will be reflected during the next refresh cycle","Once a month","Instantly","After 24 hours","The check will be updated during the next refresh cycle."
"AWS Trusted Advisor best practices are based on which framework?","AWS Well-Architected Framework","ITIL","COBIT","ISO 27001","AWS Trusted Advisor recommendations are based on the five pillars of the AWS Well-Architected Framework."
"A customer wants to know if they can automatically refresh the Trusted Advisor checks. What is the correct answer?","Yes, if the customer has a Business or Enterprise Support plan, they can use the API or the AWS console to refresh the checks","The checks are only refreshed once a week","The checks are refreshed every day, there is no option to refresh the checks manually","The checks are only refreshed at the end of the month","The checks can be manually refreshed on demand with a Business or Enterprise Support plan or they run on a recurring schedule."
"Which AWS Trusted Advisor category focuses on the design and implementation of highly available and fault-tolerant systems?","Fault Tolerance","Security","Performance","Cost Optimisation","The Fault Tolerance category is designed to provide customers with best practices for designing and implementing systems that are highly available and fault-tolerant."
"Which AWS Trusted Advisor check can assist in identifying IAM users who have not used their credentials recently?","IAM Use","Security Groups - Specific Ports Unrestricted","Underutilised Amazon EC2 Instances","S3 Bucket Permissions","IAM Use helps identify and clean up unused IAM credentials, improving the security posture of your AWS account."
"What happens if you ignore a Trusted Advisor recommendation?","You can suppress the item to remove it from the dashboard","The AWS account is suspended","The recommendation is automatically fixed","The recommendation is escalated to AWS Support","You can suppress (hide) the item from your Trusted Advisor dashboard to acknowledge and manage it later if needed."
"What AWS Trusted Advisor benefit can customers achieve by leveraging its best practice checks?","Improved security posture","Automated compliance reporting","Reduced network latency","Increased database scalability","By implementing Trusted Advisor recommendations, you can strengthen the security posture of your AWS environment."
"What is the AWS Trusted Advisor check that helps identify underutilised EBS volumes?","Idle EBS Volumes","Underutilised Amazon EC2 Instances","Large RDS Instance Sizes","Public EC2 Instances","Idle EBS Volumes identifies EBS volumes that are not attached to an EC2 instance and may be incurring costs needlessly."
"AWS Trusted Advisor helps you to optimise your infrastructure for performance. Which check can help to identify whether the Amazon EC2 instances have high CPU utilisation?","High Utilisation Amazon EC2 Instances","Public EC2 Instances","Security Groups - Specific Ports Unrestricted","Underutilised Amazon EC2 Instances","High Utilisation Amazon EC2 Instances can help you to identify EC2 instances that may be resource-constrained due to high CPU usage."
"Which AWS Trusted Advisor check assists in identifying overly permissive S3 bucket ACLs?","S3 Bucket Permissions","Security Groups - Specific Ports Unrestricted","Public EC2 Instances","Underutilised Amazon EC2 Instances","S3 Bucket Permissions helps identify buckets with configurations that may allow public access to sensitive data."
"Which of the following AWS support plans provides access to the full set of Trusted Advisor checks?","Enterprise Support","Basic Support","Developer Support","Business Support","Enterprise Support allows access to all Trusted Advisor checks."
"What does AWS Trusted Advisor check in relation to Elastic Load Balancing (ELB)?","Idle Load Balancers","Unassociated Elastic IP Addresses","Security Groups - Specific Ports Unrestricted","Underutilised Amazon EC2 Instances","The Idle Load Balancers checks can help identify load balancers that are not actively routing traffic and are potentially incurring costs unnecessarily."
"Which AWS Trusted Advisor check helps identify RDS instances that are using instance types that are considered too large for their workload?","Large RDS Instance Sizes","Security Groups - Specific Ports Unrestricted","Public EC2 Instances","Underutilised Amazon EC2 Instances","The Large RDS Instance Sizes check identifies RDS instances that are running on larger-than-needed instance types, potentially leading to unnecessary costs."
"Which aspect of the AWS Well-Architected Framework does AWS Trusted Advisor's Cost Optimisation category primarily align with?","Cost Optimisation Pillar","Security Pillar","Performance Efficiency Pillar","Reliability Pillar","The Cost Optimisation pillar of the AWS Well-Architected Framework focuses on running systems to deliver business value at the lowest price point."
"What does it mean to 'suppress' an item in AWS Trusted Advisor?","To hide a specific recommendation from the Trusted Advisor dashboard","To automatically fix the identified issue","To permanently delete the resource from your AWS environment","To disable the Trusted Advisor service entirely","Suppressing an item allows you to hide a specific recommendation from your Trusted Advisor dashboard, indicating that you have acknowledged the issue and are either addressing it separately or deeming it not applicable."
"Which AWS Trusted Advisor check can assist in detecting if root account Multi-Factor Authentication is not enabled?","MFA on Root Account","Security Groups - Specific Ports Unrestricted","Public EC2 Instances","Underutilised Amazon EC2 Instances","MFA on Root Account helps to protect the root user of your AWS account by requiring Multi-Factor Authentication."
"Which AWS Trusted Advisor check assists customers in finding and removing unassociated Elastic IP addresses?","Unassociated Elastic IP Addresses","Public EC2 Instances","Idle Load Balancers","Underutilised Amazon EC2 Instances","The Unassociated Elastic IP Addresses check helps identify Elastic IP addresses that are allocated but not attached to any EC2 instance, incurring charges unnecessarily."
"What is a primary benefit of regularly reviewing AWS Trusted Advisor recommendations?","Continuous optimisation of your AWS environment","Automatic patching of EC2 instances","Real-time threat detection","Simplified IAM policy management","Regularly reviewing and implementing Trusted Advisor recommendations allows for continuous improvement and optimisation of your AWS environment."
"Which AWS Trusted Advisor category helps customers identify and resolve issues related to the availability and resilience of their AWS resources?","Fault Tolerance","Security","Performance","Cost Optimisation","The Fault Tolerance category is designed to provide customers with best practices for designing and implementing systems that are highly available and fault-tolerant."
"What is the primary focus of the 'Performance' category in AWS Trusted Advisor?","Optimising the speed and responsiveness of your AWS applications","Identifying security vulnerabilities in your AWS environment","Reducing your AWS spending through resource optimisation","Ensuring high availability and fault tolerance for your AWS services","The 'Performance' category focuses on optimising the speed, responsiveness, and overall performance of your AWS applications and resources."
"What benefit does Trusted Advisor provide concerning Security Groups?","Highlights Security Groups with overly permissive rules.","Automatically encrypts all Security Group traffic.","Creates Security Groups based on application needs.","Allows Security Groups to bypass NACL rules.","Trusted Advisor identifies security groups with rules that might leave your resources vulnerable to unauthorised access."
"Which of the following is NOT a category of checks offered by AWS Trusted Advisor?","Compliance","Cost Optimisation","Security","Fault Tolerance","Compliance is not one of the primary categories offered by AWS Trusted Advisor."
"What is the primary function of AWS Trusted Advisor?","To provide recommendations for optimising your AWS infrastructure based on best practices.","To provide real-time monitoring of your AWS resources.","To automatically apply security patches to your EC2 instances.","To provide a cost estimate for migrating to AWS.","Trusted Advisor analyses your AWS environment and provides recommendations across cost optimisation, security, fault tolerance, and performance."
"Which of the following is NOT a category of checks performed by AWS Trusted Advisor?","Compliance","Migration","Cost Optimisation","Security","Trusted Advisor focuses on Cost Optimisation, Security, Fault Tolerance, Performance and Service Limits. Migration is not one of the checks performed by Trusted Advisor."
"Which AWS support plan levels provide full access to all Trusted Advisor checks?","Business and Enterprise","Basic and Developer","Developer and Business","Basic and Business","The Business and Enterprise support plans offer full access to all Trusted Advisor checks. Basic and Developer plans have limited checks available."
"When does AWS Trusted Advisor refresh its checks?","Automatically on a daily or weekly basis, depending on the check.","Manually by the user only.","Only when a new service is launched.","Automatically after a major AWS outage.","Trusted Advisor automatically refreshes its checks, typically on a daily or weekly basis, depending on the specific check. Users can also manually refresh some checks."
"What is the purpose of the AWS Trusted Advisor 'Service Limits' check category?","To identify resources that are approaching AWS service limits.","To configure custom service limits for your account.","To request increases in AWS service limits.","To monitor the overall performance of AWS services.","The Service Limits category helps you identify resources that are close to reaching their AWS service limits, allowing you to proactively request increases."
"You receive an AWS Trusted Advisor alert indicating a potential security vulnerability. What should be your first course of action?","Review the recommended actions and implement the suggested remediations.","Immediately shut down all affected resources.","Contact AWS Support to investigate the issue.","Ignore the alert, as Trusted Advisor can sometimes generate false positives.","The most appropriate first action is to review the alert details and follow the guidance to remediate the security vulnerability."
"Which of the following AWS services integrates directly with AWS Trusted Advisor to provide enhanced monitoring and security recommendations?","AWS Security Hub","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Security Hub integrates directly with Trusted Advisor, providing a centralised view of security alerts and compliance status."
"Which AWS Trusted Advisor check category helps you to reduce your AWS spending?","Cost Optimisation","Security","Fault Tolerance","Performance","The Cost Optimisation category in Trusted Advisor helps identify opportunities to reduce AWS spending by suggesting ways to optimise resource usage and avoid unnecessary costs."
"How can you suppress an AWS Trusted Advisor recommendation so that it no longer appears in your dashboard?","By excluding the resource or check.","By deleting the Trusted Advisor service.","By downgrading your AWS support plan.","By disabling the AWS Management Console.","You can exclude resources or checks from the Trusted Advisor dashboard to suppress recommendations that are not relevant or cannot be immediately addressed."
"What is the benefit of using AWS Trusted Advisor to identify underutilised EC2 instances?","Reduced costs by stopping or resizing instances.","Improved application performance.","Enhanced security posture.","Simplified resource management.","Identifying and stopping or resizing underutilised EC2 instances leads to reduced costs."
"What is the primary purpose of AWS Trusted Advisor?","To provide recommendations for optimising your AWS infrastructure.","To automatically enforce security policies across your AWS accounts.","To manage AWS Identity and Access Management (IAM) roles.","To monitor the real-time performance of your applications.","AWS Trusted Advisor analyses your AWS environment and provides best practice recommendations in five categories: cost optimisation, security, fault tolerance, performance, and service limits."
"Which AWS Trusted Advisor category helps you reduce your AWS costs?","Cost Optimisation","Security","Fault Tolerance","Performance","The Cost Optimisation category provides recommendations to reduce costs by identifying unused resources, reserved instance opportunities, and other potential savings."
"If AWS Trusted Advisor identifies an underutilised EC2 instance, what type of recommendation would it provide?","Resize the instance to a smaller instance type.","Terminate the instance.","Upgrade the instance to a larger instance type.","Change the instance's network interface.","If an EC2 instance is underutilised, Trusted Advisor would recommend resizing it to a smaller instance type to reduce costs."
"Which of the following actions can AWS Trusted Advisor perform automatically?","It cannot perform actions automatically.","Terminating unused EC2 instances.","Deleting unused EBS volumes.","Resizing RDS instances.","Trusted Advisor provides recommendations but does not automatically take actions. Users must manually implement the changes."
"How often does AWS Trusted Advisor refresh its checks for AWS Business and Enterprise Support customers?","Every 15 minutes","Every 24 hours","Every 7 days","Every 30 days","AWS Trusted Advisor refreshes its checks every 15 minutes for Business and Enterprise Support customers, providing up-to-date recommendations."
"Which AWS service can you use to automate the execution of Trusted Advisor recommendations?","AWS Systems Manager","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS Systems Manager can be used to automate the execution of Trusted Advisor recommendations by creating runbooks that implement the suggested changes."
"Which AWS Trusted Advisor check category focuses on ensuring that your AWS resources are designed to withstand failures?","Fault Tolerance","Security","Cost Optimisation","Performance","The Fault Tolerance category provides recommendations to improve the resilience and availability of your AWS applications and resources."
"Which AWS Support plan provides full access to all AWS Trusted Advisor checks?","Enterprise Support","Basic Support","Developer Support","Business Support","Enterprise Support provides access to all AWS Trusted Advisor checks."
"You want to exclude a specific resource from an AWS Trusted Advisor check. How can you achieve this?","By suppressing the resource from the check.","By deleting the resource.","By moving the resource to a different AWS region.","By changing the resource's tags.","You can suppress a resource from a Trusted Advisor check to exclude it from the results. This is useful for resources that you are aware of but do not want to be flagged."
"Which of the following is a benefit of using AWS Trusted Advisor?","Proactive identification of potential issues","Automatic patching of security vulnerabilities","Real-time application performance monitoring","Database schema optimisation","Trusted Advisor helps to proactively identify potential issues related to cost optimisation, security, fault tolerance, performance and service limits before they impact your environment."