"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS CloudTrail?","To log API calls made to AWS services","To provide DDoS protection","To manage IAM roles","To monitor network traffic","CloudTrail's core function is to record API calls made within your AWS account, enabling auditing and compliance."
"Which type of event is logged by default when creating an AWS CloudTrail trail?","Management events","Data events for S3 buckets","Data events for Lambda functions","Insight events","Management events, which include operations performed on AWS resources, are logged by default."
"In AWS CloudTrail, what are 'data events'?","Events that record data plane operations performed on resources","Events related to user login attempts","Events that capture infrastructure changes","Events that record the creation of IAM users","Data events specifically track data plane operations on resources like S3 objects or Lambda function invocations."
"Where are AWS CloudTrail logs typically stored?","Amazon S3 bucket","Amazon EBS volume","Amazon RDS database","Amazon EC2 instance store","CloudTrail logs are stored in an Amazon S3 bucket for durability and accessibility."
"What information is NOT typically included in an AWS CloudTrail event?","The source IP address of the API call","The name of the AWS service that was called","The user who made the API call","The content of the data being accessed","CloudTrail does not typically log the content of the data being accessed, focusing instead on the API calls themselves."
"How can you enable encryption for AWS CloudTrail log files?","By configuring server-side encryption (SSE) or KMS encryption on the S3 bucket where logs are stored","By enabling encryption on the CloudTrail trail itself","By using IAM policies to restrict access","By enabling encryption at the EC2 instance level","Encryption for CloudTrail logs is managed through the encryption settings of the S3 bucket where the logs are stored."
"Which AWS service can be used to analyse CloudTrail logs in real-time and gain operational insights?","Amazon CloudWatch Logs","Amazon SQS","Amazon SNS","Amazon Kinesis Data Streams","CloudWatch Logs can be used to ingest and analyse CloudTrail logs in real-time."
"What is the purpose of CloudTrail Insights?","To automatically detect unusual API activity in your AWS account","To encrypt CloudTrail logs at rest","To manage IAM roles and permissions","To provision EC2 instances","CloudTrail Insights helps identify unusual API activity based on historical patterns, assisting in anomaly detection."
"If you want to capture CloudTrail logs for all AWS regions in your account, what should you configure?","A trail that applies to all regions","A separate trail for each region","An IAM role with cross-region access","Cross-account access","Configuring a trail to apply to all regions ensures that logs from all regions are captured in a single S3 bucket."
"What is the default log file format for AWS CloudTrail logs?","JSON","CSV","XML","YAML","CloudTrail logs are stored in JSON format, which is easily parsable and integrates well with other AWS services."
"Which AWS service can be integrated with CloudTrail to provide security information and event management (SIEM) capabilities?","Amazon GuardDuty","Amazon Inspector","AWS Shield","AWS WAF","Amazon GuardDuty is a threat detection service that can analyse CloudTrail logs to identify potential security issues."
"What type of events are 'Insights events' in AWS CloudTrail?","Events related to unusual API call rates or error rates","Events related to network traffic patterns","Events related to database performance","Events related to storage capacity utilization","Insights events are specifically designed to detect unusual API call patterns and error rates within your AWS environment."
"What is the purpose of validating CloudTrail log file integrity?","To ensure that log files have not been tampered with or altered","To improve the performance of CloudTrail","To reduce the storage costs of CloudTrail logs","To automatically back up CloudTrail logs","Validating log file integrity ensures that the logs are authentic and haven't been modified, which is critical for compliance."
"How can you control access to the S3 bucket where CloudTrail logs are stored?","By using IAM policies","By using S3 bucket policies","By using Network ACLs","By using Security Groups","IAM policies and S3 bucket policies are used to control access to the S3 bucket containing CloudTrail logs."
"What is the recommended best practice for storing CloudTrail logs long-term?","Using S3 lifecycle policies to transition logs to Glacier or other archival storage","Storing logs on EBS volumes","Storing logs in RDS databases","Deleting logs after 90 days","S3 lifecycle policies are the most cost-effective way to manage long-term storage of CloudTrail logs by transitioning them to cheaper storage tiers like Glacier."
"How can you share CloudTrail logs with another AWS account?","By configuring cross-account access to the S3 bucket where logs are stored","By creating a read replica of the CloudTrail trail","By sharing the IAM role used by CloudTrail","By granting access to the CloudTrail console","Cross-account access to the S3 bucket allows another account to read the CloudTrail logs."
"Which AWS service can be used to query CloudTrail logs using SQL?","Amazon Athena","Amazon Redshift","Amazon EMR","Amazon Elasticsearch Service","Amazon Athena allows you to query data stored in S3, including CloudTrail logs, using SQL."
"You want to ensure that all API calls made to your AWS account, including those made by the root user, are logged. What should you do?","Enable CloudTrail for all regions and ensure the trail is configured to log management events","Disable the root user","Enable MFA for all users","Create a dedicated IAM user for each task","Enabling CloudTrail for all regions and ensuring management events are logged will capture all API calls, including those made by the root user."
"What is the maximum file size for a CloudTrail log file?","512 MB","1 GB","10 GB","100 GB","The maximum file size for a CloudTrail log file is 512 MB."
"Which AWS IAM permission is required to start logging events with CloudTrail?","cloudtrail:StartLogging","cloudtrail:CreateTrail","cloudtrail:UpdateTrail","cloudtrail:PutEvent","The `cloudtrail:StartLogging` permission is required to start logging events for a CloudTrail trail."
"You need to investigate who changed a specific security group configuration. Which AWS service would you use in conjunction with CloudTrail to determine this?","AWS Config","Amazon Inspector","Amazon GuardDuty","AWS Trusted Advisor","AWS Config can track configuration changes over time and, combined with CloudTrail logs, can pinpoint who made the change."
"What happens if you disable logging on an AWS CloudTrail trail?","CloudTrail stops recording API calls","All previous CloudTrail logs are deleted","The S3 bucket containing CloudTrail logs is automatically deleted","Your AWS account is suspended","Disabling logging on a CloudTrail trail stops the recording of API calls, but existing logs remain in the S3 bucket."
"Which of the following is an example of a 'management event' in AWS CloudTrail?","Creating an EC2 instance","Reading an S3 object","Invoking a Lambda function","Sending a message to an SQS queue","Creating an EC2 instance is a management event because it involves managing AWS resources."
"What is the retention period for CloudTrail logs stored in your S3 bucket by default?","There is no default retention period. You must configure a lifecycle policy.","30 days","90 days","1 year","There is no default retention period for CloudTrail logs. You must configure an S3 lifecycle policy to manage retention."
"How can you be alerted when specific API calls are made in your AWS account, based on CloudTrail logs?","By creating CloudWatch Alarms based on CloudWatch Logs metrics","By creating IAM policies","By creating S3 event notifications","By creating VPC Flow Logs","CloudWatch Alarms based on CloudWatch Logs metrics can be used to trigger alerts based on specific patterns in CloudTrail logs."
"What is the purpose of the CloudTrail log file digest files?","To verify the integrity of the log files","To reduce the size of the log files","To encrypt the log files","To automatically back up the log files","Digest files are used to verify the integrity of the log files, ensuring they haven't been tampered with."
"Which AWS service can be used to centrally manage CloudTrail logs from multiple AWS accounts and regions?","AWS Organizations","Amazon CloudWatch Events","Amazon IAM Access Analyzer","AWS Service Catalog","AWS Organizations allows you to centrally manage CloudTrail logs across multiple AWS accounts and regions within an organisation."
"You want to ensure that CloudTrail logs are delivered to your S3 bucket with KMS encryption. Which type of KMS key should you use?","Customer Managed Key (CMK)","AWS Managed Key (default)","AWS Owned Key","CloudTrail Managed Key","You should use a Customer Managed Key (CMK) to have full control over the encryption key used for CloudTrail logs."
"What is the maximum number of CloudTrail trails you can create per AWS region?","Five","One","Ten","Unlimited","You can create up to five CloudTrail trails per AWS region."
"Which of the following is an example of a 'data event' in AWS CloudTrail?","GetObject on an S3 bucket","Creating an IAM role","Starting an EC2 instance","Updating a security group","GetObject on an S3 bucket is a data event, as it involves data plane operations on a resource."
"How can you ensure that CloudTrail logs are not accidentally deleted from your S3 bucket?","Enable S3 versioning on the bucket and use S3 MFA Delete","Enable S3 encryption","Enable CloudTrail Insights","Enable S3 server access logging","Enabling S3 versioning and MFA Delete provides extra protection against accidental or malicious deletion of logs."
"What does AWS CloudTrail *not* record?","Data within RDS instances","API calls made to AWS services","Identity of the caller","Time of the API call","CloudTrail does not record the data contained within RDS instances."
"You need to track changes made to your AWS KMS keys. How can you do this using CloudTrail?","Configure CloudTrail to log management events, which include KMS key operations","Configure CloudTrail to log data events for KMS","Enable KMS key rotation","Enable KMS key policies","CloudTrail logs KMS key operations as management events."
"If you accidentally delete a CloudTrail trail, what happens?","You stop receiving new logs, but existing logs remain in your S3 bucket","All CloudTrail logs in your S3 bucket are deleted","Your AWS account is suspended","CloudTrail automatically recreates the trail","Deleting a CloudTrail trail stops the delivery of new logs, but existing logs in your S3 bucket remain."
"How can you filter CloudTrail logs to only include events related to a specific IAM user?","By creating a CloudWatch Logs filter based on the userIdentity.arn field","By creating an IAM policy with a condition","By creating an S3 lifecycle rule","By creating a VPC Flow Log","You can create a CloudWatch Logs filter based on the `userIdentity.arn` field to filter events for a specific IAM user."
"What is the recommended approach for sending CloudTrail logs to a central security information and event management (SIEM) system?","Configure CloudTrail to deliver logs to an S3 bucket, and then configure the SIEM to ingest logs from the bucket","Configure CloudTrail to directly send logs to the SIEM system","Use the CloudTrail console to export logs to the SIEM system","Forward CloudTrail logs to SQS","The recommended approach is to deliver logs to an S3 bucket and then configure the SIEM system to ingest them from the bucket."
"Which of the following is an advantage of using CloudTrail Insights?","It can help identify unusual API activity and potential security threats","It can automatically encrypt CloudTrail logs","It can automatically back up CloudTrail logs","It can improve the performance of your AWS services","CloudTrail Insights helps identify unusual API activity and potential security threats."
"You want to ensure that all CloudTrail logs are encrypted at rest. Which of the following options is the MOST secure?","Use KMS-managed encryption with a customer-managed key (CMK)","Use server-side encryption with S3-managed keys (SSE-S3)","Use client-side encryption","Disable encryption","Using KMS-managed encryption with a CMK is the most secure option, as it gives you full control over the encryption key."
"You need to monitor API calls made by a specific AWS service principal (e.g., ec2.amazonaws.com). How can you achieve this using CloudTrail?","Filter CloudTrail logs using the `userIdentity.principalId` field in CloudWatch Logs","Create an IAM role with specific permissions","Configure VPC Flow Logs","Enable CloudTrail Insights","You can filter CloudTrail logs using the `userIdentity.principalId` field in CloudWatch Logs to monitor API calls made by a specific service principal."
"Which action does NOT generate CloudTrail logs?","Accessing data within an EC2 instance's local storage","Creating an IAM user","Deleting an S3 bucket","Launching an EC2 instance","Accessing data within an EC2 instance's local storage will not be registered in CloudTrail."
"Which of the following is a valid use case for configuring multiple CloudTrail trails?","To store logs in different S3 buckets with different access controls","To improve the performance of CloudTrail","To reduce the cost of CloudTrail","To automatically back up CloudTrail logs","Multiple trails can be used to store logs in different S3 buckets with different access controls, catering to different compliance or security needs."
"What is the best way to ensure CloudTrail logs are not inadvertently modified after they are delivered to an S3 bucket?","Enable S3 Object Lock in governance mode on the S3 bucket","Enable S3 versioning on the S3 bucket","Use IAM policies to restrict access to the S3 bucket","Enable S3 server access logging","Enabling S3 Object Lock in governance mode ensures logs cannot be modified or deleted during the retention period."
"You need to quickly search for specific events within your CloudTrail logs. Which AWS service provides the fastest query performance?","Amazon Athena","Amazon CloudWatch Logs Insights","Amazon Redshift","Amazon ES (Elasticsearch Service)","Amazon CloudWatch Logs Insights is designed for fast interactive queries on log data."
"What happens to CloudTrail logs when an AWS account is closed?","CloudTrail logs remain in the S3 bucket until the bucket is deleted, and the logs are accessible as long as the bucket exists.","CloudTrail logs are automatically deleted from the S3 bucket.","The S3 bucket containing CloudTrail logs is automatically deleted.","AWS suspends the access to the logs after 30 days","The CloudTrail logs will remain in the S3 bucket until the bucket is deleted. The logs remain accessible as long as the S3 bucket remains intact."
"Which CloudTrail event type can you configure to log all read and write events to a specific S3 bucket?","Data events","Management events","Insight events","S3 events","Data events will log the read and write operations performed in S3 Buckets."
"When considering cost optimisation for storing CloudTrail logs, what S3 storage class is most suitable for infrequent access?","S3 Glacier Deep Archive","S3 Standard","S3 Intelligent-Tiering","S3 Standard Infrequent Access","S3 Glacier Deep Archive is designed for very long term storage and infrequent access with a low cost."
"How to ensure continuous log file integrity in AWS CloudTrail?","Enable log file validation","Enable encryption on the S3 bucket","Use IAM policies","Configure cross-account access","Enable log file validation in CloudTrail to generate digest files that can be used to verify the integrity of the log files after they are delivered."
"Where can you create CloudWatch Alarms based on specific events in the CloudTrail Logs?","Amazon CloudWatch","AWS Config","AWS IAM","Amazon S3","You create CloudWatch alarms in Amazon CloudWatch based on metrics found in the Cloudtrail logs."
"Your company needs to meet certain compliance regulations which include immutability requirements for the log data. What solution can you propose to meet these requirements?","S3 Object Lock with WORM","S3 Versioning","Cloudtrail Insights","Cloudwatch Metric Filter","The Write Once Read Many (WORM) capability of S3 Object Lock satisfies the immutability aspect of many regulatory compliance controls."
"A company wants to proactively monitor and detect unusual activity with their S3 buckets. Which AWS CloudTrail feature will help satisfy this requirement?","CloudTrail Insights","CloudTrail Lake","CloudTrail Trails","CloudWatch Insights","CloudTrail Insights helps detect unusual API activities based on anomalies in the S3 buckets."
"What is the primary purpose of AWS CloudTrail?","Auditing and compliance","Cost optimisation","Performance monitoring","Content delivery","CloudTrail's primary purpose is to track user activity and API calls, enabling auditing and compliance."
"Which AWS service integrates directly with CloudTrail to provide threat detection?","Amazon GuardDuty","AWS Config","Amazon Inspector","Amazon Macie","Amazon GuardDuty integrates with CloudTrail to analyse CloudTrail logs for malicious activity and security threats."
"What type of events does AWS CloudTrail log?","API calls made to AWS services","Network traffic","CPU utilisation","Memory usage","CloudTrail logs API calls made to AWS services, capturing who made the call, the resource accessed, and the time of the call."
"By default, where are AWS CloudTrail logs stored?","In an S3 bucket you specify","In CloudWatch Logs","In Glacier","In EBS volume","CloudTrail logs are stored in an S3 bucket that you configure. You define the S3 bucket when you create your trail."
"What is the purpose of enabling 'Log file validation' in AWS CloudTrail?","To verify the integrity of the log files","To encrypt log files","To compress log files","To delete old log files","Log file validation uses digital signatures to verify that log files haven't been altered after delivery to the S3 bucket."
"What is the minimum retention period for AWS CloudTrail logs?","There is no minimum retention period","7 days","30 days","90 days","There is no minimum retention period for CloudTrail logs. You control the retention period via the S3 bucket lifecycle policies."
"Which AWS service can be used to analyse AWS CloudTrail logs using SQL-like queries?","Amazon Athena","Amazon QuickSight","Amazon Redshift","Amazon EMR","Amazon Athena can query data in S3 using standard SQL, making it useful for analysing CloudTrail logs."
"What is the difference between a Management event and a Data event in AWS CloudTrail?","Management events relate to control plane operations, while data events relate to data plane operations.","Management events are free, while data events incur costs.","Management events are stored in CloudWatch Logs, while data events are stored in S3.","Management events are enabled by default, while data events are disabled by default.","Management events provide information about control plane operations performed on resources, while data events provide insights into data plane operations performed on or within resources."
"What is the purpose of a CloudTrail Lake?","A managed audit and security lake","A cost optimisation tool","A performance monitoring dashboard","A content delivery network","CloudTrail Lake enables you to aggregate, immutably store, and analyse audit and security logs for your cloud and on-premises environments."
"How can you receive real-time notifications about specific events logged by AWS CloudTrail?","By configuring Amazon CloudWatch Alarms","By setting up SNS notifications for S3 bucket events","By subscribing to the AWS Personal Health Dashboard","By enabling AWS Trusted Advisor checks","You can configure CloudWatch Alarms to monitor CloudTrail log files and send notifications when specific events occur."
"Which AWS IAM permission is required to view AWS CloudTrail logs in an S3 bucket?","s3:GetObject","cloudtrail:LookupEvents","cloudtrail:GetTrail","iam:PassRole","The `s3:GetObject` permission allows you to download and view the log files stored in the S3 bucket."
"What is the maximum number of trails you can create in each AWS region?","Unlimited","One","Five","Ten","There is no longer a limit for the number of trails you can create per region."
"You need to ensure that all API calls to your AWS account are logged. What is the best way to achieve this using AWS CloudTrail?","Create an organisation trail","Enable global service logging in a single region","Create a separate trail in each region","Enable data events for all S3 buckets","An organisation trail applies to all accounts within your organisation and logs all API calls."
"What is the purpose of the 'Include global service events' setting in AWS CloudTrail?","To log events from global services like IAM and CloudFront","To log events from all regions","To log events from all AWS accounts","To log data plane events","Enabling 'Include global service events' ensures that events from global services are logged, even if the trail is created in a specific region."
"Which of the following is NOT a valid use case for AWS CloudTrail?","Debugging application errors","Security incident response","Compliance auditing","Tracking infrastructure changes","CloudTrail is primarily used for auditing and tracking infrastructure changes. While it can provide some context, debugging application errors is not its primary function."
"What is the scope of a single AWS CloudTrail trail when created without being configured for all regions?","The AWS Region where the trail was created","The entire AWS account","All AWS Regions in the world","A specific resource or service","A non-global trail only captures events within the region it was created."
"Which AWS service can you use to encrypt your CloudTrail logs at rest in S3?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","AWS Key Management Service (KMS) allows you to encrypt your CloudTrail logs at rest."
"Which of the following is NOT a valid destination for CloudTrail logs?","Amazon S3 bucket","Amazon CloudWatch Logs","Amazon Glacier","AWS Lake Formation","CloudTrail can deliver logs to S3 and CloudWatch Logs but not directly to Glacier or Lake Formation."
"What type of information is contained in a CloudTrail event record?","Timestamp, user identity, API call, and resource affected","CPU utilisation, memory usage, and network traffic","Application logs and error messages","Database queries and results","CloudTrail event records capture details about API calls made to AWS services."
"Which AWS CLI command is used to retrieve CloudTrail events?","aws cloudtrail lookup-events","aws cloudtrail get-trail-status","aws cloudtrail describe-trails","aws cloudtrail start-logging","The `aws cloudtrail lookup-events` command is used to retrieve CloudTrail events based on specific criteria."
"How do you control access to the S3 bucket containing AWS CloudTrail logs?","Using IAM policies attached to the bucket","Using AWS CloudTrail policies","Using S3 access control lists (ACLs)","Using AWS Organizations policies","IAM policies attached to the S3 bucket control who can access the logs."
"What happens to AWS CloudTrail logs when you delete a trail?","The logs remain in the S3 bucket until you manually delete them.","The logs are automatically deleted from the S3 bucket.","The logs are moved to Amazon Glacier for archival.","The logs are moved to CloudWatch Logs.","Deleting the trail stops future logging, but the existing logs in the S3 bucket are not automatically deleted."
"You need to create a long-term archive of your AWS CloudTrail logs. What is the recommended approach?","Configure an S3 lifecycle policy to move logs to Glacier","Download the logs and store them on-premises","Disable logging and rely on historical data","Use AWS Backup","An S3 lifecycle policy can automatically move older logs to Glacier for cost-effective long-term storage."
"What is the purpose of the 'global service events' feature in CloudTrail?","To log events from global services such as IAM, CloudFront, and Route 53.","To log events across all regions in an AWS account.","To log events from all AWS services.","To log data plane events.","The 'global service events' feature allows you to log events from global services, which are not tied to a specific AWS region."
"Which AWS CloudTrail event selector setting enables you to filter events based on resource ARNs?","resourceNames","eventName","userIdentity","readOnly","The `resourceNames` field in an event selector allows you to filter events based on the ARNs of specific resources."
"You want to monitor all modifications to IAM policies in your AWS account. Which AWS CloudTrail event type should you focus on?","Management events","Data events","Insight events","CloudWatch events","IAM policy modifications are control plane operations, which are captured as Management events."
"What does AWS CloudTrail Insights provide?","Analysis of unusual activity in your AWS account","Real-time monitoring of EC2 CPU utilization","Cost optimization recommendations","Vulnerability scanning","CloudTrail Insights helps identify unusual activity by detecting unusual API call rates and error rates."
"Where do you configure AWS CloudTrail Insights?","In the AWS CloudTrail console, under the 'Insights' section","In the Amazon CloudWatch console, under 'Metrics'","In the AWS Config console, under 'Rules'","In the AWS IAM console, under 'Roles'","CloudTrail Insights is configured within the CloudTrail console."
"Which AWS service can be used to centrally manage CloudTrail logs from multiple AWS accounts?","AWS Organizations","AWS IAM","AWS Config","AWS Systems Manager","AWS Organizations allows you to create an organisation trail that logs events for all accounts in your organisation."
"Which of the following is NOT a valid filter criteria when searching for AWS CloudTrail events using the CloudTrail console?","Event name","User name","Source IP address","Memory Utilization","You can filter CloudTrail events by event name, user name and source IP address, but not by memory utilisation."
"What is the purpose of the `cloudtrail:GetEventSelectors` IAM permission?","To allow a user to view the event selectors configured for a CloudTrail trail","To allow a user to create a new CloudTrail trail","To allow a user to delete a CloudTrail trail","To allow a user to start or stop CloudTrail logging","The `cloudtrail:GetEventSelectors` IAM permission allows a user to view the event selectors configured for a CloudTrail trail."
"What is the purpose of the 'IsReadOnly' field in an AWS CloudTrail event record?","To indicate whether the API call modified a resource","To indicate whether the API call was successful","To indicate whether the API call required authentication","To indicate whether the API call was made by a root user","The `IsReadOnly` field indicates whether the API call modified a resource (false) or only retrieved information (true)."
"Which AWS service can be used to trigger automated actions based on AWS CloudTrail events?","Amazon CloudWatch Events (EventBridge)","AWS Lambda","AWS Step Functions","AWS Glue","Amazon CloudWatch Events (EventBridge) can be configured to trigger automated actions based on CloudTrail events."
"You need to ensure that all AWS CloudTrail logs are encrypted at rest. Which of the following is the MOST secure and manageable approach?","Use SSE-KMS encryption with a KMS key you control","Use SSE-S3 encryption","Encrypt the logs manually before uploading them to S3","Use client-side encryption","Using SSE-KMS encryption with a KMS key you control provides the most secure and manageable encryption solution."
"What is the recommended method for sharing AWS CloudTrail logs with an external auditor?","Grant the auditor read-only access to the S3 bucket containing the logs","Email the logs to the auditor","Copy the logs to a public S3 bucket","Print the logs and mail them to the auditor","Granting the auditor read-only access to the S3 bucket using IAM is the recommended approach."
"Which of the following is NOT a supported integration for delivering AWS CloudTrail logs to a third-party SIEM system?","Direct delivery to the SIEM system's API","Using an S3 bucket as an intermediary","Using Amazon Kinesis Data Firehose","Using Amazon SQS","CloudTrail doesn't have native integration for delivering directly to third-party SIEM systems' APIs. It typically involves S3, Kinesis Data Firehose or SQS as an intermediary."
"What is the primary difference between AWS CloudTrail and AWS Config?","CloudTrail logs API calls, while Config tracks resource configurations","CloudTrail monitors performance, while Config monitors security","CloudTrail is free, while Config is paid","CloudTrail encrypts data, while Config validates it","CloudTrail focuses on auditing API activity, while Config focuses on configuration management and compliance."
"You want to ensure that AWS CloudTrail logs are stored in a write-once-read-many (WORM) format. Which S3 feature should you use?","S3 Object Lock","S3 Versioning","S3 Cross-Region Replication","S3 Lifecycle Policies","S3 Object Lock allows you to store objects in a WORM format, ensuring that they cannot be modified or deleted for a specified retention period."
"Which of the following actions will cause CloudTrail to log an event?","An IAM user creates a new EC2 instance","A user reads a file from an S3 bucket","An application writes data to a DynamoDB table","A user logs into the AWS Management Console","Creating a new EC2 instance involves an API call to the EC2 service, which will be logged by CloudTrail."
"What is the purpose of using a custom AWS KMS key for encrypting AWS CloudTrail logs?","To provide more control over the encryption key and access to the logs","To reduce the cost of storing the logs","To improve the performance of log delivery","To enable multi-factor authentication for log access","Using a custom KMS key gives you more control over the key's lifecycle, rotation, and access policies."
"You are troubleshooting an issue and need to identify which IAM role was used to perform a specific action in your AWS account. Where can you find this information?","In the AWS CloudTrail event record for the action","In the AWS IAM console under 'Roles'","In the AWS CloudWatch Logs for the EC2 instance","In the AWS Config console under 'Resource Timeline'","The CloudTrail event record will contain the user identity information, including the IAM role used to perform the action."
"Which of the following AWS CloudTrail event fields contains the name of the API operation that was called?","eventName","eventSource","userIdentity","resourceName","The `eventName` field contains the name of the API operation that was called."
"You need to investigate a security incident and want to identify all API calls made by a specific IAM user over the past week. Which AWS service provides the best tools for this type of analysis?","Amazon Athena","Amazon CloudWatch Logs Insights","AWS Config","AWS Trusted Advisor","Amazon Athena allows you to query CloudTrail logs using SQL-like queries, making it ideal for complex analysis."
"What is the default event selector configuration for a new AWS CloudTrail trail?","Logging management events for all read and write operations.","Logging no events by default.","Logging data events for S3 buckets only.","Logging only read-only management events.","By default, a new trail logs all management events (both read and write operations)."
"You are using AWS CloudTrail to monitor API activity in your AWS account. You notice a large number of `AssumeRole` calls originating from an unknown source. What does this indicate?","A potential security compromise","A normal administrative task","A misconfigured IAM role","A service limit being reached","A large number of `AssumeRole` calls from an unknown source could indicate a potential security compromise, as it suggests someone is attempting to assume different IAM roles."
"Which of the following components of the AWS CloudTrail process is optional?","The S3 bucket used for log storage.","The IAM role used to write to the S3 bucket.","The trail name.","The CloudTrail agent.","While an IAM role is recommended for writing to the S3 bucket in a secure manner, it is technically possible to configure the S3 bucket permissions directly, so it is considered optional."
"What is the primary purpose of AWS CloudTrail?","To log API calls made to your AWS account.","To monitor network traffic.","To store large files.","To manage user permissions.","CloudTrail is designed to log API calls, providing an audit trail of actions taken in your AWS account."
"What type of events does AWS CloudTrail record?","Management and Data events","Compute and Storage events","Networking and Security events","Billing and Cost events","CloudTrail records Management events (control plane operations) and Data events (data plane operations)."
"Where are AWS CloudTrail logs stored by default?","In an S3 bucket that you specify.","In an EBS volume attached to your EC2 instance.","In a CloudWatch Logs group.","In an AWS Secrets Manager secret.","CloudTrail logs are stored in an S3 bucket that you configure."
"What is the default retention period for AWS CloudTrail logs stored in an S3 bucket?","There is no default retention period; logs are retained until you delete them.","7 days","30 days","90 days","CloudTrail does not enforce a retention period. Logs are retained in your S3 bucket until you configure a lifecycle policy or manually delete them."
"Which AWS service can be integrated with AWS CloudTrail to analyse log data?","Amazon Athena","Amazon SQS","Amazon SNS","Amazon Glacier","Amazon Athena can be used to query and analyse CloudTrail log data directly from S3 using SQL."
"What is the purpose of AWS CloudTrail Insights?","To detect unusual API activity in your AWS account.","To optimise costs.","To improve application performance.","To manage IAM roles.","CloudTrail Insights detects unusual API activity based on the historical usage patterns of your account."
"Which of the following is a Management event in AWS CloudTrail?","Launching an EC2 instance.","Reading an object from an S3 bucket.","Executing a Lambda function.","Writing data to a DynamoDB table.","Launching an EC2 instance is a management event, involving control plane operations."
"Which of the following is a Data event in AWS CloudTrail?","Modifying a security group.","Creating an IAM user.","Reading an object from an S3 bucket.","Creating a VPC.","Reading an object from an S3 bucket is a data event, involving data plane operations."
"How can you ensure that AWS CloudTrail logs are not tampered with after they are stored in S3?","By enabling S3 Object Lock.","By encrypting the logs with KMS.","By enabling CloudWatch Alarms.","By using IAM roles to restrict access.","S3 Object Lock helps to prevent CloudTrail logs from being altered or deleted."
"Which AWS service can be used to receive notifications when specific events occur in your AWS CloudTrail logs?","Amazon CloudWatch Events (EventBridge)","Amazon SQS","Amazon SNS","Amazon Lambda","CloudWatch Events (EventBridge) can trigger actions based on events recorded in CloudTrail logs."
"What is the purpose of AWS CloudTrail Lake?","To aggregate, store and query audit and security logs.","To store application code.","To manage VPC configurations.","To host static websites.","CloudTrail Lake allows you to aggregate, immutably store, and query audit and security logs for your AWS resources and non-AWS resources."
"When creating a trail in AWS CloudTrail, what is the scope option 'Apply trail to all regions' used for?","To log events in all AWS regions.","To encrypt logs in all AWS regions.","To create snapshots in all AWS regions.","To manage user access in all AWS regions.","This option ensures that the trail logs events in all AWS regions associated with your AWS account."
"You need to create a multi-account organisation and aggregate all the AWS CloudTrail logs in a central location. How can you achieve this?","Use AWS Organizations to create an organisation trail.","Create a separate trail in each account and manually copy logs.","Use cross-account IAM roles.","Use S3 cross-region replication.","AWS Organizations allows you to create an organisation trail, which automatically logs events for all member accounts in the organisation."
"Which of the following AWS services can be integrated with AWS CloudTrail to visualise the logged data?","Amazon QuickSight","Amazon SQS","Amazon SNS","Amazon Lambda","Amazon QuickSight can be used to visualise and gain insights from CloudTrail log data."
"What does the AWS CloudTrail log file integrity validation feature ensure?","That log files have not been modified or deleted.","That the S3 bucket containing the logs is secure.","That the IAM roles used by CloudTrail are valid.","That the CloudTrail service is running correctly.","This feature validates that the log files have not been tampered with since they were delivered to your S3 bucket."
"What is the benefit of using AWS CloudTrail to track changes made to IAM policies?","It provides an audit trail of who made changes and when.","It automatically enforces IAM policies.","It automatically generates IAM policies.","It provides real-time alerts for IAM policy changes.","CloudTrail provides a record of all API calls, including those made to modify IAM policies, which helps in auditing and compliance."
"Which AWS CloudTrail event selector can be used to filter events based on the resource involved?","Resource ARN","Event Name","User Identity","Source IP Address","You can use the Resource ARN event selector to filter events based on the specific AWS resource that was acted upon."
"You need to track all changes made to your AWS Lambda functions. Which AWS CloudTrail event type should you focus on?","Management events related to Lambda","Data events related to Lambda","All events related to Lambda","Insights events related to Lambda","Management events related to Lambda will capture changes to function configurations and deployments."
"What is the relationship between AWS Config and AWS CloudTrail?","CloudTrail records API calls; Config assesses resource configurations.","CloudTrail stores resource configurations; Config records API calls.","CloudTrail and Config perform the same function.","CloudTrail monitors network traffic; Config monitors CPU utilisation.","CloudTrail records API calls, providing an audit trail; Config assesses resource configurations to ensure they comply with defined rules."
"Which statement accurately describes the pricing model for AWS CloudTrail?","CloudTrail is free for management events, with charges for data events and CloudTrail Lake.","CloudTrail is free for all events.","CloudTrail charges a fixed monthly fee.","CloudTrail charges based on the number of API calls recorded.","CloudTrail is free for management events, with charges for data events and CloudTrail Lake usage."
"You want to use AWS CloudTrail to monitor the deletion of S3 buckets. What type of event would you need to enable?","Management events","Data events","Insights events","Lambda events","Deleting an S3 bucket is a management operation, so you would need to enable Management events."
"How can you control access to AWS CloudTrail logs stored in your S3 bucket?","By using IAM policies to grant permissions to specific users and roles.","By using S3 bucket policies to restrict access based on IP address.","By encrypting the logs with a customer-managed KMS key.","By enabling S3 Object Lock to prevent deletion.","IAM policies are used to control access to S3 buckets and the objects they contain, including CloudTrail logs."
"Which AWS service can be used to perform automated compliance checks based on AWS CloudTrail logs?","AWS Config","Amazon Inspector","Amazon GuardDuty","AWS Trusted Advisor","AWS Config can be used to assess resource configurations against compliance rules based on the events recorded in CloudTrail logs."
"You are investigating a security incident and need to identify all API calls made by a specific IAM user. How can you achieve this using AWS CloudTrail?","By filtering events based on the user identity.","By filtering events based on the source IP address.","By filtering events based on the resource ARN.","By filtering events based on the event name.","CloudTrail allows you to filter events based on the user identity, which helps in identifying actions taken by specific users."
"What is the purpose of AWS CloudTrail Insights events?","To identify unusual API activity patterns in your AWS account.","To provide real-time monitoring of resource utilisation.","To automatically remediate security vulnerabilities.","To optimise costs by identifying underutilised resources.","CloudTrail Insights helps identify unusual API activity, such as error rates or resource provisioning, that might indicate a problem."
"Which of the following is NOT a valid use case for AWS CloudTrail?","Troubleshooting operational issues.","Auditing security and compliance.","Monitoring network latency.","Detecting malicious activity.","CloudTrail is not designed to monitor network latency; it focuses on tracking API calls and changes to resource configurations."
"You need to encrypt your AWS CloudTrail logs at rest. Which service should you use?","AWS Key Management Service (KMS)","Amazon S3 Encryption","AWS Certificate Manager (ACM)","AWS Secrets Manager","AWS KMS is used to encrypt CloudTrail logs at rest, ensuring that the logs are protected."
"Which of the following statements is true regarding AWS CloudTrail and AWS Organizations?","CloudTrail can create an organization trail that logs events for all accounts in the organization.","CloudTrail cannot be used with AWS Organizations.","CloudTrail requires a separate trail in each account of the organization.","CloudTrail automatically creates trails for all accounts in the organization.","CloudTrail can create an organization trail that logs events for all accounts in the AWS Organization."
"What is the primary difference between AWS CloudTrail and Amazon CloudWatch Logs?","CloudTrail tracks API calls, while CloudWatch Logs collects application logs.","CloudTrail collects application logs, while CloudWatch Logs tracks API calls.","CloudTrail is used for security monitoring, while CloudWatch Logs is used for performance monitoring.","CloudTrail is free, while CloudWatch Logs is a paid service.","CloudTrail tracks API calls and resource changes, while CloudWatch Logs collects application and system logs."
"How can you share AWS CloudTrail logs with an external security auditor?","By granting the auditor access to the S3 bucket containing the logs using IAM.","By sending the logs to the auditor's email address.","By storing the logs in a public S3 bucket.","By using AWS Secrets Manager to share the logs.","The best method to share the logs is to use IAM to grant the auditor access to the S3 bucket with appropriate permissions, ensuring secure access."
"Which AWS service provides pre-built dashboards and reports for analysing AWS CloudTrail logs?","Amazon Athena with pre-built SQL queries.","Amazon SQS with pre-built templates.","Amazon SNS with pre-built configurations.","Amazon Lambda with pre-built functions.","Amazon Athena with pre-built SQL queries can be used to analyse CloudTrail logs."
"What is the purpose of the `cloudtrail:LookupEvents` API call?","To retrieve events from the CloudTrail event history.","To create a new CloudTrail trail.","To delete a CloudTrail trail.","To update a CloudTrail trail configuration.","The `cloudtrail:LookupEvents` API call is used to retrieve events from the CloudTrail event history based on specific criteria."
"Which of the following AWS CloudTrail configurations would allow you to log all API activities across all regions in your AWS account?","A trail configured with 'Apply trail to all regions' set to yes.","A trail configured to log only Management events.","A separate trail in each region.","A trail configured with 'Include global service events' set to no.","Setting 'Apply trail to all regions' to 'yes' ensures that the trail logs events across all AWS regions."
"You need to ensure that all new S3 buckets created in your account are automatically monitored by AWS CloudTrail. How can you achieve this?","By configuring CloudTrail to log data events for all S3 buckets.","By creating a CloudWatch event rule to trigger a Lambda function.","By enabling S3 server access logging.","By using S3 Event Notifications.","CloudTrail can be configured to log data events for all S3 buckets, ensuring that all new buckets are automatically monitored."
"Which AWS service can you use to automatically remediate security issues identified through AWS CloudTrail logs?","AWS Security Hub with custom actions.","AWS Config with remediation rules.","Amazon GuardDuty with automated responses.","AWS Trusted Advisor with recommendations.","AWS Security Hub can be used with custom actions triggered by events in CloudTrail."
"What type of information does AWS CloudTrail record about API calls?","The identity of the caller, the time of the call, and the resources affected.","The CPU utilisation of the instance making the call.","The network bandwidth consumed by the call.","The cost of the API call.","CloudTrail records the identity of the caller, the time of the call, the API called, and the resources affected by the call."
"How can you prevent unauthorized users from deleting your AWS CloudTrail logs in S3?","By using IAM policies to restrict access to the S3 bucket.","By encrypting the logs with a customer-managed KMS key.","By enabling S3 versioning.","By enabling S3 Object Lock.","IAM policies are the primary mechanism for controlling access to S3 buckets and objects, including CloudTrail logs."
"What is the main benefit of using AWS CloudTrail Lake over traditional CloudTrail logging to S3?","Centralised data aggregation and querying of logs across multiple accounts and regions.","Automatic encryption of log data at rest.","Lower storage costs for log data.","Real-time monitoring of log data.","CloudTrail Lake provides centralised data aggregation and querying capabilities, allowing you to analyse logs from multiple accounts and regions in one place."
"You want to receive real-time notifications whenever a specific API call is made in your AWS account. Which AWS service should you integrate with AWS CloudTrail?","Amazon CloudWatch Events (EventBridge)","Amazon SNS","Amazon SQS","AWS Lambda","CloudWatch Events (EventBridge) can be configured to trigger notifications based on specific API calls recorded in CloudTrail."
"Which AWS service would you use to investigate a potential security breach using AWS CloudTrail logs?","Amazon Detective","Amazon Inspector","Amazon GuardDuty","AWS Trusted Advisor","Amazon Detective is designed to analyse CloudTrail logs and other data sources to help you investigate security incidents."
"What is the maximum size of an AWS CloudTrail log file?","512MB","1GB","1TB","10GB","The maximum size of a CloudTrail log file is 512MB."
"You are required to retain AWS CloudTrail logs for seven years to meet compliance requirements. What is the recommended approach?","Configure an S3 Lifecycle policy to move logs to Glacier after a certain period.","Manually download and store the logs offline.","Enable S3 versioning and never delete any logs.","Store the logs in EBS volumes for seven years.","An S3 Lifecycle policy allows you to automatically move older logs to a cheaper storage tier like Glacier, ensuring long-term retention while minimising costs."
"What is the primary purpose of the 'Include global service events' option in AWS CloudTrail?","To log events for global services like IAM and CloudFront.","To log events across all AWS regions.","To encrypt logs using a global KMS key.","To store logs in a global S3 bucket.","This option ensures that events for global services, such as IAM and CloudFront, are logged in your CloudTrail logs."
"When troubleshooting an issue with an IAM role, which AWS CloudTrail event would be most helpful?","AssumeRole","GetObject","CreateTable","RunInstances","The AssumeRole event in CloudTrail will show when a role was assumed, by whom, and from where, which is crucial for troubleshooting IAM-related issues."
"You need to ensure that AWS CloudTrail logs are delivered to your S3 bucket even if there are temporary network issues. What feature helps with this?","CloudTrail's built-in retry mechanism.","S3 Event Notifications.","S3 Replication.","CloudWatch Alarms.","CloudTrail has a built-in retry mechanism that ensures logs are delivered to your S3 bucket even if there are temporary network issues."
"You suspect that a malicious actor has gained access to your AWS account and is deleting resources. How can AWS CloudTrail help you confirm and investigate this?","By providing a record of all API calls made to your account, including deletion requests.","By automatically blocking the malicious actor's IP address.","By providing real-time alerts whenever a resource is deleted.","By automatically backing up all your resources before they are deleted.","CloudTrail provides a comprehensive record of all API calls, allowing you to see who made the deletion requests and when."
"Which AWS service can be used to automate the process of setting up AWS CloudTrail across multiple AWS accounts?","AWS CloudFormation","AWS Lambda","AWS Config","AWS Systems Manager","AWS CloudFormation can be used to automate the deployment of CloudTrail configurations across multiple accounts."
"In AWS CloudTrail, what is the purpose of a trail?","To record API calls made within your AWS account","To encrypt data at rest in S3","To monitor network traffic in your VPC","To manage IAM policies","A trail in CloudTrail is configured to record API calls made within your AWS account and deliver the log files to an S3 bucket."
"Which AWS service is primarily used to store the CloudTrail log files?","Amazon S3","Amazon Glacier","Amazon EBS","Amazon RDS","CloudTrail log files are stored in an Amazon S3 bucket that you specify during trail configuration."
"What type of information is NOT captured in CloudTrail logs?","Network packet captures","Identity of the caller","Time of the API call","Request parameters","CloudTrail captures API calls and related events, not network packet captures."
"In AWS CloudTrail, what does event history provide?","A record of the last 90 days of activity without needing to create a trail","Real-time alerts for security threats","Detailed cost analysis of AWS resources","Long-term storage of log files","Event history provides a record of the last 90 days of account activity, even without creating a trail."
"Which AWS service can be integrated with CloudTrail to provide real-time threat detection?","Amazon GuardDuty","Amazon Inspector","AWS Shield","AWS WAF","Amazon GuardDuty can ingest CloudTrail logs to provide real-time threat detection and security monitoring."
"What is the purpose of CloudTrail Insights?","To detect unusual API activity in your AWS account","To automatically encrypt CloudTrail logs","To create custom CloudTrail trails","To manage IAM roles","CloudTrail Insights detects unusual API activity, such as spikes in error rates or calls to delete resources."
"How can you ensure the integrity of CloudTrail log files stored in S3?","By enabling log file integrity validation","By using server-side encryption","By enabling versioning on the S3 bucket","By applying IAM policies","Log file integrity validation uses cryptographic hashing to ensure the log files haven't been tampered with."
"If you need to track API calls across multiple AWS accounts, what is the recommended approach using CloudTrail?","Create an organisation trail","Create separate trails in each account","Use CloudWatch Logs for cross-account tracking","Share IAM roles between accounts","An organisation trail allows you to log events for all accounts within an AWS organisation."
"What is the benefit of integrating CloudTrail with CloudWatch Logs?","Enables real-time monitoring and alerting of API activity","Provides long-term storage of CloudTrail logs","Automatically encrypts CloudTrail logs","Allows you to create custom dashboards","Integrating with CloudWatch Logs allows for real-time monitoring, searching, and alerting based on API activity captured by CloudTrail."
"What type of event is typically NOT logged by CloudTrail?","Data plane operations for S3 buckets (by default)","Management plane operations for EC2 instances","IAM policy changes","RDS database modifications","By default, CloudTrail does not log data plane operations for S3 buckets. You need to enable S3 data events specifically."
"What is the maximum retention period for CloudTrail event history?","90 days","30 days","1 year","Unlimited","CloudTrail event history provides a record of the most recent 90 days of activity in your AWS account."
"You suspect an IAM user has been compromised. Which AWS service can you use with CloudTrail to investigate the user's recent API activity?","CloudTrail","AWS Config","AWS Trusted Advisor","AWS IAM Access Analyzer","CloudTrail records API calls, so you can use it to see what actions the IAM user has taken."
"How do you enable encryption for CloudTrail log files at rest in S3?","Configure server-side encryption on the S3 bucket","Enable encryption in CloudTrail configuration","Use client-side encryption","Encryption is automatically enabled","You configure server-side encryption (SSE) or KMS encryption on the S3 bucket where the logs are stored."
"Which log file format does CloudTrail use for storing events?","JSON","CSV","XML","YAML","CloudTrail stores log files in JSON format."
"What is the purpose of the AWS CLI command `aws cloudtrail describe-trails`?","To retrieve information about existing CloudTrail trails","To create a new CloudTrail trail","To delete an existing CloudTrail trail","To update an existing CloudTrail trail","`aws cloudtrail describe-trails` is used to list and describe existing trails."
"What is the difference between Management events and Data events in CloudTrail?","Management events record control plane operations, while Data events record data plane operations","Management events record data plane operations, while Data events record control plane operations","Management events record security-related events, while Data events record performance-related events","Management events record only read operations, while Data events record only write operations","Management events record control plane operations (e.g., creating an EC2 instance), while Data events record data plane operations (e.g., S3 object access)."
"Which AWS IAM permission is required to create a CloudTrail trail?","cloudtrail:CreateTrail","s3:PutBucketPolicy","iam:CreateRole","logs:CreateLogGroup","The `cloudtrail:CreateTrail` permission is required to create a new trail."
"You need to ensure that CloudTrail logs are delivered to your S3 bucket in a specific region. How can you achieve this?","Create a trail that applies to a single region","Create an organisation trail","Use CloudWatch Logs to forward logs to the S3 bucket","Enable S3 Cross-Region Replication","To deliver logs to a specific region, you must create a trail that applies to that specific region only."
"What is the purpose of the `Include global service events` setting in CloudTrail?","To record events for global services like IAM","To record events for regional services only","To exclude events from global services","To automatically encrypt log files","When this setting is enabled, CloudTrail logs events for global services such as IAM, which are not tied to a specific region."
"You are setting up CloudTrail for the first time. What is the first step you should take?","Create an S3 bucket to store the log files","Create an IAM role for CloudTrail","Configure CloudWatch Logs integration","Enable CloudTrail Insights","The first step is to create an S3 bucket to store the log files that CloudTrail will generate."
"What is the purpose of the 'kmsKeyId' parameter when creating a CloudTrail trail?","To specify the KMS key used to encrypt the log files","To specify the KMS key used to authenticate API calls","To specify the KMS key used to encrypt the S3 bucket","To specify the KMS key used to encrypt the IAM role","The `kmsKeyId` parameter specifies the AWS KMS key used to encrypt the CloudTrail log files stored in S3."
"What is the role of the CloudTrail event selector?","To filter the types of events that are logged","To encrypt the log files","To define the S3 bucket where logs are stored","To set the retention policy for log files","Event selectors allow you to filter the types of management and data events that are logged by CloudTrail."
"Which of the following AWS services is NOT directly integrated with CloudTrail for logging and monitoring?","Amazon EC2","Amazon S3","Amazon DynamoDB","Amazon Route 53","CloudTrail logs API calls made to all of these services."
"You need to retain CloudTrail logs for more than 90 days. What should you do?","Create a trail and store the logs in an S3 bucket","Rely on CloudTrail event history","Enable CloudTrail Insights","Enable server access logging in S3","CloudTrail event history only retains logs for 90 days. To retain logs longer, create a trail and store them in an S3 bucket."
"What is the purpose of the CloudTrail log file validation feature?","To verify that the log files have not been tampered with","To automatically compress the log files","To automatically encrypt the log files","To automatically delete old log files","The log file validation feature uses cryptographic hashes to verify that the log files have not been modified since they were delivered to the S3 bucket."
"Which CloudTrail feature can help you identify unusual API call patterns in your AWS environment?","CloudTrail Insights","CloudTrail Event History","CloudTrail Log File Validation","CloudTrail Data Events","CloudTrail Insights analyses CloudTrail logs to detect unusual API activity patterns."
"You are trying to troubleshoot an issue in your AWS environment. Which AWS service can you use with CloudTrail to identify the API calls that led to the issue?","CloudTrail","AWS X-Ray","AWS CloudWatch","AWS Trusted Advisor","CloudTrail records API calls, so you can use it to trace the sequence of events that led to the issue."
"What is the purpose of the CloudTrail Processing Library?","To parse and process CloudTrail log files programmatically","To create CloudTrail trails automatically","To encrypt CloudTrail log files","To visualize CloudTrail data","The CloudTrail Processing Library allows you to programmatically parse and process CloudTrail log files using code."
"Which AWS service can you use to visualise CloudTrail data and create dashboards?","Amazon QuickSight","Amazon CloudWatch","AWS CloudFormation","AWS Config","Amazon QuickSight can be used to visualise CloudTrail data and create dashboards."
"You want to ensure that only authorized personnel can access CloudTrail log files stored in S3. What should you do?","Configure the S3 bucket with appropriate IAM policies","Encrypt the log files using KMS","Enable versioning on the S3 bucket","Enable S3 access logs","IAM policies control who has access to the S3 bucket and the CloudTrail log files within it."
"What is the purpose of the `isLogging` parameter when creating a CloudTrail trail?","To enable or disable logging for the trail","To specify the S3 bucket where logs are stored","To specify the IAM role for the trail","To specify the KMS key for the trail","The `isLogging` parameter is used to enable or disable logging for the trail. Setting it to `true` enables logging, and setting it to `false` disables it."
"You need to track data plane events for S3 buckets. What is the first step you should take?","Enable data events for the S3 bucket in the CloudTrail configuration","Enable server access logging on the S3 bucket","Create a new S3 bucket specifically for data events","Enable CloudTrail Insights","You need to enable data events for the specific S3 bucket in the CloudTrail configuration."
"What type of data is NOT considered a Data Event in CloudTrail?","Lambda function execution","S3 object GET request","DynamoDB table scan","EC2 instance creation","EC2 instance creation is a Management event, not a Data event."
"What is the benefit of compressing CloudTrail log files?","Reduces storage costs in S3","Increases the security of the log files","Improves the performance of CloudTrail","Simplifies the process of analysing the log files","Compressing the log files reduces the storage costs in S3."
"Which AWS service can you use to automate the analysis of CloudTrail logs for security vulnerabilities?","Amazon Inspector","AWS Trusted Advisor","Amazon GuardDuty","Amazon CloudWatch","Amazon GuardDuty uses CloudTrail logs to detect security vulnerabilities and suspicious activity."
"You are creating a CloudTrail trail in the AWS Management Console. Which of the following settings is mandatory?","S3 bucket name","KMS key ID","Log file prefix","CloudWatch Logs group name","You must provide an S3 bucket name to store the CloudTrail log files."
"What is the purpose of the 'insightSelectors' parameter in CloudTrail?","To specify which API calls CloudTrail Insights should analyse","To specify which API calls should be excluded from logging","To specify the S3 bucket for CloudTrail Insights logs","To specify the IAM role for CloudTrail Insights","The 'insightSelectors' parameter is used to specify which API calls CloudTrail Insights should analyse for unusual activity."
"If you delete a CloudTrail trail, what happens to the log files that were previously stored in the S3 bucket?","The log files remain in the S3 bucket","The log files are automatically deleted","The log files are moved to Amazon Glacier","The log files are archived in AWS CloudTrail","Deleting a CloudTrail trail does not delete the log files in the S3 bucket. They remain there until you manually delete them."
"You need to configure CloudTrail to log events from all regions in your AWS account. What type of trail should you create?","Multi-region trail","Single-region trail","Organisation trail","Global trail","A multi-region trail is the appropriate type of trail to create in order to log events from all regions."
"You want to monitor changes to IAM policies in your AWS account. Which type of CloudTrail event should you focus on?","Management events","Data events","Insights events","CloudWatch events","Changes to IAM policies are considered management events, which record operations performed on resources."
"What is the recommended way to manage access to CloudTrail log files in an S3 bucket?","Use IAM policies to control access to the bucket","Use S3 bucket policies to control access to the bucket","Use access control lists (ACLs) to control access to the bucket","Use KMS key policies to control access to the bucket","IAM policies are the recommended method for controlling access to AWS resources, including S3 buckets and CloudTrail logs."
"You are using CloudTrail to monitor API activity in your AWS account. How can you ensure that you are capturing all relevant events?","Review and configure your CloudTrail event selectors","Enable CloudTrail Insights","Enable server access logging on your S3 buckets","Enable CloudWatch Logs integration","Reviewing and configuring your CloudTrail event selectors allows you to specify the types of management and data events that are logged, ensuring that you capture all relevant activity."
"What is the primary function of a CloudTrail log file prefix?","To organise log files within the S3 bucket","To encrypt log files","To compress log files","To validate log file integrity","The log file prefix helps you organise and easily locate your CloudTrail log files within the S3 bucket by creating a logical directory structure."
"Which AWS service can you use to trigger actions based on specific events recorded by CloudTrail?","Amazon CloudWatch Events (EventBridge)","AWS Lambda","Amazon SNS","Amazon SQS","Amazon CloudWatch Events (now EventBridge) can be used to trigger actions based on events captured by CloudTrail."
"You need to provide an auditor with access to CloudTrail logs without granting them full access to your AWS account. How can you achieve this?","Create a dedicated IAM role with read-only access to the S3 bucket","Share the AWS account credentials with the auditor","Grant the auditor temporary access to your AWS account","Send the auditor a copy of the log files","Creating a dedicated IAM role with read-only access to the S3 bucket allows the auditor to access the CloudTrail logs without granting them full access to your AWS account."
"You suspect that someone has deleted a critical EC2 instance in your AWS account. Which AWS service can you use to identify who performed the deletion?","CloudTrail","AWS Config","AWS CloudWatch","AWS Trusted Advisor","CloudTrail records API calls, so you can use it to identify the user who deleted the EC2 instance."
"What is the purpose of the `DeliveryFrequency` parameter when creating a CloudTrail trail?","To specify how often CloudTrail log files are delivered to the S3 bucket","To specify the encryption algorithm used for log files","To specify the compression format used for log files","To specify the retention period for log files","The `DeliveryFrequency` parameter specifies how often CloudTrail log files are delivered to the S3 bucket. This is typically set to 5 minutes."
"You are receiving an excessive number of CloudTrail log files in your S3 bucket. How can you reduce the volume of logs?","Use CloudTrail event selectors to filter the events being logged","Disable CloudTrail Insights","Enable server access logging on your S3 buckets","Reduce the retention period for your CloudTrail logs","Using CloudTrail event selectors to filter the events being logged allows you to reduce the volume of logs by only capturing the events that are relevant to your needs."
"What is the primary purpose of AWS CloudTrail?","To log API calls made to AWS services.","To monitor network traffic.","To manage user permissions.","To store large data files.","CloudTrail's primary purpose is to record API calls made to AWS services, providing an audit trail of actions taken in your account."
"Which type of information is NOT captured by AWS CloudTrail logs?","Operating System events on EC2 instances.","Identity of the caller.","Time of the API call.","AWS service called.","CloudTrail primarily logs API calls made to AWS services. Operating system events on EC2 instances are generally not captured unless specifically configured with other tools."
"Where are AWS CloudTrail logs stored by default?","In an S3 bucket you specify.","In Glacier.","In the CloudTrail console.","In the RDS instance.","CloudTrail logs are stored in an S3 bucket that you configure for log delivery."
"What AWS service can you use to analyse AWS CloudTrail logs?","Amazon Athena.","Amazon EBS.","Amazon SQS.","Amazon EC2.","Amazon Athena allows you to query CloudTrail logs stored in S3 using SQL, making it easier to analyse the data."
"What is the difference between AWS CloudTrail Insights and standard CloudTrail logging?","CloudTrail Insights detects unusual API activity, while standard logging records all API calls.","CloudTrail Insights logs network traffic, while standard logging logs API calls.","CloudTrail Insights stores logs in Glacier, while standard logging stores logs in S3.","CloudTrail Insights encrypts logs, while standard logging does not.","CloudTrail Insights detects unusual API activity by analyzing CloudTrail events and identifying anomalies, while standard logging records all API calls."
"Which AWS service is integrated with AWS CloudTrail to provide real-time monitoring and alerting of API activity?","Amazon CloudWatch.","Amazon VPC.","Amazon IAM.","Amazon S3.","Amazon CloudWatch can be used to monitor CloudTrail logs and create alarms based on specific API events or patterns."
"How can you ensure the integrity of your AWS CloudTrail logs?","Enable log file integrity validation.","Encrypt the S3 bucket.","Enable Multi-Factor Authentication (MFA).","Limit IAM permissions.","Enabling log file integrity validation creates a digitally signed hash of each log file, allowing you to verify that the logs have not been tampered with."
"What type of events does AWS CloudTrail primarily log?","Management events and data events.","Network events and security events.","Storage events and compute events.","Billing events and support events.","CloudTrail logs both management events (control plane operations) and data events (data plane operations) by default, which are different types of activities within your AWS account."
"In AWS CloudTrail, what are 'management events'?","Operations performed on resources in your AWS account.","Data access operations on S3 buckets.","Lambda function invocations.","Database queries in RDS.","Management events provide information about management operations that are performed on resources in your AWS account. For example, security group rule changes or creating an EC2 instance."
"In AWS CloudTrail, what are 'data events'?","Data access operations on S3 objects or Lambda function executions.","Operations performed on resources in your AWS account.","Changes in IAM roles.","Updates to CloudWatch alarms.","Data events provide insights into the resource operations performed on or within a resource. Examples include S3 object GET, LIST, and DELETE API calls."
"You need to retain your AWS CloudTrail logs for compliance reasons for 7 years. What is the most cost-effective way to achieve this?","Configure an S3 Lifecycle Policy to transition logs to Glacier after a certain period.","Store logs in EBS volumes.","Delete logs after one year.","Store logs in instance store.","Using an S3 Lifecycle Policy to transition logs to Glacier after a specified period allows for long-term, cost-effective storage of infrequently accessed data."
"You are configuring AWS CloudTrail for the first time. Which AWS service do you need to use to create a trail?","The CloudTrail console.","The IAM console.","The EC2 console.","The VPC console.","You need to use the CloudTrail console to create and configure a trail, specifying the S3 bucket where logs will be stored."
"What is the minimum permission required for an IAM role to allow CloudTrail to write logs to an S3 bucket?","s3:PutObject","s3:GetObject","s3:DeleteObject","s3:ListBucket","The IAM role used by CloudTrail must have `s3:PutObject` permission to write log files to the specified S3 bucket."
"Which AWS service can be used to detect and respond to malicious activity identified in AWS CloudTrail logs?","AWS GuardDuty.","AWS Trusted Advisor.","AWS Inspector.","AWS Shield.","AWS GuardDuty uses machine learning to analyse CloudTrail logs and other data sources to detect potential security threats."
"You want to track changes to specific S3 objects using AWS CloudTrail. How do you configure this?","Enable data event logging for the S3 bucket in CloudTrail.","Enable management event logging for the S3 bucket in CloudTrail.","Enable CloudTrail Insights for the S3 bucket.","Enable S3 versioning on the S3 bucket.","To track data access operations on S3 objects (like GET, PUT, DELETE), you need to enable data event logging for the specific S3 bucket in CloudTrail."
"You are receiving too many events in your AWS CloudTrail logs, making analysis difficult. What can you do to reduce the volume of logs?","Configure event selectors to filter specific events.","Disable CloudTrail entirely.","Increase the size of the S3 bucket.","Store logs in Glacier.","Using event selectors allows you to specify which types of events (management or data events) and which resources to log, reducing the overall volume of logs."
"You need to ensure that only authorized users can access your AWS CloudTrail logs. How can you achieve this?","Configure S3 bucket policies and IAM permissions.","Enable encryption on the logs.","Enable CloudTrail Insights.","Enable MFA for all users.","S3 bucket policies and IAM permissions are the primary mechanisms for controlling access to S3 buckets and the objects stored within them, including CloudTrail logs."
"What is the purpose of the 'IsLogging' parameter in the AWS CloudTrail trail configuration?","To enable or disable logging for the trail.","To specify the S3 bucket where logs are stored.","To configure event selectors.","To enable log file integrity validation.","The `IsLogging` parameter in the CloudTrail trail configuration is used to enable or disable logging for the trail, effectively starting or stopping the recording of API calls."
"You have configured AWS CloudTrail but are not seeing any logs in your S3 bucket. What is the most likely cause?","The IAM role used by CloudTrail does not have sufficient permissions to write to the S3 bucket.","CloudTrail Insights is not enabled.","The S3 bucket is not properly configured for log delivery.","Encryption is not enabled on the S3 bucket.","If CloudTrail is not able to write logs to the S3 bucket due to insufficient IAM permissions or an incorrect bucket policy, no logs will be delivered."
"Which log file integrity validation method does AWS CloudTrail use?","SHA-256 digital signatures.","MD5 checksums.","CRC32 checksums.","AES encryption.","AWS CloudTrail uses SHA-256 digital signatures to ensure the integrity of log files, allowing you to verify that the logs have not been tampered with."
"How can you aggregate AWS CloudTrail logs from multiple AWS accounts into a single location?","Configure CloudTrail to deliver logs to a central S3 bucket in one account.","Use AWS Organizations to create a consolidated logging account.","Use AWS Config to aggregate logs.","Use AWS Lambda to copy logs to a central location.","Using AWS Organizations allows you to designate a master account to collect and analyze CloudTrail logs from all member accounts."
"What is the default retention period for AWS CloudTrail logs stored in S3?","There is no default retention period; you must configure it using S3 Lifecycle Policies.","30 days.","90 days.","1 year.","CloudTrail stores logs indefinitely in S3. You need to setup S3 Lifecycle policies to manage the data, including the deletion of older data."
"You need to ensure that all future S3 buckets created in your AWS account are automatically monitored by AWS CloudTrail. How can you achieve this?","Configure CloudTrail to apply to all regions and enable data event logging for S3.","Create a custom IAM policy to monitor S3 bucket creation.","Enable AWS Config rules for S3 bucket creation.","Use AWS CloudWatch Events to trigger a Lambda function on S3 bucket creation.","By configuring CloudTrail to apply to all regions and enabling data event logging for S3, any new S3 buckets created will automatically be monitored."
"You want to be notified whenever a specific IAM user makes changes to IAM policies in your AWS account. How can you achieve this using AWS CloudTrail and CloudWatch?","Create a CloudWatch alarm based on CloudTrail events for IAM policy changes.","Enable CloudTrail Insights for IAM policies.","Enable AWS Config rules for IAM policies.","Subscribe to the AWS Security Bulletin.","You can create a CloudWatch alarm that triggers when specific CloudTrail events related to IAM policy changes are detected, allowing you to be notified of these activities."
"What is the relationship between AWS CloudTrail and AWS Config?","CloudTrail records API calls, while Config tracks resource configurations and changes.","CloudTrail monitors network traffic, while Config manages user permissions.","CloudTrail stores logs in S3, while Config stores logs in Glacier.","CloudTrail encrypts logs, while Config does not.","CloudTrail records API calls made to AWS services, while AWS Config tracks the configuration of your AWS resources and how they change over time."
"You are investigating a potential security incident and need to determine which IAM user made a specific API call. Where can you find this information?","In the AWS CloudTrail logs.","In the AWS IAM console.","In the AWS CloudWatch logs.","In the AWS Trusted Advisor console.","The identity of the caller (IAM user or role) who made the API call is recorded in the AWS CloudTrail logs."
"How can you enable encryption for AWS CloudTrail logs stored in S3?","Configure S3 bucket encryption using AWS KMS or SSE-S3.","Enable encryption in the CloudTrail console.","Enable encryption in the IAM console.","Enable encryption in the VPC console.","You can enable encryption for CloudTrail logs by configuring S3 bucket encryption using either AWS KMS or Server-Side Encryption with Amazon S3-Managed Keys (SSE-S3)."
"You need to automate the analysis of AWS CloudTrail logs for compliance reporting. Which AWS service can help with this?","Amazon Athena.","Amazon EBS.","Amazon SQS.","Amazon EC2.","Amazon Athena allows you to query CloudTrail logs stored in S3 using SQL, which can be automated for compliance reporting purposes."
"What is the cost associated with enabling AWS CloudTrail?","CloudTrail itself is free, but storage and analysis of logs incur costs.","CloudTrail is free for the first year, then incurs costs.","CloudTrail has a fixed monthly cost.","CloudTrail is free for all AWS accounts.","CloudTrail service itself is free, the storage of logs in S3 and the analysis of those logs using services like Athena incur costs."
"You want to ensure that your AWS CloudTrail logs are protected from accidental deletion. What can you do?","Enable S3 versioning on the S3 bucket.","Enable MFA Delete on the S3 bucket.","Enable CloudTrail Insights.","Enable IAM access logging.","Enabling S3 versioning ensures that any deleted objects are retained as previous versions, protecting against accidental deletion."
"What is the maximum size of a single AWS CloudTrail log file?","512 MB.","1 GB.","10 GB.","Unlimited.","The maximum size of a single CloudTrail log file is 512 MB. Once a log file reaches this size, CloudTrail will create a new log file."
"You are using AWS CloudTrail to monitor API calls made by an application. However, you are missing some events. What could be the reason?","The event selectors in your CloudTrail configuration are not configured to capture the specific events.","The application is not making API calls.","CloudTrail Insights is disabled.","The S3 bucket is full.","If the event selectors in your CloudTrail configuration are not configured to capture the specific events made by the application, those events will not be logged."
"How does AWS CloudTrail help with auditing and compliance requirements?","By providing a detailed record of all API calls made in your AWS account.","By encrypting data in transit.","By monitoring network traffic.","By managing user permissions.","CloudTrail provides a detailed record of all API calls, enabling you to audit user activity, troubleshoot issues, and meet compliance requirements."
"You have configured AWS CloudTrail to deliver logs to an S3 bucket. However, the logs are being delivered to the wrong bucket. What should you do?","Verify the S3 bucket name in the CloudTrail configuration.","Restart the CloudTrail service.","Recreate the IAM role.","Recreate the S3 bucket.","The first step is to verify that the S3 bucket name specified in the CloudTrail configuration is correct and that the S3 bucket exists."
"What is the purpose of the AWS CloudTrail Lake?","Centralised repository to ingest, store, query and audit activity logs","A service to visualize CloudTrail Logs in real time","Tool to identify malicious API Calls in your AWS accounts","A cheaper storage option for CloudTrail Logs","CloudTrail Lake allows you to centrally aggregate, store, query, and audit the audit and compliance logs for your AWS environment."
"You want to analyse AWS CloudTrail logs to identify trends and patterns in API usage. Which AWS service can help you visualize this data?","Amazon QuickSight.","Amazon EBS.","Amazon SQS.","Amazon EC2.","Amazon QuickSight can be used to visualize data from CloudTrail logs, allowing you to identify trends and patterns in API usage."
"You suspect that someone has tampered with your AWS CloudTrail logs. How can you verify the integrity of the logs?","Use the AWS CLI or SDK to validate the log file integrity.","Check the S3 bucket access logs.","Compare the logs with a known good copy.","Review the IAM access policies.","CloudTrail provides tools to validate the integrity of log files, allowing you to verify that they have not been tampered with."
"You are using AWS CloudTrail to monitor API calls made by an application. You notice a large number of failed API calls. What should you do?","Investigate the application code and IAM permissions.","Disable CloudTrail.","Increase the size of the S3 bucket.","Enable encryption on the S3 bucket.","A large number of failed API calls could indicate issues with the application code or insufficient IAM permissions, which need to be investigated."
"You want to automate the process of creating and configuring AWS CloudTrail trails across multiple AWS accounts. What is the recommended approach?","Use AWS CloudFormation or Terraform.","Use the AWS CLI manually.","Use the AWS Management Console manually.","Use AWS Trusted Advisor.","AWS CloudFormation and Terraform are infrastructure-as-code tools that can be used to automate the creation and configuration of AWS resources, including CloudTrail trails."
"You are using AWS CloudTrail to monitor API calls. How frequently are the log files delivered to the S3 bucket?","Approximately every 5 minutes.","In real time.","Once per day.","Once per week.","CloudTrail log files are delivered to the S3 bucket approximately every 5 minutes."
"What is the relationship between AWS CloudTrail and AWS IAM?","CloudTrail logs API calls made using IAM users and roles.","CloudTrail manages IAM permissions.","CloudTrail encrypts IAM credentials.","CloudTrail stores IAM policies.","CloudTrail logs API calls made using IAM users and roles, providing an audit trail of actions taken by those identities."
"What type of logging is possible with CloudTrail when dealing with Lambda functions?","Data event logging can track function invocations.","CloudTrail only logs management events for Lambda.","CloudTrail cannot log Lambda function activity.","CloudTrail logs all function code changes.","Data event logging in CloudTrail allows you to track Lambda function invocations as data events."
"You are managing multiple AWS accounts within an AWS organisation and want a consolidated view of CloudTrail activity. What is the best approach?","Use an organisation trail in CloudTrail.","Create separate CloudTrail trails in each account.","Manually copy the logs into a single S3 bucket.","Enable cross-account access on individual S3 buckets.","An organisation trail centrally captures logs for all accounts within your organisation, simplifying auditing and compliance efforts."
"Which security benefit does AWS CloudTrail offer?","Detecting unauthorised API calls.","Encrypting data at rest.","Blocking DDoS attacks.","Managing firewall rules.","CloudTrail's logs allow you to identify and investigate unauthorised API calls made within your AWS environment."
"What is a best practice when configuring an S3 bucket for AWS CloudTrail logs?","Enable S3 bucket logging to track access to the logs.","Disable S3 versioning.","Grant public read access to the bucket.","Use a short bucket name.","Enabling S3 bucket logging allows you to track access to the logs stored within the S3 bucket, providing an additional layer of security and auditing."
"You need to ensure all AWS API calls are logged, regardless of the region in which they are made. What should you configure?","A multi-region trail in AWS CloudTrail.","Separate trails for each AWS region.","An AWS Config rule to monitor API calls.","A single trail in the US East (N. Virginia) region.","A multi-region trail captures API calls made in all AWS regions, providing a comprehensive view of activity across your AWS environment."
"Besides S3 buckets, where else can CloudTrail Logs be delivered to, directly?","CloudWatch Logs","EC2 Instance","DynamoDB Table","SQS Queue","CloudTrail can directly deliver logs to CloudWatch Logs in addition to S3 buckets."
"How does AWS CloudTrail assist in incident response?","By providing a history of API calls that can be used to investigate security incidents.","By automatically blocking malicious traffic.","By providing real-time network monitoring.","By managing user access controls.","The history of API calls provided by CloudTrail allows you to reconstruct events leading up to a security incident, aiding in investigation and remediation."
"What is the primary purpose of AWS CloudTrail?","To log API calls made to AWS services","To monitor network traffic","To manage IAM roles","To store large files","CloudTrail records API calls made to AWS services, providing an audit trail of who did what, when, and from where."
"Which AWS service typically stores CloudTrail logs?","Amazon S3","Amazon EBS","Amazon Glacier","Amazon RDS","CloudTrail logs are stored in an S3 bucket that you specify when creating the trail."
"In AWS CloudTrail, what does an 'event' represent?","A single API call","A network packet","A security vulnerability","A scheduled task","An event in CloudTrail represents a single API call made to an AWS service."
"What type of events does AWS CloudTrail log by default?","Management events","Data events","CloudWatch events","Route 53 events","CloudTrail logs management events by default, which include operations performed on resources in your AWS account."
"What is the purpose of 'Data events' in AWS CloudTrail?","To log object-level API activity on S3 buckets and Lambda function invocations","To log changes to IAM policies","To log EC2 instance launches","To log VPC configuration changes","Data events log object-level API activity on S3 buckets and Lambda function invocations, providing insight into data access."
"Can AWS CloudTrail logs be encrypted?","Yes, using S3 server-side encryption or KMS","No, CloudTrail logs cannot be encrypted","Yes, using AWS Shield","Yes, using AWS WAF","CloudTrail logs can be encrypted using S3 server-side encryption (SSE) or AWS Key Management Service (KMS) to protect the logs at rest."
"What is the function of CloudTrail Insights?","To detect unusual API activity in your AWS account","To automatically correct IAM policies","To provide real-time network monitoring","To automatically scale EC2 instances","CloudTrail Insights helps detect unusual API activity by analysing CloudTrail events and identifying anomalies in API call patterns."
"How long does AWS CloudTrail store events by default if no retention period is specified on the S3 bucket?","CloudTrail does not store events. S3 bucket retention rules dictate retention.","30 days","90 days","1 year","CloudTrail itself does not store events. The S3 bucket's retention rules dictate how long the events are retained."
"How can you verify the integrity of AWS CloudTrail log files?","By using CloudTrail's log file integrity validation","By using AWS Config","By using Amazon Inspector","By using Amazon GuardDuty","CloudTrail's log file integrity validation uses cryptographic hashing to ensure that log files have not been altered since they were delivered."
"What AWS IAM permission is required to create and manage CloudTrail trails?","cloudtrail:CreateTrail","s3:PutObject","iam:CreateRole","ec2:RunInstances","Creating and managing CloudTrail trails requires the `cloudtrail:CreateTrail` permission, along with related permissions for S3 and IAM."
"You want to ensure compliance and security by monitoring API calls related to IAM. How can you achieve this using AWS CloudTrail?","Enable CloudTrail and configure it to log management events","Enable CloudTrail and configure it to log data events","Enable CloudWatch Logs","Enable AWS Config","To monitor API calls related to IAM, you need to enable CloudTrail and configure it to log management events, as IAM operations are considered management events."
"Which AWS service can be integrated with CloudTrail to receive real-time notifications about specific API activities?","Amazon CloudWatch Events (EventBridge)","Amazon SNS","Amazon SQS","Amazon SES","CloudTrail can be integrated with Amazon CloudWatch Events (now known as EventBridge) to receive real-time notifications about specific API activities based on CloudTrail events."
"How can you ensure that AWS CloudTrail logs from multiple AWS accounts are aggregated into a single location for centralised auditing?","Create an organisation trail","Enable cross-account access","Create multiple trails in the same bucket","Use AWS Organizations only","An organisation trail in CloudTrail enables you to aggregate logs from multiple AWS accounts within your AWS organisation into a single S3 bucket for centralised auditing."
"What is the scope of an AWS CloudTrail trail when configured as a 'trail that applies to all regions'?","The trail logs events in all AWS regions","The trail logs events only in the region where it was created","The trail logs events only in the global services","The trail logs events only for EC2","A trail that applies to all regions logs events in all AWS regions, providing a comprehensive audit trail across your entire AWS infrastructure."
"Which file format is used to store AWS CloudTrail logs in Amazon S3?","JSON","CSV","XML","YAML","CloudTrail logs are stored in JSON (JavaScript Object Notation) format in Amazon S3."
"What is the purpose of the AWS CLI command `aws cloudtrail describe-trails`?","To retrieve information about existing CloudTrail trails","To start logging events","To create a new CloudTrail trail","To delete a CloudTrail trail","The `aws cloudtrail describe-trails` command is used to retrieve information about existing CloudTrail trails in your AWS account."
"You need to filter CloudTrail logs to identify events related to a specific user. Which attribute in the CloudTrail log event can you use for filtering?","userIdentity","eventTime","eventName","sourceIPAddress","The `userIdentity` attribute in the CloudTrail log event contains information about the user or role that made the API call, allowing you to filter events based on a specific user."
"Which type of AWS CloudTrail event provides information about resource configurations over time?","AWS Config events","CloudWatch events","Data events","Management events","AWS Config provides information about resource configurations over time, but CloudTrail records the API calls that led to those configuration changes. AWS Config events are not directly related to CloudTrail."
"What is the purpose of integrating AWS CloudTrail with Amazon Athena?","To analyse CloudTrail logs using SQL queries","To visualise CloudTrail data in a dashboard","To automatically correct IAM policies","To encrypt CloudTrail logs","Integrating CloudTrail with Amazon Athena allows you to analyse CloudTrail logs using SQL queries, providing powerful and flexible querying capabilities."
"When setting up CloudTrail, what is the key requirement for the S3 bucket you designate for log storage?","The S3 bucket must have bucket policy that grants CloudTrail permissions to write logs.","The S3 bucket must be encrypted with KMS.","The S3 bucket must be publicly accessible.","The S3 bucket must have versioning disabled.","The S3 bucket must have a bucket policy that grants CloudTrail permissions to write logs. Without the right bucket policy CloudTrail cannot deliver logs to the bucket."
"Which of the following is an important security consideration when configuring CloudTrail?","Restricting access to the S3 bucket containing the logs","Enabling public access to the S3 bucket","Disabling encryption for the logs","Using a weak IAM role for CloudTrail","Restricting access to the S3 bucket containing the logs is crucial to prevent unauthorised access to sensitive audit information."
"If you want to track which users are accessing specific S3 objects, which type of CloudTrail event should you enable?","Data events on the S3 bucket","Management events on the S3 bucket","CloudWatch events for S3","VPC Flow Logs","To track user access to S3 objects, you need to enable data events on the specific S3 bucket, as these events capture object-level API activity."
"What does the term 'CloudTrail Lake' refer to?","A managed data lake for analysing CloudTrail logs","A cost-optimised storage option for CloudTrail logs","A real-time monitoring dashboard for CloudTrail","A method for encrypting CloudTrail logs","CloudTrail Lake is a managed data lake for analysing CloudTrail logs, enabling you to ingest, store, and query audit logs for compliance and security analysis."
"What is the primary advantage of using CloudTrail Lake over directly querying CloudTrail logs stored in S3?","CloudTrail Lake provides enhanced query performance and data management features","Querying S3 is cheaper","Querying S3 provides the most up to date information","Querying S3 requires no additional setup","CloudTrail Lake offers enhanced query performance, data management features, and integration with other AWS services, making it more efficient for complex audit analysis than directly querying S3."
"How can you use AWS CloudTrail to detect potentially malicious activity in your AWS environment?","By analysing CloudTrail logs for unusual API calls and patterns","By enabling AWS GuardDuty","By enabling AWS Shield","By enabling AWS WAF","CloudTrail logs can be analysed to identify unusual API calls and patterns, which can indicate potentially malicious activity or security breaches."
"What is the recommended way to handle CloudTrail logs in a multi-account AWS environment for compliance purposes?","Use an organisation trail to aggregate logs into a central S3 bucket","Create individual trails in each account","Share the same S3 bucket across all accounts","Disable CloudTrail in all but the primary account","Using an organisation trail to aggregate logs into a central S3 bucket is the recommended approach for centralised logging and compliance in a multi-account environment."
"You need to investigate an API call that occurred in your AWS account 3 months ago. Where can you find the CloudTrail logs for this event?","In the S3 bucket where CloudTrail logs are stored, provided the retention period is sufficient","In Amazon CloudWatch Logs","In AWS Config","In Amazon Inspector","CloudTrail logs for past events can be found in the S3 bucket where CloudTrail logs are stored, as long as the retention period for the logs is sufficient to cover the 3-month period."
"What happens to CloudTrail logs when you delete a trail?","The logs remain in the S3 bucket but the trail configuration is removed","The logs are automatically deleted from the S3 bucket","The logs are moved to Amazon Glacier","The logs are backed up to Amazon EBS","When you delete a trail, the trail configuration is removed, but the logs remain in the S3 bucket where they were stored, unless you manually delete them."
"You want to receive alerts whenever a specific API call is made in your AWS account. How can you configure this using CloudTrail and CloudWatch?","Create a CloudWatch Events (EventBridge) rule that triggers on the specific CloudTrail event","Create an SNS topic and subscribe to CloudTrail","Create an SQS queue and configure CloudTrail to send logs to the queue","Create a Lambda function that polls CloudTrail logs","You can create a CloudWatch Events (EventBridge) rule that triggers on the specific CloudTrail event, allowing you to receive alerts whenever that API call is made."
"What is the maximum number of AWS CloudTrail trails that can be active in an AWS account across all regions?","There is no limit.","One","Five","Ten","There is no limit to the number of trails you can create in your AWS account."
"Which CloudTrail feature allows you to validate that your CloudTrail logs have not been tampered with since they were delivered to the S3 bucket?","Log file integrity validation","Data event logging","Management event logging","Trail validation","CloudTrail's log file integrity validation uses cryptographic hashing to verify that log files have not been altered since they were delivered."
"What is the difference between 'Management events' and 'Data events' in AWS CloudTrail?","Management events log control plane operations, while data events log data plane operations","Management events log data plane operations, while data events log control plane operations","Management events log only IAM operations, while data events log all other operations","Management events log only EC2 operations, while data events log all other operations","Management events log control plane operations (e.g., creating, modifying, or deleting resources), while data events log data plane operations (e.g., accessing or manipulating data within resources)."
"You are tasked with creating a highly available and durable storage solution for your CloudTrail logs. What AWS service is best suited for this purpose?","Amazon S3","Amazon EBS","Amazon EFS","Amazon RDS","Amazon S3 provides highly available and durable storage for CloudTrail logs, ensuring that your audit logs are protected and accessible."
"What is the purpose of the 'Include global service events' option when creating an AWS CloudTrail trail?","To log events from global AWS services like IAM, Route 53, and CloudFront","To log events from all AWS regions","To log events from only global services","To log events from only regional services","The 'Include global service events' option logs events from global AWS services such as IAM, Route 53, and CloudFront, ensuring that you capture API calls made to these services."
"Which of the following is NOT a valid source for importing event data into CloudTrail Lake?","AWS Config","CloudTrail Events","Partner applications","On-Premise applications","AWS Config provides resource configuration information, but it doesn't directly feed event data into CloudTrail Lake like CloudTrail events or integrated partner applications and on-premise application events can."
"When using CloudTrail Lake, what query language is used to analyse the stored data?","SQL","NoSQL","JSONiq","XPath","CloudTrail Lake uses SQL as its query language, allowing you to analyse the stored data using familiar SQL syntax."
"You need to ensure that all API calls made by a specific IAM role are logged for auditing purposes. How can you achieve this using AWS CloudTrail?","Ensure CloudTrail is enabled and logging management events, as all API calls made by the role will be captured","Enable CloudWatch metrics for the IAM role","Enable AWS Config rules for the IAM role","Enable VPC Flow Logs for the IAM role","By ensuring that CloudTrail is enabled and logging management events, all API calls made by the IAM role will be captured, providing a comprehensive audit trail."
"You are setting up AWS CloudTrail for the first time. What is the first step you should take?","Create an S3 bucket to store the CloudTrail logs","Create an IAM role for CloudTrail","Create a CloudWatch Logs log group","Enable AWS Config","The first step in setting up CloudTrail is to create an S3 bucket where CloudTrail will store the logs, as this is a prerequisite for creating a trail."
"If you enable CloudTrail Insights, which type of events are analysed to detect unusual activity?","Management events","Data events","CloudWatch events","VPC Flow Logs","CloudTrail Insights analyses management events to detect unusual API activity in your AWS account."
"What is a common use case for integrating AWS CloudTrail with a SIEM (Security Information and Event Management) system?","Centralising security monitoring and incident response","Enabling encryption for CloudTrail logs","Improving the performance of CloudTrail queries","Automatically correcting IAM policies","Integrating CloudTrail with a SIEM system allows you to centralise security monitoring and incident response by aggregating and analysing CloudTrail logs along with other security data."
"What is the purpose of the 'kmsKeyId' field in an AWS CloudTrail log event?","To identify the KMS key used to encrypt the CloudTrail logs","To identify the KMS key used to encrypt the data accessed by the API call","To identify the KMS key used to encrypt the S3 bucket storing the logs","To identify the KMS key used to encrypt the CloudWatch Logs","The 'kmsKeyId' field in a CloudTrail log event identifies the KMS key used to encrypt the CloudTrail logs themselves, ensuring that the logs are protected at rest."
"How can you reduce the storage costs associated with AWS CloudTrail logs in Amazon S3?","Configure S3 lifecycle rules to archive or delete older logs","Disable CloudTrail in non-critical regions","Enable S3 versioning","Disable data event logging","You can reduce storage costs by configuring S3 lifecycle rules to automatically archive older logs to a less expensive storage class (e.g., Glacier) or delete them after a certain period."
"Which of the following actions can you perform using CloudTrail Lake's query capabilities?","Identify the most frequently called API","Automatically remediate security misconfigurations","Perform real-time network traffic analysis","Automatically generate compliance reports","CloudTrail Lake's query capabilities allow you to identify the most frequently called APIs, enabling you to optimise resource usage and identify potential security risks."
"Which service provides the ability to send CloudTrail events directly to an SQS queue?","CloudTrail does not directly support sending events to SQS.","Amazon CloudWatch Logs","Amazon SNS","AWS Config","CloudTrail itself does not directly send events to SQS. Integration with SQS would typically be managed via CloudWatch Events (EventBridge)."
"A security engineer needs to quickly identify all API calls made by a compromised IAM user. What is the MOST efficient way to accomplish this using CloudTrail?","Use CloudTrail Lake to query for events where userIdentity.arn matches the IAM user's ARN.","Manually review all CloudTrail logs in S3.","Enable CloudTrail Insights and wait for anomalous activity to be detected.","Use AWS Config to determine the IAM user's permissions and review related API calls.","Using CloudTrail Lake and querying for events with the IAM user's ARN is the most efficient and targeted way to identify all API calls made by the compromised user."
"When creating a CloudTrail trail, what is the purpose of specifying a log file prefix?","To organise logs within the S3 bucket","To encrypt logs","To compress logs","To filter logs","Specifying a log file prefix allows you to organise logs within the S3 bucket, making it easier to manage and search for specific log files."
"What is the role of 'Event selectors' in AWS CloudTrail?","To filter which events are logged by CloudTrail","To encrypt the logs","To define the retention period of the logs","To validate the integrity of the logs","Event selectors are used to filter which events are logged by CloudTrail, allowing you to specify which management and data events you want to capture."
"What is the primary purpose of AWS CloudTrail?","Auditing and compliance","Network monitoring","Data encryption","Cost optimisation","CloudTrail records API calls made within your AWS environment, enabling auditing and compliance."
"Which AWS service is used to deliver CloudTrail logs to an S3 bucket?","CloudTrail","CloudWatch Logs","S3 Transfer Acceleration","AWS Config","CloudTrail is responsible for capturing and delivering log files to a specified S3 bucket."
"What type of information does AWS CloudTrail record?","API calls made to AWS services","CPU utilisation of EC2 instances","Network traffic between VPCs","Database query performance","CloudTrail records API calls made to AWS services, including the identity of the caller, the time of the call, the request parameters, and the response elements returned by the AWS service."
"You need to investigate which user deleted an S3 bucket. Where can you find this information using AWS services?","CloudTrail logs in S3","CloudWatch metrics for S3","IAM role policies","AWS Trusted Advisor recommendations","CloudTrail logs record events related to S3 bucket deletions, including the user who initiated the action."
"Which of the following AWS services integrates directly with CloudTrail to provide real-time alerting based on specific API calls?","CloudWatch Events (now EventBridge)","AWS Config","AWS Inspector","AWS Shield","CloudWatch Events (now EventBridge) can trigger actions based on specific API calls recorded by CloudTrail, enabling real-time alerting."
"What is the difference between CloudTrail Insights and standard CloudTrail logging?","CloudTrail Insights analyses anomalies in API calls","CloudTrail Insights delivers logs to CloudWatch Logs","CloudTrail Insights captures all network traffic","CloudTrail Insights encrypts log files","CloudTrail Insights analyses CloudTrail events to detect unusual activity patterns (anomalies) such as error rates or resource provisioning."
"You want to ensure your CloudTrail logs are not tampered with. What feature can you enable to verify log integrity?","Log file integrity validation","S3 bucket encryption","IAM access control policies","Multi-Factor Authentication (MFA)","Log file integrity validation uses cryptographic hashing to detect any modifications to CloudTrail log files."
"How can you control access to CloudTrail logs stored in an S3 bucket?","IAM policies on the S3 bucket","CloudTrail policies","VPC flow logs","AWS Firewall Manager","IAM policies can be used to grant specific users or roles access to the S3 bucket containing CloudTrail logs."
"You need to track changes made to your AWS security groups. Which AWS service can provide this information?","CloudTrail","CloudWatch","AWS Config","AWS Inspector","CloudTrail captures API calls related to security group changes, allowing you to track who made the changes and when."
"What is the default retention period for CloudTrail event history if you do not configure a trail?","90 days","30 days","1 year","7 days","The Event history provides a view of the past 90 days of recorded management events."
"You need to store CloudTrail logs for longer than the default retention period. What should you do?","Configure a trail to deliver logs to an S3 bucket","Enable CloudWatch Logs integration","Use AWS Glacier to archive the logs","Use AWS Backup to backup the logs","By configuring a trail to deliver logs to an S3 bucket, you can store logs indefinitely (subject to S3 storage costs)."
"Which type of events are captured by default in CloudTrail when a trail is created?","Management events","Data events","Insights events","All API events","By default, CloudTrail trails capture management events, which include operations performed on resources in your AWS account."
"What is a 'Management event' in the context of CloudTrail?","Operations performed on the control plane","Data plane operations on S3 objects","Network traffic logs","Operating system logs","Management events provide information about management operations that are performed on resources in your AWS account. These are also known as control plane operations."
"What is a 'Data event' in the context of CloudTrail?","Data plane operations on resources","Operations performed on the control plane","Network traffic logs","Operating system logs","Data events provide information about the data plane operations performed on or within a resource. These are also known as data plane operations."
"You want to track access to specific S3 objects. Which type of CloudTrail event should you configure?","Data events","Management events","Insights events","Control plane events","To track access to specific S3 objects, you need to configure data events in CloudTrail."
"You are setting up CloudTrail for compliance reasons and need to ensure that logs are delivered to an S3 bucket in a different AWS account. What configuration is required?","Configure cross-account access to the S3 bucket","Enable S3 cross-region replication","Use AWS STS to assume a role in the other account","Create a CloudTrail trail in the other account","To deliver logs to an S3 bucket in a different account, you need to configure cross-account access permissions on the S3 bucket using IAM policies."
"What is the purpose of enabling CloudTrail log file encryption?","To protect log files from unauthorised access","To reduce storage costs","To improve query performance","To automate log analysis","Enabling CloudTrail log file encryption protects the confidentiality of the logs by encrypting them at rest in S3."
"What is the function of the CloudTrail event data store?","To aggregate and analyse CloudTrail events","To store CloudTrail logs temporarily","To encrypt CloudTrail logs","To visualise CloudTrail logs","CloudTrail event data store can be used for storing, immutable, serverless and cost effective log management, helping you meet your security and compliance needs."
"You want to quickly search and filter CloudTrail events based on specific criteria. Which feature can you use?","CloudTrail Event history","CloudWatch Logs Insights","AWS Athena","S3 Select","CloudTrail Event history provides a basic search and filtering interface for recent CloudTrail events (last 90 days)."
"For more complex analysis of CloudTrail logs, which AWS service is best suited for querying the data directly in S3?","AWS Athena","CloudWatch Logs Insights","AWS Config","CloudTrail Event history","AWS Athena allows you to query CloudTrail logs stored in S3 using SQL, enabling more complex analysis and reporting."
"You need to create a centralised logging solution for all AWS accounts in your organisation. Which AWS service feature can help you achieve this?","AWS Organizations integration with CloudTrail","AWS Config aggregation","CloudWatch cross-account dashboards","IAM cross-account roles","AWS Organizations allows you to create a trail that logs events for all accounts in your organisation, simplifying centralised logging."
"What is the benefit of integrating CloudTrail with CloudWatch Logs?","Real-time monitoring and alerting on specific events","Long-term storage of CloudTrail logs","Data encryption for CloudTrail logs","Automated log analysis","Integrating CloudTrail with CloudWatch Logs allows you to set up real-time monitoring and alerting based on specific events captured in CloudTrail logs."
"You are receiving a high volume of CloudTrail logs, and your S3 bucket costs are increasing. What can you do to reduce storage costs?","Configure S3 lifecycle policies to archive older logs to Glacier","Enable CloudWatch Logs integration","Disable CloudTrail logging","Increase the CloudTrail retention period","S3 lifecycle policies can be used to automatically move older logs to cheaper storage tiers like Glacier or delete them altogether, reducing storage costs."
"You suspect a security breach and need to investigate unusual API activity. Which AWS service can help you analyse CloudTrail logs for potential security threats?","CloudTrail Insights","AWS Inspector","AWS Trusted Advisor","AWS Shield","CloudTrail Insights analyses CloudTrail events to detect unusual API activity patterns that may indicate a security threat."
"You want to capture CloudTrail logs for a specific AWS region only. How do you configure this?","Create a trail that applies to a single region","Enable global service events logging","Configure IAM permissions for the region","Enable VPC Flow Logs for the region","When creating a trail, you can specify whether it applies to a single region or all regions."
"You need to ensure that CloudTrail logs are delivered even if there are temporary network connectivity issues. What configuration helps with this?","Enable S3 bucket versioning","Enable CloudWatch Logs integration","Configure a dead-letter queue","Configure S3 event notifications","Enabling S3 bucket versioning ensures that all versions of the log files are preserved, even if there are temporary delivery issues."
"What does the 'isManagementEvent' field in a CloudTrail event record indicate?","Whether the event is a management event or a data event","The severity of the event","The AWS region where the event occurred","The IAM role used to perform the action","The 'isManagementEvent' field indicates whether the event is a management event (control plane operation) or a data event (data plane operation)."
"You want to automate the process of identifying and responding to security incidents detected in CloudTrail logs. Which AWS service can you integrate with CloudTrail to achieve this?","AWS Security Hub","AWS Config","AWS Inspector","AWS Trusted Advisor","AWS Security Hub integrates with CloudTrail and other AWS services to provide a comprehensive view of your security posture and automate incident response."
"What is the purpose of the `cloudTrailEvent` field within a CloudTrail log entry?","It contains the detailed JSON representation of the CloudTrail event","It provides a summary of the event","It contains the timestamp of the event","It contains the user who initiated the event","The `cloudTrailEvent` field contains the complete JSON representation of the CloudTrail event, including all details about the API call."
"When creating a CloudTrail trail, what is the purpose of specifying a log file prefix?","To organise logs within the S3 bucket","To encrypt log files","To compress log files","To filter log files","The log file prefix helps organise logs within the S3 bucket by creating a specific folder structure."
"You are using AWS Organizations and want to create a single CloudTrail trail that logs events for all accounts in your organisation. What type of trail should you create?","Organisational trail","Regional trail","Global trail","Centralised trail","An organizational trail, created via the management account, automatically tracks events for all member accounts."
"Which AWS service can you use to receive notifications when specific CloudTrail events occur, such as a user creating a new IAM role?","Amazon EventBridge (formerly CloudWatch Events)","AWS Config","AWS SNS","Amazon SQS","Amazon EventBridge can be configured to trigger actions based on specific CloudTrail events, enabling automated responses to critical changes."
"What is the role of the AWS Key Management Service (KMS) in securing CloudTrail logs?","To encrypt log files at rest in S3","To authenticate users accessing CloudTrail logs","To authorise API calls made to AWS services","To monitor log file integrity","AWS KMS can be used to encrypt CloudTrail log files at rest in S3, ensuring the confidentiality of the logs."
"You need to determine which IAM role was used to perform a specific action recorded in CloudTrail. Where can you find this information in the CloudTrail event record?","`userIdentity` element","`requestParameters` element","`responseElements` element","`eventSource` element","The `userIdentity` element in the CloudTrail event record contains information about the user or role that performed the action."
"You are investigating an incident and need to correlate CloudTrail events with other log sources, such as VPC Flow Logs. Which piece of information can you use to link these different log sources?","Timestamp of the event","Source IP address","User agent","AWS region","The timestamp of the event is a key piece of information that can be used to correlate CloudTrail events with other log sources."
"What is the maximum size of a CloudTrail log file before it is delivered to S3?","500 MB","1 GB","100 MB","10 GB","The maximum size of a CloudTrail log file before it is delivered to S3 is approximately 500 MB."
"You want to ensure that only authorised personnel can modify your CloudTrail configuration. What IAM best practice should you follow?","Grant least privilege access","Use multi-factor authentication","Rotate access keys regularly","Enable AWS CloudHSM","Granting least privilege access ensures that users only have the permissions they need to perform their tasks, reducing the risk of unauthorised modifications."
"Which action is NOT recorded by AWS CloudTrail by default?","EC2 instance launch","S3 bucket creation","Database query execution","IAM user creation","CloudTrail records API calls made to AWS services, but not database queries. Capturing database queries usually requires database-specific auditing features."
"You are using a custom application that makes API calls to AWS services. How can you ensure that these API calls are captured by CloudTrail?","Ensure the application uses AWS SDKs or CLI with appropriate credentials","Enable VPC Flow Logs","Install the CloudTrail agent on the application server","Configure the application to write logs to CloudWatch Logs","CloudTrail captures API calls made through AWS SDKs, CLI, or the AWS Management Console, as long as the application uses appropriate credentials."
"What is the purpose of the 'errorCode' and 'errorMessage' fields in a CloudTrail event record?","To indicate if an API call failed and provide details about the failure","To provide information about successful API calls","To display the AWS region where the event occurred","To show the IAM role used to make the API call","The 'errorCode' and 'errorMessage' fields indicate whether an API call failed and provide details about the reason for the failure."
"You need to implement a data retention policy for your CloudTrail logs to comply with regulatory requirements. What AWS service feature can help you automate this process?","S3 Lifecycle policies","CloudWatch Logs retention policies","AWS Config rules","AWS Backup","S3 Lifecycle policies allow you to automatically transition older log files to cheaper storage tiers or delete them altogether based on your retention requirements."
"Which of the following is NOT a benefit of using CloudTrail?","Improved security posture","Simplified compliance auditing","Enhanced application performance","Better operational insights","CloudTrail primarily provides benefits related to security, compliance, and operational insights, but it does not directly enhance application performance."
"You need to identify all API calls made by a specific IAM user within a certain timeframe. How can you accomplish this using CloudTrail?","Filter CloudTrail events by user identity and time range","Analyse VPC Flow Logs","Query CloudWatch metrics","Use AWS X-Ray to trace API calls","CloudTrail allows you to filter events by user identity and time range, making it easy to identify API calls made by a specific user within a certain timeframe."
"What is the purpose of the 'userAgent' field in a CloudTrail event record?","To identify the tool or application used to make the API call","To specify the destination AWS region","To record the IAM role used to perform the action","To indicate the source IP address of the API call","The 'userAgent' field identifies the tool or application (e.g., AWS CLI, AWS Management Console, AWS SDK) used to make the API call."
"You are setting up CloudTrail for the first time. What is the recommended best practice for storing the logs?","Create a dedicated S3 bucket with appropriate access controls","Store logs in the same S3 bucket as your application data","Store logs locally on an EC2 instance","Send logs directly to CloudWatch Logs without S3","Creating a dedicated S3 bucket with appropriate access controls is the recommended best practice for storing CloudTrail logs, ensuring security and separation from other data."
"You are managing multiple AWS accounts and want to centrally analyse CloudTrail logs from all accounts. Which AWS service can help you achieve this?","AWS Security Hub","Amazon GuardDuty","AWS Config","AWS Trusted Advisor","AWS Security Hub aggregates security findings and compliance status across multiple AWS accounts, including data from CloudTrail."
"Which security feature can you enable on your S3 bucket containing CloudTrail logs to prevent accidental deletion of log files?","S3 bucket versioning","S3 server access logging","S3 cross-region replication","S3 inventory","Enabling S3 bucket versioning preserves all versions of objects in the bucket, including deleted objects, providing protection against accidental deletion."
"You are troubleshooting an issue and need to examine the request parameters and response elements of a specific API call. Where can you find this information in the CloudTrail event record?","`requestParameters` and `responseElements` elements","`userIdentity` and `eventSource` elements","`errorCode` and `errorMessage` elements","`eventTime` and `eventName` elements","The `requestParameters` and `responseElements` elements contain detailed information about the parameters sent in the API request and the elements returned in the API response."
"What is the relationship between AWS CloudTrail and AWS Config?","CloudTrail records API calls, while AWS Config tracks resource configurations","CloudTrail analyses network traffic, while AWS Config monitors system performance","CloudTrail manages IAM permissions, while AWS Config encrypts data","CloudTrail delivers logs to CloudWatch Logs, while AWS Config visualises the logs","CloudTrail records API calls made to AWS services, while AWS Config tracks the configuration changes of AWS resources."
"What is the primary purpose of AWS CloudTrail?","Auditing and governance","Performance monitoring","Cost optimisation","Security scanning","CloudTrail provides a record of actions taken by a user, role, or AWS service in your AWS account. It helps with auditing and governance."
"Which type of events are captured by default in AWS CloudTrail when creating a new Trail?","Management events","Data events","Insights events","Operational events","By default, CloudTrail captures management events, which provide information about management operations performed on resources in your AWS account."
"Where are AWS CloudTrail logs stored?","Amazon S3 bucket","Amazon EBS volume","Amazon RDS instance","Amazon Glacier archive","CloudTrail logs are stored in an Amazon S3 bucket that you specify when creating a trail."
"What is the purpose of CloudTrail Insights?","Detect unusual activity in your AWS account","Enforce compliance rules","Generate cost reports","Provide real-time alerts","CloudTrail Insights helps you detect unusual activity in your AWS account by analysing CloudTrail management events."
"What is the best way to ensure your AWS CloudTrail logs are not tampered with after delivery to S3?","Enable S3 object versioning and log file integrity validation","Apply an IAM policy that restricts access to the S3 bucket","Encrypt the S3 bucket with KMS","Enable S3 Transfer Acceleration","Enabling S3 object versioning and log file integrity validation helps ensure that your CloudTrail logs are not tampered with after delivery to S3."
"Which AWS service can be integrated with AWS CloudTrail to receive real-time notifications about API calls?","Amazon CloudWatch Events (EventBridge)","Amazon SNS","Amazon SQS","AWS Lambda","Amazon CloudWatch Events (now EventBridge) can be integrated with CloudTrail to receive real-time notifications about API calls made in your AWS account."
"Which type of event does AWS CloudTrail data events record?","Resource operations performed on or within a resource","Changes to AWS resource configurations","Login attempts to the AWS Management Console","Scheduled maintenance events","Data events record the resource operations performed on or within a resource. For example, S3 object access."
"If you want to filter AWS CloudTrail logs based on specific user identities, what should you configure?","Data events selector","Management events selector","Global service events","Insights events","You should configure the management events selector with a filter based on the specific user identities you want to track."
"How can you configure AWS CloudTrail to deliver logs to multiple AWS accounts?","By enabling organisation trails in AWS Organisations","By creating multiple trails with the same S3 bucket destination","By configuring cross-account access for the S3 bucket","By using AWS Config","By enabling organisation trails in AWS Organisations, you can deliver CloudTrail logs from multiple AWS accounts to a central S3 bucket in the management account."
"What is the minimum retention period for AWS CloudTrail logs stored in an S3 bucket?","There is no minimum retention period","7 days","30 days","90 days","There is no minimum retention period for CloudTrail logs stored in an S3 bucket. You control the retention period using S3 lifecycle policies."
"In AWS CloudTrail, what does the 'eventCategory' field indicate?","Whether the event is a management event or a data event","The AWS region where the event occurred","The source IP address of the API call","The error code returned by the API call","The 'eventCategory' field in CloudTrail indicates whether the event is a management event or a data event."
"What IAM permission is required to create an AWS CloudTrail trail?","cloudtrail:CreateTrail","s3:PutObject","iam:CreateRole","logs:CreateLogGroup","The `cloudtrail:CreateTrail` permission is required to create a new CloudTrail trail."
"Which of the following is NOT a benefit of using AWS CloudTrail?","Automated patching of EC2 instances","Compliance auditing","Security analysis","Troubleshooting operational issues","CloudTrail does not provide automated patching of EC2 instances. This is typically handled by other AWS services or third-party tools."
"What is the purpose of the AWS CloudTrail log file integrity validation feature?","To verify that log files have not been modified after delivery","To encrypt log files at rest","To compress log files to reduce storage costs","To automatically delete old log files","The log file integrity validation feature in CloudTrail verifies that log files have not been tampered with or modified after delivery to the S3 bucket."
"What is the default log file format used by AWS CloudTrail?","JSON","CSV","XML","Plain text","CloudTrail logs are delivered in JSON (JavaScript Object Notation) format by default."
"You want to enable AWS CloudTrail for all regions in your AWS account. Which type of trail should you create?","An organisation trail","A multi-region trail","A single-region trail","A global trail","A multi-region trail allows you to capture events across all AWS regions in your AWS account."
"What is the best practice for securing the S3 bucket where AWS CloudTrail logs are stored?","Apply a bucket policy that restricts access to authorised IAM roles and AWS accounts","Enable public read access to the bucket","Disable S3 server access logging","Use a short bucket name","Applying a bucket policy that restricts access to authorised IAM roles and AWS accounts is a critical security best practice for protecting CloudTrail logs."
"What AWS service can you use to analyse AWS CloudTrail logs stored in S3?","Amazon Athena","Amazon CloudWatch Logs Insights","AWS Config","Amazon Inspector","Amazon Athena allows you to query and analyse CloudTrail logs directly from S3 using SQL."
"What type of data is captured by AWS CloudTrail Insights events?","Unusual patterns in API calls","Security vulnerabilities in your infrastructure","Network traffic patterns","Cost anomalies in your AWS account","CloudTrail Insights events capture unusual patterns in API calls, such as spikes in error rates or invocation volumes."
"How does AWS CloudTrail help with compliance requirements?","By providing an audit trail of API activity","By automatically enforcing compliance rules","By generating compliance reports","By encrypting data at rest","CloudTrail helps with compliance requirements by providing an audit trail of API activity in your AWS account, which can be used to demonstrate adherence to various regulatory standards."
"What AWS service can be used to automatically remediate actions identified in AWS CloudTrail logs?","AWS Config Rules","Amazon GuardDuty","AWS Trusted Advisor","AWS Systems Manager Automation","AWS Config Rules can be triggered by CloudTrail events and used to automatically remediate actions that violate compliance rules or security best practices."
"You have an AWS CloudTrail trail configured. Which action will stop CloudTrail from recording events?","Disabling the trail","Deleting the S3 bucket","Deleting the IAM role","Stopping the CloudTrail service","Disabling the trail will stop CloudTrail from recording events in your account."
"Which AWS IAM principal can be used as the recipient of AWS CloudTrail logs stored in S3?","An IAM role","An IAM user","An AWS account ID","A security group","An IAM role is the recommended way for CloudTrail to access the S3 bucket and write logs, as it provides temporary credentials and avoids storing long-term access keys."
"What does AWS CloudTrail log file integrity validation use to verify log file integrity?","SHA-256 hash","MD5 checksum","AES encryption","RSA signature","CloudTrail log file integrity validation uses SHA-256 hashes to verify that the log files have not been tampered with."
"You need to track all API calls made by a specific IAM user in your AWS account. How can you achieve this using AWS CloudTrail?","Configure a data event selector to filter by user identity","Configure a management event selector to filter by user identity","Enable CloudTrail Insights for the user","Enable CloudWatch Logs for the user","Configure a management event selector to filter by the user identity. Management events capture API calls made by users, roles, and AWS services."
"What is the benefit of enabling server-side encryption (SSE) for the S3 bucket where AWS CloudTrail logs are stored?","To protect log data at rest","To prevent unauthorised access to the bucket","To reduce storage costs","To improve query performance","Enabling server-side encryption (SSE) for the S3 bucket protects the log data at rest by encrypting it before it is stored."
"How can you control access to AWS CloudTrail log files in S3?","Using S3 bucket policies and IAM policies","Using AWS CloudTrail event selectors","Using AWS Config rules","Using AWS CloudWatch alarms","You can control access to CloudTrail log files in S3 using S3 bucket policies and IAM policies, which allow you to specify who can access the logs and what actions they can perform."
"What is the purpose of the AWS CloudTrail Lake feature?","To aggregate, store, and query CloudTrail events using SQL-based queries","To visualise CloudTrail data in a dashboard","To send CloudTrail events to SIEM tools","To automatically remediate security issues","CloudTrail Lake allows you to aggregate, store, and query CloudTrail events using SQL-based queries, providing a centralised repository for audit and security analysis."
"What is the difference between CloudTrail events and CloudWatch Logs?","CloudTrail logs API calls, CloudWatch Logs logs application and system logs","CloudTrail logs application logs, CloudWatch Logs logs API calls","CloudTrail is for security monitoring, CloudWatch Logs is for performance monitoring","CloudTrail is for real-time monitoring, CloudWatch Logs is for historical analysis","CloudTrail logs API calls made to AWS services, while CloudWatch Logs is used to collect and monitor application and system logs."
"How can you ensure that your AWS CloudTrail logs are delivered reliably to the S3 bucket?","Enable S3 versioning and lifecycle policies","Enable S3 Transfer Acceleration","Configure CloudTrail to retry failed deliveries","No specific configuration is required, CloudTrail delivery is reliable by default","CloudTrail is designed to reliably deliver logs to the S3 bucket, and no specific configuration is needed to ensure this."
"What is the maximum size of an individual log file delivered by AWS CloudTrail to S3?","512 MB","1 GB","10 GB","Unlimited","The maximum size of an individual log file delivered by CloudTrail to S3 is approximately 512 MB."
"You want to forward your AWS CloudTrail logs to a third-party security information and event management (SIEM) tool. What is the recommended approach?","Configure CloudTrail to send logs directly to the SIEM tool","Use Amazon SQS to queue CloudTrail logs and forward them to the SIEM tool","Use Amazon Kinesis Data Firehose to stream CloudTrail logs to the SIEM tool","Download logs manually and upload them to the SIEM tool","Using Amazon Kinesis Data Firehose to stream CloudTrail logs to the SIEM tool is a common and efficient approach for integrating CloudTrail with third-party security tools."
"What is the purpose of the `cloudtrail:LookupEvents` IAM permission?","To allow users to search for specific events in CloudTrail","To allow users to create CloudTrail trails","To allow users to modify CloudTrail trails","To allow users to delete CloudTrail trails","The `cloudtrail:LookupEvents` IAM permission allows users to search for and view specific events in CloudTrail."
"You need to audit changes made to your AWS IAM policies. Which type of AWS CloudTrail event should you analyse?","Management events","Data events","Insights events","CloudWatch events","You should analyse management events in CloudTrail, as these events capture changes made to AWS IAM policies and other management operations."
"What is a typical use case for filtering AWS CloudTrail logs based on the event name?","Identifying specific API calls related to security incidents","Tracking all changes made to S3 buckets","Monitoring all user login attempts","Auditing all EC2 instance launches","Filtering CloudTrail logs based on the event name allows you to identify specific API calls related to security incidents or other areas of interest."
"How can you automate the process of analysing AWS CloudTrail logs for security threats?","Integrate CloudTrail with Amazon GuardDuty or a SIEM tool","Enable CloudWatch Logs Insights","Enable CloudTrail Insights","Use AWS Trusted Advisor","Integrating CloudTrail with Amazon GuardDuty or a SIEM tool allows you to automate the analysis of CloudTrail logs for security threats and anomalies."
"Which of the following AWS services is commonly used to visualise AWS CloudTrail data?","Amazon QuickSight","Amazon CloudWatch Dashboards","AWS Config Dashboard","Amazon Inspector Dashboard","Amazon QuickSight can be used to visualise CloudTrail data, creating dashboards and reports to gain insights into API activity and security events."
"You need to determine which IAM role was used to launch an EC2 instance. How can you find this information using AWS CloudTrail?","Examine the 'userIdentity' field in the CloudTrail event","Check the EC2 instance metadata","Check the CloudWatch logs for the instance","Check the S3 bucket logs","The 'userIdentity' field in the CloudTrail event for the EC2 instance launch will contain information about the IAM role used to launch the instance."
"What is the purpose of the AWS CloudTrail 'errorCode' and 'errorMessage' fields?","To provide details about API call failures","To provide details about successful API calls","To provide details about network latency","To provide details about resource utilisation","The 'errorCode' and 'errorMessage' fields in CloudTrail provide details about API call failures, helping you troubleshoot errors and identify potential issues."
"How does AWS CloudTrail contribute to incident response?","By providing a historical record of API activity","By automatically blocking malicious traffic","By automatically patching vulnerabilities","By automatically scaling resources","CloudTrail provides a historical record of API activity, which can be invaluable for investigating security incidents and understanding the sequence of events."
"What is the maximum number of AWS CloudTrail trails that can be created per AWS account per region?","Five","One","Unlimited","Ten","You can create up to five CloudTrail trails per AWS account per region."
"What information can be used to correlate CloudTrail logs with other log sources, such as application logs or network logs?","The event ID and timestamp","The region and account ID","The source IP address and user agent","The resource name and event name","The event ID and timestamp can be used to correlate CloudTrail logs with other log sources, allowing you to build a more complete picture of events in your environment."
"You want to track all API calls made to your AWS KMS keys. Which type of AWS CloudTrail event should you enable?","Data events","Management events","Insights events","CloudWatch events","You should enable data events for your AWS KMS keys to track all API calls made to them."
"What is the key benefit of using AWS CloudTrail Lake over directly querying CloudTrail logs in S3?","Improved query performance and scalability","Automatic data encryption","Real-time data analysis","Lower storage costs","CloudTrail Lake provides improved query performance and scalability compared to directly querying CloudTrail logs in S3, allowing you to analyse large volumes of data more efficiently."
"Which AWS service is commonly used to receive alerts based on AWS CloudTrail events?","Amazon CloudWatch Alarms","AWS Trusted Advisor","Amazon Inspector","Amazon GuardDuty","Amazon CloudWatch Alarms can be configured to trigger alerts based on specific CloudTrail events, allowing you to respond quickly to critical events."
"How does AWS CloudTrail help with detecting insider threats?","By providing an audit trail of user activity","By automatically blocking suspicious IP addresses","By automatically detecting malware","By automatically enforcing password policies","CloudTrail provides an audit trail of user activity, which can be used to detect unusual or suspicious behaviour that may indicate an insider threat."
"What is the primary difference between a CloudTrail trail and a CloudTrail Lake event data store?","A trail delivers logs to S3, while an event data store stores events for querying with SQL.","A trail is for management events, while an event data store is for data events.","A trail is for real-time monitoring, while an event data store is for historical analysis.","A trail is for single-region events, while an event data store is for multi-region events.","A trail delivers logs to S3 for storage and analysis, while a CloudTrail Lake event data store stores events for querying with SQL-based queries, providing a more structured and efficient way to analyse large volumes of data."
"What is the most efficient way to search for a specific API call across all AWS accounts in your organisation using AWS CloudTrail?","Use CloudTrail Lake and run a SQL query across the organisation.","Search the S3 buckets for each account individually.","Use AWS Config to query the API call across all accounts.","Use the CloudTrail console to search each account sequentially.","CloudTrail Lake, with its SQL-based querying capabilities, offers the most efficient way to search for a specific API call across all AWS accounts in your organisation, providing a centralised and scalable solution."
"What is the primary purpose of AWS CloudTrail?","To track API calls made to your AWS account","To monitor network traffic within your VPC","To manage user access to AWS resources","To store application logs","CloudTrail is designed to record API calls made to AWS services, providing an audit trail of actions performed in your account."
"What type of data does AWS CloudTrail log?","API calls made to AWS services","Application logs","Operating system logs","Network traffic logs","CloudTrail logs API calls made to AWS services, including the identity of the caller, the time of the call, the request parameters, and the response elements returned by the AWS service."
"Where can AWS CloudTrail log files be stored?","Amazon S3 bucket","Amazon EBS volume","Amazon RDS instance","Amazon EC2 instance store","CloudTrail log files are stored in an Amazon S3 bucket that you specify when you configure CloudTrail."
"How can you ensure the integrity of AWS CloudTrail log files?","Enable CloudTrail log file validation","Encrypt the S3 bucket containing the logs","Use IAM roles to restrict access to the logs","Enable VPC Flow Logs","CloudTrail log file validation uses digital signatures to ensure that the log files have not been tampered with since they were delivered by CloudTrail."
"What is the benefit of integrating AWS CloudTrail with Amazon CloudWatch Logs?","Real-time monitoring of API activity","Centralised storage of application logs","Automated backups of CloudTrail logs","Enhanced network security","Integrating CloudTrail with CloudWatch Logs enables you to monitor API activity in near real-time and receive alerts based on specific API events."
"Which AWS service can be used to analyse AWS CloudTrail logs for security and compliance purposes?","AWS Security Hub","AWS Config","Amazon Inspector","AWS Trusted Advisor","AWS Security Hub can analyse CloudTrail logs to provide security insights and identify potential security issues and compliance violations."
"What is the scope of an AWS CloudTrail trail when configured as an 'Organization trail'?","The trail applies to all accounts in the AWS Organization","The trail applies only to the management account","The trail applies only to selected member accounts","The trail applies only to the region where it is created","An 'Organization trail' applies to all accounts that are part of the AWS Organization, providing centralized logging across the entire organization."
"Can you use AWS CloudTrail to log data events, such as S3 object access?","Yes, CloudTrail can log data events for S3 objects and Lambda functions","No, CloudTrail only logs management events","CloudTrail can only log events for EC2 instances","CloudTrail can only log events for IAM users","CloudTrail can log both management events (control plane operations) and data events (data plane operations), such as S3 object access and Lambda function execution."
"Which AWS IAM permission is required to create a new AWS CloudTrail?","cloudtrail:CreateTrail","s3:PutObject","iam:CreateRole","ec2:RunInstances","The `cloudtrail:CreateTrail` permission is required to create a new CloudTrail in your AWS account."
"What is the retention period for AWS CloudTrail event history if not integrated with S3?","90 days","30 days","7 days","1 year","If you do not configure a CloudTrail trail to deliver logs to an S3 bucket, the event history is retained for 90 days."
"What is the primary function of AWS CloudTrail?","To log and monitor API calls made to AWS services","To provide serverless compute functions","To store and manage static website content","To provide a fully managed NoSQL database service","CloudTrail's primary function is to log and monitor API calls made to AWS services, providing an audit trail for security and compliance."
"Which AWS service integrates directly with AWS CloudTrail to provide security information and event management (SIEM) capabilities?","Amazon GuardDuty","Amazon S3","Amazon EC2","Amazon RDS","Amazon GuardDuty integrates directly with CloudTrail to analyse CloudTrail logs and detect malicious activity or unauthorized behaviour in your AWS environment."
"Where are AWS CloudTrail log files typically stored?","Amazon S3 bucket","Amazon EBS volume","Amazon Glacier vault","Amazon Redshift cluster","CloudTrail log files are stored in an Amazon S3 bucket that you specify during the CloudTrail configuration."
"What type of events does AWS CloudTrail log?","Management and data events","Network traffic","CPU utilisation","Memory usage","CloudTrail logs both management events (control plane operations) and data events (data plane operations)."
"Which of the following is a management event in AWS CloudTrail?","Creating an IAM user","Reading an object from Amazon S3","Writing data to a DynamoDB table","Sending a message to an SQS queue","Creating an IAM user is a management event, as it involves control plane operations related to managing your AWS account and resources."
"Which of the following is a data event in AWS CloudTrail?","Accessing an object in an S3 bucket","Creating a security group","Launching an EC2 instance","Deleting a VPC","Accessing an object in an S3 bucket is a data event, as it involves data plane operations related to accessing resources within a service."
"What is the best way to ensure the integrity of CloudTrail log files?","Enable CloudTrail log file validation","Enable S3 server access logging","Enable VPC Flow Logs","Enable AWS Config rules","Enabling CloudTrail log file validation helps ensure that the log files haven't been tampered with after delivery to the S3 bucket."
"What is the retention policy for AWS CloudTrail logs stored in S3?","Logs are stored indefinitely unless lifecycle policies are configured","Logs are stored for 30 days","Logs are stored for 90 days","Logs are stored for 1 year","CloudTrail logs stored in S3 are kept indefinitely unless you configure lifecycle policies to manage or delete them."
"You need to investigate a change made to a security group. Where would you look for this information?","AWS CloudTrail logs","AWS Trusted Advisor","AWS Service Health Dashboard","Amazon CloudWatch metrics","Changes to security groups are recorded as management events in AWS CloudTrail logs, providing a detailed audit trail."
"What is the purpose of creating multiple trails in AWS CloudTrail?","To log events in different regions or accounts separately","To improve the performance of CloudTrail","To reduce the cost of using CloudTrail","To simplify the configuration of CloudTrail","Multiple trails can be created to log events in different regions or AWS accounts separately, allowing for more granular control and auditing."