"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Auto Scaling, what is the primary function of a Launch Template?","Specifies the configuration for launching EC2 instances.","Defines scaling policies.","Monitors EC2 instance health.","Manages network security groups.","A Launch Template specifies the AMI, instance type, key pair, security groups, and other configurations needed to launch an EC2 instance."
"Which of the following AWS services integrates directly with Auto Scaling to distribute traffic across multiple EC2 instances?","Elastic Load Balancing (ELB)","Amazon S3","Amazon RDS","Amazon CloudFront","ELB automatically distributes incoming application traffic across multiple targets, such as EC2 instances, in multiple Availability Zones."
"What is the purpose of a 'Desired Capacity' in the context of AWS Auto Scaling?","The number of instances the group should maintain.","The maximum number of instances the group can scale to.","The minimum number of instances the group can scale to.","The number of instances ready for termination.","Desired Capacity is the number of EC2 instances that the Auto Scaling group attempts to maintain."
"When configuring an Auto Scaling group in AWS, which of the following determines how the group responds to changes in demand?","Scaling Policies","Launch Configuration","Availability Zones","Security Groups","Scaling policies define how an Auto Scaling group responds to changing demand, such as CPU utilisation or network traffic."
"Which type of scaling policy adjusts the number of instances based on a set of scaling adjustments that vary based on the size of the alarm breach?","Step Scaling","Target Tracking Scaling","Simple Scaling","Scheduled Scaling","Step Scaling allows you to specify a range of scaling adjustments based on the size of the CloudWatch alarm breach."
"What does the 'Cooldown Period' in AWS Auto Scaling refer to?","The time after a scaling activity completes before another can begin.","The time it takes for a new instance to launch.","The time it takes for an instance to terminate.","The time an instance is idle before being terminated.","The cooldown period prevents the Auto Scaling group from launching or terminating additional instances before the effects of previous scaling actions are visible."
"What is the purpose of Lifecycle Hooks in AWS Auto Scaling?","To perform custom actions before or after an instance launches or terminates.","To define the instance type for Auto Scaling groups.","To automatically update the AMI used by the Auto Scaling group.","To manage the security groups for instances launched by the Auto Scaling group.","Lifecycle Hooks enable you to perform custom actions, such as installing software or downloading data, before an instance is put into service or before it is terminated."
"In AWS Auto Scaling, what is the difference between a Launch Configuration and a Launch Template?","Launch Templates offer versioning and support more configuration options.","Launch Configurations offer versioning and support more configuration options.","Launch Templates are only for EC2 instances with Auto Scaling.","Launch Configurations are only for EC2 instances with Auto Scaling.","Launch Templates are the successor to Launch Configurations, offering versioning, parameter subsets, and support for the latest EC2 features."
"Which of the following is a benefit of using AWS Auto Scaling?","Improved application availability and fault tolerance.","Simplified database management.","Automated code deployment.","Enhanced network security.","Auto Scaling ensures that you have the correct number of EC2 instances available to handle the load for your application, improving availability and fault tolerance."
"Which scaling metric is commonly used with Target Tracking Scaling in AWS Auto Scaling?","Average CPU utilisation of the Auto Scaling group.","Total number of requests to the application.","Network latency between instances.","Disk space usage on instances.","Target Tracking Scaling automatically adjusts the number of EC2 instances to maintain a specific target value for a chosen metric, such as CPU utilisation."
"What is the function of an 'Availability Zone' in the context of AWS Auto Scaling?","To provide redundancy and fault tolerance.","To define the scaling policies.","To specify the instance type for Auto Scaling groups.","To manage the security groups for instances launched by the Auto Scaling group.","Availability Zones are distinct locations within an AWS Region that are designed to be isolated from failures in other Availability Zones, providing redundancy and fault tolerance."
"Which of the following is a component of an Auto Scaling group?","Launch template or configuration, minimum size, desired capacity, and maximum size.","IAM role, security group, and VPC subnet.","AMI, key pair, and Elastic IP address.","Scaling policies, CloudWatch alarms, and SNS topics.","An Auto Scaling group is defined by its launch template or configuration, minimum size, desired capacity, and maximum size."
"What type of scaling policy allows you to specify different scaling actions at different times of the day?","Scheduled Scaling","Simple Scaling","Step Scaling","Target Tracking Scaling","Scheduled Scaling allows you to define scaling actions based on a schedule, such as increasing capacity during peak hours."
"You need to ensure that your application scales automatically based on the number of incoming requests. Which type of scaling policy is best suited for this scenario?","Target Tracking Scaling","Simple Scaling","Step Scaling","Scheduled Scaling","Target Tracking Scaling is ideal for maintaining a specific metric, such as requests per target, by automatically scaling resources."
"What happens when an instance in an Auto Scaling group fails a health check performed by Elastic Load Balancing?","The instance is automatically terminated and replaced.","The instance is isolated and traffic is redirected to other instances.","The instance is rebooted and returned to service.","The instance is placed in a 'pending termination' state.","When an instance fails a health check, Auto Scaling automatically terminates the instance and launches a new one to replace it."
"Which configuration ensures that your Auto Scaling group distributes instances evenly across multiple Availability Zones?","Enabling cross-Availability Zone load balancing in Elastic Load Balancing.","Using a Launch Configuration with a specified Availability Zone.","Setting the 'Availability Zone Balance' scaling policy.","Creating separate Auto Scaling groups for each Availability Zone.","Cross-Availability Zone load balancing in Elastic Load Balancing is enabled by default and helps to distribute traffic evenly across multiple Availability Zones, ensuring that instances are evenly distributed."
"What is the purpose of 'Instance Warmup' in AWS Auto Scaling?","To allow an instance to initialise before receiving traffic.","To reduce the cost of running idle instances.","To minimise the impact of scaling events on application performance.","To automatically update the AMI used by the Auto Scaling group.","Instance Warmup allows an instance to initialise and warm up its caches before receiving traffic, helping to minimise the impact of scaling events on application performance."
"You want to use a custom metric to scale your Auto Scaling group. Which AWS service do you need to integrate with Auto Scaling to achieve this?","Amazon CloudWatch","AWS Lambda","Amazon SQS","Amazon SNS","Amazon CloudWatch is used to monitor custom metrics and trigger scaling events in Auto Scaling."
"Which AWS service can you use to send notifications when scaling events occur in your Auto Scaling group?","Amazon SNS","Amazon SQS","Amazon CloudWatch","AWS Lambda","Amazon SNS (Simple Notification Service) can be used to send notifications when scaling events occur in your Auto Scaling group."
"Which is the correct order of operations when Auto Scaling terminates an EC2 instance?","Detach from load balancer, Lifecycle Hook (if applicable), terminate the instance.","Terminate the instance, Detach from load balancer, Lifecycle Hook (if applicable).","Lifecycle Hook (if applicable), Terminate the instance, Detach from load balancer.","Terminate the instance, Lifecycle Hook (if applicable), Detach from load balancer.","The order is: Detach from ELB, Lifecycle Hook, and then Terminate."
"What happens when the Minimum Capacity of an Auto Scaling Group is higher than the Desired Capacity?","The Auto Scaling Group will launch instances to meet the Minimum Capacity.","The Auto Scaling Group will launch instances to meet the Desired Capacity.","The Auto Scaling Group will terminate instances to meet the Desired Capacity.","The Auto Scaling Group will terminate instances to meet the Minimum Capacity.","The Auto Scaling Group will always ensure it has at least the Minimum Capacity, even if Desired Capacity is lower."
"How can you update the AMI used by an Auto Scaling Group without downtime?","Create a new Launch Template with the updated AMI and update the Auto Scaling Group to use it, performing a rolling update.","Directly modify the existing Launch Configuration of the Auto Scaling Group.","Stop and restart the Auto Scaling Group.","Manually replace each instance in the Auto Scaling Group with a new instance based on the updated AMI.","Using a new Launch Template and performing a rolling update allows for zero downtime deployments."
"What is the purpose of the 'Health Check Type' setting in an Auto Scaling Group?","To determine how Auto Scaling monitors the health of instances.","To specify the instance type for Auto Scaling groups.","To configure network security settings for instances.","To set the desired capacity of the Auto Scaling group.","The Health Check Type determines how Auto Scaling monitors the health of instances (EC2 or ELB). If an instance is unhealthy, Auto Scaling will terminate it and launch a new one."
"What happens if you delete a Launch Template that is being used by an Auto Scaling group?","The Auto Scaling group continues to function using the latest version of the Launch Template.","The Auto Scaling group stops functioning and must be recreated with a new Launch Template.","The Auto Scaling group automatically switches to a default Launch Configuration.","The Auto Scaling group switches to using a previously used Launch Template.","The Auto Scaling group continues to function using the latest version of the Launch Template at the time of deletion."
"How can you integrate AWS Auto Scaling with a container orchestration service like Amazon ECS?","By using ECS Service Auto Scaling, which automatically scales the number of tasks in an ECS service.","By manually creating Auto Scaling groups and configuring them to launch container instances.","By using AWS Lambda to trigger scaling events based on container metrics.","By directly deploying container images to EC2 instances launched by Auto Scaling.","ECS Service Auto Scaling automatically adjusts the desired count of an ECS service based on metrics like CPU or memory utilisation."
"Which AWS service can you use to monitor the performance of your Auto Scaling group and identify potential bottlenecks?","Amazon CloudWatch","AWS CloudTrail","Amazon Config","AWS X-Ray","Amazon CloudWatch provides metrics and monitoring capabilities to track the performance of your Auto Scaling group, including CPU utilisation, network traffic, and disk I/O."
"What is the benefit of using a 'Mixed Instances Policy' in AWS Auto Scaling?","To allow Auto Scaling to launch different instance types based on availability and pricing.","To enable Auto Scaling to launch instances in both public and private subnets.","To allow Auto Scaling to switch between different AMIs based on performance.","To enable Auto Scaling to scale across multiple AWS Regions.","A Mixed Instances Policy allows Auto Scaling to launch different instance types based on availability and pricing, helping to optimise cost and performance."
"When using Simple Scaling policies in AWS Auto Scaling, what happens if multiple scaling events occur within the cooldown period?","The scaling events are queued and executed after the cooldown period ends.","Only the first scaling event is executed; subsequent events are ignored.","The cooldown period is reset with each scaling event.","The scaling events are merged into a single scaling action.","With Simple Scaling policies, only the first scaling event is executed and subsequent events are ignored until the cooldown period ends."
"You want to ensure that your Auto Scaling group always has at least two instances running, even during periods of low demand. What configuration should you use?","Set the Minimum Capacity to 2.","Set the Desired Capacity to 2.","Set the Maximum Capacity to 2.","Set the Default Cooldown period to 2.","Setting the Minimum Capacity to 2 ensures that the Auto Scaling group always has at least two instances running."
"What is the purpose of the 'Suspended Processes' feature in AWS Auto Scaling?","To temporarily disable specific scaling processes.","To permanently disable scaling policies.","To pause all scaling activity.","To delete scaling policies.","Suspended Processes allow you to temporarily disable specific scaling processes within an Auto Scaling group, providing fine-grained control over scaling behavior."
"You need to ensure that your Auto Scaling group only scales up during peak hours. Which type of scaling policy is best suited for this scenario?","Scheduled Scaling","Simple Scaling","Step Scaling","Target Tracking Scaling","Scheduled Scaling is the best option as it allows you to define scaling actions based on a pre-defined schedule, making it ideal for scaling up during peak hours."
"What is the purpose of the 'Termination Policies' setting in an Auto Scaling Group?","To define which instances are terminated first during a scale-in event.","To prevent instances from being terminated.","To control the order in which instances are launched.","To specify the instance type for Auto Scaling groups.","Termination Policies define which instances are terminated first during a scale-in event, helping to minimise disruption to your application."
"What is the advantage of using instance protection with Auto Scaling?","It prevents EC2 instances from being terminated during scale-in events.","It automatically encrypts data on EC2 instances.","It allows you to specify the instance type for Auto Scaling groups.","It enables Auto Scaling to launch instances in multiple AWS Regions.","Instance protection prevents EC2 instances from being terminated during scale-in events."
"Which AWS service allows you to define infrastructure as code and automate the provisioning of Auto Scaling groups?","AWS CloudFormation","Amazon CloudWatch","AWS Lambda","AWS Config","AWS CloudFormation enables you to define infrastructure as code, automating the provisioning and management of Auto Scaling groups and other AWS resources."
"What is the relationship between AWS Auto Scaling and AWS CloudWatch Alarms?","CloudWatch Alarms can trigger scaling policies in Auto Scaling.","Auto Scaling automatically creates CloudWatch Alarms.","Auto Scaling replaces CloudWatch Alarms.","CloudWatch Alarms are not related to Auto Scaling.","CloudWatch Alarms are used to monitor metrics and trigger scaling policies in Auto Scaling based on defined thresholds."
"Which instance state does Auto Scaling place instances in after they launch, but before they are considered healthy and in service?","InService","Pending","Warming","Detached","Auto Scaling places instances in 'Warming' state after launch, so that they have time to initialise before the application sends traffic to them."
"You want to ensure that new EC2 instances launched by your Auto Scaling group are automatically registered with a load balancer. How can you achieve this?","Configure the Auto Scaling group to use a target group associated with the load balancer.","Manually register each new instance with the load balancer.","Use AWS Lambda to trigger instance registration with the load balancer.","Configure the Auto Scaling group to use a Launch Configuration with a specified load balancer.","Associating the Auto Scaling group with a target group connected to the load balancer ensures that new instances are automatically registered and traffic is routed to them."
"Which metric can you use to create a Target Tracking scaling policy in AWS Auto Scaling to scale based on the average CPU utilization across all instances in an Auto Scaling group?","ASGAverageCPUUtilization","CPUUtilization","NetworkIn","DiskWriteOps","ASGAverageCPUUtilization is the correct metric to use for average CPU utilization in Target Tracking scaling policies within Auto Scaling."
"What is the primary benefit of integrating Auto Scaling with EC2 Fleet or Spot Fleets?","Optimising costs by utilizing a mix of On-Demand, Reserved and Spot Instances.","Improving security by automatically rotating IAM roles.","Increasing instance availability by launching instances across multiple AWS regions.","Simplifying the process of configuring scaling policies.","Auto Scaling can be used with EC2 Fleet or Spot Fleets to leverage different pricing models (On-Demand, Reserved and Spot) to optimize costs."
"What is the impact of enabling 'Instance Refresh' in an Auto Scaling group?","It gradually replaces all instances in the group, allowing for rolling updates.","It immediately terminates all instances and launches new ones.","It upgrades the operating system on existing instances.","It automatically applies security patches to the instances.","Instance Refresh enables gradual replacement of instances, allowing for rolling deployments and updates within the Auto Scaling group."
"Which feature allows you to define a custom health check for instances in your Auto Scaling group?","Lifecycle Hooks","Custom Health Checks","Health Check Grace Period","Termination Policies","Lifecycle Hooks can be used to integrate custom health checks before new instances are considered healthy."
"What is the benefit of using multiple launch templates in an Auto Scaling group?","Ability to roll instances between different operating systems.","Easier infrastructure as code deployments.","Enhanced cost savings.","Increased operational efficiency.","Multiple launch templates would allow rolling instances between different operating systems."
"Which of the following statements is true regarding rolling deployments with AWS Auto Scaling?","Auto Scaling can perform rolling deployments with zero downtime.","Auto Scaling rolling deployments require manual approval for each instance replacement.","Auto Scaling rolling deployments are only supported for EC2 instances with EBS volumes.","Auto Scaling does not support rolling deployments.","Auto Scaling can indeed perform rolling deployments with zero downtime, ensuring high availability during updates."
"When configuring 'Step Adjustments' in a scaling policy, what is the purpose of the 'Lower Bound' and 'Upper Bound' parameters?","Define a range of metric values to apply a specific scaling adjustment.","Specify the minimum and maximum number of instances in the Auto Scaling group.","Set the cooldown period for the scaling policy.","Determine the priority of scaling actions.","The 'Lower Bound' and 'Upper Bound' parameters define a range of metric values to trigger a specific scaling adjustment, providing granular control over scaling behavior."
"In AWS Auto Scaling, what determines when a new EC2 instance is launched or terminated?","Scaling policies","Security groups","IAM roles","Launch configurations","Scaling policies define the conditions under which Auto Scaling launches or terminates instances."
"Which component of AWS Auto Scaling provides a template for launching EC2 instances?","Launch template or Launch configuration","Auto Scaling group","Scaling policy","CloudWatch alarm","The Launch template or Launch configuration specifies the instance type, AMI, key pair, security groups, and other settings for newly launched instances."
"What is the purpose of a 'target tracking scaling policy' in AWS Auto Scaling?","To maintain a specific metric at a desired value","To manually adjust the number of instances","To scale based on a schedule","To scale based on instance health","Target tracking scaling policies automatically adjust the number of instances to maintain a specific metric (e.g., CPU utilisation) at a desired value."
"What is the benefit of using lifecycle hooks in AWS Auto Scaling?","To perform custom actions before or after an instance is launched or terminated","To automatically update the operating system on instances","To enforce security policies on instances","To monitor the health of instances","Lifecycle hooks allow you to perform custom actions (e.g., installing software, downloading data) before an instance enters service or after it's terminated."
"How does AWS Auto Scaling contribute to high availability?","By automatically replacing unhealthy instances","By automatically backing up data","By automatically patching operating systems","By automatically encrypting data","Auto Scaling maintains a desired capacity, automatically replacing unhealthy instances to ensure applications remain available."
"What is the primary function of an Auto Scaling group?","To maintain a desired number of EC2 instances","To monitor EC2 instance CPU utilisation","To manage EBS volumes","To configure VPC settings","An Auto Scaling group ensures that the desired number of EC2 instances are running to handle application traffic."
"Which AWS service is commonly used to monitor metrics for triggering Auto Scaling policies?","Amazon CloudWatch","Amazon S3","Amazon SNS","Amazon SQS","Amazon CloudWatch provides the metrics that Auto Scaling policies use to determine when to scale in or out."
"What type of scaling policy in AWS Auto Scaling increases or decreases capacity based on a fixed schedule?","Scheduled scaling","Simple scaling","Step scaling","Target tracking scaling","Scheduled scaling allows you to scale based on a predictable schedule, such as increasing capacity during peak hours."
"In AWS Auto Scaling, what happens when an instance fails a health check?","The instance is automatically terminated and replaced","The instance is automatically rebooted","The instance is taken out of service but not terminated","The Auto Scaling group is paused","Auto Scaling automatically terminates instances that fail health checks and launches new instances to maintain the desired capacity."
"Which of the following is NOT a benefit of using AWS Auto Scaling?","Increased cost","Improved fault tolerance","Automated scaling","Enhanced availability","While Auto Scaling improves availability and fault tolerance, it does not lead to increased costs if not configured well."
"Which scaling policy allows you to define multiple adjustment actions based on the size of the alarm breach?","Step scaling policy","Simple scaling policy","Target tracking scaling policy","Scheduled scaling policy","Step scaling policies allow you to define different adjustment actions based on the size of the alarm breach, providing more granular control over scaling."
"What is the purpose of a 'Cooldown period' in AWS Auto Scaling?","To prevent scaling actions from occurring too frequently","To automatically update the AMI used by the Launch Configuration","To automatically install security patches","To automatically enable detailed monitoring on new instances","Cooldown periods prevent Auto Scaling from launching or terminating instances too rapidly after a scaling event."
"Which of the following is a valid target metric for target tracking scaling policies in AWS Auto Scaling?","Average CPU utilisation","Disk space usage","Network latency","Number of users logged in","Average CPU utilisation is a commonly used target metric for target tracking scaling policies to maintain a desired level of CPU utilisation across instances."
"What is the role of a load balancer in an Auto Scaling group setup?","To distribute traffic evenly across instances","To monitor the health of instances","To automatically back up data","To encrypt data in transit","A load balancer distributes incoming traffic evenly across the healthy instances in the Auto Scaling group, ensuring optimal performance and availability."
"When using AWS Auto Scaling with a load balancer, what is the recommended way to ensure new instances are added to the load balancer?","Use Elastic Load Balancing health checks","Configure Auto Scaling to automatically update the load balancer","Manually add instances to the load balancer","Use a custom script to register instances","Elastic Load Balancing health checks ensure that the load balancer knows when new instances are healthy and ready to receive traffic, automatically adding them to the load balancer."
"Which AWS service can be used to send notifications when scaling events occur in AWS Auto Scaling?","Amazon SNS","Amazon S3","Amazon CloudWatch","Amazon RDS","Amazon SNS (Simple Notification Service) can be configured to send notifications when Auto Scaling events such as launch, terminate, or failure occur."
"What is the maximum number of Auto Scaling groups you can have per AWS region?","200","100","50","Unlimited","The default limit is 200, but you can request an increase from AWS support."
"You want to ensure that your Auto Scaling group has a minimum number of 3 instances running at all times. Which Auto Scaling group parameter do you configure?","Minimum capacity","Desired capacity","Maximum capacity","Default capacity","The minimum capacity parameter specifies the minimum number of instances that Auto Scaling should maintain in the group."
"Which feature of AWS Auto Scaling allows you to define a startup script that runs when an instance is launched?","User data","Launch configuration template","Scaling policy action","CloudWatch alarm action","User data is a feature that allows you to specify a script that runs when an instance is launched, enabling you to automate tasks like installing software or configuring the instance."
"What is the primary advantage of using launch templates over launch configurations in AWS Auto Scaling?","Launch templates support versioning and parameter subsets","Launch configurations support versioning and parameter subsets","Launch templates are simpler to configure","Launch configurations are cheaper to use","Launch templates offer versioning and the ability to create parameter subsets, which provides greater flexibility and control over instance configurations."
"You want to scale your application based on the number of requests in an SQS queue. Which type of scaling policy is most suitable?","Custom metric scaling","Target tracking scaling","Scheduled scaling","Simple scaling","Custom metric scaling allows you to scale based on any metric available in CloudWatch, including the number of requests in an SQS queue."
"What is the best practice for handling graceful shutdowns of instances in an Auto Scaling group?","Use lifecycle hooks to drain connections","Forcefully terminate instances","Ignore shutdown processes","Immediately detach instances from the load balancer","Lifecycle hooks allow you to perform actions before an instance is terminated, such as draining connections to prevent data loss."
"Which setting in an Auto Scaling group determines the largest number of EC2 instances the group can launch?","Maximum capacity","Desired capacity","Minimum capacity","Initial capacity","The maximum capacity setting defines the upper limit of EC2 instances that the Auto Scaling group can scale to."
"You need to ensure that your Auto Scaling group distributes instances across multiple Availability Zones. Which setting should you configure?","Multi-AZ deployment","Placement groups","VPC peering","Elastic IP addresses","Multi-AZ deployment ensures that instances are distributed across multiple Availability Zones, providing high availability and fault tolerance."
"What is the purpose of the 'Availability Zone Rebalance' feature in AWS Auto Scaling?","To redistribute instances evenly across Availability Zones","To automatically upgrade instances to newer instance types","To automatically back up instances in different regions","To automatically apply security patches to instances","Availability Zone Rebalance ensures that instances are evenly distributed across all Availability Zones in the group, improving resilience."
"You are using a launch template with AWS Auto Scaling. How do you ensure that all new instances use the latest version of the launch template?","Specify the $Latest version","Specify the $Default version","Use a specific version number","Manually update the launch template","Specifying $Latest ensures that the Auto Scaling group always uses the most recent version of the launch template when launching new instances."
"What is the correct order of steps for configuring Auto Scaling with a load balancer?","1. Create a launch configuration/template, 2. Create an Auto Scaling group, 3. Attach the load balancer","1. Create an Auto Scaling group, 2. Create a launch configuration/template, 3. Attach the load balancer","1. Attach the load balancer, 2. Create an Auto Scaling group, 3. Create a launch configuration/template","1. Create a load balancer, 2. Create a launch configuration/template, 3. Create an Auto Scaling group","This is the correct order to ensure that the Auto Scaling group can properly launch instances based on the launch configuration/template and register them with the load balancer."
"Which type of health check can you configure for an Auto Scaling group?","EC2 and ELB health checks","RDS and EC2 health checks","S3 and CloudWatch health checks","VPC and Route53 health checks","Auto Scaling supports EC2 health checks (instance status) and ELB health checks (application health)."
"What is the advantage of using 'Instance Protection' in AWS Auto Scaling?","To prevent instances from being terminated during scale-in events","To automatically back up instance data","To encrypt instance data at rest","To prevent instances from being stopped","Instance Protection prevents instances from being terminated during scale-in events, allowing you to gracefully handle application shutdowns and data persistence."
"How do you determine the cost of running an Auto Scaling group?","By using the AWS Cost Explorer","By manually calculating instance costs","By using the AWS Pricing Calculator","By reviewing the AWS Trusted Advisor recommendations","AWS Cost Explorer allows you to analyse the cost of your AWS resources, including Auto Scaling groups, providing insights into your spending."
"Which AWS service allows you to centrally manage and automate tasks across multiple AWS accounts, including Auto Scaling configurations?","AWS Systems Manager","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS Systems Manager provides tools to automate tasks across multiple AWS accounts, including managing Auto Scaling configurations."
"What is the effect of setting a 'Termination Policy' on an Auto Scaling group?","It determines which instances are terminated first during a scale-in event","It determines which instances are launched first during a scale-out event","It determines when the Auto Scaling group is deleted","It determines the maximum lifetime of instances","The termination policy defines the order in which instances are terminated during a scale-in event, allowing you to control which instances are removed first."
"What is the role of the 'Placement Group' setting in a launch template?","To specify the physical placement of instances within an Availability Zone","To specify the number of instances to launch","To specify the region in which to launch instances","To specify the security group to assign to instances","The Placement Group setting allows you to control the physical placement of instances, which can improve network performance for tightly coupled applications."
"How can you ensure that your Auto Scaling group is compliant with security best practices?","By integrating with AWS Security Hub","By manually reviewing security configurations","By using AWS Config rules","By using AWS Trusted Advisor","Integrating with AWS Security Hub allows you to monitor the security posture of your Auto Scaling group and identify potential security vulnerabilities."
"You want to update the AMI used by your Auto Scaling group. What is the recommended approach?","Create a new launch template/configuration with the updated AMI and update the Auto Scaling group","Manually update the AMI on each instance","Stop and restart all instances in the Auto Scaling group","Edit the existing launch configuration","Creating a new launch template/configuration with the updated AMI and updating the Auto Scaling group ensures that new instances will use the new AMI without interrupting existing instances."
"Which of the following is NOT a valid scaling policy type in AWS Auto Scaling?","Predictive scaling","Target tracking scaling","Scheduled scaling","Simple scaling","Predictive scaling is not a native Auto Scaling policy. It's available through other AWS services."
"Which metric is commonly used for web applications where you want to scale based on the amount of work each instance is performing?","Request count per instance","CPU Utilization","Network In","Disk I/O","Request count per instance is the most direct measure of how much work each instance is doing for web applications."
"What is the benefit of using Auto Scaling groups in conjunction with Spot Instances?","To reduce the cost of running EC2 instances","To increase the availability of EC2 instances","To simplify the management of EC2 instances","To improve the security of EC2 instances","Auto Scaling with Spot Instances can significantly reduce the cost of running EC2 instances by leveraging unused EC2 capacity."
"What happens to the EBS volumes attached to EC2 instances when those instances are terminated by Auto Scaling?","They are deleted if 'Delete on Termination' is enabled","They are automatically backed up","They are automatically detached and retained","They are encrypted","If 'Delete on Termination' is enabled on the EBS volume, the volume is deleted when the instance is terminated. If not, the volume is detached and retained."
"You want to temporarily pause scaling activities in your Auto Scaling group. How can you achieve this?","By using scaling processes suspend and resume","By deleting scaling policies","By detaching all instances from the Auto Scaling group","By deleting the Auto Scaling group","Suspending and resuming scaling processes allows you to temporarily pause scaling activities without deleting the Auto Scaling group or its configurations."
"Which Auto Scaling lifecycle hook action occurs BEFORE an instance is added to the Auto Scaling Group and starts receiving traffic?","launching","terminating","pending:wait","pending:proceed","The 'launching' action occurs as the instance is booting up before it is in service."
"What is the relationship between an Elastic Load Balancer (ELB) and an Auto Scaling Group?","The ELB distributes traffic to the instances managed by the Auto Scaling Group","The Auto Scaling Group distributes traffic to the instances managed by the ELB","The ELB manages the scaling policies of the Auto Scaling Group","The Auto Scaling Group manages the health checks of the ELB","The ELB is designed to send requests to a dynamic fleet of servers to which the Auto Scaling Group can add or remove servers based on load."
"What AWS service helps you implement infrastructure as code (IaC) for consistently provisioning resources like Auto Scaling Groups?","CloudFormation","CloudWatch","CloudTrail","CloudFront","CloudFormation lets you describe and provision infrastructure as code for repeatability."
"What is the AWS recommended practice for monitoring the health of your application and instances launched within an Auto Scaling Group?","Integrate health checks with a Load Balancer or use custom health checks","Manually check each instance every hour","Only rely on CloudWatch metrics","Disable health checks to prevent false alarms","Load Balancers and custom health checks are commonly used and are recommended by AWS."
"Which of the following CloudWatch metrics provides insights into the number of successful HTTP requests your application is serving within an Auto Scaling group?","HTTPCode_Target_2XX_Count","CPUUtilization","NetworkIn","DiskQueueDepth","2XX codes typically represent successful HTTP requests."
"If you're using AWS Auto Scaling to manage a fleet of EC2 instances for a database, which of the following is a critical consideration when designing your scaling policies?","Data consistency during scale-in and scale-out events","CPU Utilization of EC2 instances","Network bandwidth between instances","The cost of EBS volumes attached to instances","Data consistency is essential for databases to ensure that data is not lost or corrupted during scaling operations."
"Which feature should you use to send custom metrics (e.g., application response time) to CloudWatch, which you can then use to create scaling policies for your Auto Scaling Group?","CloudWatch agent","EC2 Instance Metadata","AWS Config","AWS CloudTrail","The CloudWatch agent allows you to collect custom metrics and send them to CloudWatch for monitoring and scaling purposes."
"Which of the following statements is true about Auto Scaling termination policies?","They determine which instances are terminated first during a scale-in event","They determine which instances are launched first during a scale-out event","They automatically provision new EC2 instances","They manage the security groups associated with EC2 instances","Termination policies control the order in which instances are terminated, helping to minimise disruptions."
"In AWS Auto Scaling, what is the purpose of a Launch Configuration or Launch Template?","Specifies the instance type and other settings for new instances","Defines the minimum number of instances in the Auto Scaling group","Sets the scaling policies for the Auto Scaling group","Configures the load balancer for the Auto Scaling group","A Launch Configuration or Launch Template specifies the instance type, AMI, security groups, and other settings used to launch new instances in the Auto Scaling group."
"What is the primary function of an Auto Scaling Group (ASG) in AWS?","To maintain a desired capacity of EC2 instances","To manage IAM roles and permissions","To monitor network traffic","To store application data","An Auto Scaling Group (ASG) ensures that you have the desired number of EC2 instances running to handle the load of your application. It automatically adjusts the number of instances based on demand."
"Which scaling policy adjusts the number of instances based on CloudWatch metrics?","Target Tracking Scaling","Simple Scaling","Scheduled Scaling","Manual Scaling","Target Tracking Scaling automatically adjusts the number of instances in your Auto Scaling group based on a target value for a specific CloudWatch metric."
"What is the use case for AWS Auto Scaling Scheduled Scaling?","To scale capacity based on predictable traffic patterns","To scale capacity in response to real-time demand fluctuations","To scale capacity manually based on your requirements","To scale capacity based on machine learning predictions","Scheduled Scaling allows you to scale your Auto Scaling group based on a pre-defined schedule, which is useful for predictable traffic patterns such as daily or weekly spikes."
"What does the 'desired capacity' in an AWS Auto Scaling group refer to?","The number of instances the ASG attempts to maintain","The maximum number of instances the ASG can launch","The minimum number of instances the ASG can launch","The average CPU utilisation across all instances","The desired capacity is the number of instances that the Auto Scaling group attempts to maintain. The ASG will launch or terminate instances to keep the group at this size."
"Which AWS service is most commonly integrated with Auto Scaling to distribute traffic across instances?","Elastic Load Balancing (ELB)","Amazon S3","Amazon RDS","Amazon CloudFront","Elastic Load Balancing (ELB) is commonly integrated with Auto Scaling to distribute incoming traffic across the instances in the Auto Scaling group, ensuring high availability and performance."
"What is the role of a lifecycle hook in AWS Auto Scaling?","To perform custom actions before or after an instance launches or terminates","To monitor the health of instances","To manage security groups","To automate AMI creation","Lifecycle hooks allow you to perform custom actions before or after an instance launches or terminates, such as installing software or transferring data."
"What happens when an instance fails a health check within an Auto Scaling group connected to an Elastic Load Balancer?","The instance is automatically terminated and replaced","The instance is placed in maintenance mode","The instance is automatically rebooted","The instance is isolated from the load balancer but remains running","When an instance fails a health check, the Auto Scaling group will automatically terminate it and launch a new instance to maintain the desired capacity."
"What is the benefit of using Launch Templates over Launch Configurations in AWS Auto Scaling?","Launch Templates support versioning and parameter overrides","Launch Templates are simpler to configure","Launch Templates are more cost-effective","Launch Templates automatically choose the best instance type","Launch Templates provide versioning capabilities and support parameter overrides, which allows you to easily manage and update your launch specifications."
"How can you ensure that instances in your Auto Scaling group are launched across multiple Availability Zones?","Configure the Auto Scaling group to use multiple subnets in different AZs","Use a single subnet that spans all Availability Zones","Manually launch instances in each Availability Zone","Configure the load balancer to distribute traffic across AZs","To ensure instances are launched across multiple Availability Zones, you need to configure the Auto Scaling group to use subnets in different Availability Zones. This provides high availability and fault tolerance."
"What is the purpose of the 'Cooldown Period' in AWS Auto Scaling's Simple Scaling policy?","To prevent the ASG from launching or terminating instances too quickly","To delay the health check process after an instance launches","To limit the amount of time an instance can run","To automatically reboot instances after a certain period","The Cooldown Period prevents the Auto Scaling group from launching or terminating instances too quickly after a scaling event, allowing the system to stabilise and avoid over-scaling or under-scaling."
"Which of these actions cannot be performed on a Launch Configuration once it has been created?","Modifying the instance type","Duplicating the Launch Configuration","Deleting the Launch Configuration","Viewing the Launch Configuration settings","Once a Launch Configuration has been created, you cannot modify it. You need to create a new Launch Configuration to apply changes."
"What is the purpose of the Auto Scaling lifecycle 'Terminating:Wait' hook?","To allow time for instance decommissioning tasks before termination","To delay instance launch until the application is ready","To immediately terminate the instance","To monitor instance health before termination","The 'Terminating:Wait' lifecycle hook allows you to perform tasks such as backing up data or deregistering the instance from a load balancer before the instance is terminated."
"Which feature allows you to dynamically adjust the capacity of your Auto Scaling group based on historical and predicted demand?","Predictive Scaling","Simple Scaling","Step Scaling","Manual Scaling","Predictive Scaling uses machine learning to analyse historical data and predict future demand, allowing you to proactively scale your Auto Scaling group."
"You want to use a metric other than CPU utilisation to scale your Auto Scaling group. Which AWS service can provide custom metrics for Auto Scaling?","Amazon CloudWatch","Amazon S3","Amazon RDS","Amazon VPC","Amazon CloudWatch can be used to create custom metrics that Auto Scaling can use to make scaling decisions. This allows you to scale based on application-specific metrics."
"What is the minimum number of instances required in an Auto Scaling group?","0","1","2","3","The minimum number of instances in an Auto Scaling group can be 0, allowing you to scale up from no instances when demand increases."
"What is the effect of setting 'HealthCheckType' to 'EC2' in an Auto Scaling group?","Auto Scaling relies on EC2 instance status checks to determine instance health","Auto Scaling relies on ELB health checks to determine instance health","Auto Scaling ignores health checks and assumes all instances are healthy","Auto Scaling performs custom health checks based on user-defined scripts","When 'HealthCheckType' is set to 'EC2', Auto Scaling uses the EC2 instance status checks to determine the health of instances in the group. If an instance fails these checks, it will be replaced."
"What does 'Step Scaling' policy do in AWS Auto Scaling?","Increases or decreases capacity by a fixed number of instances based on the alarm threshold","Increases or decreases capacity by a percentage of the current capacity","Adjusts capacity based on a pre-defined schedule","Maintains a target value for a specific metric","Step Scaling allows you to define different scaling adjustments based on the size of the alarm breach. For example, a small breach might trigger a small scaling adjustment, while a larger breach triggers a larger adjustment."
"How can you update the AMI used by an Auto Scaling group?","Create a new Launch Template or Launch Configuration with the updated AMI and update the ASG","Edit the existing Launch Configuration","Replace the AMI on the existing instances","Upload the new AMI directly to the Auto Scaling group","To update the AMI used by an Auto Scaling group, you need to create a new Launch Template or Launch Configuration with the updated AMI and update the Auto Scaling group to use the new template/configuration."
"Which scaling policy is best suited for maintaining a consistent average CPU utilisation across your Auto Scaling group?","Target Tracking Scaling","Simple Scaling","Scheduled Scaling","Step Scaling","Target Tracking Scaling is best suited for maintaining a consistent average CPU utilisation (or any other metric) across your Auto Scaling group. It automatically adjusts capacity to keep the metric close to the target value."
"What does the 'minimum size' parameter define in an Auto Scaling group?","The fewest number of instances that the ASG will maintain","The maximum number of instances that the ASG will maintain","The initial number of instances that the ASG will launch","The instance type with the lowest cost","The 'minimum size' parameter defines the fewest number of instances that the Auto Scaling group will maintain. It ensures that the group never scales down below this number."
"What is the purpose of Instance Protection in AWS Auto Scaling?","To prevent instances from being terminated during scale-in events","To encrypt data on the instances","To protect instances from security threats","To prevent instances from being rebooted","Instance Protection prevents instances from being terminated during scale-in events, even if they are marked as unhealthy or are part of an Auto Scaling group scaling down."
"What is the first step in creating an Auto Scaling group?","Creating a Launch Template or Launch Configuration","Creating a CloudWatch alarm","Creating a load balancer","Defining a scaling policy","The first step in creating an Auto Scaling group is to create a Launch Template or Launch Configuration, which specifies the instance type, AMI, and other settings for the instances in the group."
"Which of the following is NOT a supported scaling policy type in AWS Auto Scaling?","Reactive Scaling","Target Tracking Scaling","Scheduled Scaling","Simple Scaling","Reactive Scaling is not a supported scaling policy type in AWS Auto Scaling. The supported types are Target Tracking Scaling, Scheduled Scaling, Step Scaling and Simple Scaling."
"What is the purpose of a 'Placement Group' when used with Auto Scaling?","To launch instances in close proximity for low latency communication","To isolate instances from other AWS accounts","To automatically assign public IP addresses to instances","To launch instances in a specific Availability Zone","Placement Groups are used to launch instances in close proximity to each other, which is beneficial for applications that require low latency communication."
"Which AWS service can be used to monitor the health of instances within an Auto Scaling group and automatically replace unhealthy instances?","Elastic Load Balancing (ELB)","Amazon CloudWatch","Amazon S3","Amazon Route 53","Elastic Load Balancing (ELB) can be used to monitor the health of instances within an Auto Scaling group and automatically replace unhealthy instances, ensuring high availability and fault tolerance."
"You need to ensure that new instances launched by your Auto Scaling group have the latest version of your application code. Which approach is recommended?","Use a Launch Template with a custom AMI that includes the latest code","Manually update the code on each instance after it launches","Store the code in Amazon S3 and download it on each instance","Use a startup script to download and install the latest code from a repository","The recommended approach is to use a Launch Template with a custom AMI that includes the latest version of your application code. This ensures that all new instances are launched with the correct code from the start."
"What is the difference between 'Simple Scaling' and 'Step Scaling' policies in AWS Auto Scaling?","Simple Scaling uses a single scaling adjustment, while Step Scaling uses multiple adjustments based on the alarm threshold","Simple Scaling only supports scaling up, while Step Scaling supports both scaling up and down","Simple Scaling requires a load balancer, while Step Scaling does not","Simple Scaling is based on time, while Step Scaling is based on CloudWatch metrics","Simple Scaling uses a single scaling adjustment based on a single alarm threshold, while Step Scaling allows you to define multiple scaling adjustments based on different alarm thresholds. This provides more granular control over scaling actions."
"Which AWS service is commonly used to collect and track metrics for Auto Scaling groups?","Amazon CloudWatch","Amazon S3","Amazon RDS","Amazon VPC","Amazon CloudWatch is the primary service used to collect and track metrics for Auto Scaling groups, allowing you to monitor performance and trigger scaling actions based on these metrics."
"What is the purpose of the 'Availability Zones' setting in an Auto Scaling group?","To distribute instances across multiple physical locations for high availability","To define the security groups that instances will use","To specify the instance types that can be launched","To configure the load balancer settings","The 'Availability Zones' setting allows you to distribute instances across multiple physical locations (Availability Zones) for high availability and fault tolerance. If one AZ fails, the instances in the other AZs will continue to operate."
"When using Target Tracking Scaling, what happens if the metric being tracked consistently remains above the target value?","The Auto Scaling group will launch new instances to bring the metric down to the target value","The Auto Scaling group will terminate instances to bring the metric down to the target value","The Auto Scaling group will automatically adjust the target value","The Auto Scaling group will send an alert but take no action","If the metric being tracked consistently remains above the target value, the Auto Scaling group will launch new instances to bring the metric down to the target value."
"You want to perform a rolling update of your application code on instances in your Auto Scaling group. Which approach is recommended?","Use a Launch Template with a new AMI and update the ASG to use the new template","Manually update the code on each instance in the ASG","Terminate all instances and launch new ones with the updated code","Use AWS CodeDeploy to deploy the new code to the instances in the ASG","The recommended approach for performing a rolling update is to use a Launch Template with a new AMI that includes the updated code, and then update the Auto Scaling group to use the new template. This will gradually replace the old instances with new ones running the updated code."
"What happens when you delete an Auto Scaling group?","All instances in the ASG are terminated","The ASG configuration is deleted, but the instances continue to run","The load balancer associated with the ASG is also deleted","The EBS volumes attached to the instances are also deleted","When you delete an Auto Scaling group, all instances in the group are terminated. This ensures that you are not charged for resources that you are no longer using."
"Which of the following is a valid lifecycle hook state for an Auto Scaling instance termination process?","Terminating:Wait","Launching:Proceed","Running:Wait","Initialized:Proceed","'Terminating:Wait' is a valid lifecycle hook state during the instance termination process. It allows you to perform actions before the instance is fully terminated."
"What is the main difference between using a Launch Configuration and a Launch Template for Auto Scaling groups?","Launch Templates support versioning and parameter overrides, providing more flexibility.","Launch Configurations are newer and offer better integration with other AWS services.","Launch Configurations are easier to set up and manage for simple Auto Scaling scenarios.","Launch Templates are deprecated and should not be used for new Auto Scaling deployments.","Launch Templates offer versioning and parameter overrides, providing more flexibility than Launch Configurations. This allows you to easily manage and update your launch specifications."
"How does Auto Scaling contribute to high availability in AWS?","By automatically replacing unhealthy instances and distributing instances across multiple Availability Zones.","By providing automated backups and disaster recovery for EC2 instances.","By automatically patching and updating the operating systems on EC2 instances.","By encrypting data at rest and in transit for EC2 instances.","Auto Scaling contributes to high availability by automatically replacing unhealthy instances and distributing instances across multiple Availability Zones, ensuring that your application remains available even if one or more instances or Availability Zones fail."
"When configuring a Target Tracking scaling policy, what does the 'PredefinedMetricSpecification' option allow you to do?","Choose from a set of common CloudWatch metrics, such as CPU utilization or network traffic.","Define a custom CloudWatch metric to track for scaling purposes.","Specify the desired number of instances in the Auto Scaling group.","Set the minimum and maximum capacity for the Auto Scaling group.","The 'PredefinedMetricSpecification' option allows you to choose from a set of common CloudWatch metrics, such as CPU utilization or network traffic, making it easier to set up Target Tracking scaling without defining a custom metric."
"What is the purpose of the 'Warm-up' period in Predictive Scaling?","To allow new instances time to initialise before being used for scaling calculations.","To delay scaling actions during periods of low traffic.","To pre-warm the cache on new instances.","To allow time to gather enough data for accurate predictions.","The 'Warm-up' period in Predictive Scaling allows new instances time to initialise and become fully operational before they are included in the scaling calculations, ensuring that the predictions are based on accurate data."
"What is the recommended approach to ensure your application can handle sudden traffic spikes when using Auto Scaling?","Use a combination of Predictive Scaling and Target Tracking scaling","Use only Predictive Scaling","Use only Scheduled Scaling","Rely solely on manual scaling","A combination of Predictive Scaling and Target Tracking scaling is the recommended approach. Predictive Scaling proactively adjusts capacity based on predicted demand, while Target Tracking scaling reacts to real-time fluctuations and sudden spikes."
"What is the purpose of setting an 'instance weighting' in a mixed instances policy for Auto Scaling groups?","To control how Auto Scaling distributes instances of different types","To prioritize certain instance types over others during scaling","To define the maximum number of instances of each type in the group","To assign different costs to different instance types","Instance weighting allows you to control how Auto Scaling distributes instances of different types, taking into account the relative capacity and cost of each instance type."
"How can you ensure that your Auto Scaling group scales down gracefully, minimising disruption to users?","Use connection draining on your load balancer and lifecycle hooks to complete in-flight requests","Disable scale-in protection on all instances","Terminate the instances with the highest CPU utilisation","Immediately terminate instances without any delay","Using connection draining on your load balancer allows existing requests to complete before instances are terminated, and lifecycle hooks allow you to perform tasks such as backing up data or deregistering the instance before it is terminated, ensuring minimal disruption to users."
"Which setting in a Launch Template specifies the user data that is passed to the instance at launch?","UserData","InstanceType","ImageId","SecurityGroups","The UserData setting in a Launch Template specifies the user data that is passed to the instance at launch. This data can be used to configure the instance, install software, or perform other tasks."
"Which of the following is NOT a valid scaling metric that can be used with Target Tracking scaling policies?","CPU Utilization","Network In","Disk I/O","Request Count Per Target","Disk I/O is not a valid metric that can be directly used with Target Tracking scaling policies. Common metrics include CPU Utilization, Network In, and Request Count Per Target."
"You are using AWS Auto Scaling with EC2 instances. You want to apply tags to all new EC2 instances launched by the Auto Scaling group. Where should you configure these tags?","On the Auto Scaling group itself","On the IAM role used by the EC2 instances","On the Elastic Load Balancer (ELB)","In the EC2 user data script","Tags should be configured on the Auto Scaling group itself. This ensures that all new instances launched by the group automatically inherit these tags."
"What is the advantage of using Auto Scaling groups with mixed instance types and purchase options (e.g., On-Demand and Spot Instances)?","Cost optimisation and resilience","Simplified scaling policies","Improved security","Increased compute performance","Using mixed instance types and purchase options allows you to optimise costs by leveraging Spot Instances while ensuring resilience by using On-Demand Instances when Spot Instances are not available."
"Which AWS service works with AWS Auto Scaling to distribute traffic across multiple instances?","Elastic Load Balancing (ELB)","Amazon S3","Amazon CloudWatch","Amazon SNS","ELB distributes incoming application traffic across multiple targets, such as EC2 instances, in multiple Availability Zones. This allows Auto Scaling to provision new instances that can immediately begin serving traffic."
"What is a key benefit of using AWS Auto Scaling groups for your EC2 instances?","Maintaining a desired level of compute capacity automatically.","Automatically backing up your entire infrastructure.","Providing a global content delivery network.","Managing DNS records.","Auto Scaling groups ensure that you have the correct number of EC2 instances available to handle the load for your application. This is achieved by automatically adjusting the number of instances based on demand."
"What is the primary purpose of a Launch Template or Launch Configuration in AWS Auto Scaling?","To define the instance type and settings for new instances.","To store application code.","To manage network configurations.","To define the security group rules.","A Launch Template or Launch Configuration specifies the instance type, AMI, key pair, security groups, and other settings that Auto Scaling uses to launch new instances."
"Which scaling policy type in AWS Auto Scaling adjusts capacity based on a set of scaling metrics?","Target Tracking Scaling","Manual Scaling","Scheduled Scaling","Static Scaling","Target Tracking Scaling automatically adjusts the number of instances in your Auto Scaling group to maintain a specified target value for a chosen metric (e.g., CPU utilisation)."
"What type of scaling allows you to set the number of instances in your AWS Auto Scaling group at a specific time?","Scheduled Scaling","Dynamic Scaling","On-Demand Scaling","Predictive Scaling","Scheduled scaling actions allow you to change the number of instances in an Auto Scaling group at specific times or on a recurring schedule."
"An AWS Auto Scaling lifecycle hook allows you to perform custom actions when an instance is in which state?","Launching or terminating.","Running.","Stopped.","Idle.","Lifecycle hooks enable you to perform custom actions when an instance is launching or terminating, allowing you to, for example, install software or copy data before the instance goes into service or after it is taken out of service."
"What is the purpose of setting a 'Cooldown Period' in an AWS Auto Scaling group?","To prevent the Auto Scaling group from launching or terminating instances too frequently.","To allow instances to initialise properly before scaling occurs.","To manage the cost of your AWS infrastructure.","To ensure high availability of your application.","The cooldown period helps prevent the Auto Scaling group from launching or terminating additional instances before the previous scaling activity has taken effect."
"Which of the following is a scaling metric that can be used with AWS Auto Scaling Target Tracking Policies?","Average CPU utilisation of the Auto Scaling group.","Amount of free disk space on each instance.","Number of network interfaces attached to each instance.","Number of IAM roles assumed by each instance.","Target Tracking Scaling adjusts the capacity of your Auto Scaling group to maintain a specified target value for a chosen metric. Average CPU utilisation is a common metric."
"What is the maximum size of an AWS Auto Scaling group?","Determined by your account limits and available instance types.","Limited to 10 instances.","Limited to 100 instances.","Limited to 1000 instances.","The maximum size of an Auto Scaling group is determined by your AWS account limits and the availability of the instance types you are using."
"How does AWS Auto Scaling improve the fault tolerance of your application?","By automatically replacing unhealthy instances.","By automatically backing up your databases.","By automatically patching operating systems.","By automatically optimising network traffic.","Auto Scaling automatically replaces unhealthy instances, ensuring that your application remains available even if instances fail."
"What is the primary purpose of AWS Auto Scaling?","To automatically adjust the number of EC2 instances based on demand.","To automatically back up EC2 instances.","To automatically update EC2 instance operating systems.","To automatically optimize EC2 instance pricing.","Auto Scaling ensures you have the correct number of EC2 instances available to handle the load for your application, maintaining performance and availability."
"Which of the following components is required to create an AWS Auto Scaling group?","Launch Template or Launch Configuration","AWS Lambda Function","IAM User with Admin Access","AWS CloudTrail Configuration","A Launch Template or Launch Configuration defines the instance type, AMI, and other configurations for the instances launched by the Auto Scaling group."
"What is a scaling policy in AWS Auto Scaling used for?","To define the conditions under which an Auto Scaling group should scale out or scale in.","To specify the region in which the Auto Scaling group should operate.","To define the security group rules for the instances.","To configure the load balancer for the Auto Scaling group.","Scaling policies define the rules for automatically adjusting the number of instances in an Auto Scaling group based on metrics like CPU utilization or network traffic."
"What is the difference between 'scale-out' and 'scale-in' in AWS Auto Scaling?","Scale-out adds more instances to the Auto Scaling group, while scale-in removes instances.","Scale-out increases the instance size, while scale-in decreases it.","Scale-out migrates instances to a different Availability Zone, while scale-in moves them back.","Scale-out increases the EBS volume size, while scale-in reduces it.","Scale-out adds more instances to handle increased load, while scale-in reduces the number of instances when the load decreases, saving costs."
"What metric is commonly used to trigger scaling activities in AWS Auto Scaling?","CPU utilization","Disk space utilization","Number of active users","Network latency","CPU utilization is a common metric used to trigger scaling activities. When CPU usage exceeds a predefined threshold, the Auto Scaling group can add more instances."
"What is the purpose of a 'Cooldown Period' in AWS Auto Scaling?","To prevent the Auto Scaling group from launching or terminating instances too frequently.","To allow newly launched instances to fully initialise before serving traffic.","To temporarily suspend scaling activities during maintenance.","To delay scaling activities based on a schedule.","The cooldown period ensures that the Auto Scaling group doesn't react too quickly to fluctuations in metrics, preventing unnecessary scaling actions."
"Which of the following AWS services integrates directly with Auto Scaling to distribute traffic across instances?","Elastic Load Balancing (ELB)","Simple Queue Service (SQS)","CloudFront","Route 53","Elastic Load Balancing (ELB) distributes incoming application traffic across multiple EC2 instances in the Auto Scaling group, improving availability and fault tolerance."
"You want to ensure that your AWS Auto Scaling group maintains a specific number of instances at all times. Which scaling policy type should you use?","Target Tracking Scaling","Simple Scaling","Scheduled Scaling","Step Scaling","Target Tracking Scaling automatically adjusts the number of instances to maintain a predefined target value for a specified metric, ensuring the desired capacity is always maintained."
"What is a 'Launch Template' in AWS Auto Scaling and how does it differ from a 'Launch Configuration'?","A Launch Template is a newer and more flexible way to define the configuration for instances launched by Auto Scaling, offering versioning and support for the latest EC2 features. A Launch Configuration is an older, less flexible method.","A Launch Template is used for launching instances on-demand, while a Launch Configuration is used for spot instances.","A Launch Template is used for scaling out, while a Launch Configuration is used for scaling in.","A Launch Template is used for configuring the network settings, while a Launch Configuration is used for configuring the instance type.","Launch Templates offer versioning, which allows you to track changes to your instance configurations and easily roll back to previous versions if needed. They also support the latest EC2 features."
"How can you ensure that your AWS Auto Scaling group distributes instances across multiple Availability Zones?","By configuring multiple subnets in the Auto Scaling group settings.","By creating multiple Auto Scaling groups, one per Availability Zone.","By using a custom AMI with specific Availability Zone settings.","By manually launching instances in each Availability Zone.","Configuring multiple subnets in the Auto Scaling group settings allows the Auto Scaling group to launch instances across those Availability Zones, improving availability and resilience."
"What is the primary function of AWS Auto Scaling?","To automatically adjust the number of compute resources in response to changing demand","To manage and monitor network traffic","To provide secure storage for data backups","To analyse log data for security threats","Auto Scaling automatically increases or decreases the number of EC2 instances based on demand, ensuring application availability and performance."
"Which AWS service is commonly used as a data source for AWS Auto Scaling scaling policies?","CloudWatch","S3","IAM","VPC","CloudWatch provides metrics on resource utilisation, such as CPU utilisation and network traffic, which Auto Scaling uses to determine when to scale up or down."
"In AWS Auto Scaling, what is a 'launch configuration' or a 'launch template' primarily used for?","Specifying the configuration of new EC2 instances","Defining network security groups","Configuring load balancing settings","Setting up database replication","Launch configurations/templates define the AMI, instance type, key pair, security groups, and other parameters that are used to launch new instances."
"What does the 'Cooldown Period' setting in AWS Auto Scaling configure?","The minimum time before another scaling activity can occur after a successful scaling activity","The time it takes for an EC2 instance to boot up","The period for which scaling metrics are retained","The maximum time a scaling activity can take","The cooldown period helps prevent the Auto Scaling group from launching or terminating additional instances before the effects of previous scaling activities are visible."
"You want to scale your application based on the number of requests received per minute. What is the most appropriate CloudWatch metric to use with AWS Auto Scaling?","RequestCountPerTarget","CPUUtilization","MemoryUtilization","DiskReadBytes","RequestCountPerTarget is a metric provided by Application Load Balancers and is designed for scaling based on the number of requests being handled."
"Which statement is correct regarding AWS Auto Scaling lifecycle hooks?","They allow you to perform custom actions before or after an instance is launched or terminated","They automatically back up your data","They encrypt data at rest","They manage IAM roles and permissions","Lifecycle hooks provide a way to perform custom actions, such as installing software or copying data, before an instance joins or leaves the Auto Scaling group."
"You need to ensure your AWS Auto Scaling group only scales up when multiple metrics breach their thresholds simultaneously. How can you achieve this?","By using a composite alarm in CloudWatch","By creating multiple Auto Scaling groups","By using instance weighting","By enabling enhanced networking","A composite alarm allows you to combine multiple CloudWatch alarms into a single alarm, ensuring scaling only occurs when all conditions are met."
"What is the benefit of using instance weighting in AWS Auto Scaling with multiple instance types?","Allows you to control the distribution of instances across different instance types based on capacity units","Simplifies the process of creating AMI images","Provides cost savings by optimising storage utilisation","Enables faster scaling of instances","Instance weighting provides more flexibility and control over instance type selection based on their capacity units, allowing for optimal use of capacity reservations and pricing models."
"You want to ensure that your AWS Auto Scaling group is distributed across multiple Availability Zones. How do you configure this?","By enabling Availability Zone balancing in the Auto Scaling group settings","By manually launching instances in different Availability Zones","By using a Load Balancer with cross-zone load balancing enabled","Availability Zones are selected when configuring the VPC","Auto Scaling will automatically distribute instances across the Availability Zones defined within the subnet configuration to ensure high availability."