"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS License Manager, what is a License Configuration used for?","To define rules for license usage and tracking","To create backups of license files","To manage user access to AWS resources","To configure network settings for license servers","A License Configuration in AWS License Manager allows you to define rules regarding how licenses are used, helping you to track and manage software license usage."
"Which AWS service is primarily integrated with AWS License Manager to track license usage on EC2 instances?","AWS Systems Manager","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS Systems Manager is the primary integration point for tracking license usage on EC2 instances through its inventory and patch management features."
"What is the benefit of using AWS License Manager to manage software licenses in the cloud?","Centralised license management and tracking","Automated vulnerability scanning","Enhanced network security","Simplified cost optimisation for compute resources","AWS License Manager provides a centralised platform to manage software licenses, helping you track usage, enforce compliance, and reduce the risk of non-compliance."
"Which license type is supported in AWS License Manager?","Bring Your Own License (BYOL)","Open Source Licenses","Freeware Licenses","Trial Licenses","AWS License Manager is primarily used to manage Bring Your Own License (BYOL) scenarios, allowing you to leverage your existing software licenses in the AWS cloud."
"What is the purpose of defining a 'Rule' within an AWS License Configuration?","To specify the terms of the license agreement","To define the maximum number of vCPUs allowed per instance","To control access to the AWS Management Console","To manage IAM permissions","A 'Rule' within a License Configuration defines the terms of the license agreement and sets restrictions on the usage of the software, such as the number of vCPUs."
"What action does AWS License Manager take when an EC2 instance exceeds the defined license limit?","It terminates the EC2 instance","It stops the EC2 instance","It triggers an alarm in CloudWatch","It prevents the instance from launching or triggers an alert, depending on configuration","Depending on the configuration, License Manager can prevent the instance from launching or trigger an alert when the license limit is exceeded."
"Which AWS service can be used to create custom dashboards to visualise license usage data managed by AWS License Manager?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS IAM","Amazon CloudWatch can be used to create custom dashboards to visualise license usage data managed by AWS License Manager, providing insights into license consumption."
"How does AWS License Manager help with compliance related to software licensing?","By providing automated license usage tracking and reporting","By automatically generating license agreements","By managing user access control lists","By encrypting software license files","AWS License Manager helps with compliance by providing automated license usage tracking and reporting, helping organisations ensure they are adhering to the terms of their software licenses."
"What is the purpose of using delegated administration in AWS License Manager?","To grant specific permissions to users or groups for managing licenses","To automatically back up license files","To encrypt network traffic to license servers","To manage operating system patches on EC2 instances","Delegated administration in AWS License Manager allows you to grant specific permissions to users or groups for managing licenses, enabling a more granular approach to access control."
"Which AWS account is typically designated as the 'License Manager administrator' account in a multi-account environment?","The management account","The logging account","The security account","The audit account","In a multi-account environment, the management account is typically designated as the License Manager administrator account, which has centralised control over license configurations and usage."
"What is the purpose of integrating AWS License Manager with AWS Marketplace?","To track the usage of software purchased through AWS Marketplace","To automate the deployment of software from AWS Marketplace","To manage user access to AWS Marketplace","To encrypt data stored in AWS Marketplace","Integrating AWS License Manager with AWS Marketplace allows you to track the usage of software purchased through AWS Marketplace, ensuring compliance with license terms."
"What is the role of AWS Organizations in relation to AWS License Manager?","To enable centralised license management across multiple AWS accounts","To manage user access to AWS License Manager","To automate the creation of License Configurations","To encrypt data in AWS License Manager","AWS Organizations enables centralised license management across multiple AWS accounts, allowing you to manage software licenses across your entire AWS environment from a single point."
"Which of the following resources can be associated with a License Configuration in AWS License Manager?","EC2 instances","S3 buckets","IAM roles","CloudWatch alarms","EC2 instances are the primary resource type that can be associated with a License Configuration in AWS License Manager, enabling you to track license usage on those instances."
"What type of data is collected and tracked by AWS License Manager regarding software licenses?","Number of licenses used, product codes, and instance types","Usernames and passwords","Network traffic data","Email addresses","AWS License Manager collects and tracks data such as the number of licenses used, product codes, and instance types to provide a comprehensive view of license consumption."
"What is the purpose of the 'Managed Instance Activation' feature in AWS License Manager?","To associate EC2 instances with a specific License Configuration","To automatically patch EC2 instances","To encrypt EBS volumes","To manage user access to EC2 instances","The 'Managed Instance Activation' feature allows you to associate EC2 instances with a specific License Configuration, enabling license tracking and enforcement."
"What type of license compliance checks can be performed using AWS License Manager?","Checks for over-utilisation of licenses","Checks for software vulnerabilities","Checks for network security misconfigurations","Checks for IAM policy violations","AWS License Manager performs checks for over-utilisation of licenses, helping you identify instances where license limits are being exceeded."
"Which AWS service can be used to automate the remediation of license compliance violations detected by AWS License Manager?","AWS Systems Manager","AWS CloudTrail","AWS Config","Amazon CloudWatch","AWS Systems Manager can be used to automate the remediation of license compliance violations detected by AWS License Manager, such as stopping instances that are exceeding license limits."
"What is the purpose of the 'License Manager console' in the AWS Management Console?","To provide a central interface for managing software licenses","To manage user access to the AWS Management Console","To monitor network traffic","To configure DNS settings","The 'License Manager console' provides a central interface for managing software licenses, allowing you to create License Configurations, track license usage, and generate reports."
"How does AWS License Manager integrate with AWS CloudTrail?","To log all actions performed within AWS License Manager","To encrypt CloudTrail logs","To manage user access to CloudTrail","To automate the creation of CloudTrail trails","AWS License Manager integrates with AWS CloudTrail to log all actions performed within the service, providing an audit trail of license management activities."
"What is the purpose of setting a 'Hard Limit' in a License Configuration?","To prevent EC2 instances from launching if the license limit is exceeded","To automatically terminate EC2 instances if the license limit is exceeded","To send an email notification if the license limit is exceeded","To reduce the cost of EC2 instances","Setting a 'Hard Limit' in a License Configuration prevents EC2 instances from launching if the license limit is exceeded, ensuring strict license compliance."
"What is the benefit of using AWS License Manager with Microsoft Windows Server AMIs?","Automated tracking and reporting of Windows Server license usage","Automated patching of Windows Server instances","Simplified cost optimisation for Windows Server instances","Automated backup of Windows Server instances","Using AWS License Manager with Microsoft Windows Server AMIs allows for automated tracking and reporting of Windows Server license usage, simplifying license management for Windows workloads."
"How does AWS License Manager assist in migrating software licenses to the AWS cloud?","By providing a centralised platform for managing and tracking licenses","By automatically converting on-premises licenses to cloud licenses","By providing free software licenses","By migrating data to the cloud","AWS License Manager assists in migrating software licenses to the AWS cloud by providing a centralised platform for managing and tracking licenses, simplifying the transition process."
"Which of the following is a key component of the AWS License Manager architecture?","License Configurations","IAM roles","Security Groups","Route Tables","License Configurations are a key component of the AWS License Manager architecture, defining the rules and terms for license usage."
"What is the 'Usage Tracking' feature in AWS License Manager used for?","To monitor license usage patterns and identify potential issues","To track user activity within the AWS Management Console","To monitor network traffic","To manage user access to AWS resources","The 'Usage Tracking' feature in AWS License Manager is used to monitor license usage patterns and identify potential issues, helping you optimise license consumption."
"How can AWS License Manager help in optimising software licensing costs?","By identifying underutilised licenses and recommending license adjustments","By automatically purchasing new licenses","By reducing the cost of EC2 instances","By encrypting software license files","AWS License Manager can help optimise software licensing costs by identifying underutilised licenses and recommending license adjustments, helping you avoid unnecessary expenses."
"Which of the following is a potential benefit of integrating AWS License Manager with your existing software asset management (SAM) tools?","Improved visibility and control over license usage across hybrid environments","Automated patching of EC2 instances","Simplified cost optimisation for EC2 instances","Automated backup of EC2 instances","Integrating AWS License Manager with your existing SAM tools provides improved visibility and control over license usage across hybrid environments, enabling a more comprehensive approach to license management."
"What is the purpose of the 'License Consumption' metric in AWS License Manager?","To track the number of licenses being used by EC2 instances","To track the CPU utilisation of EC2 instances","To track the network traffic of EC2 instances","To track the memory usage of EC2 instances","The 'License Consumption' metric tracks the number of licenses being used by EC2 instances, providing insights into license utilisation."
"How does AWS License Manager help with meeting audit requirements for software licensing?","By providing detailed reports on license usage and compliance","By automatically generating license agreements","By managing user access control lists","By encrypting software license files","AWS License Manager helps with meeting audit requirements by providing detailed reports on license usage and compliance, simplifying the audit process."
"What is the relationship between AWS License Manager and AWS Marketplace entitlements?","AWS License Manager tracks usage of entitlements purchased through AWS Marketplace","AWS License Manager manages user access to AWS Marketplace","AWS License Manager automates the deployment of software from AWS Marketplace","AWS License Manager encrypts data stored in AWS Marketplace","AWS License Manager tracks usage of entitlements purchased through AWS Marketplace, ensuring compliance with the terms of the entitlements."
"How does AWS License Manager help prevent software license violations?","By enforcing license limits and preventing instances from exceeding those limits","By automatically patching software vulnerabilities","By encrypting software license files","By managing user access to EC2 instances","AWS License Manager helps prevent software license violations by enforcing license limits and preventing instances from exceeding those limits, reducing the risk of non-compliance."
"What is the recommended approach for managing licenses for containerised applications using AWS License Manager?","Associate the License Configuration with the underlying EC2 instances","Associate the License Configuration with the Docker images","Associate the License Configuration with the container registry","Associate the License Configuration with the ECS tasks","The recommended approach is to associate the License Configuration with the underlying EC2 instances that are running the containerised applications."
"How does AWS License Manager assist in managing licenses for applications deployed on Amazon EKS?","By associating the License Configuration with the underlying EC2 worker nodes","By associating the License Configuration with the Kubernetes pods","By associating the License Configuration with the Kubernetes namespaces","By associating the License Configuration with the EKS cluster","AWS License Manager assists in managing licenses for applications deployed on Amazon EKS by associating the License Configuration with the underlying EC2 worker nodes that support the EKS cluster."
"What is the purpose of using tags in conjunction with AWS License Manager?","To categorise and track license usage based on specific criteria","To manage user access to AWS resources","To automate the creation of License Configurations","To encrypt data stored in AWS License Manager","Using tags in conjunction with AWS License Manager allows you to categorise and track license usage based on specific criteria, enabling more granular reporting and analysis."
"Which AWS service can be integrated with AWS License Manager to receive alerts when license usage exceeds a defined threshold?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS IAM","Amazon CloudWatch can be integrated with AWS License Manager to receive alerts when license usage exceeds a defined threshold, enabling proactive monitoring and management of licenses."
"What is the difference between a 'Hard Limit' and a 'Soft Limit' in a License Configuration?","A 'Hard Limit' prevents instances from exceeding the limit, while a 'Soft Limit' allows instances to exceed the limit but triggers an alert","A 'Hard Limit' terminates instances if the limit is exceeded, while a 'Soft Limit' stops instances","A 'Hard Limit' encrypts license files, while a 'Soft Limit' manages user access","A 'Hard Limit' manages network traffic, while a 'Soft Limit' manages user access","A 'Hard Limit' prevents instances from exceeding the limit, while a 'Soft Limit' allows instances to exceed the limit but triggers an alert."
"How can you ensure that newly launched EC2 instances are automatically associated with the correct License Configuration?","By using AWS Systems Manager Automation to associate instances with License Configurations based on tags","By manually associating each instance with a License Configuration","By encrypting EC2 instances","By creating a CloudWatch event","Using AWS Systems Manager Automation to associate instances with License Configurations based on tags ensures that newly launched EC2 instances are automatically associated with the correct License Configuration."
"What is the purpose of the 'AWS License Manager dedicated host' feature?","To allocate dedicated physical hosts for running software with specific licensing requirements","To manage user access to AWS resources","To automate the creation of License Configurations","To encrypt data stored in AWS License Manager","The 'AWS License Manager dedicated host' feature allocates dedicated physical hosts for running software with specific licensing requirements, helping you comply with licensing terms that require dedicated hardware."
"In AWS License Manager, what does a 'License Conversion' refer to?","Changing a license type from BYOL to AWS provided","Converting a data format in S3","Converting the CPU type of EC2 Instance","Converting from IPv4 to IPv6","In AWS License Manager, 'License Conversion' refers to the ability to change a license type from Bring Your Own License (BYOL) to an AWS provided license, simplifying the process of switching to AWS managed licenses."
"Which of the following AWS services can AWS License Manager utilise to collect inventory data from instances?","AWS Systems Manager Inventory","Amazon Inspector","AWS Trusted Advisor","AWS Config","AWS License Manager utilises AWS Systems Manager Inventory to collect inventory data from instances, providing insights into the software installed on those instances."
"How does AWS License Manager assist in managing licenses for software that is installed on-premises?","It doesn't, AWS License Manager primarily manages licenses for AWS resources","By automatically backing up license files on-premises","By managing user access control lists on-premises","By encrypting software license files on-premises","AWS License Manager primarily manages licenses for AWS resources and does not directly manage licenses for software installed on-premises."
"What information is typically contained within a License Configuration in AWS License Manager?","License type, license count, and license rules","Usernames and passwords","Network traffic data","Email addresses","A License Configuration typically contains information such as the license type, license count, and license rules, defining the terms of the license agreement."
"What is the primary benefit of using AWS License Manager for managing licenses of database software?","Centralised tracking and control of database license usage","Automated patching of database instances","Simplified cost optimisation for database instances","Automated backup of database instances","The primary benefit of using AWS License Manager for managing licenses of database software is centralised tracking and control of database license usage, helping you ensure compliance and optimise costs."
"Which AWS service can be used to automate tasks related to license management using AWS License Manager?","AWS Step Functions","AWS CloudTrail","AWS Config","AWS IAM","AWS Step Functions can be used to automate tasks related to license management using AWS License Manager, enabling you to build workflows for license provisioning, tracking, and remediation."
"What is the role of product codes in AWS License Manager?","To identify the specific software product being licensed","To manage user access to AWS resources","To automate the creation of License Configurations","To encrypt data stored in AWS License Manager","Product codes in AWS License Manager are used to identify the specific software product being licensed, ensuring that licenses are correctly assigned and tracked."
"How can you use AWS License Manager to ensure that only authorised users can launch EC2 instances with specific software licenses?","By integrating AWS License Manager with AWS IAM and defining policies that restrict instance launch based on license availability","By manually approving each instance launch","By encrypting EC2 instances","By creating a CloudWatch event","You can use AWS License Manager to ensure that only authorised users can launch EC2 instances with specific software licenses by integrating it with AWS IAM and defining policies that restrict instance launch based on license availability."
"What is the purpose of associating an AMI (Amazon Machine Image) with a License Configuration in AWS License Manager?","To automatically apply the correct license to instances launched from that AMI","To encrypt the AMI","To manage user access to the AMI","To automate the creation of the AMI","Associating an AMI with a License Configuration ensures that instances launched from that AMI are automatically associated with the correct license, simplifying license management."
"What is the role of the AWS License Manager console in the AWS Management Console?","To provide a central interface for managing software licenses","To manage user access to the AWS Management Console","To monitor network traffic","To configure DNS settings","The AWS License Manager console in the AWS Management Console provides a central interface for managing software licenses, allowing you to create License Configurations, track license usage, and generate reports."
"What does AWS License Manager offer in terms of integrating with existing on-premises license servers?","Limited or no direct integration, focuses on AWS resources","Automatic migration of on-premises licenses to AWS","Free software licenses","Encryption of on-premises licenses","AWS License Manager offers limited or no direct integration with existing on-premises license servers, as it primarily focuses on managing licenses for AWS resources."
"Which AWS service does AWS License Manager integrate with to discover software usage?","AWS Systems Manager","AWS CloudTrail","AWS Config","Amazon Inspector","AWS License Manager integrates with AWS Systems Manager to discover software usage on EC2 instances and on-premises servers."
"What type of licensing rule can be configured in AWS License Manager to prevent instances from launching if licence capacity is exceeded?","Hard limit","Soft limit","Warning limit","Informational limit","A 'hard limit' licensing rule will prevent new instances from launching if the available licence capacity is exceeded."
"What is a License Configuration in AWS License Manager?","A set of rules that define the terms of a software licence.","A security group for EC2 instances.","A CloudWatch alarm configuration.","A cost allocation tag.","A License Configuration is a set of rules that define the terms of a software licence, including the licence type, count, and reporting behaviour."
"In AWS License Manager, what is the purpose of a managed licence?","To track usage of software licences provided by AWS Marketplace vendors.","To create custom AMI images.","To manage network ACLs.","To configure autoscaling groups.","A managed licence is used to track the usage of software licences provided by AWS Marketplace vendors, allowing you to monitor consumption and enforce compliance."
"What is the benefit of integrating AWS License Manager with AWS Organizations?","Centralised licence management across multiple AWS accounts.","Automated patching of EC2 instances.","Simplified network configuration across VPCs.","Enhanced security auditing capabilities.","Integrating with AWS Organizations enables centralised licence management across multiple AWS accounts, making it easier to track and control software usage."
"Which AWS service can be used in conjunction with AWS License Manager to automate the cleanup of unused licences?","AWS Lambda","AWS CloudFormation","Amazon SQS","AWS Step Functions","AWS Lambda can be used in conjunction with AWS License Manager to automate tasks such as cleaning up unused licences or notifying administrators of potential compliance issues."
"Which AWS License Manager feature enables you to track the usage of software licences in your on-premises environment?","AWS Systems Manager integration","AWS CloudTrail integration","AWS Config integration","Amazon Inspector integration","AWS Systems Manager integration allows AWS License Manager to track software licence usage in on-premises environments by discovering installed software on managed instances."
"What is the purpose of defining a borrow duration in an AWS License Manager configuration?","To specify how long a licence can be temporarily assigned to a user or application.","To set the frequency of licence compliance checks.","To define the period for which a licence is valid.","To determine the time it takes to provision a new licence.","A borrow duration specifies how long a licence can be temporarily assigned to a user or application before it is automatically returned to the pool."
"What is the main benefit of using AWS License Manager for bring-your-own-licence (BYOL) scenarios?","Improved tracking and compliance of your existing software licences in the cloud.","Automatic conversion of on-premises licences to cloud licences.","Free software licences for EC2 instances.","Unlimited access to AWS Marketplace software.","AWS License Manager provides improved tracking and compliance of your existing software licences when you bring them to the cloud, helping you avoid unexpected costs and compliance violations."
"Which AWS License Manager feature helps ensure that you do not exceed your allowed software licence usage?","Licence throttling","Automatic scaling","Resource optimisation","Security hardening","License throttling ensures that you do not exceed the specified license usage limits by preventing instances from launching when those limits are reached."
"What information can be tracked using AWS License Manager for software licences?","Licence usage, compliance status, and expiry dates.","CPU utilisation, network traffic, and disk I/O.","IAM role assignments, security group rules, and VPC settings.","CloudWatch metrics, CloudTrail logs, and Config rules.","AWS License Manager allows you to track license usage, compliance status, and expiry dates, providing a comprehensive view of your software license inventory and utilisation."
"Which AWS service can be used to audit license usage events tracked by AWS License Manager?","AWS CloudTrail","AWS Config","Amazon CloudWatch Logs","AWS X-Ray","AWS CloudTrail logs all API calls made to AWS License Manager, allowing you to audit license usage events and track changes to license configurations."
"What is the role of the AWS License Manager dashboard?","To provide a centralised view of licence usage, compliance status, and potential issues.","To manage IAM users and roles.","To configure network security groups.","To deploy EC2 instances.","The AWS License Manager dashboard provides a centralised view of licence usage, compliance status, and potential issues, allowing you to quickly identify and address any problems."
"How does AWS License Manager help with software licence cost optimisation?","By preventing over-licensing and identifying unused licences.","By automatically purchasing additional licences.","By negotiating discounts with software vendors.","By providing free software licences.","AWS License Manager helps optimise costs by preventing over-licensing (ensuring you don't pay for more licences than you need) and identifying unused licences that can be reclaimed."
"Which AWS service does AWS License Manager rely on for instance tagging to associate licences with specific resources?","AWS Resource Groups","AWS CloudFormation","AWS Config","AWS Systems Manager","AWS License Manager relies on AWS Resource Groups and tags for instance tagging to associate licenses with specific resources. This is used to properly track licensing and associate with instances."
"What is the significance of setting a licence type in AWS License Manager?","It specifies the licensing model (e.g., per core, per socket, per user).","It defines the security level of the licence.","It determines the geographical region where the licence is valid.","It controls the access permissions for the licence.","Setting the license type is crucial because it specifies the licensing model, such as per core, per socket, or per user, which determines how license usage is calculated and enforced."
"How can AWS License Manager assist in meeting regulatory compliance requirements related to software licensing?","By providing audit trails of licence usage and ensuring adherence to licence terms.","By automatically generating compliance reports for regulatory bodies.","By negotiating compliance requirements with software vendors.","By providing legal advice on software licensing.","AWS License Manager assists in meeting regulatory compliance requirements by providing audit trails of license usage and ensuring adherence to license terms, helping you demonstrate compliance to auditors."
"Which AWS License Manager feature allows you to control the number of concurrent users for a software application?","Concurrent licence management","User access control","Session management","Application load balancing","Concurrent license management in AWS License Manager allows you to control the number of concurrent users for a software application, preventing overuse and ensuring compliance with license agreements."
"What is the effect of enabling 'terminate instances on licence violation' in AWS License Manager?","EC2 instances will be automatically terminated if they violate licence terms.","EC2 instances will be automatically rebooted.","The AWS account will be suspended.","The software licence will be revoked.","Enabling 'terminate instances on license violation' will cause EC2 instances to be automatically terminated if they violate the defined license terms, helping prevent costly non-compliance."
"Which of the following is NOT a supported licence metric type in AWS License Manager?","Per vCPU","Per GB of RAM","Per core","Per user","AWS License Manager supports license metrics such as per vCPU, per core, and per user, but not per GB of RAM. It generally tracks metrics related to processors or users."
"How does AWS License Manager help with managing software licences during disaster recovery scenarios?","It allows you to quickly reallocate licences to your disaster recovery environment.","It automatically backs up your software licences.","It guarantees zero downtime during a disaster recovery event.","It provides free software licences for disaster recovery purposes.","AWS License Manager helps with managing software licenses during disaster recovery scenarios by allowing you to quickly reallocate licenses to your disaster recovery environment, ensuring continued compliance and functionality."
"Which AWS License Manager feature allows you to define the maximum number of instances that can use a particular licence?","Licence limit","Instance count","Resource allocation","Capacity reservation","The license limit feature allows you to define the maximum number of instances that can use a particular license, preventing overuse and ensuring compliance."
"How does AWS License Manager integrate with AWS Marketplace software subscriptions?","It allows you to track and manage your AWS Marketplace software licences.","It automatically renews your AWS Marketplace subscriptions.","It provides discounts on AWS Marketplace software.","It allows you to publish your own software on AWS Marketplace.","AWS License Manager integrates with AWS Marketplace software subscriptions, allowing you to track and manage your purchased licenses and ensure compliance with vendor agreements."
"What is the advantage of using AWS License Manager for managing Microsoft SQL Server licences on EC2 instances?","It simplifies the process of tracking and managing SQL Server licences, ensuring compliance.","It provides free Microsoft SQL Server licences.","It automatically updates SQL Server to the latest version.","It eliminates the need for SQL Server licences altogether.","AWS License Manager simplifies the process of tracking and managing Microsoft SQL Server licenses on EC2 instances, helping you maintain compliance with Microsoft's licensing terms."
"How can AWS License Manager help in a hybrid cloud environment (AWS and on-premises)?","By tracking and managing software licences across both AWS and on-premises resources.","By automatically migrating on-premises applications to AWS.","By providing free software licences for on-premises servers.","By eliminating the need for on-premises software licences.","AWS License Manager helps in a hybrid cloud environment by tracking and managing software licenses across both AWS and on-premises resources, providing a unified view of your software licensing landscape."
"What is the primary benefit of integrating AWS License Manager with AWS CloudWatch?","To monitor licence usage metrics and set up alarms for potential issues.","To encrypt licence data.","To manage IAM permissions.","To configure network security groups.","Integrating with AWS CloudWatch allows you to monitor license usage metrics and set up alarms for potential issues, enabling proactive management of your software licenses."
"Which AWS License Manager feature can prevent an EC2 instance from launching if it exceeds the available licence capacity?","Hard licence limit","Soft licence limit","Warning threshold","Informational message","A hard license limit in AWS License Manager will prevent an EC2 instance from launching if it would exceed the available license capacity, ensuring compliance and preventing over-licensing."
"What is the purpose of the 'Consumption Type' setting within an AWS License Manager licence configuration?","To specify how the licence is consumed (e.g., instance-based, user-based).","To define the type of software being licenced.","To determine the geographical region where the licence can be used.","To set the payment terms for the licence.","The 'Consumption Type' setting is to specify how the license is consumed, which is often either instance-based (per instance) or user-based (per user) to track correct use."
"Which AWS License Manager component defines the rules for how a specific software licence should be managed and enforced?","License configuration","Resource tag","Licence pool","Software inventory","A Licence Configuration defines the rules for how a specific software licence should be managed and enforced. It specifies the license type, count, and other settings."
"You are using AWS License Manager to manage your SQL Server licences. Which of the following metrics is most relevant for tracking licence usage?","Number of vCPUs","Network bandwidth","Disk I/O","Memory utilisation","The most relevant metric for SQL Server licensing (particularly on AWS) is the number of vCPUs. SQL Server licensing is often based on the number of vCPUs assigned to the instance."
"Which security principle is enhanced by using AWS License Manager to control software licences?","Least privilege","Defence in depth","Shared responsibility","Zero trust","The principle of least privilege is enhanced by using AWS License Manager to control software licenses, ensuring that only authorised resources can access and use specific software."
"How does AWS License Manager help to avoid unexpected software licensing costs?","By proactively alerting you when you are approaching or exceeding your licence limits.","By automatically negotiating better licensing terms with vendors.","By providing free software licences for development and testing.","By deferring billing until you are ready to use the software.","AWS License Manager helps avoid unexpected software licensing costs by proactively alerting you when you are approaching or exceeding your license limits, giving you time to take corrective action."
"Which AWS service can you use with AWS License Manager to ensure that only licensed software is installed on your EC2 instances?","AWS Systems Manager","AWS CloudTrail","AWS Config","Amazon Inspector","AWS Systems Manager can be used with AWS License Manager to ensure that only licensed software is installed on your EC2 instances by automating software inventory, patch management, and configuration management."
"What is the primary reason for integrating AWS License Manager with AWS Organizations?","To centralise licence management and visibility across multiple AWS accounts.","To automate the provisioning of EC2 instances.","To improve network performance between AWS accounts.","To enhance security auditing capabilities across accounts.","The primary reason for integrating AWS License Manager with AWS Organizations is to centralise license management and visibility across multiple AWS accounts, making it easier to track and control software usage organisation-wide."
"What is the purpose of creating a custom license configuration in AWS License Manager?","To define specific terms and conditions for using a particular software licence.","To automatically generate licence keys for software.","To bypass the need for software licences altogether.","To provide free software licences to AWS customers.","The purpose of creating a custom license configuration in AWS License Manager is to define the specific terms and conditions for using a particular software license, ensuring that it is used in accordance with the licensing agreement."
"When setting up a licence configuration in AWS License Manager, which option determines what happens when the number of instances exceeds the licence limit?","Enforcement type","Compliance status","Reporting frequency","Borrow duration","The Enforcement Type option determines what happens when the number of instances exceeds the license limit, allowing you to either prevent new instances from launching or receive a warning."
"Which AWS License Manager feature can be used to ensure that software licences are available during peak demand periods?","Licence reservation","Automatic scaling","Demand forecasting","Capacity planning","Licence reservation can be used to ensure that software licences are available during peak demand periods by reserving a specific number of licenses for critical applications or users."
"How does AWS License Manager provide visibility into software licence usage?","By providing a centralised dashboard that displays licence consumption, compliance status, and potential issues.","By automatically generating reports on software installation and usage.","By sending email notifications when software licences are being used.","By integrating with third-party software asset management tools.","AWS License Manager provides visibility into software license usage by providing a centralised dashboard that displays license consumption, compliance status, and potential issues, giving you a clear view of your licensing landscape."
"Which AWS service does AWS License Manager use to discover software inventory on EC2 instances?","AWS Systems Manager Inventory","AWS CloudTrail","AWS Config","Amazon Inspector","AWS License Manager integrates with AWS Systems Manager Inventory to discover software inventory on EC2 instances, allowing you to track installed software and ensure compliance with license agreements."
"What is the main advantage of using AWS License Manager's 'borrow licence' feature?","It allows users to temporarily use a licence offline or in disconnected environments.","It provides free software licences for temporary use.","It allows you to bypass licence restrictions for testing purposes.","It automatically renews software licences before they expire.","The 'borrow license' feature allows users to temporarily use a license offline or in disconnected environments, providing flexibility and ensuring that users can continue to work even without a network connection."
"Which of the following is a benefit of automating software licence management with AWS License Manager?","Reduced administrative overhead and improved compliance.","Eliminated software licensing costs.","Automated software development and deployment.","Enhanced network security.","Automating software license management with AWS License Manager provides reduced administrative overhead and improved compliance by automating tasks such as tracking license usage, enforcing license limits, and generating compliance reports."
"What is the purpose of setting a 'renewal date' for a licence configuration in AWS License Manager?","To track when the software licence needs to be renewed.","To automatically renew the software licence.","To trigger an alert when the software licence is about to expire.","To set the date for a software update.","Setting a 'renewal date' for a license configuration in AWS License Manager is to track when the software license needs to be renewed, helping you avoid disruptions in service and ensure continued compliance."
"Which AWS License Manager feature can help you optimise your software licence spending by identifying unused licences?","Licence utilisation reporting","Automated licence purchasing","Cost allocation tagging","Resource optimisation","Licence utilisation reporting can help you optimise your software licence spending by identifying unused licenses, allowing you to reclaim them and reduce your overall licensing costs."
"What is the primary function of AWS License Manager?","Simplifying software license management and tracking","Managing AWS IAM roles and permissions","Monitoring network traffic","Managing AWS billing and cost allocation","AWS License Manager is primarily designed to simplify the management and tracking of software licenses, ensuring compliance and optimising usage."
"Which AWS service does AWS License Manager integrate with to discover software usage?","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS Trusted Advisor","AWS License Manager integrates with AWS Config to discover software usage across your AWS environment."
"What is a 'License Configuration' in AWS License Manager?","A set of rules that define the usage of a software license","A type of EC2 instance","A method for encrypting data at rest","A tool for monitoring CPU utilisation","A License Configuration in AWS License Manager defines the rules for using a particular software license, including the number of instances that can use it, the type of license, and other constraints."
"What is the benefit of using AWS License Manager to track Bring Your Own License (BYOL) software?","It helps ensure compliance with licensing terms and prevents overuse","It automatically converts BYOL licenses to AWS Marketplace licenses","It provides free software updates","It migrates on-premises licenses to the cloud","Using AWS License Manager for BYOL software helps ensure that you remain compliant with the licensing terms of your software and prevents accidental overuse, which can lead to penalties."
"Which of the following is a feature of AWS License Manager?","Automated license inventory management","Automated patching of operating systems","Automatic scaling of EC2 instances","Automated code deployment","AWS License Manager provides automated license inventory management, allowing you to track and manage your software licenses more effectively."
"What type of licenses can be managed using AWS License Manager?","Both AWS Marketplace and Bring Your Own Licenses (BYOL)","Only AWS Marketplace licenses","Only Bring Your Own Licenses (BYOL)","Only licenses purchased directly from software vendors","AWS License Manager supports the management of both AWS Marketplace licenses and Bring Your Own Licenses (BYOL), providing a centralised platform for all your software license needs."
"How does AWS License Manager help prevent over-deployment of software?","By enforcing license limits defined in license configurations","By automatically terminating EC2 instances","By sending email alerts to administrators","By restricting access to the AWS Management Console","AWS License Manager enforces the license limits defined in license configurations, preventing the deployment of software beyond the allowed number of licenses."
"Which AWS service can be used in conjunction with AWS License Manager to automate the discovery of software inventory across your AWS environment?","AWS Systems Manager Inventory","AWS CloudWatch Logs","AWS Identity and Access Management (IAM)","AWS Lambda","AWS Systems Manager Inventory can be used with AWS License Manager to automatically discover software inventory, providing a comprehensive view of the software deployed across your AWS environment. "
"How does AWS License Manager contribute to cost optimisation?","By optimising software license usage and preventing unnecessary spending","By automatically resizing EC2 instances","By providing discounts on AWS services","By optimising database queries","AWS License Manager contributes to cost optimisation by ensuring that software licenses are used efficiently and preventing unnecessary spending on underutilised or unused licenses."
"What is the main purpose of setting 'license rules' in AWS License Manager?","To enforce compliance with license terms and conditions","To automatically update software versions","To automatically backup software configurations","To provide access control to software","License rules in AWS License Manager are primarily used to enforce compliance with the terms and conditions of software licenses, ensuring that software is used in accordance with the license agreement."
"What is the primary function of AWS License Manager?","To manage and track software licenses for both AWS and on-premises environments","To automatically scale EC2 instances based on CPU utilisation","To provide a centralised logging service for all AWS resources","To manage IAM roles and permissions across multiple AWS accounts","AWS License Manager simplifies the process of managing and tracking software licenses, helping organisations maintain compliance and control costs."
"Which of the following AWS services integrates with AWS License Manager to track license usage?","AWS CloudTrail","AWS CloudWatch","AWS Systems Manager","Amazon S3","AWS Systems Manager integrates with License Manager to allow you to track software usage against the licenses you have defined."
"In AWS License Manager, what is a 'License Configuration'?","A set of rules that defines how licenses are consumed and enforced","A pre-configured EC2 instance type with specific software installed","A monthly billing report showing license usage costs","A security policy that restricts access to licensed software","A License Configuration defines the rules around how licenses are consumed and enforced, including settings for license type, tenancy, and enforcement limits."
"What benefit does AWS License Manager provide for managing on-premises software licenses?","It extends license tracking and management to on-premises environments, providing a centralised view.","It automatically converts on-premises licenses to cloud-based licenses.","It provides free software licenses for on-premises use.","It migrates all on-premises workloads to AWS.","AWS License Manager extends its capabilities to on-premises environments through integrations with AWS Systems Manager, allowing you to manage licenses across hybrid environments."
"Which license rule can you configure within AWS License Manager to prevent over-licensing?","Hard limit","Soft limit","Usage limit","Notification limit","A 'Hard Limit' in License Manager prevents new instances from launching if they would exceed the license limit. It strictly enforces the license count."
"What is the purpose of the AWS License Manager Managed Entitlements feature?","To grant and manage access to software licenses across different AWS accounts or organisations","To automatically generate license keys for new software installations","To provide a marketplace for buying and selling software licenses","To encrypt software binaries to prevent unauthorised use","Managed Entitlements in AWS License Manager allows you to share and manage software licenses (entitlements) across different AWS accounts or organisations, facilitating better license utilisation and control."
"Which AWS License Manager feature helps in discovering and tracking software usage across an organisation's AWS environment?","AWS Systems Manager Discovery","License usage dashboard","License configuration templates","Automated patching","The License Usage Dashboard in AWS License Manager provides a central location to view software usage across your AWS environment, helping you track compliance and optimise license spending."
"You want to ensure that instances launched without proper licensing are automatically terminated. How can you achieve this using AWS License Manager?","Configure a hard limit with instance termination action.","Enable enhanced networking on all instances.","Create a new IAM role with restricted permissions.","Implement a network ACL to block unlicensed instances.","By setting a hard limit and configuring the License Configuration to terminate instances when the limit is reached, License Manager will automatically terminate instances that violate the licensing rules."
"Which of the following is a valid licensing rule type supported by AWS License Manager?","Sockets","vCPUs","Memory","Storage","License Manager supports tracking based on vCPUs, Cores, Sockets, Instances or user limits. Sockets is therefore a valid licensing rule type."
"You need to report on the historical usage of a specific software license within your AWS environment. Which AWS service, when integrated with AWS License Manager, can provide this information?","AWS CloudTrail","Amazon QuickSight","AWS Config","Amazon Inspector","AWS CloudTrail captures API calls made to License Manager, enabling you to track license usage changes and generate historical reports."