"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Managed Grafana, what is the primary function of a data source?","To connect Grafana to your monitoring and observability data.","To define user roles and permissions.","To customize the Grafana user interface.","To create alerts based on metric thresholds.","Data sources are the bridge between Grafana and the services that store your metrics, logs, and traces, enabling you to visualise that data within Grafana dashboards."
"Which AWS service is commonly used as a data source for Amazon Managed Grafana?","Amazon CloudWatch","Amazon S3","Amazon EC2","Amazon Lambda","Amazon CloudWatch is a popular AWS service for monitoring metrics and logs, making it a natural fit as a data source for Grafana to visualise that data."
"What is the purpose of workspaces in Amazon Managed Grafana?","To provide isolated Grafana environments with their own configurations and data sources.","To manage user authentication across multiple Grafana instances.","To centrally store Grafana dashboard definitions.","To automate the deployment of Grafana dashboards.","Workspaces in Amazon Managed Grafana act as isolated environments, allowing you to create separate Grafana instances for different teams, projects, or environments with dedicated configurations and data sources."
"What type of authentication is supported by Amazon Managed Grafana out of the box?","AWS IAM Identity Center (Successor to AWS SSO)","LDAP","Kerberos","Custom SAML","Amazon Managed Grafana integrates natively with AWS IAM Identity Center (Successor to AWS SSO) simplifying user access management using your existing AWS identities."
"Which of the following is NOT a key benefit of using Amazon Managed Grafana?","Fully managed service reduces operational overhead.","Automatic scaling based on usage.","Unlimited data ingestion.","Native integration with AWS data sources.","While Amazon Managed Grafana simplifies operations and integrates well with AWS, it doesn't offer unlimited data ingestion as resources are governed by your AWS account limits."
"How can you control access to dashboards within Amazon Managed Grafana?","Through Grafana's built-in roles and permissions.","Using AWS Identity and Access Management (IAM) policies.","By configuring network ACLs.","By encrypting dashboard data.","Grafana's built-in roles (Admin, Editor, Viewer) can be used to control who can create, edit, and view dashboards within a workspace."
"Which Amazon Managed Grafana feature allows you to be notified when metrics cross a defined threshold?","Alerting","Annotations","Transformations","Templating","Alerting is the built-in feature in Grafana for setting up rules that trigger notifications when metric values exceed or fall below specified thresholds."
"What does the term 'provisioning' refer to in the context of Amazon Managed Grafana?","Configuring Grafana settings and resources using code or configuration files.","Monitoring the health and performance of the Grafana instance.","Scaling the Grafana instance based on demand.","Creating backups of Grafana dashboards and configurations.","Provisioning in Grafana involves automating the configuration of dashboards, data sources, and other resources using code or configuration files, enabling infrastructure-as-code principles."
"Which of these Grafana features allows you to dynamically change the data displayed in a dashboard based on user input?","Templating","Annotations","Transformations","Alerting","Templating lets you create dashboards with dynamic variables that can be changed by users, allowing them to filter or select specific data for visualisation."
"What is the purpose of the 'Explore' feature in Amazon Managed Grafana?","To interactively query and explore data from your data sources.","To manage user permissions and roles.","To create and manage alerts.","To import and export Grafana dashboards.","The 'Explore' feature provides a dedicated interface for querying data, troubleshooting issues, and understanding the data returned by your data sources."
"When using Amazon Managed Grafana, what is the recommended way to manage and version control your dashboard configurations?","Using Grafana's API and a version control system like Git.","Using the Grafana UI to manually create and update dashboards.","Exporting dashboards as JSON files and storing them locally.","Relying on Grafana's built-in backup and restore functionality.","Managing dashboards as code using the Grafana API and a version control system like Git provides versioning, collaboration, and automated deployment capabilities."
"What kind of data can be visualised in an Amazon Managed Grafana dashboard?","Metrics, logs, and traces.","Images and videos.","Documents and spreadsheets.","Source code and configuration files.","Grafana is designed to visualise time-series data like metrics, logs, and traces, which are commonly used in monitoring and observability scenarios."
"How does Amazon Managed Grafana handle data encryption?","Data is encrypted at rest and in transit using AWS KMS.","Data is encrypted only at rest.","Data is encrypted only in transit.","Data encryption is not supported.","Amazon Managed Grafana leverages AWS KMS to encrypt data both at rest (stored data) and in transit (data being transmitted) for enhanced security."
"What is the primary use case for using annotations in Amazon Managed Grafana dashboards?","To mark specific events or occurrences on a graph, providing context.","To add interactive elements to the dashboard.","To filter data based on specific criteria.","To trigger alerts when certain conditions are met.","Annotations allow you to overlay events or contextual information on top of your graphs, helping you correlate changes in metrics with specific events or deployments."
"Which of the following describes a 'panel' in the context of Amazon Managed Grafana dashboards?","A visual representation of data, such as a graph or table.","A collection of related dashboards.","A data source connection.","A user role with specific permissions.","A panel is a fundamental building block of a Grafana dashboard, representing a single visualisation of data, such as a graph, table, or gauge."
"What is the purpose of the 'transformations' feature in Amazon Managed Grafana?","To modify or manipulate data before it is visualised.","To change the appearance of dashboard panels.","To filter data based on time ranges.","To create alerts based on metric values.","Transformations allow you to perform calculations, aggregations, and other manipulations on your data before it's displayed, enabling you to create more meaningful visualisations."
"Which IAM permission is required to create a new Amazon Managed Grafana workspace?","grafana:CreateWorkspace","grafana:CreateDashboard","grafana:ConnectDataSource","grafana:GetWorkspace","The `grafana:CreateWorkspace` permission is required to provision a new Grafana workspace."
"Which feature of Amazon Managed Grafana would you use to create a dashboard that displays data from multiple data sources in a single panel?","Data source blending/querying","Templating variables","Annotation linking","Panel plugins","Grafana has the ability to query multiple data sources in a single panel allowing you to visualise metrics from different sources on one graph."
"What is the best way to share an Amazon Managed Grafana dashboard with a user who doesn't have access to the AWS account?","Create a shareable snapshot of the dashboard.","Add the user to the AWS account with read-only permissions.","Export the dashboard as a PDF and email it to the user.","Print the dashboard and give it to the user.","Shareable snapshots allow you to create a static, interactive version of a dashboard that can be shared publicly without requiring access to the underlying AWS account."
"What is the role of the 'plugins' in Amazon Managed Grafana?","To extend Grafana's functionality with new data sources, panels, and features.","To manage user authentication and authorization.","To define the layout and structure of dashboards.","To automate the deployment of Grafana instances.","Plugins provide a way to extend Grafana's capabilities by adding support for new data sources, visualisation types, and other features."
"Which of the following actions does NOT require elevated privileges (e.g., Admin role) in Amazon Managed Grafana?","Viewing dashboards.","Creating users.","Installing plugins.","Configuring data sources.","Viewing dashboards typically requires the Viewer role, which is a low-privilege role."
"What is the purpose of folders in Amazon Managed Grafana?","To organise dashboards into logical groupings.","To store data source configurations.","To manage user permissions.","To define alert rules.","Folders provide a way to organise and manage a large number of dashboards, making it easier to find and navigate related dashboards."
"How can you monitor the health and performance of your Amazon Managed Grafana workspace?","Using Amazon CloudWatch metrics for the Grafana workspace.","Using Grafana's built-in monitoring tools.","By manually checking the Grafana logs.","By contacting AWS Support.","Amazon CloudWatch metrics provide insights into the performance and resource utilisation of your Grafana workspace, helping you identify and troubleshoot issues."
"Which of the following is NOT a supported method for accessing Amazon Managed Grafana?","Through the AWS Management Console.","Using the Grafana API.","Using the Grafana CLI.","Directly connecting to the underlying EC2 instance.","Amazon Managed Grafana is a managed service, and you don't have direct access to the underlying EC2 instance."
"What is the benefit of using AWS CloudFormation to deploy Amazon Managed Grafana?","It allows you to automate the creation and configuration of Grafana workspaces.","It provides a graphical interface for managing Grafana dashboards.","It automatically scales the Grafana instance based on demand.","It encrypts data stored in Grafana.","CloudFormation enables you to define your Grafana infrastructure as code, allowing you to automate the creation, configuration, and management of your workspaces."
"In Amazon Managed Grafana, what is the role of the 'query editor'?","To construct queries to retrieve data from data sources.","To define the layout and appearance of dashboard panels.","To manage user permissions and roles.","To create and manage alerts.","The query editor is used to construct queries that retrieve data from your configured data sources. The syntax and options available will depend on the specific data source being used."
"What is the purpose of the Grafana 'dashboard list' panel type?","To display a dynamic list of dashboards.","To display a static list of dashboards.","To create new dashboards.","To manage dashboard permissions.","The dashboard list panel allows you to create a panel that displays a list of available dashboards, making it easier for users to navigate to different dashboards."
"Which of the following is NOT a valid use case for Amazon Managed Grafana?","Visualising metrics from Amazon CloudWatch.","Analysing logs from Amazon CloudWatch Logs.","Monitoring the performance of Amazon EC2 instances.","Hosting static websites.","Grafana is designed for visualising time-series data like metrics, logs, and traces, not for hosting static websites."
"How can you integrate Amazon Managed Grafana with your existing CI/CD pipeline?","By using Grafana's API to automate the provisioning and deployment of dashboards.","By manually creating and updating dashboards in the Grafana UI.","By exporting dashboards as JSON files and storing them locally.","By using Grafana's built-in backup and restore functionality.","Grafana's API allows you to automate the deployment and management of dashboards within your CI/CD pipeline, enabling infrastructure-as-code practices."
"What is the default data retention policy for metrics stored in Amazon Managed Grafana?","Amazon Managed Grafana does not store any data. It visualizes data from other data sources.","30 days","90 days","1 year","Amazon Managed Grafana itself does not store any data. It connects to existing data sources where your metrics, logs, and traces are stored. Therefore, data retention is governed by the data source's retention policy, not Grafana's."
"Which of the following is a recommended security practice for Amazon Managed Grafana?","Enabling encryption at rest and in transit.","Using a weak password for the Grafana admin user.","Exposing the Grafana instance to the public internet without any security measures.","Disabling authentication for Grafana.","Enabling encryption at rest and in transit is a crucial security practice to protect sensitive data stored and transmitted by Grafana."
"What is the purpose of the 'data links' feature in Amazon Managed Grafana?","To create links from dashboard panels to external resources or other dashboards.","To link data sources together.","To link users to specific dashboards.","To link annotations to specific data points.","Data links allow you to create links from dashboard panels to external resources, such as documentation, runbooks, or other dashboards, providing a more seamless workflow."
"Which of the following is a valid data source plugin for Amazon Managed Grafana?","InfluxDB","Microsoft Excel","Adobe Photoshop","Zoom","InfluxDB is a popular time-series database that is commonly used as a data source for Grafana."
"What is the purpose of the Grafana CLI?","To manage Grafana instances from the command line.","To create and edit Grafana dashboards.","To manage user permissions.","To configure data sources.","The Grafana CLI provides a command-line interface for managing Grafana instances, allowing you to automate tasks such as installing plugins, creating users, and configuring data sources."
"How can you create a read-only view of an Amazon Managed Grafana dashboard for external stakeholders?","By creating a shareable snapshot of the dashboard.","By giving them access to the Grafana instance with Viewer permissions.","By exporting the dashboard as a PDF and emailing it to them.","By printing the dashboard and giving it to them.","Shareable snapshots create a static, interactive version of a dashboard that can be shared publicly without requiring access to the underlying Grafana instance."
"You want to give a contractor temporary access to Amazon Managed Grafana. What is the MOST secure way to achieve this?","Create a temporary IAM user with limited permissions.","Share your personal AWS credentials with the contractor.","Disable authentication for the duration of the contract.","Create a new AWS account for the contractor.","Creating a temporary IAM user with limited permissions aligns with the principle of least privilege and ensures the contractor only has access to the resources they need for a limited time."
"What is the purpose of 'organisations' in Grafana?","To logically divide the users and assets like dashboards.","To isolate data sources and user access.","To manage authentication and authorisation for Grafana.","To monitor the health and performance of Grafana.","Organisations are like isolated 'tenants' in Grafana that allow you to split users and assets, like dashboards."
"Your Amazon Managed Grafana dashboard is showing slow load times. What is the first thing you should investigate?","The query performance and efficiency of the data sources.","The network connectivity to the Grafana instance.","The amount of free memory on the Grafana server.","The number of users accessing the dashboard concurrently.","Slow dashboard load times are often caused by inefficient queries or slow data sources. Optimising the queries and ensuring the data sources are performing well is the first step in troubleshooting."
"What is the relationship between Amazon Managed Grafana and Prometheus?","Prometheus can be used as a data source for Amazon Managed Grafana.","Amazon Managed Grafana replaces Prometheus.","Amazon Managed Grafana is a prerequisite for running Prometheus.","They are completely unrelated services.","Amazon Managed Grafana can connect to Prometheus as a data source, allowing you to visualise Prometheus metrics in Grafana dashboards."
"You need to create a dashboard that automatically updates every 5 seconds. How do you achieve this in Amazon Managed Grafana?","Set the 'refresh interval' in the dashboard settings to 5s.","Use a JavaScript script to reload the dashboard every 5 seconds.","Manually refresh the dashboard every 5 seconds.","Set the 'data source query interval' to 5s.","The 'refresh interval' setting in the dashboard allows you to configure how often the dashboard automatically updates with new data."
"What is the purpose of the 'annotations & overlays' feature in Amazon Managed Grafana?","To add contextual information to graphs and dashboards.","To change the appearance of dashboard panels.","To filter data based on time ranges.","To create alerts based on metric values.","Annotations and overlays allow you to add contextual information to graphs and dashboards, such as deployment events, incidents, or other relevant events."
"Which of the following is a key benefit of using Amazon Managed Grafana over self-managing your own Grafana instance?","Reduced operational overhead and simplified management.","Greater control over the underlying infrastructure.","Lower cost.","Greater flexibility in terms of customisation and configuration.","Amazon Managed Grafana reduces operational overhead by providing a fully managed service, freeing you from tasks such as patching, scaling, and infrastructure management."
"Which AWS region(s) is Amazon Managed Grafana currently available in?","Availability varies by region, check the AWS documentation for the most up-to-date information.","Only US East (N. Virginia).","Only Europe (Ireland).","Globally available in all AWS regions.","Amazon Managed Grafana's availability varies by region and is continuously expanding. Refer to the official AWS documentation for a current list of supported regions."
"You are using Amazon Managed Grafana to monitor the performance of your microservices. Which type of data is MOST relevant to visualise?","Metrics, logs, and traces.","Images and videos.","Documents and spreadsheets.","Source code and configuration files.","Metrics, logs, and traces provide valuable insights into the performance, errors, and behaviour of microservices, making them the most relevant data to visualise in Grafana."
"What is the primary purpose of Amazon Managed Grafana?","To visualise and analyse metrics, logs, and traces.","To manage and deploy containerised applications.","To store and process large datasets.","To provide a serverless compute environment.","Amazon Managed Grafana is a fully managed data visualisation service that allows you to query, visualise, alert on, and understand your metrics no matter where they are stored."
"Which AWS service can be directly integrated as a data source with Amazon Managed Grafana without additional configuration?","Amazon CloudWatch","Amazon S3","Amazon EC2","AWS Lambda","Amazon CloudWatch is directly integrated with Amazon Managed Grafana, allowing you to visualise CloudWatch metrics without additional setup."
"In Amazon Managed Grafana, what is the purpose of the 'Workspace' concept?","A workspace isolates your Grafana instance and resources.","A workspace provides a serverless compute environment.","A workspace manages user access control policies.","A workspace provides centralised billing for all AWS services.","A workspace in Amazon Managed Grafana is an isolated Grafana instance, providing a dedicated environment for your dashboards, data sources, and users."
"What is the default authentication method for users accessing an Amazon Managed Grafana workspace?","AWS IAM Identity Center (Successor to AWS SSO)","LDAP","SAML","Username and password","By default, users authenticate using AWS IAM Identity Center (Successor to AWS SSO), which simplifies user management and provides a centralised authentication solution."
"Which of the following is NOT a supported data source in Amazon Managed Grafana?","Microsoft SQL Server","Amazon Elasticsearch Service","Prometheus","Amazon Timestream","Microsoft SQL Server is not a natively supported data source in Amazon Managed Grafana. You can use other methods to visualise data from SQL Server but is not a native data source."
"What is the advantage of using Amazon Managed Grafana over self-managing a Grafana instance on EC2?","Reduced operational overhead and simplified management","Increased customisation options for the underlying operating system","Lower overall cost for small-scale deployments","Direct access to the Grafana configuration files","Amazon Managed Grafana handles infrastructure provisioning, scaling, patching, and backups, reducing the operational burden compared to self-managing Grafana on EC2."
"In Amazon Managed Grafana, how are permissions managed for users to access dashboards and data sources?","Through AWS IAM Identity Center (Successor to AWS SSO) and Grafana roles.","Through direct assignment of IAM roles to users.","Through network ACLs configured on the VPC.","Through resource-based policies attached to data sources.","Permissions are managed through AWS IAM Identity Center (Successor to AWS SSO) for user authentication and then through Grafana roles within the workspace for access control to dashboards and data sources."
"What type of alert is NOT supported directly in Amazon Managed Grafana without additional plugins or integrations?","Multi-dimensional alerts based on complex queries","Threshold-based alerts","Anomaly detection alerts","Rate-based alerts","Amazon Managed Grafana requires custom configurations/plugins to perform anomaly detection alerting, as standard alert features are only threshold based or rate based."
"Which feature of Amazon Managed Grafana enables users to share dashboards and visualisations with external stakeholders who may not have Grafana accounts?","Public Dashboards via snapshots","Single sign-on (SSO)","Multi-factor authentication (MFA)","API Key Authentication","Amazon Managed Grafana allows users to share dashboards and visualisations with external stakeholders who may not have Grafana accounts by creating public dashboards via snapshots."
"What is a key advantage of using Amazon Managed Grafana's alerting feature?","Centralised alerting across multiple data sources","Real-time video streaming","Advanced machine learning algorithms","Automated code deployment","The alerting feature allows you to create centralised alerts across a wide range of supported data sources."
"In Amazon Managed Grafana, what is the primary purpose of data sources?","To connect Grafana to various databases and services","To define user permissions and access controls","To create visual dashboards","To configure alert notifications","Data sources in Grafana are used to connect Grafana to various databases, metrics stores, and other services from which Grafana can retrieve data for visualisation."
"Which AWS service is commonly used as a data source for Amazon Managed Grafana to visualise AWS service metrics?","Amazon CloudWatch","Amazon S3","Amazon EC2","Amazon Lambda","Amazon CloudWatch provides metrics for AWS services, making it a common data source for visualising AWS infrastructure performance in Grafana."
"In Amazon Managed Grafana, what is the role of plugins?","To extend Grafana's functionality with new data sources, panel types, and applications","To manage user authentication and authorisation","To configure Grafana's global settings","To schedule automatic dashboard exports","Plugins extend Grafana's functionality by adding new data sources, panel types (visualisations), and entire applications that can run within Grafana."
"How does Amazon Managed Grafana handle user authentication?","Via AWS Identity and Access Management (IAM) or AWS Single Sign-On (SSO)","Using Grafana's built-in user management","Through integration with Active Directory","Using client-side certificates","Amazon Managed Grafana integrates with AWS IAM or AWS SSO for user authentication, providing a secure and centralised way to manage user access."
"What is the benefit of using Amazon Managed Grafana's built-in alert manager?","It allows you to define alerting rules based on data source queries and send notifications","It automatically optimises dashboard performance","It automatically scales Grafana instances","It provides real-time data encryption","Grafana's alert manager lets you define alerting rules based on your data source queries and send notifications via various channels (email, Slack, etc.) when those rules are triggered."
"When using Amazon Managed Grafana, what is a workspace?","A dedicated Grafana environment for your organisation or project","A collection of dashboards","A specific data source configuration","A predefined set of alerting rules","A workspace in Amazon Managed Grafana provides a dedicated Grafana environment for your organisation or project, allowing you to isolate resources and manage access independently."
"How can you control access to Amazon Managed Grafana dashboards and resources?","Using Grafana's permission settings within the Amazon Managed Grafana workspace and IAM policies","Using AWS CloudTrail policies","Using VPC security groups","Using AWS Config rules","Amazon Managed Grafana access control is managed through Grafana's permission settings, allowing control over who can view, edit, or manage dashboards and resources, supplemented by AWS IAM policies for workspace-level access."
"What level of customisation is available with Amazon Managed Grafana compared to self-managed Grafana?","Amazon Managed Grafana offers less customisation and focuses on operational simplicity","Amazon Managed Grafana offers more customisation as AWS manages the underlying infrastructure","Amazon Managed Grafana and self-managed Grafana have equal levels of customisation","Amazon Managed Grafana has more options for user authentication","Amazon Managed Grafana typically offers less customisation than a self-managed Grafana instance because AWS manages the underlying infrastructure and certain settings. This trade-off simplifies operations but limits configuration options."
"What is the advantage of using Amazon Managed Grafana over setting up and managing your own Grafana server on EC2?","Reduced operational overhead and automatic updates","Lower cost for small deployments","Greater control over server configuration","Native support for all data sources","Amazon Managed Grafana handles the patching, scaling, and maintenance of the Grafana instance, reducing operational overhead for users and providing automatic updates."
"Which of the following is a typical use case for Amazon Managed Grafana?","Visualising infrastructure and application metrics collected by Amazon CloudWatch, Prometheus, and other monitoring tools.","Managing and configuring AWS IAM roles and policies","Running machine learning models on large datasets","Storing and retrieving files from object storage","Amazon Managed Grafana excels at visualising infrastructure and application metrics collected from various sources, providing insights into system performance and health."