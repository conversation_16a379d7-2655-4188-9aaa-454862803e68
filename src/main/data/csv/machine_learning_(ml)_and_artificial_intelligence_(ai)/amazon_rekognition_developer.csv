"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Amazon Rekognition's 'DetectLabels' API?","Identifying objects, scenes, and concepts within an image.","Recognising specific individuals in an image.","Transcribing text from an image.","Anonymising faces in an image.","DetectLabels is designed to analyse images and identify various objects, scenes, and concepts present within them. This includes identifying items like 'car', 'dog', 'beach', etc."
"Which Amazon Rekognition API would you use to find similar faces in a large collection of faces?","SearchFaces","DetectFaces","CompareFaces","IndexFaces","SearchFaces allows you to search for faces that are similar to a given face in a collection of faces that have been indexed."
"What is the purpose of using 'FaceLivenessSession' with Amazon Rekognition Face Liveness?","To ensure the user is a real person and present during the authentication.","To compare two faces and return a similarity score.","To detect faces in an image or video.","To identify the emotions on a person's face.","Face Liveness helps to prevent spoofing attacks by verifying that the user is a real person interacting with the authentication system in real-time."
"In Amazon Rekognition Video, what is a 'Segment'?","A section of video containing similar content or activity.","A single frame extracted from the video.","A metadata tag added to a video.","A collection of video streams.","Segments are defined by the user to label parts of the video with certain qualities or content within them."
"Which Amazon Rekognition API is used to identify and recognise celebrities in an image?","RecognizeCelebrities","DetectFaces","DetectLabels","SearchFacesByImage","RecognizeCelebrities is specifically designed to identify and provide information about celebrities recognised in an image."
"What does Amazon Rekognition's 'DetectModerationLabels' API do?","Identifies potentially inappropriate or offensive content in images and videos.","Detects the dominant colours in an image.","Identifies the makes and models of vehicles in an image.","Detects the age range of faces in an image.","DetectModerationLabels identifies potentially offensive content in images and videos, returning labels indicating the type and severity of the content."
"What is the function of 'CreateCollection' in Amazon Rekognition?","To create a repository for storing indexed faces.","To create a group of images for batch processing.","To create a queue for processing video streams.","To create a set of custom labels for object detection.","CreateCollection is used to create a container or repository in Rekognition where you can store and manage indexed faces. The collection is used to store face metadata."
"Which Amazon Rekognition API would you use to determine the quality and suitability of an image for identity verification?","DetectFaces","DetectQuality","CompareFaces","GetFaceLivenessSessionResults","DetectQuality analyses the face image to determine whether it is of sufficient quality for face comparison."
"What is the 'Bounding Box' in Amazon Rekognition's 'DetectFaces' API?","A rectangle defining the location and size of a face in an image.","A list of detected emotions in a face.","A score indicating the confidence level of face detection.","A set of facial landmarks identified in a face.","The bounding box defines the spatial location of a face in the image by specifying the coordinates of the rectangle that surrounds the face."
"What type of information can Amazon Rekognition 'DetectText' extract?","Printed and handwritten text from images and videos.","The sentiment expressed in a text document.","The dominant language in a text document.","The author of a text document.","DetectText is capable of extracting both printed and handwritten text from images and videos, making it useful for a variety of applications like document processing and OCR."
"What is the purpose of the 'IndexFaces' API in Amazon Rekognition?","To add facial features from an image to an existing face collection.","To remove a specific face from a face collection.","To list all faces in a face collection.","To delete a face collection.","IndexFaces adds the facial features of a detected face in an image to an existing face collection, allowing for later identification and searching."
"Which Amazon Rekognition API helps identify the demographic information (age range, gender) of detected faces?","DetectFaces","RecognizeCelebrities","DetectLabels","DetectModerationLabels","The DetectFaces API can provide demographic information such as the estimated age range and gender of detected faces."
"In Amazon Rekognition Video, what does 'GetPersonTracking' do?","Returns information about persons detected in a stored video.","Tracks objects moving in a video.","Detects faces in a live video stream.","Analyses the emotions of people in a video.","GetPersonTracking provides details about people detected in a stored video, including their movements and attributes over time."
"What is the confidence score in Amazon Rekognition?","The probability that the detected object or face is correctly identified.","The level of image quality.","The processing speed of the API.","The security level of the API request.","The confidence score represents the probability that the detected object or face has been correctly identified by Amazon Rekognition. A higher score indicates greater certainty."
"What kind of analysis can Amazon Rekognition Custom Labels perform?","Image classification and object detection with custom datasets.","Sentiment analysis of text data.","Speech-to-text transcription.","Real-time translation of text data.","Amazon Rekognition Custom Labels enables you to train the service to identify objects and image scenes specific to your business needs, providing image classification and object detection with custom datasets."
"Which Amazon Rekognition API is used to compare two faces and return a similarity score?","CompareFaces","SearchFaces","DetectFaces","IndexFaces","CompareFaces is used to determine the similarity between two faces, returning a similarity score indicating how closely the two faces match."
"What is the function of 'DeleteCollection' in Amazon Rekognition?","To remove a face collection and all its associated data.","To delete a specific image from a collection.","To remove a specific face from a face collection.","To clear all indexed faces from a collection.","DeleteCollection permanently removes a face collection and all of the faces indexed within it. This action cannot be undone."
"In Amazon Rekognition, what does 'GetFaceSearch' do?","Returns the results of searching for matching faces in a collection.","Detects faces in an image.","Compares two faces.","Creates a new face collection.","GetFaceSearch returns the results of a face search operation performed on a stored video, allowing you to find instances where a specific face appears in the video."
"What kind of data does Amazon Rekognition 'DetectProtectiveEquipment' detect?","Personal protective equipment (PPE) such as face masks and helmets.","The make and model of vehicles.","The brand logos in an image.","The dominant colours in an image.","DetectProtectiveEquipment can identify whether people in an image are wearing the appropriate protective equipment, such as face masks or helmets."
"What is the use case for Amazon Rekognition Face Liveness?","Verify a user's identity in real time to prevent fraud.","Detect the sentiment expressed in text.","Recognize celebrities in a video.","Transcribe speech to text.","Face Liveness enables real-time verification that a user is a real person and physically present during the authentication process, helping to prevent spoofing and fraud."
"Which feature of Amazon Rekognition helps you find faces in an image that match a particular face ID from a face collection?","SearchFaces","DetectFaces","CompareFaces","IndexFaces","SearchFaces can be used to find faces that match a specified face ID in a face collection."
"What does the 'AgeRange' attribute returned by Amazon Rekognition's 'DetectFaces' API indicate?","The estimated age range of the detected face.","The exact age of the person in the image.","The number of wrinkles on the person's face.","The quality of the image.","The AgeRange attribute provides an estimated age range for the detected face, helping in demographic analysis and other applications."
"What is the purpose of the 'StartFaceSearch' API in Amazon Rekognition Video?","Starts the asynchronous process of searching for faces in a video.","Detects faces in a live video stream.","Compares two faces in a video.","Indexes faces in a video.","StartFaceSearch initiates the asynchronous processing of searching for matching faces in a video."
"In Amazon Rekognition, what does the 'TextDetection' type returned by 'DetectText' represent?","A line or word of text detected in the image.","The font size of the detected text.","The language of the detected text.","The confidence level of the text detection.","The TextDetection type represents a line or word of text that was detected in the image. The value indicates the precise text detected."
"Which of the following is NOT a feature provided by Amazon Rekognition?","Speech recognition","Face detection","Object and scene detection","Text detection","Amazon Rekognition provides powerful image and video analysis capabilities, but does not offer speech recognition services. Other AWS Services like Transcribe provide such functionalities."
"Which Amazon Rekognition feature would you use to automatically redact faces from images or videos?","DetectFaces with bounding box masking","CompareFaces with redaction","DetectModerationLabels with redaction","CreateCollection with redaction","DetectFaces can be used to identify the bounding box of faces and these coordinates can then be used to mask or redact the faces."
"What is the purpose of the 'RegionsOfInterest' parameter within Amazon Rekognition Custom Labels?","To specify areas within an image for focused training.","To identify geographical regions in images.","To filter images based on their location.","To group images by region.","RegionsOfInterest helps the custom model focus on specific areas in images during training, improving accuracy for those areas."
"Which Amazon Rekognition API can detect explicit or suggestive adult content in images?","DetectModerationLabels","DetectFaces","DetectLabels","RecognizeCelebrities","The DetectModerationLabels API can identify various types of inappropriate or offensive content, including explicit or suggestive adult material."
"Which Amazon Rekognition Video operation allows you to get the details of labels detected in a stored video?","GetLabelDetection","DetectLabels","StartLabelDetection","GetPersonTracking","GetLabelDetection retrieves the results of label detection from a stored video, providing information about what objects and scenes were identified and when they appeared."
"What is the purpose of the 'MinConfidence' parameter in Amazon Rekognition APIs like DetectFaces and DetectLabels?","To set the minimum confidence level for a detection to be returned.","To define the maximum number of detections to return.","To specify the region of interest for detections.","To set the maximum processing time for the API call.","The MinConfidence parameter sets the threshold for confidence scores. Only detections with a confidence score above this value are returned."
"What does the Amazon Rekognition API 'DetectProtectiveEquipment' provide?","Identifies if people are wearing protective equipment like masks, helmets, and gloves.","Recognises the emotions of people in an image.","Determines the age range of individuals in an image.","Detects the makes and models of vehicles in an image.","DetectProtectiveEquipment specifically detects the presence of personal protective equipment (PPE) such as face masks, safety helmets, and gloves on individuals in an image or video."
"In Amazon Rekognition, what is the purpose of the 'PersonDetections' array returned by 'GetPersonTracking'?","Contains information about each person detected in the video segment.","Lists the number of people detected.","Provides the timestamps when each person entered the scene.","Shows the direction each person is moving.","The PersonDetections array within the GetPersonTracking response provides detailed information about each person detected, including their bounding box, confidence, and tracked ID."
"Which service complements Amazon Rekognition by providing text-to-speech functionality?","Amazon Polly","Amazon Translate","Amazon Comprehend","Amazon Transcribe","Amazon Polly provides text-to-speech functionality, which is different from Rekognition’s image and video analysis capabilities."
"Which type of Amazon Rekognition Custom Labels model allows you to identify the location of specific objects within an image?","Object detection","Image classification","Face detection","Text detection","Object detection models, as opposed to image classification models, can identify the location of each object in the image, using a bounding box."
"What is a common use case for Amazon Rekognition's 'DetectText' API in the financial industry?","Automating the extraction of information from scanned documents like invoices and bank statements.","Analysing customer sentiment in social media posts.","Detecting fraudulent transactions based on image analysis.","Generating reports on financial market trends.","DetectText is used to extract information from scanned documents, which is useful for automating tasks like invoice processing and bank statement analysis in the financial industry."
"What is the name of the Amazon Rekognition feature that enables real-time analysis of video streams for tasks like security monitoring and traffic analysis?","Amazon Rekognition Video Streams","Amazon Rekognition Live","Amazon Rekognition Stream Analytics","Amazon Rekognition Video Events","Amazon Rekognition Video Streams allows for real-time video analysis, making it suitable for use cases like security monitoring and traffic management."
"What is the benefit of using Amazon Rekognition with AWS Lambda?","Automating image and video analysis workflows based on events.","Scaling database capacity automatically.","Monitoring the health of EC2 instances.","Managing user access to AWS resources.","Integrating Amazon Rekognition with AWS Lambda allows you to create serverless applications that automatically process images and videos in response to events like file uploads to S3."
"Which Amazon Rekognition API would you use to automatically tag images with relevant keywords based on detected objects and scenes?","DetectLabels","IndexFaces","DetectText","RecognizeCelebrities","DetectLabels automatically identifies objects, scenes, and concepts in an image, allowing for automatic tagging with relevant keywords."
"What is the purpose of Amazon Rekognition Face Liveness Session ID?","Uniquely identifies a face liveness session.","Identifies a specific face in a collection.","Determines the video quality of a face liveness session.","Determines the overall confidence score in the accuracy of the face match during the liveness check.","The session ID uniquely identifies each face liveness session, allowing to track the details and results of each attempt."
"What is one advantage of using Amazon Rekognition compared to building your own image and video analysis solution from scratch?","Reduced development time and cost due to pre-trained models.","Unlimited customisation options for specific use cases.","Full control over the underlying algorithms.","Higher accuracy in all scenarios.","Amazon Rekognition provides pre-trained models, which significantly reduces the development time and cost compared to building a solution from scratch."
"What kind of labels can Amazon Rekognition DetectLabels API identify?","Objects, scenes, and concepts","Personal Protective Equipment","Explicit or suggestive content","Facial landmarks","DetectLabels is designed to identify objects, scenes and concepts and has been trained on millions of images. Some examples are; 'tree', 'sunset', 'indoors'"
"What is the first step that must be taken before indexing faces using the Amazon Rekognition API?","Create a collection","Create an IAM role","Create a DynamoDB table","Create an S3 bucket","Before indexing faces, you must first create a collection to store the face metadata."
"Which of the following is a good use case for Amazon Rekognition in the retail industry?","Analysing customer demographics in a store.","Predicting sales based on historical data.","Managing inventory levels automatically.","Optimising website search results.","Rekognition can analyse video footage of customers in a store to understand demographics (age, gender, etc.) and foot traffic patterns."
"What does the 'ExternalImageId' parameter in the IndexFaces API allow you to do?","Associate a custom identifier with each face in a collection.","Specifies the location of the image in an external storage system.","Sets the expiration date for the indexed face.","Encrypts the image data before indexing.","ExternalImageId allows you to associate a unique, custom identifier with each face in the collection, making it easier to manage and retrieve face data."
"What is the main benefit of using Amazon Rekognition Custom Labels for identifying specific parts in manufacturing assembly lines?","Allows to train custom models tailored to the specific components and visual characteristics unique to manufacturing lines.","Allows to identify handwritten text within assembly line manuals.","Provides real-time translation of instructions from one language to another.","Facilitates the tracking of employee attendance.","Amazon Rekognition Custom Labels allows businesses to train models on their specific components, which makes it ideal for identifying these specific parts in the manufacturing assembly lines."
"In Amazon Rekognition, what is the primary function of the 'DetectFaces' operation?","To identify and analyse human faces within an image or video.","To transcribe speech in an audio file.","To moderate inappropriate content in images.","To recognise text in an image.","'DetectFaces' specifically identifies and analyses facial attributes like age range, gender, and emotions."
"Which Amazon Rekognition feature is used for recognising well-known individuals in images or videos?","Celebrity Recognition","Face Detection","Image Moderation","Text Detection","Celebrity Recognition identifies publicly known figures based on a database of celebrities."
"What type of analysis does Amazon Rekognition's 'DetectLabels' operation perform?","Identifies objects, scenes, and concepts in an image.","Detects and extracts text from images.","Analyses the sentiment of text.","Identifies personal protective equipment.","'DetectLabels' analyses the overall content of an image and provides labels representing the detected objects, scenes, and concepts."
"Which Amazon Rekognition feature is designed to automatically identify explicit or suggestive content in images and videos?","Image Moderation","Face Detection","Label Detection","Custom Labels","Image Moderation helps you flag inappropriate content and maintain brand safety."
"What does the term 'bounding box' refer to in the context of Amazon Rekognition?","A rectangular frame around an object detected in an image.","A text box used for entering image metadata.","A storage container for processed images.","A configuration setting for API calls.","A bounding box is a rectangular frame that outlines the detected object, providing its location within the image."
"Which Amazon Rekognition API is used for comparing a face in a source image with faces detected in a target image?","CompareFaces","DetectFaces","SearchFaces","IndexFaces","'CompareFaces' allows you to determine the similarity between faces in two different images."
"What is the purpose of Amazon Rekognition Video's 'Person Tracking' feature?","To identify and follow the movements of individuals within a video.","To blur the faces of individuals in a video.","To transcribe the speech of individuals in a video.","To moderate content detected in the video.","'Person Tracking' allows you to monitor and analyse the movements of specific individuals throughout a video."
"Which Amazon Rekognition feature allows you to create custom machine learning models to identify objects and scenes specific to your business needs?","Custom Labels","Face Detection","Label Detection","Content Moderation","Custom Labels lets you train your own models to recognise objects and scenes specific to your requirements."
"What type of data source can Amazon Rekognition Video analyse?","Video stored in Amazon S3.","Audio stored in Amazon S3.","Text files stored in Amazon S3.","Databases stored in Amazon RDS.","Rekognition Video processes video files stored in S3."
"Which Amazon Rekognition API is primarily used for creating a searchable collection of faces?","IndexFaces","DetectFaces","CompareFaces","SearchFacesByImage","'IndexFaces' adds facial information to a collection, making it searchable."
"What is the function of the 'SearchFacesByImage' API in Amazon Rekognition?","To find faces similar to the face in the input image within a collection.","To detect all faces present in an image.","To compare two faces for similarity.","To create a collection of faces.","'SearchFacesByImage' searches an existing face collection for faces that resemble the face in the input image."
"Which Amazon Rekognition feature can be used to detect and analyse the emotions displayed on a person's face?","Face Detection","Label Detection","Celebrity Recognition","Content Moderation","Face Detection can analyse various attributes, including emotional expressions."
"In Amazon Rekognition, what is a 'collection'?","A container for storing indexed faces.","A set of images used for training custom models.","A list of labels detected in an image.","A group of AWS accounts with access to Rekognition.","A collection is a database of faces that Rekognition uses to perform searches."
"Which Amazon Rekognition operation returns the dominant language detected in an image containing text?","DetectText","DetectLabels","DetectFaces","DetectModerationLabels","While DetectText extracts the text, it doesn't explicitly identify language. It might be inferred based on the output, but language detection isn't its primary function."
"Which of the following is NOT a common use case for Amazon Rekognition?","Analysing financial transactions for fraud detection.","Analysing security camera footage for person identification.","Moderating user-generated content.","Adding metadata to images based on detected objects.","Rekognition is not generally used for analysing financial data."
"Which Amazon Rekognition feature can be used to automatically tag images with descriptive keywords?","Label Detection","Face Detection","Celebrity Recognition","Image Moderation","Label Detection automatically identifies objects, scenes and concepts, allowing for automatic tagging."
"What is the 'confidence score' returned by Amazon Rekognition APIs?","A measure of the accuracy of the detected object or attribute.","A measure of the processing time required.","A measure of the cost associated with the API call.","A measure of the image quality.","The confidence score indicates how confident Rekognition is in its prediction."
"What is the purpose of the 'RegionsOfInterest' parameter in Amazon Rekognition APIs?","To specify the area of an image to be analysed.","To specify the AWS region to use.","To define the pricing tier for the API call.","To define the level of image detail to be considered.","'RegionsOfInterest' allows you to focus the analysis on specific areas of an image."
"Which Amazon Rekognition feature is most suitable for identifying potential safety hazards in a manufacturing environment?","Custom Labels","Celebrity Recognition","Face Detection","Text Detection","Custom labels allows you to train a model to identify custom objects."
"Which Amazon Rekognition API is used to find faces in a collection that are similar to a face ID?","SearchFaces","CompareFaces","DetectFaces","IndexFaces","SearchFaces lets you search a collection for similar face IDs."
"What is the maximum number of faces that Amazon Rekognition can detect in a single image using the 'DetectFaces' API?","100","10","Unlimited","50","Rekognition can detect up to 100 faces in a single image."
"Which Amazon Rekognition feature can be used to identify the brand logos present in an image?","Label Detection with Custom Labels","Face Detection","Celebrity Recognition","Image Moderation","Using Custom Labels, you could train a model to detect brand logos."
"What is the purpose of the 'MinConfidence' parameter in Amazon Rekognition APIs?","To set the minimum confidence level for detected objects or attributes to be returned.","To set the minimum number of objects or attributes to be returned.","To set the minimum image quality required for analysis.","To set the minimum processing time allowed for the API call.","'MinConfidence' allows you to filter results based on confidence level."
"Which AWS service does Amazon Rekognition typically integrate with for storing and accessing images and videos?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","Rekognition typically retrieves images and videos directly from S3 buckets."
"Which Amazon Rekognition feature can be used to analyse demographic information such as age range and gender from faces in an image?","Face Detection","Label Detection","Celebrity Recognition","Image Moderation","Face Detection provides attributes such as age range and gender."
"Which Amazon Rekognition API is most appropriate to use in order to detect if an image contains explicit adult content?","DetectModerationLabels","DetectFaces","DetectLabels","DetectText","DetectModerationLabels is designed to find explicit or suggestive content."
"When using Amazon Rekognition Video, what is a 'segment'?","A defined portion of a video being analysed.","A collection of faces.","A label detected in a video.","A custom model.","In Rekognition Video, a segment is a specified part of the video you're analysing."
"What is the main purpose of using Amazon Rekognition with AWS Lambda?","To automate image and video analysis workflows.","To store images and videos.","To manage user access to Rekognition.","To monitor the cost of Rekognition usage.","Lambda can be used to trigger Rekognition analysis automatically."
"Which of the following is a key benefit of using Amazon Rekognition for image analysis?","It reduces the need for manual image tagging and categorisation.","It eliminates the need for image storage.","It replaces traditional image editing software.","It completely automates video production.","Rekognition automates tagging and categorisation, saving manual effort."
"With regards to Amazon Rekognition, what does the term 'UnsearchedFaces' represent?","The number of faces that could not be indexed into the face collection.","The number of faces that were successfully indexed into the face collection.","The number of images that could not be processed.","The number of users with no permissions to the face collection.","UnsearchedFaces represents the faces in the source image that could not be indexed."
"What type of analysis would you perform if you wanted to identify objects within an image using a pre-trained model from Amazon Rekognition?","Label detection","Custom label detection","Face detection","Text detection","Label detection uses pre-trained models to identify common objects, scenes and concepts."
"What's a primary benefit of using Amazon Rekognition for content moderation?","It automates the process of identifying inappropriate content.","It eliminates the need for human moderators.","It guarantees 100% accuracy in content detection.","It reduces storage costs for images and videos.","Content moderation automates the detection of unwanted content."
"Which Amazon Rekognition operation can be used to identify the text present in an image?","DetectText","DetectLabels","DetectFaces","DetectModerationLabels","DetectText is specifically designed to extract text from images."
"You want to use Amazon Rekognition to build an application that automatically identifies the breed of dogs in images uploaded by users. Which Rekognition feature would be best suited for this task?","Custom Labels","Face Detection","Label Detection","Celebrity Recognition","Custom Labels is the correct choice, as it can be trained with domain-specific objects."
"Which AWS service is typically used to stream videos in real-time to Amazon Rekognition Video for analysis?","Amazon Kinesis Video Streams","Amazon SQS","Amazon SNS","Amazon CloudWatch","Kinesis Video Streams enables real-time video ingestion into Rekognition Video."
"What is the relationship between Amazon Rekognition and machine learning?","Amazon Rekognition is a pre-trained machine learning service.","Amazon Rekognition requires users to build their own machine learning models.","Amazon Rekognition is a database for storing machine learning models.","Amazon Rekognition is a tool for monitoring machine learning performance.","Rekognition leverages machine learning without requiring you to build your own model."
"Which Amazon Rekognition API operation allows you to determine the age range of faces detected in an image?","DetectFaces","CompareFaces","SearchFaces","IndexFaces","The DetectFaces API returns the detected faces including attributes such as age ranges."
"What is the purpose of the 'CreateCollection' API operation in Amazon Rekognition?","To create a container for storing indexed faces.","To upload images to Rekognition for analysis.","To create a custom label model.","To configure access policies for Rekognition.","'CreateCollection' creates a repository for storing indexed faces."
"Which Amazon Rekognition feature can be used to analyse the sentiment expressed in text within an image?","Comprehend","DetectText","DetectLabels","Translate","Comprehend is used to analyse the sentiment in text."
"What is a key difference between 'DetectLabels' and 'Custom Labels' in Amazon Rekognition?","'Custom Labels' requires training on your own data, while 'DetectLabels' uses a pre-trained model.","'DetectLabels' is used for face detection, while 'Custom Labels' is used for object detection.","'DetectLabels' is used for video analysis, while 'Custom Labels' is used for image analysis.","'DetectLabels' is free, while 'Custom Labels' requires a paid subscription.","Custom Labels needs to be trained before it can be used."
"You have a large collection of images and need to efficiently search for images containing a specific object. Which approach would be most suitable using Amazon Rekognition?","Use Custom Labels and index images based on the custom labels.","Use DetectFaces and manually compare each image.","Use DetectText and search for relevant keywords.","Use Image Moderation to filter images.","Custom labels can be used to efficiently find images."
"What is the purpose of providing an 'ExternalImageId' when indexing faces in Amazon Rekognition?","To associate a unique identifier with each face for easier tracking and management.","To specify the storage location of the image.","To encrypt the image data.","To specify the image resolution.","'ExternalImageId' provides a way to uniquely identify and track faces within your system."
"What is a typical use case for Amazon Rekognition in the retail industry?","Analysing customer demographics and behaviour in stores.","Managing inventory levels in warehouses.","Processing online payments.","Providing customer support chatbots.","Rekognition analyses customer demographics."
"What is a key advantage of using Amazon Rekognition's face comparison feature over manual comparison?","Increased speed and accuracy.","Lower storage costs.","Improved image quality.","Enhanced security.","Face comparison using Rekognition is generally faster and more accurate than manual comparison."
"In Amazon Rekognition Video, what is the function of the 'GetCelebrityRecognition' API?","To retrieve the celebrities detected in a video.","To detect faces in a video.","To moderate content detected in a video.","To transcribe text in a video.","GetCelebrityRecognition returns the details of the celebrities identified in a video analysis job."
"What type of data can Amazon Rekognition's 'DetectProtectiveEquipment' API identify?","Personal protective equipment (PPE) such as hard hats and safety glasses.","Types of vehicle such as cars and trucks.","Types of animals such as dogs and cats.","Types of furniture such as tables and chairs.","DetectProtectiveEquipment is designed to identify various types of PPE."
"You need to ensure that the faces indexed in an Amazon Rekognition collection meet a certain quality threshold. How can you achieve this?","By setting a minimum confidence level when indexing faces.","By manually reviewing each image before indexing.","By using a higher resolution camera.","By enabling encryption for the collection.","Setting a confidence level during indexing ensures only high-quality faces are stored."
"Which parameter in the 'DetectText' operation of Amazon Rekognition allows you to specify the level of detail in the detected text, such as individual words or lines?","TextDetections","Confidence","Filters","MaxResults","TextDetections provides information about the detected text, including confidence and geometry."
"You have an image stored in Amazon S3 that you want to analyse using Amazon Rekognition. Which of the following is the correct way to specify the image location in the API request?","Provide the S3 bucket name and object key.","Provide the local file path.","Provide the HTTP URL of the image.","Provide the Amazon EC2 instance ID.","S3 location is indicated by bucket name and object key."
