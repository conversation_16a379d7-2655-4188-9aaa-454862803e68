"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Kendra, what is the purpose of a data source connector in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To crawl and ingest data from various repositories","To train custom ML models","To visualise search analytics","To manage user permissions","Data source connectors in Amazon Kendra are used to crawl and ingest data from various repositories, such as S3 buckets, websites, and databases, making it searchable."
"Which AWS CLI command is used to create an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra create-index","aws kendra create-kendra-index","aws kendra start-index","aws kendra provision-index","The `aws kendra create-index` command is used to create a new Amazon Kendra index."
"What IAM permission is required to allow an Amazon Kendra index to access an S3 bucket in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","s3:GetObject","kendra:ReadIndex","s3:ListBucket","kendra:CreateIndex","The `s3:GetObject` permission is required to allow an Amazon Kendra index to access objects within an S3 bucket."
"How does Amazon Kendra use relevance tuning in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To adjust the importance of specific fields in the search results","To automatically translate documents into different languages","To encrypt data at rest","To automatically generate summaries of search results","Relevance tuning in Amazon Kendra allows you to adjust the importance of specific fields in the search results, improving the accuracy and relevance of the results."
"What is the purpose of the 'SubmitFeedback' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To provide feedback on the relevance of search results","To submit bug reports","To request new features","To provide feedback on the Kendra service itself","The 'SubmitFeedback' API in Amazon Kendra is used to provide feedback on the relevance of search results, which helps Kendra improve its search accuracy over time."
"In Amazon Kendra, what is a custom entity in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","A specific piece of information that Kendra extracts from documents","A user-defined synonym for a search term","A pre-trained ML model","A data source connector","A custom entity in Amazon Kendra is a specific piece of information that Kendra extracts from documents, such as product names or employee titles."
"Which of the following is a valid data source type for Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","SharePoint","Amazon SQS","AWS Lambda","Amazon CloudWatch Logs","SharePoint is a valid data source type for Amazon Kendra, allowing you to index documents stored in SharePoint."
"What is the purpose of the 'Query' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To search the Kendra index","To create a new Kendra index","To update the Kendra index","To delete the Kendra index","The 'Query' API in Amazon Kendra is used to search the Kendra index and retrieve relevant results."
"How does Amazon Kendra handle incremental updates to a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By automatically detecting and indexing new or modified documents","By requiring a full re-index of the data source","By manually triggering an update","By ignoring changes to the data source","Amazon Kendra automatically detects and indexes new or modified documents in a data source, ensuring that the index is always up-to-date."
"What security measure can be used to restrict access to documents in Amazon Kendra based on user identity in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","User context filtering","IP address filtering","MFA","Password policies","User context filtering allows you to restrict access to documents in Amazon Kendra based on the user's identity, ensuring that users only see the documents they are authorised to access."
"In Amazon Kendra, what is the purpose of a thesaurus in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To define synonyms for search terms","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A thesaurus in Amazon Kendra is used to define synonyms for search terms, allowing users to find relevant documents even if they use different words to describe the same concept."
"Which AWS CLI command is used to start a data source sync job in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra start-data-source-sync-job","aws kendra sync-data-source","aws kendra index-data-source","aws kendra update-data-source","The `aws kendra start-data-source-sync-job` command is used to start a data source sync job in Amazon Kendra."
"What is the maximum number of custom entities that can be defined in an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","500","100","1000","Unlimited","The maximum number of custom entities that can be defined in an Amazon Kendra index is 500."
"How does Amazon Kendra support faceted search in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By allowing users to filter search results based on metadata fields","By automatically grouping search results into categories","By providing a visual representation of search results","By allowing users to sort search results by relevance","Amazon Kendra supports faceted search by allowing users to filter search results based on metadata fields, such as author, date, or file type."
"What is the purpose of the 'DescribeIndex' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To retrieve information about a Kendra index","To create a new Kendra index","To update a Kendra index","To delete a Kendra index","The 'DescribeIndex' API in Amazon Kendra is used to retrieve information about a Kendra index, such as its status, configuration, and capacity."
"In Amazon Kendra, what is a query capacity unit (QCU) in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","A unit of measure for the query processing capacity of a Kendra index","A unit of measure for the storage capacity of a Kendra index","A unit of measure for the number of documents in a Kendra index","A unit of measure for the number of users who can access a Kendra index","A query capacity unit (QCU) in Amazon Kendra is a unit of measure for the query processing capacity of a Kendra index, determining how many queries it can handle concurrently."
"Which of the following is a valid document format for Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","PDF","Amazon SQS message","AWS CloudWatch Logs event","Amazon SNS notification","PDF is a valid document format for Amazon Kendra, allowing you to index PDF documents."
"What is the purpose of the 'UpdateIndex' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To modify the configuration of a Kendra index","To search the Kendra index","To create a new Kendra index","To delete a Kendra index","The 'UpdateIndex' API in Amazon Kendra is used to modify the configuration of a Kendra index, such as its name, description, or capacity units."
"How does Amazon Kendra handle access control for documents stored in an S3 bucket in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By using IAM roles and policies","By using access control lists (ACLs)","By using encryption keys","By using password policies","Amazon Kendra handles access control for documents stored in an S3 bucket by using IAM roles and policies, ensuring that only authorised users can access the documents."
"What type of encryption does Amazon Kendra use to protect data at rest in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","AWS Key Management Service (KMS)","SSL/TLS","AES-256","No encryption","Amazon Kendra uses AWS Key Management Service (KMS) to encrypt data at rest, providing a secure way to protect sensitive data."
"In Amazon Kendra, what is the purpose of a stop word list in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To exclude common words from the index","To include only specific words in the index","To boost the ranking of certain documents","To automatically correct spelling errors","A stop word list in Amazon Kendra is used to exclude common words from the index, such as 'the', 'a', and 'an', which can improve search accuracy and performance."
"Which AWS CLI command is used to delete an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra delete-index","aws kendra remove-index","aws kendra terminate-index","aws kendra destroy-index","The `aws kendra delete-index` command is used to delete an Amazon Kendra index."
"What is the maximum document size that Amazon Kendra can ingest in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","50 MB","10 MB","100 MB","1 GB","The maximum document size that Amazon Kendra can ingest is 50 MB."
"How does Amazon Kendra use custom document enrichment in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To modify the content or metadata of documents before indexing","To automatically translate documents into different languages","To encrypt data at rest","To automatically generate summaries of search results","Custom document enrichment in Amazon Kendra allows you to modify the content or metadata of documents before indexing, such as adding custom fields or removing irrelevant information."
"What is the purpose of the 'ListIndices' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To retrieve a list of all Kendra indices","To create a new Kendra index","To update a Kendra index","To delete a Kendra index","The 'ListIndices' API in Amazon Kendra is used to retrieve a list of all Kendra indices in your AWS account."
"In Amazon Kendra, what is the purpose of a data source schedule in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To automatically synchronise data from the data source","To manually trigger a data source sync","To define the data retention period","To define the data access permissions","A data source schedule in Amazon Kendra is used to automatically synchronise data from the data source at regular intervals."
"Which of the following is a valid authentication method for accessing a data source in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","IAM role","Password","Multi-factor authentication","API key","IAM role is a valid authentication method for accessing a data source in Amazon Kendra, providing secure access to the data."
"What is the purpose of the 'DescribeDataSource' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To retrieve information about a data source","To create a new data source","To update a data source","To delete a data source","The 'DescribeDataSource' API in Amazon Kendra is used to retrieve information about a data source, such as its status, configuration, and sync schedule."
"How does Amazon Kendra handle duplicate documents in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By indexing only the most recent version of the document","By indexing all versions of the document","By ignoring duplicate documents","By flagging duplicate documents for review","Amazon Kendra handles duplicate documents in a data source by indexing only the most recent version of the document, ensuring that search results are not cluttered with duplicates."
"What type of query expansion does Amazon Kendra support in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Synonym expansion","Acronym expansion","Spelling correction","All of the above","Amazon Kendra supports synonym expansion, allowing users to find relevant documents even if they use different words to describe the same concept."
"In Amazon Kendra, what is the purpose of a featured result in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To promote a specific document to the top of the search results","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A featured result in Amazon Kendra is used to promote a specific document to the top of the search results for a given query."
"Which AWS CLI command is used to update a data source in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra update-data-source","aws kendra modify-data-source","aws kendra change-data-source","aws kendra set-data-source","The `aws kendra update-data-source` command is used to update a data source in Amazon Kendra."
"What is the maximum number of featured results that can be defined for a query in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","10","5","20","Unlimited","The maximum number of featured results that can be defined for a query in Amazon Kendra is 10."
"How does Amazon Kendra use query suggestions in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To provide users with suggested search terms as they type","To automatically correct spelling errors","To filter out irrelevant documents","To boost the ranking of certain documents","Query suggestions in Amazon Kendra are used to provide users with suggested search terms as they type, helping them to find what they are looking for more quickly."
"What is the purpose of the 'CreateIndex' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To create a new Kendra index","To retrieve information about a Kendra index","To update a Kendra index","To delete a Kendra index","The 'CreateIndex' API in Amazon Kendra is used to create a new Kendra index."
"In Amazon Kendra, what is the purpose of a custom vocabulary in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To define specific terms and their meanings for the Kendra index","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A custom vocabulary in Amazon Kendra is used to define specific terms and their meanings for the Kendra index, improving search accuracy for those terms."
"Which of the following is a valid language for Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","English","SQL","Python","JSON","English is a valid language for Amazon Kendra, allowing you to index documents in English."
"What is the purpose of the 'DeleteDataSource' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To delete a data source","To retrieve information about a data source","To update a data source","To create a new data source","The 'DeleteDataSource' API in Amazon Kendra is used to delete a data source from the Kendra index."
"How does Amazon Kendra handle documents that are password protected in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By skipping the password-protected documents","By prompting the user for the password","By automatically decrypting the documents","By indexing the documents without decrypting them","Amazon Kendra handles documents that are password protected in a data source by skipping the password-protected documents, as it cannot access them."
"What type of machine learning model does Amazon Kendra use for natural language understanding in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Transformer models","Recurrent neural networks","Convolutional neural networks","Support vector machines","Amazon Kendra uses transformer models for natural language understanding, providing state-of-the-art accuracy and performance."
"In Amazon Kendra, what is the purpose of a user group in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To control access to documents based on user identity","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A user group in Amazon Kendra is used to control access to documents based on user identity, ensuring that users only see the documents they are authorised to access."
"Which AWS CLI command is used to list data source sync jobs in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra list-data-source-sync-jobs","aws kendra show-data-source-sync-jobs","aws kendra get-data-source-sync-jobs","aws kendra describe-data-source-sync-jobs","The `aws kendra list-data-source-sync-jobs` command is used to list data source sync jobs in Amazon Kendra."
"What is the maximum number of user groups that can be defined in an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","1000","500","2000","Unlimited","The maximum number of user groups that can be defined in an Amazon Kendra index is 1000."
"How does Amazon Kendra use custom ranking in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To adjust the ranking of search results based on custom criteria","To automatically correct spelling errors","To filter out irrelevant documents","To boost the ranking of certain documents","Custom ranking in Amazon Kendra allows you to adjust the ranking of search results based on custom criteria, such as document age or user feedback."
"What is the purpose of the 'StartDataSourceSyncJob' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To start a data source sync job","To retrieve information about a data source","To update a data source","To delete a data source","The 'StartDataSourceSyncJob' API in Amazon Kendra is used to start a data source sync job, which synchronises data from the data source to the Kendra index."
"In Amazon Kendra, what is the purpose of a relevance feedback loop in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To improve search accuracy based on user feedback","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A relevance feedback loop in Amazon Kendra is used to improve search accuracy based on user feedback, such as thumbs up or thumbs down on search results."
"Which of the following is a valid file type for custom document enrichment in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","JSON","SQL","Python","XML","JSON is a valid file type for custom document enrichment in Amazon Kendra, allowing you to define custom metadata for documents."
"What is the purpose of the 'StopDataSourceSyncJob' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To stop a running data source sync job","To retrieve information about a data source","To update a data source","To delete a data source","The 'StopDataSourceSyncJob' API in Amazon Kendra is used to stop a running data source sync job, which can be useful if the sync job is taking too long or encountering errors."
"How does Amazon Kendra handle documents that are encrypted with customer-managed keys in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By requiring the customer to provide the KMS key","By automatically decrypting the documents","By skipping the encrypted documents","By indexing the encrypted documents without decrypting them","Amazon Kendra handles documents that are encrypted with customer-managed keys by requiring the customer to provide the KMS key, allowing Kendra to decrypt and index the documents."
"What type of data source is best suited for indexing frequently updated content in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","A web crawler","An S3 bucket","A database","A SharePoint site","A web crawler is best suited for indexing frequently updated content, as it can automatically detect and index new or modified documents."
"In Amazon Kendra, what is the purpose of a query log in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To track user search queries for analysis and improvement","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A query log in Amazon Kendra is used to track user search queries for analysis and improvement, helping you understand what users are searching for and how to improve search accuracy."
"Which AWS CLI command is used to describe a data source sync job in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra describe-data-source-sync-job","aws kendra get-data-source-sync-job","aws kendra show-data-source-sync-job","aws kendra list-data-source-sync-job","The `aws kendra describe-data-source-sync-job` command is used to describe a data source sync job in Amazon Kendra."
"What is the maximum number of query suggestions that can be displayed to a user in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","5","10","20","Unlimited","The maximum number of query suggestions that can be displayed to a user in Amazon Kendra is 5."
"How does Amazon Kendra use the 'Relevance' score in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To indicate the relevance of a document to a search query","To automatically correct spelling errors","To filter out irrelevant documents","To boost the ranking of certain documents","The 'Relevance' score in Amazon Kendra is used to indicate the relevance of a document to a search query, with higher scores indicating more relevant documents."
"What is the purpose of the 'ListDataSourceSyncJobs' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To list data source sync jobs","To retrieve information about a data source","To update a data source","To delete a data source","The 'ListDataSourceSyncJobs' API in Amazon Kendra is used to list data source sync jobs for a given data source."
"In Amazon Kendra, what is the purpose of a custom attribute in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To add custom metadata to documents","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A custom attribute in Amazon Kendra is used to add custom metadata to documents, which can be used for faceted search, relevance tuning, and access control."
"Which of the following is a valid data source for user context filtering in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","A JSON file in S3","An SQS queue","A CloudWatch Logs stream","A DynamoDB table","A JSON file in S3 is a valid data source for user context filtering in Amazon Kendra, allowing you to define user groups and their access permissions."
"What is the purpose of the 'UpdateQuerySuggestionsConfig' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To update the configuration of query suggestions","To retrieve information about a data source","To update a data source","To delete a data source","The 'UpdateQuerySuggestionsConfig' API in Amazon Kendra is used to update the configuration of query suggestions, such as the minimum query length and the data source for query suggestions."
"How does Amazon Kendra handle documents that are too large to ingest in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By skipping the oversized documents","By automatically splitting the documents into smaller chunks","By prompting the user to manually split the documents","By indexing only the first 50 MB of the documents","Amazon Kendra handles documents that are too large to ingest by skipping the oversized documents, as it cannot process them."
"What type of algorithm does Amazon Kendra use for synonym expansion in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","A machine learning-based algorithm","A rule-based algorithm","A dictionary-based algorithm","A regular expression-based algorithm","Amazon Kendra uses a machine learning-based algorithm for synonym expansion, providing more accurate and relevant synonyms than rule-based or dictionary-based algorithms."
"In Amazon Kendra, what is the purpose of a boost in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To increase the ranking of certain documents for specific queries","To filter out irrelevant documents","To automatically correct spelling errors","To exclude common words from the index","A boost in Amazon Kendra is used to increase the ranking of certain documents for specific queries, making them more likely to appear at the top of the search results."
"Which AWS CLI command is used to list the tags associated with an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra list-tags-for-resource","aws kendra show-tags-for-resource","aws kendra get-tags-for-resource","aws kendra describe-tags-for-resource","The `aws kendra list-tags-for-resource` command is used to list the tags associated with an Amazon Kendra index."
"What is the maximum number of tags that can be associated with an Amazon Kendra index in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","50","100","200","Unlimited","The maximum number of tags that can be associated with an Amazon Kendra index is 50."
"How does Amazon Kendra use the 'Freshness' boost in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To increase the ranking of newer documents","To automatically correct spelling errors","To filter out irrelevant documents","To exclude common words from the index","The 'Freshness' boost in Amazon Kendra is used to increase the ranking of newer documents, making them more likely to appear at the top of the search results."
"What is the purpose of the 'TagResource' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To add tags to a Kendra index","To retrieve information about a Kendra index","To update a Kendra index","To delete a Kendra index","The 'TagResource' API in Amazon Kendra is used to add tags to a Kendra index, allowing you to organise and manage your Kendra resources."
"In Amazon Kendra, what is the purpose of a data source exclusion filter in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To exclude certain documents from being indexed","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A data source exclusion filter in Amazon Kendra is used to exclude certain documents from being indexed, such as documents that are outdated or irrelevant."
"Which of the following is a valid operator for a data source exclusion filter in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Equals","Contains","StartsWith","All of the above","All of the above are valid operators for a data source exclusion filter in Amazon Kendra, allowing you to define complex exclusion rules."
"What is the purpose of the 'UntagResource' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To remove tags from a Kendra index","To retrieve information about a Kendra index","To update a Kendra index","To delete a Kendra index","The 'UntagResource' API in Amazon Kendra is used to remove tags from a Kendra index, allowing you to manage your Kendra resources."
"How does Amazon Kendra handle documents that are in a language that is not supported in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By skipping the documents in the unsupported language","By automatically translating the documents into a supported language","By prompting the user to manually translate the documents","By indexing the documents without processing them","Amazon Kendra handles documents that are in a language that is not supported by skipping the documents in the unsupported language, as it cannot process them."
"What type of evaluation metric does Amazon Kendra use to measure search accuracy in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Mean Average Precision (MAP)","F1 score","Accuracy","Recall","Amazon Kendra uses Mean Average Precision (MAP) to measure search accuracy, providing a comprehensive evaluation of the ranking of search results."
"In Amazon Kendra, what is the purpose of a confidence threshold in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To filter out search results with low confidence scores","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A confidence threshold in Amazon Kendra is used to filter out search results with low confidence scores, ensuring that only the most relevant and accurate results are displayed."
"Which AWS CLI command is used to create a thesaurus in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra create-thesaurus","aws kendra make-thesaurus","aws kendra add-thesaurus","aws kendra new-thesaurus","The `aws kendra create-thesaurus` command is used to create a thesaurus in Amazon Kendra."
"What is the maximum size of a thesaurus file that can be uploaded to Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","10 MB","5 MB","20 MB","Unlimited","The maximum size of a thesaurus file that can be uploaded to Amazon Kendra is 10 MB."
"How does Amazon Kendra use the 'Popularity' boost in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To increase the ranking of frequently accessed documents","To automatically correct spelling errors","To filter out irrelevant documents","To exclude common words from the index","The 'Popularity' boost in Amazon Kendra is used to increase the ranking of frequently accessed documents, making them more likely to appear at the top of the search results."
"What is the purpose of the 'DescribeThesaurus' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To retrieve information about a thesaurus","To create a new thesaurus","To update a thesaurus","To delete a thesaurus","The 'DescribeThesaurus' API in Amazon Kendra is used to retrieve information about a thesaurus, such as its status, configuration, and synonyms."
"In Amazon Kendra, what is the purpose of a custom normalisation in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To transform text for consistent indexing and searching","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A custom normalisation in Amazon Kendra is used to transform text for consistent indexing and searching, such as converting all text to lowercase or removing punctuation."
"Which of the following is a valid normalisation type for custom normalisation in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Lowercase","Uppercase","RemovePunctuation","All of the above","All of the above are valid normalisation types for custom normalisation in Amazon Kendra, allowing you to define complex normalisation rules."
"What is the purpose of the 'DeleteThesaurus' API in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To delete a thesaurus","To retrieve information about a thesaurus","To update a thesaurus","To create a new thesaurus","The 'DeleteThesaurus' API in Amazon Kendra is used to delete a thesaurus from the Kendra index."
"How does Amazon Kendra handle documents that have broken links in a data source in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","By indexing the documents with a warning about the broken links","By automatically fixing the broken links","By skipping the documents with broken links","By indexing the documents without checking for broken links","Amazon Kendra handles documents that have broken links by indexing the documents with a warning about the broken links, allowing users to be aware of the issue."
"What type of weighting does Amazon Kendra use for different fields in a document in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","Field weighting","Term weighting","Document weighting","Query weighting","Amazon Kendra uses field weighting to give different importance to different fields in a document, such as title, body, and keywords, improving search accuracy."
"In Amazon Kendra, what is the purpose of a query intent in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","To understand the user's goal behind a search query","To filter out irrelevant documents","To boost the ranking of certain documents","To automatically correct spelling errors","A query intent in Amazon Kendra is used to understand the user's goal behind a search query, allowing Kendra to provide more relevant and accurate results."
"Which AWS CLI command is used to describe a thesaurus in Amazon Kendra in the context of Machine Learning (ML) and Artificial Intelligence (AI)?","aws kendra describe-thesaurus","aws kendra get-thesaurus","aws kendra show-thesaurus","aws kendra list-thesaurus","The `aws kendra describe-thesaurus` command is used to describe a thesaurus in Amazon Kendra."
"In Amazon Kendra, what is the primary function of a data source?","To connect to and ingest data from various repositories","To define access control policies","To configure query suggestions","To visualise search analytics","Data sources are used to establish connections with different repositories (e.g., S3, SharePoint) and ingest data into Kendra for indexing."
"What Amazon Kendra API operation is used to submit or update documents in an index?","BatchPutDocument","UpdateIndex","CreateDataSource","StartIndexSyncJob","The `BatchPutDocument` API operation is used to add new documents or update existing ones in a Kendra index."
"Which Amazon Kendra feature allows you to boost the ranking of specific documents based on certain criteria?","Featured Results","Thesaurus","Stop Words","Facets","Featured Results let you manually select the documents to be promoted for a search."
"When creating a new index in Amazon Kendra, which of the following is a required parameter?","Index name","Description","Role ARN","Data source","The index name must be specified for identification."
"What is the purpose of the 'QuerySuggestionsEnabled' parameter when creating an Amazon Kendra index?","To enable or disable query suggestions","To configure relevance tuning","To filter results based on user groups","To set the data source sync schedule","The 'QuerySuggestionsEnabled' parameter is a boolean flag that enables or disables the query suggestions feature for the index."
"Which Amazon Kendra API operation allows you to retrieve a list of available data sources?","ListDataSources","DescribeIndex","ListIndices","DescribeDataSource","The `ListDataSources` API operation returns a list of data sources associated with your Kendra instance."
"What is the main function of the Amazon Kendra 'DescribeIndex' API operation?","To get details about a specific index","To delete an index","To start an index sync job","To create a new index","`DescribeIndex` provides detailed information about an existing index, such as its status, configuration, and other metadata."
"How can you improve the search relevance of an Amazon Kendra index for a specific domain or industry?","By customising relevance tuning","By increasing storage capacity","By adding more data sources","By enabling document encryption","Relevance tuning allows you to adjust the importance of different fields and attributes to improve the relevance of search results for your specific use case."
"In Amazon Kendra, what is the purpose of a 'Thesaurus'?","To define synonyms for search terms","To filter out irrelevant documents","To define access control policies","To schedule data source synchronisation","A thesaurus is used to define synonyms for search terms, which helps improve search recall by matching queries to documents that use different but related terms."
"Which of the following Amazon Kendra features allows you to control access to documents based on user identity?","User context filtering","Custom data source","Relevance tuning","Query suggestions","User context filtering allows you to restrict search results based on the user's identity and their associated permissions."
"What is the role of the IAM role associated with an Amazon Kendra data source?","To grant Kendra permission to access the data source","To define user access permissions","To encrypt data at rest","To manage the indexing schedule","The IAM role grants Kendra the necessary permissions to access and ingest data from the specified data source."
"What type of data source is commonly used to ingest website content into Amazon Kendra?","Web crawler","Database","SharePoint","S3 bucket","A web crawler is designed to crawl websites and extract content for indexing."
"What is the purpose of the 'FacetedResultsEnabled' parameter when creating an Amazon Kendra index?","To enable or disable faceted search","To configure query expansion","To set the data source crawl depth","To manage user groups","The `FacetedResultsEnabled` parameter allows to enable faceted results."
"Which Amazon Kendra API operation is used to delete a data source?","DeleteDataSource","UpdateIndex","DeleteIndex","StopIndexSyncJob","The `DeleteDataSource` API operation is used to remove a data source from your Kendra instance."
"How does Amazon Kendra handle document updates and deletions in a data source?","Through incremental indexing","By performing a full re-index","By ignoring changes","By relying on manual updates","Kendra uses incremental indexing to efficiently update the index with changes in the data source, such as new documents, updates, and deletions."
"In Amazon Kendra, what is the purpose of defining custom attributes for documents?","To enhance search relevance and filtering","To control user access permissions","To manage storage costs","To monitor indexing performance","Custom attributes allow you to add metadata to documents, which can be used to improve search relevance, filtering, and faceting."
"Which Amazon Kendra feature allows you to provide end-users with suggested search terms as they type their queries?","Query suggestions","Featured results","Thesaurus","Stop words","Query suggestions provide real-time search term suggestions to users as they type, improving the search experience."
"What is the purpose of 'Stop Words' in Amazon Kendra?","To exclude common words from indexing and search","To define synonyms for search terms","To boost the ranking of specific documents","To control access to documents","Stop words are common words (e.g., 'the', 'a', 'an') that are excluded from indexing and search to improve performance and relevance."
"When connecting Amazon Kendra to a SharePoint data source, what authentication method is typically used?","OAuth 2.0","Basic Authentication","API Key","AWS Signature","OAuth 2.0 is a common authentication method for connecting to SharePoint."
"What is the primary function of the Amazon Kendra 'StartIndexSyncJob' API operation?","To initiate a data source synchronisation process","To delete an index","To update an index's configuration","To create a new index","`StartIndexSyncJob` triggers the synchronisation of a data source with the Kendra index, ensuring that the index is up-to-date with the latest data."
"What is the benefit of using Amazon Kendra's 'Featured Results' feature?","It highlights specific documents to boost their visibility in search results","It filters out irrelevant documents","It automatically generates summaries of search results","It translates search queries into multiple languages","Featured Results are used to manually promote specific documents in search results, making them more visible to users."
"When you need to apply fine-grained access control to documents in Amazon Kendra based on user context, which capability should you utilise?","User context filtering","Relevance tuning","Thesaurus management","Query expansion","User context filtering enables the restriction of search results based on a user's identity and permissions, providing fine-grained access control."
"What is the correct order to prepare and use Amazon Kendra for search?","Create Index -> Configure Data Sources -> Ingest Data -> Query Index","Configure Data Sources -> Create Index -> Ingest Data -> Query Index","Ingest Data -> Create Index -> Configure Data Sources -> Query Index","Create Index -> Ingest Data -> Configure Data Sources -> Query Index","The correct order is to create an index, configure data sources, ingest the data and then query the data. Kendra needs to know the format to ingest before starting to use it."
"How can you monitor the performance and usage of your Amazon Kendra index?","Using Amazon CloudWatch metrics","Using Amazon S3 analytics","Using AWS Config rules","Using AWS CloudTrail logs","Amazon CloudWatch provides metrics for monitoring Kendra's performance, usage, and error rates."
"What is the purpose of relevance tuning in Amazon Kendra?","To optimise the ranking of search results based on specific criteria","To encrypt data at rest","To manage user access permissions","To schedule data source synchronisation","Relevance tuning allows you to fine-tune the ranking of search results by adjusting the importance of different fields and attributes."
"Which Amazon Kendra API operation allows you to submit a search query to an index?","Query","Search","GetQuerySuggestions","Retrieve","The `Query` API operation is used to submit search queries to a Kendra index and retrieve the results."
"What should you consider when choosing the edition of Amazon Kendra (Developer or Enterprise)?","The volume of data and query throughput requirements","The desired user interface","The programming language used by developers","The geographic location of users","The choice between Developer and Enterprise editions depends primarily on the volume of data and the expected query throughput."
"What is the purpose of the 'DocumentMetadataConfigurationUpdates' parameter when updating an Amazon Kendra data source?","To update the metadata fields associated with documents","To configure data source access credentials","To modify the indexing schedule","To enable document encryption","The `DocumentMetadataConfigurationUpdates` parameter allows you to update the metadata fields that are extracted and indexed from documents."
"Which of the following file types is commonly supported by Amazon Kendra for document ingestion?","PDF","EXE","ZIP","ISO","PDF is a widely supported document format for ingestion into Kendra."
"What is the main advantage of using Amazon Kendra's natural language processing (NLP) capabilities?","To understand the context and meaning of search queries","To encrypt data at rest","To manage user identities","To monitor network traffic","Kendra's NLP capabilities help it understand the intent and context of search queries, improving the accuracy and relevance of search results."
"How does Amazon Kendra determine the relevance of documents to a search query?","By using machine learning algorithms","By randomly selecting documents","By sorting documents alphabetically","By using a simple keyword match","Kendra uses machine learning algorithms to analyse the content and context of documents and determine their relevance to search queries."
"What is the maximum size of a single document that can be indexed by Amazon Kendra?","50 MB","10 MB","100 MB","1 GB","The maximum size of a single document that can be indexed by Amazon Kendra is 50 MB."
"How can you ensure that sensitive data within documents is not indexed by Amazon Kendra?","By using data source exclusion patterns","By encrypting the entire index","By disabling indexing","By manually removing sensitive data","Data source exclusion patterns allow you to specify patterns to exclude certain documents or parts of documents from being indexed, helping to protect sensitive data."
"What is the primary function of an Amazon Kendra custom plugin?","To extend Kendra's functionality by adding support for new data sources or document types","To define user access permissions","To encrypt data in transit","To monitor indexing performance","Custom plugins allow you to extend Kendra's capabilities by integrating with data sources or document types that are not natively supported."
"How can you improve the speed of indexing in Amazon Kendra?","By increasing the number of indexing workers","By reducing the storage capacity","By disabling document encryption","By simplifying the search queries","Increasing the number of indexing workers allows Kendra to process more documents in parallel, improving indexing speed."
"What is the purpose of the 'DescribeThesaurus' API operation in Amazon Kendra?","To retrieve details about a specific thesaurus","To delete a thesaurus","To create a new thesaurus","To update a thesaurus","`DescribeThesaurus` provides detailed information about an existing thesaurus, such as its status, configuration, and other metadata."
"What type of documents can you upload via the BatchPutDocument API?","JSON and HTML","Images and Videos","Database backups","System logs","The BatchPutDocument API accepts JSON and HTML documents along with their metadata."
"What would be the impact of disabling query suggestions on an Amazon Kendra index?","End-users will no longer see suggested search terms as they type","Search relevance will be reduced","Indexing performance will be degraded","User access control will be disabled","Disabling query suggestions will prevent Kendra from providing real-time search term suggestions to users."
"What is the purpose of the 'RoleArn' parameter when creating an Amazon Kendra data source?","To specify the IAM role that Kendra assumes to access the data source","To define user access permissions","To encrypt data at rest","To manage the indexing schedule","The `RoleArn` parameter specifies the IAM role that Kendra uses to access the data source, ensuring that Kendra has the necessary permissions."
"If you are using Amazon Kendra in a multi-tenant environment, what feature can you use to isolate search results for each tenant?","User context filtering","Relevance tuning","Thesaurus management","Query expansion","User context filtering can be used to isolate search results for each tenant by restricting access to documents based on user identity and permissions."
"What is the purpose of defining exclusion patterns in Amazon Kendra data sources?","To prevent certain documents or parts of documents from being indexed","To encrypt data in transit","To manage user access permissions","To schedule data source synchronisation","Exclusion patterns allow you to specify patterns to exclude specific documents or parts of documents from being indexed, which can be useful for filtering out irrelevant or sensitive information."
"What is the best way to handle a large volume of documents in Amazon Kendra?","Use batch processing and incremental indexing","Use a smaller Kendra edition","Manually upload documents","Disable indexing for certain documents","Using batch processing to upload documents and incremental indexing to update the index ensures efficient handling of large document volumes."
"How can you improve the search experience for users who are not familiar with specific terminology used in your organisation?","By using a thesaurus to define synonyms for search terms","By encrypting data at rest","By managing user identities","By monitoring network traffic","A thesaurus can be used to define synonyms for search terms, allowing users to find relevant documents even if they use different terminology."
"What is the purpose of using facets in Amazon Kendra search results?","To allow users to filter search results based on specific criteria","To encrypt data at rest","To manage user identities","To monitor network traffic","Facets provide a way for users to refine their search results by filtering based on different attributes or categories."
"What is the main function of the Amazon Kendra 'Retrieve' API operation?","To retrieve answers to natural language questions","To retrieve documents","To query the index","To describe the index","The `Retrieve` API returns answers to natural language queries rather than only listing documents."
"What is the primary reason for using Amazon Kendra instead of a traditional search engine?","Kendra provides intelligent search powered by machine learning","Kendra offers unlimited storage","Kendra is free to use","Kendra requires no configuration","Kendra leverages machine learning to understand the context and meaning of search queries, providing more relevant and accurate results compared to traditional search engines."
"If your Amazon Kendra search relevance is low, what are the possible solutions you should investigate?","Relevance tuning and thesaurus creation","Increasing compute capacity","Changing data sources","Disabling user context filtering","Relevance tuning and thesaurus creation are the main actions to perform to enhance search relevance."
"Which feature allows Kendra to suggest refinements of a user's initial search query to narrow down results?","Query expansions","Featured Results","Thesaurus","Stop Words","Query expansions enable Kendra to automatically suggest related search queries to the user, helping them refine their search and find the desired information."
"In Amazon Kendra, what is the primary function of a data source?","To connect to and ingest content from various repositories","To define access control policies","To manage user queries","To visualise search analytics","Data sources are configured to connect to different repositories (e.g., S3, SharePoint) and ingest their content into the Kendra index."
"What is the purpose of 'Custom Document Enrichment' in Amazon Kendra?","To modify document content and metadata before indexing","To improve query latency","To enhance security","To create custom dashboards","Custom Document Enrichment allows you to modify the document content and metadata before it's indexed by Kendra, enabling you to add, update, or delete information."
"Which of the following is NOT a supported data source for Amazon Kendra out-of-the-box?","Amazon CloudWatch Logs","Amazon S3","SharePoint Online","Salesforce","Amazon CloudWatch Logs is not a supported data source for Amazon Kendra. Kendra supports S3, SharePoint Online and Salesforce."
"What is the role of the 'Index' in Amazon Kendra?","To store and search the processed content from data sources","To define user permissions","To configure data source connections","To monitor Kendra's performance","The index is where Kendra stores the processed content from your data sources, making it searchable by users."
"In Amazon Kendra, what does the term 'Facet' refer to?","A filterable attribute of the search results","A type of document","A query language","A custom connector","A facet is a filterable attribute of the search results, allowing users to narrow down their search based on specific criteria."
"How can you improve the relevance of search results in Amazon Kendra using the query suggestions feature?","By providing a list of query suggestions based on user search history","By manually editing the index content","By creating custom plugins","By changing the data source configuration","Query suggestions helps users find relevant information by suggesting queries based on their search history and the content within the index."
"Which Amazon Kendra API operation is used to submit a search query to an index?","Query","StartSearch","SearchIndex","FindDocument","The `Query` API operation is used to submit a search query to an Amazon Kendra index."
"What is the purpose of 'User Context Filtering' in Amazon Kendra?","To restrict search results based on user permissions","To improve query processing speed","To analyse user behaviour","To customise the user interface","User Context Filtering allows you to restrict search results based on the permissions associated with the user making the query."
"Which of the following actions requires a Kendra Enterprise edition subscription?","Using a limited number of data sources","Using custom data source connectors","Using the Kendra API","Using Kendra's console","Using custom data source connectors is a feature of the Kendra Enterprise edition subscription."
"In Amazon Kendra, what is a 'Crawler'?","A service that automatically discovers and indexes content in a data source","A tool for monitoring Kendra's performance","A type of user interface element","A security protocol","A crawler automatically discovers and indexes content within a specified data source."
"What is the primary purpose of the Amazon Kendra 'Experience Builder'?","To create a custom search interface for end-users","To configure data source connections","To define user roles and permissions","To visualise search analytics","The Experience Builder allows you to create a custom search interface for end-users, tailoring the search experience to your specific needs."
"How can you programmatically update documents already indexed in Amazon Kendra?","By using the 'UpdateDocuments' API operation","By deleting and re-uploading the documents","By manually editing the index","By modifying the data source and re-syncing","The `UpdateDocuments` API operation allows you to programmatically update documents that are already indexed in Amazon Kendra."
"Which of the following is a benefit of using Amazon Kendra's natural language processing capabilities?","Improved search relevance and understanding of user intent","Faster data ingestion","Enhanced data encryption","Simplified user authentication","Amazon Kendra's natural language processing capabilities improve search relevance by understanding the intent behind user queries."
"What type of content repository is best suited for a Kendra 'Web Crawler' data source?","Publicly accessible websites","Encrypted databases","Local file systems","Private networks","A Web Crawler data source is designed for indexing publicly accessible websites."
"What type of security is most important to configure before connecting Amazon Kendra to an S3 bucket?","IAM role with read permissions to the S3 bucket","Network ACLs","MFA","Data encryption","IAM roles with proper read permissions to the S3 bucket is important so that Kendra has access to ingest documents from the S3 bucket."
"Which of the following features allows you to add synonyms and acronyms to improve search accuracy in Amazon Kendra?","Custom Thesaurus","Crawler Configuration","User Context Filtering","Experience Builder","A Custom Thesaurus allows you to define synonyms and acronyms, improving search accuracy by helping Kendra understand different ways users might express the same query."
"What is the impact of increasing the 'Data Source Sync Frequency' in Amazon Kendra?","Kendra will update the index with the latest content more frequently","Kendra will process queries faster","Kendra will provide more accurate search analytics","Kendra will have more storage available","Increasing the data source sync frequency means Kendra will update the index with the latest content more frequently, ensuring search results are up-to-date."
"Which of the following is NOT a valid data type for a custom attribute in Amazon Kendra?","Boolean","Date","XML","String","XML is not a supported data type for custom attributes in Amazon Kendra. Supported data types include Boolean, Date, and String."
"What is the purpose of the 'Featured Results' feature in Amazon Kendra?","To promote specific documents to the top of search results for certain queries","To highlight keywords in search results","To provide suggestions for related searches","To create custom dashboards","Featured Results allows you to promote specific documents to the top of search results for certain queries, ensuring important information is easily accessible."
"How does Amazon Kendra handle access control for search results when using data sources like SharePoint or Confluence?","It uses the native access control lists (ACLs) defined in those systems","It requires a separate user directory for Kendra","It ignores existing access control and makes all content public","It relies on IP-based filtering","Amazon Kendra leverages the native access control lists (ACLs) defined in data sources like SharePoint or Confluence to ensure users only see results they are authorised to access."
"What is the advantage of using Amazon Kendra over a traditional keyword-based search engine?","Kendra uses natural language understanding to better interpret user queries","Kendra is cheaper to operate","Kendra requires less configuration","Kendra provides faster search results","Amazon Kendra uses natural language understanding to better interpret user queries, leading to more relevant and accurate search results."
"Which Amazon Kendra API operation is used to create a new index?","CreateIndex","CreateSearchIndex","StartIndex","NewIndex","The `CreateIndex` API operation is used to create a new Amazon Kendra index."
"How can you monitor the performance and usage of your Amazon Kendra deployment?","Using Amazon CloudWatch metrics and Kendra's built-in analytics dashboard","By manually reviewing log files","By using third-party monitoring tools","By running custom scripts on the Kendra index","Amazon CloudWatch metrics and Kendra's built-in analytics dashboard provide insights into the performance and usage of your Kendra deployment."
"What does the term 'Confidence Score' represent in Amazon Kendra search results?","The likelihood that a document is relevant to the query","The speed at which the search results were returned","The amount of storage used by the index","The number of times a document has been accessed","The confidence score represents the likelihood that a document is relevant to the query, helping users prioritise results."
"Which Amazon Kendra feature can be used to provide users with a guided search experience?","Query Suggestions","User Context Filtering","Featured Results","Crawler Configuration","Query Suggestions can be used to provide users with a guided search experience by suggesting relevant search terms as they type."
"When configuring a data source in Amazon Kendra, what is the purpose of specifying a 'Document Title Field'?","To identify the field in the document that should be used as the title in search results","To define the document's encryption key","To specify the document's author","To determine the document's language","Specifying a Document Title Field tells Kendra which field in the document should be used as the title in the search results, improving the user experience."
"What is the purpose of the 'Stop Word List' in Amazon Kendra?","To exclude common words (e.g., 'the', 'a', 'is') from being indexed","To prevent specific users from accessing the search index","To encrypt sensitive data","To automatically translate documents","The Stop Word List is used to exclude common words (e.g., 'the', 'a', 'is') from being indexed, improving search relevance by focusing on more meaningful terms."
"How can you programmatically delete documents from an Amazon Kendra index?","By using the 'DeleteDocuments' API operation","By modifying the data source and re-syncing","By manually editing the index","By using the 'RemoveDocument' API operation","The `DeleteDocuments` API operation allows you to programmatically delete documents from an Amazon Kendra index."
"What is the purpose of using AWS Lambda functions in conjunction with Amazon Kendra?","To perform custom document enrichment and pre-processing","To monitor Kendra's performance","To create custom connectors","To manage user permissions","AWS Lambda functions can be used to perform custom document enrichment and pre-processing, allowing you to modify document content and metadata before it's indexed."
"Which of the following is a key benefit of using the Amazon Kendra API?","Automating index creation, data source management, and query submission","Reduced cost","Improved security","Faster search results","The Amazon Kendra API allows you to automate various tasks, including index creation, data source management, and query submission."
"What does the 'Fuzzy Matching' feature in Amazon Kendra do?","Finds documents that are similar to the search query, even if there are spelling errors or slight variations","Allows search results to be sorted","Allows users to provide feedback on the relevance of documents","It limits which documents are returned","Fuzzy matching allows Kendra to find documents that are similar to the search query, even if there are spelling errors or slight variations."
"Which of the following is the recommended approach for indexing content from a custom application in Amazon Kendra?","Developing a custom connector","Using the Web Crawler data source","Using the S3 data source","Using the Database data source","Developing a custom connector is the recommended approach for indexing content from a custom application, allowing you to integrate Kendra with your specific data source."
"What is the maximum number of documents that can be indexed in Amazon Kendra (Enterprise Edition)?","Millions","Thousands","Hundreds","Tens","The Enterprise Edition of Kendra supports indexing millions of documents. The Developer Edition has a smaller limit."
"What is the purpose of 'Relevance Tuning' in Amazon Kendra?","To adjust the ranking of search results based on specific criteria","To improve query latency","To enhance security","To create custom dashboards","Relevance Tuning allows you to adjust the ranking of search results based on specific criteria, ensuring the most relevant documents appear at the top."
"What is the function of the Amazon Kendra 'SubmitFeedback' API?","To allow users to provide feedback on the relevance of search results","To report errors in the Kendra service","To request new features","To configure data source connections","The `SubmitFeedback` API allows users to provide feedback on the relevance of search results, helping Kendra learn and improve its accuracy."
"Which factor does NOT directly impact Amazon Kendra's pricing?","Number of queries","Amount of indexed data","Number of users","Data Source Type","Number of users does not directly impact Kendra pricing."
"What is the purpose of setting up a 'Sync Job' in an Amazon Kendra data source configuration?","To schedule the periodic indexing of data from the data source","To define user permissions","To configure data source connections","To monitor Kendra's performance","A Sync Job schedules the periodic indexing of data from the data source, ensuring that Kendra's index is up-to-date."
"Which Amazon Kendra feature helps in discovering related content beyond the initial search results?","Suggestions","Featured Results","Custom Vocabulary","Relevance tuning","Suggestions helps users to find related content beyond the initial search results."
"When configuring an Amazon Kendra index, what does the 'edition' setting determine?","The features and pricing tier of the index","The region where the index is located","The data sources that can be used with the index","The number of users who can access the index","The 'edition' setting determines the features and pricing tier of the index, with options like Developer and Enterprise."
"What can you use to create a searchable FAQ (Frequently Asked Questions) resource in Amazon Kendra?","Extract the FAQ and load them as a custom Kendra data source","Import a JSON file containing the questions and answers into Kendra","You cannot directly ingest FAQ data into Kendra","Build a custom connector","You can import a JSON file containing the questions and answers into Kendra and it will be searchable."
"You have an Amazon Kendra index configured to ingest content from a website using the Web Crawler data source. After a recent website redesign, some documents are no longer being indexed. What is the most likely cause?","The website's robots.txt file is blocking the crawler","Kendra's cache is full","The website's SSL certificate has expired","A DNS server is down","The website's robots.txt file is likely blocking the crawler from indexing certain pages after the redesign."
"A developer is integrating Amazon Kendra with a web application and needs to programmatically retrieve the user context information for the current user. Which method should they use?","Leverage the Kendra user context filtering API","Use the IAM role","Retrieve the information from the web application's user management system","The Amazon Cognito User Pools","The developer should retrieve the user information from the web application's user management system, then pass it to Kendra's query API."
"What feature of Kendra allows the user to promote specific results to the top of the list for specific search terms?","Featured Results","Query Suggestions","Result Rankings","Custom Vocabulary","The Featured Results functionality is a feature that allows you to promote specific documents to the top of the list for specific search terms."
"When setting up an Amazon Kendra data source for a database, what type of information is required?","Database connection details, table names, and column mappings","Database user credentials and storage capacity","Database encryption keys","Database firewall rules","Database connection details, table names, and column mappings are all required when setting up a data source for a database."
"You want to provide different search results for users based on their department. How would you implement this in Kendra?","Use User Context Filtering","Create multiple indexes","Implement custom scoring logic","Use different data sources for each department","User Context Filtering enables you to tailor search results based on user attributes, such as department."
"Which AWS service can be integrated with Amazon Kendra to provide user authentication and authorisation for accessing the search index?","Amazon Cognito","AWS IAM","AWS Directory Service","AWS Single Sign-On","Amazon Cognito can be integrated with Amazon Kendra to provide user authentication and authorization."
"What happens when a document is updated in a data source that is connected to Amazon Kendra?","Kendra automatically re-indexes the document during the next sync","The document is removed from the index","The document is marked as outdated","Kendra only indexes documents when they are created","Amazon Kendra automatically re-indexes updated documents during the next sync."