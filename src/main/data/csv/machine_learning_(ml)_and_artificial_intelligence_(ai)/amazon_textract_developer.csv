"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Amazon Textract?","To extract text and data from scanned documents and images","To translate text from one language to another","To analyse sentiment in social media posts","To manage and organise digital assets","Textract is designed to automatically extract text and data from scanned documents and images, including tables and forms."
"Which type of data can Amazon Textract extract?","Text, tables, and forms","Audio and video content","Financial market data","GPS coordinates","Textract is capable of extracting structured data, like tables and forms, in addition to regular text."
"What is the Amazon Textract feature that detects key-value pairs in documents called?","Forms","Queries","Tables","Signatures","The Forms feature is designed to detect and extract key-value pairs from form-based documents."
"Which Amazon Textract API operation is designed to process documents in an asynchronous manner?","StartDocumentAnalysis","DetectDocumentText","AnalyzeExpense","DetectDocumentTextDetection","StartDocumentAnalysis is designed for asynchronous processing, which is ideal for large documents."
"Which Amazon Textract API operation is used to extract text only, without any structured data?","DetectDocumentText","AnalyzeDocument","AnalyzeExpense","StartDocumentTextDetection","DetectDocumentText extracts only the raw text from a document, without attempting to understand the document structure."
"What file formats does Amazon Textract support as input?","PDF, JPEG, PNG, TIFF","MP3, WAV, AAC","CSV, XLSX","HTML, XML","Textract supports common image and document formats like PDF, JPEG, PNG, and TIFF."
"Which Amazon Textract feature can extract specific information from invoices and receipts?","AnalyzeExpense","AnalyzeID","StartDocumentTextDetection","DetectDocumentText","AnalyzeExpense is tailored to extract information such as vendor name, invoice number, and line items from invoices and receipts."
"What is the purpose of using Amazon Textract's OCR (Optical Character Recognition) technology?","To convert images of text into machine-readable text","To translate text into different languages","To summarise large text documents","To encrypt sensitive data within documents","OCR technology enables the conversion of images of text, like scanned documents, into text that a computer can understand and process."
"Which Amazon Textract API is used for synchronous analysis of a document and immediately returns the results?","AnalyzeDocument","StartDocumentAnalysis","StartDocumentTextDetection","GetDocumentAnalysis","AnalyzeDocument provides synchronous analysis of a document."
"What does the 'Block' object in Amazon Textract represent?","A unit of detected content, such as a word, line, or table","A security permission setting","A storage location for processed documents","A user account within the system","A Block is the fundamental unit of detected content returned by Textract, representing elements like words, lines, or tables."
"Which confidence score does Amazon Textract provide for extracted information?","A score representing the accuracy of the extracted text and data","A score representing the security level of the document","A score representing the processing time","A score representing the file size","Textract provides a confidence score indicating the accuracy of the extracted text and data, helping users assess the reliability of the results."
"In Amazon Textract, what is the purpose of the 'Geometry' object within a 'Block'?","To provide the bounding box coordinates of the detected text or data","To define the font size and style of the text","To specify the language of the text","To determine the storage location of the document","The Geometry object provides the location of the text or data within the document image, represented by a bounding box."
"Which Amazon Textract feature helps identify and extract data from identity documents like passports and driving licences?","AnalyzeID","AnalyzeExpense","DetectDocumentText","StartDocumentTextDetection","AnalyzeID is specifically designed to extract information from identity documents."
"What information can be extracted using Amazon Textract's 'Tables' feature?","Structured data organised in rows and columns","Handwritten signatures","Embedded images","Audio transcriptions","The 'Tables' feature extracts structured data from tables, preserving the row and column relationships."
"Which AWS service is commonly used in conjunction with Amazon Textract to store and process the extracted data?","Amazon S3","Amazon CloudFront","Amazon Route 53","Amazon Glacier","Amazon S3 is frequently used to store the original documents and the extracted data from Textract."
"How does Amazon Textract handle handwritten text?","It can extract handwritten text with varying degrees of accuracy","It converts handwritten text to typed text","It ignores handwritten text","It only supports perfectly legible handwriting","Textract can extract handwritten text, but the accuracy may vary depending on the legibility of the handwriting."
"Which Amazon Textract API operation would be most suitable for analysing a large batch of scanned documents in a cost-effective manner?","StartDocumentAnalysis (asynchronous)","AnalyzeDocument (synchronous)","DetectDocumentText (synchronous)","AnalyzeExpense (synchronous)","The asynchronous StartDocumentAnalysis is most cost-effective for large batches as it allows for parallel processing and avoids API timeouts."
"What is the benefit of using Amazon Textract with AWS Lambda?","To automate document processing workflows","To monitor network traffic","To manage user identities","To deploy machine learning models","Textract can be integrated with Lambda to create automated document processing workflows, such as extracting data upon document upload."
"What is the role of 'Relationship Types' in Amazon Textract Block objects?","To define the relationship between different blocks of text and data","To define the security permissions for accessing the blocks","To define the storage location of the blocks","To define the data type of the blocks","Relationship Types define how different blocks of extracted content are related, such as a table cell being related to a table."
"Which of the following is a typical use case for Amazon Textract in the healthcare industry?","Automating the extraction of data from patient records","Analysing medical images","Predicting patient outcomes","Managing hospital inventory","Textract can automate the extraction of information from patient records, improving efficiency and accuracy."
"Which pricing model does Amazon Textract use?","Pay-per-use based on the number of pages processed","Flat monthly fee","Free for limited use","Pay-per-API call, regardless of page count","Textract uses a pay-per-use model, charging based on the number of pages processed by the service."
"What security measures should be implemented when using Amazon Textract with sensitive data?","Encrypting data at rest and in transit","Enabling multi-factor authentication for all users","Regularly auditing network traffic","Limiting the size of documents processed","Data encryption, both at rest and in transit, is crucial for securing sensitive data processed by Textract."
"Which Amazon Textract API is designed to extract data from structured forms such as applications or questionnaires?","AnalyzeDocument with Forms feature","DetectDocumentText","AnalyzeExpense","StartDocumentTextDetection","AnalyzeDocument with Forms feature is designed to handle structured forms with key-value pairs."
"What type of data can Amazon Textract not reliably extract?","Distorted or very low-resolution images","Clearly printed text","Data from standard forms","Information from ID cards","Textract struggles with highly distorted or low-resolution images where the text is difficult to discern."
"What is the primary benefit of using Amazon Textract over manually extracting data from documents?","Increased accuracy and reduced processing time","Lower cost","Better data visualisation","Improved document storage","Textract provides higher accuracy and significantly reduces the time required compared to manual data extraction."
"When using the asynchronous Amazon Textract APIs (StartDocumentAnalysis), how do you retrieve the results?","Using the GetDocumentAnalysis API","The results are automatically emailed to you","The results are stored in an S3 bucket specified during document upload","Using the AnalyzeDocument API","The GetDocumentAnalysis API is used to retrieve the results of an asynchronous document analysis job."
"What level of access control should be applied when using Amazon Textract with S3 buckets containing sensitive documents?","Least privilege principle","Full administrative access","Public read access","Shared access with all AWS accounts","The principle of least privilege should be applied, granting only necessary access to the S3 bucket."
"What is the purpose of the 'Feature Types' parameter in the AnalyzeDocument API?","To specify which types of data to extract (e.g., TABLES, FORMS)","To specify the font style of the extracted text","To specify the language of the document","To specify the storage location for extracted data","The Feature Types parameter allows you to specify the types of data you want to extract from the document, such as tables or forms."
"What is the relationship between Amazon Textract and Amazon Comprehend?","Textract extracts the text, and Comprehend can analyse the text for sentiment or entities","Comprehend extracts the text, and Textract analyzes the text for sentiment or entities","They both perform OCR on documents","They both translate text into different languages","Textract extracts the text from documents, and then services like Comprehend can be used to analyse the extracted text for insights."
"What kind of pre-processing might be necessary to improve Amazon Textract's accuracy on scanned documents?","Deskewing and image enhancement","Text translation","Data encryption","File compression","Deskewing (straightening) and image enhancement can improve the quality of the document image, thus improving Textract's accuracy."
"If you are working with a multi-page document, how does Amazon Textract handle page numbering?","It provides page numbers within the Block objects for each page","It only processes the first page of the document","It ignores page numbers","It requires manual input of page numbers","Textract provides page numbers in the Block objects, allowing you to understand the structure of multi-page documents."
"What is a key consideration when choosing between synchronous and asynchronous Amazon Textract APIs?","The size and complexity of the document","The cost of the service","The availability of AWS support","The programming language used","Synchronous APIs are suitable for smaller documents and quick results, while asynchronous APIs are best for larger, more complex documents."
"How can you monitor the performance and usage of Amazon Textract in your AWS account?","Using Amazon CloudWatch metrics","Using AWS Trusted Advisor","Using AWS Cost Explorer","Using AWS Systems Manager","Amazon CloudWatch provides metrics for monitoring the performance and usage of Textract, allowing you to track its activity."
"What is the main difference between the `AnalyzeDocument` and `DetectDocumentText` Textract APIs?","`AnalyzeDocument` extracts structured data and text, while `DetectDocumentText` extracts only text","`AnalyzeDocument` is for synchronous processing, while `DetectDocumentText` is for asynchronous processing","`AnalyzeDocument` is more expensive than `DetectDocumentText`","`AnalyzeDocument` requires more IAM permissions than `DetectDocumentText`","`AnalyzeDocument` provides structured information like forms and tables in addition to text, whereas `DetectDocumentText` only returns extracted text."
"When should you consider using Amazon Textract Custom Vocabulary?","When your documents contain domain-specific terminology or acronyms not recognised by Textract's default vocabulary","When you want to translate the extracted text into another language","When you need to encrypt the extracted data","When you need to compress the documents before processing","Custom Vocabulary can enhance accuracy for domain-specific terms."
"Which AWS service can be used to orchestrate a workflow involving Amazon Textract, data validation, and storage?","AWS Step Functions","AWS Glue","AWS Lambda","AWS CloudTrail","Step Functions allows you to visually design and orchestrate complex workflows."
"What is the recommended approach for handling Personally Identifiable Information (PII) extracted by Amazon Textract?","Mask or redact the PII using another service like AWS Comprehend or custom code","Store the PII in plain text in an S3 bucket","Share the PII with third-party analytics providers","Ignore the PII and focus on extracting other data","Masking or redacting PII is crucial to protect sensitive information."
"What is the purpose of the 'Query' feature in Amazon Textract?","To extract specific information from a document by asking a direct question","To search for keywords within a document","To analyse the sentiment of the document","To translate the document into a different language","The Query feature allows you to ask a specific question and have Textract extract the answer from the document."
"Which of the following is a benefit of using Amazon Textract's serverless architecture?","Scalability and cost-effectiveness","Increased security","Improved control over hardware","Reduced complexity in data visualisation","Serverless architectures provide scalability and cost-effectiveness by automatically scaling resources as needed."
"How can you improve the accuracy of Amazon Textract when processing documents with varying layouts?","By pre-processing the documents to standardize the layout","By using a more powerful AWS EC2 instance","By increasing the timeout period for the API calls","By using a different OCR engine","Standardising the layout before processing can significantly improve Textract's accuracy."
"What is the purpose of the BlockType 'CELL' in Amazon Textract?","It represents a cell within a table","It represents the title of the document","It represents a block of text","It represents an image","The CELL BlockType is used to represent individual cells within a table structure."
"You want to extract specific fields from a large number of invoices, but the layout varies between vendors. Which Amazon Textract feature would be most suitable?","Amazon Textract Custom Queries","Amazon Textract AnalyzeExpense API","Amazon Textract DetectDocumentText API","Amazon Textract AnalyzeID API","Custom Queries allow you to define the specific information you need, regardless of layout variations."
"What is the maximum file size supported by the Amazon Textract synchronous APIs?","10MB","500MB","1GB","5GB","The maximum file size supported by Amazon Textract synchronous APIs is 10MB."
"Which of the following actions can help reduce the cost of using Amazon Textract?","Optimising image quality before processing","Using smaller EC2 instances","Enabling CloudTrail logging","Disabling data encryption","Optimising image quality can reduce the processing time and therefore cost."
"What is the function of the Amazon Textract GetDocumentAnalysis API?","Retrieves the results of an asynchronous document analysis operation","Starts an asynchronous document analysis operation","Performs a synchronous document analysis operation","Deletes a document from the Textract service","The GetDocumentAnalysis API is used to retrieve the results of an asynchronous analysis."
"Which of the following is a typical use case for integrating Amazon Textract with Robotic Process Automation (RPA) tools?","Automating data entry from scanned documents into business applications","Automating the deployment of machine learning models","Automating the management of AWS infrastructure","Automating the monitoring of network traffic","Textract can be integrated with RPA to automate data entry."
"How does Amazon Textract contribute to improving compliance in industries like finance and healthcare?","By providing an audit trail of document processing activities","By automatically encrypting data at rest","By preventing unauthorised access to documents","By automatically translating documents into multiple languages","Textract provides an audit trail of document processing, supporting compliance requirements."
"What is the impact of document skew on Amazon Textract accuracy?","Skewed documents can reduce the accuracy of text and data extraction","Skewed documents improve the accuracy of handwriting recognition","Skewed documents have no impact on Textract's accuracy","Skewed documents only affect the speed of processing","Document skew can hinder Textract's ability to accurately extract information."
"When using Amazon Textract, how would you handle a scenario where some documents are handwritten, and others are typed?","Use separate Textract API calls with optimised configurations for each type","Convert all documents to typed text before processing","Only process the typed documents","Use a single Textract API call with default settings","Processing handwritten and typed documents with separate API calls with tailored configurations would provide optimal accuracy for each type."
"Which service can be used to store and version control the custom vocabularies used by Amazon Textract?","Amazon S3","Amazon CloudFront","Amazon RDS","Amazon EC2","S3 is suitable for storing objects like vocabulary files."
"What is the primary purpose of using Amazon Textract with AWS CloudTrail?","To track and monitor Textract API calls for auditing and security purposes","To optimise the performance of Textract","To reduce the cost of using Textract","To improve the accuracy of text extraction","CloudTrail tracks API calls, which is important for auditing and security compliance."
"How can you ensure the security of data processed by Amazon Textract?","By encrypting data at rest and in transit, using IAM roles with least privilege, and monitoring API calls with CloudTrail","By using a strong password for your AWS account","By regularly backing up your data","By limiting the size of documents processed by Textract","Data encryption, IAM roles and CloudTrail are essential for securing data."
"What is the primary purpose of Amazon Textract?","To extract text and data from scanned documents.","To translate text between different languages.","To analyse sentiment in text.","To generate text from images.","Textract is designed to extract text and data from scanned documents and images, enabling automation and data processing."
"Which type of document processing is Amazon Textract primarily used for?","Optical Character Recognition (OCR)","Natural Language Processing (NLP)","Machine Translation","Speech Recognition","Textract focuses on OCR to extract text from documents."
"Which Textract feature allows you to extract structured data, such as forms and tables?","Layout Analysis","Optical Character Recognition","Sentiment Analysis","Language Detection","Layout Analysis identifies and extracts structured data from forms and tables."
"What is the purpose of the 'Detect Document Text' API in Amazon Textract?","To extract all text from a document.","To classify the document type.","To identify faces in the document.","To translate the document into another language.","The 'Detect Document Text' API extracts all lines and words of text from a document."
"Which Amazon Textract API is specifically designed for extracting data from forms?","Analyze Document","Detect Document Text","Analyze Expense","Translate Document","The 'Analyze Document' API is used for extracting structured data from documents, including forms."
"What type of data can be extracted using Amazon Textract's 'Analyze Expense' API?","Invoice and receipt data","Social media posts","Audio transcriptions","Video metadata","The 'Analyze Expense' API specifically extracts data from invoices and receipts, such as vendor name, amounts, and dates."
"Which of the following file formats are supported as input for Amazon Textract?","PDF, JPEG, PNG","MP3, WAV, AVI","DOCX, PPTX, XLSX","HTML, XML, JSON","Textract supports PDF, JPEG, and PNG file formats for document analysis."
"What is the main advantage of using Amazon Textract over traditional OCR software?","Improved accuracy and machine learning-based features","Lower cost of implementation","Faster processing speed","Simpler user interface","Textract utilizes machine learning to improve accuracy and offer features like layout analysis and form extraction compared to traditional OCR."
"Which AWS service is often used in conjunction with Amazon Textract for storing and processing the extracted data?","Amazon S3","Amazon EC2","Amazon RDS","Amazon CloudFront","Amazon S3 is commonly used to store the input documents and the extracted data output from Textract."
"What level of access control can be applied to Amazon Textract?","AWS Identity and Access Management (IAM)","Amazon Cognito","AWS Organizations","Amazon Cloud Directory","AWS Identity and Access Management (IAM) controls access to Textract resources and API calls."
"Which Amazon Textract feature can automatically detect and extract key-value pairs from forms?","Key-Value Pair Extraction","Text Detection","Table Detection","Signature Detection","Key-Value Pair Extraction automatically identifies and extracts key-value pairs from forms."
"Which Amazon Textract API operation can be used to extract data from tables within a document?","Analyze Document","Detect Document Text","Translate Document","Analyze Sentiment","The 'Analyze Document' API operation can be used to detect and extract data from tables."
"What kind of confidence score does Amazon Textract provide for extracted data?","A score indicating the accuracy of the extracted information.","A score indicating the processing speed.","A score indicating the file size.","A score indicating the user satisfaction.","Textract provides a confidence score for each extracted piece of information, indicating the accuracy of the extraction."
"How can Amazon Textract be used in automating invoice processing?","By extracting data such as invoice number, date, and amount due.","By generating invoices automatically.","By sending invoices to customers.","By tracking invoice payments.","Textract extracts data such as invoice number, date, and amount due from invoices, enabling automated processing."
"Which Amazon Textract feature helps to identify and extract printed and handwritten text?","Optical Character Recognition","Facial Recognition","Voice Recognition","Object Detection","Textract uses OCR to identify and extract both printed and handwritten text."
"What type of encryption does Amazon Textract use to protect data at rest?","Server-Side Encryption (SSE) with KMS-managed keys","Client-Side Encryption","No encryption","Data masking","Textract uses Server-Side Encryption (SSE) with KMS-managed keys to encrypt data at rest."
"Which AWS service is often used with Amazon Textract to orchestrate complex workflows?","AWS Step Functions","Amazon SQS","Amazon SNS","AWS Lambda","AWS Step Functions is often used to orchestrate complex workflows involving Textract and other services."
"What is the purpose of synchronous APIs in Amazon Textract?","Processing small documents in real-time.","Processing large documents in batches.","Scheduling tasks for later execution.","Storing data for long-term archiving.","Synchronous APIs are used for processing smaller documents where you need the results immediately."
"What is the purpose of asynchronous APIs in Amazon Textract?","Processing large documents in batches.","Processing small documents in real-time.","Scheduling tasks for immediate execution.","Storing data for short-term caching.","Asynchronous APIs are designed for processing larger documents in batches where you don't need the results immediately."
"Which of the following is a use case for Amazon Textract in the healthcare industry?","Extracting patient information from medical records.","Generating prescriptions.","Scheduling appointments.","Managing insurance claims.","Textract can extract patient information from medical records to automate data entry and processing."
"Which of the following is a use case for Amazon Textract in the financial services industry?","Automating loan application processing.","Providing financial advice.","Managing investment portfolios.","Generating financial reports.","Textract can automate the extraction of data from loan applications and other financial documents."
"What is the pricing model for Amazon Textract?","Pay-per-page analyzed.","Fixed monthly fee.","Pay-per-hour of processing.","Pay-per-API call.","Textract's pricing is based on the number of pages analyzed, with separate pricing for different API operations."
"How can you integrate Amazon Textract with other AWS services to build a serverless application?","Using AWS Lambda functions to trigger Textract.","Using Amazon EC2 instances to run Textract.","Using Amazon RDS to store Textract results.","Using Amazon CloudFront to cache Textract data.","AWS Lambda functions can be used to trigger Textract jobs and process the results in a serverless architecture."
"What is the purpose of Amazon Textract's 'Analyze ID' API?","Extracting information from identification documents.","Analysing images of products.","Analysing customer feedback.","Analysing code.","The 'Analyze ID' API is designed for extracting information from identification documents like driver's licenses and passports."
"What can be used to manage and control access to Amazon Textract resources?","AWS Identity and Access Management (IAM) policies.","AWS CloudTrail logs.","Amazon CloudWatch metrics.","AWS Config rules.","IAM policies are used to manage and control access to Textract resources, specifying which users or roles have permission to perform actions."
"Which of the following is NOT a feature of Amazon Textract?","Video analysis.","Table detection.","Form extraction.","Key-value pair extraction.","Textract focuses on document analysis and does not support video analysis."
"How can you monitor the performance of Amazon Textract using AWS services?","Using Amazon CloudWatch metrics.","Using AWS Config rules.","Using AWS CloudTrail logs.","Using Amazon Inspector.","Amazon CloudWatch metrics provide insights into Textract's performance, such as API usage and error rates."
"What type of machine learning models does Amazon Textract use?","Optical Character Recognition (OCR) and Natural Language Processing (NLP)","Speech recognition and text-to-speech","Image recognition and object detection","Time series forecasting and regression analysis","Textract uses OCR to recognize text and NLP to understand the structure and relationships within documents."
"What is the maximum document size supported by Amazon Textract's synchronous API?","10MB","100MB","1GB","5GB","The maximum document size supported by the synchronous API is 10MB."
"What is the recommended way to process a very large document using Amazon Textract?","Use the asynchronous API to process the document in chunks.","Use the synchronous API to process the document in real-time.","Split the document manually into smaller parts and process them individually using the synchronous API.","Compress the document to reduce its size before processing it with the synchronous API.","For very large documents, the asynchronous API allows you to process the document in chunks, making it more manageable."
"Which Amazon Textract feature allows you to correct errors in the extracted text?","Human review using Amazon Augmented AI (A2I)","Automated error correction using machine learning","Manual correction directly in the Textract API","Real-time error feedback from users","Amazon Augmented AI (A2I) allows you to implement human review workflows to correct errors in the extracted text."
"What is the key benefit of using Amazon Textract for data extraction compliance?","Improved accuracy and consistency.","Reduced manual effort.","Enhanced security.","All of the above.","Using Textract for data extraction compliance leads to improved accuracy, reduced manual effort, and enhanced security, ensuring adherence to regulatory requirements."
"What data residency options are available when using Amazon Textract?","Data can be processed and stored in the AWS region of your choice.","Data is always processed in the US East (N. Virginia) region.","Data is processed in a random AWS region.","Data is processed in the EU (Ireland) region only.","Textract allows you to choose the AWS region where your data is processed and stored, ensuring data residency compliance."
"Which is NOT a typical use case of Amazon Textract?","Analysing video content","Processing invoices and receipts","Extracting data from forms","Analysing scanned documents","Textract is designed for document processing and doesn't analyse video content."
"Which Textract operation provides information about the location of each character, word, and line of text detected in a document?","GetDocumentAnalysis","DetectDocumentText","AnalyzeID","AnalyzeExpense","`GetDocumentAnalysis` provides detailed location information for each detected element."
"What does Amazon Textract use to process data in a secure manner?","Encryption at rest and in transit","Publicly accessible storage","Unencrypted connections","Storing data without any retention policies","Textract ensures data security by encrypting data both at rest and in transit."
"Which is the most appropriate use case for the AnalyzeExpense API?","Extracting data from receipts","Extracting data from bank statements","Extracting data from social media posts","Extracting data from medical records","The AnalyzeExpense API is specifically designed for extracting data from expense receipts."
"Which AWS service allows for integrating human review of Textract results?","Amazon Augmented AI (A2I)","Amazon SageMaker","Amazon Comprehend","Amazon Rekognition","Amazon Augmented AI (A2I) provides the capability to integrate human review of Textract's output, improving accuracy."
"What is the role of the 'Block' object in Amazon Textract's output?","Represents different elements in a document like words, lines, and tables.","Represents the storage unit in Amazon S3.","Represents a permission setting in IAM.","Represents a machine learning model in SageMaker.","The 'Block' object represents various elements within a document, such as words, lines, and tables."
"For what kind of document is the Amazon Textract 'AnalyzeID' API designed?","Government-issued identification documents","Handwritten notes","Business reports","Website content","The 'AnalyzeID' API extracts information from government-issued identification documents."
"What can be done if Amazon Textract's automatic table extraction is not perfect?","Use Amazon Augmented AI (A2I) for human review and correction","Ignore the incorrect results","Switch to a different OCR service","Manually rewrite the entire document","Amazon Augmented AI (A2I) can be used to enable human review and correction of the extracted table data, improving accuracy."
"Which Amazon Textract operation would you use to extract all of the raw text without structure from a scanned document?","DetectDocumentText","AnalyzeDocument","AnalyzeExpense","AnalyzeID","The `DetectDocumentText` operation extracts all text without any structural understanding."
"What type of data can you NOT extract using Amazon Textract?","Audio data","Text from tables","Key-value pairs from forms","Text from images","Amazon Textract is designed for document text and data, and cannot extract audio data."
"What is the main purpose of using Amazon Textract's Layout feature?","To identify and extract structured information such as tables and forms","To perform sentiment analysis on text","To translate the document into another language","To identify faces in the document","The Layout feature is used to identify structured information like tables and forms within documents."
"What is the best way to process many documents periodically using Amazon Textract?","Using asynchronous APIs with SQS queues to manage the tasks.","Using synchronous APIs to process each document one by one.","Using Amazon EC2 instances to run Textract.","Using a local desktop application to process the documents.","Asynchronous APIs paired with SQS queues help manage the processing of large numbers of documents over time."
"How do you control the cost of using Amazon Textract?","Monitor your usage with CloudWatch and optimize your document processing workflows.","Disable Textract during off-peak hours.","Use a lower performance tier.","Delete all your documents after processing.","Monitoring usage with CloudWatch allows you to identify areas for optimization and control costs."
"When extracting data from a document with handwriting, what should you consider when using Amazon Textract?","The handwriting quality can affect the accuracy of the extraction.","The handwriting style is not relevant.","Amazon Textract automatically improves handwriting quality before extraction.","Handwriting is always perfectly extracted.","Handwriting quality significantly impacts the accuracy of Textract, so cleaner handwriting leads to better results."
