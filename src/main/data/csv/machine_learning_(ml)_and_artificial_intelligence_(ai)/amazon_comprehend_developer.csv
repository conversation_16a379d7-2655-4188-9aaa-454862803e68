"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Comprehend, what type of analysis identifies people, places, organisations, and events in a text?","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","Language Detection","Entity Recognition identifies entities such as people, places, organisations, and events."
"Which Amazon Comprehend feature provides insights into how positive or negative a piece of text is?","Sentiment Analysis","Topic Modelling","Syntax Analysis","Custom Classification","Sentiment Analysis determines the overall sentiment of a text, whether positive, negative, neutral, or mixed."
"What is the primary function of Key Phrase Extraction in Amazon Comprehend?","To identify the most important topics discussed in the text","To translate text from one language to another","To detect the language of the text","To identify the parts of speech in the text","Key Phrase Extraction identifies the most important phrases in the text, offering a quick summary of its main topics."
"What is the purpose of Custom Classification in Amazon Comprehend?","To create custom categories for classifying documents","To identify the dominant language in a document","To perform real-time translation","To redact sensitive information","Custom Classification allows you to train Comprehend with your own data to classify documents according to categories specific to your business or domain."
"Which Amazon Comprehend feature can determine the grammatical structure of a text?","Syntax Analysis","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","Syntax Analysis analyses the grammatical structure of the text, identifying parts of speech and their relationships."
"Which Amazon Comprehend feature allows you to identify the language of a document?","Language Detection","Key Phrase Extraction","Syntax Analysis","Sentiment Analysis","Language Detection automatically identifies the predominant language of a document."
"What is the purpose of Topic Modelling in Amazon Comprehend?","To discover hidden topics within a collection of documents","To identify the sentiment expressed in a document","To translate the document into multiple languages","To correct grammatical errors in a document","Topic Modelling identifies the main themes or subjects discussed in a collection of documents without prior training or labelled data."
"Which Amazon Comprehend API allows you to analyse a batch of documents asynchronously?","StartEntitiesDetectionJob","DetectEntities","DetectSentiment","DetectKeyPhrases","StartEntitiesDetectionJob allows you to process a large number of documents asynchronously to detect entities."
"What is the maximum size of a document that can be processed by the synchronous Amazon Comprehend APIs?","5 KB","10 MB","1 MB","10 KB","The maximum size of a document for synchronous APIs like DetectEntities or DetectSentiment is 5 KB."
"Which Amazon Comprehend feature can be used to identify personally identifiable information (PII) in a document?","PII Redaction","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","PII Redaction is specifically designed to identify and redact PII data in a document, helping you protect sensitive information."
"What is the purpose of the 'Confidence Score' returned by Amazon Comprehend?","To indicate the accuracy of the analysis","To rank documents by importance","To show the processing speed","To measure the size of the document","The Confidence Score represents the level of certainty that Comprehend has in its analysis, such as the accuracy of an identified entity or the sentiment of a text."
"Which Amazon Comprehend feature requires training data to be provided by the user?","Custom Entity Recognition","Language Detection","Sentiment Analysis","Key Phrase Extraction","Custom Entity Recognition needs labelled data to train the model to recognise entities specific to your use case."
"What is the benefit of using Amazon Comprehend Medical?","It can identify medical conditions, medications, and dosages in unstructured text","It can translate medical documents into multiple languages","It can summarise medical research papers","It can predict patient outcomes based on medical history","Amazon Comprehend Medical is designed to extract medical information from unstructured text, such as medical notes and patient records."
"Which AWS service can be used to orchestrate a workflow that uses Amazon Comprehend to process documents?","AWS Step Functions","Amazon SQS","Amazon SNS","AWS Lambda","AWS Step Functions allows you to create visual workflows that can integrate with Amazon Comprehend to process documents in a structured manner."
"What is the pricing model for Amazon Comprehend?","Pay-as-you-go based on the amount of text processed","Fixed monthly fee","Annual subscription","Free tier with limited usage","Amazon Comprehend uses a pay-as-you-go pricing model based on the amount of text processed by the service."
"Which of these is an example of a real-world application of Amazon Comprehend sentiment analysis?","Analysing customer feedback to understand brand perception","Detecting network intrusions","Transcribing audio files","Generating code documentation","Sentiment analysis is often used to gauge customer opinions and feelings towards products, services, or brands by analysing their feedback."
"What type of input does Amazon Comprehend typically accept?","Unstructured text data","Structured data in a database","Image files","Video files","Amazon Comprehend is designed to analyse unstructured text data."
"When using custom entity recognition in Amazon Comprehend, what format should your training data be in?","A CSV or JSON file with annotated entities","A PDF document with highlighted entities","An audio file with spoken entities","An image file with labelled entities","Custom entity recognition requires a CSV or JSON file that annotates the entities you want to detect within your training documents."
"What is the purpose of the 'Offset' value returned by Amazon Comprehend when detecting entities?","To indicate the start and end positions of the entity in the text","To show the confidence score of the entity","To indicate the language of the entity","To indicate the type of the entity","The 'Offset' value gives the precise location of the identified entity within the text, specifying the starting and ending character positions."
"Which Amazon Comprehend feature can be used to automatically remove sensitive information from text data?","PII Redaction","Key Phrase Extraction","Entity Recognition","Sentiment Analysis","PII Redaction can automatically identify and remove personally identifiable information (PII) to protect sensitive data."
"In Amazon Comprehend, what is a 'collection' when referring to Topic Modelling?","A set of documents for topic analysis","A group of recognised entities","A list of sentiment scores","A set of language codes","In topic modelling, a 'collection' refers to the group of documents that are analysed to discover the underlying topics."
"What is the main purpose of using synchronous API calls in Amazon Comprehend?","To get immediate results for small amounts of text","To process large batches of documents","To schedule analysis for later execution","To train custom models","Synchronous API calls are used for analysing small amounts of text where immediate results are needed."
"What is the purpose of the 'DocumentReader' parameter when using Amazon Comprehend with AWS Lambda?","To specify how the input document is read and processed","To specify the output format of the analysis","To define the IAM role for the Lambda function","To set the memory allocation for the Lambda function","The 'DocumentReader' parameter helps define how Comprehend reads and processes the input document, especially when integrating with Lambda functions."
"What AWS service do you typically use to store and manage the training data for custom models in Amazon Comprehend?","Amazon S3","Amazon DynamoDB","Amazon RDS","Amazon EC2","Amazon S3 is commonly used to store training data for custom models in Amazon Comprehend."
"When training a custom classification model in Amazon Comprehend, what is the 'augmentation' process?","Adding more labelled data to improve model accuracy","Removing irrelevant data from the training set","Adjusting the model's hyperparameters","Changing the model's architecture","Augmentation involves adding more labelled data to the training set to improve the model's accuracy and generalisation capabilities."
"Which Amazon Comprehend feature allows you to identify relationships between entities in a document?","Relationship Extraction","Entity Recognition","Key Phrase Extraction","Sentiment Analysis","Relationship Extraction identifies how different entities in a document are related to each other."
"What is the purpose of the 'Endpoint' in Amazon Comprehend?","To provide a URL for accessing the Comprehend service","To store the results of the analysis","To define the input data format","To configure the IAM role for accessing the service","The 'Endpoint' provides the URL that your application uses to access the Amazon Comprehend service."
"Which Amazon Comprehend API would you use to detect the dominant language of a large batch of documents?","StartDominantLanguageDetectionJob","DetectDominantLanguage","BatchDetectDominantLanguage","DetectLanguageBatch","StartDominantLanguageDetectionJob allows for asynchronous processing of a large batch of documents to detect their dominant languages."
"What is the benefit of using Amazon Comprehend's 'Real-time Analysis' feature?","Provides immediate analysis results for each input text","Allows for processing extremely large documents","Enables analysis without an AWS account","Allows you to create visualisations of the results","Real-time Analysis provides immediate results for each input text, making it suitable for applications requiring instant feedback."
"What is the primary purpose of the 'Entity Recognizer Evaluation Metrics' in Amazon Comprehend?","To assess the performance of a custom entity recognition model","To measure the processing speed of the analysis","To determine the cost of using the Comprehend service","To track the number of API calls made","Entity Recognizer Evaluation Metrics are used to assess how well a custom entity recognition model is performing, including precision, recall, and F1-score."
"Which of the following file formats is commonly used for providing training data for custom classification in Amazon Comprehend?","CSV","PNG","MP3","ZIP","CSV is a common format for training data because it can easily represent labelled data for classification tasks."
"What is the significance of 'Precision' and 'Recall' in the context of evaluating custom models in Amazon Comprehend?","'Precision' measures the accuracy of positive predictions, while 'Recall' measures the ability to find all positive instances.","'Precision' measures the processing speed, while 'Recall' measures the cost efficiency.","'Precision' measures the size of the training data, while 'Recall' measures the model complexity.","'Precision' measures the confidence score, while 'Recall' measures the number of API calls.","'Precision' measures the accuracy of positive predictions, while 'Recall' measures the ability to find all positive instances, providing a comprehensive evaluation of the model's performance."
"Which of the following is a typical use case for Amazon Comprehend's syntax analysis feature?","Analysing the grammatical structure of a sentence to understand its meaning","Identifying the sentiment of a text document","Detecting the language of a document","Extracting key phrases from a document","Syntax analysis helps understand the grammatical structure of a sentence, which can be valuable in applications like language learning and content analysis."
"What is the purpose of using the 'Stop Words' feature in Amazon Comprehend?","To exclude common words from analysis to improve accuracy","To identify words with negative sentiment","To translate words into different languages","To highlight important keywords in the text","Stop Words are common words (like 'the', 'a', 'is') that are excluded from analysis to focus on more meaningful terms, improving the accuracy of results."
"Which of the following AWS services is commonly used to collect and pre-process data before feeding it into Amazon Comprehend?","AWS Glue","Amazon SQS","Amazon SNS","Amazon Lambda","AWS Glue is commonly used to extract, transform, and load (ETL) data from various sources before it's analysed by Amazon Comprehend."
"What is the purpose of the 'Named Entity Recognition (NER)' process in Amazon Comprehend?","To identify and classify named entities in the text","To analyse the sentiment of the text","To extract key phrases from the text","To translate the text into different languages","NER identifies and categorises named entities such as people, organisations, locations, and dates within a text."
"When creating a custom entity recogniser, what is the purpose of providing 'entity lists'?","To define specific values for each entity type","To specify the languages supported by the recogniser","To set the confidence threshold for entity recognition","To define the pricing model for the recogniser","Entity lists define specific values or examples for each entity type, helping the recogniser to accurately identify those entities."
"What is the primary advantage of using asynchronous API calls in Amazon Comprehend?","The ability to process large batches of documents efficiently","Immediate results for each input text","Lower cost compared to synchronous API calls","More accurate analysis results","Asynchronous API calls are ideal for processing large batches of documents because they allow you to submit jobs and retrieve results later, improving efficiency."
"Which of the following is a valid use case for Amazon Comprehend Medical?","Extracting medical information from patient records to improve clinical decision support","Generating marketing content for pharmaceutical companies","Analysing financial reports for healthcare organisations","Creating educational materials for medical students","Comprehend Medical is designed to extract medical information from patient records, clinical trial reports, and other healthcare documents to improve clinical decision support."
"Which Amazon Comprehend feature allows you to categorise documents into predefined categories without providing training data?","Pre-trained Classification","Custom Classification","Topic Modelling","Key Phrase Extraction","Pre-trained Classification uses existing models to categorise documents into predefined categories without requiring user-provided training data."
"When working with custom entity recognition in Amazon Comprehend, what does 'active learning' refer to?","A process of continuously improving the model's performance by reviewing and correcting predictions","A technique for reducing the cost of training the model","A method for automatically selecting the best model parameters","A way to visualise the model's performance","Active learning involves reviewing and correcting the model's predictions to improve its performance over time, especially in scenarios with limited labelled data."
"What is the purpose of the 'Data Security' configurations in Amazon Comprehend?","To control access to the input data and analysis results","To optimise the processing speed of the analysis","To define the pricing model for the service","To specify the language of the input documents","Data Security configurations ensure that input data and analysis results are securely stored and accessed, often involving encryption and access control measures."
"Which of the following Amazon Comprehend features is most useful for summarising large documents?","Key Phrase Extraction","Sentiment Analysis","Entity Recognition","Language Detection","Key Phrase Extraction is most useful for summarising large documents as it identifies the most important phrases, giving a concise overview of the content."
"What is the purpose of the 'Vocabulary' feature in Amazon Comprehend Custom Classification?","To improve the accuracy of classification by providing a list of relevant terms","To specify the languages supported by the classification model","To set the maximum number of categories for classification","To define the pricing model for the classification tasks","The Vocabulary feature helps improve the accuracy of classification by providing a list of relevant terms that are specific to your classification task, allowing the model to focus on the most important words."
"Which Amazon Comprehend API is used to identify the dominant language of a document?","DetectDominantLanguage","StartDominantLanguageDetectionJob","IdentifyLanguage","DominantLanguage","DetectDominantLanguage API is used to identify the dominant language of a document."
"What is the function of 'Incremental Training' in Amazon Comprehend Custom Classification?","To update an existing classification model with new data without retraining from scratch","To train multiple classification models simultaneously","To reduce the time required to train a classification model","To improve the accuracy of classification by adding more features","Incremental training allows you to update an existing classification model with new data without retraining the entire model from scratch, saving time and resources."
"You are analysing social media posts using Amazon Comprehend. Which feature would be most useful for understanding the overall public opinion towards a product?","Sentiment Analysis","Key Phrase Extraction","Entity Recognition","Syntax Analysis","Sentiment Analysis is best suited for understanding the overall public opinion towards a product by analysing the positive, negative, or neutral sentiment expressed in social media posts."
"Which AWS service allows you to create a custom vocabulary that can be used with Amazon Comprehend to improve the accuracy of text analysis?","Amazon Transcribe","Amazon Polly","Amazon Translate","Amazon Lex","Amazon Transcribe is primarily used for creating a custom vocabulary to improve the accuracy of speech-to-text analysis, this can indirectly improve text analysis accuracy as well."
"What is the role of 'Annotations' when creating a custom entity recognition model in Amazon Comprehend?","To label the entities in the training data","To define the data storage location","To set up the access permissions","To visualise the model's performance","Annotations are used to label the entities in the training data, which is essential for training the custom entity recognition model to accurately identify those entities in new text."
"Which Amazon Comprehend feature identifies named entities in text, such as people, places, and organisations?","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","Topic Modelling","Entity Recognition identifies and categorises named entities found in the text."
"What type of information does Amazon Comprehend Sentiment Analysis provide?","The overall emotional tone of the text (positive, negative, neutral, or mixed)","The main topics discussed in the text","A list of the most important words and phrases in the text","The language of the text","Sentiment Analysis analyses the text to determine its emotional tone."
"Which Amazon Comprehend feature extracts the most important words and phrases from a piece of text?","Key Phrase Extraction","Language Detection","Syntax Analysis","Custom Classification","Key Phrase Extraction identifies the most relevant words and phrases within the text."
"What is the primary purpose of Amazon Comprehend Language Detection?","To identify the language of the text","To translate the text into another language","To correct grammatical errors in the text","To summarise the text","Language Detection identifies the language in which the text is written."
"Which Amazon Comprehend feature analyses the grammatical structure of text, identifying parts of speech and their relationships?","Syntax Analysis","PII Detection","Custom Entity Recognition","Sentiment Analysis","Syntax Analysis parses the text to understand its grammatical structure."
"Which Amazon Comprehend feature can be used to identify personally identifiable information (PII) in text?","PII Detection","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","PII Detection is designed to identify and redact sensitive information like names, addresses, and credit card numbers."
"What is the purpose of Amazon Comprehend Custom Entity Recognition?","To identify entities specific to your domain or business","To perform sentiment analysis on customer reviews","To translate documents into different languages","To automatically generate summaries of long documents","Custom Entity Recognition allows you to train the model to recognise entities that are specific to your needs."
"What type of model does Amazon Comprehend Custom Classification use?","Machine learning model trained on your data","Rule-based model with predefined keywords","Statistical model based on public datasets","Regular expression matching","Custom Classification uses machine learning models trained on your data to categorise text."
"Which Amazon Comprehend feature helps you organise a collection of documents by automatically identifying the main topics discussed?","Topic Modelling","Sentiment Analysis","Entity Recognition","Key Phrase Extraction","Topic Modelling automatically identifies the main topics discussed within a collection of documents."
"What is the maximum size of a single document that can be processed by Amazon Comprehend's synchronous APIs?","5 KB","1 KB","10 KB","20 KB","The synchronous APIs have a size limit of 5 KB per document."
"What programming languages are primarily used to interact with Amazon Comprehend's APIs?","Python and Java","C++ and C#","Ruby and PHP","JavaScript and Go","The primary languages are Python, through the boto3 library, and Java, through the AWS SDK."
"Which AWS service can be used to orchestrate workflows involving Amazon Comprehend, such as processing large batches of documents?","AWS Step Functions","Amazon SQS","Amazon SNS","AWS Lambda","AWS Step Functions allows for the creation of complex workflows involving multiple AWS services, including Comprehend."
"What is the main advantage of using Amazon Comprehend's asynchronous APIs over the synchronous APIs?","Processing larger documents and batches","Real-time analysis","Lower latency","Simplified API calls","Asynchronous APIs are designed for processing large amounts of data and larger documents that synchronous APIs cannot handle."
"How does Amazon Comprehend pricing work?","Pay-per-request based on the amount of text processed","Fixed monthly fee regardless of usage","One-time fee for unlimited usage","Pay-per-hour based on the compute resources used","Amazon Comprehend uses a pay-per-request pricing model based on the amount of text processed."
"What IAM permission is required to allow an AWS Lambda function to call the Amazon Comprehend API?","comprehend:DetectSentiment","lambda:InvokeFunction","s3:GetObject","iam:PassRole","The Lambda function needs the `comprehend:DetectSentiment` (or similar appropriate Comprehend action) permission."
"In Amazon Comprehend, what is a 'document'?","A unit of text that Comprehend analyses","A file containing configuration settings","A user profile with access permissions","A database table storing text data","A 'document' in Comprehend refers to the individual piece of text that is being analysed."
"Which Amazon Comprehend feature helps you to understand the relationships between entities in the text?","Relationship Extraction","Topic Modelling","Sentiment Analysis","Key Phrase Extraction","Relationship Extraction helps to understand how entities are related to each other within the text."
"What is the benefit of using Amazon Comprehend's pre-trained models?","They can be used immediately without needing any training data","They provide higher accuracy than custom models","They are free to use","They can be easily customised","Pre-trained models are immediately usable as they are already trained on large datasets."
"When should you consider using Amazon Comprehend Custom Vocabulary?","When you need Comprehend to recognise specific terms or jargon unique to your domain","When you need to translate text into a different language","When you want to improve the speed of Comprehend processing","When you want to reduce the cost of using Comprehend","Custom Vocabulary allows you to improve the accuracy of Comprehend when dealing with domain-specific terms."
"What is the purpose of the 'Endpoint' in Amazon Comprehend?","A hosted service for accessing custom models","A storage location for training data","A tool for monitoring Comprehend performance","A graphical user interface for using Comprehend","An Endpoint allows you to use your custom models without managing the underlying infrastructure."
"What type of data is typically used to train Amazon Comprehend Custom Classification models?","Text documents labelled with categories","Images with bounding boxes","Numerical data with target variables","Audio recordings with transcriptions","Custom Classification requires labelled text data for training the model."
"Which Amazon Comprehend feature can be used to identify and categorize different parts of speech in a text, such as nouns, verbs, and adjectives?","Part of Speech Tagging (POS Tagging)","Named Entity Recognition (NER)","Sentiment Analysis","Key Phrase Extraction","Part of Speech Tagging (POS Tagging) is specifically designed to identify and categorize different parts of speech in a text."
"What is the primary use case for Amazon Comprehend Medical?","Analysing medical text and extracting health information","Analysing financial documents","Analysing customer reviews","Analysing social media posts","Comprehend Medical is specifically designed to extract health information from medical text."
"Which of these is NOT a capability of Amazon Comprehend Medical?","Detecting medical conditions, medications, and dosages","Identifying patient demographics","Extracting relationships between medical entities","Performing sentiment analysis on medical notes","Comprehend Medical does not directly identify patient demographics; its focus is on medical entities and relationships."
"What is the advantage of using Amazon Comprehend's asynchronous processing for large text datasets?","It allows for processing larger documents than synchronous processing","It provides real-time analysis results","It reduces the cost of processing","It requires less coding effort","Asynchronous processing is suitable for processing larger documents and datasets as it is not bound by the same size limitations as synchronous processing."
"You want to extract all mentions of pharmaceutical drugs from a collection of medical records. Which Amazon Comprehend feature is most suitable for this task?","Comprehend Medical Entity Recognition","Comprehend Key Phrase Extraction","Comprehend Sentiment Analysis","Comprehend Topic Modelling","Comprehend Medical Entity Recognition is designed to identify and categorise medical entities, including pharmaceutical drugs."
"What is the purpose of the `DataAccessRoleArn` parameter when creating a custom model in Amazon Comprehend?","Specifies the IAM role Comprehend assumes to access training data in S3","Defines the IAM role used to access the Comprehend API","Sets the permissions for accessing the custom model endpoint","Grants access to Comprehend documentation","The `DataAccessRoleArn` specifies the IAM role that Comprehend assumes to access the training data stored in S3."
"What is the minimum number of documents required to train a custom classification model in Amazon Comprehend?","50","10","1","1000","A minimum of 50 documents per class is recommended to train a custom classification model."
"What is the maximum training time allowed for an Amazon Comprehend Custom Entity Recognition model?","24 hours","1 hour","30 minutes","72 hours","The maximum training time allowed for Custom Entity Recognition models is 24 hours."
"You have a large dataset of customer feedback in different languages. Which Amazon Comprehend feature should you use first to determine the language of each feedback entry?","Language Detection","Sentiment Analysis","Key Phrase Extraction","Entity Recognition","Language Detection should be used first to identify the language of each feedback entry before further analysis."
"Which input format is supported for batch analysis jobs in Amazon Comprehend?","A single text file containing all documents","A CSV file with one document per row","A directory in S3 containing individual text files","A database table containing text data","For batch analysis, the input should be a directory in S3 containing individual text files."
"What is the purpose of the 'Stop Words' list in Amazon Comprehend Topic Modelling?","To exclude common words that do not contribute to topic identification","To include only the most important words for topic identification","To define the languages to be considered for topic modelling","To specify the categories for topic assignment","The Stop Words list helps to exclude common words that don't contribute significantly to topic identification."
"What type of output does Amazon Comprehend provide for a PII Detection job?","The original text with PII entities redacted","A list of all PII entities found in the text","A summary of the PII entities found","A score indicating the confidence of PII detection","Comprehend provides the original text with the PII entities redacted."
"Which feature of Amazon Comprehend helps improve accuracy when analysing text containing domain-specific terminology?","Custom Vocabulary","Topic Modelling","Sentiment Analysis","Language Detection","Custom Vocabulary allows you to specify domain-specific terms, improving accuracy."
"What is the purpose of the 'Number of Topics' parameter in Amazon Comprehend Topic Modelling?","Specifies the desired number of topics to identify in the collection of documents","Sets the minimum number of documents per topic","Determines the number of words to include in each topic","Controls the number of concurrent topic modelling jobs","The 'Number of Topics' parameter sets the desired number of topics to identify."
"What is the best way to handle sensitive data when using Amazon Comprehend?","Use PII detection to identify and redact sensitive information","Encrypt the data before sending it to Comprehend","Do not send sensitive data to Comprehend","Store sensitive data in a separate database","PII detection allows for the identification and redaction of sensitive information."
"Which of the following is NOT a valid output format for Amazon Comprehend batch processing jobs?","JSON","CSV","Text","Parquet","Amazon Comprehend batch processing jobs can output JSON or text formats, but not CSV or Parquet."
"What is the main difference between synchronous and asynchronous Amazon Comprehend API calls?","Synchronous calls return results immediately, while asynchronous calls require polling for results","Synchronous calls are cheaper than asynchronous calls","Synchronous calls can process larger documents than asynchronous calls","Synchronous calls support more languages than asynchronous calls","Synchronous calls return results immediately, whereas asynchronous calls require polling as they are for larger jobs."
"You want to analyse customer reviews to determine overall customer satisfaction. Which Amazon Comprehend feature is most suitable?","Sentiment Analysis","Entity Recognition","Key Phrase Extraction","Topic Modelling","Sentiment Analysis is designed to determine the emotional tone of the text, making it suitable for gauging customer satisfaction."
"Which of the following factors impacts the accuracy of Amazon Comprehend's Sentiment Analysis feature?","The clarity and grammatical correctness of the text","The length of the text","The number of entities in the text","The specific AWS region used","The accuracy of Sentiment Analysis depends on the clarity and grammatical correctness of the input text."
"You need to ensure that your Amazon Comprehend custom model is highly available. What should you do?","Deploy the model to multiple AWS Regions","Monitor the model's performance regularly and retrain it if necessary","Increase the instance size of the endpoint hosting the model","Nothing, Comprehend handles High Availability","Regularly monitoring and retraining the model can help to ensure its performance and stability, contributing to its high availability."
"What is the primary function of the Amazon Comprehend service?","To analyse text and extract insights","To translate text between languages","To store and manage large text datasets","To generate text from structured data","Amazon Comprehend is designed to analyse text and extract insights such as sentiment, entities, and key phrases."
"When using Amazon Comprehend, what is the purpose of defining a 'document type' for custom classification tasks?","To specify the format of the input documents (e.g., PDF, TXT)","To categorise the documents based on content (e.g., news articles, product reviews)","To define the languages used in the documents","To limit the size of the documents","Defining a 'document type' categorises the documents based on their content, so the model can then classify accordingly."
"What's a use case for the Amazon Comprehend DetectDominantLanguage API?","Automatically routing customer support requests based on the language used","Summarising long text articles into shorter paragraphs","Identifying Personally Identifiable Information (PII) in customer reviews","Identifying the emotional tone of emails","DetectDominantLanguage is ideal for routing customer support requests."
"Your company wants to analyse text in many languages, which Comprehend features must be applied before any further operations?","Language Detection","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","Language Detection must be applied first so the other features know what is being processed."
"What is the purpose of using the 'ClientRequestToken' parameter in Amazon Comprehend API calls?","To ensure idempotency of the API requests","To authenticate the API requests","To specify the region for the API requests","To set the timeout for the API requests","The 'ClientRequestToken' parameter helps ensure that the API request is idempotent."
"Which of the following is NOT a factor when determining the cost of using Amazon Comprehend?","The number of documents processed","The size of the documents processed","The features used (e.g., Sentiment Analysis, Entity Recognition)","The AWS region where Comprehend is used","The AWS region does not directly affect the cost of using Comprehend."
"Your organisation stores customer feedback data in S3 and wants to automate the process of analysing this feedback using Amazon Comprehend. What AWS service can you use to trigger the Comprehend analysis whenever new feedback data is added to S3?","AWS Lambda","Amazon EC2","Amazon SQS","Amazon SNS","AWS Lambda can be triggered by S3 events, allowing you to automate the Comprehend analysis."
"In Amazon Comprehend, what is the primary purpose of the Key Phrase Extraction feature?","To identify the most important concepts in a text","To translate text between languages","To identify the sentiment of the text","To redact Personally Identifiable Information (PII)","Key Phrase Extraction identifies the main topics or ideas discussed in the input text."
"What is the main function of Amazon Comprehend's Entity Recognition?","To identify and categorise named entities in text","To identify the dominant language of the text","To classify the sentiment of a document","To extract key phrases from a document","Entity Recognition identifies and classifies specific entities such as people, organisations, and locations within the text."
"Which of the following is a common use case for Amazon Comprehend's Sentiment Analysis feature?","Analysing customer feedback to understand opinions","Translating documents into different languages","Identifying the language of a text","Extracting key phrases from a document","Sentiment analysis helps understand the overall emotional tone of text, which is crucial for customer feedback analysis."
"What type of data is Amazon Comprehend primarily designed to process?","Text data","Image data","Video data","Audio data","Amazon Comprehend is specifically designed to analyse and extract insights from unstructured text data."
"What is a custom entity recognizer in Amazon Comprehend used for?","To identify entities specific to a particular domain or organisation","To translate entities into different languages","To perform sentiment analysis on entities","To extract key phrases related to entities","Custom entity recognizers are trained to identify entities that are not recognised by the built-in entity recognition models."
"Which Amazon Comprehend feature can be used to automatically redact Personally Identifiable Information (PII) from text?","PII Detection","Sentiment Analysis","Key Phrase Extraction","Entity Recognition","PII Detection is designed to identify and redact sensitive personal information from text documents."
"What is the purpose of the 'DetectDominantLanguage' operation in Amazon Comprehend?","To identify the language in which a text is written","To translate text from one language to another","To identify the sentiment of a text","To extract key phrases from a text","'DetectDominantLanguage' determines the language of the input text."
"Which of the following is a benefit of using Amazon Comprehend Medical over standard Amazon Comprehend?","It is trained on medical texts and can identify medical entities","It can translate medical texts into different languages","It can only perform sentiment analysis on medical texts","It is free to use for medical research","Amazon Comprehend Medical is trained on medical data, enabling it to identify medical entities like diagnoses and medications."
"What is a 'topic modelling' job in Amazon Comprehend used for?","To discover hidden topics within a collection of documents","To identify the sentiment of each document","To translate documents between languages","To extract key phrases from documents","Topic modelling identifies underlying themes or subjects within a large set of documents without prior knowledge."
"Which of the following is NOT a feature offered by Amazon Comprehend?","Image recognition","Sentiment analysis","Entity recognition","Key phrase extraction","Image recognition is not a feature provided by Amazon Comprehend. It focuses on text analysis."
"What is the purpose of using custom classification in Amazon Comprehend?","To categorise documents into specific custom categories","To translate documents into different languages","To identify the sentiment of documents","To extract key phrases from documents","Custom classification allows you to train a model to categorise documents based on your specific business needs."
"How can you improve the accuracy of Amazon Comprehend's entity recognition?","By training a custom entity recognizer with relevant data","By disabling sentiment analysis","By using only short text snippets","By reducing the size of the input text","Training a custom entity recognizer allows the model to learn specific patterns and improve accuracy for your particular use case."
"Which AWS service is commonly integrated with Amazon Comprehend to process large batches of documents?","Amazon S3","Amazon EC2","Amazon RDS","Amazon Lambda","Amazon S3 is commonly used to store large volumes of documents that can be processed by Amazon Comprehend."
"What is the maximum size of a single document that can be processed by Amazon Comprehend's synchronous API?","5 KB","5 MB","50 MB","500 MB","The synchronous API has a limit on the size of the document that can be processed, which is 5KB."
"What does the 'ConfidenceScore' indicate in Amazon Comprehend's output?","The level of certainty the model has in its prediction","The speed at which the analysis was performed","The cost of the analysis","The size of the input text","The ConfidenceScore represents the model's confidence in the accuracy of its predictions."
"In Amazon Comprehend, what does the term 'Offset' refer to when extracting entities?","The start and end character positions of the entity within the text","The confidence score of the entity","The type of the entity","The language of the entity","The Offset provides the exact location of the identified entity within the original text."
"When using Amazon Comprehend, which API operation would you use to find the relationships between entities in a document?","Syntax","Sentiment Analysis","Entity Recognition","Key Phrase Extraction","The Syntax API can be used to determine relationships between words in a sentence, which can help infer relationships between entities."
"Which feature of Amazon Comprehend is best suited for identifying the grammatical structure of a sentence?","Syntax Analysis","Entity Recognition","Sentiment Analysis","Key Phrase Extraction","Syntax Analysis is specifically designed to analyse the grammatical structure of a sentence."
"Which of the following statements is true about asynchronous processing in Amazon Comprehend?","It's suitable for processing large batches of documents","It's only used for real-time sentiment analysis","It's faster than synchronous processing for small documents","It doesn't support custom models","Asynchronous processing is ideal for handling large volumes of documents in batches."
"What is the purpose of the 'Endpoint' feature in Amazon Comprehend?","To create a persistent, scalable inference endpoint for real-time analysis","To store the results of analysis","To configure access control policies","To define the input format of the text","Endpoints are used to host and scale custom models for real-time inference."
"When using Amazon Comprehend, what information does the 'Entities' section of the output provide?","Details about the identified entities, such as type, text, and confidence score","The sentiment score of the text","The key phrases extracted from the text","The dominant language of the text","The Entities section contains information about the entities identified in the text, including their type, text, and confidence score."
"What type of machine learning model does Amazon Comprehend primarily use for its analysis?","Natural Language Processing (NLP) models","Computer Vision models","Reinforcement Learning models","Time Series models","Amazon Comprehend relies on NLP models to perform its text analysis tasks."
"Which Amazon Comprehend feature is useful for summarising a long document into shorter, more concise text?","Abstractive Summarization","Extractive Summarization","Key Phrase Extraction","Entity Recognition","Abstractive Summarization in Amazon Comprehend can generate a concise summary of a long document in a coherent and readable manner."
"Which Comprehend API call can be used to determine whether a text expresses a positive, negative, neutral, or mixed opinion?","DetectSentiment","DetectEntities","DetectKeyPhrases","DetectSyntax","The DetectSentiment API call assesses the sentiment expressed in the input text."
"You need to identify medical conditions mentioned in patient notes. Which service is most suitable?","Amazon Comprehend Medical","Amazon Translate","Amazon Transcribe","Amazon Polly","Amazon Comprehend Medical is designed to extract medical information from text."
"What does the 'Classes' parameter refer to when creating a custom classification model in Amazon Comprehend?","The categories into which the documents will be classified","The programming languages supported by Comprehend","The hardware configurations for training the model","The different types of entities that can be recognised","The Classes parameter defines the custom categories for classifying documents."
"Which of the following is an example of Personally Identifiable Information (PII) that Amazon Comprehend can detect?","Email address","General location","Product names","Company size","Email addresses are considered PII and can be detected by Comprehend."
"Which Amazon Comprehend API action is designed to identify relationships between words in a sentence?","DetectSyntax","DetectEntities","DetectSentiment","DetectKeyPhrases","The DetectSyntax API is used for syntax analysis to understand sentence structure and word relationships."
"What is the purpose of the 'Training Data' when building a custom model in Amazon Comprehend?","To provide examples of text and their corresponding labels","To define the deployment environment","To specify the hardware requirements","To configure the API endpoints","Training data is used to teach the model how to recognise patterns and make accurate predictions."
"What is the primary benefit of using Amazon Comprehend's serverless architecture?","It eliminates the need to manage servers","It provides faster processing speeds than traditional architectures","It allows for custom hardware configurations","It reduces the cost of storage","A serverless architecture eliminates the need to manage servers, simplifying deployment and maintenance."
"Which of the following is a valid input format for Amazon Comprehend?","Plain text","JPEG image","MP3 audio","MPEG video","Amazon Comprehend processes plain text data."
"What type of information does Amazon Comprehend's 'Detect Targeted Sentiment' feature provide?","The sentiment expressed towards specific entities or aspects within the text","The overall sentiment of the entire document","The sentiment of the text translated into another language","The emotional tone of the author","Detect Targeted Sentiment identifies sentiment related to specific entities or aspects."
"Which AWS service can be used to trigger an Amazon Comprehend job when a new document is uploaded to S3?","AWS Lambda","Amazon EC2","Amazon RDS","Amazon CloudWatch","AWS Lambda can be configured to trigger Comprehend jobs when new files are added to S3."
"What is a common use case for Amazon Comprehend in the financial services industry?","Analysing customer feedback for fraud detection","Automating video transcoding","Performing image recognition for KYC","Transcribing audio recordings of meetings","Comprehend can be used to analyse customer feedback to identify potentially fraudulent activities."
"What is the relationship between Amazon Comprehend and Amazon SageMaker?","Amazon SageMaker can be used to build and train custom Comprehend models","Amazon Comprehend is used to monitor SageMaker endpoints","Amazon Comprehend is a component of Amazon SageMaker","They are not related services","Amazon SageMaker is used for building, training, and deploying custom models that can be used with Comprehend."
"Which of the following is NOT a supported language by Amazon Comprehend?","Klingon","English","Spanish","German","Klingon is not a supported language by Amazon Comprehend."
"Which feature of Amazon Comprehend can help identify compliance risks within a document?","PII Detection","Sentiment Analysis","Key Phrase Extraction","Entity Recognition","PII Detection can help identify sensitive information that may pose compliance risks."
"What is the purpose of the 'Vocabulary' parameter when creating a custom classification model?","To specify the set of words the model should focus on","To define the output format","To configure the training data location","To set the maximum processing time","The Vocabulary parameter defines the set of words the model should use during training."
"When using Amazon Comprehend's custom models, what does 'Model Version' refer to?","A specific iteration of the model after training","The programming language used to train the model","The storage location of the model","The AWS region where the model is deployed","A Model Version represents a specific iteration of the model after training, allowing you to manage and deploy different versions."
"Which of the following is a use case for Amazon Comprehend in the healthcare industry?","Extracting medical conditions and medications from patient records","Generating synthetic medical images","Predicting hospital readmission rates","Monitoring patient vital signs in real-time","Comprehend Medical can extract valuable medical information from patient records."
"Which of the following is an example of a 'Category' in Amazon Comprehend's custom classification feature?","A specific topic or theme relevant to your data","A specific type of entity, such as 'Person' or 'Location'","A particular sentiment, such as 'Positive' or 'Negative'","A particular language, such as 'English' or 'Spanish'","A Category represents a specific topic or theme used to classify documents."
"In Amazon Comprehend, what is the purpose of the 'Data Security Configuration' parameter?","To specify encryption and access controls for data at rest and in transit","To configure the model's training parameters","To define the input data format","To set the maximum processing time","The Data Security Configuration parameter is used to configure encryption and access controls for data."
"What type of analysis is used to find relationships between words in a document?","Syntax analysis","Sentiment analysis","Entity recognition","Key phrase extraction","Syntax analysis examines the grammatical structure of sentences to find word relationships."
"How can the cost of using Amazon Comprehend be optimised?","By using asynchronous processing for large volumes of data","By only using synchronous processing","By increasing the document size","By disabling PII detection","Asynchronous processing is cost-effective for large volumes of data."
"Which of the following is NOT a valid output format for Amazon Comprehend's analysis results?","CSV","JSON","XML","Plain Text","Amazon Comprehend outputs in JSON format."
"Which of the following Amazon Comprehend features helps in understanding customer opinions and feedback?","Sentiment Analysis","Key Phrase Extraction","Entity Recognition","Syntax Analysis","Sentiment Analysis is specifically designed to analyse customer feedback."
"You need to create a custom entity recognition model to identify specific product codes within technical documents. Which step is most important?","Providing sufficient training data with labelled product codes","Selecting the most recent version of Amazon Comprehend","Disabling sentiment analysis","Using a very short document size","Providing sufficient labelled training data is critical for training an accurate custom entity recognition model."
"What is the significance of the 'S3Uri' parameter in Amazon Comprehend asynchronous jobs?","It specifies the location of the input data in Amazon S3","It defines the AWS region for processing","It configures access control policies","It sets the maximum processing time","The S3Uri parameter specifies the location of the input data in Amazon S3 that Comprehend will process."
"When creating a custom entity recognizer, what does the 'EntityList' parameter refer to?","A list of pre-defined entities for the model to recognize","A list of AWS regions where the model can be deployed","A list of programming languages supported by the model","A list of permissions required to access the model","The EntityList parameter contains a list of pre-defined entities the model should recognize."
"What is the maximum number of entities that Amazon Comprehend can detect in a single document by default?","Unlimited","100","500","There is not a technical limitation","Amazon Comprehend can detect a large number of entities by default."
"Which feature of Amazon Comprehend would you use to find topics that customers are frequently mentioning in product reviews?","Topic Modelling","Key Phrase Extraction","Entity Recognition","Sentiment Analysis","Topic Modelling is ideal for discovering common themes or topics within customer reviews."
"What is the primary function of Amazon Comprehend?","Natural language processing","Data warehousing","Code compilation","Image recognition","Amazon Comprehend is a natural language processing (NLP) service that uses machine learning to find insights and relationships in text."
"Which Amazon Comprehend feature extracts key phrases from a document?","Key phrase extraction","Sentiment analysis","Entity recognition","Topic modelling","Key phrase extraction identifies the most important phrases in a text."
"Which Amazon Comprehend feature can be used to determine the overall feeling expressed in a text?","Sentiment analysis","Language detection","Syntax analysis","Entity recognition","Sentiment analysis identifies the overall feeling (positive, negative, neutral, or mixed) expressed in a text."
"What type of entities does Amazon Comprehend's entity recognition feature identify?","People, places, organisations, dates, quantities","File names, folders, extensions","HTML tags, CSS classes, JavaScript functions","IP addresses, MAC addresses, DNS records","Entity recognition identifies various entities such as people, places, organisations, dates, and quantities."
"Which Amazon Comprehend feature can be used to identify the language of a text?","Language detection","Sentiment analysis","Syntax analysis","Key phrase extraction","Language detection identifies the language of the input text."
"What is the purpose of Amazon Comprehend Medical?","To extract medical information from unstructured text","To process medical images","To manage patient records","To schedule medical appointments","Amazon Comprehend Medical is a natural language processing service that extracts medical information from unstructured text."
"What type of information can Amazon Comprehend Medical extract?","Medical conditions, medications, dosages, tests, treatments, procedures","Stock prices, financial reports, economic indicators","Legal contracts, terms of service, privacy policies","Engineering diagrams, circuit schematics, technical specifications","Comprehend Medical extracts health data like medical conditions, medications, dosages, tests, treatments, and procedures."
"Which of the following is NOT a capability of Amazon Comprehend?","Image analysis","Sentiment analysis","Entity recognition","Key phrase extraction","Amazon Comprehend is focused on text analysis and does not perform image analysis."
"What is the maximum size limit for a single document processed by Amazon Comprehend?","5 KB","1 MB","10 MB","1 GB","The maximum size limit for a single document processed by Amazon Comprehend is 5 KB."
"Which service does Amazon Comprehend integrate with to process large volumes of text data?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","Amazon Comprehend integrates with Amazon S3 to process large volumes of text data stored in S3 buckets."
"What type of analysis does Amazon Comprehend perform to identify the grammatical structure of a text?","Syntax analysis","Sentiment analysis","Entity recognition","Key phrase extraction","Syntax analysis identifies the grammatical structure of a text, including parts of speech and dependencies between words."
"Which feature of Amazon Comprehend allows you to classify documents into predefined categories?","Custom classification","Sentiment analysis","Entity recognition","Language detection","Custom classification allows you to train Comprehend to classify documents into categories specific to your business."
"What is the purpose of Amazon Comprehend's Topic Modelling feature?","To discover topics present in a collection of documents","To identify the sentiment of each document","To extract key phrases from individual documents","To translate documents into different languages","Topic modelling discovers abstract topics that are present in a collection of documents."
"Which of the following is a use case for Amazon Comprehend's Entity Recognition?","Identifying customer names in support tickets","Detecting fraudulent transactions","Predicting stock prices","Generating marketing copy","Entity recognition can be used to identify customer names in support tickets to route them to the appropriate agent."
"How can you use Amazon Comprehend to improve customer service?","By analysing customer feedback to identify common issues","By predicting customer churn","By automating customer support responses","By generating personalised marketing emails","Analysing customer feedback with Comprehend can identify common issues and improve customer service."
"What is the purpose of using a custom vocabulary with Amazon Comprehend?","To improve the accuracy of entity recognition for domain-specific terms","To translate documents into different languages","To summarise documents","To encrypt documents","A custom vocabulary improves the accuracy of entity recognition for domain-specific terms that may not be recognised by the default model."
"Which AWS service is often used to store the output of Amazon Comprehend jobs?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","The output of Amazon Comprehend jobs is typically stored in Amazon S3 buckets."
"Which of the following is NOT a valid input format for Amazon Comprehend?","PDF","Plain text","HTML","JSON","Amazon Comprehend does not directly process PDF files."
"How does Amazon Comprehend handle personally identifiable information (PII) in text?","It can redact or mask PII entities","It automatically encrypts PII data","It ignores PII data","It stores PII data in a separate database","Amazon Comprehend can redact or mask PII entities to protect sensitive information."
"Which feature of Amazon Comprehend allows you to identify relationships between entities in a text?","Relationship extraction","Sentiment analysis","Syntax analysis","Topic modelling","Relationship extraction identifies how entities are related to each other in a text."
"When using custom entity recognition in Amazon Comprehend, what is required to train the model?","A labelled dataset of text documents","A list of keywords","A regular expression","A database connection","Training a custom entity recognition model requires a labelled dataset of text documents with annotated entities."
"Which of the following is an advantage of using Amazon Comprehend for NLP tasks?","Scalability and cost-effectiveness","Unlimited storage capacity","Real-time video processing","High-performance gaming capabilities","Amazon Comprehend provides scalability and cost-effectiveness for NLP tasks, as it is a fully managed service."
"How can you monitor the performance and costs of your Amazon Comprehend jobs?","Using Amazon CloudWatch","Using Amazon Inspector","Using Amazon Trusted Advisor","Using Amazon Macie","Amazon CloudWatch can be used to monitor the performance and costs of Amazon Comprehend jobs."
"What type of API is used to interact with Amazon Comprehend?","REST API","SOAP API","GraphQL API","FTP API","Amazon Comprehend is accessed through a REST API."
"Which Amazon Comprehend feature is useful for identifying and classifying different parts of speech in a sentence?","Part-of-speech tagging","Sentiment analysis","Entity recognition","Key phrase extraction","Part-of-speech tagging identifies and classifies different parts of speech (e.g., nouns, verbs, adjectives) in a sentence."
"Which of the following is a benefit of using Amazon Comprehend's pre-trained models?","Faster processing and reduced training time","Lower storage costs","Increased network bandwidth","Enhanced security features","Pre-trained models offer faster processing and reduced training time, as they are already trained on large datasets."
"What is the purpose of the 'DocumentClassifier' resource in Amazon Comprehend?","To classify entire documents based on their content","To extract key phrases from documents","To identify entities in documents","To analyse the sentiment of documents","The 'DocumentClassifier' resource is used to classify entire documents into predefined categories."
"What type of data should be used to train a custom document classifier in Amazon Comprehend?","Text documents with labels indicating their category","Images with labels indicating their content","Audio recordings with transcriptions","Spreadsheet data with category assignments","A custom document classifier requires text documents with labels indicating their category for training."
"What is the purpose of the 'Endpoint' resource in Amazon Comprehend?","To provide a real-time inference endpoint for custom models","To store training data","To manage access control","To configure logging","The 'Endpoint' resource provides a real-time inference endpoint for custom models, allowing you to make predictions on new data."
"What does the 'Entities' section in the Amazon Comprehend API response represent?","The list of entities identified in the text","The sentiment scores for the text","The language detected in the text","The key phrases extracted from the text","The 'Entities' section contains the list of entities identified in the text, along with their types and confidence scores."
"Which Amazon Comprehend feature is best suited for analysing social media posts to understand public opinion?","Sentiment analysis","Language detection","Syntax analysis","Entity recognition","Sentiment analysis is ideal for analysing social media posts to understand public opinion towards a brand, product, or topic."
"When should you consider using a custom model in Amazon Comprehend instead of the pre-trained models?","When you need to analyse data specific to your industry or domain","When you need to process documents in a widely spoken language","When you need to perform basic sentiment analysis","When you need to extract common entities like people and places","A custom model is beneficial when you need to analyse data specific to your industry or domain, as it can be trained on data relevant to your specific use case."
"What is the purpose of the 'inputDataConfig' parameter in Amazon Comprehend?","To specify the location of the input data","To define the output format","To configure the encryption settings","To set the maximum number of documents to process","The 'inputDataConfig' parameter is used to specify the location of the input data, such as an Amazon S3 bucket containing the text files."
"What is the role of the 'outputDataConfig' parameter in Amazon Comprehend?","To specify the location where the results should be stored","To define the input data format","To configure the access control settings","To set the maximum processing time","The 'outputDataConfig' parameter specifies the location where the results of the analysis should be stored, typically an Amazon S3 bucket."
"Which of the following is a use case for Amazon Comprehend's Key Phrase Extraction feature?","Summarising a document by identifying its main topics","Translating a document into another language","Detecting the sentiment of a document","Identifying the language of a document","Key Phrase Extraction can be used to summarise a document by identifying its main topics and key points."
"Which encryption option is available for securing data at rest in Amazon Comprehend?","AWS Key Management Service (KMS)","SSL/TLS","AES-256","RSA","Amazon Comprehend uses AWS Key Management Service (KMS) for encrypting data at rest."
"What is the primary function of the 'DetectDominantLanguage' API in Amazon Comprehend?","To determine the dominant language of a text","To translate a text into multiple languages","To identify all languages present in a text","To verify the accuracy of a translation","The 'DetectDominantLanguage' API determines the dominant language of a text."
"Which feature of Amazon Comprehend allows you to group similar documents together based on their content?","Topic modelling","Sentiment analysis","Entity recognition","Key phrase extraction","Topic modelling groups similar documents together based on shared topics."
"Which Amazon Comprehend API call is used to extract named entities from a document?","DetectEntities","DetectSentiment","DetectKeyPhrases","DetectSyntax","DetectEntities is used to extract named entities from a document."
"Which of the following best describes the Amazon Comprehend 'syntax' API?","Identifies the grammatical structure of sentences and words","Determines the overall sentiment of a text","Extracts key phrases from a document","Identifies specific entities like people and places","The syntax API identifies the grammatical structure of sentences, determining parts of speech and dependencies."
"If you need to process a large number of documents with Amazon Comprehend, what is the most efficient approach?","Use a batch processing job with Amazon S3","Process each document individually using the API","Use Amazon EC2 to host your own NLP library","Translate the documents into a single large file","Batch processing with Amazon S3 is the most efficient way to process a large number of documents."
"What is the maximum number of custom entity types that can be defined in a single custom entity recognition model in Amazon Comprehend?","25","5","100","50","You can define up to 25 custom entity types in a single custom entity recognition model."
"Which of the following is NOT a common use case for custom classification models in Amazon Comprehend?","Identifying spam emails","Categorizing customer support tickets","Analysing financial reports","Predicting weather patterns","Predicting weather patterns isn't an NLP task and isn't suited to custom classification."
"When training a custom model in Amazon Comprehend, what type of file is used to provide the training data?","CSV","JSON","XML","PDF","A CSV file is used to provide the training data for custom models in Amazon Comprehend."
"What is the primary advantage of using asynchronous processing in Amazon Comprehend?","Allows processing large volumes of data without waiting for immediate results","Enables real-time processing of data","Provides stronger data encryption","Reduces the cost of processing data","Asynchronous processing is designed for large volumes of data where immediate results are not needed."
"How do you grant Amazon Comprehend access to data stored in an Amazon S3 bucket?","By creating an IAM role with appropriate permissions","By sharing the S3 bucket publicly","By encrypting the S3 bucket with a specific key","By whitelisting the Amazon Comprehend IP address","An IAM role with appropriate permissions is required to grant access to Amazon Comprehend to data in S3."
"Which Amazon Comprehend API is used for identifying relationships between entities mentioned in a document?","DetectRelationships","DetectEntities","DetectKeyPhrases","DetectSentiment","The 'DetectRelationships' API is specifically designed for identifying relationships between entities mentioned in a document."
"What is the key benefit of using Amazon Comprehend's real-time endpoint?","Provides low-latency inference for immediate results","Reduces the cost of processing data","Increases the accuracy of results","Simplifies the integration with other AWS services","The real-time endpoint offers low-latency inference, making it ideal for applications requiring immediate results."
"What is the maximum size of a training dataset for custom entity recognition in Amazon Comprehend?","50 MB","1 GB","10 GB","1 TB","The maximum size of a training dataset for custom entity recognition is 50 MB."
"When creating a custom vocabulary for Amazon Comprehend, what should you consider including in the vocabulary?","Domain-specific terms and jargon","Common English words","Numbers and punctuation","Stop words","Domain-specific terms and jargon are most useful to add to a custom vocabulary."
"You have a dataset of customer reviews and want to identify the topics customers are discussing. Which Amazon Comprehend feature is best suited for this?","Topic modelling","Sentiment analysis","Entity recognition","Key phrase extraction","Topic modelling is best suited for identifying the main topics in a collection of documents, such as customer reviews."