"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of AWS Panorama Appliance?","To run computer vision applications at the edge.","To store large datasets.","To manage AWS Lambda functions.","To host static websites.","The AWS Panorama Appliance is designed to process and analyse video streams locally, running computer vision applications directly at the edge."
"What type of models does AWS Panorama support for computer vision applications?","Models trained with SageMaker, TensorFlow, and PyTorch.","Models trained with only Keras.","Models trained with only Caffe.","Models trained with only MXNet.","AWS Panorama supports a broad range of model types, including those trained using popular frameworks like SageMaker, TensorFlow, and PyTorch, allowing developers to bring their existing models to the edge."
"Which AWS service is primarily used for model training and optimisation before deploying to AWS Panorama?","Amazon SageMaker","Amazon Rekognition","Amazon S3","Amazon CloudWatch","Amazon SageMaker is the go-to service for training, tuning, and deploying machine learning models, including those intended for use with AWS Panorama. It offers tools to optimise models for edge deployment."
"What is the purpose of the AWS Panorama Application SDK?","To develop and test computer vision applications.","To monitor hardware resource usage.","To manage IAM roles.","To configure network settings.","The AWS Panorama Application SDK provides the necessary tools and libraries for developers to create, test, and debug their computer vision applications before deploying them to the Panorama Appliance."
"In AWS Panorama, what does a 'node' typically represent in a computer vision application graph?","A processing unit (e.g., a model or algorithm).","A physical camera connected to the appliance.","A storage location for video data.","A network connection to AWS Cloud.","In the context of AWS Panorama, a 'node' within an application graph represents a specific processing unit, such as a machine learning model, image processing algorithm, or data transformation step."
"What is the main benefit of running computer vision applications on AWS Panorama at the edge compared to the cloud?","Reduced latency and bandwidth usage.","Unlimited storage capacity.","Simplified security management.","Automated software updates.","Processing data locally at the edge with AWS Panorama significantly reduces latency, as data doesn't need to be transmitted to the cloud for processing, and also lowers bandwidth costs."
"What role does AWS IoT Greengrass play in an AWS Panorama deployment?","Provides secure connectivity between the Panorama Appliance and AWS Cloud.","Manages user access control for the Panorama Appliance.","Handles data encryption for video streams.","Performs model training on the Panorama Appliance.","AWS IoT Greengrass facilitates secure communication and data exchange between the AWS Panorama Appliance and the AWS Cloud, allowing for remote management and monitoring."
"Which of the following is NOT a valid input source for an AWS Panorama application?","USB camera","RTSP stream","Recorded video file","Amazon S3 bucket","AWS Panorama doesn't directly accept video data from Amazon S3 buckets as a direct input source for real-time processing.  It typically uses live video streams from USB cameras or RTSP sources."
"What type of networking is required for the AWS Panorama Appliance to communicate with AWS cloud services?","Internet connectivity or a private network connection via AWS Direct Connect.","Bluetooth connectivity.","Serial connection.","Near-field communication (NFC).","The AWS Panorama Appliance needs a reliable network connection, either through the internet or via a private network like AWS Direct Connect, to communicate securely with AWS cloud services."
"Which of the following is a key benefit of using AWS Panorama for industrial automation?","Automated visual inspection and quality control.","Automated software deployment.","Automated infrastructure provisioning.","Automated database backups.","AWS Panorama enables automated visual inspection and quality control in industrial settings by analysing video feeds from cameras, helping to detect defects and ensure product quality."
"How does AWS Panorama handle model updates and application deployments?","Over-the-air updates managed through the AWS Management Console.","Manual updates via USB drive.","Direct updates through SSH access.","Updates are not supported.","AWS Panorama supports over-the-air updates, allowing for seamless deployment of new models and application versions from the AWS Management Console."
"What security measures are implemented in AWS Panorama to protect sensitive video data?","Encryption in transit and at rest, and secure boot processes.","Publicly accessible video feeds.","No encryption implemented.","Simple password protection.","AWS Panorama employs robust security measures, including encryption of data both in transit and at rest, along with secure boot processes to protect sensitive video data from unauthorised access."
"Which of the following is a common use case for AWS Panorama in the retail industry?","Inventory management and customer behaviour analysis.","Predictive maintenance of equipment.","Remote monitoring of construction sites.","Optimisation of agricultural yields.","In the retail industry, AWS Panorama can be used for tasks such as inventory management by tracking stock levels and analysing customer behaviour by monitoring foot traffic patterns."
"What are the typical steps involved in deploying an AWS Panorama application?","Create an application, upload models, deploy to the appliance, and monitor performance.","Download the application, configure the network, start the application.","Install the appliance, connect the camera, start the application.","Only connecting the camera, and starting the application.","Deploying an AWS Panorama application typically involves creating an application, uploading machine learning models, deploying the application to the Panorama Appliance, and then monitoring the application's performance."
"What is the purpose of the AWS Panorama Device SDK?","To develop custom hardware integrations with AWS Panorama.","To create AWS Lambda functions.","To manage AWS CloudWatch alarms.","To configure AWS IAM policies.","The AWS Panorama Device SDK is used to develop custom hardware integrations with AWS Panorama, allowing developers to integrate their own cameras and sensors with the Panorama platform."
"Which AWS service can be used to store and manage the raw video data captured by the AWS Panorama Appliance?","Amazon S3","Amazon RDS","Amazon DynamoDB","Amazon EC2","Amazon S3 (Simple Storage Service) is commonly used to store and manage the raw video data captured by the AWS Panorama Appliance, providing a scalable and cost-effective storage solution."
"What is the maximum number of concurrent video streams that an AWS Panorama Appliance can typically process?","Depends on the complexity of the application and the hardware configuration.","One stream at a time.","Unlimited streams.","Two streams at a time.","The maximum number of concurrent video streams an AWS Panorama Appliance can process depends on the complexity of the computer vision application and the hardware configuration of the appliance.  It's not a fixed number."
"How does AWS Panorama ensure data privacy and compliance with regulations such as GDPR?","By allowing for data anonymisation and processing at the edge.","By storing all data in a central database.","By ignoring data privacy regulations.","By sending all data to AWS without filtering.","AWS Panorama promotes data privacy and compliance by enabling data anonymisation and processing at the edge, minimising the need to send sensitive data to the cloud."
"Which of the following is NOT a typical output from an AWS Panorama application?","Real-time alerts based on detected anomalies.","Summarised reports on object detection and classification.","Complete raw video recordings.","Automated actions triggered by specific events.","While AWS Panorama can trigger automated actions and generate alerts and reports, it doesn't typically output complete raw video recordings; rather, it processes the video and extracts relevant information."
"What is the primary use case for AWS Panorama in the logistics and transportation industry?","Monitoring cargo and ensuring security in warehouses.","Optimising train schedules.","Tracking airline baggage.","Analysing weather patterns.","In the logistics and transportation industry, AWS Panorama can be used to monitor cargo, ensure security in warehouses and distribution centres, and improve overall operational efficiency."
"What role does a 'model package' play in an AWS Panorama application?","It contains the machine learning model and its associated metadata.","It contains the application code.","It contains the network configuration.","It contains the hardware drivers.","A 'model package' in AWS Panorama includes the machine learning model itself, along with the necessary metadata, such as input and output specifications, required for the Panorama Appliance to execute the model."
"Which programming language is commonly used for developing applications for AWS Panorama?","Python","Java","C++","JavaScript","Python is commonly used for developing computer vision applications for AWS Panorama due to its rich ecosystem of libraries and tools for machine learning and image processing."
"How does AWS Panorama handle network disruptions and intermittent connectivity?","It can continue processing data locally and synchronise results when connectivity is restored.","It stops processing data until connectivity is restored.","It automatically switches to a backup network.","It alerts the administrator.","AWS Panorama is designed to handle network disruptions by continuing to process data locally even when connectivity is intermittent, and then synchronising the results with the cloud once the connection is restored."
"Which of the following is a key consideration when choosing a camera for use with AWS Panorama?","Compatibility with the AWS Panorama Appliance and its operating system.","Camera price.","Camera brand.","Camera colour.","A key consideration is ensuring that the camera is compatible with the AWS Panorama Appliance, including its operating system and supported protocols, to ensure seamless integration."
"What is the purpose of the AWS Panorama console?","To manage and monitor AWS Panorama applications and appliances.","To develop machine learning models.","To store video data.","To manage AWS IAM users.","The AWS Panorama console provides a centralised interface for managing and monitoring AWS Panorama applications and appliances, allowing users to deploy, update, and monitor their computer vision solutions."
"How can you monitor the performance of an AWS Panorama application?","Using Amazon CloudWatch metrics and logs.","Using AWS CloudTrail logs.","Using AWS Config rules.","Using AWS Trusted Advisor recommendations.","The performance of an AWS Panorama application can be monitored using Amazon CloudWatch metrics and logs, allowing users to track resource utilisation, latency, and other key performance indicators."
"What is the significance of 'edge computing' in the context of AWS Panorama?","It allows for real-time processing of video data without relying on cloud connectivity.","It allows for storing the videos only in the cloud.","It allows for training ML model only in the cloud.","It allows for monitoring the edge without any video processing.","Edge computing, as enabled by AWS Panorama, allows for real-time processing of video data directly on the appliance without relying on constant cloud connectivity, reducing latency and bandwidth usage."
"What is the typical workflow for deploying a new computer vision model to AWS Panorama?","Train the model, package it, upload it to AWS Panorama, and deploy the application.","Download the model from AWS Marketplace, deploy the application.","Train the model on the Panorama appliance.","Train the model, deploy the application to EC2, and connect that EC2 to Panorama.","The typical workflow involves training the model, packaging it into a deployable format, uploading it to AWS Panorama, and then deploying the application to the Panorama Appliance."
"In AWS Panorama, what does the term 'application manifest' refer to?","A file that defines the application's dependencies and configuration.","A file that contains the source code of the application.","A file that stores the application's logs.","A file that contains the application's security credentials.","The 'application manifest' is a file that defines the application's dependencies, configuration parameters, and other metadata necessary for deploying and running the application on the AWS Panorama Appliance."
"What is the purpose of the 'AWS Panorama sample applications'?","To provide examples of how to develop and deploy computer vision applications.","To provide a collection of pre-trained machine learning models.","To provide a set of pre-configured AWS Panorama Appliances.","To provide a library of camera drivers.","The 'AWS Panorama sample applications' are designed to provide developers with practical examples and guidance on how to develop and deploy computer vision applications using the AWS Panorama platform."
"How does AWS Panorama integrate with existing camera infrastructure?","By supporting standard video protocols such as RTSP and USB.","By requiring proprietary camera interfaces.","By only supporting cloud-based cameras.","By only supporting Amazon branded cameras.","AWS Panorama integrates with existing camera infrastructure by supporting standard video protocols such as RTSP (Real Time Streaming Protocol) and USB, allowing for flexibility in choosing cameras."
"What is the benefit of using the AWS Panorama Appliance over a custom-built edge computing solution?","Simplified management, security, and integration with AWS services.","Lower cost.","Increased customisation options.","Faster processing speeds.","The AWS Panorama Appliance offers simplified management, enhanced security features, and seamless integration with other AWS services, making it easier to deploy and maintain computer vision applications compared to a custom-built solution."
"What is the role of the AWS Cloud in an AWS Panorama deployment?","Provides centralised management, model storage, and analytics capabilities.","Handles all video processing.","Provides power to the AWS Panorama appliance.","Provides only the Camera drivers to the appliance.","The AWS Cloud provides centralised management, model storage, and analytics capabilities for AWS Panorama, enabling remote monitoring, updates, and data analysis."
"Which of the following is NOT a valid way to interact with the AWS Panorama Appliance?","Using the AWS Management Console.","Using SSH.","Using the AWS CLI.","Using the AWS Panorama Application SDK.","Direct SSH access to the AWS Panorama Appliance is generally not recommended or supported for security reasons. Interactions are primarily managed through the AWS Management Console, AWS CLI, and AWS Panorama Application SDK."
"What type of data is typically processed by AWS Panorama applications?","Video streams from cameras.","Text data from sensors.","Audio data from microphones.","Geospatial data from GPS devices.","AWS Panorama applications are primarily designed to process video streams from cameras, analysing the visual data to extract insights and trigger actions."
"How does AWS Panorama contribute to improved operational efficiency in manufacturing?","By automating quality control and detecting defects in real-time.","By tracking employee attendance.","By managing inventory levels.","By automating machine maintenance schedules.","AWS Panorama contributes to improved operational efficiency in manufacturing by automating quality control processes and detecting defects in real-time, reducing manual inspection efforts and improving product quality."
"What is the purpose of the AWS Panorama 'device registration' process?","To securely connect the appliance to the AWS account and enable remote management.","To configure network settings on the appliance.","To install the operating system on the appliance.","To update the firmware on the appliance.","The 'device registration' process securely connects the AWS Panorama Appliance to your AWS account, enabling remote management and monitoring of the device through the AWS Management Console."
"Which of the following factors can influence the performance of an AWS Panorama application?","The complexity of the computer vision model, the resolution of the video stream, and the available network bandwidth.","The colour of the Panorama Appliance.","The brand of the Camera","The location of the Panorama Appliance","The performance of an AWS Panorama application can be influenced by factors such as the complexity of the computer vision model being used, the resolution of the video stream being processed, and the available network bandwidth for communication with the cloud."
"What are some common challenges in deploying computer vision applications at the edge that AWS Panorama addresses?","Managing hardware compatibility, ensuring security, and optimising model performance for resource-constrained devices.","Only ensuring the application is correctly deployed.","Only managing network bandwidth.","The cost of cameras.","AWS Panorama addresses challenges such as managing hardware compatibility, ensuring security, and optimising model performance for resource-constrained devices, making it easier to deploy computer vision applications at the edge."
"How can AWS Panorama be used to enhance safety and security in public spaces?","By detecting suspicious activities and alerting authorities in real-time.","By optimising traffic flow.","By managing parking spaces.","By controlling access to buildings.","AWS Panorama can be used to enhance safety and security in public spaces by detecting suspicious activities, such as unattended packages or unusual behaviour, and alerting authorities in real-time."
"What is a typical use case for AWS Panorama in the healthcare industry?","Monitoring patient safety and compliance with hygiene protocols.","Tracking medical supplies.","Managing patient records.","Scheduling appointments.","In the healthcare industry, AWS Panorama can be used to monitor patient safety, ensure compliance with hygiene protocols, and improve overall operational efficiency in hospitals and clinics."
"How does AWS Panorama help reduce costs associated with computer vision deployments?","By processing data locally, reducing bandwidth usage, and optimising resource utilisation.","By outsourcing all processing to the cloud.","By eliminating the need for cameras.","By eliminating the need for machine learning models.","AWS Panorama helps reduce costs by processing data locally at the edge, which reduces bandwidth usage, and by optimising resource utilisation on the Panorama Appliance."
"What is the role of AWS IAM (Identity and Access Management) in an AWS Panorama deployment?","To control access to AWS Panorama resources and manage user permissions.","To manage network settings on the Panorama Appliance.","To configure the operating system on the Panorama Appliance.","To manage the AWS Panorama Application itself.","AWS IAM is used to control access to AWS Panorama resources and manage user permissions, ensuring that only authorised users can access and manage the Panorama Appliance and its associated services."
"Which of the following is a benefit of using AWS Panorama with AWS CloudWatch?","Real-time monitoring of application performance and resource utilisation.","Automatically triggering Lambda function.","Automatically detecting a new camera.","Automatically sending alerts.","Integrating AWS Panorama with AWS CloudWatch enables real-time monitoring of application performance and resource utilisation, allowing users to identify and address potential issues quickly."
"What is the maximum frame rate that AWS Panorama appliance can process?","Depends on model complexity and hardware resources","10 frames per second","20 frames per second","60 frames per second","The maximum frame rate depends on the complexity of the model being used, the resolution of the video and the hardware resources on the appliance itself. It can vary."
"What is the typical power consumption of AWS Panorama Appliance?","Depends on usage","5W","10W","100W","The typical power consumption varies based on application usage, hardware resources and camera integration."
"Which AWS service is NOT directly integrated with AWS Panorama?","Amazon Comprehend","Amazon SageMaker","AWS IoT Greengrass","Amazon CloudWatch","Amazon Comprehend is focused on natural language processing whereas AWS Panorama focuses on Computer Vision. While the other AWS Services provide useful functions to AWS Panorama the use of Amazon Comprehend isn't integrated."
"What is the primary function of AWS Panorama?","To add computer vision capabilities to on-premises cameras.","To provide cloud storage for video recordings.","To offer serverless compute for image processing.","To manage AWS IoT devices.","AWS Panorama allows you to run computer vision applications on edge devices using existing IP cameras."
"Which AWS service is used to register an AWS Panorama Appliance?","AWS IoT Device Management","AWS Greengrass","AWS IAM","AWS CloudFormation","AWS IoT Device Management is used to register and manage Panorama Appliances."
"What type of applications can you deploy with AWS Panorama?","Computer vision applications","Web applications","Mobile applications","Database applications","AWS Panorama specialises in deploying and running computer vision applications."
"What is the purpose of the AWS Panorama Application SDK?","To build and deploy computer vision applications to Panorama Appliances","To monitor the health of Panorama Appliances","To manage the configuration of Panorama Appliances","To provision AWS infrastructure for Panorama","The Panorama Application SDK is used to develop and deploy computer vision applications specifically for Panorama Appliances."
"What is the role of AWS Panorama's 'model packaging' feature?","To optimise and package machine learning models for deployment on the appliance.","To encrypt the video streams captured by cameras.","To automatically train machine learning models.","To manage the deployment of applications to multiple appliances.","Model packaging optimises the ML model for deployment on the Panorama Appliance."
"Which programming language is commonly used for developing AWS Panorama applications?","Python","Java","C++","Go","Python is a common language used for Panorama Applications, specifically with frameworks like TensorFlow and MXNet."
"What is the purpose of the AWS Panorama Appliance?","To run computer vision applications at the edge.","To store video data in the cloud.","To train machine learning models in the cloud.","To manage network traffic between on-premises devices and AWS.","The Panorama Appliance is an on-premises device that executes computer vision applications locally."
"How does AWS Panorama handle network connectivity?","It requires a direct connection to AWS over the internet.","It can operate in a fully offline mode after initial configuration.","It uses AWS Direct Connect for secure connections.","It relies on local area network (LAN) connectivity for its operations and can use an internet connection for uploading data or updates.","AWS Panorama connects to the internet for updates, but primarily uses local network."
"What is a key benefit of using AWS Panorama for computer vision tasks?","Reduced latency by processing video data at the edge.","Unlimited storage for video recordings.","Free access to pre-trained machine learning models.","Automatic scaling of compute resources in the cloud.","Processing video at the edge reduces latency, making it ideal for real-time applications."
"Which AWS service can be integrated with AWS Panorama for storing and analysing video data?","Amazon S3","Amazon EC2","Amazon DynamoDB","Amazon Lambda","Amazon S3 can be used to store video data captured and processed by AWS Panorama."
"What is the typical use case for AWS Panorama in a retail environment?","Analysing customer behaviour and optimising store layouts.","Managing inventory levels.","Processing online transactions.","Providing customer support via video chat.","Panorama can be used to analyse customer behaviour in retail environments."
"What type of machine learning models can be deployed on AWS Panorama?","Models trained using TensorFlow, Apache MXNet, and PyTorch.","Only models trained using Amazon SageMaker.","Models specifically designed for mobile devices.","Models that use only CPU-based inference.","AWS Panorama supports models trained using TensorFlow, Apache MXNet, and PyTorch."
"How does AWS Panorama contribute to data privacy and security?","By processing video data locally, reducing the need to transmit sensitive information to the cloud.","By automatically anonymising video data before storage.","By providing encryption for all video streams.","By using facial recognition to identify and blur sensitive data.","Processing locally minimises the need to send sensitive data to the cloud."
"What is the role of the AWS Panorama console?","To manage and deploy computer vision applications to appliances.","To train machine learning models.","To store video recordings.","To monitor the network performance of the appliance.","The Panorama console allows you to manage and deploy applications."
"What is an 'AWS Panorama node'?","A building block for constructing computer vision pipelines within Panorama applications.","A physical server running AWS Panorama services.","A data storage location for video recordings.","A security mechanism for controlling access to the appliance.","A node is a component used to build the computer vision pipeline."
"How does AWS Panorama handle updates to deployed applications?","Updates are automatically deployed to the appliance from the AWS Panorama console.","Updates must be manually installed on the appliance.","Updates are pushed through AWS IoT Device Management.","Updates are managed through the AWS CLI.","Updates are deployed to the appliance from the Panorama console."
"What is the purpose of the 'AWS Panorama container'?","To encapsulate the computer vision application and its dependencies.","To store video recordings.","To provide a secure connection to the cloud.","To manage the network traffic between the appliance and the cameras.","Containers provide a consistent environment for applications and their dependencies."
"What type of video input does AWS Panorama typically support?","Real-time video streams from IP cameras.","Pre-recorded video files stored in Amazon S3.","Images uploaded directly to the AWS Panorama console.","Live streams from webcams.","AWS Panorama works with real-time video streams from IP cameras."
"How does AWS Panorama differ from AWS Rekognition?","AWS Panorama processes video at the edge, while AWS Rekognition processes images and video in the cloud.","AWS Panorama is free to use, while AWS Rekognition requires a subscription.","AWS Panorama supports only image recognition, while AWS Rekognition supports video analysis.","AWS Panorama is designed for training machine learning models, while AWS Rekognition is designed for inference.","Panorama processes locally, Rekognition processes in the cloud."
"What is the advantage of using AWS Panorama for industrial automation?","It enables real-time monitoring and analysis of production processes.","It automatically manages inventory levels.","It provides predictive maintenance for equipment.","It controls robotic arms on the factory floor.","Panorama enables real-time analysis of production processes."
"What is the maximum video resolution supported by AWS Panorama?","1080p","4K","720p","480p","Panorama supports up to 1080p resolution."
"How does AWS Panorama integrate with AWS IAM?","AWS IAM is used to control access to the AWS Panorama console and resources.","AWS IAM is used to manage access to the video data stored on the appliance.","AWS IAM is used to authenticate the appliance to the AWS cloud.","AWS IAM is not used with Panorama.","IAM controls access to the Panorama console and resources."
"What is the purpose of the AWS Panorama device certificate?","To authenticate the Panorama Appliance to the AWS cloud.","To encrypt the video streams captured by the cameras.","To authorise access to the Panorama console.","To manage software updates on the appliance.","The device certificate is used to authenticate the appliance."
"What is the function of AWS Panorama’s 'application manifest'?","It describes the application's dependencies and configuration to the Panorama Appliance.","It defines the security policies for the application.","It specifies the video input sources for the application.","It contains the machine learning model used by the application.","The application manifest defines the dependencies and configuration."
"What is a key consideration when choosing an IP camera for use with AWS Panorama?","Compatibility with the Panorama Appliance and supported video codecs.","The number of megapixels supported by the camera.","The price of the camera.","The camera's ability to operate in low-light conditions.","Compatibility with the appliance is key."
"What is the role of the AWS Panorama 'stream manager'?","To handle the ingestion and processing of video streams from the cameras.","To manage the deployment of applications to the appliance.","To store video recordings.","To monitor the network performance of the appliance.","The stream manager handles video stream ingestion and processing."
"How does AWS Panorama support debugging of computer vision applications?","By providing logging and monitoring tools in the AWS Panorama console.","By allowing remote debugging of the application on the appliance.","By automatically identifying and fixing errors in the code.","By simulating the application's behaviour in a virtual environment.","The AWS Panorama console offers logging and monitoring tools."
"What is a typical use case for AWS Panorama in transportation?","Monitoring traffic patterns and optimising traffic flow.","Managing logistics and supply chain operations.","Providing real-time navigation for drivers.","Tracking the location of vehicles.","Panorama can monitor traffic patterns."
"How does AWS Panorama handle different video codecs?","It supports a range of common video codecs, such as H.264 and H.265.","It requires all video streams to be converted to a specific codec.","It automatically detects and adapts to the video codec used by the camera.","It only supports video streams in the MJPEG format.","AWS Panorama can support H.264 and H.265."
"What is the primary advantage of using AWS Panorama for real-time object detection?","Low latency inference at the edge.","Free access to pre-trained object detection models.","Automatic scaling of compute resources.","Unlimited storage for video recordings.","Low latency is achieved through edge processing."
"Which AWS service is used to manage the machine learning models deployed to AWS Panorama?","Amazon SageMaker","Amazon S3","Amazon EC2","AWS Lambda","SageMaker is used to train and manage models."
"What type of network configuration is required for an AWS Panorama Appliance?","A connection to the local network and optionally an internet connection for updates.","A direct connection to AWS via AWS Direct Connect.","A VPN connection to a virtual private cloud (VPC) in AWS.","A wireless connection to the internet.","A local network connection is essential, and an internet connection helps with updates."
"What is the purpose of the AWS Panorama 'input stream'?","To define the source of the video stream for the application.","To specify the output format for the processed video.","To manage the security settings for the video stream.","To control the resolution and frame rate of the video stream.","The input stream defines the source of the video for processing."
"What is the recommended method for deploying updates to an AWS Panorama application?","Using the AWS Panorama console to push updates to the appliance.","Manually updating the application on the appliance via SSH.","Using AWS CodeDeploy to automate the deployment process.","Using AWS CloudFormation to manage the application deployment.","The Panorama console is the recommended way to deploy updates."
"What type of data can be extracted from video streams using AWS Panorama?","Object detections, classifications, and metadata.","Audio recordings and transcriptions.","User biometrics and facial recognition data.","GPS coordinates and location data.","Panorama can be used to extract object detections and classifications."
"What is a typical use case for AWS Panorama in the healthcare industry?","Monitoring patient activity and ensuring safety in hospitals.","Managing patient records and medical history.","Scheduling appointments and managing patient flow.","Processing medical images and diagnostic data.","Panorama can monitor patient activity in hospitals."
"How does AWS Panorama handle security vulnerabilities in deployed applications?","By providing security updates and patches through the AWS Panorama console.","By automatically scanning applications for vulnerabilities before deployment.","By isolating applications in secure containers to prevent exploits.","By relying on the camera manufacturers to provide security updates.","Security updates and patches are deployed through the Panorama console."
"What is the purpose of the AWS Panorama 'output stream'?","To define where the processed video data is sent (e.g., to Amazon S3 or a local dashboard).","To specify the video input source for the application.","To manage the security settings for the video stream.","To control the resolution and frame rate of the video stream.","The output stream defines where the processed data is sent."
"What is a key advantage of using AWS Panorama over cloud-based video analytics?","Reduced latency and improved real-time performance.","Lower costs for video storage and processing.","Greater flexibility and customisation of video analytics algorithms.","Simplified deployment and management of video analytics applications.","Processing data at the edge lowers latency."
"Which machine learning frameworks are officially supported by AWS Panorama?","TensorFlow, Apache MXNet, and PyTorch","Caffe, Theano, and Torch","Scikit-learn, Keras, and Pandas","OpenCV, NumPy, and SciPy","TensorFlow, MXNet and Pytorch are the frameworks that are officially supported."
"What role does Amazon S3 play in a typical AWS Panorama workflow?","Storing raw video footage and processed output from Panorama applications.","Hosting the computer vision applications deployed to Panorama Appliances.","Serving as the primary compute environment for Panorama applications.","Providing a content delivery network (CDN) for video streams.","S3 is used to store both the raw footage and the processed output."
"What is the primary benefit of using AWS Panorama in a smart city application?","Enabling real-time monitoring and analysis of urban environments.","Providing free Wi-Fi access to residents.","Managing energy consumption in public buildings.","Automating waste collection and recycling processes.","Panorama enables real-time monitoring in smart city applications."
"How does AWS Panorama address challenges related to bandwidth and internet connectivity?","By processing video data locally on the edge appliance.","By automatically compressing video streams to reduce bandwidth usage.","By using AWS Direct Connect to establish dedicated network connections.","By caching video data locally to reduce reliance on the internet.","Processing locally reduces the bandwidth needed to be used as very little data needs to be transmitted to the cloud."
"What is the AWS Panorama 'application version'?","A way to manage changes and updates to a Panorama application deployment.","A measure of the application's performance and accuracy.","A pricing tier for the application.","A security classification for the application.","It's a mechanism to manage changes and updates to Panorama applications."
"How can developers test their AWS Panorama applications before deployment?","By using the AWS Panorama Appliance Simulator to simulate the appliance environment.","By deploying the application to a test environment in the AWS cloud.","By running the application on a local computer with a connected IP camera.","By using the AWS Cloud9 IDE to debug the application remotely.","The Appliance Simulator is used to simulate the environment."
"What is the purpose of the AWS Panorama 'data pre-processing' node in an application pipeline?","To transform and prepare video data for input to the machine learning model.","To manage the storage of video data.","To monitor the performance of the application.","To encrypt the video data for security purposes.","Data Pre-processing prepares video data for use in ML models."
"In an AWS Panorama application, how is inference typically performed?","On the AWS Panorama Appliance using the deployed machine learning model.","In the AWS cloud using Amazon SageMaker Inference.","On a separate GPU-powered server in the local network.","On the IP camera itself using built-in processing capabilities.","Inference takes place locally on the Panorama appliance."
"What is a typical use case for AWS Panorama in supply chain management?","Tracking goods and materials as they move through the supply chain.","Managing inventory levels in warehouses.","Optimising delivery routes and schedules.","Coordinating production schedules across multiple factories.","It is used to track goods and materials in the supply chain."
"Which of the following is a recommended best practice for optimising AWS Panorama application performance?","Use optimised machine learning models that are compatible with the Panorama Appliance.","Always use the highest possible video resolution for analysis.","Store all video data in Amazon S3 for long-term retention.","Stream video data directly to the AWS cloud for processing.","Using optimised ML models improves performance."
