"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Polly, what is the primary function of a lexicon?","To customise the pronunciation of specific words","To control the pitch of the generated speech","To adjust the speaking rate","To add background music to the speech","A lexicon in Amazon Polly is used to customise the pronunciation of specific words, which is particularly useful for proper nouns, acronyms, or words with unusual pronunciations."
"In Amazon Polly, which SSML tag is used to control the volume of the generated speech?","<volume>","<pitch>","<rate>","<emphasis>","The <volume> tag in SSML is used to control the loudness or softness of the speech. It allows you to adjust the volume level to suit your needs."
"In Amazon Polly, what is the purpose of the 'SpeechMarkTypes' parameter when using the 'SynthesizeSpeech' API?","To specify the types of speech marks to generate (e.g., sentence, word, phoneme, viseme)","To set the overall tone of the generated speech","To define the language of the output text","To control the maximum duration of the speech synthesis","The 'SpeechMarkTypes' parameter allows you to specify which types of speech marks (sentence, word, phoneme, viseme) should be included in the output, providing precise timing information for each element."
"In Amazon Polly, which of the following is NOT a supported output format?","MP4","MP3","PCM","Ogg Vorbis","Amazon Polly supports MP3, PCM, and Ogg Vorbis output formats. MP4 is not a supported format for speech synthesis."
"In Amazon Polly, how can you ensure that Polly uses a specific voice for speech synthesis?","By specifying the 'VoiceId' parameter in the 'SynthesizeSpeech' API call","By setting a global voice preference in the AWS Management Console","By tagging the text with a specific voice identifier","By using a custom lexicon with voice assignments","The 'VoiceId' parameter in the 'SynthesizeSpeech' API call is used to specify the voice to be used for synthesizing the speech. This ensures that Polly uses the desired voice."
"In Amazon Polly, what is the maximum text character limit for the 'Text' parameter in the 'SynthesizeSpeech' API?","3000 characters","1000 characters","5000 characters","10000 characters","The 'Text' parameter in the 'SynthesizeSpeech' API has a maximum limit of 3000 characters. For longer texts, you need to break them into smaller chunks."
"In Amazon Polly, which SSML tag is used to add a pause in the speech?","<break>","<pause>","<silence>","<delay>","The <break> tag in SSML is used to insert a pause in the speech. You can control the duration of the pause using the 'time' attribute."
"In Amazon Polly, what is the purpose of the 'Engine' parameter in the 'SynthesizeSpeech' API?","To select between the 'standard' and 'neural' engines for voice synthesis","To specify the language of the input text","To adjust the audio sampling rate","To enable or disable SSML processing","The 'Engine' parameter allows you to choose between the 'standard' and 'neural' engines. The neural engine provides more natural-sounding voices but may not be available for all voices or regions."
"In Amazon Polly, how can you programmatically discover the available voices in a specific AWS region?","By calling the 'DescribeVoices' API","By reading the Polly documentation for the region","By using the AWS CLI to list available voices","By checking the AWS Management Console","The 'DescribeVoices' API allows you to programmatically retrieve a list of available voices, including their IDs, languages, and other characteristics, in a specific AWS region."
"In Amazon Polly, which SSML tag is used to emphasise certain words or phrases?","<emphasis>","<prosody>","<speak>","<say-as>","The <emphasis> tag in SSML is used to add emphasis to specific words or phrases, making them stand out in the synthesized speech."
"In Amazon Polly, what is the purpose of using Speech Synthesis Markup Language (SSML)?","To control various aspects of speech such as pronunciation, pitch, and rate","To define the overall structure of the audio file","To encrypt the audio data for secure transmission","To add digital signatures to the audio file","SSML allows developers to control various aspects of the synthesized speech, including pronunciation, pitch, rate, and volume, providing a richer and more customized experience."
"In Amazon Polly, which AWS service can be used to store and manage custom lexicons?","Amazon S3","Amazon DynamoDB","Amazon RDS","Amazon Glacier","Amazon S3 is commonly used to store and manage custom lexicons for Amazon Polly. You can store the lexicon files in an S3 bucket and reference them in your Polly API calls."
"In Amazon Polly, what is the 'text-type' parameter used for in the SynthesizeSpeech API?","To specify whether the input text is plain text or SSML","To define the font style of the output text","To set the encoding of the input text","To control the language of the output speech","The 'text-type' parameter is used to indicate whether the input text is plain text or SSML. This allows Polly to correctly interpret and process the input."
"In Amazon Polly, which of the following is a valid use case for Viseme ID speech marks?","Synchronising lip movements in animated characters","Generating subtitles for audio files","Analysing the sentiment of the generated speech","Creating audio transcripts","Viseme ID speech marks provide information about the visual representation of speech sounds, making them ideal for synchronising lip movements in animated characters."
"In Amazon Polly, how does Amazon Polly handle words that are not present in its dictionary?","It uses a phonetic approximation based on the language","It skips the word, resulting in a pause","It throws an error and stops the synthesis","It attempts to translate the word to a known language","When Amazon Polly encounters a word that is not in its dictionary, it uses a phonetic approximation based on the language to pronounce the word."
"In Amazon Polly, what is the maximum size limit for a custom lexicon file?","100KB","50KB","1MB","5MB","The maximum size limit for a custom lexicon file in Amazon Polly is 100KB. This ensures efficient processing and management of lexicon data."
"In Amazon Polly, which SSML tag can be used to specify a different language for a specific section of text?","<lang>","<language>","<voice>","<say-as>","The <lang> tag in SSML is used to specify a different language for a specific section of text, allowing you to include words or phrases from other languages in your synthesized speech."
"In Amazon Polly, what is the purpose of the 'sample-rate' parameter in the SynthesizeSpeech API?","To control the audio quality and file size of the output","To adjust the speaking rate of the voice","To set the pitch of the synthesized speech","To add background noise to the audio","The 'sample-rate' parameter is used to control the audio quality and file size of the output. Higher sample rates result in better audio quality but also larger file sizes."
"In Amazon Polly, how can you monitor the usage and performance of Amazon Polly in your AWS account?","Using Amazon CloudWatch metrics","By checking the AWS Service Health Dashboard","By reviewing the AWS Cost and Usage Report","By contacting AWS Support","Amazon CloudWatch provides metrics for monitoring the usage and performance of Amazon Polly, allowing you to track key metrics and set up alarms for potential issues."
"In Amazon Polly, which SSML tag is used to control the speaking rate of the synthesized speech?","<prosody rate>","<speak rate>","<voice rate>","<say-as rate>","The <prosody rate> tag in SSML is used to control the speaking rate of the synthesized speech. You can use it to speed up or slow down the speech as needed."
"In Amazon Polly, what is the purpose of the 'voice' tag in SSML?","To specify the voice to be used for synthesizing the speech","To control the volume of the generated speech","To adjust the speaking rate","To add background music to the speech","The 'voice' tag in SSML is used to specify the voice to be used for synthesizing the speech, allowing you to select a specific voice from the available Polly voices."
"In Amazon Polly, which of the following is a valid value for the 'type' attribute of the 'say-as' tag?","date","volume","rate","pitch","The 'date' value for the 'type' attribute of the 'say-as' tag is used to specify that the text should be interpreted as a date."
"In Amazon Polly, what is the purpose of the 'get-lexicon' command in the AWS CLI?","To retrieve information about a specific lexicon","To create a new lexicon","To delete an existing lexicon","To update an existing lexicon","The 'get-lexicon' command in the AWS CLI is used to retrieve information about a specific lexicon, such as its content and metadata."
"In Amazon Polly, which of the following is a valid way to improve the pronunciation of specific words?","Using a custom lexicon","Adjusting the overall pitch of the voice","Slowing down the speaking rate","Adding pauses before and after the word","Using a custom lexicon allows you to define the specific pronunciation of words, overriding the default pronunciation used by Amazon Polly."
"In Amazon Polly, what is the purpose of the 'describe-voices' command in the AWS CLI?","To list the available voices in a specific region","To synthesize speech from text","To create a new lexicon","To delete an existing lexicon","The 'describe-voices' command in the AWS CLI is used to list the available voices in a specific region, including their IDs, languages, and other characteristics."
"In Amazon Polly, which of the following is a valid SSML tag for controlling the pronunciation of a word?","<phoneme>","<say>","<pronounce>","<spell>","The '<phoneme>' tag is a valid SSML tag for controlling the pronunciation of a word."
"In Amazon Polly, what is the purpose of the 'delete-lexicon' command in the AWS CLI?","To delete an existing lexicon","To create a new lexicon","To retrieve information about a specific lexicon","To update an existing lexicon","The 'delete-lexicon' command in the AWS CLI is used to delete an existing lexicon from your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a break or pause?","<break>","<pause>","<silence>","<delay>","The '<break>' tag is a valid SSML tag for inserting a break or pause."
"In Amazon Polly, what is the purpose of the 'update-lexicon' command in the AWS CLI?","To update an existing lexicon","To create a new lexicon","To retrieve information about a specific lexicon","To delete an existing lexicon","The 'update-lexicon' command in the AWS CLI is used to update an existing lexicon in your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for controlling the emphasis of a word?","<emphasis>","<strong>","<bold>","<important>","The '<emphasis>' tag is a valid SSML tag for controlling the emphasis of a word."
"In Amazon Polly, what is the purpose of the 'SynthesizeSpeech' API call?","To convert text into speech","To convert speech into text","To translate text from one language to another","To identify the language of the input text","The 'SynthesizeSpeech' API call is used to convert text into speech using Amazon Polly."
"In Amazon Polly, which of the following is a valid way to specify the output format when using the 'SynthesizeSpeech' API?","Using the 'OutputFormat' parameter","Using the 'Format' parameter","Using the 'AudioFormat' parameter","Using the 'Type' parameter","The 'OutputFormat' parameter is used to specify the output format when using the 'SynthesizeSpeech' API."
"In Amazon Polly, what is the purpose of the 'create-lexicon' command in the AWS CLI?","To create a new lexicon","To delete an existing lexicon","To retrieve information about a specific lexicon","To update an existing lexicon","The 'create-lexicon' command in the AWS CLI is used to create a new lexicon in your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for controlling the pitch of the synthesized speech?","<prosody pitch>","<speak pitch>","<voice pitch>","<say-as pitch>","The '<prosody pitch>' tag is a valid SSML tag for controlling the pitch of the synthesized speech."
"In Amazon Polly, what is the purpose of the 'ListLexicons' API call?","To list all available lexicons","To retrieve information about a specific lexicon","To create a new lexicon","To delete an existing lexicon","The 'ListLexicons' API call is used to list all available lexicons in your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a telephone number?","<say-as interpret-as='telephone'>","<phone>","<telephone>","<number type='telephone'>","The '<say-as interpret-as='telephone'>' tag is a valid SSML tag for inserting a telephone number."
"In Amazon Polly, what is the purpose of the 'LanguageCode' parameter in the 'SynthesizeSpeech' API?","To specify the language of the input text","To specify the voice to be used for synthesizing the speech","To control the volume of the generated speech","To adjust the speaking rate","The 'LanguageCode' parameter is used to specify the language of the input text."
"In Amazon Polly, which of the following is a valid SSML tag for inserting an acronym?","<acronym>","<abbr>","<say-as interpret-as='acronym'>","<short>","The '<say-as interpret-as='acronym'>' tag is a valid SSML tag for inserting an acronym."
"In Amazon Polly, what is the purpose of the 'LexiconName' parameter in the 'SynthesizeSpeech' API?","To specify the lexicon to be used for synthesizing the speech","To specify the voice to be used for synthesizing the speech","To control the volume of the generated speech","To adjust the speaking rate","The 'LexiconName' parameter is used to specify the lexicon to be used for synthesizing the speech."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a date?","<say-as interpret-as='date' format='mdy'>","<date format='mdy'>","<insert date format='mdy'>","<date type='mdy'>","The '<say-as interpret-as='date' format='mdy'>' tag is a valid SSML tag for inserting a date."
"In Amazon Polly, what is the purpose of the 'Text' parameter in the 'SynthesizeSpeech' API?","To provide the text to be synthesized","To specify the output format","To specify the voice to be used","To control the volume of the generated speech","The 'Text' parameter is used to provide the text that you want to be synthesized into speech."
"In Amazon Polly, which of the following is a valid value for the 'Engine' parameter in the 'SynthesizeSpeech' API?","neural","standard","both","none","The valid values for the 'Engine' parameter in the 'SynthesizeSpeech' API are 'standard' and 'neural'."
"In Amazon Polly, what is the purpose of the 'SpeechMark' element in the response from the 'SynthesizeSpeech' API?","To provide timing information about the synthesized speech","To provide the synthesized speech itself","To provide information about the voice used","To provide error messages","The 'SpeechMark' element provides timing information about the synthesized speech, such as the start and end times of words and sentences."
"In Amazon Polly, which of the following is a valid way to specify the language of the lexicon?","Using the 'LanguageCode' parameter","Using the 'Language' parameter","Using the 'Code' parameter","Using the 'LexiconLanguage' parameter","The 'LanguageCode' parameter is used to specify the language of the lexicon."
"In Amazon Polly, what is the purpose of the 'Content' element in the response from the 'GetLexicon' API?","To provide the content of the lexicon","To provide the name of the lexicon","To provide the language of the lexicon","To provide the size of the lexicon","The 'Content' element provides the actual content of the lexicon, which is a list of words and their pronunciations."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a strong emphasis?","<emphasis level='strong'>","<strong>","<bold>","<say-as emphasis='strong'>","The '<emphasis level='strong'>' tag is a valid SSML tag for inserting a strong emphasis."
"In Amazon Polly, what is the purpose of the 'Size' element in the response from the 'GetLexicon' API?","To provide the size of the lexicon in bytes","To provide the number of words in the lexicon","To provide the number of languages supported by the lexicon","To provide the number of voices that can use the lexicon","The 'Size' element provides the size of the lexicon in bytes."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a sentence break?","<p>","<s>","<break strength='x-strong'>","<sentence>","The '<s>' tag is a valid SSML tag for inserting a sentence break."
"In Amazon Polly, what is the purpose of the 'LastModified' element in the response from the 'GetLexicon' API?","To provide the date and time when the lexicon was last modified","To provide the date and time when the lexicon was created","To provide the date and time when the lexicon was last accessed","To provide the date and time when the lexicon will expire","The 'LastModified' element provides the date and time when the lexicon was last modified."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a paragraph break?","<p>","<s>","<break strength='x-strong'>","<paragraph>","The '<p>' tag is a valid SSML tag for inserting a paragraph break."
"In Amazon Polly, what is the purpose of the 'DescribeVoices' API call?","To retrieve a list of available voices","To synthesize speech from text","To create a new lexicon","To delete an existing lexicon","The 'DescribeVoices' API call is used to retrieve a list of available voices, including their IDs, languages, and other characteristics."
"In Amazon Polly, which of the following is a valid way to specify the voice to be used for synthesizing speech?","Using the 'VoiceId' parameter in the 'SynthesizeSpeech' API call","Using the 'Voice' parameter in the 'SynthesizeSpeech' API call","Using the 'Name' parameter in the 'SynthesizeSpeech' API call","Using the 'Speaker' parameter in the 'SynthesizeSpeech' API call","The 'VoiceId' parameter in the 'SynthesizeSpeech' API call is used to specify the voice to be used for synthesizing speech."
"In Amazon Polly, what is the purpose of the 'LexiconAttributes' element in the response from the 'GetLexicon' API?","To provide attributes of the lexicon, such as its language code and size","To provide the content of the lexicon","To provide the name of the lexicon","To provide the date and time when the lexicon was last modified","The 'LexiconAttributes' element provides attributes of the lexicon, such as its language code and size."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a phone number?","<say-as interpret-as='telephone'>","<phone>","<telephone>","<number type='telephone'>","The '<say-as interpret-as='telephone'>' tag is a valid SSML tag for inserting a phone number."
"In Amazon Polly, what is the purpose of the 'ApplyLexicon' parameter in the 'SynthesizeSpeech' API?","To specify whether to apply the lexicon to the text","To specify the name of the lexicon to apply","To specify the language of the lexicon to apply","To specify the version of the lexicon to apply","The 'LexiconName' parameter is used to specify the name of the lexicon to apply to the text. There is no 'ApplyLexicon' parameter."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a currency amount?","<say-as interpret-as='currency'>","<currency>","<money>","<amount type='currency'>","The '<say-as interpret-as='currency'>' tag is a valid SSML tag for inserting a currency amount."
"In Amazon Polly, what is the purpose of the 'ResponseMetadata' element in the response from the 'SynthesizeSpeech' API?","To provide metadata about the response, such as the request ID and HTTP status code","To provide the synthesized speech itself","To provide information about the voice used","To provide error messages","The 'ResponseMetadata' element provides metadata about the response, such as the request ID and HTTP status code."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a time?","<say-as interpret-as='time' format='hms12'>","<time format='hms12'>","<insert time format='hms12'>","<clock type='hms12'>","The '<say-as interpret-as='time' format='hms12'>' tag is a valid SSML tag for inserting a time."
"In Amazon Polly, what is the purpose of the 'RequestCharacters' metric in CloudWatch?","To track the number of characters processed by Amazon Polly","To track the number of API requests made to Amazon Polly","To track the number of errors encountered by Amazon Polly","To track the average latency of Amazon Polly","The 'RequestCharacters' metric is used to track the number of characters processed by Amazon Polly."
"In Amazon Polly, which of the following is a valid SSML tag for inserting an address?","<say-as interpret-as='address'>","<address>","<location>","<place type='address'>","There is no specific SSML tag for inserting an address. You would typically use plain text or combine other SSML tags."
"In Amazon Polly, what is the purpose of the 'InvalidLexicon' error in Amazon Polly?","The 'InvalidLexicon' error indicates that the lexicon is invalid or contains errors","The 'InvalidLexicon' error indicates that the lexicon does not exist","The 'InvalidLexicon' error indicates that the user does not have permission to access the lexicon","The 'InvalidLexicon' error indicates that the lexicon is too large","The 'InvalidLexicon' error indicates that the lexicon is invalid or contains errors."
"In Amazon Polly, what is the purpose of the 'Marks' element in the response from the 'SynthesizeSpeech' API when using Speech Marks?","To provide the timing information for words, sentences, or other elements in the synthesized speech","To provide the synthesized speech itself","To provide information about the voice used","To provide error messages","The 'Marks' element provides the timing information for words, sentences, or other elements in the synthesized speech when using Speech Marks."
"In Amazon Polly, which of the following is a valid way to specify the type of speech marks to generate when using the 'SpeechMarkTypes' parameter?","By specifying 'word', 'sentence', 'viseme', or 'phoneme'","By specifying 'word', 'sentence', 'visual', or 'sound'","By specifying 'text', 'audio', 'visual', or 'timing'","By specifying 'all', 'none', 'some', or 'custom'","The valid values for the 'SpeechMarkTypes' parameter are 'word', 'sentence', 'viseme', and 'phoneme'."
"In Amazon Polly, what is the purpose of the 'LexiconNotFound' error in Amazon Polly?","The 'LexiconNotFound' error indicates that the specified lexicon does not exist","The 'LexiconNotFound' error indicates that the lexicon is invalid or contains errors","The 'LexiconNotFound' error indicates that the user does not have permission to access the lexicon","The 'LexiconNotFound' error indicates that the lexicon is too large","The 'LexiconNotFound' error indicates that the specified lexicon does not exist."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a date with the year, month, and day?","<say-as interpret-as='date' format='ymd'>","<date format='ymd'>","<insert date format='ymd'>","<date type='ymd'>","The '<say-as interpret-as='date' format='ymd'>' tag is a valid SSML tag for inserting a date with the year, month, and day."
"In Amazon Polly, what is the purpose of the 'MaxLexiconReachedException' error in Amazon Polly?","The 'MaxLexiconReachedException' error indicates that the user has reached the maximum number of lexicons allowed","The 'MaxLexiconReachedException' error indicates that the lexicon is too large","The 'MaxLexiconReachedException' error indicates that the user does not have permission to create more lexicons","The 'MaxLexiconReachedException' error indicates that the lexicon name is too long","The 'MaxLexiconReachedException' error indicates that the user has reached the maximum number of lexicons allowed."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a cardinal number?","<say-as interpret-as='cardinal'>","<cardinal>","<number type='cardinal'>","<num>","The '<say-as interpret-as='cardinal'>' tag is a valid SSML tag for inserting a cardinal number."
"In Amazon Polly, what is the purpose of the 'LexiconSizeExceededException' error in Amazon Polly?","The 'LexiconSizeExceededException' error indicates that the lexicon file is too large","The 'LexiconSizeExceededException' error indicates that the lexicon contains too many words","The 'LexiconSizeExceededException' error indicates that the lexicon name is too long","The 'LexiconSizeExceededException' error indicates that the user has reached the maximum number of lexicons allowed","The 'LexiconSizeExceededException' error indicates that the lexicon file is too large."
"In Amazon Polly, which of the following is a valid SSML tag for inserting an ordinal number?","<say-as interpret-as='ordinal'>","<ordinal>","<number type='ordinal'>","<order>","The '<say-as interpret-as='ordinal'>' tag is a valid SSML tag for inserting an ordinal number."
"In Amazon Polly, what is the purpose of the 'ServiceFailureException' error in Amazon Polly?","The 'ServiceFailureException' error indicates that there was a general service failure","The 'ServiceFailureException' error indicates that the user has exceeded the API request rate limit","The 'ServiceFailureException' error indicates that the user does not have permission to access the service","The 'ServiceFailureException' error indicates that the input text is too long","The 'ServiceFailureException' error indicates that there was a general service failure."
"In Amazon Polly, which of the following is a valid SSML tag for inserting an exponent?","<say-as interpret-as='expletive'>","<sup>","<exponent>","<power>","The '<say-as interpret-as='expletive'>' tag is used to mask words, not to insert an exponent. There is no specific SSML tag for inserting an exponent."
"In Amazon Polly, what is the purpose of the 'SynthesizeSpeech' API call?","To convert text into speech","To convert speech into text","To translate text from one language to another","To identify the language of the input text","The 'SynthesizeSpeech' API call is used to convert text into speech using Amazon Polly."
"In Amazon Polly, which of the following is a valid way to specify the output format when using the 'SynthesizeSpeech' API?","Using the 'OutputFormat' parameter","Using the 'Format' parameter","Using the 'AudioFormat' parameter","Using the 'Type' parameter","The 'OutputFormat' parameter is used to specify the output format when using the 'SynthesizeSpeech' API."
"In Amazon Polly, what is the purpose of the 'create-lexicon' command in the AWS CLI?","To create a new lexicon","To delete an existing lexicon","To retrieve information about a specific lexicon","To update an existing lexicon","The 'create-lexicon' command in the AWS CLI is used to create a new lexicon in your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for controlling the pitch of the synthesized speech?","<prosody pitch>","<speak pitch>","<voice pitch>","<say-as pitch>","The '<prosody pitch>' tag is a valid SSML tag for controlling the pitch of the synthesized speech."
"In Amazon Polly, what is the purpose of the 'ListLexicons' API call?","To list all available lexicons","To retrieve information about a specific lexicon","To create a new lexicon","To delete an existing lexicon","The 'ListLexicons' API call is used to list all available lexicons in your AWS account."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a telephone number?","<say-as interpret-as='telephone'>","<telephone>","<number type='telephone'>","<phone>","The '<say-as interpret-as='telephone'>' tag is a valid SSML tag for inserting a telephone number."
"In Amazon Polly, what is the purpose of the 'LanguageCode' parameter in the 'SynthesizeSpeech' API?","To specify the language of the input text","To specify the voice to be used for synthesizing the speech","To control the volume of the generated speech","To adjust the speaking rate","The 'LanguageCode' parameter is used to specify the language of the input text."
"In Amazon Polly, which of the following is a valid SSML tag for inserting an acronym?","<acronym>","<abbr>","<say-as interpret-as='acronym'>","<short>","The '<say-as interpret-as='acronym'>' tag is a valid SSML tag for inserting an acronym."
"In Amazon Polly, what is the purpose of the 'LexiconName' parameter in the 'SynthesizeSpeech' API?","To specify the lexicon to be used for synthesizing the speech","To specify the voice to be used for synthesizing the speech","To control the volume of the generated speech","To adjust the speaking rate","The 'LexiconName' parameter is used to specify the lexicon to be used for synthesizing the speech."
"In Amazon Polly, which of the following is a valid SSML tag for inserting a date?","<say-as interpret-as='date' format='mdy'>","<date format='mdy'>","<insert date format='mdy'>","<date type='mdy'>","The '<say-as interpret-as='date' format='mdy'>' tag is a valid SSML tag for inserting a date."
"In Amazon Polly, what is the purpose of the 'Text' parameter in the 'SynthesizeSpeech' API?","To provide the text to be synthesized","To specify the output format","To specify the voice to be used","To control the volume of the generated speech","The 'Text' parameter is used to provide the text that you want to be synthesized into speech."
"In Amazon Polly, which of the following is a valid value for the 'Engine' parameter in the 'SynthesizeSpeech' API?","neural","standard","both","none","The valid values for the 'Engine' parameter in the 'SynthesizeSpeech' API are 'standard' and 'neural'."
"In Amazon Polly, what is the purpose of the 'SpeechMark' element in the response from the 'SynthesizeSpeech' API?","To provide timing information about the synthesized speech","To provide the synthesized speech itself","To provide information about the voice used","To provide error messages","The 'SpeechMark' element provides timing information about the synthesized speech, such as the start and end times of words and sentences."
"In Amazon Polly, which of the following is a valid way to specify the language of the lexicon?","Using the 'LanguageCode' parameter","Using the 'Language' parameter","Using the 'Code' parameter","Using the 'LexiconLanguage' parameter","The 'LanguageCode' parameter is used to specify the language of the lexicon."
