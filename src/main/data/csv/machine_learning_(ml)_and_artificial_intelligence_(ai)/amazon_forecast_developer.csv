"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Forecast, what type of algorithm is AutoPredictor designed to automatically select and optimise?","Time series forecasting algorithms","Image recognition algorithms","Natural language processing algorithms","Machine translation algorithms","AutoPredictor automates the selection and optimisation of time series forecasting algorithms for a given dataset."
"What is the primary purpose of the 'Target Time Series' dataset in Amazon Forecast?","To provide the historical data you want to forecast","To provide supplemental information like holidays","To define the item categories","To specify the forecast horizon","The 'Target Time Series' dataset contains the historical time series data that you want to predict."
"Which of the following dataset import formats is NOT supported by Amazon Forecast?","CSV","JSON","Parquet","TSV","Amazon Forecast supports CSV, JSON and Parquet but NOT TSV."
"What is a key advantage of using Amazon Forecast's DeepAR+ algorithm?","Handles complex dependencies and seasonality well","Simplifies data preparation","Requires minimal historical data","Provides explainability for predictions","DeepAR+ is a recurrent neural network-based algorithm that excels at modelling complex dependencies and seasonality in time series data."
"In Amazon Forecast, what is the purpose of the 'Related Time Series' dataset?","To provide additional information that can influence the forecast","To define the forecast evaluation metrics","To specify the data frequency","To partition the data for training and testing","The 'Related Time Series' dataset contains time series data that may influence the target time series, such as price promotions or weather data."
"What does the 'forecast horizon' define in Amazon Forecast?","The number of time steps into the future the model will predict","The time it takes to train the model","The period of historical data used for training","The acceptable error margin for predictions","The 'forecast horizon' specifies how many time steps into the future you want to generate forecasts."
"Which of the following is NOT a valid forecast type in Amazon Forecast?","Point forecast","Probability forecast","Interval forecast","Causal forecast","A causal forecast is not a standard forecast type in Amazon Forecast, point, probability and interval forecasts are available."
"What type of data transformations does Amazon Forecast apply automatically?","Handling missing values and outliers","Converting categorical features to numerical features","Feature scaling","Text normalisation","Amazon Forecast automatically handles missing values and outliers in the time series data."
"How does Amazon Forecast handle cold start problems (new items with little or no history)?","By leveraging related time series data and item metadata","By using a simple moving average","By excluding these items from the forecast","By imputing missing values with the mean","Amazon Forecast can handle cold start problems by using related time series data and item metadata to make predictions for new items."
"Which Amazon Forecast feature allows you to evaluate the accuracy of your forecasting model?","Backtesting","Explainability","AutoML","Data visualisation","Backtesting allows you to evaluate the accuracy of your forecasting model by comparing its predictions to actual historical data."
"What is the purpose of defining 'Item ID' in Amazon Forecast?","To uniquely identify each item being forecast","To specify the timestamp format","To define the target variable","To set the forecast frequency","The 'Item ID' is used to uniquely identify each individual item for which you are generating a forecast."
"Which of the following is NOT a typical use case for Amazon Forecast?","Predicting future sales demand","Forecasting inventory levels","Detecting fraudulent transactions","Optimising resource allocation","Detecting fraudulent transactions is not a common use case for Amazon Forecast; it's designed for time series forecasting, not anomaly detection."
"What is the role of AWS Identity and Access Management (IAM) in Amazon Forecast?","To control access to Forecast resources and data","To manage the lifecycle of datasets","To monitor the performance of forecasting models","To automate the training process","IAM is used to control access to Forecast resources and data, ensuring that only authorised users and roles can access and modify them."
"What is the purpose of 'explainability' in Amazon Forecast?","To understand which factors influence the forecast","To visualise the forecast results","To automate the model training process","To improve the accuracy of the forecast","Explainability helps you understand which factors (e.g., price, promotions) have the greatest influence on the forecast."
"What is the maximum number of predictors you can compare using the 'What-If Analysis' feature in Amazon Forecast?","5","2","10","Unlimited","You can compare up to 5 predictors using the 'What-If Analysis' feature."
"Which of the following is the most suitable data frequency for using Amazon Forecast?","Hourly","Daily","Annually","Ad-hoc","Amazon Forecast is best suited to data with regular frequency, such as hourly or daily, because it is designed to analyse consistent time-series trends."
"Which evaluation metric is used by Amazon Forecast to assess the accuracy of the model when the actual value is 0?","Weighted Absolute Percentage Error (WAPE)","Root Mean Square Error (RMSE)","Mean Absolute Percentage Error (MAPE)","Symmetric Mean Absolute Percentage Error (SMAPE)","WAPE is most suitable to assess the accuracy of the model when the actual value is 0 because MAPE divides by the actual value and leads to infinite values when the actual value is 0."
"What is the function of the holiday effect when using the AutoML predictor in Amazon Forecast?","To identify and incorporate public holidays into the training data","To remove weekends from the training data","To convert dates into numbers","To add random noise to the training data","To identify and incorporate public holidays into the training data"
"What is the difference between the Item metadata and the Related Time Series Datasets?","Item metadata adds information about each item, such as its product category, while the Related Time Series Dataset provides time-variant data such as pricing or promotion for each item","Item metadata is used to store the name of the items, while the Related Time Series Dataset provides the location of the data in the file system","Item metadata can be used to store the time series of the items, while the Related Time Series Dataset provides extra data on those time series","Item metadata is used for configuration, while the Related Time Series Dataset is used to configure the data","Item metadata adds information about each item, such as its product category, while the Related Time Series Dataset provides time-variant data such as pricing or promotion for each item"
"Which algorithm is especially suitable to handle intermittent time series data?","Zero inflated negative binomial","Prophet","DeepAR+","ETS","Zero inflated negative binomial"
"In Amazon Forecast, what is the purpose of defining the schema for your datasets?","To tell Forecast the data type of each column","To specify the file format","To define the relationships between datasets","To set the time zone","Defining the schema tells Forecast the data type of each column in your dataset, such as integer, string, or timestamp."
"Which of the following parameters is NOT configurable when creating a Forecast in Amazon Forecast?","The predictor to use","The start date of the forecast","The data frequency","The number of CPUs to use","The number of CPUs used is not configurable when creating a forecast."
"What type of hyperparameter optimisation does Amazon Forecast use?","Bayesian optimisation","Grid search","Random search","Manual tuning","Amazon Forecast uses Bayesian optimisation for hyperparameter optimisation, which is more efficient than grid or random search."
"What is the purpose of using categorical features in Amazon Forecast?","To provide additional context about the items being forecast","To specify the time zone","To define the forecast frequency","To create multiple forecasts","Categorical features provide additional context about the items being forecast, such as product category or store location, which can improve forecast accuracy."
"Which of these algorithms provided in Amazon Forecast is useful for seasonality modeling?","Prophet","ARIMA","Linear Regression","Random Forest","Prophet is particularly useful for time series data with strong seasonality, such as daily or weekly patterns."
"What is the recommended minimum amount of historical data required for training a forecast model in Amazon Forecast?","Three times the forecast horizon","At least one year","One month","Two weeks","Amazon recommends having at least three times the amount of data as the forecast horizon."
"What kind of data does the 'Attribute Data set' contain in Amazon Forecast?","Item metadata","Historical sales data","Future pricing promotions","Weather forecast","Attribute Data sets contains item metadata"
"How can you use AWS Lambda in conjunction with Amazon Forecast?","To automate tasks such as data preprocessing or post-processing","To store historical data","To visualise forecast results","To replace Amazon Forecast algorithms","AWS Lambda can be used to automate tasks such as data preprocessing, post-processing, or triggering forecast creation based on events."
"Which of the following is a key benefit of using Amazon Forecast over building your own forecasting model from scratch?","It eliminates the need for manual feature engineering","It allows you to use custom algorithms","It provides complete control over the underlying infrastructure","It supports real-time forecasting","Amazon Forecast automates many of the tasks involved in building forecasting models, such as data preprocessing, algorithm selection, and hyperparameter optimisation, reducing the need for manual feature engineering."
"What is the purpose of the 'What-If Analysis' feature in Amazon Forecast?","To simulate the impact of different scenarios on your forecasts","To visualise forecast results","To automate the training process","To improve the accuracy of the forecast","The 'What-If Analysis' feature allows you to simulate the impact of different scenarios (e.g., price changes, promotions) on your forecasts."
"What happens if you provide future data for a Target Time Series when creating a forecast using Amazon Forecast?","Forecast will ignore the future data, and only use historical data to create the prediction","Forecast will throw an error and stop the process","Forecast will use the future data as actual and train a model based on that","Forecast will include this data, and it may lead to a biased outcome","Forecast will ignore the future data, and only use historical data to create the prediction"
"What is the advantage of using the built-in error metrics in Amazon Forecast versus building your own?","Amazon Forecast automatically computes the errors, streamlining the evaluation process","It enables complete control over the underlying infrastructure","It uses custom algorithms","It removes the need for manual feature engineering","Amazon Forecast automatically computes the errors, streamlining the evaluation process"
"Which action is NOT automatically handled by Amazon Forecast?","Handling missing data","Handling outliers","Feature selection","Defining business objectives","Amazon Forecast is not able to define your business objectives; you must define them."
"How do you access the forecasts generated by Amazon Forecast?","Through the Amazon Forecast console or API","Through Amazon S3","Through Amazon CloudWatch","Through Amazon Athena","Forecasts are accessible through the Amazon Forecast console or API, allowing you to retrieve the predictions programmatically."
"Which algorithm from Amazon Forecast is best suitable when you have very little historical data?","Deep_AR+","ETS","NP-TS","Prophet","NP-TS stands for Non-Parametric Time Series and requires very little historical data to generate predictions."
"Can Amazon Forecast forecast multiple time series at once?","Yes, by using item IDs to distinguish between different time series","No, you need to create a different forecast for each time series","Only if they share the same Item ID","Only if you use the AutoPredictor","Yes, by using item IDs to distinguish between different time series"
"Which technique can be used to improve the accuracy of Amazon Forecast when dealing with seasonal time series data?","Feature engineering to explicitly model seasonality","Removing outliers","Using a larger dataset","Applying data normalisation","Feature engineering to explicitly model seasonality"
"When does Amazon Forecast create a new version of the forecast?","When you retrain the predictor with new data","When you change the forecast horizon","When you modify the schema of the dataset","When you update the IAM role","When you retrain the predictor with new data"
"What are the advantages of using the AutoPredictor functionality in Amazon Forecast?","It reduces the manual work needed to select the most accurate model","It has the ability to incorporate external data sources like weather or social media trends","It allows for real-time monitoring of forecast accuracy","It supports various programming languages and frameworks","It reduces the manual work needed to select the most accurate model"
"You have created a new data ingestion pipeline and want to use Amazon Forecast to generate new time-series prediction with updated data. What is the best next step?","Create a new predictor and forecast with the updated data","Update the current predictor with the updated data and generate a new forecast","You need to first delete the old predictor","Amazon forecast does not support incremental learning, you need to create a new predictor from scratch","Create a new predictor and forecast with the updated data"
"You notice that the time granularity (frequency) in your dataset varies, but Amazon Forecast requires the granularity to be consistent. What should you do?","Resample the data to a consistent frequency","Remove the irregular data points","Split the data into different datasets based on granularity","Implement a custom algorithm to handle the variable granularity","Resample the data to a consistent frequency"
"In Amazon Forecast, what is the main difference between Quantile Forecast and Point Forecast?","Quantile Forecast provides a range of possible values","Point Forecast is more computationally efficient","Quantile Forecast requires less historical data","Point Forecast takes into account the error metrics","Quantile Forecast provides a range of possible values"
"Why might you choose to use the 'What-If Analysis' feature in Amazon Forecast when planning your supply chain?","To evaluate the impact of different promotional scenarios on future demand","To identify the most profitable products","To optimise inventory levels","To reduce transportation costs","To evaluate the impact of different promotional scenarios on future demand"
"What is the purpose of defining Time Series attributes when creating a dataset group in Amazon Forecast?","To specify which column contains the timestamps","To define the forecasting horizon","To enable anomaly detection","To set the desired confidence interval","To specify which column contains the timestamps"
"Your colleague needs to access the forecast data to build a dashboard. What is the most efficient way to provide your colleague access?","Grant them read-only access to the Amazon Forecast console","Share the AWS credentials","Export the data to a CSV file and share that","Create a new IAM user with full access to Amazon Forecast","Grant them read-only access to the Amazon Forecast console"
"You want to train your Amazon Forecast model with data stored in AWS S3. What access permission will you need to give to Amazon Forecast?","Read access to the S3 bucket","Write access to the S3 bucket","Full control access to the S3 bucket","No access to the S3 bucket","Read access to the S3 bucket"
"You need to ensure the security of your Amazon Forecast data. Which AWS service would you use to encrypt your data at rest?","AWS KMS","AWS IAM","AWS CloudTrail","AWS Config","AWS KMS"
"What is a potential benefit of using the 'Explainability' feature in Amazon Forecast for inventory management?","It can help identify factors driving unexpected changes in demand","It reduces the computational cost of generating forecasts","It automates the process of reordering inventory","It eliminates the need for safety stock","It can help identify factors driving unexpected changes in demand"
"In Amazon Forecast, what is the purpose of the target time series?","It contains the historical data you want to forecast.","It contains external features.","It contains item metadata.","It contains future projected sales figures.","The target time series is the historical data that Forecast uses to learn patterns and generate predictions."
"Which Amazon Forecast algorithm is suitable for time series with intermittent demand (many zero values)?","DeepAR+","Prophet","AutoML","NPTS","DeepAR+ can handle intermittent demand patterns because it models the probability distribution of the forecast, allowing it to predict zero values effectively."
"What does the 'FeaturizationMethod' parameter in Amazon Forecast control?","The way categorical features are encoded.","The algorithm used for forecasting.","The source of external data.","The data export format.","The FeaturizationMethod parameter defines how different features are transformed or encoded before being used in the forecasting model."
"In Amazon Forecast, what is a predictor?","A trained model that generates forecasts.","The input data used for training.","The configuration settings for a forecast job.","A visualization of forecast results.","A predictor is the trained model that is used to generate forecasts based on the input data and specified algorithm."
"What type of data transformation is generally required for categorical features before using them in Amazon Forecast?","Encoding (e.g., one-hot encoding).","Normalization.","Standardisation.","Aggregation.","Categorical features need to be encoded into numerical values using methods like one-hot encoding to be used effectively by most machine learning algorithms in Forecast."
"What is the purpose of specifying a forecast horizon in Amazon Forecast?","To define how far into the future the forecast should predict.","To set the data retention period.","To control the level of granularity in the forecast.","To specify the geographical region for the forecast.","The forecast horizon specifies the number of time steps into the future that the forecast should predict, determining the length of the forecast."
"Which Amazon Forecast feature allows you to incorporate information about known future events?","Related Time Series.","Item Metadata.","Target Time Series.","Forecast Horizon.","Related Time Series allow you to add extra information about future and past events that have impact on the target time series. This helps to improve forecast accuracy."
"What is the recommended way to handle missing values in your target time series data before using Amazon Forecast?","Impute the missing values using appropriate methods.","Delete the rows with missing values.","Leave the missing values as is.","Replace them with a constant value.","It's best to impute missing values using techniques that maintain the integrity of the time series data (e.g., interpolation, mean/median imputation)."
"Which metric is NOT commonly used to evaluate the accuracy of forecasts generated by Amazon Forecast?","Root Mean Square Error (RMSE)","Mean Absolute Percentage Error (MAPE)","Weighted Quantile Loss (wQL)","F1-score","F1-score is a metric primarily used in classification problems, not typically in evaluating the accuracy of time series forecasts."
"In Amazon Forecast, what is the purpose of hyperparameter optimisation (HPO)?","To find the best combination of model parameters for a specific dataset.","To speed up the training process.","To reduce the amount of training data required.","To simplify the model architecture.","HPO aims to find the optimal settings for a model's parameters to maximize its performance on a given dataset, improving forecast accuracy."
"You want to train an Amazon Forecast model using data stored in S3. Which IAM role must you configure?","A role that grants Forecast permission to read from the S3 bucket.","A role that allows users to access the Forecast console.","A role that grants Forecast permission to write to CloudWatch Logs.","A role that allows Forecast to manage EC2 instances.","Forecast needs IAM permissions to read the data from the S3 bucket; the role should be configured with the appropriate S3 read permissions."
"Which Amazon Forecast capability is designed to automatically select the best forecasting algorithm for your data?","AutoML","DeepAR+","Prophet","NPTS","AutoML automates the algorithm selection process and trains several models, selecting the one that performs best on your data."
"When using the AutoML feature in Amazon Forecast, can you restrict the algorithms considered?","Yes, by specifying the 'AlgorithmSelections' parameter.","No, AutoML always considers all available algorithms.","Yes, by manually adjusting the training data.","Yes, by modifying the Forecast API endpoint.","You can constrain the algorithms considered by AutoML by using the `AlgorithmSelections` parameter in the CreatePredictor API."
"In Amazon Forecast, what does 'Item Metadata' refer to?","Data about the items being forecasted (e.g., product category, location).","Data about the source S3 bucket.","Data about the target time series.","Data about the performance of the model.","Item Metadata provides additional information about the items being forecasted, which can help improve forecast accuracy by providing context."
"Which feature in Amazon Forecast allows you to generate probabilistic forecasts?","DeepAR+","Prophet","AutoML","All of the above.","DeepAR+ is known for its ability to generate probabilistic forecasts, providing a range of possible outcomes with associated probabilities."
"When should you consider using the Prophet algorithm in Amazon Forecast?","When you have time series data with strong seasonality and trend.","When you have very little historical data.","When you have a large number of missing values.","When you need to forecast in real-time.","Prophet is designed to handle time series data with strong seasonal patterns and trends, making it well-suited for these types of datasets."
"What is the main benefit of using the 'Weighted Quantile Loss' (wQL) metric in Amazon Forecast?","It allows you to optimise for specific forecast percentiles (e.g., P50, P90).","It simplifies the model training process.","It reduces the overall forecast error.","It improves the interpretability of the forecast.","wQL allows you to optimise the model's performance for specific quantiles, which is useful when you need accurate predictions for specific probability levels."
"What is the purpose of the Amazon Forecast backtesting feature?","To evaluate the accuracy of your model on historical data.","To prepare data for training.","To optimise model hyperparameters.","To deploy the model to production.","Backtesting uses past data to simulate real-world forecasting, providing an estimate of how well the model will perform when forecasting the future."
"Which of the following is NOT a valid input data source for Amazon Forecast?","Amazon S3","Amazon Redshift","Snowflake","CSV files stored on a local machine","CSV files stored locally are not a valid input data source. Amazon Forecast supports importing data from S3 and Redshift."
"What security measure is recommended when storing sensitive data in S3 buckets used by Amazon Forecast?","Encrypt the data at rest using S3 encryption.","Disable public access to the S3 bucket.","Use IAM roles with least privilege.","All of the above.","It is recommended to encrypt the data at rest, disable public access to S3 buckets, and use IAM roles with least privilege to secure sensitive data used by Amazon Forecast."
"How do you update the forecast horizon of an existing Amazon Forecast predictor?","You must create a new predictor with the desired horizon.","You can modify the existing predictor's configuration via the AWS console.","You can update the forecast horizon using the UpdatePredictor API.","The forecast horizon cannot be changed after the predictor is created.","You must create a new predictor with the updated forecast horizon, as the original cannot be changed after creation."
"In Amazon Forecast, what is the purpose of the Explainability feature?","To understand the factors that influence your forecasts.","To improve the accuracy of your forecasts.","To simplify the data preparation process.","To automate model deployment.","Explainability helps you understand which factors (features) are most influential in driving the predictions, providing insights into the model's behavior."
"What is the impact of increasing the number of backtest windows in Amazon Forecast?","It provides a more robust estimate of the model's accuracy.","It reduces the training time.","It simplifies the hyperparameter optimization process.","It decreases the amount of historical data required.","Increasing the number of backtest windows provides a more comprehensive evaluation of the model's performance across different time periods, leading to a more reliable accuracy estimate."
"When using Amazon Forecast, what is the purpose of defining a schema for your input data?","To specify the data types and formats of the columns.","To encrypt the data.","To filter the data.","To sort the data.","The schema defines the structure of your input data, specifying the data types and formats for each column, which is essential for Forecast to process the data correctly."
"You want to forecast demand for multiple products with varying historical data lengths. Which Amazon Forecast feature is most helpful?","Item Metadata.","Related Time Series.","AutoML.","Global Forecasting Model.","Item Metadata to provide product-specific details and train a global model so that even new products are forecasted accurately."
"What is the benefit of using Amazon Forecast's 'What-If Analysis' feature?","To simulate the impact of different scenarios on your forecasts.","To identify anomalies in your historical data.","To optimise your data storage costs.","To improve the speed of model training.","What-If Analysis allows you to explore how different events or interventions might affect future demand, providing insights for decision-making."
"Which action can improve the accuracy of Amazon Forecast when dealing with promotional events that affect sales?","Include promotional event details as Related Time Series.","Exclude data from promotional periods.","Use a shorter forecast horizon.","Increase the frequency of data collection.","Including promotional event details as Related Time Series in Amazon Forecast provides useful information to the algorithm and usually increases accuracy."
"What should you do if your Amazon Forecast model is consistently under-predicting demand?","Review the data for missing values and external factors and retrain the model.","Decrease the forecast horizon.","Increase the backtest window size.","Change the algorithm to Prophet.","Missing data or unaccounted external factors can lead to under-prediction. Reviewing these and retraining can improve accuracy."
"What is the primary purpose of the Amazon Forecast Import API?","To import historical data into Amazon Forecast.","To export forecast results to S3.","To create a new predictor.","To delete a dataset.","The Amazon Forecast Import API is used to import historical data from various sources into Amazon Forecast for training and forecasting."
"When creating a Forecast export, what data format is NOT supported by Amazon Forecast?","CSV","JSON","Parquet","XLSX","Amazon Forecast supports CSV, and JSON but not XLSX format for Forecast exports."
"Which Forecast feature allows you to group your time series data based on shared characteristics?","Hierarchical Forecasting.","Related Time Series.","Item Metadata.","Forecast Groups.","Hierarchical forecasting can be used to group related time series so that you can forecast a collection of similar items."
"What is the recommended way to scale the training of Amazon Forecast models when dealing with large datasets?","Use the AutoML feature.","Increase the forecast horizon.","Amazon Forecast automatically scales its resources.","Implement distributed training using SageMaker.","Amazon Forecast is a fully managed service which automatically scales its resources to handle large datasets."
"When using Amazon Forecast with Retail data, what type of Item Metadata might be useful to include?","Product Category","Store Location","Price","All of the above.","Product Category, Store Location and price are all useful item metadata which can be used to train an Amazon Forecast Retail model."
"Which of the following algorithms in Amazon Forecast is based on recurrent neural networks?","DeepAR+","NPTS","Prophet","ARIMA","DeepAR+ (Deep Autoregressive) is based on recurrent neural networks (RNNs), specifically LSTMs, making it suitable for capturing complex temporal dependencies."
"In Amazon Forecast, which time series type can be used to represent promotions or external factors?","Related Time Series","Target Time Series","Item Metadata","Forecast Time Series","Related Time Series are used to represent external data that may affect the target time series."
"If you need to forecast multiple time series that are related and influence each other, which Amazon Forecast feature should you consider using?","Hierarchical Forecasting","Related Time Series","Item Metadata","Time Series Groups","Hierarchical forecasting is used to generate consistent forecasts across different time granularities that are related to each other."
"What is the primary benefit of using the AWS Forecast console?","It provides a user-friendly interface for managing Forecast resources.","It provides better performance than the API.","It offers additional features not available through the API.","It is the only way to access Amazon Forecast.","The AWS Forecast console provides a user-friendly interface for managing Forecast resources, creating datasets, predictors, and forecasts without writing code."
"If you want to receive notifications about the status of your Amazon Forecast jobs (e.g., training completion, errors), what AWS service can you integrate with?","Amazon SNS (Simple Notification Service)","Amazon SQS (Simple Queue Service)","Amazon CloudWatch Events","Amazon Lambda","Amazon SNS allows you to receive notifications about the status of your Amazon Forecast jobs."
"What is a key consideration when choosing the time granularity for your time series data in Amazon Forecast?","The frequency of data collection and the desired forecast granularity.","The total size of your dataset.","The number of items you are forecasting.","The region where you are running your forecasts.","The chosen time granularity should align with how frequently you collect data and the level of detail you need in your forecasts (e.g., hourly, daily, weekly)."
"You need to forecast sales for a new product with no historical data. Which Amazon Forecast feature can help?","Item Metadata combined with a global forecasting model.","AutoML with limited data.","A very short forecast horizon.","Using only Related Time Series data.","By using item metadata, the algorithm can leverage information from existing products to make predictions for the new one."
"Which Amazon Forecast algorithm is best suited for modelling linear trends in your data?","ARIMA","DeepAR+","Prophet","NPTS","ARIMA, AutoRegressive Integrated Moving Average, is well suited for data exhibiting linear trends."
"What is the primary purpose of the evaluation metrics provided by Amazon Forecast?","To assess the accuracy and reliability of your forecasts.","To optimise model hyperparameters.","To prepare data for training.","To monitor the cost of using Amazon Forecast.","The metrics provided by Amazon Forecast are used to assess the accuracy and reliability of your forecasts allowing you to optimise and compare different models."
"When using Amazon Forecast, which of the following steps is typically performed first?","Importing your data into Amazon Forecast.","Creating a predictor.","Creating a forecast.","Evaluating your model.","The first step in using Amazon Forecast is typically to import your historical data into Amazon Forecast."
"What is the maximum forecast horizon supported by Amazon Forecast?","500 time points","10000 time points","50% of the length of the dataset","Twice the length of the dataset","The maximum forecast horizon is limited to 500 time points or 1/3 of the length of your historical data."
"What action will reduce the cost of your Amazon Forecast deployments?","Optimise data storage costs by storing your data in a cheaper storage class.","Delete unused datasets and predictors.","Reduce the forecast horizon.","All of the above.","Reducing the forecast horizon will reduce the cost, as will deleting unused datasets and predictors. It is always good practice to optimise storage costs."
