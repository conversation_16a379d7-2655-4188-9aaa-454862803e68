"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Kendra, what is the primary function of a data source?","To connect to and ingest data from various repositories.","To define the access control policies for the Kendra index.","To visualise the search analytics of the Kendra service.","To create custom machine learning models for query understanding.","A data source in Kendra is responsible for connecting to different repositories (e.g., S3, SharePoint) and ingesting the data into the Kendra index."
"Which Amazon Kendra feature allows you to boost the ranking of certain documents or answers based on specific criteria?","Relevance Tuning","Query Suggestions","Thesaurus","Custom Vocabulary","Relevance Tuning allows you to influence the search results by boosting specific documents or answers based on criteria such as document age, popularity, or business importance."
"What is the purpose of the 'SubmitFeedback' API call in Amazon Kendra?","To provide feedback on the relevance of search results.","To report issues with the Kendra service.","To request new features for Kendra.","To update the Kendra index with new documents.","The 'SubmitFeedback' API allows users to provide feedback on the relevance of search results, helping <PERSON> improve its ranking and relevance over time."
"Which Amazon Kendra connector is best suited for indexing content from a website?","Web Crawler","SharePoint","Salesforce","Confluence","The Web Crawler connector is specifically designed for crawling and indexing content from websites, following links and extracting text."
"What is the purpose of using custom document enrichment in Amazon Kendra?","To modify the content and metadata of documents during indexing.","To encrypt documents before they are ingested.","To reduce the storage cost of the Kendra index.","To automatically translate documents into different languages.","Custom document enrichment allows developers to modify the content and metadata of documents during the indexing process, enhancing the search experience by adding or transforming information."
"In Amazon Kendra, what is the role of a custom index field?","To define specific properties that can be used to filter and sort search results.","To automatically generate summaries of documents.","To classify documents based on their content.","To encrypt sensitive information within documents.","Custom index fields allow developers to define specific properties that can be used to filter, sort, and refine search results, providing more control over the search experience."
"Which Amazon Kendra API call is used to initiate a search query?","Query","Retrieve","Search","Find","The 'Query' API is the primary method for submitting search queries to Amazon Kendra and retrieving the corresponding search results."
"What type of machine learning model does Amazon Kendra primarily use for understanding search queries?","Natural Language Processing (NLP)","Computer Vision","Reinforcement Learning","Time Series Analysis","Amazon Kendra heavily relies on Natural Language Processing (NLP) models to understand the meaning and intent behind search queries."
"Which Amazon Kendra feature allows you to define synonyms for search terms?","Thesaurus","Stop Words","Custom Vocabulary","Relevance Tuning","A thesaurus in Amazon Kendra allows you to define synonyms for search terms, ensuring that users find relevant results even if they use different words to describe the same concept."
"What is the purpose of Stop Words in Amazon Kendra?","To exclude common words that don't contribute to search relevance.","To highlight important keywords in search results.","To automatically translate search queries into different languages.","To correct spelling errors in search queries.","Stop words are common words (e.g., 'the', 'a', 'is') that are excluded from the index because they don't contribute to search relevance."
"Which AWS service can be used to build a custom user interface for interacting with Amazon Kendra?","Amazon Lex","Amazon S3","Amazon CloudWatch","Amazon SNS","Amazon Lex is used to build conversational interfaces. These interfaces can use Amazon Kendra to search through your documents and return the search results to the user via the bot you've created."
"What does the 'AccessControlList' parameter in the Amazon Kendra 'Query' API allow you to do?","Filter search results based on user access permissions.","Specify the order in which search results are displayed.","Limit the number of search results returned.","Encrypt the search query.","The 'AccessControlList' parameter allows you to filter search results based on user access permissions, ensuring that users only see documents they are authorised to view."
"Which document format is supported by Amazon Kendra for indexing content?","PDF","CSV","JSON","XML","PDF is a supported document format. Kendra supports a variety of document formats including HTML, Word, PowerPoint, Text and PDF."
"How does Amazon Kendra handle incremental updates to indexed content?","Automatically detects and indexes changed documents.","Requires manual re-indexing of the entire data source.","Requires scheduled re-indexing of the entire data source.","Updates must be pushed via the API.","Kendra automatically detects and indexes changed documents in the data source."
"What is the purpose of the 'FuzzyMatching' setting in Amazon Kendra?","To improve search results by matching terms with similar spellings.","To encrypt search queries.","To automatically translate search queries.","To filter out irrelevant search results.","The 'FuzzyMatching' setting improves search results by matching terms with similar spellings, compensating for typos and variations in user input."
"Which Amazon Kendra API is used to delete a data source?","DeleteDataSource","RemoveDataSource","DestroyDataSource","EraseDataSource","The 'DeleteDataSource' API is used to delete an existing data source from Amazon Kendra."
"What is the maximum number of documents that can be indexed in an Amazon Kendra index?","Millions","Thousands","Hundreds","Tens","An Amazon Kendra index can handle millions of documents."
"Which Amazon Kendra feature can be used to automatically generate answers to frequently asked questions (FAQs) from indexed content?","Extractive Question Answering","Semantic Search","Relevance Tuning","Query Suggestions","Extractive Question Answering feature in Kendra helps to automatically generate answers to frequently asked questions (FAQs) from indexed content."
"What is the purpose of the 'DocumentRelevance' parameter in the Amazon Kendra 'Query' API response?","To indicate the confidence score of each search result.","To specify the file size of each document.","To display the date when the document was last modified.","To indicate the source of the document.","The 'DocumentRelevance' parameter indicates the confidence score or relevance score of each search result, helping users assess the quality of the results."
"Which AWS IAM permission is required to allow an IAM role to start a Kendra data source sync job?","kendra:StartDataSourceSyncJob","kendra:SyncDataSource","kendra:ExecuteDataSourceSync","kendra:RunDataSourceSyncJob","The IAM permission 'kendra:StartDataSourceSyncJob' is required to allow an IAM role to start a data source sync job in Amazon Kendra."
"In Amazon Kendra, what is a 'featured result'?","A specific search result that is promoted to the top of the results list.","A search result that includes images and videos.","A search result that is automatically translated.","A search result that is encrypted.","A 'featured result' in Kendra is a specific search result that is promoted to the top of the results list, ensuring it is highly visible to users."
"What type of connector does Amazon Kendra provide for indexing data stored in Amazon S3?","Native S3 Connector","JDBC Connector","Web Crawler Connector","Salesforce Connector","Amazon Kendra provides a native S3 Connector to crawl and index data stored in Amazon S3."
"Which Amazon Kendra API is used to create a new index?","CreateIndex","CreateKendraIndex","NewIndex","SetupIndex","The 'CreateIndex' API is used to create a new index in Amazon Kendra."
"What is the purpose of the 'DescribeIndex' API call in Amazon Kendra?","To retrieve information about an existing index.","To delete an index.","To update the settings of an index.","To list all indexes in the account.","The 'DescribeIndex' API is used to retrieve information about an existing index, such as its status, configuration, and capacity."
"Which of the following is NOT a built-in Amazon Kendra data source connector?","Jira","Google Drive","ServiceNow","Github","Github is not a built-in connector for Amazon Kendra."
"What is the maximum file size limit for documents indexed in Amazon Kendra?","50 MB","10 MB","100 MB","1 GB","The maximum file size limit for documents indexed in Amazon Kendra is 50 MB."
"In Amazon Kendra, what is the role of a 'user context'?","To personalize search results based on the user's identity and permissions.","To encrypt search queries.","To automatically translate search results.","To validate the user's credentials.","A 'user context' in Kendra is used to personalize search results based on the user's identity and permissions, ensuring that users only see documents they are authorised to view."
"Which Amazon Kendra API is used to update an existing index?","UpdateIndex","ModifyIndex","EditIndex","ReviseIndex","The 'UpdateIndex' API is used to update the configuration of an existing Amazon Kendra index, such as changing the index name or description."
"What is the main advantage of using the Amazon Kendra 'QuerySuggestions' feature?","It helps users formulate effective search queries.","It automatically translates search results into different languages.","It automatically corrects spelling errors in search queries.","It automatically generates summaries of documents.","The Amazon Kendra 'QuerySuggestions' feature helps users formulate effective search queries by providing suggestions as they type, improving the overall search experience."
"Which encryption method is used by Amazon Kendra for data at rest?","AWS Key Management Service (KMS)","SSL/TLS","AES-256","HTTPS","Amazon Kendra encrypts data at rest using AWS Key Management Service (KMS)."
"What is the purpose of the Amazon Kendra 'Experience Builder'?","To create a search interface without coding.","To create custom data source connectors.","To train custom machine learning models for Kendra.","To visualise search analytics.","The Amazon Kendra 'Experience Builder' is a tool for creating search interfaces without needing to write any code."
"Which AWS service can be used to monitor Amazon Kendra's performance and usage metrics?","Amazon CloudWatch","AWS CloudTrail","Amazon Config","Amazon X-Ray","Amazon CloudWatch can be used to monitor Amazon Kendra's performance and usage metrics."
"Which of the following is a valid Amazon Kendra edition?","Enterprise Edition","Standard Edition","Developer Edition","Basic Edition","Enterprise Edition is a valid Amazon Kendra edition."
"What is the purpose of the 'Relevance' parameter when creating a custom index field in Amazon Kendra?","To control the influence of the field on search result ranking.","To specify the data type of the field.","To define the access control permissions for the field.","To encrypt the data stored in the field.","The 'Relevance' parameter controls the influence of the custom index field on the search result ranking, allowing developers to fine-tune the importance of different fields."
"Which Amazon Kendra feature can be used to group similar documents together in search results?","Facets","Synonyms","Thesaurus","Stop Words","Facets in Amazon Kendra can be used to group similar documents together in search results."
"What is the purpose of the 'UserGroupResolutionConfiguration' setting in Amazon Kendra?","To map user identities to groups for access control.","To encrypt user data.","To automatically translate user profiles.","To validate user credentials.","The 'UserGroupResolutionConfiguration' setting is used to map user identities to groups for access control, ensuring that users only see documents they are authorised to view."
"What type of data source is best suited for indexing structured data, such as data in a relational database?","JDBC Connector","Web Crawler Connector","Salesforce Connector","SharePoint Connector","A JDBC Connector is best suited for indexing structured data from relational databases."
"Which Amazon Kendra API is used to list all data sources in an index?","ListDataSources","GetDataSourceList","RetrieveDataSources","ShowDataSources","The 'ListDataSources' API is used to retrieve a list of all data sources associated with a given Amazon Kendra index."
"In Amazon Kendra, what is the purpose of a 'Custom Entity Recognizer'?","To identify and extract specific entities from documents that are not recognized by the built-in recognizers.","To encrypt sensitive information within documents.","To automatically translate documents into different languages.","To correct spelling errors in documents.","A 'Custom Entity Recognizer' is used to identify and extract specific entities from documents that are not recognized by the built-in recognizers, allowing developers to extract domain-specific information."
"Which Amazon Kendra feature can be used to automatically complete search queries as the user types?","Auto Completion","Query Suggestions","Predictive Search","Smart Search","Query Suggestions is the feature that provides suggestions as the user types."
"What is the maximum query length supported by Amazon Kendra?","200 characters","500 characters","1000 characters","1500 characters","Amazon Kendra supports a maximum query length of 200 characters."
"Which Amazon Kendra API is used to remove a document from the index?","DeleteDocument","RemoveDocument","PurgeDocument","EraseDocument","The 'DeleteDocument' API is used to remove a document from the Amazon Kendra index."
"In Amazon Kendra, what does the 'boost' parameter in the 'RelevanceTuning' API do?","Increase the ranking score of matching documents.","Decrease the ranking score of matching documents.","Specify the file size of matching documents.","Set access control permissions for matching documents.","The 'boost' parameter in the 'RelevanceTuning' API is used to increase the ranking score of matching documents, making them appear higher in the search results."
"Which of the following is a valid data source synchronization schedule for Amazon Kendra?","Daily","Hourly","Weekly","Monthly","Daily is a valid data source synchronisation schedule."
"What is the purpose of the 'PrincipalStore' when configuring access control in Amazon Kendra?","To manage user and group identities.","To encrypt user data.","To automatically translate user profiles.","To validate user credentials.","The 'PrincipalStore' is used to manage user and group identities for access control."
"Which Amazon Kendra feature allows you to create a search application without writing any code?","Experience Builder","Code Editor","AWS Cloud9","AWS Amplify","Experience Builder provides a way to create a search application without writing any code."
"What is the purpose of the 'DocumentAttributeBoostingConfiguration' in Amazon Kendra?","To boost the relevance of documents based on specific attribute values.","To reduce the storage cost of documents.","To automatically translate documents.","To encrypt document attributes.","The 'DocumentAttributeBoostingConfiguration' allows developers to boost the relevance of documents based on specific attribute values, influencing the search result ranking."
"Which file format is used to create a thesaurus for Amazon Kendra?","CSV","JSON","XML","TXT","Thesauruses can be created using a CSV format file."
"In Amazon Kendra, what is the function of the 'QueryResultType'?","To filter results based on the type of content (e.g., document, answer).","To specify the sort order of search results.","To limit the number of search results.","To encrypt the search query.","The 'QueryResultType' is used to filter results based on the type of content (e.g., document, answer)."
"Which connector is best suited for indexing data from a corporate file share?","SharePoint Connector","S3 Connector","Web Crawler Connector","Confluence Connector","The SharePoint Connector is best suited for corporate file shares."
"In Amazon Kendra, what is the primary purpose of a data source connector?","To ingest data from various repositories into the Kendra index.","To define the search result display.","To manage user access permissions.","To monitor system performance.","Data source connectors are responsible for extracting data from different repositories, such as websites, file systems, and databases, and bringing it into the Kendra index for searching."
"Which Amazon Kendra API call is used to initiate a search query?","Query","StartIndex","SubmitDocument","CreateIndex","The 'Query' API call is used to send a search query to an Amazon Kendra index and retrieve relevant results."
"What is the purpose of the Amazon Kendra 'Relevance Tuning' feature?","To improve the accuracy and ranking of search results.","To reduce the cost of indexing.","To increase the speed of indexing.","To simplify the data source connection process.","Relevance Tuning allows you to adjust parameters to fine-tune how Kendra ranks search results, improving the overall accuracy and relevance of the results."
"Which of the following document formats is NOT supported natively by Amazon Kendra?","MP3","PDF","DOCX","TXT","Amazon Kendra supports various document formats like PDF, DOCX and TXT for indexing, but audio formats such as MP3 are not supported."
"What is the function of 'Custom Entity Extraction' in Amazon Kendra?","To identify and extract specific entities from documents that are not recognised by default.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","Custom Entity Extraction enables you to define custom entities (e.g., project codes, product names) that Kendra can recognise and extract from documents, enhancing search capabilities."
"What is the purpose of the Amazon Kendra Experience Builder?","To create a custom search interface for users to interact with Kendra.","To configure data source connectors.","To manage user permissions.","To monitor system performance.","The Experience Builder enables developers to build user-friendly search interfaces, customising the look and feel and user interaction with Amazon Kendra."
"Which Amazon Kendra API operation is used to create a new index?","CreateIndex","StartIndex","CreateDataSource","StartDataSourceSyncJob","The `CreateIndex` API operation is used to create a new Kendra index, which is the foundation for storing and searching your data."
"What is the purpose of the Amazon Kendra 'Stop Words' list?","To exclude common words from being indexed to improve search relevance.","To identify potentially offensive words.","To highlight important words in search results.","To translate words into different languages.","The 'Stop Words' list allows you to exclude common words like 'the', 'a', and 'is' from being indexed, as these words often don't contribute to search relevance."
"In Amazon Kendra, what is the 'boosting' feature used for?","To prioritise certain documents or fields in the search results.","To reduce the size of the index.","To increase the speed of indexing.","To simplify the data source connection process.","Boosting allows you to give higher priority to certain documents or fields during the search process, increasing their ranking in the search results."
"Which AWS service is commonly used to provide user authentication and authorisation for an Amazon Kendra search application?","AWS Cognito","AWS IAM","AWS Lambda","Amazon S3","AWS Cognito is often used to manage user identities and authentication for Kendra search applications, ensuring that only authorised users can access the search functionality."
"Which Amazon Kendra API operation is used to update an existing index?","UpdateIndex","ModifyIndex","AlterIndex","ChangeIndex","The `UpdateIndex` API operation is used to modify the settings of an existing Kendra index, such as its name, description, or role."
"What is the purpose of the Amazon Kendra 'Thesaurus'?","To expand search queries with synonyms to improve recall.","To translate search queries into different languages.","To correct spelling errors in search queries.","To summarise search results into shorter excerpts.","The Thesaurus allows you to define synonyms for terms, enabling Kendra to expand search queries and improve the retrieval of relevant documents."
"Which of the following actions CANNOT be performed using the Amazon Kendra console?","Fine-tuning ranking using relevance tuning","Creating a user profile","Creating a new index","Connecting to a data source","The Kendra console provides extensive functionality to create indices, connect to data sources, and tune relevance. Creating a user profile is generally not a Kendra console specific function."
"What is the purpose of the Amazon Kendra 'User Context Filtering' feature?","To restrict search results based on user roles and permissions.","To track user search history.","To personalise search results based on user preferences.","To translate search queries into different languages.","User Context Filtering enables you to restrict search results based on user roles and permissions, ensuring that users only see documents they are authorised to access."
"In Amazon Kendra, what is a 'Faq'?","A question-answer pair that can be directly returned as a search result.","A collection of documents.","A search query template.","A data source connector.","A FAQ is a simple question-answer pair that Kendra can directly return as a search result, providing immediate answers to common questions."
"Which Amazon Kendra API operation is used to delete an existing index?","DeleteIndex","RemoveIndex","DropIndex","TerminateIndex","The `DeleteIndex` API operation is used to permanently remove an existing Kendra index and all its associated data."
"What is the purpose of the Amazon Kendra 'Crawler'?","To automatically discover and index content from websites.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","The Kendra Crawler automatically discovers and indexes content from websites, making it easier to ingest web-based information."
"Which of the following data sources is NOT natively supported by Amazon Kendra?","Salesforce","Confluence","Amazon S3","Azure Blob Storage","While Kendra supports many popular data sources like Salesforce, Confluence and S3, it does not natively support Azure Blob Storage."
"What is the purpose of the Amazon Kendra 'Featured Results' feature?","To manually promote specific documents to the top of search results for certain queries.","To automatically generate summaries of search results.","To translate search queries into different languages.","To correct spelling errors in search queries.","Featured Results enable you to manually promote specific documents to the top of search results for certain queries, ensuring that important information is always prominently displayed."
"Which AWS service is often used to store and manage the documents that Amazon Kendra indexes?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","Amazon S3 is commonly used to store the documents that Kendra indexes, providing a scalable and cost-effective storage solution."
"What is the purpose of the Amazon Kendra 'Custom Document Enrichment' feature?","To modify document content before it is indexed by Kendra.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","Custom Document Enrichment allows you to modify document content before it is indexed by Kendra, enabling you to add metadata, remove sensitive information, or perform other transformations."
"Which Amazon Kendra API operation is used to start a synchronisation job for a data source?","StartDataSourceSyncJob","SyncDataSource","RunDataSourceSync","ExecuteDataSourceSync","The `StartDataSourceSyncJob` API operation is used to initiate a synchronisation job for a data source, which updates the Kendra index with the latest data."
"What is the purpose of the Amazon Kendra 'Query Suggestions' feature?","To provide users with suggested search terms as they type.","To automatically correct spelling errors in search queries.","To translate search queries into different languages.","To summarise search results into shorter excerpts.","Query Suggestions provide users with suggested search terms as they type, making it easier for them to find the information they are looking for."
"In Amazon Kendra, what is a 'Knowledge Article'?","A structured document containing information on a specific topic, often used in help centres.","A collection of documents.","A search query template.","A data source connector.","A Knowledge Article is a structured document containing information on a specific topic, often used in help centres or knowledge bases."
"Which Amazon Kendra API operation is used to describe an existing index?","DescribeIndex","GetIndex","ShowIndex","ViewIndex","The `DescribeIndex` API operation is used to retrieve detailed information about an existing Kendra index, such as its status, configuration, and ARN."
"What is the purpose of the Amazon Kendra 'User Personalisation' feature?","To tailor search results to individual user preferences and behaviour.","To track user search history.","To restrict search results based on user roles and permissions.","To translate search queries into different languages.","User Personalisation enables you to tailor search results to individual user preferences and behaviour, providing a more relevant and personalised search experience."
"Which of the following document formats is generally the most complex to parse when indexing in Amazon Kendra?","HTML","PDF","TXT","DOCX","HTML documents can be more complex to parse due to their diverse structure, potential scripting, and varying quality of markup compared to other document formats."
"What is the purpose of the Amazon Kendra 'Data Source Sync Schedule'?","To automatically synchronise data sources with the Kendra index on a regular basis.","To manually trigger synchronisation jobs.","To track the history of synchronisation jobs.","To configure the data source connector.","The Data Source Sync Schedule allows you to automatically synchronise data sources with the Kendra index on a regular basis, ensuring that the index is always up-to-date."
"In Amazon Kendra, what is the purpose of setting 'Document Metadata'?","To provide additional information about documents that can be used for filtering and boosting search results.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","Setting Document Metadata allows you to provide additional information about documents that can be used for filtering and boosting search results, enhancing the relevance and accuracy of search results."
"Which Amazon Kendra API operation is used to list all existing indexes?","ListIndices","GetIndices","ShowIndices","ViewIndices","The `ListIndices` API operation is used to retrieve a list of all Kendra indexes in your account."
"Which Amazon Kendra feature helps developers ensure sensitive information isn't indexed?","Configuration of data source access control lists","Content removal via the DeleteDocument API","Filtering and blocking content using regular expressions.","Server-side encryption of documents","Filtering and blocking content using regular expressions in the data source configuration helps ensure sensitive information isn't indexed."
"Which Kendra feature allows developers to programmatically delete documents from the index?","DeleteDocument API","RemoveDocument API","PurgeDocument API","EraseDocument API","The DeleteDocument API operation allows developers to programmatically delete documents from a Kendra index."
"What is the purpose of the 'Access Control List' (ACL) setting when configuring a data source in Amazon Kendra?","To define which users and groups have access to specific documents.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","The ACL setting allows you to define which users and groups have access to specific documents, ensuring that users only see the documents they are authorised to access."
"Which Amazon Kendra API operation is used to update a data source?","UpdateDataSource","ModifyDataSource","AlterDataSource","ChangeDataSource","The `UpdateDataSource` API operation is used to modify the settings of an existing Kendra data source, such as its name, description, or connection information."
"What is the purpose of using a 'Lambda Function' as a data source connector in Amazon Kendra?","To implement custom data ingestion logic.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","Using a Lambda Function as a data source connector allows you to implement custom data ingestion logic, enabling you to connect to data sources that are not natively supported by Kendra."
"In Amazon Kendra, what is the role of the 'Service Role' associated with an index?","To grant Kendra permissions to access data sources and other AWS resources.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","The Service Role grants Kendra permissions to access data sources and other AWS resources, ensuring that Kendra can securely access and index your data."
"Which Amazon Kendra API operation is used to get statistics about an index?","DescribeIndex","GetStatistics","ShowStatistics","ViewStatistics","The DescribeIndex API operation provides statistics about the index, including document count and storage usage."
"Which Amazon Kendra feature allows developers to assign different weights to different document fields during indexing?","Field Boosting","Attribute Prioritisation","Document Scoring","Metadata Weighting","Field Boosting allows developers to assign different weights to different document fields during indexing, influencing search result ranking."
"What is the purpose of the 'Capacity Units' setting when creating an Amazon Kendra index?","To define the processing power and storage capacity allocated to the index.","To encrypt sensitive information within documents.","To translate documents into different languages.","To summarise long documents into shorter excerpts.","The Capacity Units setting defines the processing power and storage capacity allocated to the index, impacting the performance and scalability of the search service."
"Which of these options is NOT a factor when estimating the cost of running Kendra?","Number of Questions answered","Storage space used by the index","Number of Queries made","Number of documents ingested","The number of questions answered is not a cost factor."
"Which file format is typically used for uploading a thesaurus to Kendra?","CSV","JSON","XML","YAML","A CSV format is required for uploading a thesaurus."
"When using the Kendra Retrieve API, which parameter would you use to apply user context filtering?","UserContext","SecurityGroups","AccessControl","UserGroups","The UserContext parameter enables you to apply user context filtering."
"What is the impact of setting the 'edition' configuration to 'DeveloperEdition' when creating a Kendra index?","Limits the index size and usage for testing purposes.","Increases the search speed significantly.","Enables enterprise-grade security features.","Automatically creates a backup of the index every day.","The DeveloperEdition limits index size and usage and is generally used for testing purposes."
"Which Amazon Kendra API action do you use to suggest a related result?","SubmitFeedback","UpdateQuerySuggestionsConfig","CreateFeaturedResultsSet","AssociateEntities","The SubmitFeedback API is used to send suggestions for related results, improving Kendra's ability to connect information with user queries."
"How do you ensure documents from an S3 bucket are automatically indexed in Amazon Kendra when new documents are added?","Enable incremental syncs on the S3 data source connector.","Enable versioning on the S3 bucket.","Create an S3 event notification to trigger the Kendra re-indexing.","Configure a Lambda trigger for S3 events that calls Kendra's Index API.","Incremental syncs on the S3 data source connector will ensure new documents are automatically indexed."
"You need to limit access to sensitive documents within your Amazon Kendra index based on the user's department. Which Amazon Kendra feature would you use?","User context filtering","Query result reranking","Featured results","Stop words","User context filtering restricts search results based on user roles and permissions."
"Which of the following Kendra features would be most effective for ensuring specific high-priority documents always appear at the top of the search results for certain key search phrases?","Featured results","Thesaurus","Relevance tuning","Stop words","Featured results allow you to manually promote specific documents to the top of the search results for certain queries."
"Which Kendra edition offers the highest query volume and number of data source connections?","Enterprise","Developer","Standard","Limited","The Enterprise edition offers the highest query volume and number of data source connections."
