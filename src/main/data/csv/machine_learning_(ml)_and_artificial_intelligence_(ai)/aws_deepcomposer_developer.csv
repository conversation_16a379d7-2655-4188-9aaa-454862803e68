"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS DeepComposer, what is the primary purpose of the Generative Adversarial Network (GAN)?","To generate new musical compositions.","To transcribe musical notation.","To analyse audio signals.","To synthesise existing musical pieces.","The GAN is at the heart of DeepComposer, generating new music by pitting two neural networks against each other."
"What type of keyboard is AWS DeepComposer compatible with?","MIDI keyboard","USB keyboard","Bluetooth keyboard","PS/2 keyboard","AWS DeepComposer is designed to work with a standard MIDI keyboard to input musical notes and control parameters."
"What is the main function of the DeepComposer console?","To train and evaluate AI models for music generation.","To manage AWS IAM roles.","To configure Amazon EC2 instances.","To monitor network traffic.","The DeepComposer console provides the interface to interact with the service, manage models and create new compositions."
"What is the 'training data' in the context of AWS DeepComposer primarily composed of?","Collections of MIDI files.","Sets of audio recordings.","Sheet music images.","Text-based music descriptions.","DeepComposer uses existing music, usually in MIDI format, as training data to learn musical styles and patterns."
"Which AWS service does DeepComposer use to store trained models?","Amazon S3","Amazon RDS","Amazon DynamoDB","Amazon EBS","Trained models, along with other related data, are typically stored in Amazon S3 for easy access and scalability."
"In DeepComposer, what does the term 'composition' generally refer to?","A MIDI file generated by the model.","A set of audio effects.","A description of a musical style.","A list of musical instruments.","In DeepComposer, a 'composition' is the resulting musical piece created by the trained model, usually stored as a MIDI file."
"Which machine learning framework is primarily used by AWS DeepComposer under the hood?","TensorFlow","PyTorch","Caffe","Theano","While specifics may vary, TensorFlow is a popular choice for deep learning tasks, and commonly used in DeepComposer's architecture."
"What is the purpose of the 'style model' in AWS DeepComposer?","To influence the characteristics of the generated music.","To correct errors in the input data.","To automatically name the generated compositions.","To visualise the training process.","The style model allows users to guide the GAN, influencing the type of music it creates, e.g. jazz, classical etc."
"What role does the MIDI keyboard play in the AWS DeepComposer workflow?","Provides real-time input for the AI model.","It acts as a storage device for MIDI files.","It displays musical scores.","It connects to external speakers.","The MIDI keyboard allows the user to provide an initial melody which is then fed into the AI model for generation."
"What is the purpose of the AWS DeepComposer chart in the console?","To visualise the loss function during training.","To display the current CPU utilisation.","To show the current storage capacity.","To display network latency metrics.","The chart visualises the loss function during training, indicating the model's performance over time and helping users evaluate training progress."
"What is the advantage of using AWS DeepComposer over other music generation tools?","It provides a user-friendly interface integrated with AWS services.","It offers unlimited storage capacity.","It is completely free of charge.","It supports all music genres.","AWS DeepComposer offers a streamlined way to explore generative AI within the AWS ecosystem, providing easy integration with other AWS services."
"In AWS DeepComposer, what does 'fine-tuning' a model refer to?","Further training a pre-trained model with custom data.","Adjusting the volume levels of the generated music.","Changing the tempo of the composition.","Selecting the musical instruments.","Fine-tuning involves taking a pre-trained model and training it further with a new dataset, allowing users to adapt the model to their specific needs or style."
"Which AWS service is used to host the DeepComposer web application?","AWS Amplify","Amazon EC2","AWS Lambda","Amazon ECS","AWS Amplify is commonly used to host web applications that require scalability, security, and ease of deployment. It allows developers to build and deploy fullstack applications with CI/CD workflows."
"What does the AWS DeepComposer leaderboard track?","The accuracy of the generated music compared to original compositions.","The computational resources used by different models.","The popularity of different musical styles.","The number of users using the DeepComposer service.","The leaderboard tracks the accuracy of the generated music compared to original compositions."
"What type of input data is best suited for AWS DeepComposer training?","MIDI files with clear musical structure.","Audio recordings with spoken words.","Image files of musical scores.","Text files containing lyrics.","AWS DeepComposer excels when trained with MIDI files that have a clear musical structure, allowing the model to learn and replicate musical patterns effectively."
"What is a key benefit of using AWS DeepComposer for music creators?","It can help creators explore new musical ideas and styles.","It guarantees royalties on generated music.","It eliminates the need for musical instruments.","It replaces human composers entirely.","AWS DeepComposer serves as a creative tool, providing musicians with new perspectives and inspiration by generating novel musical ideas and styles."
"In the context of AWS DeepComposer, what is the purpose of the 'latent space'?","A multi-dimensional space where musical features are encoded.","A storage area for temporary MIDI files.","A directory containing training data.","A graphical representation of musical notes.","The latent space is a multi-dimensional space in which the GAN encodes the essence of the training data, and where new musical variations are generated."
"Which of these is NOT a key benefit of using AWS DeepComposer?","Automated music licensing","AI-powered music generation","Exploration of new musical styles","Hands-on experience with AI and ML","Automated music licensing is not a feature directly offered by DeepComposer. The other options are core benefits of the service."
"What is the typical workflow when using AWS DeepComposer?","Input melody -> Style selection -> AI generation -> Output MIDI","Upload MIDI file -> Transcribe to sheet music -> Edit in DAW","Connect to a physical orchestra -> Conduct the performance -> Record the audio","Compose lyrics -> Generate melody -> Create music video","The typical workflow involves providing an initial melody, selecting a desired style, leveraging AI to generate the rest of the song, and finally outputting a MIDI file."
"What type of neural network architecture is commonly employed in AWS DeepComposer's style models?","Recurrent Neural Network (RNN)","Convolutional Neural Network (CNN)","Deep Belief Network (DBN)","Support Vector Machine (SVM)","RNNs are commonly used in style models."
"Which file format is primarily used for storing the output compositions generated by AWS DeepComposer?","MIDI (.mid)","MP3 (.mp3)","WAV (.wav)","AAC (.aac)","The compositions are typically stored in MIDI format."
"What is the primary purpose of the AWS DeepComposer sample code and tutorials?","To guide users through the process of building and training music generation models.","To provide pre-built compositions for users to remix.","To offer a database of royalty-free music samples.","To explain the fundamentals of music theory.","The sample code and tutorials help users to understand and build their own custom music generation models using DeepComposer."
"In AWS DeepComposer, what is the relationship between the input melody and the generated composition?","The input melody serves as a starting point for the AI to create a complete song in the chosen style.","The input melody is directly copied into the final composition.","The generated composition is completely independent of the input melody.","The input melody is only used to determine the tempo of the final song.","The input melody acts as a seed, guiding the AI to create a full-fledged song within the chosen style."
"What is the main advantage of using the AWS Management Console for managing DeepComposer projects?","Centralised management of all AWS resources, including DeepComposer models and data.","Faster model training times compared to other interfaces.","Direct access to AWS support engineers.","Automatic software updates for DeepComposer.","The AWS Management Console provides a central point for managing all AWS resources, including those related to DeepComposer projects."
"What is a common use case for AWS DeepComposer in music education?","To teach students about AI, machine learning, and music composition.","To provide students with free musical instruments.","To automatically grade student compositions.","To replace traditional music theory lessons.","AWS DeepComposer serves as an educational tool, introducing students to AI, machine learning, and the principles of music composition in a practical way."
"What is the role of Amazon SageMaker in the AWS DeepComposer ecosystem?","To provide the infrastructure and tools for training the AI models.","To store the generated MIDI files.","To manage user accounts and permissions.","To provide real-time audio processing.","SageMaker provides the underlying infrastructure and tools for training machine learning models in DeepComposer."
"What is a common way to customise the musical style of compositions generated by AWS DeepComposer?","By adjusting the parameters of the style model.","By changing the keyboard settings.","By editing the generated MIDI file directly.","By selecting different musical instruments.","The most direct way to influence the musical style is by adjusting the parameters of the style model."
"What is the purpose of the 'validation dataset' in AWS DeepComposer training?","To evaluate the performance of the model on unseen data.","To store the training data.","To visualise the training process.","To automatically correct errors in the training data.","The validation dataset is used to assess how well the model generalises to new data, helping to avoid overfitting and improve overall performance."
"How does AWS DeepComposer handle potential copyright issues related to generated music?","Users are responsible for ensuring their compositions do not infringe on existing copyrights.","AWS automatically licenses all generated music.","All generated music is considered public domain.","DeepComposer only generates music that is completely original and free from any existing influences.","While DeepComposer generates new music, users still need to ensure their compositions do not infringe on existing copyrights."
"What is the 'inference' stage in AWS DeepComposer related to?","Generating new music using a trained model.","Preparing the training data.","Evaluating the accuracy of the model.","Optimising the model for faster training.","Inference refers to using the trained model to create new music."
"What is the primary benefit of using AWS DeepComposer's pre-trained models?","Faster time to results and less training required.","More accurate compositions compared to custom-trained models.","Lower cost compared to custom-trained models.","Greater flexibility in customising the musical style.","Using pre-trained models allows you to start generating music quickly without needing to train a model from scratch."
"Which programming language is primarily used when working with AWS DeepComposer's APIs and custom models?","Python","Java","C++","JavaScript","Python is commonly used in this context."
"How does AWS DeepComposer facilitate collaboration among music creators?","By allowing users to share their trained models and compositions.","By providing a built-in audio editing tool.","By automatically generating sheet music for ensembles.","By connecting users with recording studios.","DeepComposer makes collaboration easier by allowing you to share your creations and trained models."
"What is a potential limitation of using AWS DeepComposer for generating music?","The generated music may lack the emotional depth and nuance of human compositions.","It requires advanced knowledge of music theory.","It only supports a limited range of musical instruments.","It is not compatible with professional audio editing software.","AI-generated music may not fully capture the complexities and emotions found in human-created pieces."
"What is the role of the 'discriminator' network in AWS DeepComposer's GAN?","To distinguish between real and generated music.","To create the initial melody.","To set the tempo of the composition.","To select the musical instruments.","The discriminator tries to tell the difference between music from the training data and music generated by the generator."
"What does the 'loss function' in AWS DeepComposer training measure?","The difference between the generated music and the training data.","The CPU utilisation during training.","The storage capacity used by the model.","The number of users accessing the DeepComposer service.","The loss function quantifies how well the model is learning, indicating the difference between the generated output and the expected output."
"What is the purpose of AWS DeepComposer's 'Style Transfer' feature?","To apply the style of one piece of music to another.","To automatically transcribe music from audio to MIDI.","To convert MIDI files to different audio formats.","To create variations of a single musical theme.","Style Transfer allows you to apply the stylistic characteristics of one piece of music to another."
"What is a common way to monitor the progress of AWS DeepComposer model training?","By observing the loss function graph in the AWS DeepComposer console.","By listening to the generated music in real time.","By checking the CPU utilisation of the training instance.","By reviewing the AWS CloudTrail logs.","The loss function graph provides visual feedback on the model's learning progress."
"How does AWS DeepComposer contribute to the democratisation of AI and machine learning?","By providing an accessible and engaging way for users to learn about AI through music creation.","By offering free access to all AWS services.","By automatically generating code for machine learning models.","By replacing human data scientists with AI-powered tools.","AWS DeepComposer provides a fun and approachable way to learn about AI by creating music."
"What is a common method for extending the capabilities of AWS DeepComposer?","By integrating with other AWS services and custom code.","By purchasing additional hardware.","By subscribing to premium support.","By using only the pre-built models.","Integration with other AWS services and custom code allow you to customize and extend DeepComposer's functionality."
"What are some of the limitations of training AWS DeepComposer models using small datasets?","The model may overfit to the training data and generate repetitive or predictable music.","The model may take longer to train.","The generated music may be of higher quality.","The model may require more computational resources.","With small datasets, there is a risk that the model memorises the training data and fails to generalise to new musical ideas."
"What is the role of the AWS Deep Learning Containers in the DeepComposer ecosystem?","Provide pre-configured environments with the necessary deep learning frameworks and libraries.","To automatically scale the AWS DeepComposer infrastructure based on demand.","To manage access control policies for DeepComposer resources.","To encrypt data stored in AWS DeepComposer.","AWS Deep Learning Containers provide pre-built environments that simplify the process of setting up and managing the software dependencies required for training deep learning models."
"What is a key difference between AWS DeepComposer and other cloud-based music creation tools?","AWS DeepComposer leverages AI and machine learning for music generation.","AWS DeepComposer offers unlimited storage for musical compositions.","AWS DeepComposer is completely free to use.","AWS DeepComposer supports all musical instruments and genres.","AWS DeepComposer uses AI."
"In AWS DeepComposer, what does 'overfitting' typically sound like in generated music?","Repetitive patterns and lack of originality.","Too much variation and incoherence.","Silence and pauses.","Abrupt changes in tempo and key.","Overfitting is when the model learns the training data too well, which in music results in lack of originality."
"What is the purpose of the AWS DeepComposer community forums?","To connect with other DeepComposer users, share ideas, and get help.","To access exclusive discounts on AWS services.","To participate in music competitions and win prizes.","To report bugs and request new features.","The community forums provide a platform for users to connect, share knowledge, and collaborate."
"What is a typical use case for AWS DeepComposer in the context of game development?","To generate background music and sound effects for games.","To automatically create game levels.","To control the game characters' movements.","To translate game text into different languages.","Generating music is a key component for Game Development"
"How can AWS DeepComposer be used to create music for video content?","To generate custom soundtracks and themes for videos.","To automatically edit and enhance video footage.","To add subtitles to videos.","To create animated characters for videos.","Creating unique soundtracks is important for video content"
"How does AWS DeepComposer enable real-time music generation?","By using low-latency cloud infrastructure and optimised AI algorithms.","By connecting to a physical orchestra in real time.","By pre-generating all possible musical combinations.","By using special hardware accelerators.","Low-latency is key"
"In AWS DeepComposer, what type of AI service is primarily used for generating music?","Generative AI","Predictive AI","Descriptive AI","Diagnostic AI","Generative AI algorithms are at the core of DeepComposer, as they create new musical pieces based on input melodies and training data."
"What is the primary input method used with AWS DeepComposer?","MIDI keyboard","Microphone","Text prompt","Mouse clicks","DeepComposer is designed to work with a MIDI keyboard to allow users to play initial melodies and interact with the AI model."
"What type of model is used by AWS DeepComposer to generate compositions?","Transformer","Regression","Clustering","Decision tree","DeepComposer utilises transformer based models to analyse music, understand the underlying structure and then generate output compositions that follow similar patterns."
"What is the purpose of the AWS DeepComposer console?","To train and deploy generative models","To manage EC2 instances","To monitor network traffic","To configure security groups","The DeepComposer console allows users to interact with pre-trained AI models, compose music, and further train models using their own compositions."
"In AWS DeepComposer, what is a 'style'?","A pre-defined musical genre or set of characteristics","A specific type of MIDI keyboard","A method for encrypting music files","A tool for audio editing","In DeepComposer, a style defines the musical characteristics of the generated composition, allowing users to choose genres like jazz, rock, or classical."
"What output format does AWS DeepComposer typically generate?","MIDI files","MP3 files","WAV files","TXT files","DeepComposer primarily generates MIDI files, which can then be converted into other audio formats."
"What is the AWS DeepComposer Chart?","A monthly competition where users can submit their AI-generated music","A graphical representation of CPU usage","A list of available machine learning algorithms","A guide on setting up the DeepComposer keyboard","The DeepComposer Chart is a monthly competition where users can submit their music generated using DeepComposer and compete for recognition."
"What is a benefit of using AWS DeepComposer?","It provides a hands-on way to learn about generative AI","It automates complex software development tasks","It optimises database performance","It manages cloud infrastructure costs","DeepComposer offers a practical and interactive way to learn about generative AI and experiment with creating music using machine learning."
"Which AWS service is deeply integrated with AWS DeepComposer for storing models and datasets?","Amazon S3","Amazon EC2","Amazon RDS","Amazon DynamoDB","Amazon S3 is commonly used to store the models and datasets used by DeepComposer."
"When training a custom model in AWS DeepComposer, what data is typically used?","MIDI files of existing music","Text files containing lyrics","Images of musical instruments","Sensor data from musical performances","When training custom models in DeepComposer, MIDI files of existing music are used as the training data to teach the model the desired style and patterns."
"What does 'overfitting' refer to in the context of AWS DeepComposer model training?","The model memorises the training data instead of generalising","The model fails to learn any patterns from the training data","The model trains too quickly","The model uses too much memory","Overfitting occurs when the model learns the training data too well, resulting in poor performance on new, unseen data."
"What role does Amazon SageMaker play in the AWS DeepComposer workflow?","Model training and deployment","Data warehousing","Content delivery","Serverless computing","SageMaker is used for training and deploying the machine learning models used within DeepComposer."
"In AWS DeepComposer, what is 'latency' a measurement of?","The time taken to generate a music sample","The number of concurrent users","The size of the model file","The complexity of the musical piece","Latency, in this context, refers to the time taken for the model to generate a musical output based on the input melody."
"What is the purpose of the 'inference' step in the AWS DeepComposer workflow?","Generating new music based on the trained model","Collecting training data","Optimising the model parameters","Visualising the training progress","Inference is the process of using a trained model to generate new music based on a given input."
"What musical element can AWS DeepComposer models typically generate?","Melody","Lyrics","Harmony","Sound effects","DeepComposer models are specifically tailored to generate melodies based on input and chosen styles."
"Which element is crucial for effectively using AWS DeepComposer?","Understanding of music theory","Knowledge of AWS CLI","Expertise in graphic design","Familiarity with database management","Understanding of music theory will help you to more effectively express your musical vision when interacting with DeepComposer."
"What is the primary purpose of the AWS DeepComposer Community?","To share music, collaborate, and learn from other users","To report bugs in the AWS service","To manage AWS billing","To access AWS documentation","The AWS DeepComposer Community exists for users to connect, share their creations, collaborate, and learn from one another about using the service and AI in music."
"What does 'fine-tuning' a pre-trained AWS DeepComposer model involve?","Training the model with additional data to adapt it to a specific style","Adjusting the model's hyperparameters","Re-writing the model's code","Changing the model's architecture","Fine-tuning a pre-trained model involves training it with additional data specific to the desired output style to enhance its performance in that particular genre or style."
"What's a common issue that may require re-training an AWS DeepComposer model?","Poor generalisation to new musical ideas","Slow training time","Incompatibility with certain MIDI keyboards","Insufficient storage space","If the model doesn't generalise well to novel musical ideas, showing that it has just memorised the training examples then re-training is usually required."
"What is the function of 'temperature' in AWS DeepComposer's music generation?","Controls randomness","Controls the volume","Controls the tempo","Controls the octave","Temperature is a parameter that controls the randomness of the generated music; a higher temperature results in more random outputs, and a lower temperature makes the outputs more predictable."
"Which is an advantage of pre-trained models in AWS DeepComposer?","Require less time and data to create a working model","Offer complete customisation","Are always more accurate than custom models","Can be used without an AWS account","Pre-trained models dramatically reduce the amount of time and resources required to get started since someone else has already done the heavy lifting of training a base model."
"What is the first step in composing with AWS DeepComposer?","Playing a starting melody on the keyboard","Selecting a cloud region","Configuring IAM permissions","Downloading the AWS SDK","The process of composing with DeepComposer involves playing a starting melody on the MIDI keyboard, which the AI uses as a base for generating new music."
"What is the purpose of the 'Variations' feature in AWS DeepComposer?","To generate different versions of a melody","To adjust the tempo of the composition","To change the key of the music","To add instruments to the song","The Variations feature generates multiple versions of a melody based on the chosen style, allowing you to explore a range of possibilities."
"What is the role of AWS IAM in securing your AWS DeepComposer environment?","Managing access to AWS resources","Monitoring system performance","Encrypting data at rest","Automating software deployments","AWS IAM enables you to securely manage access to your AWS resources, defining who can do what within your DeepComposer environment."
"Which component of AWS DeepComposer allows you to train your own custom AI models?","SageMaker integration","DeepComposer keyboard","DeepComposer chart","DeepComposer community","The integration of Amazon SageMaker with AWS DeepComposer enables you to train your own custom AI models using your own data and musical styles."
"What can influence the cost of using AWS DeepComposer?","The amount of model training time and data storage","The number of users accessing the service","The number of available AWS regions","The length of the generated music","The cost is primarily influenced by the amount of time spent training models and the amount of storage used to store your models and datasets."
"Which type of algorithm would you typically use to pre-process audio data before passing it to AWS DeepComposer?","Feature extraction","Data encryption","Data compression","Noise reduction","Feature extraction algorithms are applied to derive meaningful numerical features from audio data suitable for training ML models."
"Which of these is a security best practice for AWS DeepComposer?","Use IAM roles with least privilege","Share your AWS credentials with colleagues","Disable multi-factor authentication","Use the root user for all tasks","Granting the minimum level of access needed to AWS resources helps you avoid accidentally granting to wide permissions."
"What is the most important parameter for judging the quality of the music output from AWS DeepComposer?","Subjective musical assessment","Computational complexity","Model training time","Number of lines of code","Subjective musical assessment by humans is generally the best way of determining the quality of the music output. Ultimately, if the music doesn't sound good, the process has failed."
"Which scenario is best suited for using AWS DeepComposer?","Experimenting with AI-driven music creation","Predicting customer churn","Optimising supply chain logistics","Managing network security","DeepComposer is designed specifically for experimenting with using artificial intelligence to drive music creation."
"How do you typically start a new composition in AWS DeepComposer?","Record a short melody as a starting point","Write a Python script to generate music","Upload a CSV file containing musical notes","Connect to a database of existing music","The first step is usually to record a short melody on a MIDI keyboard which will be the base of the composition."
"What data is used for training custom AWS DeepComposer models?","MIDI files of existing musical pieces","WAV files of orchestral recordings","JPG images of music scores","TXT files of musical notation","AWS DeepComposer models require MIDI data to be trained and to then be able to generate MIDI data."
"What benefit do AWS DeepComposer workshops provide?","Hands-on training and guidance","Free AWS credits","Access to exclusive software","Job placement assistance","AWS DeepComposer workshops are designed to provide hands-on training and guidance in using the service and machine learning for music composition."
"Which file extension is commonly associated with the audio data used with AWS DeepComposer?","MIDI","WAV","MP3","AIFF","MIDI files are by far the most common files associated with AWS DeepComposer and its audio generation as it's an efficient way to store notes."
"How can you evaluate the performance of a custom AWS DeepComposer model?","Listening to the generated music and assessing its quality","Checking the CPU utilisation of the training instance","Counting the number of lines of code in the model","Measuring the network latency","The ultimate evaluation of a DeepComposer model comes down to subjective listening by a human and the quality of its output."
"In AWS DeepComposer, what is 'Generative Adversarial Network' (GAN)?","A machine learning architecture used for generating new data","A type of MIDI keyboard","A music notation software","A cloud computing service","Generative Adversarial Networks (GANs) are a type of neural network architecture used to generate new, realistic data, in this case, music."
"Which type of AWS account is required to use AWS DeepComposer?","Any AWS account","AWS Free Tier account","AWS Enterprise Support account","AWS Educate account","You can use any AWS account, however, remember some of the more advanced functionality may exceed free-tier thresholds and incur costs."
"What is the 'AWS DeepComposer Keytar'?","A MIDI keyboard with built-in generative AI capabilities","A software plugin for music production","A type of virtual instrument","A cloud-based audio workstation","It is essentially a MIDI keyboard for inputting data into DeepComposer, but does not include generative AI capabilities as such."
"What is the most important factor in generating appealing music with AWS DeepComposer?","Creativity and a good starting melody","Hardware specifications of the AWS DeepComposer keyboard","Size of the DeepComposer community","Complexity of the AI model","The model can only amplify your musical starting point, so a creative initial input is critical to getting a good result."
"What type of input data is commonly used to train AWS DeepComposer models?","MIDI files","Audio recordings","Image files","Text documents","MIDI files are ideal for training DeepComposer models as they provide structured musical information like notes, timing and dynamics."
"How do you generally choose the musical style for AWS DeepComposer to use?","Select from pre-set style options within the console","Write a custom Python script","Upload a style configuration file","Record the desired style on the keyboard","AWS DeepComposer provides a number of pre-set styles within the console and you select one as a target style."
"How can you share your AWS DeepComposer-generated music with others?","Download the MIDI file and share it","Share your AWS credentials","Upload the music directly to AWS S3","Create an AWS CloudFormation template","MIDI files can easily be shared with others and there are many pieces of software to convert from MIDI to other filetypes should you require."
"What is a common use case of the AWS DeepComposer service?","Educational tool to learn about AI music generation","Enterprise financial forecasting","Medical image analysis","Robotics process automation","DeepComposer is specifically designed as an educational tool for learning about how AI can be used to generate music."
"What is the difference between transfer learning and zero-shot learning in AWS DeepComposer?","Transfer learning uses a pre-trained model, zero-shot doesn't","Transfer learning generates music, zero-shot doesn't","Transfer learning requires more computational power","Transfer learning and zero-shot learning are the same","Transfer learning utilises the learning from a pre-trained model but zero-shot attempts to use a model in a context that it hasn't explicitly been trained for."
"Which AWS service is used to store and manage AWS DeepComposer's trained models?","Amazon S3","Amazon EC2","Amazon RDS","Amazon SageMaker","Trained models generated from SageMaker training jobs are stored as model artifacts in S3 for later use."
"Which of the following is a limitation of using AWS DeepComposer?","It requires a MIDI keyboard for input","It cannot be used without an AWS account","It is unable to generate music in real-time","It does not support multiple users","AWS DeepComposer always requires a MIDI keyboard to get a starting melody into the system."
"Which AWS DeepComposer component provides a user interface?","The AWS DeepComposer console","The AWS CLI","The AWS SDK","Amazon S3","The AWS DeepComposer console provides a graphical user interface for interacting with the service."
