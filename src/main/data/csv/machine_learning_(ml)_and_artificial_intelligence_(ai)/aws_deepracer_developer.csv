"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What type of learning does AWS DeepRacer primarily use?","Reinforcement Learning","Supervised Learning","Unsupervised Learning","Transfer Learning","Reinforcement learning is the core algorithm used in DeepRacer, where the agent learns through trial and error."
"In AWS DeepRacer, what is the 'reward function' used for?","To provide feedback to the agent on the quality of its actions","To define the environment's physical characteristics","To store the car's telemetry data","To visualise the training progress","The reward function is crucial as it guides the agent to learn optimal behaviour by rewarding desired actions and penalising undesired ones."
"Which AWS service is most directly used for storing the trained models in AWS DeepRacer?","Amazon S3","Amazon EC2","Amazon Lambda","Amazon DynamoDB","Amazon S3 is the primary storage service used for storing trained models and other assets in DeepRacer."
"What is the purpose of 'waypoints' in the AWS DeepRacer console?","To define the racing track's geometry and guide the car's path","To visualise the car's telemetry data","To create obstacles on the track","To set the simulation speed","Waypoints are used to define the track's shape and are crucial for calculating the car's position and reward during training."
"What is the function of the AWS DeepRacer Simulator?","To provide a virtual environment for training the reinforcement learning model","To physically assemble the DeepRacer car","To design new racing tracks","To analyse the car's performance in a real-world environment","The simulator allows you to train your model without needing to drive the actual car until the model is sufficiently trained."
"What is the role of 'states' in the context of DeepRacer's reinforcement learning?","Representing the car's current observation of its environment","Representing the different stages of the training process","Representing the car's hardware configurations","Representing the different racing tracks available","The 'state' provides a snapshot of the car's environment, such as its position, speed, and distance to the track boundaries, which informs the agent's actions."
"What does 'exploration vs. exploitation' refer to in the context of DeepRacer training?","The balance between trying new actions and using known good actions","The balance between using the simulator and the real car","The balance between training on different track types","The balance between different reward function designs","Finding the right balance between exploration (trying new actions to discover better strategies) and exploitation (using already learned strategies) is crucial for optimal training."
"Which AWS service is used to log and monitor the training progress of your DeepRacer model?","Amazon CloudWatch","Amazon SQS","Amazon SNS","Amazon Cognito","CloudWatch provides metrics and logs that allow you to monitor the training progress, helping you identify areas for improvement."
"In AWS DeepRacer, what is the purpose of the 'action space'?","To define the range of possible steering angles and speeds the car can take","To define the boundaries of the racing track","To specify the sensors available on the car","To manage the user's account permissions","The action space dictates the possible actions the car can take at each step, directly influencing its ability to navigate the track."
"What is the primary function of the AWS RoboMaker service in the context of AWS DeepRacer?","Providing the simulation environment and compute resources for training","Building the physical DeepRacer car","Managing user authentication and authorisation","Analysing telemetry data from the real car","RoboMaker provides the necessary infrastructure for simulating the DeepRacer environment and running the training process at scale."
"What is the purpose of a DeepRacer 'Local Training' using a notebook?","To allow for more customisation and debugging of the training process","To train the model faster than using the AWS DeepRacer console","To avoid incurring AWS costs","To train the car in a local environment instead of a virtual one","Local training in a notebook offers increased flexibility and control over the training process, enabling advanced users to fine-tune their models."
"In the AWS DeepRacer console, what does 'Episode' refer to?","One complete run of the car on the track from start to finish, or until it goes off track","A setting that specifies the number of cars on the track","One lap around the track","The period during which the car is allowed to train","An episode is a single simulation run where the agent tries to complete the track, providing data for reinforcement learning."
"What is the function of the 'discount factor' in the DeepRacer reinforcement learning algorithm?","To determine the importance of future rewards relative to immediate rewards","To reduce the training time","To minimise the risk of over-fitting the model","To determine the optimal learning rate","The discount factor influences how much the agent values future rewards compared to immediate ones, affecting its long-term strategy."
"What happens if you set a very high learning rate for your DeepRacer model?","The training may become unstable and fail to converge to an optimal solution","The training will be faster and more efficient","The model will generalise better to new environments","The car will drive faster on the track","A high learning rate can cause the model to overshoot the optimal solution, leading to instability and poor performance."
"What does 'generalisation' mean in the context of AWS DeepRacer models?","The ability of the model to perform well on tracks it hasn't seen during training","The ability of the model to drive extremely fast","The ability of the model to use different sensors","The ability of the model to learn very quickly","Generalisation is crucial as it allows the car to perform well on tracks that differ from those used in training, making it more versatile."
"Which factor significantly affects the time it takes to train an AWS DeepRacer model?","The complexity of the reward function and the size of the action space","The physical dimensions of the DeepRacer car","The number of users training models simultaneously","The ambient temperature in the training environment","A more complex reward function and a larger action space increase the computational requirements, leading to longer training times."
"How can you prevent your DeepRacer model from overfitting to the training track?","Use a variety of tracks during training and implement techniques like regularisation","Increase the learning rate significantly","Use a very simple reward function","Train the model for a very short period","Training on diverse tracks and using regularisation techniques can help the model generalise better and avoid overfitting to specific track characteristics."
"What is the purpose of the 'stopping condition' in AWS DeepRacer training?","To automatically stop the training process when the model reaches a desired level of performance","To stop the car immediately when it goes off track","To prevent the car from exceeding a certain speed","To limit the amount of AWS resources consumed during training","Stopping conditions ensure that the training process terminates when the model has achieved a satisfactory level of performance, saving time and resources."
"What is the benefit of using multiple GPUs during DeepRacer model training?","Reduced training time due to parallel processing","Improved fuel efficiency of the car","Enhanced sensor accuracy","Lower AWS costs","Multiple GPUs allow for parallel processing of the training data, which significantly reduces the time required to train the model."
"What is the primary function of the 'AWS DeepRacer League'?","To provide a competitive platform for testing and showcasing DeepRacer models","To offer training courses on reinforcement learning","To sell DeepRacer cars and accessories","To develop new reinforcement learning algorithms","The DeepRacer League provides a platform for racers to test and compete with their models, fostering community engagement and knowledge sharing."
"Which of the following is NOT a typical input to the AWS DeepRacer reward function?","The car's current speed and steering angle","The distance to the nearest waypoint","The current lap number","The number of other cars on the track","While a multi-agent simulation could use data about other cars, the standard DeepRacer environment focuses on single-agent racing, so the number of other cars isn't typically used."
"What is the benefit of using a convolutional neural network (CNN) in the AWS DeepRacer model?","CNNs can effectively process image data from the car's camera to understand the track","CNNs reduce the AWS costs of model training","CNNs make the car drive faster","CNNs are easier to understand and debug","CNNs are well-suited for image processing, allowing the model to extract relevant features from the camera input to make informed decisions."
"In AWS DeepRacer, what is the significance of the 'model metadata'?","It contains information about the training parameters, reward function, and track used to train the model","It controls the physical behaviour of the DeepRacer car","It determines the visual appearance of the DeepRacer car in the simulator","It manages user access permissions to the AWS DeepRacer service","Model metadata provides valuable information about how the model was trained, enabling users to understand its behaviour and reproduce or improve upon it."
"What does 'sim2real' refer to in the context of AWS DeepRacer?","The challenge of transferring a model trained in simulation to perform well in the real world","The process of converting a real-world track into a virtual track for training","The use of the simulator to create realistic images of the DeepRacer car","The optimisation of the reward function to work in both simulation and the real world","Sim2real is a key challenge in robotics and reinforcement learning, where models trained in simulation often struggle to perform well when deployed in the real world due to differences in the environment."
"Which of the following actions is MOST likely to improve the lap time of an AWS DeepRacer model?","Optimising the reward function to encourage smoother driving and higher speeds","Increasing the size of the training dataset","Reducing the complexity of the neural network","Training the model for a shorter duration","Optimising the reward function is the most direct way to influence the car's behaviour and improve its lap time."
"How can you use AWS DeepRacer logs to diagnose issues with your model?","By analysing the reward values, episode lengths, and other metrics to identify areas where the model is struggling","By physically inspecting the DeepRacer car for hardware malfunctions","By using the logs to estimate the car's battery life","By using the logs to predict the car's lap time on a new track","Analysing the logs provides insights into the model's learning process, helping you identify and address issues such as poor exploration or overfitting."
"What is the primary advantage of using the AWS DeepRacer Community?","Sharing knowledge, models, and reward functions with other racers","Getting discounts on AWS services","Winning prizes in virtual racing competitions","Accessing exclusive training tracks","The DeepRacer Community fosters collaboration and knowledge sharing, helping racers learn from each other and improve their models."
"Which of the following is a common technique for improving the robustness of an AWS DeepRacer model?","Adding random noise to the sensor inputs during training","Using a very simple reward function","Training the model on only one track","Decreasing the learning rate significantly","Adding noise to the sensor inputs helps the model learn to handle imperfect data, making it more robust to variations in the real world."
"What is the purpose of the 'AWS DeepRacer Vehicle' IAM role?","To grant the DeepRacer car permissions to access AWS resources during training and evaluation","To allow users to control the DeepRacer car remotely","To restrict access to the AWS DeepRacer console","To manage the billing for AWS DeepRacer services","The IAM role grants the DeepRacer car the necessary permissions to interact with AWS services, such as S3 for model storage and RoboMaker for simulation."
"Which of the following is NOT a consideration when designing an AWS DeepRacer reward function?","The physical dimensions of the racing track","Encouraging the car to stay on the track","Encouraging the car to drive smoothly","Encouraging the car to drive fast","While the physical dimensions of the track influence the reward function, the function itself doesn't directly define them. It focuses on behaviour."
"What is the impact of using a high 'exploration rate' in DeepRacer's reinforcement learning algorithm?","The car will try more random actions early in training, potentially discovering better strategies","The car will learn faster","The car will be more likely to stay on the track","The training will be more stable","A high exploration rate encourages the car to explore the state space more thoroughly, which can lead to the discovery of better strategies but may also result in more erratic behaviour early on."
"In AWS DeepRacer, what does 'off-policy' learning mean?","The agent learns from experiences generated by a different policy than the one it's currently following","The agent only learns from experiences it generates itself","The agent does not learn from any experiences","The agent learns from both its own experiences and those of other agents","Off-policy learning allows the agent to learn from a broader range of experiences, which can accelerate the training process and improve performance."
"What is a potential downside of using a very complex reward function in AWS DeepRacer?","It can make the training process more difficult to debug and optimise","The car will drive more slowly","The model will be less likely to generalise","The car will be more likely to go off track","A complex reward function can be challenging to debug and optimise, as it may be difficult to understand how each component is influencing the car's behaviour."
"What is the advantage of using 'AWS CloudFormation' to deploy AWS DeepRacer resources?","It allows you to automate the creation and management of AWS resources in a consistent and repeatable way","It reduces the cost of training AWS DeepRacer models","It improves the performance of the DeepRacer car","It simplifies the process of building the physical DeepRacer car","CloudFormation provides a way to automate the deployment of AWS resources, ensuring consistency and reducing the risk of errors."
"What is the purpose of 'Greedy' policy in AWS DeepRacer?","Always choosing the action that yields the highest immediate reward","Randomly selecting actions","Choosing actions based on a predefined plan","Avoiding risky actions","A greedy policy focuses solely on maximizing immediate reward, potentially sacrificing long-term gains."
"When training a DeepRacer model, what does it mean if the reward graph plateaus?","The model has likely converged and is no longer improving significantly","The simulation environment has crashed","The car is driving too slowly","The reward function is not well-defined","A plateauing reward graph indicates that the model has likely reached a point where it is no longer making significant progress, suggesting that further training may not be beneficial without adjustments."
"Which type of sensor data is primarily used by the AWS DeepRacer car?","Camera images","Lidar data","GPS coordinates","Radar data","The DeepRacer car primarily relies on camera images to perceive its environment and make driving decisions."
"What is a common strategy for improving the performance of a DeepRacer model on hairpin turns?","Adjusting the reward function to heavily penalise going off track on turns","Increasing the car's maximum speed","Removing waypoints from the track","Decreasing the discount factor","Rewarding the agent for staying on track during turns can incentivise it to learn how to navigate them successfully."
"In AWS DeepRacer, what is the purpose of 'cloning' a model?","To create a copy of an existing model for further training or experimentation","To physically replicate the DeepRacer car","To create a new track in the simulator","To share the model with other users","Cloning allows you to create a separate copy of a model, enabling you to experiment with different training parameters or reward functions without affecting the original model."
"How can you use 'AWS SageMaker' with AWS DeepRacer?","To perform advanced model training and hyperparameter optimisation","To physically assemble the DeepRacer car","To control the car remotely in real time","To design new racing tracks","SageMaker provides tools and services for advanced machine learning, enabling you to fine-tune your DeepRacer models and explore different training configurations."
"What is the recommended way to handle different track designs when training an AWS DeepRacer model?","Train the model on a variety of tracks to improve generalisation","Train separate models for each track","Use a very simple reward function that works on all tracks","Ignore the track design and focus on other factors","Training on multiple track designs allows the model to learn more robust features and generalise better to new environments."
"What is 'deadlock' in terms of AWS DeepRacer training?","The car is stuck and cannot move forward, usually against a wall or obstacle","The training process has stopped unexpectedly","The car has reached the maximum speed limit","The reward function is returning zero for all actions","Deadlock is a specific scenario where the agent gets stuck, often because it cannot find a valid action to move forward due to the way it is being rewarded or penalised."
"What is the effect of increasing the number of layers in a DeepRacer neural network?","It can increase the model's capacity to learn complex patterns, but also increase the risk of overfitting","It will always improve the model's performance","It will reduce the training time","It will make the model easier to understand","More layers allow the network to learn more complex relationships in the data, but also increase the risk of overfitting, so careful tuning is needed."
"How can you encourage the DeepRacer agent to stay closer to the centre line of the track?","Provide a higher reward when the car is near the centre line","Penalise the agent for driving slowly","Increase the learning rate","Remove waypoints from the track","Rewarding the agent for staying near the centre line directly incentivises it to drive closer to the optimal path."
"What is the primary benefit of using 'early stopping' during AWS DeepRacer training?","To prevent overfitting by stopping the training when the model's performance on a validation set starts to decline","To speed up the training process","To reduce the AWS costs of training","To prevent the car from going off track","Early stopping helps prevent overfitting by monitoring the model's performance on a validation set and stopping the training when the performance starts to degrade."
"What is the role of 'Hyperparameter optimisation' in AWS DeepRacer model training?","To find the best combination of training parameters, such as learning rate and batch size","To physically tune the DeepRacer car","To optimise the reward function","To design new racing tracks","Hyperparameter optimisation involves searching for the optimal combination of training parameters to achieve the best possible model performance."
"How can you monitor the car's performance in different environments and assess the robustness of your model?","By simulating the DeepRacer car in a variety of track environments","By analyzing the AWS DeepRacer logs","By physically inspecting the car's components","By asking other racers for feedback","Testing your car in diverse environments will allow you to better assess your models overall robustness and identify potential weaknesses in new environments."
"What is a reason to favour the use of continuous action spaces over discrete action spaces in AWS DeepRacer?","Continuous action spaces allows for finer control over steering and speed","Discrete action spaces allow for finer control over steering and speed","Continuous action spaces are always cheaper to train","Discrete action spaces result in faster convergence","Continuous action spaces allow for finer-grained control over the car's steering and speed, leading to smoother and potentially faster driving."
"What is a typical effect of increasing the batch size during AWS DeepRacer training?","Can improve training stability, especially when using larger learning rates","Always results in slower training","Leads to more noisy gradient updates","Always prevents overfitting","Increasing the batch size can reduce the noise in the gradient estimates, leading to more stable training, especially when using larger learning rates."
"In AWS DeepRacer, what is the purpose of the reward function?","To guide the reinforcement learning agent to drive optimally","To define the environment for the simulation","To track the agent's progress during training","To visualise the agent's driving behaviour","The reward function defines what constitutes 'good' behaviour for the agent, incentivising it to learn optimal driving strategies."
"Which AWS service is primarily used for storing and managing your DeepRacer models?","Amazon S3","Amazon EC2","Amazon Lambda","Amazon DynamoDB","DeepRacer models, like any other machine learning artefact, are stored and managed using Amazon S3's scalable storage."
"During AWS DeepRacer training, what does 'exploration' refer to?","The agent trying out different actions to discover the best ones","The process of analysing the reward function","The act of visualising the training environment","The process of tuning hyper parameters","Exploration involves the agent trying various actions, even if they seem initially suboptimal, to potentially discover better strategies."
"What is the main purpose of the AWS DeepRacer simulator?","To provide a virtual environment for training the reinforcement learning agent","To manage the DeepRacer physical car","To generate training data from real-world tracks","To deploy the trained model to a physical car","The simulator allows the agent to train in a virtual environment without needing to run the physical car constantly."
"Which of the following is a key hyperparameter you can tune in AWS DeepRacer?","Learning rate","Image resolution","Track size","Number of obstacles","The learning rate controls how quickly the agent updates its policy based on new experiences."
"What is the purpose of the action space in AWS DeepRacer?","To define the possible actions the agent can take (speed and steering)","To store the agent's current location","To track the best lap time","To visualise the agent's progress","The action space defines the set of all possible actions (combinations of speed and steering) that the agent can choose from."
"What type of machine learning is used in AWS DeepRacer?","Reinforcement learning","Supervised learning","Unsupervised learning","Semi-supervised learning","AWS DeepRacer uses reinforcement learning, where the agent learns by interacting with the environment and receiving rewards or penalties."
"In AWS DeepRacer, what does 'exploitation' refer to?","The agent using its current knowledge to choose actions that maximise reward","The process of exploring the training environment","The act of resetting the simulation","The process of debugging the reward function","Exploitation involves the agent using its learned policy to take actions that are expected to yield the highest reward."
"What does the 'Waypoint' information provide in AWS DeepRacer?","The coordinates of key points along the track centreline","The optimal speed for each section of the track","The location of obstacles on the track","The total length of the track","Waypoints provide the (x, y) coordinates of points along the track centerline, helping the agent understand the track's layout."
"Which programming language is used to write the reward function in AWS DeepRacer?","Python","Java","C++","JavaScript","The reward function, which defines how the agent is rewarded for its actions, is written in Python."
"Which sensor input is available to the AWS DeepRacer vehicle?","Camera images","GPS coordinates","Lidar","Radar","The AWS DeepRacer vehicle primarily relies on camera images as input for perception."
"In AWS DeepRacer, what is the role of the 'episodes' during training?","One complete run of the agent around the track","The number of iterations the agent runs for each action","A single step in the simulation","The process of resetting the simulation","An episode represents one full run (or a partial run if the agent goes off track) around the training track."
"What is the purpose of the 'Local Training' feature in AWS DeepRacer?","To train models on your own computer using a local environment","To train models using a physical DeepRacer car","To optimise the hyperparameters using cloud resources","To share training progress with other users","Local training allows faster iteration without using cloud resources and is available through the DeepRacer Local package."
"What factor MOST influences the AWS DeepRacer vehicle's ability to stay on the track during training?","The reward function","The colour of the vehicle","The length of the training time","The weather conditions","The reward function heavily influences the agent's behavior and its ability to learn to stay on the track."
"What is the purpose of cloning a DeepRacer model?","To create a new model based on the training of an existing model","To make a copy of the environment","To share models with other users","To train the model on multiple devices","Cloning a model allows you to start with a pre-trained model and further refine it or experiment with different hyperparameters."
"What is the difference between 'off-policy' and 'on-policy' reinforcement learning algorithms in the context of AWS DeepRacer?","On-policy algorithms learn from the actions they take, while off-policy algorithms learn from actions taken by a different policy","Off-policy algorithms are more computationally expensive than on-policy algorithms","On-policy algorithms can only be used for local training","There is no difference.","On-policy algorithms learn by evaluating or improving the policy that is used to make decisions, while off-policy algorithms evaluate or improve a policy different from the one used to generate the data."
"In AWS DeepRacer, what does 'discount factor' control?","The importance of future rewards relative to immediate rewards","The learning rate of the agent","The size of the action space","The amount of exploration during training","The discount factor (gamma) determines how much the agent values future rewards compared to immediate rewards, influencing long-term planning."
"What is the relationship between the number of sensors an AWS DeepRacer has and its complexity to train?","More sensors increase the complexity and training time","More sensors decrease the complexity and training time","The number of sensors does not affect complexity or training time","Sensors are irrelevant for training","More sensors provide more data, which can lead to a more robust model, but also increases the complexity of the problem and the training time."
"What does the term 'generalisation' mean in the context of AWS DeepRacer?","The ability of the agent to perform well on tracks it has not seen before","The ability of the agent to drive at very high speeds","The ability of the agent to avoid obstacles","The ability of the agent to drive in different weather conditions","Generalisation refers to the agent's capability to apply its learned knowledge to new, unseen tracks and scenarios."
"What happens when the AWS DeepRacer vehicle goes off track during training?","The episode is terminated and a new one starts","The vehicle continues driving in a random direction","The simulation pauses automatically","The reward function is reset to zero","When the vehicle goes off track, the episode is typically terminated, and a new episode begins to help the agent learn from its mistakes."
"In AWS DeepRacer, what is the benefit of using multiple training tracks?","To improve the agent's ability to generalise to new environments","To reduce training time","To increase the simulation speed","To make the training more visually appealing","Training on multiple tracks exposes the agent to more diverse scenarios, improving its ability to generalise to unseen tracks."
"What is the purpose of the 'Stopping Distance' parameter in AWS DeepRacer?","To define the distance at which the agent should start slowing down before a turn","To measure the total distance the agent travels during training","To track the distance remaining to the finish line","To set the minimum distance the agent must maintain from obstacles","The stopping distance helps the agent anticipate turns and adjust its speed accordingly, contributing to smoother and more efficient driving."
"Which of the following is NOT a typical input to an AWS DeepRacer reward function?","The current steering angle","The distance to the nearest waypoint","The lap number","The number of obstacles avoided","The lap number is not typically a standard input parameter in the reward function, which focuses on immediate driving conditions."
"What type of neural network architecture is commonly used in AWS DeepRacer?","Convolutional Neural Network (CNN)","Recurrent Neural Network (RNN)","Generative Adversarial Network (GAN)","Deep Belief Network (DBN)","CNNs are commonly used in DeepRacer due to their ability to extract features from image data obtained from the camera sensor."
"What is the purpose of the 'exploration vs. exploitation' trade-off in AWS DeepRacer training?","To balance trying new things with using what the agent already knows","To balance the computational resources used during training","To balance the number of training episodes with the training time","To balance the visual appeal of the simulation with its accuracy","The exploration vs. exploitation trade-off ensures that the agent both tries new actions to discover better strategies and uses its current knowledge to maximise reward."
"What is the maximum training time allowed for a DeepRacer model in the AWS console?","Unlimited. Training can continue until stopped manually","24 hours","72 hours","1 week","AWS imposes a limit on the training time to manage resources, however, training can continue until stopped manually."
"What does the 'progress' parameter indicate in the reward function?","The percentage of the track completed so far","The agent's current speed","The number of obstacles avoided","The total training time","The 'progress' parameter represents the percentage of the track that the agent has successfully completed."
"What is the impact of increasing the batch size hyperparameter in AWS DeepRacer?","Potentially smoother training and better use of GPU memory","Faster training time","Increased exploration","Less memory required","A larger batch size can lead to more stable updates and better utilization of GPU memory, but it also reduces the frequency of updates."
"What is the main advantage of using a smaller action space in AWS DeepRacer?","Faster training time and reduced computational cost","More precise control over the vehicle","Improved generalisation to new tracks","Higher top speed","A smaller action space reduces the number of possible actions, making the learning process faster and less computationally expensive."
"Which of the following metrics is MOST useful for evaluating the performance of a DeepRacer model during training?","Average reward per episode","Total training time","Number of crashes","Number of waypoints visited","The average reward per episode provides a good indication of how well the agent is learning and improving its driving strategy."
"In the context of AWS DeepRacer, what is the role of AWS RoboMaker?","To provide the simulation environment for training","To manage the physical DeepRacer car","To deploy models to Amazon SageMaker","To provide the compute resources for local training","AWS RoboMaker provides the simulation environment necessary to train the models."
"What is a common strategy to improve the AWS DeepRacer agent's ability to handle sharp turns?","Increase the steering angle granularity in the action space","Reduce the vehicle's speed","Increase the exploration rate","Decrease the discount factor","Increasing the steering angle granularity provides the agent with more fine-grained control, allowing it to navigate sharper turns more effectively."
"What is one disadvantage of relying solely on camera images for AWS DeepRacer navigation?","Camera images can be affected by lighting conditions and perspective","Camera images require significant processing power","Camera images do not provide enough information about the track layout","Camera images are not supported by the DeepRacer simulator","Camera images are susceptible to changes in lighting, shadows, and perspective, which can affect the agent's performance."
"What is the main benefit of using the DeepRacer console instead of training locally?","The DeepRacer console automatically scales compute resources","The DeepRacer console supports more reward function parameters","The DeepRacer console can support more custom sensor configuration","The DeepRacer console provides more accurate reward function calculations","The DeepRacer console automatically scales the necessary compute resources to allow you to train the best model possible."
"What does the term 'vanishing gradient' refer to in AWS DeepRacer?","A problem where gradients become too small during training, preventing the model from learning","A problem where the vehicle disappears from the simulation","A technique for reducing the size of the trained model","A problem where the vehicle goes off track frequently","Vanishing gradients is a common issue where, during training, the gradients become vanishingly small, preventing weights from updating correctly and hindering learning."
"In AWS DeepRacer, which type of reinforcement learning algorithm does the service primarily use?","Proximal Policy Optimization (PPO)","Q-Learning","SARSA","Deep Q-Network (DQN)","AWS DeepRacer primarily uses the PPO algorithm, known for its stability and efficiency in continuous action spaces."
"What is the purpose of the 'AWS DeepRacer League'?","To provide a competitive platform for DeepRacer enthusiasts","To offer training courses on reinforcement learning","To promote the use of AWS services","To sell DeepRacer vehicles","The AWS DeepRacer League is a global racing league where participants can compete by training their agents and racing them on virtual or physical tracks."
"What type of data transformation might be useful for pre-processing camera images in AWS DeepRacer?","Normalisation","Encryption","Compression","Duplication","Normalisation scales the pixel values to a range (e.g., 0 to 1), which can improve training stability and performance."
"How does increasing the number of layers in the neural network typically affect training in AWS DeepRacer?","It may improve the model's ability to learn complex patterns, but can also increase training time and the risk of overfitting","It always improves the model's performance","It always decreases the training time","It has no effect on training performance","Increasing the number of layers can allow the model to learn more complex features, but can also lead to longer training times and a higher risk of overfitting if not regularised properly."
"What is the function of the 'Region of Interest' in AWS DeepRacer?","To specify an area of the camera image that the model should focus on","To define the area where the vehicle is allowed to drive","To set the boundaries of the training track","To highlight areas where obstacles are located","The region of interest allows you to focus the agent's attention on the most relevant parts of the camera image, such as the track directly ahead."
"What is the purpose of the 'evaluation' phase in AWS DeepRacer training?","To assess the performance of the trained model on a separate track","To visualise the agent's behavior during training","To adjust the hyperparameters of the model","To generate training data","The evaluation phase is used to assess the performance of the trained model on a track that it has not seen during training, providing an indication of its generalisation ability."
"What is the purpose of using 'continuous action space' versus 'discrete action space' in AWS DeepRacer?","Allows for smoother transitions between different speeds and steering angles","Simpler model training","More predictable car behaviour","Lower compute costs","A continuous action space permits the car to smoothly transition between speeds and steering angles offering better control."
"In AWS DeepRacer, why is it important to avoid local optima when designing the reward function?","To ensure that the agent finds the globally optimal driving strategy","To reduce training time","To prevent the agent from crashing","To make the reward function easier to understand","A well-designed reward function should avoid local optima, which are suboptimal solutions that the agent might get stuck in, preventing it from finding the best possible driving strategy."
"Which is the best strategy to avoid overfitting during training?","Early stopping","Increasing the training time","Using a larger reward scale","Removing layers from the neural network","Early stopping is a cross-validation strategy that helps to avoid overfitting the model by stopping the training process before it converges to the training set."
"What is the purpose of the 'AWS DeepRacer Device Software Update'?","To update the software on the physical DeepRacer vehicle","To update the DeepRacer simulator environment","To update the DeepRacer console","To update the RoboMaker environment","The software update ensures the physical DeepRacer vehicle operates with the latest features and improvements and is necessary to connect to the AWS Cloud."
"What is the effect of using a high steering angle in the AWS DeepRacer vehicle's action space?","Enables sharper turns but can also cause the car to veer off-track if used excessively","Slows down the vehicle speed","Improves vehicle stability","Minimises wear on the tyres","A high steering angle facilitates sharp turns but, if not managed effectively, can also lead to oversteering and the vehicle going off-track."
"What is the best way to create an AWS DeepRacer model that reliably drives through all sorts of courses?","Train the model with a diverse set of courses","Use a high learning rate","Use a very basic reward function","Train only with clockwise courses","Training with a set of diverse courses is the best way to ensure that your vehicle will reliably drive through different sorts of courses."
