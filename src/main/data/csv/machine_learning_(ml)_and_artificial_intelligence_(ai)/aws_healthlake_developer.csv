"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS HealthLake?","To store, transform, and analyse healthcare data at scale","To manage and secure cloud infrastructure","To run machine learning models on financial data","To provide real-time video streaming services","HealthLake is designed to ingest, store, transform, and analyse healthcare data, enabling interoperability and insights."
"What data standard is AWS HealthLake primarily designed to support?","HL7 FHIR","HIPAA","DICOM","GDPR","HealthLake uses the HL7 FHIR (Fast Healthcare Interoperability Resources) standard to provide a standardised view of patient and population data."
"Within AWS HealthLake, what does the term 'data normalisation' refer to?","Standardising data into a common format for analysis","Encrypting data for security","Compressing data for storage efficiency","Backing up data for disaster recovery","Data normalisation in HealthLake involves transforming data into a consistent and standardised format, which facilitates analysis and interoperability."
"Which AWS service does AWS HealthLake integrate with for analytics and machine learning?","Amazon SageMaker","Amazon SQS","Amazon Glacier","Amazon SES","HealthLake integrates with Amazon SageMaker, allowing users to build, train, and deploy machine learning models using the transformed healthcare data."
"What security compliance certifications does AWS HealthLake support?","HIPAA eligibility","PCI DSS Level 1","SOC 2 Type II","All of the mentioned certifications","HealthLake is HIPAA eligible and compliant with other industry-standard security certifications, ensuring data privacy and security."
"What type of analysis is AWS HealthLake best suited for?","Population health analytics","Real-time stock trading","Geospatial analysis","Social media sentiment analysis","HealthLake is designed to enable population health analytics by providing a centralised and normalised view of healthcare data."
"What is the main benefit of using AWS HealthLake for healthcare organisations?","Improved data interoperability and analytics capabilities","Reduced cloud storage costs","Faster website loading times","Enhanced video conferencing features","HealthLake improves data interoperability and provides powerful analytics capabilities by centralising and standardising healthcare data."
"What is the role of FHIR (Fast Healthcare Interoperability Resources) in AWS HealthLake?","It provides a standardised data model for healthcare information","It manages user access and permissions","It monitors system performance and uptime","It encrypts data in transit and at rest","FHIR provides a standardised data model for healthcare information, enabling interoperability and easier data exchange."
"How does AWS HealthLake help reduce the burden on healthcare providers?","By automating data transformation and analysis","By providing telemedicine solutions","By managing patient scheduling and appointments","By automating billing and insurance claims processing","HealthLake automates data transformation and analysis, reducing the manual effort required by healthcare providers to gain insights from their data."
"Which AWS service can be used to ingest streaming data into AWS HealthLake?","Amazon Kinesis Data Streams","Amazon S3","Amazon RDS","Amazon EC2","Amazon Kinesis Data Streams is suitable for ingesting streaming data into AWS HealthLake for real-time processing and analysis."
"What is the purpose of the AWS HealthLake console?","To manage and monitor HealthLake data stores","To create and manage virtual machines","To configure network settings","To manage user identities and access","The HealthLake console provides a user interface for managing data stores, configuring settings, and monitoring the status of your HealthLake environment."
"What is the role of AWS Lambda in a typical AWS HealthLake architecture?","To perform custom data transformation and enrichment","To manage user authentication","To provide caching for frequently accessed data","To handle email notifications","AWS Lambda can be used to perform custom data transformation and enrichment tasks as data is ingested into HealthLake."
"What type of data can be stored in AWS HealthLake?","Clinical data, insurance claims, and research data","Financial transaction data","Geospatial data","Social media posts","HealthLake can store various types of healthcare data, including clinical data, insurance claims, and research data, as long as it is conformant with the FHIR standard."
"How does AWS HealthLake support data privacy and compliance?","Through encryption, access controls, and audit logging","Through data compression","Through content delivery networks","Through load balancing","HealthLake supports data privacy and compliance through encryption, access controls, and audit logging, ensuring adherence to regulations like HIPAA."
"Which of the following is a key feature of the AWS HealthLake FHIR transformation process?","Mapping non-FHIR data to the FHIR standard","Creating backups of data","Implementing network security groups","Managing database schemas","The FHIR transformation process involves mapping data from various formats into the FHIR standard, enabling interoperability and standardised analysis."
"What AWS service can be used to query data stored in AWS HealthLake?","Amazon Athena","Amazon Redshift","Amazon DynamoDB","Amazon SQS","Amazon Athena can be used to query data stored in HealthLake using SQL, providing ad-hoc querying capabilities."
"What is the purpose of data versioning in AWS HealthLake?","To track changes to data over time","To encrypt data","To optimise storage costs","To manage user permissions","Data versioning in HealthLake allows you to track changes to data over time, enabling auditing and historical analysis."
"How does AWS HealthLake integrate with electronic health record (EHR) systems?","By ingesting data using FHIR APIs","By replacing existing EHR systems","By providing a user interface for EHR systems","By managing user credentials for EHR systems","HealthLake integrates with EHR systems by ingesting data using FHIR APIs, allowing organisations to centralise and analyse data from multiple sources."
"What role does Amazon CloudWatch play in monitoring AWS HealthLake?","Provides monitoring and logging of HealthLake resources","Manages security groups","Controls access to S3 buckets","Automates deployments","Amazon CloudWatch provides monitoring and logging of HealthLake resources, allowing users to track performance metrics and troubleshoot issues."
"What is the benefit of using AWS HealthLake over building a custom healthcare data lake?","Reduced development effort and faster time to market","Increased control over data formats","Lower storage costs","Unlimited storage capacity","HealthLake reduces development effort and accelerates time to market by providing a managed service specifically designed for healthcare data."
"What does the 'Bulk Data Access' feature in AWS HealthLake enable?","Exporting large datasets for analytics and research","Encrypting data at rest","Managing user access control","Optimising database performance","The 'Bulk Data Access' feature allows users to export large datasets from HealthLake for analytics and research purposes, enabling population-level insights."
"Which of the following is a key consideration when designing a data ingestion pipeline for AWS HealthLake?","Ensuring data quality and completeness","Managing network bandwidth","Optimising CPU utilisation","Minimising memory usage","Ensuring data quality and completeness is crucial for accurate analytics and reliable insights from HealthLake."
"How does AWS HealthLake support de-identification of protected health information (PHI)?","Through integration with services like AWS Comprehend Medical","By automatically deleting PHI data","By restricting access to PHI data","By providing a sandbox environment for testing","HealthLake can integrate with services like AWS Comprehend Medical to help de-identify PHI, allowing researchers and analysts to work with data in a privacy-preserving manner."
"What is the difference between a Data Store and a Data Set in AWS HealthLake?","A Data Store is a container for datasets; a Data Set is a collection of FHIR resources","A Data Store is used to run machine learning models; a Data Set is a collection of configuration settings","A Data Store is used for backups; a Data Set is used for monitoring","A Data Store is the underlying storage mechanism; a Data Set is a user interface","A Data Store is a container for datasets which contains a collection of FHIR resources."
"You need to ensure that any modifications made to FHIR resources in AWS HealthLake are tracked. What feature should you enable?","Versioning","Encryption","Auditing","Optimisation","Versioning enables you to track changes made to FHIR resources in AWS HealthLake over time."
"What is the benefit of using AWS CloudTrail with AWS HealthLake?","Tracks API calls made to AWS HealthLake for auditing and security","Encrypts the health records","Allows for integration with data analytics platforms","Provides dashboards on HealthLake performance","CloudTrail allows you to track API calls, which provides an audit trail of actions performed on AWS HealthLake, improving security and compliance."
"Which AWS service is commonly used to build interactive dashboards and visualisations using data from AWS HealthLake?","Amazon QuickSight","AWS Glue","Amazon Redshift","Amazon EC2","Amazon QuickSight integrates seamlessly with AWS HealthLake data, allowing you to create interactive dashboards and gain insights."
"What type of problem is AWS HealthLake NOT designed to solve?","Transactional data processing","Data interoperability","Standardised health record storage","Analytics on health data","AWS HealthLake is not intended for transactional data processing as it is built for aggregated analytics."
"You need to securely share data stored in AWS HealthLake with a research partner. What AWS service can you use to control access to the data?","AWS Identity and Access Management (IAM)","AWS CloudHSM","AWS Certificate Manager","AWS Shield","AWS IAM (Identity and Access Management) can be used to control and manage access to data in HealthLake, ensuring secure data sharing with research partners."
"What is the purpose of using a data transformation pipeline with AWS HealthLake?","To convert data from different formats to FHIR","To encrypt data at rest","To optimise queries","To accelerate data delivery","The main purpose of using a data transformation pipeline is to convert data from various sources and formats into the FHIR standard."
"How can you enhance the security of your data in AWS HealthLake?","By encrypting the data at rest and in transit","By optimising storage capacity","By regularly auditing access logs","By increasing instance sizes","Encrypting the data at rest and in transit ensures data confidentiality, enhancing security."
"What is the primary benefit of using AWS HealthLake for research institutions?","Facilitates data sharing and collaboration","Provides unlimited storage capacity","Automates research workflows","Decreases regulatory compliance overhead","AWS HealthLake simplifies data sharing and fosters collaboration between researchers by centralising and normalising healthcare data."
"Which AWS service helps automate the data cleansing and ETL processes needed to prepare data for AWS HealthLake?","AWS Glue","Amazon SQS","AWS Lambda","Amazon Inspector","AWS Glue can be used to automate the ETL (Extract, Transform, Load) processes, cleaning and preparing data for use in AWS HealthLake."
"In an AWS HealthLake deployment, what is the purpose of a FHIR endpoint?","To allow applications to interact with the stored data","To backup the data","To manage user access","To optimize search queries","The FHIR endpoint allows applications to interact with HealthLake using the FHIR standard, making it easier to integrate with other healthcare systems."
"Which strategy helps ensure business continuity when using AWS HealthLake?","Automating backups and disaster recovery procedures","Using the most powerful instances","Implementing multi-factor authentication","Optimizing performance","Automating backups and having disaster recovery processes in place is crucial for maintaining business continuity."
"What is the role of data validation in an AWS HealthLake ingestion pipeline?","Ensuring data quality and compliance","Encrypting sensitive information","Reducing storage costs","Increasing processing speeds","Data validation helps ensure the ingested data is accurate, complete, and complies with relevant standards."
"You are experiencing slow query performance in AWS HealthLake. Which of the following actions could improve the query speed?","Optimise queries and ensure proper indexing","Increase security group rules","Reduce the dataset sizes","Switch to a different data store","Optimising queries and ensuring proper indexing helps AWS HealthLake return data faster."
"Which compliance standard is MOST relevant for an AWS HealthLake deployment handling protected health information (PHI) in the US?","HIPAA","PCI DSS","GDPR","SOC 2","HIPAA compliance is most relevant as it is the regulation that addresses the privacy and security of protected health information (PHI) in the US."
"What is a limitation of AWS HealthLake?","Limited regions where available","Cannot store unstructured data","Does not provide data analytics capabilities","Difficult integration with FHIR standards","AWS HealthLake is not universally available in all AWS regions, which can be a limitation for some users."
"How can you use AWS HealthLake to improve clinical decision support?","By providing real-time data insights to clinicians","By managing patient appointments","By processing insurance claims","By optimising hospital bed capacity","Real-time data insights in HealthLake can empower clinicians to make better-informed decisions."
"What is the function of the 'Resource Version Aware' option when querying AWS HealthLake?","To filter resources based on their versions","To optimize query performance","To validate data quality","To encrypt PHI","This allows querying a specific version of a FHIR resource, which is useful for auditing and historical analysis."
"Which AWS HealthLake operation is responsible for converting inbound data to the FHIR format?","Data Transformation","Data Encryption","Data Optimisation","Data Backup","The Data Transformation process is the mechanism which ensures all data within HealthLake adheres to the FHIR standard."
"Which AWS service is commonly used to trigger automated workflows based on changes to data in AWS HealthLake?","AWS Step Functions","Amazon SQS","AWS CloudTrail","Amazon RDS","AWS Step Functions can be used to orchestrate complex workflows that are triggered by changes in AWS HealthLake data."
"In AWS HealthLake, what is the benefit of categorising and tagging data?","Improved data discoverability and governance","Enhanced encryption","Faster processing","Automated back-ups","Categorising and tagging improves the ability to find and manage data within the data lake."
"Which AWS service could be used to build a patient portal leveraging data stored in AWS HealthLake?","AWS Amplify","Amazon SQS","Amazon Connect","AWS WAF","AWS Amplify is designed to build scalable mobile and web applications quickly and easily including patient portals."
"What is the purpose of a 'Read Replica' in the context of AWS HealthLake database management?","HealthLake does not support read replicas","To provide disaster recovery","To improve read query performance","To enable multi-region deployment","HealthLake does not directly offer 'read replicas' in the traditional database sense as it is a managed service optimised for analytics rather than transactional workloads."
"How does AWS HealthLake address the challenge of data silos in healthcare?","By centralising data from disparate sources into a unified platform","By encrypting data for increased security","By providing a standardised data model","By automating administrative tasks","Data silos are addressed by bringing data from various healthcare systems into one place, breaking down the barriers."
"What should you consider when setting data retention policies for your AWS HealthLake data store?","Legal, regulatory, and business requirements","Network bandwidth limitations","Compute resources","Cost of Amazon S3","Understanding and adhering to the regulatory and legal needs regarding record keeping are key to setting a good data retention policy."
"What is the primary purpose of AWS HealthLake?","To store, transform, and analyse healthcare data at scale.","To provide real-time medical diagnosis.","To manage electronic health records for individual patients.","To conduct medical research using simulated data.","HealthLake is designed to allow healthcare organisations to store, transform, and analyse their data in a secure and compliant environment."
"Which data format is natively supported by AWS HealthLake for data ingestion?","HL7 FHIR","JSON","XML","CSV","HealthLake is built specifically to support the HL7 FHIR standard for healthcare data interoperability."
"What type of analytics can be performed on data stored in AWS HealthLake?","Predictive analytics using machine learning models.","Real-time video analysis.","Geospatial data visualisation.","Network traffic analysis.","HealthLake enables predictive analytics by integrating with other AWS services, allowing you to build and deploy machine learning models on your healthcare data."
"What is the purpose of the AWS HealthLake clinical data transformation process?","To normalise and standardise healthcare data for analysis.","To encrypt healthcare data at rest.","To compress healthcare data for storage.","To redact sensitive patient information.","The data transformation process in HealthLake is designed to normalise and standardise healthcare data into the FHIR format, improving data quality and interoperability."
"Which AWS service is commonly used with AWS HealthLake to build machine learning models?","Amazon SageMaker","Amazon Redshift","Amazon QuickSight","Amazon CloudWatch","Amazon SageMaker is a commonly used service for building, training, and deploying machine learning models on healthcare data stored in HealthLake."
"How does AWS HealthLake contribute to improved patient outcomes?","By enabling data-driven insights and personalised care.","By automating surgical procedures.","By providing direct patient communication tools.","By replacing human doctors with AI.","HealthLake contributes to improved patient outcomes by enabling healthcare providers to gain data-driven insights that can be used to personalize care and improve treatment plans."
"What security and compliance standards does AWS HealthLake support?","HIPAA eligibility","PCI DSS compliance","GDPR compliance","CCPA compliance","HealthLake is HIPAA eligible, which means it meets the requirements for storing and processing protected health information (PHI)."
"What is the role of FHIR (Fast Healthcare Interoperability Resources) in AWS HealthLake?","It provides a standardised data model for healthcare information.","It serves as the user interface for accessing healthcare data.","It manages user authentication and authorisation.","It automates the process of data ingestion.","FHIR is a standard that defines how healthcare information can be exchanged over the internet. HealthLake uses FHIR as its primary data model."
"How does AWS HealthLake help reduce the administrative burden on healthcare organisations?","By automating data integration and analytics.","By replacing human administrators with AI.","By eliminating the need for data security measures.","By outsourcing all IT operations to AWS.","HealthLake automates data integration and analytics processes, which reduces the administrative burden on healthcare organisations."
"What type of insights can be derived from data stored in AWS HealthLake?","Trends in disease prevalence and treatment effectiveness.","Stock market predictions.","Weather forecasting.","Social media sentiment analysis.","HealthLake enables healthcare providers to identify trends in disease prevalence and treatment effectiveness, which can inform public health initiatives."
"Which AWS service can be used to visualise data stored in AWS HealthLake?","Amazon QuickSight","Amazon S3","Amazon EC2","Amazon Lambda","Amazon QuickSight is a business intelligence service that can be used to visualise data stored in HealthLake."
"What is a key benefit of using AWS HealthLake compared to traditional data warehouses?","It is specifically designed for healthcare data and interoperability.","It can store unlimited amounts of data.","It automatically scales to handle any workload.","It is free to use.","HealthLake is specifically designed for healthcare data, with native support for FHIR and other healthcare standards. Traditional data warehouses may require more customisation to handle healthcare data."
"What is the primary use case for the 'bulk data export' feature in AWS HealthLake?","Migrating data to another system or for offline analysis.","Real-time data streaming.","On-demand data backup.","Data encryption at rest.","The bulk data export feature is primarily used to export large volumes of data from HealthLake for migration or offline analysis."
"Which feature in AWS HealthLake allows for searching clinical data based on specific criteria?","FHIR Search","SQL Query","Regular Expression Matching","Full-Text Search","FHIR Search allows for searching clinical data based on specific criteria defined in the FHIR standard."
"What type of events can trigger AWS Lambda functions when used in conjunction with AWS HealthLake?","Changes to FHIR resources","New user logins","Network security alerts","System performance metrics","Changes to FHIR resources (e.g., creation, modification, deletion) can trigger Lambda functions for real-time processing and analysis."
"What is the significance of 'Controlled Vocabularies' in the context of AWS HealthLake?","Ensuring consistency and accuracy of clinical data.","Providing data encryption at rest.","Managing user access control.","Optimising data storage costs.","Controlled vocabularies (e.g., SNOMED CT, ICD-10) ensure consistency and accuracy of clinical data by standardising the terms used to represent medical concepts."
"How does AWS HealthLake facilitate data sharing among healthcare providers?","By providing a secure and interoperable platform for exchanging FHIR resources.","By automatically transmitting patient data to government agencies.","By allowing direct access to patient records by anyone with an internet connection.","By printing patient data on paper and mailing it to providers.","HealthLake provides a secure and interoperable platform for exchanging FHIR resources, enabling data sharing among authorised healthcare providers."
"What is the benefit of using AWS HealthLake's 'Smart on FHIR' capabilities?","It allows developers to build applications that seamlessly integrate with HealthLake.","It automates the process of data encryption.","It provides a graphical user interface for data management.","It allows users to access HealthLake from any web browser.","'Smart on FHIR' allows developers to build applications that seamlessly integrate with HealthLake using standardised APIs."
"What role does AWS Identity and Access Management (IAM) play in securing AWS HealthLake?","It controls access to HealthLake resources and data.","It encrypts data at rest and in transit.","It monitors system performance and security threats.","It automatically backs up and restores data.","IAM is used to control access to HealthLake resources and data, ensuring that only authorised users and applications can access sensitive information."
"Which AWS service can be used to create a secure API gateway for accessing data in AWS HealthLake?","Amazon API Gateway","Amazon CloudFront","Amazon VPC","Amazon Route 53","Amazon API Gateway can be used to create a secure and scalable API gateway for accessing data in HealthLake."
"What is the purpose of the 'Clinical Trial Matching' feature, if available, when using AWS HealthLake?","To identify patients who may be eligible for clinical trials based on their medical history.","To automatically enrol patients in clinical trials.","To generate random patient data for research purposes.","To track the progress of clinical trials.","The 'Clinical Trial Matching' feature can help identify patients who may be eligible for clinical trials based on their medical history and other relevant criteria."
"How does AWS HealthLake support population health management?","By aggregating and analysing data from multiple sources to identify trends and patterns.","By providing direct medical care to patients in underserved communities.","By replacing human healthcare workers with AI robots.","By lobbying for healthcare reform.","HealthLake supports population health management by aggregating and analysing data from multiple sources to identify trends and patterns that can inform public health initiatives."
"What is the key difference between AWS HealthLake and Amazon Comprehend Medical?","HealthLake is for storing and analysing healthcare data, while Comprehend Medical extracts insights from unstructured text.","HealthLake performs medical diagnoses, while Comprehend Medical performs medical research.","HealthLake manages patient records, while Comprehend Medical manages hospital inventory.","HealthLake is free to use, while Comprehend Medical requires a paid subscription.","HealthLake is for storing and analysing healthcare data, while Comprehend Medical is a natural language processing service that extracts insights from unstructured medical text."
"What is a potential use case for integrating AWS HealthLake with Amazon Lex?","Building a conversational chatbot for patients to access their medical information.","Automating surgical procedures.","Generating medical diagnoses.","Managing hospital finances.","Integrating HealthLake with Amazon Lex can enable the creation of a conversational chatbot that allows patients to access their medical information and interact with healthcare providers more easily."
"How does AWS HealthLake address the challenge of data silos in healthcare?","By providing a centralised platform for storing and integrating data from multiple sources.","By physically moving all patient records to a single location.","By forcing all healthcare providers to use the same electronic health record system.","By eliminating the need for data security measures.","HealthLake provides a centralised platform for storing and integrating data from multiple sources, breaking down data silos and enabling more comprehensive analysis."
"What is the purpose of the 'data validation' process when ingesting data into AWS HealthLake?","To ensure that the data conforms to the FHIR standard and is accurate.","To encrypt the data at rest.","To compress the data to reduce storage costs.","To redact sensitive patient information.","The data validation process ensures that the data conforms to the FHIR standard and is accurate, improving data quality and interoperability."
"How can AWS HealthLake be used to support pharmaceutical research and development?","By providing access to real-world patient data for drug discovery and clinical trials.","By manufacturing pharmaceutical drugs.","By regulating the pharmaceutical industry.","By providing financial support to pharmaceutical companies.","HealthLake provides access to real-world patient data that can be used for drug discovery, clinical trials, and other research and development activities in the pharmaceutical industry."
"Which of the following is a key feature of AWS HealthLake's audit logging capabilities?","Tracking all data access and modification events.","Encrypting data at rest.","Compressing data to reduce storage costs.","Redacting sensitive patient information.","HealthLake's audit logging capabilities allow you to track all data access and modification events, providing transparency and accountability."
"What are the key benefits of using AWS HealthLake for healthcare organisations compared to building their own data lake?","Reduced cost and complexity, faster time to value, and improved security and compliance.","Lower data storage costs, unrestricted access to data, no need for security measures.","Complete control over the infrastructure, ability to use any data format, no need for compliance certifications.","Unlimited scalability, no maintenance required, no need for technical expertise.","Using HealthLake reduces cost and complexity, accelerates time to value, and provides improved security and compliance compared to building a data lake from scratch."
"What AWS service is used with AWS HealthLake for ingestion and streaming of real-time data?","Amazon Kinesis","Amazon SQS","Amazon SNS","Amazon Glacier","Amazon Kinesis is used to ingest and stream real-time healthcare data into AWS HealthLake."
"How can AWS HealthLake be used to improve the efficiency of healthcare operations?","By automating tasks such as appointment scheduling, billing, and claims processing.","By replacing human doctors and nurses with AI robots.","By eliminating the need for human interaction.","By providing free healthcare to everyone.","HealthLake improves efficiency by automating appointment scheduling, billing, and claims processing based on analysed data, reducing manual work."
"Which of the following best describes the 'Data Store' in AWS HealthLake?","A secure and compliant repository for storing FHIR resources.","A temporary cache for storing frequently accessed data.","A set of pre-built machine learning models.","A tool for visualising healthcare data.","The Data Store in AWS HealthLake is a secure and compliant repository specifically designed for storing FHIR resources."
"What is the significance of 'Provenance' information in the context of AWS HealthLake?","It provides a history of data transformations and actions performed on FHIR resources.","It encrypts data at rest.","It compresses data to reduce storage costs.","It redacts sensitive patient information.","Provenance information provides a history of data transformations and actions performed on FHIR resources, which is important for auditing and compliance."
"How can AWS HealthLake be used to support value-based care initiatives?","By providing data-driven insights to improve care quality and reduce costs.","By automatically generating revenue for healthcare providers.","By replacing human doctors and nurses with AI robots.","By providing free healthcare to everyone.","HealthLake supports value-based care by providing data-driven insights that help healthcare providers improve care quality and reduce costs."
"Which of the following is a common use case for AWS HealthLake in the context of chronic disease management?","Identifying patients at high risk for developing chronic diseases and implementing preventative measures.","Performing surgery on patients with chronic diseases.","Developing new drugs to treat chronic diseases.","Managing hospital finances.","HealthLake helps identify high-risk patients and enables preventative measures for chronic diseases."
"How does AWS HealthLake enable the development of personalised medicine solutions?","By providing a platform for integrating and analysing diverse patient data, including genomic information.","By performing surgery on patients.","By developing new drugs.","By managing hospital finances.","HealthLake supports personalised medicine by integrating and analysing diverse patient data, including genomic information, to tailor treatment plans to individual patients."
"What role does AWS Glue play when integrating data with AWS HealthLake?","ETL for data transformation and normalisation","Performing risk assessments","Managing security access","Monitoring and logging","AWS Glue performs ETL (Extract, Transform, Load) to prepare and normalise data for seamless integration with AWS HealthLake."
"Which AWS service can be used to trigger actions based on events occurring within the HealthLake data store?","AWS Lambda","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Lambda can be triggered by events in HealthLake, enabling automated workflows in response to data changes."
"What level of control does AWS HealthLake provide over data access?","Granular control using IAM roles and policies.","No control, as the data is publicly accessible.","Full control, but only through AWS Support.","Limited control, only at the database level.","AWS HealthLake offers granular control over data access through IAM roles and policies."
"How does AWS HealthLake handle Personally Identifiable Information (PII)?","It provides tools and controls to de-identify and protect PII.","It automatically shares PII with third-party researchers.","It prohibits the storage of PII.","It ignores PII and focuses solely on aggregate data.","HealthLake offers tools to de-identify and protect PII, ensuring compliance with privacy regulations."
"In the context of AWS HealthLake, what does 'FHIR Path' refer to?","A query language for extracting data from FHIR resources.","A data encryption method.","A data compression algorithm.","A network routing protocol.","FHIR Path is a query language used to extract data from FHIR resources within AWS HealthLake."
"When using AWS HealthLake with Federated Identity Management, where can the configuration of the identity provider be found?","AWS IAM","AWS Cognito","AWS Secrets Manager","AWS Systems Manager Parameter Store","The Identity Provider information for federated identity management is usually configured with AWS IAM."
"What is the best way to secure data in transit to AWS HealthLake?","Using HTTPS encryption and secure APIs.","Using standard HTTP protocol.","Storing the data locally before uploading.","Sharing credentials via email.","Using HTTPS encryption and secure APIs is a vital security measure."
"What action will AWS HealthLake take in the event that a FHIR resource cannot be parsed or is invalid during import?","It will log the error and skip the resource, continuing with the rest of the dataset.","It will automatically attempt to correct the resource and continue.","It will halt the import process completely.","It will send an alert to all users associated with the account.","AWS HealthLake will log the error and skip the resource to not halt the entire import process."
"When considering disaster recovery (DR) for AWS HealthLake, what is the best practice?","Use cross-region replication with automated failover.","Rely solely on AWS managed backups.","Manually copy data to another region every week.","Assume that HealthLake is inherently resilient and requires no DR planning.","Cross-region replication with automated failover is the best practice for DR."
"What is the role of the AWS HealthLake 'Message Processing Job'?","To transform and enrich FHIR resources.","To send alerts to users.","To control data encryption.","To monitor the health of the HealthLake instance.","The Message Processing Job is responsible for transforming and enriching the FHIR resources."
"Which of the following strategies could reduce the cost of operating AWS HealthLake?","Archiving infrequently accessed FHIR resources to a lower cost storage tier.","Deleting all the data on a weekly basis.","Increasing the size of the instance.","Using only encrypted data.","Archiving infrequently accessed FHIR resources to a lower cost storage tier can significantly reduce your overall AWS costs."
