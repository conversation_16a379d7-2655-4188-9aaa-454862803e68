"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Fraud Detector, what is the primary function of a detector?","To evaluate events for fraud risk based on defined rules and models.","To store historical fraud data.","To manage user authentication and authorisation.","To automatically generate training data for fraud models.","The core purpose of a detector is to assess the risk of fraud for incoming events, using the rules and models configured within it."
"Which Amazon Fraud Detector component represents a specific fraudulent or legitimate activity you want to detect?","Event","Variable","Entity","Rule","An Event represents the specific activity, such as a purchase or account creation, that you're evaluating for fraud."
"What is the purpose of the 'variables' section in Amazon Fraud Detector?","To define the data elements used in rules and models.","To store the model performance metrics.","To configure the fraud risk threshold.","To define the data sources used by the detector.","Variables define the individual data elements (e.g., IP address, email address) that are used in rules and models to make fraud predictions."
"In Amazon Fraud Detector, what does a 'rule' define?","A condition that, when met, triggers a specific outcome.","A machine learning model used for fraud prediction.","A specific type of event being monitored.","A historical record of fraud events.","Rules allow you to create custom logic to detect fraud, by specifying conditions that trigger certain outcomes, such as assigning a risk score or flagging an event."
"What is the purpose of an 'outcome' in Amazon Fraud Detector?","To define the action taken when a rule is triggered.","To evaluate the performance of a detector.","To filter event data.","To train machine learning models.","An outcome defines what happens when a rule's conditions are met, such as assigning a specific risk score or marking the event as potentially fraudulent."
"Which of the following is a valid event variable type in Amazon Fraud Detector?","EMAIL_ADDRESS","BOOLEAN_NUMBER","ALPHANUMERIC_DATE","NUMERIC_STRING","Fraud Detector accepts EMAIL_ADDRESS as a valid variable type, allowing you to use email addresses in rules and models."
"In Amazon Fraud Detector, which model type is commonly used for fraud detection scenarios without prior labelled data?","Unsupervised learning","Supervised learning","Semi-supervised learning","Reinforcement learning","Unsupervised learning models, like anomaly detection, can identify unusual patterns in data even without knowing what fraud looks like."
"What is the purpose of the Amazon Fraud Detector 'Entity' type?","To represent a specific user, account, or device associated with an event.","To define the data schema for events.","To manage access permissions for the Fraud Detector service.","To categorise different types of fraud.","The Entity represents the actor involved in an event, allowing you to track fraud patterns related to specific users, accounts, or devices."
"Which of the following is a key benefit of using Amazon Fraud Detector?","Customisable rules and machine learning models.","Automatically encrypts all data at rest with KMS.","Directly integrates with all third-party fraud detection services.","Free to use for all AWS customers.","Fraud Detector provides the ability to define custom rules and incorporate machine learning models, which can be tailored to fit specific needs and data."
"What kind of data can Amazon Fraud Detector use as input?","Event data from various sources.","CloudWatch metrics.","VPC Flow Logs.","AWS Config rules.","Amazon Fraud Detector can leverage event data from various sources, like websites, applications and internal systems."
"What is the purpose of the 'Training Data' in Amazon Fraud Detector model training?","To provide labelled examples of fraudulent and legitimate events.","To define the variables used in the model.","To store the model performance metrics.","To configure the model's hyperparameters.","The training data is used to teach the model to differentiate between fraudulent and legitimate events based on past behaviour."
"Which statement is true regarding Amazon Fraud Detector's 'Model Versioning'?","It allows you to track and manage different versions of your models.","It automatically updates models with the latest data.","It allows you to deploy multiple models simultaneously.","It reduces the cost of model training.","Model Versioning enables to track changes and manage different iterations of your models, which facilitates experimentation and improvements."
"When should you consider using a 'Supplemental Data Integration' in Amazon Fraud Detector?","When you need to enrich your event data with external data sources.","When you need to encrypt your event data.","When you need to reduce the cost of event storage.","When you need to improve the performance of your detector.","Supplemental data integration allows you to enhance your event data by incorporating information from external sources, like threat intelligence feeds or third-party databases."
"How does Amazon Fraud Detector help reduce false positives?","By allowing you to define precise rules and use machine learning models trained on your data.","By automatically deleting suspicious events.","By blocking all transactions from high-risk countries.","By using only publicly available data sources.","Fraud Detector helps reduce false positives by allowing you to fine-tune your rules and models, and train them on data specific to your business."
"What is the role of the 'Detector Version' in Amazon Fraud Detector?","To track changes and deployments of a specific detector configuration.","To manage access control for the detector.","To define the data schema for the detector.","To store historical event data processed by the detector.","Detector Versions allow you to track modifications and deployment history for a detector, making it easier to manage and roll back changes if needed."
"Which AWS service can be used to stream event data to Amazon Fraud Detector in real-time?","Amazon Kinesis","Amazon SQS","Amazon SNS","Amazon CloudWatch","Amazon Kinesis is often used to stream event data to Amazon Fraud Detector in real-time, enabling timely fraud detection."
"What is the purpose of the 'GetEventPrediction' API in Amazon Fraud Detector?","To retrieve fraud predictions for a specific event.","To train a new machine learning model.","To create a new detector version.","To delete an existing event.","The GetEventPrediction API is used to request a fraud risk assessment for a given event based on your configured detector."
"Which of the following is a key consideration when choosing the right model type in Amazon Fraud Detector?","The amount and type of labelled data available.","The cost of training the model.","The geographic location of the events.","The programming language used to implement the rules.","The amount and type of labelled data available significantly influences the choice of model type, as some models require labelled data while others can work with unlabelled data."
"What is the function of the 'FraudScore' returned by Amazon Fraud Detector?","It represents the predicted likelihood of fraud for the event.","It defines the cost associated with processing the event.","It indicates the data quality of the event.","It specifies the location of the event.","The FraudScore represents the model's assessment of the event's fraud risk, based on the rules and models configured in the detector."
"How can you evaluate the performance of your Amazon Fraud Detector models?","By using the built-in model performance metrics.","By monitoring the cost of processing events.","By analysing the network traffic associated with events.","By reviewing the AWS CloudTrail logs for the Fraud Detector service.","Fraud Detector provides built-in metrics to assess model performance, such as precision, recall, and AUC."
"When creating a rule in Amazon Fraud Detector, what is the function of the 'expression' field?","To define the condition that must be met for the rule to be triggered.","To specify the data source for the rule.","To define the action to be taken when the rule is triggered.","To store the rule's performance metrics.","The expression field contains the logical condition that must evaluate to true for the rule to execute."
"What type of algorithm does Amazon Fraud Detector use to build the Online Fraud Insights (OFI) model?","XGBoost","Linear Regression","K-Means Clustering","Support Vector Machine","Amazon Fraud Detector uses the XGBoost algorithm to build the Online Fraud Insights (OFI) model to predict fraudulent events."
"Which of the following is a valid use case for Amazon Fraud Detector?","Detecting fraudulent online transactions.","Managing user identity and access.","Monitoring network traffic for intrusions.","Generating compliance reports.","Amazon Fraud Detector is well-suited for detecting fraudulent online transactions by analysing event data and applying custom rules and machine learning models."
"What is the maximum size of a CSV file that can be imported into Amazon Fraud Detector to create datasets?","5 GB","1 GB","10 GB","2 GB","Amazon Fraud Detector supports importing CSV files up to 5 GB in size to create datasets for model training and evaluation."
"What is the 'training data completeness' metric in Amazon Fraud Detector?","It measures the percentage of variables in the training data that have values.","It measures the training time of the model.","It measures the data types of the variables used.","It measures the amount of data used to train the model.","The 'training data completeness' metric in Amazon Fraud Detector measures the percentage of variables in the training data that have values, helping to ensure data quality."
"What is the maximum number of rules allowed in an Amazon Fraud Detector detector?","200","50","1000","10","An Amazon Fraud Detector detector can have up to 200 rules, allowing you to implement complex fraud detection logic."
"What is the role of the 'IAM role' when using Amazon Fraud Detector?","To grant the service permission to access your data and resources.","To manage user authentication for the Fraud Detector console.","To define the data schema for events.","To configure the fraud risk threshold.","The IAM role provides Fraud Detector with the necessary permissions to access your data sources and other AWS resources."
"When using Amazon Fraud Detector, what does 'Variable Importance' refer to?","The relative influence of each variable on the model's predictions.","The cost of processing each variable.","The data quality of each variable.","The encryption status of each variable.","Variable Importance indicates how much each variable contributes to the model's prediction accuracy."
"What is the purpose of 'Continuous Training' in Amazon Fraud Detector?","To automatically retrain models with new data on a regular schedule.","To continuously monitor the performance of models.","To continuously update the service with new features.","To continuously encrypt data at rest.","Continuous Training enables models to adapt to changing fraud patterns by regularly incorporating new data."
"Which model retraining frequency is NOT supported by Amazon Fraud Detector?","Yearly","Monthly","Weekly","Daily","Yearly is not a supported model retraining frequency in Amazon Fraud Detector, the available options are Daily, Weekly, and Monthly."
"What is the purpose of using 'Tags' in Amazon Fraud Detector?","To organize and manage your resources.","To define data validation rules.","To configure fraud risk thresholds.","To encrypt data at rest.","Tags allow you to categorise and manage Fraud Detector resources, making it easier to track costs and apply policies."
"When deploying an Amazon Fraud Detector detector, what is the purpose of defining an 'Execution Mode'?","To specify whether the rules are evaluated sequentially or in parallel.","To configure the detector's access permissions.","To choose the data source for the detector.","To define the detector's geographic region.","The Execution Mode determines whether the rules are processed in sequence or concurrently, affecting the detector's performance and behaviour."
"What is the primary function of the 'Event Orchestration' feature in Amazon Fraud Detector?","To manage the flow of events through different stages of fraud detection.","To encrypt event data at rest.","To define the data schema for events.","To configure the fraud risk threshold.","Event Orchestration is a feature that allows managing the movement of events through various stages of the fraud detection process, enabling a structured and controlled approach."
"How does Amazon Fraud Detector help in reducing the manual review process?","By automating the fraud detection process using machine learning and rules.","By automatically deleting suspicious events.","By blocking all transactions from high-risk countries.","By using only publicly available data sources.","Amazon Fraud Detector automates the fraud detection, reducing the reliance on manual review and enabling faster decisions."
"What is the purpose of the 'External Models' integration feature in Amazon Fraud Detector?","To incorporate fraud prediction models trained outside of Amazon Fraud Detector.","To encrypt data at rest.","To define the data schema for events.","To configure the fraud risk threshold.","The External Models integration allows the import of previously trained models to use in fraud detection."
"Which statement is correct regarding the integration of Amazon Fraud Detector with AWS Step Functions?","Amazon Fraud Detector can be orchestrated within Step Functions workflows for more complex decision-making processes.","Step Functions can trigger Amazon Fraud Detector to train new models.","Step Functions can automatically back up Amazon Fraud Detector configurations.","Amazon Fraud Detector can be used to monitor Step Functions execution costs.","Amazon Fraud Detector's integration with Step Functions enables orchestration within workflows, allowing you to build more sophisticated fraud management processes."
"What is the purpose of the 'Bias Detection' feature in Amazon Fraud Detector?","To identify and mitigate unfair biases in models.","To encrypt data at rest.","To define the data schema for events.","To configure the fraud risk threshold.","The 'Bias Detection' feature in Amazon Fraud Detector helps to identify and mitigate unfair biases in fraud models."
"Which type of data transformations are NOT supported by Amazon Fraud Detector?","Custom Python code","String manipulations","Numerical scaling","Date formatting","Amazon Fraud Detector does not currently natively support custom Python code as a data transformation within the data preparation phase."
"What are the two supported model types within Amazon Fraud Detector?","Online Fraud Insights (OFI) model and Transaction Change Model (TCM)","Support Vector Machine (SVM) and K-Nearest Neighbors (KNN)","Linear Regression and Logistic Regression","Decision Tree and Random Forest","Amazon Fraud Detector provides two model types: Online Fraud Insights (OFI) model and Transaction Change Model (TCM)."
"When should you use the Transaction Change Model (TCM) in Amazon Fraud Detector?","To detect changes in customer behaviour.","To perform real-time fraud analysis.","To detect fraudulent transactions.","To manage compliance reports.","The Transaction Change Model (TCM) should be used to detect changes in customer behaviour that might indicate fraud."
"What are two benefits of using the Transaction Change Model (TCM) model type?","Adaptation to evolving fraud patterns and improved model accuracy","Simplified setup and immediate results","Reduced storage costs and increased compliance","Enhanced data security and decreased network latency","The Transaction Change Model (TCM) adapts to evolving fraud patterns and enhances model accuracy, making it ideal for detecting dynamic fraud scenarios."
"What is the minimum training dataset size required to train a TCM model?","5,000 events.","1,000 events.","10,000 events.","100,000 events.","The minimum dataset size required to train a TCM model is 5,000 events to ensure sufficient data for model training and accuracy."
"What is the maximum number of outcomes that can be defined for a single Amazon Fraud Detector rule?","One","Unlimited","Five","Ten","Only one outcome can be defined per rule in Amazon Fraud Detector to ensure clear and specific actions."
"What are the two supported event timestamp formats when you are importing data?","ISO 8601 UTC and Unix Epoch","YYYY-MM-DD and MM-DD-YYYY","Julian date and Modified Julian Date","RFC 2822 and RFC 3339","Amazon Fraud Detector supports importing data when the timestamp is formatted as ISO 8601 UTC or Unix Epoch."
"What is a potential use case for the Amazon Fraud Detector's pre-built account takeover fraud detection solution?","Detecting brute force attacks on user accounts.","Monitoring network traffic for intrusions.","Managing user identity and access.","Generating compliance reports.","Amazon Fraud Detector's pre-built account takeover fraud detection solution can detect brute force attacks on user accounts."
"How can you monitor the number of 'GetEventPrediction' API calls made to Amazon Fraud Detector?","Using Amazon CloudWatch metrics.","Using AWS CloudTrail logs.","Using Amazon Inspector findings.","Using Amazon VPC Flow Logs.","Amazon CloudWatch metrics can be used to monitor the number of 'GetEventPrediction' API calls made to Amazon Fraud Detector."
"Where can you find the Amazon Fraud Detector service limits?","AWS Documentation","AWS Trusted Advisor","AWS Personal Health Dashboard","AWS Support Center","Amazon Fraud Detector's service limits can be found within the AWS Documentation."
"What can you use to automate the deployment of Amazon Fraud Detector resources and configurations?","AWS CloudFormation or AWS CDK","AWS Systems Manager","AWS Config","AWS CodePipeline","AWS CloudFormation and AWS CDK can be used to automate the deployment of Amazon Fraud Detector resources."
"What is the default event ingestion limit for Amazon Fraud Detector?","100 events per second","10 events per second","1000 events per second","10000 events per second","The default event ingestion limit for Amazon Fraud Detector is 100 events per second."
"How is Amazon Fraud Detector priced?","Pay-as-you-go pricing based on usage.","Flat monthly fee.","Annual subscription.","Free tier for all users.","Amazon Fraud Detector uses pay-as-you-go pricing, meaning you only pay for what you use."
"What is the purpose of the 'Model Insights' dashboard in Amazon Fraud Detector?","To provide detailed performance metrics and explanations for model predictions.","To display the cost of training the model.","To show the number of events processed by the model.","To configure the model's hyperparameters.","The Model Insights dashboard is used to provide detailed performance metrics and explanations for model predictions."
"What is the function of the 'Tags' in Amazon Fraud Detector 'Detectors'?","To assign metadata for organization and tracking.","To configure the fraud risk threshold.","To define the data schema for events.","To encrypt data at rest.","Tags enable the classification of detector resources, enhancing organization and tracking."
"What is the main purpose of 'Explainable AI' (XAI) in Amazon Fraud Detector?","To provide insights into how models arrive at their predictions.","To define data validation rules.","To configure fraud risk thresholds.","To encrypt data at rest.","Explainable AI (XAI) enhances transparency by offering insights into the reasoning behind model predictions."
"In Amazon Fraud Detector, what is the primary purpose of an 'entity'?","To represent a user, customer, or account involved in an event.","To define the outcome of a fraud detection model.","To store historical transaction data.","To configure the AWS region for fraud detection.","An entity in Fraud Detector represents the subject performing an action, like a user making a purchase, and is a key input to the model."
"In Amazon Fraud Detector, what is the purpose of a 'detector'?","To define the end-to-end fraud evaluation process.","To store the data used for fraud detection.","To manage access control policies for fraud prevention.","To visualise fraud detection results.","A detector encompasses the entire fraud evaluation process, linking events, models, and rules together."
"When creating an Amazon Fraud Detector model, which model type is best suited for detecting anomalies in user behaviour?","Online Fraud Insights","Transaction Fraud Insights","Account Takeover Insights","Identity Verification Insights","Online Fraud Insights is best suited for detecting anomalous patterns in online activities, making it ideal for user behaviour analysis."
"What data source is typically used to train an Amazon Fraud Detector 'Transaction Fraud Insights' model?","Historical transaction records","Website access logs","Social media activity","Email communication data","Transaction Fraud Insights models are trained on historical transaction data to learn patterns associated with fraudulent transactions."
"Which Amazon Fraud Detector resource allows you to define specific conditions and actions to take based on model scores?","Rule","Detector","Entity","Event","Rules allow you to specify conditions based on model scores and define actions like approving or rejecting a transaction."
"Which Amazon Fraud Detector evaluation metric helps you determine the percentage of actual fraud cases correctly identified by your model?","Recall","Precision","Accuracy","F1-Score","Recall measures the proportion of actual fraud cases that the model correctly identified.  A higher recall value indicates better detection of fraudulent activities."
"What is the significance of the 'trainingDataLocation' parameter when creating an Amazon Fraud Detector model?","It specifies the S3 location containing the historical data used for training.","It defines the AWS region where the model will be trained.","It sets the retention period for the training data.","It determines the data encryption method for the training data.","The 'trainingDataLocation' parameter points to the S3 bucket and path where the model training data is stored."
"What is the purpose of 'Variable' in Amazon Fraud Detector?","To define the input feature used for fraud detection.","To configure the model type.","To set the prediction threshold.","To manage the IAM role used by Fraud Detector.","A Variable represents an input feature used by Fraud Detector, such as transaction amount or IP address."
"Which Amazon Fraud Detector model deployment option is suitable for real-time fraud detection with low latency?","Online deployment","Batch deployment","Offline deployment","Scheduled deployment","Online deployment provides real-time predictions with low latency, essential for immediate fraud detection during transactions."
"What is the benefit of using Amazon Fraud Detector's pre-trained models?","They reduce the need for manual model training and feature engineering.","They guarantee 100% fraud detection accuracy.","They automatically scale to handle any volume of data.","They allow customisation of the underlying algorithm.","Pre-trained models offer a quick start by leveraging Amazon's expertise and require less manual effort in training and feature engineering."
"What is a primary advantage of using Amazon Fraud Detector compared to building a custom fraud detection system from scratch?","Faster time to deployment and reduced operational overhead.","Complete control over the underlying fraud detection algorithms.","Lower cost for small-scale deployments.","Greater ability to integrate with third-party data sources.","Amazon Fraud Detector streamlines the fraud detection process, reducing time to deployment and operational burdens."
"In Amazon Fraud Detector, what is the purpose of the 'Event Type'?","To define the schema and structure of incoming event data.","To categorise different types of fraud.","To specify the data source for fraud detection.","To manage the retention policy for event data.","The Event Type defines the data schema and structure of the events you send to Amazon Fraud Detector."
"Which action does Amazon Fraud Detector NOT support directly within its rules engine?","Calling external APIs for additional data enrichment.","Assigning risk scores based on predefined thresholds.","Defining variables for rule conditions.","Returning a fraud prediction label.","Amazon Fraud Detector does not directly support calling external APIs within its rule engine; external data enrichment needs to be handled outside of Fraud Detector."
"What is the purpose of the 'AWSServiceRoleForAmazonFraudDetector' IAM role?","To grant Amazon Fraud Detector permissions to access other AWS services.","To allow users to access the Amazon Fraud Detector console.","To restrict access to specific Fraud Detector resources.","To enable encryption of data at rest within Fraud Detector.","This IAM role allows Fraud Detector to access other AWS services, such as S3 for training data, on your behalf."
"Which Amazon Fraud Detector feature allows you to test the performance of your rules and models before deploying them to production?","Simulation","Data validation","Model versioning","A/B testing","Simulation allows you to test rules and models against historical data to assess their performance before production deployment."
"You want to create a new version of an Amazon Fraud Detector model with improved accuracy. What should you do?","Train a new model version using the existing model configuration.","Delete the old model and create a new one.","Modify the existing model directly.","Change the name of the existing model.","Training a new model version allows you to iterate on existing configurations and improve accuracy while keeping the old version available."
"What is the purpose of the 'DescribeModelVersions' API call in Amazon Fraud Detector?","To retrieve details about specific model versions.","To create a new model version.","To delete a model version.","To update a model version.","'DescribeModelVersions' retrieves detailed information about specific model versions, including status and training metrics."
"When integrating Amazon Fraud Detector with your application, what is the typical sequence of steps?","Send event data -> Get prediction -> Take action","Create model -> Train model -> Send event data","Define detector -> Create event type -> Create entity","Evaluate model -> Deploy model -> Monitor performance","The typical sequence involves sending event data to Fraud Detector, receiving a prediction, and then taking action based on that prediction."
"Which of the following is NOT a supported event attribute data type in Amazon Fraud Detector?","Boolean","String","Integer","Array","Arrays are not supported as event attribute data types in Amazon Fraud Detector."
"What is the maximum size of the training data that can be used to train an Amazon Fraud Detector model?","200 GB","10 GB","1 TB","500 MB","Amazon Fraud Detector allows up to 200 GB of training data for model training."
"In Amazon Fraud Detector, what is the recommended approach for handling personally identifiable information (PII) in your event data?","Mask or hash PII before sending it to Fraud Detector.","Store PII directly in Amazon Fraud Detector.","Use a dedicated PII data type for event attributes.","Encrypt PII using a customer-managed key.","Masking or hashing PII is recommended to protect sensitive information while still allowing Fraud Detector to leverage it for fraud detection."
"What is the purpose of the 'ModelVersionStatus' attribute in Amazon Fraud Detector?","To indicate the current status of a model version (e.g., Training, Active, Inactive).","To define the acceptable range of prediction scores for a model version.","To specify the data retention period for a model version.","To manage access control policies for a model version.","The 'ModelVersionStatus' attribute indicates the current status of a model version, which can be 'Training', 'Active', 'Inactive', etc."
"Which Amazon Fraud Detector feature helps you understand the factors that contribute most to a fraud prediction?","Model explainability","Data lineage","Real-time monitoring","A/B testing","Model explainability provides insights into the variables that have the greatest impact on a model's prediction."
"You are experiencing high latency when calling the 'GetPrediction' API in Amazon Fraud Detector. What is the most likely cause?","The model is not deployed in the same region as your application.","The training data is too large.","The model is not optimised for real-time predictions.","The detector is not properly configured.","Deploying your model in the same region as your application can significantly reduce latency."
"What is the purpose of the 'DetectorVersionStatus' attribute in Amazon Fraud Detector?","To indicate the deployment status of a detector version.","To define the model version used by a detector.","To specify the data source for the detector.","To manage the IAM role associated with the detector.","The 'DetectorVersionStatus' attribute indicates the current status of a detector version, whether it is deployed, inactive, or in draft."
"Which AWS service can be used to monitor the performance and health of your Amazon Fraud Detector detectors and models?","Amazon CloudWatch","AWS CloudTrail","AWS Config","Amazon Inspector","Amazon CloudWatch allows you to monitor metrics related to your Fraud Detector detectors and models, such as prediction latency and error rates."
"What is the primary benefit of using Amazon Fraud Detector's Managed Rules?","They simplify the creation of common fraud detection rules.","They allow you to write custom code for fraud detection.","They provide pre-built models for specific fraud scenarios.","They automatically optimise your fraud detection models.","Managed Rules simplify the creation of common fraud detection rules by providing pre-configured rule templates."
"In Amazon Fraud Detector, what is the purpose of the 'External Model Integration'?","To use models trained outside of Amazon Fraud Detector in your detectors.","To export models trained in Amazon Fraud Detector to other platforms.","To share models between different AWS accounts.","To automatically deploy models to multiple regions.","External Model Integration allows you to incorporate models trained outside of Fraud Detector into your Fraud Detector workflow."
"What is the maximum number of rules that can be included in a single Amazon Fraud Detector detector version?","2500","10","200","1000","A single detector version in Amazon Fraud Detector can contain up to 2500 rules."
"You want to automatically retrain your Amazon Fraud Detector model when new training data becomes available. What approach should you take?","Use the Amazon Fraud Detector API to schedule retraining based on data updates.","Manually retrain the model whenever new data is available.","Use AWS Lambda to trigger retraining when new data is uploaded to S3.","Use AWS Step Functions to orchestrate the retraining process.","Using a Lambda function triggered by S3 events allows for automatic retraining whenever new data is available."
"Which feature of Amazon Fraud Detector helps you identify potential data quality issues in your training data?","Data validation","Model explainability","Simulation","Real-time monitoring","Data validation can highlight potential data quality issues in your training data, helping you to improve model accuracy."
"In Amazon Fraud Detector, what is the purpose of the 'TagResource' API call?","To add metadata tags to Fraud Detector resources for organisation and management.","To specify the AWS region for Fraud Detector resources.","To manage access control policies for Fraud Detector resources.","To encrypt data at rest within Fraud Detector.","'TagResource' allows you to add metadata tags to Fraud Detector resources, which can be helpful for organisation, cost tracking, and access control."
"Which type of event data is Amazon Fraud Detector primarily designed to analyse?","Transactional data","Log data","Image data","Sensor data","Amazon Fraud Detector is primarily designed to analyse transactional data for fraud detection purposes."
"What is the purpose of the 'Associated Models' section of a Detector in Amazon Fraud Detector?","To link pre-trained models to rules within the detector.","To configure the training parameters for the detector's models.","To define the input variables for the detector's models.","To manage the IAM roles associated with the detector's models.","The 'Associated Models' section is where you link the pre-trained or custom models to the rules within your detector, enabling the rule to access the model's predictions."
"Which of the following is NOT a valid action that can be taken based on a rule's outcome in Amazon Fraud Detector?","Approve the transaction","Reject the transaction","Send notification","Manual review","While you can trigger a notification outside of Fraud Detector (e.g., using SNS based on the prediction), Fraud Detector itself doesn't directly support sending notifications as a rule action."
"What is the purpose of 'Event Orchestration' in Amazon Fraud Detector?","To manage the sequence of events used for fraud detection.","To integrate Amazon Fraud Detector with other AWS services.","To visualise fraud detection results.","To automatically retrain models based on new data.","Event orchestration refers to the management and coordination of the sequence of events used in the fraud detection process, ensuring that they are processed in the correct order and with the appropriate data."
"When evaluating an Amazon Fraud Detector model, which metric is most important if you want to minimise false positives?","Precision","Recall","Accuracy","F1-Score","Precision measures the proportion of positive identifications that were actually correct, so it's key to minimise false positives."
"You need to ensure that your Amazon Fraud Detector model is only used by authorised users. How can you control access to the model?","Use IAM policies to restrict access to the model.","Use AWS CloudTrail to monitor model access.","Use AWS Config to track changes to the model.","Use AWS Secrets Manager to store the model's API key.","IAM policies are used to control which users and roles have permission to access and use the Amazon Fraud Detector model."
"What is the purpose of the 'trainingDataSchema' parameter in Amazon Fraud Detector?","To define the data types and structure of the training data.","To specify the AWS region where the training data is stored.","To manage access control policies for the training data.","To encrypt the training data.","The 'trainingDataSchema' defines the data types and structure of the columns in your training data, which is crucial for the model to understand the data correctly."
"What is the maximum length of an 'entityId' in Amazon Fraud Detector?","256 characters","128 characters","64 characters","512 characters","The maximum length for an entityId in Amazon Fraud Detector is 256 characters."
"When using the 'Transaction Fraud Insights' model in Amazon Fraud Detector, what is the typical use case?","Detecting fraudulent online transactions.","Detecting account takeover attempts.","Detecting identity theft.","Detecting insider threats.","The 'Transaction Fraud Insights' model is specifically designed for detecting fraudulent online transactions by analysing historical transaction data."
"Which Amazon Fraud Detector feature allows you to monitor the performance of your detectors in real-time and identify potential issues?","Real-time monitoring dashboards","Data validation reports","Model explainability reports","Simulation results","Real-time monitoring dashboards provide insights into detector performance, helping you to identify issues and optimise your fraud detection strategies."
"What is the purpose of the 'kmsEncryptionKeyArn' parameter when creating an Amazon Fraud Detector model?","To specify the KMS key used to encrypt the model data at rest.","To manage access control policies for the model.","To specify the AWS region where the model will be trained.","To configure the model's prediction threshold.","The 'kmsEncryptionKeyArn' specifies the KMS key used to encrypt the model data at rest, providing an extra layer of security."
"You want to create a complex rule in Amazon Fraud Detector that involves multiple conditions and actions. What is the recommended approach?","Use a combination of multiple simpler rules.","Write custom code for the rule.","Use a single rule with nested conditions.","Use a managed rule.","While complex rules are possible, combining multiple simpler rules makes them easier to understand, maintain, and debug."
"What is the purpose of the 'BatchGetVariable' API call in Amazon Fraud Detector?","To retrieve the values of multiple variables for a single event.","To create a batch of new variables.","To delete multiple variables.","To update multiple variables.","'BatchGetVariable' retrieves the values of multiple variables, allowing you to efficiently access the data you need for fraud evaluation."
"Which of the following data transformations is NOT directly supported by Amazon Fraud Detector's variable transformation capabilities?","Concatenation","Mathematical calculations","String manipulation","Text Analysis","While you can perform string manipulation, Amazon Fraud Detector does not directly offer advanced Text Analysis capabilities as a built-in variable transformation."
"What is the maximum number of variables that can be used in a single Amazon Fraud Detector detector?","1000","200","50","Unlimited","Amazon Fraud Detector allows a maximum of 200 variables in a single detector."
"You are using Amazon Fraud Detector to detect fraudulent account registrations. Which type of model is most suitable for this use case?","Account Takeover Insights","Transaction Fraud Insights","Online Fraud Insights","Identity Verification Insights","Account Takeover Insights model is most suitable as it is designed to detect anomalies and patterns associated with fraudulent attempts to gain control of existing accounts."
