"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of an AWS Transit Gateway?","To simplify network connectivity between VPCs and on-premises networks","To provide a managed NAT service","To act as a global content delivery network","To provide serverless compute resources","Transit Gateway simplifies network architecture by acting as a central hub to connect multiple VPCs and on-premises networks, reducing the complexity of peering relationships."
"Which routing strategy is used by AWS Transit Gateway by default?","Dynamic routing using BGP","Static routing","Policy-based routing","Source-based routing","Transit Gateway uses Border Gateway Protocol (BGP) for dynamic route propagation and exchange between connected networks."
"What is the maximum Transmission Unit (MTU) supported by AWS Transit Gateway?","9000 bytes (jumbo frames)","1500 bytes (standard Ethernet)","65535 bytes","576 bytes","Transit Gateway supports jumbo frames with an MTU of up to 9000 bytes, allowing for larger packet sizes and potentially improved network performance."
"What type of attachment is used to connect an AWS Direct Connect gateway to an AWS Transit Gateway?","AWS Transit Gateway Connect","VPC Attachment","VPN Attachment","Direct Connect Gateway Attachment","A Transit Gateway Connect attachment is used to connect an AWS Direct Connect gateway to an AWS Transit Gateway, enabling connectivity between on-premises networks and AWS."
"What is the purpose of the AWS Transit Gateway Network Manager?","To provide a central place to monitor and manage your global network","To automate the creation of VPCs","To manage IAM roles and permissions","To configure DNS settings","The Network Manager allows you to create a global network representation of your AWS and on-premises networks, visualise them and monitor network health."
"When should you use AWS Transit Gateway multicast?","When you need to distribute the same data stream to multiple destinations efficiently","When you need to encrypt data in transit","When you need to perform network address translation","When you need to load balance traffic across multiple instances","Multicast is designed for one-to-many data distribution, making it suitable for scenarios where the same data stream needs to reach multiple recipients simultaneously."
"Which component of AWS Transit Gateway is responsible for isolating traffic between different groups of VPCs?","Route tables","Security groups","Network ACLs","Transit Gateway Connect Peers","Transit Gateway route tables control the routing of traffic between attached VPCs and other networks. Associating different VPCs with different route tables isolates their traffic."
"What is the benefit of using AWS Transit Gateway Inter-Region Peering?","To connect Transit Gateways in different AWS Regions","To connect Transit Gateways in the same AWS Region","To connect VPCs in different AWS accounts","To connect on-premises networks to AWS","Inter-Region Peering allows you to connect Transit Gateways located in different AWS Regions, creating a global network."
"Which AWS service is commonly used with AWS Transit Gateway to establish a secure connection to on-premises networks?","AWS Site-to-Site VPN","AWS CloudFront","AWS S3","AWS Lambda","AWS Site-to-Site VPN is used in conjunction with Transit Gateway to create secure connections between your on-premises networks and your AWS environment."
"How does AWS Transit Gateway simplify network management compared to traditional VPC peering?","It reduces the number of required peering connections","It automates security group configuration","It eliminates the need for route tables","It removes the need for IP address management","Transit Gateway provides a central hub-and-spoke model, which drastically reduces the number of peering connections required compared to full mesh VPC peering."
"What is a key benefit of using AWS Transit Gateway over VPC peering for connecting many VPCs?","Simplified management of complex network topologies","Lower latency network connections","Automatic encryption of traffic","Simplified security group configuration","Transit Gateway simplifies management by centralising network control and reducing the number of required peering connections, especially in large and complex network environments."
"How does AWS Transit Gateway handle overlapping CIDR blocks between connected VPCs?","Overlapping CIDR blocks are not allowed","Transit Gateway automatically NATs the traffic","Transit Gateway prioritises traffic based on a pre-defined rule","Transit Gateway randomly drops packets from overlapping CIDR blocks","Transit Gateway requires that connected VPCs have non-overlapping CIDR blocks to ensure proper routing and avoid conflicts."
"What is the purpose of a Transit Gateway route table association?","To associate a VPC attachment with a specific route table","To associate a security group with a Transit Gateway","To associate a Network ACL with a Transit Gateway","To associate an IAM role with a Transit Gateway","Route table associations determine which route table a VPC attachment will use for routing traffic, controlling the flow of network traffic."
"When would you choose to use AWS Transit Gateway Connect attachments over standard VPC attachments?","When connecting SD-WAN appliances","When connecting VPCs in the same Region","When connecting VPCs in different Regions","When connecting to a Direct Connect gateway","Transit Gateway Connect attachments are used to integrate SD-WAN appliances into your AWS network, enabling seamless connectivity between your on-premises SD-WAN infrastructure and AWS."
"Which protocol is used for routing information exchange between AWS Transit Gateway and connected networks?","Border Gateway Protocol (BGP)","Open Shortest Path First (OSPF)","Routing Information Protocol (RIP)","Internet Control Message Protocol (ICMP)","Transit Gateway utilises BGP for the dynamic exchange of routing information with connected networks, allowing for automatic route propagation and updates."
"Which AWS Transit Gateway feature helps to control the flow of traffic based on tags?","Transit Gateway route table propagation","Transit Gateway Connect Peers","Transit Gateway multicast","Transit Gateway route table associations","Route table propagation controls whether routes from a connected attachment are automatically propagated to a route table based on tags."
"How do you enable traffic inspection for traffic flowing through AWS Transit Gateway?","By routing traffic through a security VPC with inspection appliances","By enabling VPC Flow Logs on the Transit Gateway","By configuring security groups on the Transit Gateway","By configuring Network ACLs on the Transit Gateway","Traffic inspection can be achieved by routing traffic through a dedicated security VPC containing inspection appliances such as firewalls or intrusion detection systems."
"What is the purpose of the 'default route table association' setting in AWS Transit Gateway?","To automatically associate new attachments with a specific route table","To disable route propagation","To enable encryption","To disable default route propagation","The default route table association determines which route table new attachments are automatically associated with when they are created."
"What is a limitation of AWS Transit Gateway compared to traditional routing solutions?","It has a limit on the number of attachments per Transit Gateway","It does not support dynamic routing","It does not support IPv6","It requires manual route configuration","Transit Gateway has a limit on the number of attachments it can support, which may be a limitation for very large or complex networks."
"How can you use AWS Transit Gateway to simplify network management in a multi-account environment?","By centralising network connectivity and routing in a hub-and-spoke model","By automating the creation of VPCs in all accounts","By managing IAM roles across all accounts","By controlling network access using security groups","Transit Gateway provides a centralised hub-and-spoke architecture, simplifying network connectivity and management in a multi-account environment by reducing the complexity of peering relationships and routing configurations."
"Which of the following security features is directly integrated into AWS Transit Gateway?","Route table propagation control","Network Firewall integration","Security Groups","IAM Access Control","Route table propagation control allows for granular control over which routes are propagated to different route tables, enhancing security by segmenting network traffic."
"What type of attachment can be used to connect a shared services VPC to a Transit Gateway?","VPC Attachment","VPN Attachment","Direct Connect Gateway Attachment","Transit Gateway Connect","A VPC attachment is used to connect a VPC, including a shared services VPC, to a Transit Gateway, allowing resources within the shared services VPC to be accessible to other connected networks."
"Which statement is true regarding AWS Transit Gateway route tables?","Each Transit Gateway has at least one route table","Each Transit Gateway can only have one route table","Transit Gateway route tables are automatically configured","Transit Gateway route tables are not required","Each Transit Gateway must have at least one route table. This route table dictates how traffic is routed between connected attachments."
"What is the primary benefit of using AWS Transit Gateway over creating individual VPC peering connections?","Reduced operational complexity and simplified management","Increased network bandwidth","Lower latency","Automatic encryption of all traffic","Transit Gateway simplifies network management by reducing the number of individual peering connections required, especially in complex network topologies with many VPCs."
"Which AWS service enables you to monitor the performance and availability of your AWS Transit Gateway?","AWS CloudWatch","AWS Config","AWS Trusted Advisor","AWS Inspector","AWS CloudWatch allows you to monitor metrics such as bytes in/out, packets in/out, and CPU utilisation for your Transit Gateway."
"What happens to traffic if there is no matching route in the Transit Gateway route table?","The traffic is dropped","The traffic is automatically routed to the internet","The traffic is routed to the default VPC","The traffic is forwarded to all connected VPCs","If there is no matching route in the Transit Gateway route table, the traffic will be dropped."
"You need to connect multiple VPCs, some of which have overlapping CIDR blocks. What is the best approach using Transit Gateway?","You cannot connect VPCs with overlapping CIDR blocks to a Transit Gateway.","Use Transit Gateway Connect to NAT the traffic.","Use a separate Transit Gateway for each VPC with overlapping CIDR blocks.","Use static routes to avoid the CIDR block overlaps.","Transit Gateway requires that connected VPCs have non-overlapping CIDR blocks to ensure proper routing."
"How does AWS Transit Gateway contribute to network segmentation?","By using separate route tables to isolate traffic between VPCs","By using Security Groups to filter traffic","By using Network ACLs to filter traffic","By encrypting all traffic between VPCs","Transit Gateway allows you to create multiple route tables and associate different VPC attachments with different route tables, enabling network segmentation and isolation."
"You want to inspect all traffic flowing between VPCs connected to your AWS Transit Gateway. What is the recommended approach?","Route the traffic through an inspection VPC with security appliances","Enable VPC Flow Logs on the Transit Gateway","Enable AWS Shield on the Transit Gateway","Configure Security Groups on the Transit Gateway attachments","The recommended approach is to route the traffic through an inspection VPC that contains security appliances like firewalls and intrusion detection systems."
"What is the maximum number of attachments allowed per AWS Transit Gateway?","5000","100","1000","50","There is a default limit of 5000 attachments per Transit Gateway, but this can be increased upon request."
"When configuring a Transit Gateway, what determines which VPCs can communicate with each other?","The route tables associated with the VPC attachments","The security groups configured on the VPC attachments","The Network ACLs configured on the VPC attachments","The VPC CIDR blocks","The route tables associated with the VPC attachments determine which VPCs can communicate with each other by controlling the routing of traffic between them."
"Which AWS service can be used to automate the deployment and configuration of AWS Transit Gateway?","AWS CloudFormation","AWS Systems Manager","AWS Lambda","AWS CloudTrail","AWS CloudFormation allows you to automate the deployment and configuration of Transit Gateway and its related resources."
"How can you enable Transit Gateway Multicast?","By enabling multicast support on the Transit Gateway and configuring source and group associations","By configuring Security Groups to allow multicast traffic","By configuring Network ACLs to allow multicast traffic","By enabling multicast on the VPC attachments","To enable Transit Gateway Multicast, you need to enable multicast support on the Transit Gateway and configure source and group associations to define the multicast traffic flow."
"What is the purpose of an AWS Transit Gateway appliance mode?","To force traffic through a specific virtual appliance for inspection and security purposes","To automatically scale the Transit Gateway based on traffic volume","To enable encryption of traffic between VPCs","To enable multicast on the Transit Gateway","Appliance mode forces traffic to be routed through a specified virtual appliance in a VPC for inspection and security, regardless of destination CIDR."
"What type of traffic is supported with Transit Gateway Connect?","Generic Routing Encapsulation (GRE) traffic","IPSec traffic","All TCP/UDP traffic","Only HTTP/HTTPS traffic","Transit Gateway Connect supports Generic Routing Encapsulation (GRE) traffic, which is commonly used by SD-WAN appliances."
"What does AWS Transit Gateway use to determine the next hop for a packet?","Route tables","Security Groups","Network ACLs","IAM Policies","Transit Gateway uses route tables to determine the next hop for a packet based on the destination IP address."
"What is the purpose of AWS Transit Gateway peering attachments?","To connect Transit Gateways across different AWS regions","To connect Transit Gateways within the same AWS account","To connect VPCs within the same AWS region","To connect on-premise networks","AWS Transit Gateway peering attachments are used to connect Transit Gateways across different AWS regions."
"Which of the following is a key factor in determining the cost of using AWS Transit Gateway?","The amount of data processed by the Transit Gateway","The number of VPCs connected to the Transit Gateway","The number of route tables configured on the Transit Gateway","The number of Transit Gateway Connect Peers","The cost is primarily driven by the amount of data processed by the Transit Gateway."
"You are designing a network with a large number of VPCs and need to ensure that each VPC has access to a set of shared services. What is the most efficient way to accomplish this using AWS Transit Gateway?","Create a central shared services VPC and connect it to the Transit Gateway, then configure the Transit Gateway route tables to allow access from all other VPCs","Create a Transit Gateway peering attachment for each VPC that needs access to the shared services","Configure a VPN connection between each VPC and the shared services VPC","Create a custom route table for each VPC and manually add routes to the shared services VPC","The most efficient approach is to centralise shared services in a dedicated VPC and use Transit Gateway route tables to control access from all other VPCs, minimising the need for individual connections or configurations."
"Which statement is correct regarding the use of security groups with AWS Transit Gateway?","Security groups are applied at the VPC attachments and control traffic entering and exiting the VPCs","Security groups are directly applied to the Transit Gateway and control all traffic flowing through it","Security groups are automatically created by the Transit Gateway","Security groups are not supported with Transit Gateway","Security groups are applied at the VPC attachments, functioning as usual, and control traffic entering and exiting the VPCs."
"When setting up an AWS Transit Gateway, how do you control which VPCs can communicate with each other?","By configuring route tables in the Transit Gateway and associating them with VPC attachments","By configuring security groups at the Transit Gateway level","By configuring Network ACLs at the Transit Gateway level","By configuring VPC peering connections between the VPCs","You control VPC communication by configuring route tables in the Transit Gateway and associating them with the relevant VPC attachments. This dictates the paths traffic can take."
"What is the recommended method for connecting a branch office with a SD-WAN device to AWS using Transit Gateway?","Use Transit Gateway Connect with a GRE tunnel","Use a standard VPN attachment","Use a Direct Connect connection","Use a VPC peering connection","Transit Gateway Connect, utilising GRE tunnels, is the recommended method for connecting SD-WAN devices to AWS Transit Gateway."
"What is the maximum bandwidth supported per connection on an AWS Transit Gateway?","50 Gbps","10 Gbps","100 Gbps","1 Gbps","Each connection on an AWS Transit Gateway supports up to 50 Gbps of bandwidth."
"Which type of attachment would you use to connect an AWS Transit Gateway to a network deployed in an AWS Outposts?","VPC Attachment","VPN Attachment","Direct Connect Gateway Attachment","AWS Outposts Attachment","You would use a VPC Attachment to connect a network deployed in an AWS Outposts to an AWS Transit Gateway. AWS Outposts networks connect to AWS regions via VPC Attachments."
"In AWS Transit Gateway, what is the purpose of a route table?","To control the flow of traffic between connected VPCs and on-premises networks","To store encryption keys for secure communication","To define the security groups associated with the gateway","To track the cost of data transfer through the gateway","Route tables in Transit Gateway determine how traffic is routed between different connected networks, such as VPCs and on-premises environments."
"Which of the following is NOT a valid attachment type for an AWS Transit Gateway?","Direct Connect Gateway","Internet Gateway","VPN Connection","VPC","An Internet Gateway is not a valid attachment type for Transit Gateway. Transit Gateway is designed to connect VPCs and on-premises networks, not provide direct internet access."
"What does AWS Transit Gateway use to ensure isolation between different networks connected to it?","Separate route tables","Security Groups","Network ACLs","IAM Roles","Transit Gateway uses separate route tables to isolate traffic between connected networks. Each attachment is associated with a route table, which determines where its traffic can be routed."
"You want to connect multiple VPCs in different AWS Regions using Transit Gateway. Which feature should you use?","Transit Gateway peering","VPC peering","Direct Connect","Site-to-Site VPN","Transit Gateway peering allows you to connect Transit Gateways in different AWS Regions, enabling inter-region connectivity."
"Which of the following is a benefit of using AWS Transit Gateway over VPC peering for connecting multiple VPCs?","Simplified management of complex network topologies","Lower cost for small deployments","Higher network bandwidth","Native IPv6 support in all regions","Transit Gateway simplifies management of complex networks by providing a central hub for connecting multiple VPCs and on-premises networks."
"What is the primary purpose of AWS Transit Gateway Connect attachments?","To connect SD-WAN appliances","To connect Internet Gateways","To connect NAT Gateways","To connect VPC Endpoints","Transit Gateway Connect attachments enable integration with SD-WAN appliances, allowing you to extend your on-premises network into AWS."
"Which of the following statements is TRUE regarding AWS Transit Gateway route propagation?","Route propagation automatically updates the Transit Gateway route table with routes from attached networks","Route propagation is disabled by default and must be manually enabled","Route propagation only works for VPC attachments","Route propagation only works for VPN attachments","Route propagation automatically updates the Transit Gateway route table with routes learned from attached networks, simplifying route management."
"What is the maximum transmission unit (MTU) size supported by AWS Transit Gateway?","9001 bytes","1500 bytes","8900 bytes","65535 bytes","AWS Transit Gateway supports a maximum transmission unit (MTU) size of 9001 bytes, allowing for larger packets and potentially improved network performance."
"You need to filter traffic flowing through your AWS Transit Gateway based on source and destination IP addresses. Which feature can you use?","Transit Gateway network firewall","Security Groups","Network ACLs","Route tables","Transit Gateway network firewall allows you to filter traffic flowing through the Transit Gateway based on various criteria, including source and destination IP addresses."
"How does AWS Transit Gateway handle overlapping CIDR blocks between connected VPCs?","It does not allow overlapping CIDR blocks","It automatically NATs the traffic","It prioritises traffic based on VPC ID","It randomly routes traffic to one of the VPCs","AWS Transit Gateway does not allow overlapping CIDR blocks between connected VPCs. You must ensure that your VPCs have unique CIDR blocks."
"What is the role of a 'default route' in an AWS Transit Gateway route table?","To route traffic to the internet","To route traffic to the VPC with the smallest CIDR block","To route traffic to any destination not explicitly defined in other routes","To block traffic from unknown sources","A default route in a Transit Gateway route table routes traffic to any destination that is not explicitly defined in other routes within the table, often used for routing to an inspection VPC or a centralised firewall."
"Which AWS service provides detailed logging and monitoring of traffic flowing through an AWS Transit Gateway?","VPC Flow Logs","CloudTrail","CloudWatch Logs","Config","VPC Flow Logs can be enabled on the Transit Gateway attachments to capture information about the IP traffic going to and from network interfaces in your VPC."
"What is the purpose of enabling 'Auto Accept Shared Attachments' on an AWS Transit Gateway?","To automatically accept attachments shared from other AWS accounts","To automatically create route table associations","To automatically propagate routes","To automatically encrypt traffic","Enabling 'Auto Accept Shared Attachments' allows the Transit Gateway to automatically accept attachments that have been shared from other AWS accounts, simplifying cross-account connectivity."
"Which of the following best describes a use case for AWS Transit Gateway's multicast support?","Distributing financial data feeds","Connecting web servers to a database","Routing traffic to the internet","Creating a highly available web application","Transit Gateway's multicast support is often used for distributing financial data feeds or other applications requiring one-to-many communication."
"In AWS Transit Gateway, what happens if you have overlapping routes with the same prefix in a route table?","The most specific route (longest prefix match) is preferred","Traffic is randomly distributed between the routes","The route added first is preferred","An error is thrown and the route table is invalid","In Transit Gateway, the route with the most specific prefix (longest prefix match) is preferred when there are overlapping routes in the route table."
"You want to restrict specific VPCs from communicating with each other via an AWS Transit Gateway. What is the best way to achieve this?","Use separate route tables and avoid associating them","Use Security Groups to block traffic","Use Network ACLs to block traffic","Use IAM policies to restrict access","Using separate route tables and avoiding associations between them is the most effective way to prevent specific VPCs from communicating through a Transit Gateway."
"Which of the following metrics is useful for monitoring the health and performance of an AWS Transit Gateway?","PacketsDropped","CPUUtilization","DiskQueueDepth","MemoryUtilization","The `PacketsDropped` metric can be used to monitor the health and performance of an AWS Transit Gateway by indicating the number of packets that were dropped due to congestion or other issues."
"When creating a VPN attachment to an AWS Transit Gateway, what is required?","A customer gateway device configured on the on-premises network","An internet gateway attached to the transit gateway","A NAT gateway in each VPC","A Direct Connect connection","A customer gateway device must be configured on the on-premises network to establish the VPN connection to the Transit Gateway."
"What is the purpose of the 'Blackhole Route' in an AWS Transit Gateway route table?","To drop traffic destined for a specific CIDR block","To route traffic to the internet","To route traffic to a default gateway","To encrypt traffic","A 'Blackhole Route' in a Transit Gateway route table is used to drop traffic destined for a specific CIDR block, preventing it from reaching its destination."
"Which statement accurately describes the relationship between AWS Transit Gateway and VPC peering?","Transit Gateway is a replacement for VPC peering and offers more advanced features","VPC peering is the recommended solution for connecting more than 10 VPCs","Transit Gateway and VPC peering serve the same purpose and are interchangeable","VPC peering is more suitable for complex network topologies","Transit Gateway is a replacement for VPC peering and offers more advanced features and simplified management for complex network topologies."
"What is the maximum number of attachments that can be associated with a single AWS Transit Gateway route table?","There is no limit","100","50","20","There is no specific documented limit to the number of attachments that can be associated with a single Transit Gateway route table, but practical limits may apply based on performance and management considerations."
"Which AWS service can be used to automate the creation and management of AWS Transit Gateways and their attachments?","AWS CloudFormation","AWS Config","AWS CloudTrail","AWS Systems Manager","AWS CloudFormation can be used to automate the creation and management of Transit Gateways and their attachments, allowing for Infrastructure as Code."
"You need to ensure that traffic flowing through your AWS Transit Gateway is encrypted. Which of the following options is the most appropriate?","Use IPsec VPN tunnels for all attachments","Enable Transit Gateway network firewall","Use Security Groups to encrypt traffic","Enable encryption at rest on all attached VPCs","Using IPsec VPN tunnels for all attachments ensures that all traffic flowing through the Transit Gateway is encrypted in transit."
"What is the benefit of using Transit Gateway's 'Shared Services VPC' architecture?","Centralised management of network services like firewalls and intrusion detection systems","Reduced cost for inter-VPC communication","Increased bandwidth for VPC connections","Simplified internet access for all VPCs","A 'Shared Services VPC' architecture allows for centralised management of network services, such as firewalls and intrusion detection systems, for all VPCs connected to the Transit Gateway."
"When you create an AWS Transit Gateway, what is the initial state of the default route table?","Empty, with no routes defined","Contains a default route to the internet","Contains routes for all attached VPCs","Disabled and must be manually enabled","When a Transit Gateway is created, the default route table is initially empty, with no routes defined. You must manually configure routes in the route table."
"Which AWS service provides centralised network management capabilities for AWS Transit Gateway, including visualisation and automation?","AWS Network Manager","AWS CloudWatch","AWS Trusted Advisor","AWS Control Tower","AWS Network Manager provides centralised network management capabilities for Transit Gateway, including visualisation, monitoring, and automation."
"What type of traffic can be inspected by the AWS Transit Gateway network firewall?","Traffic flowing between VPCs and on-premises networks","Traffic flowing between EC2 instances within the same VPC","Traffic flowing between AWS services","Traffic flowing to the internet","The Transit Gateway network firewall can inspect traffic flowing between VPCs and on-premises networks, providing a centralised point for security inspection."
"What is the purpose of setting up a 'Centralised Inspection VPC' with Transit Gateway?","To inspect all traffic flowing between VPCs and on-premises networks","To provide internet access for all VPCs","To encrypt all traffic flowing through the Transit Gateway","To reduce the cost of inter-VPC communication","A 'Centralised Inspection VPC' allows you to inspect all traffic flowing between VPCs and on-premises networks through a central security appliance."
"You have multiple AWS accounts and want to use a single Transit Gateway to connect VPCs in different accounts. How can you achieve this?","Share the Transit Gateway with other accounts using AWS Resource Access Manager (RAM)","Create a separate Transit Gateway in each account and peer them","Use VPC peering between the VPCs in different accounts","Use Direct Connect to connect the VPCs in different accounts","You can share a Transit Gateway with other AWS accounts using AWS Resource Access Manager (RAM), allowing them to attach their VPCs to the shared Transit Gateway."
"What is the purpose of enabling 'DNS Support' on an AWS Transit Gateway?","To allow VPCs connected to the Transit Gateway to resolve DNS queries from other VPCs","To encrypt DNS traffic","To automatically configure DNS servers in the VPCs","To route DNS traffic to the internet","Enabling 'DNS Support' allows VPCs connected to the Transit Gateway to resolve DNS queries from other VPCs, facilitating communication between services in different VPCs."
"In AWS Transit Gateway, what does the term 'attachment' refer to?","A connection between the Transit Gateway and another network resource, such as a VPC or VPN","A security group associated with the Transit Gateway","A route table associated with the Transit Gateway","An IAM role used by the Transit Gateway","In AWS Transit Gateway, an 'attachment' refers to a connection between the Transit Gateway and another network resource, such as a VPC, VPN connection, or Direct Connect gateway."
"What is the cost model for AWS Transit Gateway?","Hourly charge per attachment, plus data processing charges","Fixed monthly fee per Transit Gateway","Free, with only charges for the underlying resources","Hourly charge per VPC connection","The cost model for AWS Transit Gateway involves an hourly charge per attachment, plus data processing charges for the traffic that flows through the Transit Gateway."
"Which of the following is the MOST secure method of connecting your on-premises network to an AWS Transit Gateway?","AWS Site-to-Site VPN using IPsec","AWS Direct Connect","Public Internet","VPC Peering","AWS Direct Connect provides a dedicated network connection from your on-premises network to AWS, offering more security and reliability compared to VPN over the public internet."
"You are deploying a new application that requires low latency and high bandwidth. Which AWS Transit Gateway attachment type is most suitable for connecting to your on-premises network?","AWS Direct Connect Gateway","AWS Site-to-Site VPN","Transit Gateway Connect","VPC attachment","AWS Direct Connect Gateway provides a dedicated, high-bandwidth connection with low latency, making it ideal for applications that require high performance and reliability."
"When using AWS Transit Gateway, how can you ensure that your on-premises network can reach all of your VPCs without manually configuring routes?","Enable route propagation on the Transit Gateway route table","Create static routes in each VPC's route table","Configure a default route in the on-premises network to the Transit Gateway","Disable route propagation on the Transit Gateway route table","Enabling route propagation on the Transit Gateway route table allows the Transit Gateway to automatically learn routes from attached VPCs and propagate them to other attachments, including the on-premises network."
"Which statement about AWS Transit Gateway route tables is correct?","Each attachment must be associated with exactly one route table","Each attachment can be associated with multiple route tables","Route tables are automatically created for each attachment","Route tables are only required for VPC attachments","Each attachment must be associated with exactly one route table, which determines how traffic from that attachment is routed."
"What is the purpose of AWS Transit Gateway route table associations?","To determine which route table is used for routing traffic from a specific attachment","To determine which attachments can propagate routes to a route table","To encrypt traffic flowing through the Transit Gateway","To define security groups for the Transit Gateway","Route table associations determine which route table is used for routing traffic originating from a specific attachment."
"Which AWS service is best suited to provide detailed visibility and centralised auditing of all changes made to your AWS Transit Gateway configuration?","AWS CloudTrail","AWS Config","AWS CloudWatch","AWS Trusted Advisor","AWS CloudTrail records all API calls made to your AWS account, including changes to your Transit Gateway configuration, providing detailed auditing and visibility."
"You want to implement a hub-and-spoke network topology using AWS Transit Gateway. Which of the following best describes this topology?","A central Transit Gateway connecting multiple VPCs (spokes)","A fully meshed network of VPCs","A chain of VPCs connected sequentially","A single VPC connected to the internet","In a hub-and-spoke topology, the Transit Gateway acts as the central hub, connecting multiple VPCs (spokes) to each other and to on-premises networks."
"Which AWS service can you use to establish a secure connection between your on-premises network and AWS Transit Gateway over the public internet?","AWS Site-to-Site VPN","AWS Direct Connect","AWS PrivateLink","VPC Peering","AWS Site-to-Site VPN allows you to establish a secure connection between your on-premises network and AWS Transit Gateway over the public internet using IPsec."
"What is a key consideration when designing your AWS Transit Gateway network for scalability?","Using multiple Transit Gateways in different regions","Using a single large Transit Gateway for all VPCs","Overlapping CIDR ranges","Attaching internet gateways to the transit gateway","Using multiple Transit Gateways in different regions to accommodate growing network needs and avoid hitting limits is a key consideration when designing your AWS Transit Gateway for scalability."
"You need to ensure that traffic between your VPCs and your on-premises network is encrypted when using AWS Transit Gateway. Which of the following is the best approach?","Use IPsec VPN tunnels for all connections","Use Security Groups","Enable encryption at rest on all EC2 instances","Configure Network ACLs","Using IPsec VPN tunnels for all connections (VPC and on-premises) is the most straightforward method to ensure end-to-end encryption when using AWS Transit Gateway."
"What security benefit does AWS Transit Gateway offer compared to traditional VPN solutions?","Centralised security management and visibility","Automatic DDoS protection","Integration with AWS IAM","Built-in intrusion detection system","Transit Gateway's main advantage is centralised management and visibilty which enables to have a single point of firewall for all attachments."
"Which AWS CLI command is used to create an AWS Transit Gateway?","aws ec2 create-transit-gateway","aws networkmanager create-transit-gateway","aws transitgateway create-gateway","aws vpc create-transit-gateway","The command `aws ec2 create-transit-gateway` is the correct AWS CLI command for creating a Transit Gateway."
"What does an AWS Transit Gateway 'Proposal Subnet' refer to in the context of SD-WAN Connect attachments?","The subnets you want to advertise from the SD-WAN appliance to AWS.","The subnets of the VPC you will use for the SD-WAN appliance","The subnets which are not permitted for peering","The subnets that are only available for multicast","The Proposal Subnets are the subnets that are advertised from the SD-WAN appliance to AWS in the BGP announcements, and this has to be configured on the Connect Attachment."
"Which feature allows an AWS Transit Gateway to act as a local router for multicast traffic within the AWS cloud?","Transit Gateway Multicast","Transit Gateway Peering","VPC Peering","Transit Gateway Connect","Transit Gateway Multicast provides a way to distribute multicast traffic in AWS via a Transit Gateway."
"When using shared services VPCs and inspection VPCs connected to an AWS Transit Gateway, what is a primary design consideration?","Ensuring proper routing to direct traffic through inspection points","Minimising cross-region traffic to reduce costs","Using the smallest possible CIDR blocks for all VPCs","Enabling encryption at rest on all EC2 instances","The architecture design should properly funnel the traffic through the inspection VPC."
"What is the primary function of an AWS Transit Gateway?","To act as a network transit hub, simplifying network connectivity between VPCs and on-premises networks","To provide a managed NAT service","To act as a firewall for all VPC traffic","To provide a DNS resolution service","Transit Gateway acts as a hub, simplifying connectivity between multiple VPCs and on-premises networks, reducing the complexity of managing many individual connections."
"Which of the following statements is true regarding AWS Transit Gateway attachments?","Each VPC can only have one attachment to a Transit Gateway","Transit Gateway attachments are limited to VPCs in the same AWS account and Region","Transit Gateway attachments require a dedicated subnet in each attached VPC","Transit Gateway attachments do not support inter-Region peering","Each VPC can have multiple attachments to different Transit Gateways for redundancy or specific routing requirements. Note that attachments *are* limited to the same region."
"When creating an AWS Transit Gateway, what is the purpose of enabling auto accept shared attachments?","To automatically accept attachment requests from other AWS accounts","To automatically create route table entries for new attachments","To automatically encrypt traffic flowing through the Transit Gateway","To automatically peer with other Transit Gateways","Enabling auto accept shared attachments automatically accepts attachment requests from other AWS accounts, streamlining the process of sharing resources and networks."
"Which routing option is supported by AWS Transit Gateway?","Static and Dynamic Routing","Dynamic Routing only","Static Routing only","Policy Based Routing only","Transit Gateway supports both static and dynamic routing, allowing for flexible and adaptable network configurations based on your specific needs and network topology."
"How does AWS Transit Gateway handle overlapping CIDR blocks between attached VPCs?","Overlapping CIDR blocks are not supported and will cause attachment failures","Transit Gateway automatically NATs the overlapping CIDR blocks","Transit Gateway prioritises traffic based on attachment creation order","Transit Gateway isolates overlapping CIDR blocks into separate routing domains","Transit Gateway does not support overlapping CIDR blocks between attached VPCs to prevent routing conflicts and ensure deterministic traffic flow."
"What is the purpose of Transit Gateway Connect attachments?","To connect SD-WAN appliances to the Transit Gateway","To connect VPCs directly to each other","To provide a direct connection to AWS Direct Connect","To establish a secure tunnel to another AWS Region","Transit Gateway Connect attachments enable integration with SD-WAN appliances by supporting Generic Routing Encapsulation (GRE) and Border Gateway Protocol (BGP), allowing for seamless connectivity between your on-premises network and AWS."
"Which of the following features can be used to isolate traffic between different VPCs connected to an AWS Transit Gateway?","Transit Gateway Route Tables","Transit Gateway Network ACLs","Transit Gateway Security Groups","Transit Gateway VPC Endpoints","Transit Gateway Route Tables are used to control the flow of traffic between different attachments (VPCs, VPNs, Direct Connect) by defining which routes are propagated and accepted, enabling isolation of traffic."
"What is the function of an AWS Transit Gateway peering attachment?","To connect Transit Gateways in different AWS Regions","To connect Transit Gateways in the same AWS account","To connect Transit Gateways in the same AWS Region","To connect Transit Gateways in different AWS accounts within the same Region","A peering attachment connects Transit Gateways in different AWS Regions, allowing you to build a global network and route traffic between geographically dispersed VPCs and on-premises networks."
"Which AWS service provides a dedicated network connection from your on-premises environment to AWS, which can then be integrated with a Transit Gateway?","AWS Direct Connect","AWS Site-to-Site VPN","AWS Client VPN","AWS Global Accelerator","AWS Direct Connect provides a dedicated network connection from your on-premises environment to AWS, offering lower latency and higher bandwidth compared to VPN connections. It can be used with a Transit Gateway for seamless connectivity."
"What is a key benefit of using AWS Transit Gateway Network Manager?","Provides a central view for monitoring and managing your global network","Automates the creation of Transit Gateway attachments","Automatically optimises network performance","Replaces the need for Transit Gateway route tables","Transit Gateway Network Manager provides a single dashboard to monitor and manage your global network, including Transit Gateways, attachments, and on-premises connections, offering insights into network performance and connectivity."
"What is the primary purpose of AWS Transit Gateway?","To simplify network connectivity between VPCs and on-premises networks","To provide a managed NAT gateway service","To provide a managed VPN endpoint","To provide a content delivery network","Transit Gateway acts as a hub, simplifying network connectivity between VPCs and on-premises networks, reducing the complexity of managing multiple connections."
"Which routing option is available with AWS Transit Gateway?","Static and Dynamic routing","Dynamic routing only","Static routing only","Policy-based routing only","Transit Gateway supports both Static and Dynamic routing, enabling flexible routing configurations based on your network requirements."
"In AWS Transit Gateway, what does a Transit Gateway attachment represent?","A connection between the Transit Gateway and a VPC, VPN, or Direct Connect gateway","A security group associated with the Transit Gateway","A route table within the Transit Gateway","A network ACL applied to the Transit Gateway","A Transit Gateway attachment represents the connection between the Transit Gateway and a VPC, VPN, or Direct Connect gateway, allowing traffic to flow between them."
"When using AWS Transit Gateway, what is the purpose of route tables?","To control the flow of traffic between attachments","To define security group rules","To define network ACL rules","To define the CIDR blocks for VPCs","Route tables in Transit Gateway are used to control the flow of traffic between attachments, enabling you to segment your network and control traffic flow."
"You are using AWS Transit Gateway to connect multiple VPCs. How can you ensure that VPCs in the same region can communicate with each other?","Associate the VPC attachments with the same Transit Gateway route table.","Create separate Transit Gateways for each VPC.","Use VPC peering connections instead of Transit Gateway.","Configure Network ACLs to allow traffic between VPCs.","Associating the VPC attachments with the same Transit Gateway route table allows them to communicate with each other, as the route table defines the allowed traffic flows."
"What is the purpose of AWS Transit Gateway Network Manager?","To provide a central management interface for Transit Gateways and network resources","To automatically scale Transit Gateway bandwidth","To configure security groups for Transit Gateway attachments","To monitor CPU utilisation of Transit Gateway instances","Transit Gateway Network Manager provides a central management interface for Transit Gateways and network resources, simplifying network management and troubleshooting."
"When connecting to on-premises networks via AWS Transit Gateway, what is the recommended way to establish a VPN connection?","Create a VPN attachment on the Transit Gateway and configure a customer gateway on-premises","Use VPC peering to connect to a VPN Gateway in a separate VPC","Establish a Direct Connect connection without a VPN","Configure a software VPN on an EC2 instance","The recommended way to connect to on-premises networks is to create a VPN attachment on the Transit Gateway and configure a customer gateway on-premises to establish the VPN connection."
"What is the key benefit of using AWS Transit Gateway inter-region peering?","Enables connectivity between Transit Gateways in different AWS Regions","Automatically configures VPC peering connections","Reduces the cost of data transfer between VPCs in the same Region","Provides a dedicated connection for on-premises access","Inter-region peering enables connectivity between Transit Gateways in different AWS Regions, allowing you to build a global network with simplified routing."
"You have multiple VPCs using overlapping CIDR blocks. Can you connect them using AWS Transit Gateway?","No, Transit Gateway does not support overlapping CIDR blocks.","Yes, Transit Gateway supports overlapping CIDR blocks without any configuration changes.","Yes, but you need to enable overlapping CIDR block support in the Transit Gateway configuration.","Yes, but you must use static routing only.","Transit Gateway does not support overlapping CIDR blocks. You need to ensure that your VPCs have unique CIDR blocks before connecting them."
"Which statement best describes how Transit Gateway handles traffic inspection?","Transit Gateway provides a central point for traffic inspection, but you must configure it using third-party appliances.","Transit Gateway automatically inspects all traffic for security threats.","Transit Gateway only allows traffic from trusted sources.","Transit Gateway does not support traffic inspection.","Transit Gateway provides a central point for traffic inspection. However, this is achieved through integration with third-party network security appliances (e.g., firewalls, intrusion detection/prevention systems) that are deployed and managed separately, typically in a centralised inspection VPC attached to the Transit Gateway."