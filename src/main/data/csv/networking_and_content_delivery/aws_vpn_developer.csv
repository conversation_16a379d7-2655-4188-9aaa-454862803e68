"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What AWS service primarily facilitates establishing a VPN connection to your AWS VPC?","AWS Virtual Private Network (VPN)","AWS Direct Connect","AWS PrivateLink","AWS Transit Gateway","AWS VPN is the dedicated service for creating and managing VPN connections to your VPC."
"Which type of VPN connection does AWS Site-to-Site VPN support?","IPsec VPN","SSL VPN","PPTP VPN","L2TP VPN","AWS Site-to-Site VPN uses IPsec (Internet Protocol Security) to establish secure connections."
"When configuring AWS Site-to-Site VPN, what is a Customer Gateway?","A resource that represents your on-premises VPN device","A security group for the VPN connection","A managed AWS VPN appliance","A route table entry for the VPN connection","The Customer Gateway resource represents your on-premises VPN device and its configuration in AWS."
"What is the purpose of the Virtual Private Gateway (VGW) in an AWS Site-to-Site VPN?","It's the VPN concentrator on the AWS side of the connection","It's the firewall protecting the VPN connection","It's a routing table for the VPN","It's a DNS server for the VPC","The Virtual Private Gateway (VGW) is the VPN concentrator on the AWS side, providing the endpoint for the VPN connection within your VPC."
"Which routing option is available for AWS Site-to-Site VPN?","Static Routing","Dynamic Routing (BGP)","Policy-Based Routing","All of the provided answers","AWS Site-to-Site VPN supports both Static Routing and Dynamic Routing (using BGP - Border Gateway Protocol)."
"What is the primary benefit of using BGP (Border Gateway Protocol) with AWS Site-to-Site VPN?","Automatic route propagation and failover","Increased encryption strength","Reduced latency","Simplified configuration","BGP allows for automatic route propagation and failover detection, making the VPN connection more resilient and easier to manage."
"What does 'Tunnel Options' in AWS Site-to-Site VPN configuration define?","Encryption algorithms and key exchange methods","The physical location of the VPN endpoints","The bandwidth allocation for the VPN connection","The cost associated with the VPN connection","Tunnel options specify the security parameters for the VPN tunnels, including encryption algorithms, key exchange methods, and pre-shared keys."
"How many VPN tunnels are typically established per AWS Site-to-Site VPN connection for redundancy?","Two","One","Three","Four","AWS typically creates two VPN tunnels per Site-to-Site VPN connection to provide redundancy. If one tunnel fails, the other can maintain connectivity."
"What is the purpose of a VPN Connection in AWS?","To create a secure, encrypted connection between your network and AWS","To publicly expose your VPC resources to the internet","To monitor network traffic in your VPC","To limit access to specific AWS services","A VPN Connection is used to create a secure, encrypted connection between your on-premises network (or another cloud network) and your AWS VPC."
"What security feature does AWS Site-to-Site VPN use to protect data in transit?","IPsec (Internet Protocol Security)","SSL/TLS","AES Encryption","Firewall Rules","AWS Site-to-Site VPN uses IPsec to encrypt data in transit, providing a secure connection between your network and AWS."
"What is the function of the 'Pre-Shared Key' (PSK) in an AWS Site-to-Site VPN?","To authenticate the VPN connection","To encrypt the VPN traffic","To authorise users to access the VPN","To determine the routing policy for the VPN","The Pre-Shared Key (PSK) is used to authenticate the VPN connection between the Customer Gateway and the Virtual Private Gateway."
"What is the best practice for rotating Pre-Shared Keys (PSKs) in AWS Site-to-Site VPN?","Rotate PSKs regularly for enhanced security","PSKs should never be changed","Rotating PSKs requires a full VPN reconfiguration","AWS automatically manages PSK rotation","Rotating PSKs regularly is a security best practice to protect the VPN connection from potential compromises."
"Which AWS service can be used to monitor the health and performance of your AWS Site-to-Site VPN?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and alarms to monitor the health and performance of your AWS Site-to-Site VPN connections."
"If you need to connect multiple on-premises networks to a single AWS VPC, which AWS service is most suitable?","AWS Transit Gateway","AWS VPN CloudHub","AWS Direct Connect","AWS PrivateLink","AWS Transit Gateway simplifies the connection of multiple on-premises networks to a single VPC, acting as a central hub."
"Which AWS service allows you to create a VPN connection over the public internet?","AWS Site-to-Site VPN","AWS Direct Connect","AWS PrivateLink","AWS Client VPN","AWS Site-to-Site VPN allows you to create a VPN connection over the public internet using IPsec."
"What is the purpose of the IKE (Internet Key Exchange) protocol in AWS Site-to-Site VPN?","To establish a secure channel for key exchange and authentication","To encrypt data transferred over the VPN","To manage routing between networks","To monitor the VPN connection's health","IKE (Internet Key Exchange) is used to establish a secure channel for negotiating security associations and exchanging cryptographic keys between the VPN endpoints."
"What AWS VPN type allows individual users to securely connect to AWS resources from remote locations?","AWS Client VPN","AWS Site-to-Site VPN","AWS Transit Gateway","AWS Direct Connect","AWS Client VPN provides secure access to AWS resources for individual users from remote locations."
"Which authentication method is supported by AWS Client VPN?","Active Directory authentication","Mutual Authentication","Client Certificate authentication","All of the provided answers","AWS Client VPN supports Active Directory authentication, Mutual Authentication, and Client Certificate authentication."
"In AWS Client VPN, what is an 'Endpoint'?","The AWS resource that allows clients to connect to the VPC","A firewall rule for the VPN","A routing table entry for the VPN","A DNS server used by the VPN","An AWS Client VPN Endpoint is the resource that enables clients to connect securely to your VPC."
"What is the purpose of 'Authorisation Rules' in AWS Client VPN?","To control which network resources clients can access","To define the encryption algorithms used by the VPN","To manage user authentication methods","To monitor client activity","Authorisation Rules in AWS Client VPN control which network resources (e.g., subnets) clients can access once connected."
"Which is NOT a component of Site-to-Site VPN?","Transit Gateway","Customer Gateway","Virtual Private Gateway","VPN Connection","Transit Gateway is a service that connects VPCs and on-premises networks through a central hub, while Customer Gateway, Virtual Private Gateway and VPN Connection are components of the Site-to-Site VPN."
"In the context of AWS VPN, what does 'MTU' refer to?","Maximum Transmission Unit","Minimum Transfer Utility","Mean Time to Update","Multi-Tenant Usage","MTU stands for Maximum Transmission Unit, which defines the largest packet size that can be transmitted over the network."
"What is the purpose of Dead Peer Detection (DPD) in AWS Site-to-Site VPN?","To detect and remove inactive VPN tunnels","To encrypt data in transit","To authenticate VPN peers","To optimize network traffic","Dead Peer Detection (DPD) is used to detect and remove inactive or failed VPN tunnels, ensuring that only active tunnels are used."
"Which AWS VPN service is best suited for connecting branch offices to a central AWS VPC?","AWS Site-to-Site VPN","AWS Client VPN","AWS Direct Connect","AWS PrivateLink","AWS Site-to-Site VPN is ideal for connecting branch offices or on-premises networks to a central AWS VPC."
"Which component in AWS VPN represents your physical on-premises VPN device?","Customer Gateway","Virtual Private Gateway","VPN Connection","Transit Gateway","The Customer Gateway represents your physical on-premises VPN device and its configuration within the AWS environment."
"What is the advantage of using Dynamic Routing over Static Routing in AWS Site-to-Site VPN?","Automatic route propagation and failover","Simpler configuration","Increased security","Lower cost","Dynamic Routing (using BGP) automates route propagation and failover, reducing manual configuration and improving resilience."
"What is the role of the Virtual Private Gateway (VGW) in the context of AWS Site-to-Site VPN?","It provides the AWS-side endpoint for the VPN connection","It manages user authentication","It monitors network traffic","It encrypts data in transit","The Virtual Private Gateway (VGW) serves as the AWS-side endpoint for the VPN connection, connecting your VPC to the VPN tunnel."
"What is a key security best practice when configuring AWS Site-to-Site VPN?","Using strong pre-shared keys and regularly rotating them","Disabling encryption for faster performance","Using default security settings","Sharing the pre-shared key publicly","Using strong, unique pre-shared keys and rotating them regularly is a critical security best practice for AWS Site-to-Site VPN."
"What is the main purpose of AWS VPN CloudHub?","To create a hub-and-spoke VPN network connecting multiple sites","To accelerate VPN performance","To encrypt VPN traffic","To simplify VPN configuration","AWS VPN CloudHub allows you to create a hub-and-spoke VPN network, connecting multiple branch offices or remote sites to a central AWS VPC through VPN connections."
"What is the advantage of using AWS Client VPN over a traditional VPN solution?","Centralised management and scalability","Lower cost","Increased security","Faster performance","AWS Client VPN offers centralised management, scalability, and integration with AWS services, simplifying VPN administration and scaling."
"Which authentication protocol is typically used for Active Directory authentication with AWS Client VPN?","LDAP (Lightweight Directory Access Protocol)","Kerberos","RADIUS","SAML","LDAP (Lightweight Directory Access Protocol) is commonly used for Active Directory authentication with AWS Client VPN, allowing users to authenticate with their existing Active Directory credentials."
"What is the purpose of the 'Target Network' setting in AWS Client VPN Authorisation Rules?","To specify the destination CIDR block that clients can access","To define the source IP address range for clients","To configure the VPN tunnel encryption","To manage user authentication methods","The 'Target Network' setting in AWS Client VPN Authorisation Rules specifies the destination CIDR block within your VPC that clients are authorised to access."
"What is the relationship between AWS VPN and AWS Direct Connect?","AWS VPN creates a secure tunnel over the public internet, while AWS Direct Connect provides a dedicated private network connection.","AWS VPN is faster, while AWS Direct Connect is more secure.","AWS VPN is more expensive, while AWS Direct Connect is cheaper.","AWS VPN is used for backups, while AWS Direct Connect is used for primary connectivity.","AWS VPN establishes a secure connection over the internet, while AWS Direct Connect provides a dedicated, private network connection between your on-premises infrastructure and AWS."
"What is the purpose of the 'Tunnel Inside IP CIDR' block in AWS Client VPN?","To assign IP addresses to VPN clients","To define the allowed IP addresses for VPN servers","To configure the encryption algorithm for the VPN tunnel","To manage user authentication","The 'Tunnel Inside IP CIDR' block in AWS Client VPN specifies the range of IP addresses that will be assigned to VPN clients when they connect."
"When troubleshooting an AWS Site-to-Site VPN connection, which CloudWatch metric is most helpful for monitoring tunnel status?","TunnelState","CPUUtilization","NetworkPacketsIn","DiskSpaceUtilization","The TunnelState CloudWatch metric indicates the status of each VPN tunnel, allowing you to quickly identify if a tunnel is up or down."
"What is the purpose of configuring a 'Static Route' in an AWS VPC route table associated with a Virtual Private Gateway (VGW)?","To direct traffic destined for your on-premises network through the VPN connection","To redirect internet traffic through the VPN","To block traffic from specific IP addresses","To prioritize VPN traffic over other traffic","A static route in the VPC route table directs traffic destined for your on-premises network (specified by the destination CIDR block) through the Virtual Private Gateway (VGW), thus routing it through the VPN connection."
"In AWS Client VPN, what does 'Mutual Authentication' involve?","Both the client and the server verify each other's identities using certificates","The client authenticates using a username and password","The server authenticates using a pre-shared key","The client authenticates using multi-factor authentication","Mutual Authentication requires both the client and the server to present valid certificates, ensuring that both parties trust each other."
"What is the recommended method for handling overlapping IP address ranges between your on-premises network and your AWS VPC when using AWS Site-to-Site VPN?","Use Network Address Translation (NAT)","Change the IP address range of your VPC","Disable security group rules","Use a different VPN technology","Network Address Translation (NAT) allows you to translate IP addresses, avoiding conflicts when overlapping ranges exist between your on-premises network and AWS VPC."
"What AWS service is primarily used for establishing a dedicated network connection from on-premises to AWS, bypassing the public internet?","AWS Direct Connect","AWS Site-to-Site VPN","AWS Client VPN","AWS Transit Gateway","AWS Direct Connect provides a dedicated, private network connection between your on-premises infrastructure and AWS, bypassing the public internet."
"Which of the following is NOT a supported transport protocol for AWS Client VPN?","UDP","TCP","SCTP","HTTP","AWS Client VPN supports UDP and TCP as transport protocols, providing flexibility based on network requirements."
"In AWS Site-to-Site VPN, what is the purpose of the 'Phase 1' configuration parameters?","To establish the secure IKE (Internet Key Exchange) SA (Security Association)","To encrypt data transmitted over the VPN","To manage routing between the networks","To monitor the VPN connection's health","Phase 1 parameters in AWS Site-to-Site VPN define how the secure IKE (Internet Key Exchange) Security Association (SA) is established, which is the initial secure channel for negotiating security parameters."
"What type of security group rule is essential for allowing traffic to flow through a Site-to-Site VPN connection?","Rules allowing inbound traffic from the on-premises network CIDR block","Rules denying all outbound traffic","Rules allowing inbound traffic from the internet","Rules denying all inbound traffic","To allow traffic to flow from your on-premises network into your VPC, you must configure security group rules that allow inbound traffic from the CIDR block representing your on-premises network."
"What is the primary benefit of using AWS Transit Gateway with Site-to-Site VPN connections from multiple remote locations?","Simplified network management and routing","Increased VPN tunnel bandwidth","Enhanced VPN encryption","Reduced VPN setup costs","Transit Gateway simplifies network management by providing a central hub for connecting multiple VPN connections and VPCs, reducing the complexity of routing and network configuration."
"What is the role of 'split-tunnel' configuration in AWS Client VPN?","Allows clients to access both AWS resources and the internet simultaneously","Forces all client traffic through the VPN tunnel","Encrypts all client traffic, regardless of destination","Limits client access to specific AWS resources","Split-tunnel configuration allows clients to access both AWS resources through the VPN and the internet directly, rather than forcing all traffic through the VPN tunnel."
"When using AWS Client VPN, where are the client configuration files generated from?","The Client VPN Endpoint","The VPC settings","The IAM user configuration","The EC2 instance settings","The client configuration files, which are used to connect to the VPN, are generated from the Client VPN Endpoint."
"Which AWS service allows you to centrally manage VPN connections across multiple AWS accounts?","AWS Transit Gateway","AWS VPN CloudHub","AWS Resource Access Manager (RAM) in conjunction with Transit Gateway","AWS Organizations","AWS Resource Access Manager (RAM) allows you to share Transit Gateways across multiple AWS accounts within your AWS Organization, enabling centralised management of VPN connections."
"You need to ensure your VPN connection remains active even during brief network interruptions. Which feature can help with this?","Dead Peer Detection (DPD)","Multi-Factor Authentication (MFA)","Encryption at Rest","Firewall Rules","Dead Peer Detection (DPD) periodically checks the health of the VPN tunnel, and can re-establish the connection automatically if an interruption is detected, thus improving resilience."
"Which of the following is a key requirement for establishing an AWS Site-to-Site VPN connection?","A valid Customer Gateway Device","A public IPv4 address for your EC2 instances","An IAM user with VPN access","A pre-configured S3 bucket","A valid Customer Gateway Device (your on-premises VPN device) is a crucial requirement for establishing an AWS Site-to-Site VPN connection."
"What is a possible reason for a Site-to-Site VPN tunnel to be in a 'DOWN' state despite correct configuration?","Incorrect security group rules blocking traffic","Insufficient IAM permissions","Lack of a public IP address","Region outage in AWS","Incorrect security group rules are a common reason for a VPN tunnel to be down, as they might be blocking necessary traffic between the on-premises network and the VPC."
"What is the main purpose of AWS Site-to-Site VPN?","Creating a secure connection between your on-premises network and your AWS VPC","Providing DDoS protection for your AWS resources","Enabling content delivery to end-users","Managing user identities across multiple AWS accounts","AWS Site-to-Site VPN allows you to create encrypted connections between your on-premises network and your AWS Virtual Private Cloud (VPC)."
"Which AWS service is used to create a virtual private gateway (VGW) for your Site-to-Site VPN connection?","Amazon VPC","AWS Direct Connect","AWS Transit Gateway","AWS Cloud WAN","The Virtual Private Gateway (VGW) is created within Amazon VPC and serves as the VPN concentrator on the AWS side."
"Which of the following routing options are supported for Site-to-Site VPN connections in AWS?","Static and Dynamic (BGP) routing","Policy-based routing only","Route table propagation only","Dynamic (BGP) routing with a default static route","Site-to-Site VPN supports both static and dynamic routing using Border Gateway Protocol (BGP)."
"What is the purpose of a Customer Gateway (CGW) in the context of AWS Site-to-Site VPN?","It represents the on-premises VPN device","It represents the AWS VPN endpoint","It's an AWS service used for monitoring VPN connections","It's a tool for generating VPN configuration files","The Customer Gateway (CGW) represents the physical or software VPN device located on your on-premises network."
"Which encryption protocols are supported for AWS Site-to-Site VPN?","IPsec","SSL/TLS","SSH","Kerberos","AWS Site-to-Site VPN uses IPsec (Internet Protocol Security) to establish encrypted tunnels."
"What is the main benefit of using AWS Client VPN?","Providing secure remote access to your AWS resources","Connecting multiple VPCs together","Extending your on-premises network to AWS","Filtering network traffic","AWS Client VPN allows individual users to securely access AWS resources from anywhere using a VPN client."
"Which authentication methods are supported by AWS Client VPN?","Active Directory and mutual authentication","Only Active Directory","Only mutual authentication","Username/Password only","AWS Client VPN supports Active Directory authentication and mutual authentication using client certificates."
"What is the purpose of a Client VPN endpoint?","It's the termination point for Client VPN connections in your VPC","It's a tool for managing Client VPN users","It's a service for monitoring Client VPN traffic","It's a software client installed on user devices","The Client VPN endpoint is the resource within your VPC that handles Client VPN connections from remote users."
"What is the role of authorisation rules in AWS Client VPN?","To control which subnets clients can access","To control the encryption protocol used","To control the authentication method","To limit the bandwidth available to clients","Authorisation rules in Client VPN determine which subnets connected to the Client VPN endpoint that clients are allowed to access."
"How does AWS Client VPN ensure secure communication?","By using TLS-based VPN tunnels","By using SSH tunnels","By using unencrypted connections","By using public key encryption only","AWS Client VPN uses TLS-based VPN tunnels to provide secure, encrypted communication between clients and the AWS network."
"What is the purpose of a Transit Gateway in the context of AWS VPN connections?","To simplify connectivity between multiple VPCs and on-premises networks","To provide redundancy for a single VPN connection","To encrypt data at rest in S3","To manage user permissions in IAM","Transit Gateway acts as a hub, simplifying connectivity between multiple VPCs, VPN connections, and Direct Connect connections."
"Which of the following is a benefit of using AWS VPN CloudHub?","It provides simple hub-and-spoke VPN connectivity between multiple sites","It automatically scales VPN bandwidth based on demand","It offers enhanced security compared to standard VPN connections","It reduces the cost of VPN connections","AWS VPN CloudHub provides a simple hub-and-spoke model for connecting multiple on-premises sites using AWS as the central hub."
"Which component of the AWS Site-to-Site VPN is responsible for performing IKE (Internet Key Exchange)?","The Customer Gateway and Virtual Private Gateway","The AWS Management Console","The VPC route table","The Internet Gateway","Both the Customer Gateway (on-premises VPN device) and the Virtual Private Gateway (AWS side) participate in the IKE process to establish secure tunnels."
"When using dynamic routing with AWS Site-to-Site VPN, what protocol is typically used to exchange routing information?","BGP (Border Gateway Protocol)","OSPF (Open Shortest Path First)","RIP (Routing Information Protocol)","EIGRP (Enhanced Interior Gateway Routing Protocol)","BGP is the standard protocol used for dynamic routing in AWS Site-to-Site VPN configurations."
"What is the purpose of Dead Peer Detection (DPD) in AWS Site-to-Site VPN?","To detect and handle VPN tunnel failures","To detect malicious traffic on the VPN tunnel","To optimize VPN tunnel bandwidth","To monitor CPU utilisation of the VPN gateways","DPD is used to detect and handle VPN tunnel failures by periodically checking the reachability of the peer VPN gateway."
"Which AWS service can be used to monitor the health and performance of your AWS Site-to-Site VPN connections?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and alarms that can be used to monitor the health and performance of VPN connections."
"What is the purpose of VPN tunnel options within an AWS Site-to-Site VPN connection?","To provide redundancy and high availability","To increase VPN bandwidth","To enable encryption at rest","To simplify VPN configuration","Each Site-to-Site VPN connection has two tunnels to provide redundancy and high availability. If one tunnel fails, traffic automatically fails over to the other tunnel."
"What does 'IKE Security Association' refer to in the context of AWS VPN?","The negotiated security parameters for the VPN tunnel","The AWS IAM role associated with the VPN connection","The security group applied to the VPN gateway","The AWS KMS key used to encrypt VPN traffic","IKE Security Association refers to the negotiated security parameters (encryption algorithms, keys, etc.) used to secure the VPN tunnel."
"What is the maximum transmission unit (MTU) size you should configure on your on-premises VPN device to avoid fragmentation issues with AWS Site-to-Site VPN?","1460","1500","9001","65535","The recommended MTU size is typically around 1460 bytes to account for VPN encapsulation overhead."
"Which of the following is a key difference between AWS Site-to-Site VPN and AWS Direct Connect?","Site-to-Site VPN uses the public internet, while Direct Connect uses a dedicated private network connection","Site-to-Site VPN is more expensive than Direct Connect","Site-to-Site VPN offers lower latency than Direct Connect","Site-to-Site VPN cannot be used for hybrid cloud deployments","Site-to-Site VPN uses the public internet (or a private network that uses the internet), while Direct Connect provides a dedicated private network connection to AWS."
"What is the primary advantage of using AWS Transit Gateway over multiple Site-to-Site VPN connections for a hub-and-spoke network?","Simplified management and reduced complexity","Increased VPN bandwidth","Enhanced security","Lower cost","Transit Gateway simplifies network management by providing a central hub for connecting multiple VPCs and VPN connections, reducing the complexity of managing multiple peer-to-peer VPN connections."
"You need to provide temporary VPN access to a contractor who doesn't have an Active Directory account. Which authentication method is most suitable for AWS Client VPN?","Mutual authentication with client certificates","Active Directory authentication","SAML-based federation","AWS IAM user authentication","Mutual authentication with client certificates allows you to provide access without requiring an Active Directory account, by issuing a client certificate to the contractor."
"When configuring an AWS Client VPN endpoint, what is the purpose of the 'Server Certificate ARN'?","To specify the SSL/TLS certificate used by the VPN endpoint","To specify the certificate used for mutual authentication","To specify the AWS IAM role used by the VPN endpoint","To specify the certificate authority used to issue client certificates","The 'Server Certificate ARN' specifies the SSL/TLS certificate that the Client VPN endpoint uses to encrypt traffic between clients and the AWS network."
"What is the role of 'connection logging' in AWS Client VPN?","To log connection events for auditing and troubleshooting purposes","To log all network traffic passing through the VPN tunnel","To automatically terminate inactive VPN connections","To encrypt the VPN tunnel logs","Connection logging captures connection events such as connect, disconnect, and authentication failures, which are useful for auditing and troubleshooting."
"Which AWS service can be used to centralise the management of security policies across multiple AWS accounts, including VPN configurations?","AWS Firewall Manager","AWS Inspector","AWS Shield","AWS Certificate Manager","AWS Firewall Manager allows you to centrally manage security policies, including AWS Network Firewall rules, across multiple AWS accounts and resources."
"You have an on-premises network with multiple subnets. Which configuration is required to ensure that all subnets can communicate with the AWS VPC over a Site-to-Site VPN connection?","Configure static routes or BGP to advertise all on-premises subnets","Configure a single default route in the on-premises network","Configure a Network ACL on the VPC","Configure a security group on the VPC","You need to configure static routes or BGP on your on-premises VPN device to advertise all the subnets that need to communicate with the VPC.  On the AWS side, ensure the VPC route tables include routes to the on-premises subnets via the VGW."
"What is the purpose of the 'Pre-shared Key' (PSK) in AWS Site-to-Site VPN?","To authenticate the VPN endpoints before establishing the IPsec tunnel","To encrypt the VPN traffic","To authorize users connecting to the VPN","To generate ephemeral session keys","The pre-shared key is used to authenticate the VPN endpoints (Customer Gateway and Virtual Private Gateway) before the IPsec tunnel is established. It acts as a shared secret between the two sides."
"What is the maximum number of tunnels that can be configured per AWS Site-to-Site VPN connection?","Two","One","Four","Unlimited","Each Site-to-Site VPN connection has two VPN tunnels for redundancy.  AWS encourages to configure both for high availability."
"When using AWS Client VPN, how are client IP addresses assigned?","From a CIDR block associated with the Client VPN endpoint","From the VPC CIDR block","From a public IP address pool","From the on-premises network's IP address range","Client IP addresses are assigned from a CIDR block that you associate with the Client VPN endpoint. This CIDR block must not overlap with any of your VPC CIDR blocks or on-premises network CIDR blocks."
"What is the purpose of the 'Split Tunnel' option in AWS Client VPN?","To route only traffic destined for the VPC through the VPN tunnel","To split VPN traffic across multiple tunnels","To split the authentication process into multiple steps","To encrypt only specific types of traffic","When split tunnel is enabled, only traffic destined for the VPC is routed through the VPN tunnel. All other traffic is routed directly to the internet."
"You need to create a highly available Site-to-Site VPN connection. Which steps should you take?","Configure two VPN tunnels, enable Dead Peer Detection, and use BGP for dynamic routing","Configure a single VPN tunnel with static routing","Use a low-cost VPN gateway instance type","Disable Dead Peer Detection to reduce overhead","To create a highly available VPN connection, you should configure two VPN tunnels, enable Dead Peer Detection to detect tunnel failures, and use BGP for dynamic routing to automatically failover between tunnels."
"Which AWS service can be used to automate the deployment and management of VPN connections and other network infrastructure?","AWS CloudFormation","AWS CodeDeploy","AWS Config","AWS Lambda","AWS CloudFormation allows you to define and provision your infrastructure as code, including VPN connections, using templates."
"What is the recommended way to control access to AWS resources for users connecting through AWS Client VPN?","Using AWS IAM policies attached to the user's Active Directory group or client certificate","Using security groups on the Client VPN endpoint","Using Network ACLs on the subnets","Using the Client VPN authorisation rules only","IAM policies can be used to control access to AWS resources based on the user's Active Directory group or client certificate information. This provides fine-grained access control."
"What type of firewall rule is typically configured on the security groups associated with resources in the VPC that need to be accessed via Site-to-Site VPN?","Allow traffic from the CIDR block of the on-premises network","Deny all inbound traffic","Allow all outbound traffic","Allow traffic from the internet","You need to allow traffic from the CIDR block of your on-premises network in the security groups of the resources that need to be accessed via the VPN connection."
"You want to monitor the CPU utilisation of your AWS Virtual Private Gateway. Which CloudWatch metric should you use?","CPUUtilization","NetworkPacketsIn","NetworkPacketsOut","TunnelState","The `CPUUtilization` metric provides information about the CPU utilisation of your Virtual Private Gateway."
"Which feature of AWS Transit Gateway allows you to isolate different network environments and control communication between them?","Route tables","Security groups","Network ACLs","VPC endpoints","Transit Gateway route tables control the flow of traffic between connected networks, allowing you to isolate different environments and enforce security policies."
"Which AWS service can be used to create a centralised logging solution for all VPN connections in your AWS environment?","AWS CloudWatch Logs","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS CloudWatch Logs can be used to collect and centralise logs from various sources, including VPN connections, for analysis and monitoring."
"What is the purpose of 'tunnel options' in AWS Site-to-Site VPN configuration?","To configure encryption settings, authentication methods, and tunnel lifetime","To specify the IP address of the VPN gateway","To specify the routing protocol","To configure the MTU size","Tunnel options allow you to configure various parameters such as encryption settings, authentication methods (pre-shared key), and tunnel lifetime to customize the VPN tunnel configuration."
"What is the difference between a static route and a dynamic route in AWS Site-to-Site VPN configuration?","Static routes are manually configured, while dynamic routes use BGP to automatically learn routes","Static routes are more secure than dynamic routes","Dynamic routes are less reliable than static routes","Static routes are only used for small networks","Static routes are manually configured and do not automatically adapt to network changes, while dynamic routes use BGP to automatically learn and update routes based on network conditions."
"You need to provide access to an AWS resource that does not have a public IP address, from an on-premises network. Which AWS service should you use?","AWS Site-to-Site VPN","Amazon S3","Amazon CloudFront","AWS Direct Connect","AWS Site-to-Site VPN allows you to create a secure connection between your on-premises network and your VPC, enabling access to resources without public IP addresses."
"Which AWS CLI command is used to create a customer gateway?","aws ec2 create-customer-gateway","aws vpc create-customer-gateway","aws rds create-customer-gateway","aws iam create-customer-gateway","The `aws ec2 create-customer-gateway` command is used to create a customer gateway in AWS."
"Which AWS service provides a dedicated network connection from your on-premises network to AWS, bypassing the public internet?","AWS Direct Connect","AWS Site-to-Site VPN","AWS Client VPN","Amazon VPC","AWS Direct Connect provides a dedicated network connection from your on-premises network to AWS, bypassing the public internet and offering lower latency and more consistent network performance."
"What is the default lifetime of a VPN tunnel (Phase 1 and Phase 2) in AWS Site-to-Site VPN?","Phase 1: 28800 seconds (8 hours), Phase 2: 3600 seconds (1 hour)","Phase 1: 3600 seconds (1 hour), Phase 2: 28800 seconds (8 hours)","Phase 1: 86400 seconds (24 hours), Phase 2: 3600 seconds (1 hour)","Phase 1: 3600 seconds (1 hour), Phase 2: 86400 seconds (24 hours)","The default lifetime of a VPN tunnel in AWS Site-to-Site VPN is Phase 1 (IKE SA) 28800 seconds (8 hours) and Phase 2 (IPsec SA) is 3600 seconds (1 hour)."
"When setting up AWS Client VPN, which security best practice should be followed regarding client certificate management?","Store client certificates securely and revoke them when no longer needed","Share client certificates with multiple users to simplify management","Disable client certificate verification to simplify the setup process","Use a weak passphrase for the client certificate private key","Client certificates should be stored securely, and revoked immediately when no longer needed to prevent unauthorized access."
"What is the purpose of AWS VPN 'acceleration' and which AWS service can you use to achieve it?","Improve VPN performance by routing traffic through AWS Global Accelerator","Reduce the cost of VPN connections","Increase the security of VPN traffic","Simplify the VPN configuration process","AWS Global Accelerator can be used to improve VPN performance by routing traffic through the AWS global network infrastructure, reducing latency and improving reliability."
"When using AWS VPN, what is a typical use case for deploying a third-party Network Virtual Appliance (NVA) in a VPC?","Implementing advanced security features such as intrusion detection or next-generation firewall","Reducing the cost of VPN connections","Simplifying the VPN configuration process","Increasing the bandwidth of the VPN connection","NVAs can provide advanced security features such as intrusion detection, threat prevention, and next-generation firewall capabilities that are not natively available in AWS VPN."
"Which AWS service allows you to connect your on-premises network to multiple AWS Regions over a single VPN connection?","AWS Cloud WAN","AWS Transit Gateway","AWS Direct Connect","AWS Site-to-Site VPN","AWS Cloud WAN enables you to build a global network connecting your on-premises infrastructure to multiple AWS Regions over a single VPN connection."
"What is the primary function of an AWS VPN Gateway?","To provide a secure connection between your on-premises network and your VPC.","To load balance traffic across multiple EC2 instances.","To provide DNS resolution for your VPC.","To manage user identities and access control.","A VPN Gateway enables you to create a secure and private connection between your on-premises data centre or network and your AWS VPC."
"Which type of AWS VPN connection uses the public internet to establish a secure tunnel?","AWS Site-to-Site VPN","AWS Direct Connect","AWS Transit Gateway","AWS Client VPN","AWS Site-to-Site VPN uses the public internet with encryption to create a secure connection."
"When configuring an AWS Site-to-Site VPN connection, what is the purpose of the Customer Gateway Device?","It's a device or software application on your side of the VPN connection that acts as the VPN endpoint.","It's an AWS-managed device that handles VPN traffic.","It's a virtual firewall within your VPC.","It's a service for monitoring network traffic.","The Customer Gateway Device is your on-premises VPN appliance that connects to the AWS VPN Gateway."
"What routing options are available for AWS Site-to-Site VPN?","Static and Dynamic (BGP)","Static only","Dynamic (BGP) only","Policy-based routing only","AWS Site-to-Site VPN supports both static routing and dynamic routing using Border Gateway Protocol (BGP)."
"What is the purpose of a Transit Gateway in the context of AWS VPN?","To simplify network architecture by providing a central hub for connecting multiple VPCs and on-premises networks.","To provide a managed NAT service.","To act as a firewall for your VPC.","To provide a caching layer for frequently accessed data.","Transit Gateway acts as a network transit hub, simplifying connectivity between multiple VPCs and on-premises networks."
"Which AWS service is used to manage and provision AWS Client VPN endpoints?","AWS VPN Client","AWS VPN Manager","AWS Client VPN","AWS VPN Console","AWS Client VPN is the service used to create and manage Client VPN endpoints."
"What authentication methods are supported by AWS Client VPN?","Active Directory and Certificate-based authentication","Password-based authentication only","Multi-Factor Authentication only","IP Address restriction only","AWS Client VPN supports Active Directory authentication for centralised user management and certificate-based authentication for enhanced security."
"What is a key benefit of using AWS Client VPN compared to setting up your own VPN server in EC2?","AWS Client VPN is a managed service, reducing the operational overhead of managing a VPN server.","AWS Client VPN provides higher network throughput.","AWS Client VPN is free to use.","AWS Client VPN supports more VPN protocols.","As a managed service, AWS Client VPN removes the complexity of server management, patching, and scaling."
"Which VPN protocol is supported by AWS Client VPN?","OpenVPN","IPsec","L2TP/IPsec","PPTP","AWS Client VPN uses the OpenVPN protocol for secure connections."
"What is the purpose of the VPN Tunnel Options in an AWS Site-to-Site VPN Connection?","To configure the IPsec security associations (SA) for the VPN tunnel.","To configure the routing protocol for the VPN connection.","To configure the firewall rules for the VPN connection.","To configure the DNS settings for the VPN connection.","VPN Tunnel Options configure parameters like encryption algorithms, Diffie-Hellman groups, and lifetime for the VPN tunnel's security associations."
"How does BGP (Border Gateway Protocol) contribute to AWS Site-to-Site VPNs?","It automatically learns and propagates network routes between the VPN Gateway and Customer Gateway.","It encrypts the traffic between the VPN Gateway and Customer Gateway.","It load balances traffic across multiple VPN tunnels.","It monitors the health of the VPN tunnel.","BGP enables dynamic routing, allowing networks to automatically discover and adapt to changes in network topology."
"What is the function of the 'Pre-shared Key' (PSK) in an AWS Site-to-Site VPN configuration?","To authenticate the VPN connection between the VPN Gateway and the Customer Gateway.","To encrypt the data transmitted through the VPN tunnel.","To authorise user access to the VPN.","To define the routing protocol for the VPN.","The Pre-shared Key (PSK) is a secret key used to authenticate the VPN connection during the initial key exchange."
"If you need to connect multiple VPCs together, and also connect to on-premises networks, which AWS service provides the most scalable solution?","AWS Transit Gateway","VPC Peering","AWS Site-to-Site VPN","AWS Direct Connect Gateway","AWS Transit Gateway simplifies this scenario by acting as a central hub, avoiding the need for numerous individual VPN connections or peering relationships."
"What is the purpose of Dead Peer Detection (DPD) in AWS Site-to-Site VPN?","To detect when the VPN tunnel has failed and re-establish the connection.","To detect malicious traffic on the VPN tunnel.","To detect unauthorized access attempts to the VPN.","To detect performance bottlenecks on the VPN tunnel.","DPD allows each end of the VPN tunnel to verify the other end is still active and responsive, triggering a re-establishment if a failure is detected."
"What does the term 'Tunnel Interface' refer to in the context of AWS VPN?","A virtual network interface that encapsulates the VPN traffic.","A physical network interface on the Customer Gateway device.","A network interface on the AWS VPN Gateway.","A software application used to manage the VPN connection.","The tunnel interface is a virtual interface configured on both the VPN Gateway and Customer Gateway devices to encapsulate and route VPN traffic."
"Which AWS service offers a dedicated, private network connection to AWS, which can be used in conjunction with VPN for added security?","AWS Direct Connect","AWS Storage Gateway","AWS CloudFront","AWS Snowball","AWS Direct Connect provides a dedicated network connection that bypasses the public internet, offering more consistent performance and security when used with VPN."
"What is the role of a 'Route Table' when configuring an AWS Site-to-Site VPN connection?","To direct traffic from your VPC to the VPN Gateway.","To define the security rules for the VPN connection.","To monitor the health of the VPN tunnel.","To manage user access to the VPN.","Route Tables are used to specify which traffic should be routed to the VPN Gateway, allowing communication between your VPC and your on-premises network."
"In AWS Client VPN, what is a 'Connection Log'?","A record of client connection events, including connection attempts, successful connections, and disconnections.","A log of network traffic passing through the VPN tunnel.","A log of user authentication attempts.","A log of security events related to the VPN connection.","Connection Logs provide valuable information for troubleshooting connection issues, monitoring VPN usage, and auditing security events."
"When troubleshooting an AWS Site-to-Site VPN connection, which AWS service can provide insights into network traffic flow?","VPC Flow Logs","CloudTrail","CloudWatch","Config","VPC Flow Logs capture information about the IP traffic going to, from, and within your VPC, which can help identify network connectivity issues."
"How can you improve the resilience of an AWS Site-to-Site VPN connection?","By configuring multiple VPN tunnels to different VPN Gateways in different Availability Zones.","By increasing the bandwidth of the VPN connection.","By enabling encryption on the VPN tunnel.","By using static routing instead of BGP.","Multiple tunnels ensure redundancy, so if one tunnel fails, traffic can automatically failover to another tunnel."
"What is the maximum transmission unit (MTU) size you might need to adjust when using AWS VPN, and why?","Adjusting MTU size can prevent packet fragmentation and improve performance.","MTU size does not affect VPN performance.","Adjusting MTU size is only necessary for connections to Direct Connect.","Adjusting MTU size is only necessary for IPv6 connections.","Large packets may be fragmented when travelling over the VPN tunnel, reducing performance. Adjusting MTU size can prevent this."
"What is the advantage of using certificate-based authentication in AWS Client VPN?","It provides stronger security than username/password authentication.","It's easier to configure than username/password authentication.","It's less expensive than username/password authentication.","It provides faster connection speeds than username/password authentication.","Certificate-based authentication relies on digital certificates, making it more resistant to password-based attacks."
"Which AWS service allows you to establish VPN connections over AWS Direct Connect?","AWS Direct Connect Gateway","AWS Transit Gateway","AWS VPN Gateway","AWS Client VPN","Direct Connect Gateway enables you to create VPN connections over your Direct Connect link, providing a secure and private connection."
"What is the purpose of a 'Security Group' in relation to your VPC and AWS VPN?","To control inbound and outbound traffic to and from EC2 instances within your VPC.","To control access to the VPN Gateway itself.","To monitor VPN traffic.","To encrypt VPN traffic.","Security Groups act as virtual firewalls, controlling network access to resources within your VPC, including those communicating over the VPN."
"What is the difference between AWS Site-to-Site VPN and AWS Client VPN in terms of use cases?","Site-to-Site VPN connects networks; Client VPN connects individual users.","Site-to-Site VPN is for connecting to on-premises networks; Client VPN is for connecting to other VPCs.","Site-to-Site VPN is more secure than Client VPN.","Site-to-Site VPN is cheaper than Client VPN.","Site-to-Site VPN is used for connecting a physical office to your VPC, while Client VPN allows individual users to securely connect from anywhere."
"When using dynamic routing with BGP in AWS Site-to-Site VPN, what is an 'Autonomous System Number' (ASN)?","A unique identifier for your network, used for BGP routing.","An encryption key for the VPN tunnel.","A firewall rule for the VPN connection.","A monitoring tool for the VPN tunnel.","An ASN is a globally unique identifier for an autonomous system, which is required for BGP routing."
"What happens if one of the two VPN tunnels in an AWS Site-to-Site VPN connection fails?","Traffic automatically fails over to the remaining tunnel.","The entire VPN connection is terminated.","The VPN connection operates at half capacity.","The VPN connection slows down significantly.","AWS Site-to-Site VPN connections are designed with two tunnels for redundancy. If one fails, traffic seamlessly switches to the other."
"What is the purpose of enabling 'Route Propagation' in a VPC Route Table when using a Transit Gateway with VPN?","To automatically add routes learned from the Transit Gateway to the Route Table.","To manually configure routes in the Route Table.","To prevent routes from being added to the Route Table.","To encrypt the traffic between the Transit Gateway and the Route Table.","Route Propagation allows the Route Table to automatically learn routes advertised by the Transit Gateway, simplifying route management."
"Which of the following is a valid use case for AWS VPN CloudHub?","Connecting multiple branch offices to each other and to AWS, through a hub-and-spoke model.","Connecting a single on-premises network to a single VPC.","Encrypting traffic between EC2 instances within a VPC.","Monitoring network traffic in a VPC.","AWS VPN CloudHub enables you to easily connect multiple geographically diverse branch offices to each other and to your AWS resources, forming a hub-and-spoke network."
"What is the recommended way to monitor the health and performance of your AWS Site-to-Site VPN connection?","Using CloudWatch metrics and alarms.","Using VPC Flow Logs only.","Using AWS Trusted Advisor only.","Using the AWS Console only.","CloudWatch provides comprehensive metrics and allows you to set up alarms to proactively monitor your VPN's health."
"In AWS Client VPN, what is a 'Target Network'?","The VPC subnet to which the VPN clients are allowed to connect.","The on-premises network to which the VPN clients are allowed to connect.","The public IP address of the VPN client.","The hostname of the VPN server.","The Target Network specifies the VPC subnets that VPN clients are authorised to access after connecting to the Client VPN endpoint."
"What is the main difference between a 'static route' and a 'dynamic route' in the context of AWS Site-to-Site VPN?","Static routes are manually configured, while dynamic routes are automatically learned via BGP.","Static routes are more secure than dynamic routes.","Static routes are faster than dynamic routes.","Static routes are less expensive than dynamic routes.","Static routes must be manually added to the route table, while dynamic routes use BGP to automatically discover and update routes."
"When configuring an AWS Site-to-Site VPN, what does the term 'IPsec Security Associations' (SAs) refer to?","The security parameters for the VPN tunnel, including encryption algorithms and authentication methods.","The IP addresses used for the VPN connection.","The routing protocol used for the VPN connection.","The firewall rules for the VPN connection.","IPsec SAs define the cryptographic algorithms, keys, and parameters used to secure the VPN tunnel."
"Which AWS service can be used to store and manage the client certificates required for certificate-based authentication in AWS Client VPN?","AWS Certificate Manager (ACM)","AWS Secrets Manager","AWS Key Management Service (KMS)","AWS IAM","AWS Certificate Manager is the recommended service for storing, managing, and deploying SSL/TLS certificates, including client certificates for Client VPN."
"If you are experiencing slow performance over your AWS Site-to-Site VPN connection, what are some potential causes?","Network congestion, incorrect MTU settings, or suboptimal routing.","Incorrect security group rules, incorrect IAM permissions, or incorrect DNS settings.","Outdated operating system, insufficient CPU resources, or insufficient memory.","Software bugs, hardware failures, or power outages.","Performance issues are often caused by factors impacting network throughput, such as congestion, fragmentation due to MTU issues, or inefficient routing."
"When using AWS Transit Gateway with VPN connections, what is the purpose of a 'Transit Gateway Route Table'?","To control the routing of traffic between VPCs and VPN connections attached to the Transit Gateway.","To control the security rules for the VPN connections.","To monitor the health of the Transit Gateway.","To manage user access to the Transit Gateway.","Transit Gateway Route Tables determine how traffic is routed between different attachments (VPCs, VPN connections, Direct Connect gateways) connected to the Transit Gateway."
"In AWS Client VPN, what is a 'Client CIDR'?","The IP address range assigned to VPN clients connecting to the VPN endpoint.","The IP address range of the VPC subnet.","The IP address range of the on-premises network.","The public IP address of the VPN client.","The Client CIDR defines the pool of IP addresses that will be assigned to connecting VPN clients."
"What is the maximum number of VPN connections that can be created on an AWS Transit Gateway?","50","10","100","Unlimited","By default, a Transit Gateway can support up to 50 VPN connections. This limit can be increased by contacting AWS Support."
"Which of the following is NOT a valid encryption algorithm for AWS Site-to-Site VPN tunnels?","DES","AES256","AES128","Triple DES","DES is considered weak and is not a valid encryption algorithm for modern VPN configurations."
"What is the purpose of using AWS VPN with AWS Direct Connect?","To encrypt the data transmitted over the Direct Connect connection.","To increase the bandwidth of the Direct Connect connection.","To provide redundancy for the Direct Connect connection.","To simplify the configuration of the Direct Connect connection.","VPN over Direct Connect provides an added layer of security by encrypting data transmitted over the dedicated Direct Connect connection."
"What is the key difference between a Customer Gateway and a Virtual Private Gateway when setting up an AWS VPN connection?","A Customer Gateway is a device you manage, a Virtual Private Gateway is managed by AWS.","A Customer Gateway handles encryption, a Virtual Private Gateway handles routing.","A Customer Gateway connects to Direct Connect, a Virtual Private Gateway connects to the internet.","A Customer Gateway is used for Client VPN, a Virtual Private Gateway is used for Site-to-Site VPN.","The Customer Gateway is your side of the VPN connection (on-premises device), while the Virtual Private Gateway is the AWS side of the connection."
"If your AWS Site-to-Site VPN connection is experiencing intermittent connectivity issues, what is one of the first things you should check?","Check the CloudWatch metrics for tunnel status and packet loss.","Check the CPU utilisation on your EC2 instances.","Check the security group rules for your EC2 instances.","Check the IAM roles associated with your EC2 instances.","Monitoring CloudWatch metrics provides immediate insight into the health and performance of the VPN tunnels."
"Which AWS service can you use to centrally manage VPN connections across multiple AWS accounts?","AWS Transit Gateway","AWS Organizations","AWS Systems Manager","AWS CloudFormation","AWS Transit Gateway, especially when used with AWS Organizations, can simplify management of VPN connections across multiple accounts by acting as a central point of connectivity."
"What is the default lifetime for Phase 1 of the Internet Key Exchange (IKE) in an AWS Site-to-Site VPN connection?","28800 seconds (8 hours)","3600 seconds (1 hour)","86400 seconds (24 hours)","600 seconds (10 minutes)","The default lifetime for IKE Phase 1 is 28800 seconds (8 hours), which determines how often the initial key exchange is renegotiated."
"What is a potential drawback of using static routing in a large, complex network with AWS Site-to-Site VPN?","It requires manual updates whenever the network topology changes, making it difficult to maintain.","It's less secure than dynamic routing.","It's less performant than dynamic routing.","It's more expensive than dynamic routing.","Manual configuration is prone to errors, and managing changes across a large network can be time-consuming and difficult."
"In AWS Client VPN, what is the purpose of using a split-tunnel configuration?","To route only traffic destined for the VPC or on-premises network through the VPN tunnel, while routing other traffic directly to the internet.","To split traffic across multiple VPN tunnels for increased bandwidth.","To split traffic based on the type of application.","To split traffic based on the user's location.","Split-tunneling allows you to selectively route traffic through the VPN, which can improve performance and reduce bandwidth costs."
"Which of the following AWS services can be integrated with AWS Client VPN for enhanced security and compliance?","AWS CloudTrail and AWS CloudWatch Logs","Amazon S3 and Amazon EC2","AWS Lambda and Amazon RDS","AWS IAM and Amazon DynamoDB","Integrating with CloudTrail and CloudWatch Logs allows you to monitor and audit Client VPN connections and traffic for security and compliance purposes."
"When creating an AWS Site-to-Site VPN, you're prompted to select an option called 'Tunnel Inside IP CIDR'. What does this refer to?","A private IP range AWS uses to facilitate communication between the two VPN endpoints.","The public IP range you use to access AWS resources.","The range of IP addresses you will use to connect clients to AWS.","An external IP range used to access the Internet.","This IP range is used by AWS to establish the VPN tunnel, and should not overlap with any of the IP ranges in your VPC or on your on-premises network."
"What is the primary purpose of AWS Virtual Private Network (VPN)?","To establish a secure connection between your on-premises network and your AWS VPC.","To provide content delivery services.","To provide managed DNS services.","To host static websites.","AWS VPN allows you to create a secure, encrypted connection between your on-premises data centre or network and your Amazon Virtual Private Cloud (VPC)."
"Which type of VPN connection does AWS offer?","Site-to-Site VPN and Client VPN","Direct Connect and Transit Gateway","Global Accelerator and CloudFront","Storage Gateway and Backup Gateway","AWS offers Site-to-Site VPN for connecting your on-premises network to your VPC, and Client VPN for securely connecting individual devices to your AWS resources."
"In AWS Site-to-Site VPN, what is a Virtual Private Gateway (VGW)?","The VPN concentrator on the AWS side of the connection.","A software firewall in AWS.","An endpoint for AWS Direct Connect.","A load balancer in AWS.","The Virtual Private Gateway (VGW) is the VPN concentrator on the AWS side that the customer gateway connects to."
"In AWS Site-to-Site VPN, what is a Customer Gateway (CGW)?","A representation of your on-premises VPN device in AWS.","An AWS firewall service.","A service to route traffic within AWS.","An AWS-managed NAT gateway.","A Customer Gateway (CGW) is a resource you create in AWS that represents your on-premises VPN device. You provide information about your device, such as its IP address."
"Which protocol is used by default for the tunnel encryption in AWS Site-to-Site VPN?","Internet Protocol Security (IPsec)","Secure Sockets Layer (SSL)","Transport Layer Security (TLS)","Secure Shell (SSH)","AWS Site-to-Site VPN uses the Internet Protocol Security (IPsec) protocol suite by default to establish encrypted tunnels between your network and AWS."
"What is the benefit of using Border Gateway Protocol (BGP) with AWS Site-to-Site VPN?","Dynamic route advertisement between your network and AWS.","Automatic encryption of data at rest.","Automated deployment of EC2 instances.","Real-time monitoring of network latency.","BGP enables dynamic routing, which automatically learns routes and advertises network reachability information between your network and the AWS environment."
"What does AWS Client VPN allow you to do?","Securely connect individual client devices to your AWS resources.","Create a point-to-point link between two AWS regions.","Encrypt data stored in S3 buckets.","Automatically scale EC2 instances based on demand.","AWS Client VPN allows you to securely connect individual users or client devices to your AWS resources, typically from remote locations."
"Which authentication method is supported by AWS Client VPN?","Active Directory authentication, mutual authentication using certificates, and SAML authentication","Multi-Factor Authentication (MFA) only","Password-based authentication only","IP address filtering only","AWS Client VPN supports Active Directory authentication, mutual authentication using certificates, and SAML authentication, providing flexible and secure access options."
"What is a VPN Connection in the context of AWS Site-to-Site VPN?","The logical connection between the VGW and the CGW.","An EC2 instance running a VPN application.","An S3 bucket storing VPN configuration files.","A CloudWatch alarm monitoring VPN traffic.","A VPN Connection represents the logical link between the Virtual Private Gateway (VGW) and the Customer Gateway (CGW), allowing traffic to flow between your network and AWS."
"What is the purpose of a Tunnel in the context of AWS Site-to-Site VPN?","An encrypted communication channel between the VGW and CGW.","A public subnet in a VPC.","A routing table entry in a VPC.","A security group rule in a VPC.","A Tunnel provides an encrypted communication channel between the Virtual Private Gateway (VGW) and the Customer Gateway (CGW), ensuring secure data transmission."
"Which AWS service can be used to monitor the status of your AWS Site-to-Site VPN tunnels?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch provides metrics and alarms for monitoring the status and performance of your AWS Site-to-Site VPN tunnels."
"What is the role of a Transit Gateway in relation to AWS VPNs?","Simplifying VPN connections across multiple VPCs.","Encrypting data at rest in EBS volumes.","Load balancing traffic across EC2 instances.","Providing a managed DNS service.","A Transit Gateway simplifies VPN connections across multiple VPCs by acting as a central hub for routing traffic, eliminating the need for complex peering configurations."
"What is the recommended method for establishing a highly available Site-to-Site VPN connection?","Create multiple VPN connections with redundant tunnels.","Use a single VPN connection with a high bandwidth connection.","Enable VPN acceleration.","Disable tunnel maintenance.","To achieve high availability with Site-to-Site VPN, create multiple VPN connections, each with at least two tunnels, to provide redundancy in case of failures."
"When setting up AWS Client VPN, what is an 'Association'?","The link between the Client VPN endpoint and a subnet.","A link between two VPN Gateways.","The association of an IAM role to a user.","A list of approved IP addresses.","In AWS Client VPN, an 'Association' is the link between the Client VPN endpoint and a subnet, allowing clients to access resources within that subnet."
"What is the purpose of the 'Split Tunnel' configuration in AWS Client VPN?","To route only traffic destined for AWS resources through the VPN tunnel.","To send all traffic through the VPN tunnel.","To split the VPN tunnel into multiple tunnels.","To encrypt only specific types of traffic.","The 'Split Tunnel' configuration in AWS Client VPN allows you to route only traffic destined for AWS resources through the VPN tunnel, while other traffic goes directly to the internet."
"What is the significance of the 'Outside IP Address' when configuring a Customer Gateway (CGW)?","It's the public IP address of your on-premises VPN device.","It's the private IP address of your on-premises VPN device.","It's the IP address of your AWS Virtual Private Gateway.","It's the IP address of an EC2 instance in your VPC.","The 'Outside IP Address' is the public IP address of your on-premises VPN device, which is used by AWS to establish the VPN connection."
"Which routing option is required for AWS Site-to-Site VPN if you don't want to use BGP?","Static Routing","Dynamic Routing","Policy Based Routing","Source Based Routing","If you don't want to use BGP, you must configure static routing by manually adding routes to your VPC's route tables."
"What is the primary benefit of using AWS VPN CloudHub?","To provide secure communication between multiple remote networks.","To provide network address translation services.","To load balance traffic across multiple VPN connections.","To encrypt EBS volumes at rest.","AWS VPN CloudHub enables secure communication between multiple remote networks, allowing them to connect to each other through a hub-and-spoke model."
"What is the purpose of setting up a pre-shared key (PSK) for AWS Site-to-Site VPN?","To authenticate the VPN connection between the VGW and CGW.","To encrypt data at rest in S3.","To authorise users to access AWS resources.","To protect against DDoS attacks.","The pre-shared key (PSK) is used for initial authentication of the VPN connection between the Virtual Private Gateway (VGW) and the Customer Gateway (CGW)."
"What is the main difference between AWS Site-to-Site VPN and AWS Direct Connect?","Site-to-Site VPN uses the public internet, while Direct Connect uses a dedicated private connection.","Site-to-Site VPN uses a dedicated private connection, while Direct Connect uses the public internet.","Site-to-Site VPN is more expensive than Direct Connect.","Direct Connect is only available in certain AWS regions.","Site-to-Site VPN uses the public internet to establish a secure connection, while Direct Connect uses a dedicated private connection for higher bandwidth and lower latency."
"In AWS Site-to-Site VPN, what is the purpose of Dead Peer Detection (DPD)?","To detect and recover from tunnel failures.","To detect and block malicious traffic.","To detect and prevent DDoS attacks.","To detect and fix routing misconfigurations.","Dead Peer Detection (DPD) is used to detect and recover from tunnel failures by periodically checking the health of the VPN connection."
"Which AWS service can be used to create and manage a centralised network configuration across multiple AWS accounts and regions, including VPN connections?","AWS Transit Gateway Network Manager","AWS CloudFormation","AWS Systems Manager","AWS Service Catalog","AWS Transit Gateway Network Manager provides a central place to create and manage global networks, including VPN connections, across multiple AWS accounts and regions."
"When configuring AWS Client VPN, what is a 'Target Network'?","The VPC subnet that the Client VPN clients can access.","The public IP address of the Client VPN endpoint.","The private key used for Client VPN authentication.","The DNS server used by Client VPN clients.","A 'Target Network' is the VPC subnet that the Client VPN clients can access once connected to the Client VPN endpoint."
"What is the purpose of the 'Server Certificate ARN' when setting up AWS Client VPN?","To ensure secure communication between the client and the Client VPN endpoint.","To authorize users to access the AWS Management Console.","To encrypt data stored in S3 buckets.","To provide network address translation services.","The 'Server Certificate ARN' is used to ensure secure communication between the client and the Client VPN endpoint, verifying the identity of the server."
"What is the maximum transmission unit (MTU) size for VPN tunnels in AWS Site-to-Site VPN?","1436 bytes","1500 bytes","9001 bytes","65535 bytes","The maximum transmission unit (MTU) size for VPN tunnels in AWS Site-to-Site VPN is typically 1436 bytes to account for VPN overhead."
"Which is a factor when considering the cost of AWS Site-to-Site VPN?","The hourly cost of the VPN connection and data transfer charges.","The number of EC2 instances in your VPC.","The amount of storage used in S3.","The number of IAM users in your account.","The cost of AWS Site-to-Site VPN is based on the hourly cost of the VPN connection and the amount of data transferred through the VPN tunnels."
"Which of these is a key consideration when configuring security groups for your VPN connection?","Ensure security group rules allow traffic between your on-premises network and your VPC.","Disable all security group rules for the VPN connection.","Configure security groups to allow traffic from any source.","Use only default security groups for the VPN connection.","You must ensure security group rules allow traffic between your on-premises network and your VPC to enable communication through the VPN connection."
"How can you ensure that your on-premises network can communicate with resources in your AWS VPC via a VPN connection?","By configuring appropriate route table entries in your VPC.","By disabling network ACLs in your VPC.","By enabling public access to your VPC.","By using a single security group for all resources in your VPC.","You need to configure appropriate route table entries in your VPC to direct traffic destined for your on-premises network through the Virtual Private Gateway (VGW)."
"What is the purpose of AWS PrivateLink in the context of VPN connectivity?","To access AWS services privately from your on-premises network without exposing traffic to the public internet, in addition to your vpn.","To encrypt data at rest in S3 buckets.","To automatically scale EC2 instances based on demand.","To provide content delivery services.","AWS PrivateLink allows you to access AWS services privately from your on-premises network without exposing traffic to the public internet. While it complements VPNs for broader connectivity, it's a distinct service for private endpoints."
"You are setting up AWS Client VPN. Which of the following steps is essential for allowing users to access resources in your VPC?","Associating the Client VPN endpoint with a subnet in your VPC.","Configuring the Client VPN endpoint to use a public IP address.","Enabling VPN acceleration on the Client VPN endpoint.","Configuring the Client VPN endpoint to use a default security group.","Associating the Client VPN endpoint with a subnet in your VPC is essential for allowing users to access resources within that subnet."
"What is the advantage of using AWS VPN with a NAT Gateway in your VPC?","Allows instances in private subnets to initiate outbound traffic to the internet while hiding their private IP addresses.","Provides increased network bandwidth for your VPN connection.","Enables automatic scaling of EC2 instances based on network traffic.","Encrypts data at rest in S3 buckets.","A NAT Gateway allows instances in private subnets to initiate outbound traffic to the internet while hiding their private IP addresses, enhancing security."
"Which of the following is a key benefit of using AWS VPN over the public internet?","Enhanced security through encryption and private connections.","Increased network bandwidth compared to dedicated connections.","Lower cost compared to dedicated connections.","Automated scaling of network resources.","AWS VPN provides enhanced security through encryption and private connections, protecting data in transit."
"When using BGP with AWS Site-to-Site VPN, what is the purpose of the Autonomous System Number (ASN)?","To identify your network to AWS and enable BGP routing.","To encrypt data at rest in S3 buckets.","To authorize users to access AWS resources.","To protect against DDoS attacks.","The Autonomous System Number (ASN) is used to identify your network to AWS and enable BGP routing, facilitating dynamic route advertisement."
"Which factor determines the performance of an AWS Site-to-Site VPN connection?","Network bandwidth, latency, and packet loss between your on-premises network and AWS.","Number of EC2 instances running in your VPC.","Amount of data stored in S3.","Number of IAM users in your account.","The performance of an AWS Site-to-Site VPN connection is highly dependent on network bandwidth, latency, and packet loss between your on-premises network and AWS."
"What is the purpose of enabling 'Acceleration' on an AWS Site-to-Site VPN connection?","To use AWS Global Accelerator to optimise the VPN connection's path.","To automatically scale EC2 instances based on network traffic.","To encrypt data at rest in S3 buckets.","To provide network address translation services.","Enabling 'Acceleration' on an AWS Site-to-Site VPN connection allows you to use AWS Global Accelerator to optimise the VPN connection's path, improving performance and reducing latency."
"When troubleshooting connectivity issues with an AWS Site-to-Site VPN, which logs can provide useful information?","Customer Gateway device logs and CloudWatch metrics for the VPN connection.","CloudTrail logs and S3 access logs.","EC2 instance system logs and VPC flow logs.","IAM access logs and AWS Config logs.","Customer Gateway device logs and CloudWatch metrics for the VPN connection provide valuable insights into the status and performance of the VPN tunnels."
"You are setting up AWS Client VPN and need to configure the authorisation rules. What is the purpose of an authorisation rule?","To specify which CIDR blocks are allowed to access the target network.","To encrypt data at rest in S3 buckets.","To authorize users to access the AWS Management Console.","To provide network address translation services.","An authorisation rule specifies which CIDR blocks are allowed to access the target network, controlling access to resources within the VPC."
"Which AWS service can you integrate with AWS Client VPN to provide centralised user authentication?","AWS Directory Service","AWS IAM","AWS Certificate Manager","AWS Shield","You can integrate AWS Directory Service with AWS Client VPN to provide centralised user authentication using Active Directory."
"What is a key consideration when choosing a VPN connection type (Site-to-Site vs. Client VPN) for your AWS environment?","Whether you need to connect an entire on-premises network or individual client devices.","The amount of data you need to transfer.","The AWS region where your resources are located.","The number of IAM users in your account.","The primary factor is whether you need to connect an entire on-premises network (Site-to-Site) or individual client devices (Client VPN)."
"You have an AWS Site-to-Site VPN connection that is experiencing intermittent disconnections. What is a common cause of this issue?","Incompatible IKE/IPsec settings between the Customer Gateway and the Virtual Private Gateway.","Insufficient CPU resources on EC2 instances.","Too much data stored in S3.","Too many IAM users in your account.","Incompatible IKE/IPsec settings between the Customer Gateway and the Virtual Private Gateway can lead to intermittent disconnections."
"Which action should you take to improve the security of your AWS Client VPN connection?","Enable mutual authentication using certificates.","Disable all security group rules for the VPN connection.","Configure the Client VPN endpoint to use a public IP address.","Use only default security groups for the VPN connection.","Enabling mutual authentication using certificates adds an extra layer of security by verifying the identity of both the client and the server."
"What is the impact of enabling 'Logging' for AWS Client VPN?","Logs connection and disconnection events to CloudWatch Logs for auditing and troubleshooting.","Encrypts data at rest in S3 buckets.","Authorizes users to access the AWS Management Console.","Provides network address translation services.","Enabling 'Logging' for AWS Client VPN logs connection and disconnection events to CloudWatch Logs, providing valuable data for auditing and troubleshooting."
"What is one advantage of using a hardware VPN device at the Customer Gateway end of an AWS Site-to-Site VPN connection?","Provides greater control over VPN configuration and security features.","Guarantees higher network bandwidth compared to software VPNs.","Automatically scales network resources.","Encrypts EBS volumes at rest.","Using a hardware VPN device at the Customer Gateway end provides greater control over VPN configuration and security features."
"When configuring an AWS Site-to-Site VPN, what is the purpose of setting the 'Tunnel Options'?","To configure parameters like the IKE version, pre-shared key, and lifetime of the VPN tunnel.","To encrypt data at rest in S3 buckets.","To authorize users to access the AWS Management Console.","To provide network address translation services.","The 'Tunnel Options' are used to configure parameters such as the IKE version, pre-shared key, and lifetime of the VPN tunnel."
"What is the purpose of the CloudWatch metric 'TunnelState' for an AWS Site-to-Site VPN?","Indicates whether the VPN tunnel is up or down.","Measures the amount of data transferred through the VPN tunnel.","Monitors the CPU utilisation of the VPN gateway.","Tracks the number of connected VPN clients.","The 'TunnelState' metric indicates whether the VPN tunnel is up or down, providing a quick way to check the status of your VPN connection."
"You need to allow your remote workers access to resources within your VPC. Which AWS service is most suitable for this use case?","AWS Client VPN","AWS Site-to-Site VPN","AWS Direct Connect","AWS Transit Gateway","AWS Client VPN is designed for securely connecting individual users or client devices to your AWS resources, making it ideal for remote workers."
"Which AWS VPN solution requires you to manage the virtual private gateway (VGW) and customer gateway devices?","AWS Site-to-Site VPN","AWS Client VPN","AWS Direct Connect","AWS Global Accelerator","With AWS Site-to-Site VPN, you are responsible for managing the customer gateway on your premises and the virtual private gateway (VGW) within your VPC."
"What is a key benefit of using AWS Client VPN compared to setting up your own VPN server on an EC2 instance?","Simplified management and scalability","Lower cost","Increased control over encryption algorithms","Direct access to the underlying operating system","AWS Client VPN handles the management and scaling of the VPN infrastructure, reducing the operational burden compared to self-managing a VPN server."
"Which component represents your on-premises VPN device when configuring an AWS Site-to-Site VPN?","Customer Gateway","Virtual Private Gateway","Transit Gateway","Internet Gateway","The Customer Gateway represents your on-premises VPN device or software application on the AWS side of the connection."
"When creating an AWS Site-to-Site VPN connection, what is the purpose of the 'Tunnel Options'?","To configure routing protocols and security parameters","To select the AWS region","To define the VPC CIDR block","To specify the instance type to use","'Tunnel Options' within AWS Site-to-Site VPN allows you to configure the specific routing protocols, encryption algorithms, and authentication methods used for the VPN tunnel."
"What type of routing is supported by AWS Site-to-Site VPN?","Static and Dynamic (BGP)","Dynamic only","Static only","Policy-based","AWS Site-to-Site VPN supports both static routing, where you manually configure routes, and dynamic routing using Border Gateway Protocol (BGP)."
"In AWS Client VPN, what is an 'Authorisation Rule' used for?","To control which users or groups can access specific network resources","To specify the VPN connection timeout","To define the DNS servers used by the VPN","To enforce multi-factor authentication","Authorisation Rules in AWS Client VPN define which users or groups are permitted to access specific network resources after connecting to the VPN."
"When setting up AWS Site-to-Site VPN, what does the Virtual Private Gateway (VGW) attach to?","A VPC","An Internet Gateway","A Customer Gateway","A NAT Gateway","The Virtual Private Gateway (VGW) is attached to a Virtual Private Cloud (VPC) to enable secure connectivity between your on-premises network and the VPC."
"What is the purpose of 'connection health checks' in AWS Site-to-Site VPN?","To monitor the status and availability of the VPN tunnels","To automatically update the routing tables","To encrypt data in transit","To optimise network performance","Connection health checks monitor the status and availability of the VPN tunnels, allowing you to identify and address connectivity issues."
"Which AWS service can you use to manage and monitor your AWS VPN connections, including Site-to-Site VPN and Client VPN?","AWS CloudWatch","AWS Trusted Advisor","AWS Config","AWS IAM","AWS CloudWatch can be used to monitor the performance and availability of your AWS VPN connections."
"What is the benefit of using multiple tunnels with AWS Site-to-Site VPN?","Increased redundancy and high availability","Reduced latency","Increased bandwidth","Improved security","Using multiple tunnels in AWS Site-to-Site VPN provides redundancy and high availability, ensuring that your VPN connection remains active even if one tunnel fails."
"What is the primary purpose of AWS Site-to-Site VPN?","To establish a secure connection between your on-premises network and your AWS VPC.","To encrypt data at rest in S3.","To load balance traffic across multiple EC2 instances.","To provide a content delivery network (CDN).","Site-to-Site VPN is designed to create a secure, encrypted tunnel between your on-premises infrastructure and your AWS Virtual Private Cloud (VPC)."
"Which AWS VPN connection type allows you to connect multiple on-premises networks to multiple VPCs in a hub-and-spoke configuration?","AWS VPN CloudHub","AWS Direct Connect","AWS Client VPN","AWS Global Accelerator","AWS VPN CloudHub enables secure communication between multiple on-premises locations using your AWS VPN connections."
"Which of the following is a component of an AWS Site-to-Site VPN connection on the AWS side?","Virtual Private Gateway (VGW)","Customer Gateway (CGW)","Transit Gateway (TGW)","Internet Gateway (IGW)","A Virtual Private Gateway (VGW) is attached to your VPC and provides the AWS side endpoint for the VPN connection."
"Which of the following is required on the customer (on-premises) side to establish an AWS Site-to-Site VPN connection?","Customer Gateway (CGW) device","Elastic Load Balancer (ELB)","NAT Gateway","AWS Lambda function","A Customer Gateway (CGW) device is a physical or software appliance on your side that serves as the endpoint for the VPN connection."
"What protocol is used for the VPN tunnel establishment in AWS Site-to-Site VPN?","Internet Key Exchange (IKE)","Secure Shell (SSH)","Transport Layer Security (TLS)","Simple Mail Transfer Protocol (SMTP)","Internet Key Exchange (IKE) is a protocol used to establish a secure, authenticated tunnel between two endpoints for VPN connections."
"What is the maximum transmission unit (MTU) size for AWS VPN connections if Path MTU Discovery is disabled?","1460","1500","9001","8900","When Path MTU Discovery is disabled, AWS recommends an MTU size of 1460 to accommodate VPN overhead."
"What does the 'static routing' option in AWS Site-to-Site VPN configuration mean?","You manually configure routes on both the AWS side and the customer gateway device.","AWS automatically learns routes from your on-premises network.","AWS uses Border Gateway Protocol (BGP) to exchange routing information.","AWS dynamically adjusts routing based on network latency.","With static routing, you must manually configure the routes on both sides of the VPN connection. This is simpler for smaller networks."
"What is a key advantage of using dynamic routing (BGP) with AWS Site-to-Site VPN?","Automatic route propagation and failover.","Simplified initial configuration.","Lower bandwidth consumption.","No need for route tables.","Dynamic routing with BGP allows for automatic route propagation and failover, making it more resilient and adaptable to network changes."
"Which AWS service should be used to provide remote access to applications running in AWS, allowing users to connect from anywhere?","AWS Client VPN","AWS Site-to-Site VPN","AWS Direct Connect","AWS Transit Gateway","AWS Client VPN provides secure remote access to AWS resources, enabling users to connect from various locations."
"What is the purpose of Dead Peer Detection (DPD) in AWS Site-to-Site VPN?","To detect and replace inactive VPN tunnels.","To encrypt data in transit.","To monitor network bandwidth.","To optimise routing configurations.","Dead Peer Detection (DPD) helps to detect and replace inactive VPN tunnels, ensuring that connections remain active and functional."