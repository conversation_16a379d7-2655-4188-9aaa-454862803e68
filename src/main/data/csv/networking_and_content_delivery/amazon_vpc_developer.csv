"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of a subnet in an Amazon VPC?","To segment the VPC into smaller networks","To provide internet access","To create a VPN connection","To configure security groups","Subnets allow you to divide your VPC into smaller, isolated networks, which can be used for different purposes (e.g., public vs. private). Each subnet resides within a single Availability Zone."
"What AWS service can be used to establish a private network connection between your Amazon VPC and another AWS service without using the public internet?","VPC Endpoint","Internet Gateway","NAT Gateway","Virtual Private Gateway","VPC Endpoints allow you to privately connect your VPC to supported AWS services without requiring an Internet Gateway, NAT device, VPN connection, or Direct Connect connection."
"Which Amazon VPC component allows instances in a private subnet to initiate outbound connections to the internet, but prevents the internet from initiating connections to those instances?","NAT Gateway","Internet Gateway","Route Table","Security Group","A NAT Gateway allows instances in a private subnet to connect to the internet or other AWS services, but prevents the internet from initiating a connection with those instances."
"What is the purpose of a Network ACL in an Amazon VPC?","To control inbound and outbound traffic at the subnet level","To control inbound and outbound traffic at the instance level","To encrypt traffic between subnets","To monitor network traffic within the VPC","Network ACLs (Access Control Lists) act as a firewall for controlling traffic in and out of one or more subnets. They operate at the subnet level."
"You need to allow SSH access to an EC2 instance in a public subnet of your Amazon VPC. Which component should you configure?","Security Group","Route Table","Network ACL","VPC Peering Connection","Security Groups act as a virtual firewall for your EC2 instances, controlling inbound and outbound traffic at the instance level. They are stateful, meaning that if you allow inbound traffic, the outbound traffic is automatically allowed."
"What is the function of a Virtual Private Gateway (VGW) in an Amazon VPC?","To enable a VPN connection between your VPC and your on-premises network","To provide internet access to your VPC","To route traffic between subnets within the VPC","To create a Direct Connect connection","A Virtual Private Gateway (VGW) is used to enable a VPN connection between your VPC and your on-premises network. It is attached to the VPC and provides a VPN endpoint for your on-premises network."
"How does VPC Peering help in managing multiple Amazon VPCs?","Enables networking connections between two VPCs","Provides internet access to multiple VPCs","Automates the creation of VPCs","Replicates data between VPCs","VPC Peering allows you to connect two VPCs, enabling you to route traffic between them privately. Instances in either VPC can communicate with each other as if they were within the same network."
"What is the purpose of an Internet Gateway in an Amazon VPC?","To allow instances in the VPC to connect to the internet","To create a VPN connection to your on-premises network","To isolate the VPC from the internet","To monitor network traffic in the VPC","An Internet Gateway is a VPC component that allows communication between instances in your VPC and the internet."
"What is the maximum size of a subnet CIDR block in an Amazon VPC?","/16","/28","/8","/32","The largest subnet you can create in a VPC is a /16. This means you can have a large number of addresses within that subnet."
"You have two Amazon VPCs in different regions. What AWS service can you use to connect them privately?","VPC Peering","VPN Gateway","Direct Connect","Global Accelerator","Classic VPC Peering does not support Inter-Region connectivity. Inter-Region VPC Peering helps you connect two VPCs across different AWS regions."
"What is the purpose of Elastic Network Interfaces (ENIs) in an Amazon VPC?","To provide networking capabilities to EC2 instances","To encrypt network traffic","To control network access","To route network traffic","Elastic Network Interfaces (ENIs) provide networking capabilities to EC2 instances within a VPC. An ENI can have a public IP address, a private IP address, and security group associations."
"Which of the following is a stateful firewall service provided by AWS to control traffic to EC2 instances within a VPC?","Security Groups","Network ACLs","Route Tables","NAT Gateway","Security Groups are stateful firewalls that control inbound and outbound traffic at the instance level.  If a connection is allowed inbound, the return traffic is automatically allowed."
"What is the function of a Route Table in an Amazon VPC?","To control the routing of traffic within the VPC","To control access to the VPC","To encrypt traffic within the VPC","To monitor network performance in the VPC","A Route Table contains a set of rules, called routes, that are used to determine where network traffic is directed within the VPC."
"You want to ensure that traffic between your Amazon VPC and your on-premises network is encrypted. Which service should you use?","AWS VPN","Direct Connect","VPC Peering","Internet Gateway","AWS VPN creates an encrypted tunnel between your VPC and your on-premises network, ensuring that data transmitted between the two is secure."
"What type of IP address is used for communication within an Amazon VPC?","Private IP address","Public IP address","Elastic IP address","Global IP address","Within a VPC, instances communicate using private IP addresses. These addresses are not routable over the internet."
"What type of communication does VPC Flow Logs capture?","IP traffic going to and from network interfaces in your VPC","EC2 instance CPU usage","RDS database query performance","S3 bucket access logs","VPC Flow Logs captures information about the IP traffic going to and from network interfaces in your VPC, providing valuable network monitoring and security insights."
"You need to connect your Amazon VPC to multiple on-premises networks. Which service should you use?","AWS Site-to-Site VPN","VPC Peering","Internet Gateway","NAT Gateway","AWS Site-to-Site VPN allows you to create multiple VPN connections to your VPC, enabling connectivity to multiple on-premises networks."
"What is the purpose of a Customer Gateway in the context of AWS VPN?","To provide information to AWS about your on-premises VPN device","To encrypt traffic between your VPC and your on-premises network","To provide internet access to your VPC","To route traffic within your VPC","A Customer Gateway is a resource you create in AWS that represents your on-premises VPN device. It provides information about the device to AWS, such as its IP address and routing information."
"You are setting up a VPC Peering connection, and one of the VPCs has overlapping CIDR blocks with another VPC. What will happen?","The VPC Peering connection will fail to establish","The VPC Peering connection will establish but traffic will not be routed correctly","AWS will automatically reassign the CIDR block of one of the VPCs","Traffic will be routed randomly between the VPCs","VPC Peering connections cannot be established if the CIDR blocks of the two VPCs overlap. This is to prevent routing conflicts."
"When setting up a NAT Gateway, in which subnet should it reside?","Public subnet","Private subnet","Isolated Subnet","Any subnet","NAT Gateways must reside in a public subnet. This allows them to access the internet to route traffic on behalf of instances in private subnets."
"What is the key benefit of using VPC Flow Logs for security monitoring?","Identify anomalous traffic patterns and potential security threats","Track CPU utilisation of EC2 instances","Monitor disk I/O performance of EBS volumes","Manage user access to S3 buckets","VPC Flow Logs provide valuable insights into network traffic patterns, which can be used to identify anomalous behaviour and potential security threats. This enables proactive security monitoring and threat detection."
"How can you ensure that your EC2 instances in a private subnet can access S3 without using the internet?","Using a VPC Endpoint for S3","Using an Internet Gateway","Using a NAT Gateway","Using a VPN connection","A VPC Endpoint for S3 allows your instances in private subnets to access S3 without routing traffic over the public internet. This enhances security and reduces data transfer costs."
"What type of address is used for communication between your on-premises network and your AWS VPC when using a VPN connection?","Public IP addresses","Private IP addresses","Both Public and Private IP addresses","Elastic IP addresses","When using a VPN connection, traffic between your on-premises network and your AWS VPC is routed using public IP addresses. The VPN tunnel is established over the public internet."
"You need to limit the type of traffic that can enter and exit a specific subnet in your VPC. What is the best way to achieve this?","Configure a Network ACL for the subnet","Configure a Security Group for the subnet","Configure a Route Table for the subnet","Configure a VPC Endpoint for the subnet","Network ACLs operate at the subnet level and allow you to define rules to control inbound and outbound traffic based on IP addresses, ports, and protocols."
"What is the role of a Transit Gateway in AWS networking?","To simplify the connection of multiple VPCs and on-premises networks","To provide internet access to a VPC","To encrypt traffic within a VPC","To monitor network performance in a VPC","Transit Gateway simplifies the connection of multiple VPCs and on-premises networks into a single hub, reducing the complexity of managing numerous connections."
"What happens if you try to create a subnet with a CIDR block that overlaps with the VPC's CIDR block?","The subnet creation will fail","The subnet will be created, but instances cannot be launched in it","The subnet will be created, but routing will be unpredictable","AWS will automatically assign a new CIDR block to the subnet","The subnet creation will fail if the specified CIDR block overlaps with the VPC's CIDR block. Subnets must have unique, non-overlapping CIDR blocks within the VPC."
"You want to allow instances in a public subnet to receive traffic directly from the internet. What is required?","The instances must have a public IP address and the subnet must have a route to an Internet Gateway","The instances must have a private IP address and the subnet must have a route to a NAT Gateway","The instances must have an Elastic IP address and the subnet must have a route to a Virtual Private Gateway","The instances must have a static IP address and the subnet must have a route to a Transit Gateway","For instances in a public subnet to receive traffic directly from the internet, they must have a public IP address (either auto-assigned or an Elastic IP) and the subnet's route table must have a route to an Internet Gateway."
"What is the difference between a Security Group and a Network ACL?","Security Groups are stateful, while Network ACLs are stateless","Security Groups operate at the subnet level, while Network ACLs operate at the instance level","Security Groups control outbound traffic only, while Network ACLs control inbound traffic only","Security Groups are used for VPN connections, while Network ACLs are used for internet access","Security Groups are stateful, meaning they automatically allow return traffic for allowed inbound traffic. Network ACLs are stateless, requiring explicit rules for both inbound and outbound traffic."
"Which of the following Amazon VPC components is necessary for instances in a private subnet to download software patches from the internet?","NAT Gateway or NAT Instance","Internet Gateway","Virtual Private Gateway","VPC Endpoint","A NAT Gateway or NAT Instance is required for instances in a private subnet to initiate outbound connections to the internet, such as downloading software patches. It allows the instances to connect to the internet without being directly accessible from it."
"You need to inspect network traffic flowing through your VPC. Which tool can you use?","VPC Flow Logs","CloudTrail","CloudWatch Logs","AWS Config","VPC Flow Logs capture information about the IP traffic going to and from network interfaces in your VPC, allowing you to inspect network traffic and identify potential security issues."
"What is the purpose of Direct Connect in relation to Amazon VPC?","To create a dedicated network connection from your on-premises network to your VPC","To provide internet access to your VPC","To encrypt traffic within your VPC","To monitor network performance in your VPC","Direct Connect allows you to establish a dedicated network connection from your on-premises network to your VPC, bypassing the public internet and providing more consistent network performance."
"Which statement is correct regarding the use of multiple subnets within an Amazon VPC?","Subnets must be in different Availability Zones","Subnets must all use the same CIDR block","Subnets must be in the same AWS Region","Subnets cannot communicate with each other","Subnets within a VPC must be in the same AWS Region, but they can be in different Availability Zones. This allows for high availability and fault tolerance."
"What is the default network ACL behaviour in a newly created Amazon VPC?","It allows all inbound and outbound traffic","It denies all inbound and outbound traffic","It allows all inbound traffic and denies all outbound traffic","It denies all inbound traffic and allows all outbound traffic","The default network ACL allows all inbound and outbound traffic. You can modify it to restrict traffic as needed."
"How do you enable DNS resolution for private hosted zones within your Amazon VPC?","Enable DNS resolution for the VPC","Create a Route 53 resolver endpoint","Configure a DNS server in the VPC","Enable VPC Flow Logs","To enable DNS resolution for private hosted zones within a VPC, you must enable DNS resolution for the VPC. This allows instances within the VPC to resolve the records in the private hosted zone."
"What is the maximum number of VPCs you can peer with a single VPC?","There is no hard limit, but AWS recommends keeping the number of peers low for manageability","5","10","20","While there isn't a hard technical limit, AWS recommends keeping the number of VPC peers low for ease of management and to avoid complex routing configurations."
"You are using a VPC Endpoint to access S3. What is the benefit of using a gateway endpoint versus an interface endpoint?","Gateway endpoints are free of charge","Interface endpoints are faster","Gateway endpoints support more services","Interface endpoints are easier to configure","Gateway endpoints for S3 and DynamoDB are free of charge. Interface endpoints have a cost associated with them."
"When creating a VPC, what is the minimum size of the CIDR block that you can specify?","/28","/24","/20","/16","The minimum size of the CIDR block you can specify when creating a VPC is a /28. This provides a small number of addresses for the VPC."
"What is the purpose of an egress-only internet gateway?","To allow IPv6 traffic from your VPC to the internet, while blocking inbound IPv6 traffic","To allow IPv4 traffic from your VPC to the internet, while blocking inbound IPv4 traffic","To route traffic between VPCs","To provide NAT functionality for IPv6","An egress-only internet gateway is used to allow IPv6 traffic from your VPC to the internet, while preventing the internet from initiating an IPv6 connection to instances in your VPC. It provides stateful outbound traffic for IPv6."
"You have two Amazon VPCs that need to communicate with each other. One VPC has overlapping CIDR blocks with your on-premises network. Can you establish a VPN connection between your on-premises network and both VPCs?","No, overlapping CIDR blocks can cause routing conflicts","Yes, AWS automatically resolves CIDR block conflicts","Yes, you can use NAT to avoid CIDR block conflicts","Yes, but you need to manually configure routing for each subnet","No. Overlapping CIDR blocks can cause routing conflicts, making it impossible to establish a VPN connection between your on-premises network and both VPCs. You would need to resolve the CIDR block overlap first."
"You want to ensure that your EC2 instances are launched in a specific Availability Zone within your Amazon VPC. How can you achieve this?","Specify the subnet ID associated with the desired Availability Zone when launching the instance","Configure a placement group for the instance","Use an Elastic IP address","Use a dedicated host","To launch an EC2 instance in a specific Availability Zone, you need to specify the subnet ID associated with that Availability Zone when launching the instance. Subnets are tied to specific Availability Zones."
"What is the difference between VPC peering and Transit Gateway?","Transit Gateway allows connectivity to more than two VPCs and on-premise locations","VPC peering provides centralized traffic management","Transit Gateway is less scalable than VPC peering","VPC peering provides more granular control over network traffic","Transit Gateway allows connectivity to more than two VPCs and on-premise locations."
"Which of the following AWS services can you integrate with VPC Flow Logs to analyse network traffic?","Amazon Athena","AWS CloudTrail","AWS Config","Amazon Inspector","VPC Flow Logs can be integrated with Amazon Athena to analyse network traffic using SQL queries, allowing you to gain insights into network behaviour and identify potential security issues."
"When configuring a security group, what happens if you don't specify an outbound rule?","All outbound traffic is allowed by default","All outbound traffic is denied by default","Outbound traffic is allowed only to instances within the same security group","Outbound traffic is allowed only to the internet","By default, security groups allow all outbound traffic. You can create outbound rules to restrict traffic as needed."
"Which type of Amazon VPC Endpoint provides private connectivity to AWS services powered by PrivateLink?","Interface VPC Endpoint","Gateway VPC Endpoint","NAT Gateway","Internet Gateway","Interface VPC Endpoints provide private connectivity to AWS services powered by PrivateLink. These endpoints appear as elastic network interfaces (ENIs) with private IP addresses in your VPC."
"Which of the following AWS services can be used to centralise the management of security rules across multiple VPCs?","AWS Firewall Manager","AWS Config","AWS CloudTrail","AWS Trusted Advisor","AWS Firewall Manager allows you to centrally configure and manage firewall rules across multiple VPCs and AWS accounts, simplifying security management and ensuring consistent security policies."
"What is the purpose of a Network Access Control List (NACL) in an Amazon VPC?","To act as a stateless firewall at the subnet level","To encrypt data in transit","To manage user access to EC2 instances","To control traffic at the instance level","NACLs are stateless firewalls that control traffic in and out of subnets. They evaluate traffic based on configured rules."
"In Amazon VPC, what is the primary function of a route table?","To control the routing of network traffic","To store security group rules","To define the size of subnets","To create VPN connections","Route tables contain a set of rules, called routes, that are used to determine where network traffic is directed."
"What is the purpose of an Internet Gateway in an Amazon VPC?","To allow instances in a public subnet to connect to the Internet","To encrypt data between VPCs","To provide a secure tunnel to on-premises networks","To manage DNS resolution","An Internet Gateway enables communication between instances in a VPC and the Internet. It provides a target in your VPC route tables for Internet-routable traffic."
"Which of the following is the function of a VPC Peering connection in Amazon VPC?","To enable network connectivity between two VPCs","To provide a dedicated connection to on-premises networks","To encrypt data at rest","To route traffic to the internet","VPC Peering allows you to connect two VPCs together, enabling you to route traffic between them privately."
"What does CIDR block stand for in the context of Amazon VPC?","Classless Inter-Domain Routing","Cloud Infrastructure Data Repository","Centralised Identity and Directory Resource","Container Instance Deployment Region","A CIDR block is a range of IP addresses used to define the address space of a VPC or subnet."
"What is the purpose of a VPC Endpoint in Amazon VPC?","To allow instances to connect to AWS services privately","To provide redundant network connections","To manage security group rules","To enable internet access for instances","VPC Endpoints allow you to connect to AWS services (like S3 or DynamoDB) from your VPC without requiring an Internet Gateway or NAT device, keeping traffic within the AWS network."
"In Amazon VPC, what is the function of a NAT Gateway?","To allow instances in a private subnet to initiate outbound internet traffic","To prevent all internet traffic","To encrypt data at rest","To manage DNS resolution","A NAT Gateway allows instances in a private subnet to connect to the internet without being directly exposed to it, typically for software updates and patching."
"Which statement best describes a Security Group in Amazon VPC?","It acts as a virtual firewall for your EC2 instances","It provides network connectivity between VPCs","It defines the size of subnets","It controls the routing of network traffic","Security Groups act as a virtual firewall that controls inbound and outbound traffic for your EC2 instances."
"What is the difference between a public subnet and a private subnet in Amazon VPC?","A public subnet has a route to an Internet Gateway, while a private subnet does not","A public subnet can only contain Linux instances, while a private subnet can only contain Windows instances","A public subnet is always encrypted, while a private subnet is not","A public subnet has more available IP addresses than a private subnet","A public subnet has a route to an Internet Gateway, allowing instances within it to communicate directly with the internet. A private subnet does not and relies on NAT Gateway or VPC Endpoint."
"What is the maximum Transmission Unit (MTU) size for traffic within an Amazon VPC by default?","1500 bytes","9001 bytes","576 bytes","68 bytes","The default MTU size for traffic within a VPC is 1500 bytes. Jumbo frames (9001 bytes) can be enabled."
"How can you connect an on-premises data centre to your Amazon VPC?","Using a VPN connection or Direct Connect","Using an Internet Gateway","Using a NAT Gateway","Using a VPC Endpoint","You can connect your on-premises data centre to your VPC using a VPN connection (over the public internet) or Direct Connect (a dedicated network connection)."
"What is the benefit of using a VPC Flow Log in Amazon VPC?","To capture information about the IP traffic going to and from network interfaces in your VPC","To monitor CPU utilisation of EC2 instances","To manage user access to AWS resources","To create backups of EC2 instances","VPC Flow Logs capture information about the IP traffic going to and from network interfaces in your VPC, which can be used for security monitoring, troubleshooting, and compliance."
"What is the purpose of a Customer Gateway in Amazon VPC?","To provide information to AWS about your on-premises network for VPN connections","To manage security groups in your VPC","To create Internet Gateways","To define the size of your subnets","A Customer Gateway is a resource you create in AWS to represent the on-premises VPN device used to establish a VPN connection to your VPC."
"Which of the following is a key benefit of using multiple Availability Zones (AZs) within an Amazon VPC?","Increased fault tolerance and high availability","Reduced network latency within the VPC","Lower cost of data transfer","Increased security compliance","Using multiple AZs provides redundancy and fault tolerance, allowing your applications to remain available even if one AZ experiences an outage."
"Which service allows you to privately access multiple AWS services from your Amazon VPC, powered by PrivateLink?","VPC Endpoint","NAT Gateway","Internet Gateway","VPN Gateway","VPC Endpoints, powered by PrivateLink, provide private connectivity to supported AWS services and VPC endpoint services without requiring an internet gateway, NAT device, VPN connection, or Direct Connect connection."
"What is the function of a Transit Gateway in Amazon VPC?","To simplify network connectivity between multiple VPCs and on-premises networks","To provide a firewall service for your VPC","To encrypt data at rest","To manage DNS resolution","A Transit Gateway acts as a network transit hub, simplifying network connectivity between multiple VPCs and on-premises networks."
"When creating an Amazon VPC, what is the smallest CIDR block size you can specify?"," /28"," /32"," /16"," /8","The smallest CIDR block you can specify when creating a VPC is a /28, which provides 16 IP addresses, although 5 are reserved by AWS."
"What is the function of a Gateway Load Balancer (GWLB) in Amazon VPC?","To distribute network traffic to virtual appliances, such as firewalls or intrusion detection systems","To distribute traffic to EC2 instances","To terminate SSL connections","To cache content","A Gateway Load Balancer distributes network traffic to virtual appliances, enabling you to insert security or monitoring appliances into your network traffic flow."
"You need to configure your Amazon VPC to resolve custom domain names. Which service should you use?","Route 53 Resolver","VPC Peering","CloudFront","IAM","Route 53 Resolver allows you to forward DNS queries between your VPCs and your on-premises networks, enabling you to resolve custom domain names."
"What is the primary purpose of a Direct Connect gateway when used with Amazon VPC?","To connect multiple VPCs in different regions to a single Direct Connect connection","To provide a redundant Internet Gateway","To encrypt data in transit","To manage security group rules","A Direct Connect gateway allows you to connect multiple VPCs in different regions to a single Direct Connect connection, simplifying network management."
"In Amazon VPC, which feature allows you to block specific websites or applications at the network level?","AWS Network Firewall","Security Groups","NACLs","Route Tables","AWS Network Firewall allows you to filter network traffic based on domain names or other application-layer attributes."
"What is the purpose of using a private hosted zone in Route 53 with your Amazon VPC?","To resolve domain names only within the VPC","To provide public DNS resolution for your domain","To encrypt DNS traffic","To manage SSL certificates","A private hosted zone allows you to manage DNS records and resolve domain names only within your VPC, keeping your internal domain names private."
"How can you enable IPv6 for your Amazon VPC?","By associating an IPv6 CIDR block with your VPC and subnets","By creating an Internet Gateway","By configuring a NAT Gateway","By enabling encryption on your VPC","To enable IPv6, you need to associate an IPv6 CIDR block with your VPC and subnets. Then you must modify route tables to route IPv6 traffic."
"What is the purpose of egress-only internet gateway in an Amazon VPC?","To allow IPv6 instances in a private subnet to initiate outbound internet traffic","To allow IPv4 instances in a private subnet to initiate outbound internet traffic","To prevent all internet traffic from leaving the VPC","To provide encryption for all outbound traffic","An egress-only internet gateway allows IPv6 instances in a private subnet to initiate outbound traffic to the internet but prevents the internet from initiating a connection with those instances."
"Which statement is true about the default Security Group in an Amazon VPC?","It allows all inbound and outbound traffic within the VPC","It denies all inbound and outbound traffic","It allows all outbound traffic and allows inbound traffic only from within the security group","It allows all inbound traffic and denies all outbound traffic","By default, the default Security Group allows all outbound traffic and allows inbound traffic only from within the security group itself."
"What is the purpose of AWS PrivateLink when used in conjunction with an Amazon VPC?","To provide private connectivity between your VPC and supported AWS services without exposing your traffic to the public internet","To encrypt data at rest in your VPC","To provide public access to your VPC resources","To manage user authentication for your VPC","AWS PrivateLink provides private connectivity between your VPC and supported AWS services without exposing your traffic to the public internet, enhancing security."
"How do you configure an Amazon VPC to automatically assign public IP addresses to instances launched in a subnet?","Enable 'Auto-assign public IPv4 address' on the subnet","Create a NAT Gateway","Configure a security group to allow all inbound traffic","Enable VPC Flow Logs","You can enable 'Auto-assign public IPv4 address' on a subnet to automatically assign public IP addresses to instances launched in that subnet."
"What is the purpose of a bastion host in a VPC?","To provide secure access to instances in a private subnet","To load balance traffic across multiple EC2 instances","To encrypt all data in transit","To provide a highly available DNS service","A bastion host is a hardened server that sits in a public subnet and allows administrators to securely connect to instances in private subnets via SSH or RDP."
"When using VPC peering, what is a limitation regarding overlapping CIDR blocks?","VPC peering cannot be established between VPCs with overlapping CIDR blocks","VPC peering requires overlapping CIDR blocks","VPC peering is faster with overlapping CIDR blocks","VPC peering automatically resolves CIDR block conflicts","VPC peering cannot be established between VPCs with overlapping CIDR blocks to avoid routing conflicts."
"Which Amazon VPC feature can be used to inspect and filter network traffic flowing between EC2 instances within a VPC?","Traffic Mirroring","VPC Flow Logs","AWS Shield","AWS WAF","Traffic Mirroring allows you to copy network traffic from EC2 instances for analysis and inspection, which can be used for security and troubleshooting."
"Which AWS service allows you to create a fully managed, scalable, and highly available DNS service within your Amazon VPC?","Amazon Route 53","Amazon CloudFront","AWS Certificate Manager","AWS Systems Manager","Amazon Route 53 allows you to create public and private hosted zones, enabling you to manage DNS records within your VPC."
"What is the primary benefit of using multiple Availability Zones (AZs) for resources deployed within an Amazon VPC?","Increased fault tolerance","Decreased network latency","Reduced cost","Simplified management","Using multiple AZs ensures that your application remains available even if one AZ experiences an outage, providing increased fault tolerance."
"In Amazon VPC, what is the function of VPC sharing?","Allows multiple AWS accounts to create their application resources into a shared, centrally managed VPC","Allows sharing security group rules across VPCs","Allows sharing internet gateways across VPCs","Allows sharing route tables across VPCs","VPC Sharing enables multiple AWS accounts to create their application resources (such as EC2 instances, RDS databases) into shared and centrally managed Virtual Private Clouds (VPCs)."
"What does the 'enableDnsSupport' attribute in an Amazon VPC control?","Whether DNS resolution is enabled for the VPC","Whether instances receive public IP addresses","Whether VPC Flow Logs are enabled","Whether VPC peering is allowed","The 'enableDnsSupport' attribute controls whether DNS resolution is enabled for the VPC, allowing instances to resolve domain names."
"What is the purpose of the 'enableDnsHostnames' attribute in an Amazon VPC?","Whether instances receive DNS hostnames","Whether DNS resolution is enabled for the VPC","Whether VPC Flow Logs are enabled","Whether VPC peering is allowed","The 'enableDnsHostnames' attribute controls whether instances receive DNS hostnames, allowing them to be identified by DNS names."
"What is the function of a local route in a VPC route table?","It enables communication within the VPC's CIDR block","It routes traffic to the Internet Gateway","It routes traffic to a VPC Peering connection","It routes traffic to a virtual private gateway","A local route in a VPC route table allows resources within the VPC's CIDR block to communicate with each other."
"When configuring a site-to-site VPN connection to your Amazon VPC, what is the purpose of the Virtual Private Gateway (VGW)?","It is the VPN endpoint on the Amazon side of the connection","It is the VPN endpoint on the customer side of the connection","It is a firewall for the VPN connection","It is a load balancer for the VPN connection","The Virtual Private Gateway (VGW) is the VPN endpoint on the Amazon side of the site-to-site VPN connection."
"What is the purpose of setting up custom route tables in an Amazon VPC?","To control the routing of traffic within specific subnets","To create a default route for all traffic","To automatically create security groups","To manage VPC endpoints","Custom route tables allow you to define specific routing rules for traffic within particular subnets, providing more granular control over network traffic flow."
"You have an application running in a private subnet in your Amazon VPC that needs to access AWS S3. Which is the MOST secure and cost-effective way to enable this access?","Create a VPC Endpoint for S3","Create a NAT Gateway","Create an Internet Gateway and assign a public IP address to the instance","Configure a proxy server in a public subnet","A VPC Endpoint for S3 provides private connectivity to S3 without requiring a NAT Gateway or exposing traffic to the internet, making it the most secure and cost-effective option."
"In an Amazon VPC, what is the purpose of a Destination NAT (DNAT) rule when configuring a NAT Gateway?","To translate the destination IP address of incoming traffic","To translate the source IP address of outgoing traffic","To drop unwanted traffic","To encrypt traffic","DNAT translates the destination IP address of incoming traffic, typically used to forward traffic from a public IP to a private IP within the VPC."
"What is the purpose of AWS Transit Gateway Connect?","To simplify branch connectivity to Transit Gateway using SD-WAN appliances","To directly connect AWS Direct Connect to a VPC","To enable VPC Peering between regions","To manage firewall rules across multiple VPCs","AWS Transit Gateway Connect simplifies branch connectivity to Transit Gateway using SD-WAN appliances, enabling easier integration of branch offices into your AWS network."
"Which of the following is NOT a valid use case for VPC Flow Logs?","Auditing security group rules","Troubleshooting connectivity issues","Monitoring network traffic patterns","Estimating AWS billing costs","While VPC Flow Logs can provide data for cost analysis, they are not designed to be the primary tool for estimating AWS billing costs. AWS Cost Explorer and Cost Allocation Tags are better suited for this."
"What is the purpose of the EC2 Instance Connect Endpoint feature in Amazon VPC?","To allow secure SSH and RDP connections to instances without requiring public IPs","To provide load balancing across EC2 instances","To encrypt data at rest on EC2 instances","To automate patching of EC2 instances","EC2 Instance Connect Endpoint allows you to securely connect to instances without requiring public IPs or the need to manage SSH keys or bastion hosts."
"What is the purpose of specifying a 'deny' rule in a Network ACL (NACL) within an Amazon VPC?","To explicitly block specific traffic","To allow all traffic","To prioritise certain types of traffic","To log all network traffic","A 'deny' rule in a NACL explicitly blocks specific traffic based on IP address, port, or protocol, providing granular control over network access."
"What is a key difference between Security Groups and Network ACLs (NACLs) in an Amazon VPC?","Security Groups are stateful, while NACLs are stateless","Security Groups operate at the subnet level, while NACLs operate at the instance level","Security Groups can only allow traffic, while NACLs can both allow and deny traffic","Security Groups are free to use, while NACLs incur costs","Security Groups are stateful, meaning they remember previous connections, while NACLs are stateless and evaluate traffic based on explicitly configured rules for both inbound and outbound traffic."
"What is the maximum number of VPCs that an AWS account can have per region by default?","5","10","20","100","By default, an AWS account can have up to 5 VPCs per region. This limit can be increased by contacting AWS Support."
"You need to ensure that your Amazon VPC is protected against common web exploits. Which AWS service can you integrate with your Application Load Balancer to achieve this?","AWS WAF (Web Application Firewall)","AWS Shield","Amazon GuardDuty","AWS Inspector","AWS WAF (Web Application Firewall) protects your web applications from common web exploits by allowing you to define rules to filter malicious traffic."
"What is the purpose of a VPC prefix list?","To simplify security group and route table configuration by referencing a group of CIDR blocks","To encrypt data in transit","To create a backup of VPC configuration","To monitor network traffic in VPC","A VPC prefix list allows you to create a named list of CIDR blocks, which can be referenced in security group rules and route table entries, simplifying configuration and management."
"Which of the following is a key advantage of using AWS Network Firewall compared to Network ACLs (NACLs) in an Amazon VPC?","AWS Network Firewall provides deeper packet inspection and application-level filtering capabilities","AWS Network Firewall is free to use, while NACLs incur costs","AWS Network Firewall operates at the subnet level, while NACLs operate at the VPC level","AWS Network Firewall is simpler to configure than NACLs","AWS Network Firewall offers more advanced features such as intrusion prevention and detection, domain filtering, and application-level filtering compared to the basic stateless filtering provided by NACLs."
"What is the purpose of a Network Access Control List (NACL) in an Amazon VPC?","To act as a stateless firewall at the subnet level","To route traffic between different VPCs","To encrypt traffic between instances","To manage user access to AWS resources","NACLs are stateless firewalls that control traffic in and out of subnets within a VPC. They evaluate traffic based on configured rules."
"Which of the following is true regarding the default VPC in an Amazon VPC?","All new AWS accounts start with a default VPC in each Region.","Default VPCs must be manually created.","You can only have one VPC per AWS account.","Default VPCs are isolated and cannot connect to the internet.","AWS automatically creates a default VPC in each Region for new accounts, making it easy to launch instances without initial configuration."
"In Amazon VPC, what is the purpose of a VPC peering connection?","To enable network connectivity between two VPCs","To encrypt data at rest in S3","To manage IAM roles across accounts","To establish a direct connection to on-premises networks","VPC peering allows you to connect two VPCs, enabling network traffic to flow between them privately."
"What is the primary function of a VPC route table in an Amazon VPC?","To determine where network traffic is directed","To define the allowed traffic using security groups","To configure load balancing across instances","To manage IP address allocation within the VPC","A route table contains a set of rules, called routes, that are used to determine where network traffic from your subnet or gateway is directed."
"In the context of Amazon VPC, what is a CIDR block?","A range of IP addresses used for the VPC","A security group rule","An instance type","A VPN connection","A CIDR block is a range of IP addresses that you specify for your VPC, allowing you to allocate private IP addresses to your resources."
"When creating an Amazon VPC, what is the smallest CIDR block that can be used?","\/28","\/32","\/16","\/8","The smallest CIDR block you can specify for a VPC is a /28, which provides 16 IP addresses, though only 11 are usable due to reserved addresses."
"What is the purpose of a VPC endpoint in Amazon VPC?","To privately connect to AWS services without using the public internet","To create a direct connection to on-premises networks","To manage encryption keys","To monitor network traffic within the VPC","VPC endpoints enable you to privately connect your VPC to supported AWS services and VPC endpoint services powered by PrivateLink without requiring an internet gateway, NAT device, VPN connection, or Direct Connect connection."
"In Amazon VPC, what is the difference between a Security Group and a Network ACL (NACL)?","Security Groups are stateful, while NACLs are stateless.","Security Groups operate at the subnet level, while NACLs operate at the instance level.","Security Groups control outbound traffic only, while NACLs control inbound traffic only.","Security Groups are used for routing traffic, while NACLs are used for security.","Security Groups are stateful, meaning they remember previous connections, while NACLs are stateless and evaluate each packet independently."
"What is the function of a Transit Gateway in Amazon VPC?","To simplify network connectivity between multiple VPCs and on-premises networks.","To encrypt data in transit between VPCs.","To provide DNS resolution within a VPC.","To act as a load balancer for VPC traffic.","Transit Gateway provides a hub-and-spoke architecture for connecting multiple VPCs and on-premises networks, simplifying network management."
"In Amazon VPC, what is the purpose of a NAT Gateway?","To allow instances in a private subnet to connect to the internet.","To provide a direct connection to on-premises networks.","To encrypt data at rest in S3.","To manage user access to AWS resources.","A NAT (Network Address Translation) Gateway allows instances in a private subnet to initiate outbound traffic to the Internet or other AWS services, but prevents the Internet from initiating a connection with those instances."
"In Amazon VPC, what is the primary purpose of a Network Access Control List (NACL)?","To control traffic at the subnet level","To control traffic at the instance level","To control traffic between regions","To control traffic to the Internet Gateway","NACLs act as a stateless firewall that controls traffic in and out of subnets."
"When creating an Amazon VPC, which of the following CIDR blocks is NOT a valid private IP address range according to RFC 1918?","**********/12","***********/16","10.0.0.0/8","***********/16","***********/16 is the Automatic Private IP Addressing (APIPA) range and is not valid for VPC CIDR blocks."
"What is the purpose of a VPC peering connection in Amazon VPC?","To connect two VPCs together privately","To connect to the internet","To connect to on-premises networks via VPN","To connect to AWS Direct Connect","VPC peering enables you to connect one VPC with another via a direct network route using private IP addresses."
"What is the function of a route table in Amazon VPC?","To control network traffic direction within the VPC","To control access to EC2 instances","To encrypt data in transit","To monitor network performance","Route tables contain a set of rules, called routes, that determine where network traffic from your subnet or gateway is directed."
"In Amazon VPC, what does a 'default' security group allow?","All outbound traffic and inbound traffic only from within the security group itself","All inbound and outbound traffic","No inbound or outbound traffic","Only SSH traffic","A default security group allows all outbound traffic, and inbound traffic is only allowed from within the security group itself. This is the default configuration when one is not specified."
"When setting up a Site-to-Site VPN connection in Amazon VPC, what is the purpose of the Customer Gateway?","To provide information about your on-premises network to AWS","To encrypt the VPN tunnel","To provide internet access to your on-premises network","To authenticate users accessing the VPN","The Customer Gateway provides information to AWS about your on-premises network, including the IP address of your VPN device and the authentication method."
"Which AWS service allows you to create a private connection between your Amazon VPC and other AWS services, without exposing your traffic to the public internet?","VPC Endpoints","Internet Gateway","NAT Gateway","VPN Gateway","VPC Endpoints allow you to privately connect your VPC to supported AWS services and VPC endpoint services powered by PrivateLink without requiring an internet gateway, NAT device, VPN connection, or AWS Direct Connect connection."
"What is the role of an Internet Gateway (IGW) in an Amazon VPC?","To allow communication between the VPC and the internet","To provide private connectivity to other AWS services","To act as a firewall for the VPC","To enable VPC peering","An Internet Gateway allows instances in the VPC to connect to the internet, and vice versa."
"In Amazon VPC, what is the purpose of a NAT Gateway?","To allow instances in a private subnet to connect to the internet, while preventing the internet from initiating a connection with those instances","To provide VPN connectivity to your on-premises network","To allow instances in a public subnet to connect to the internet","To load balance traffic across multiple EC2 instances","NAT Gateways allow instances in private subnets to initiate outbound connections to the internet or other AWS services, but prevent the internet from initiating connections with those instances."
"Within the context of Amazon VPC, what does the acronym 'CIDR' stand for?","Classless Inter-Domain Routing","Classful Inter-Domain Routing","Central Internet Domain Registry","Controlled Internal Data Routing","CIDR stands for Classless Inter-Domain Routing, a method for allocating IP addresses and IP routing."