"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary benefit of using AWS Global Accelerator for your application?","Improved application availability and performance for global users","Reduced storage costs","Simplified IAM role management","Automated patching of EC2 instances","AWS Global Accelerator directs traffic to the optimal endpoint based on user location, health, and configuration, improving performance and availability."
"How does AWS Global Accelerator improve application performance?","By routing traffic through the AWS global network to the closest healthy endpoint","By automatically scaling EC2 instances","By compressing data before transmission","By encrypting data at rest","Global Accelerator uses the AWS global network to route traffic, reducing latency and improving performance by directing users to the closest healthy endpoint."
"What type of addresses does AWS Global Accelerator provide to clients?","Static IP addresses","Dynamic IP addresses","MAC addresses","IPv6 addresses only","Global Accelerator provides static IP addresses, making it easier to manage and whitelist IP addresses for your application."
"What is the purpose of an endpoint group in AWS Global Accelerator?","To associate endpoints with a specific AWS region","To manage security groups","To configure CloudWatch alarms","To define network ACLs","An endpoint group associates endpoints (e.g., EC2 instances, Network Load Balancers) with a specific AWS region, allowing Global Accelerator to route traffic accordingly."
"What type of endpoints can you associate with AWS Global Accelerator?","Network Load Balancers, Application Load Balancers, EC2 instances, and Elastic IP addresses","Only Application Load Balancers","Only EC2 instances","Only Network Load Balancers","Global Accelerator supports a variety of endpoints, including Network Load Balancers, Application Load Balancers, EC2 instances, and Elastic IP addresses."
"How does AWS Global Accelerator handle endpoint health checks?","It performs health checks on endpoints and only directs traffic to healthy ones","It relies on the application to perform health checks","It doesn't perform health checks","It only checks CPU utilisation","Global Accelerator performs health checks on endpoints to ensure that traffic is only directed to healthy and available resources."
"What happens to traffic if all endpoints in an endpoint group become unhealthy in AWS Global Accelerator?","Traffic is routed to the next closest healthy endpoint group or AWS region","Traffic is dropped","Traffic is redirected to an error page","Traffic is queued until an endpoint becomes healthy","If all endpoints in an endpoint group are unhealthy, Global Accelerator routes traffic to the next closest healthy endpoint group or AWS region, ensuring continuous availability."
"Which AWS service integrates with AWS Global Accelerator to distribute traffic to applications?","Elastic Load Balancing (ELB)","Amazon S3","Amazon EC2","Amazon RDS","Global Accelerator integrates with Elastic Load Balancing (ELB), allowing you to use Network Load Balancers (NLB) and Application Load Balancers (ALB) as endpoints."
"What is the primary use case for using AWS Global Accelerator with multiple AWS regions?","Improving fault tolerance and disaster recovery","Reducing costs","Simplifying security management","Improving database performance","Using Global Accelerator with multiple AWS regions improves fault tolerance and disaster recovery capabilities by automatically routing traffic to healthy regions."
"Which type of application would benefit most from using AWS Global Accelerator?","A globally distributed application with users in multiple geographic locations","A single-region application with users in the same geographic location","A static website hosted on S3","A batch processing application","Globally distributed applications benefit most from Global Accelerator because it optimises traffic routing for users in different geographic locations."
"When should you use AWS Global Accelerator over Amazon CloudFront?","When you need to accelerate TCP or UDP traffic and require static IP addresses","When you need to cache static content","When you need to encrypt data at rest","When you need to manage DNS records","Global Accelerator is better suited for accelerating TCP or UDP traffic and providing static IP addresses, while CloudFront is designed for caching static and dynamic content."
"What type of routing policy does AWS Global Accelerator use by default?","Proximity-based routing","Latency-based routing","Geolocation routing","Weighted routing","By default, Global Accelerator uses proximity-based routing, directing traffic to the closest healthy endpoint group."
"You are using AWS Global Accelerator for a gaming application. What is the primary benefit in this scenario?","Reduced latency for players connecting from different regions","Improved security against DDoS attacks","Simplified game server deployment","Automatic scaling of game servers","For gaming applications, Global Accelerator reduces latency, providing a better gaming experience for players connecting from different geographic locations."
"How does AWS Global Accelerator contribute to improved application availability?","By providing automatic failover to healthy endpoints in different regions","By automatically scaling EC2 instances","By providing DDoS protection","By managing DNS records","Global Accelerator improves availability by automatically failing over to healthy endpoints in different regions if an endpoint becomes unhealthy."
"Which of the following is a key component of AWS Global Accelerator architecture?","Accelerator","Listener","VPC Endpoint","CloudFront Distribution","The 'Accelerator' is the core component of Global Accelerator, directing traffic to optimal endpoints."
"What is the purpose of a Listener in AWS Global Accelerator?","To process inbound connection requests based on port and protocol","To monitor endpoint health","To define security groups","To configure network ACLs","A Listener in Global Accelerator processes inbound connection requests based on port and protocol, directing traffic to the appropriate endpoint groups."
"You want to use AWS Global Accelerator to improve the performance of your web application. Which endpoint type is most suitable in this scenario?","Application Load Balancer (ALB)","EC2 instance","Network Load Balancer (NLB)","Elastic IP address","An Application Load Balancer (ALB) is the most suitable endpoint for web applications, as it can handle HTTP/HTTPS traffic."
"How can you monitor the performance of AWS Global Accelerator?","Using Amazon CloudWatch metrics and logs","Using AWS CloudTrail logs","Using VPC Flow Logs","Using AWS Config","You can monitor the performance of Global Accelerator using Amazon CloudWatch metrics and logs, providing insights into traffic patterns and endpoint health."
"What is the difference between AWS Global Accelerator and AWS Direct Connect?","Global Accelerator accelerates traffic over the internet, while Direct Connect provides a dedicated network connection","Global Accelerator is used for storage, while Direct Connect is used for compute","Global Accelerator is a database service, while Direct Connect is a networking service","Global Accelerator caches static content, while Direct Connect routes traffic dynamically","Global Accelerator accelerates traffic over the internet, while Direct Connect provides a dedicated network connection from your on-premises environment to AWS."
"Which protocol does AWS Global Accelerator use to optimise the path between the user and the application endpoint?","TCP","HTTP","UDP","ICMP","Global Accelerator uses TCP protocol to optimise the path between the user and the application endpoint."
"You need to implement a solution where users are always directed to the nearest healthy endpoint. Which AWS service is best suited for this?","AWS Global Accelerator","Amazon CloudFront","Amazon Route 53","AWS Direct Connect","AWS Global Accelerator is specifically designed to direct users to the nearest healthy endpoint, improving application performance and availability."
"What is a weight in the context of AWS Global Accelerator endpoint groups?","A value that determines the proportion of traffic directed to the endpoint group","A value that determines the maximum number of connections allowed to the endpoint group","A value that determines the priority of the endpoint group during failover","A value that determines the cost of using the endpoint group","The weight of an endpoint group determines the proportion of traffic directed to that endpoint group relative to other endpoint groups in the same listener."
"How does AWS Global Accelerator help mitigate DDoS attacks?","By absorbing and distributing malicious traffic across multiple endpoints","By blocking all traffic from suspicious IP addresses","By encrypting all traffic to prevent interception","By automatically scaling resources to handle increased load","Global Accelerator helps mitigate DDoS attacks by distributing traffic across multiple endpoints and utilising the AWS global network to absorb and distribute malicious traffic."
"You want to implement a blue/green deployment strategy using AWS Global Accelerator. How can you achieve this?","By using traffic dial to shift traffic between endpoint groups","By using weighted routing in Route 53","By using Auto Scaling groups to manage instances","By using CloudFormation to manage infrastructure","Using traffic dial allows gradual shift of traffic between endpoint groups, facilitating Blue/Green deployments."
"What is the pricing model for AWS Global Accelerator?","Hourly fee plus data transfer out charges","Pay-per-request","Pay-per-connection","Free tier available","The pricing model for Global Accelerator is based on an hourly fee plus data transfer out charges."
"What is the maximum number of static IP addresses you can associate with an AWS Global Accelerator?","Two","One","Three","Four","An Accelerator in AWS Global Accelerator is associated with two static IP addresses."
"You are using AWS Global Accelerator and want to ensure that traffic is only routed to endpoints within a specific geographic region. How can you achieve this?","By creating an endpoint group in that region","By using a Network ACL","By using a security group","By using a route table","Creating an endpoint group in a specific region ensures that traffic is only routed to endpoints within that region."
"How does AWS Global Accelerator integrate with AWS Shield?","Global Accelerator provides automatic DDoS protection via AWS Shield Standard","Global Accelerator requires a separate AWS Shield Advanced subscription","Global Accelerator disables AWS Shield","Global Accelerator only integrates with AWS Shield for HTTP traffic","Global Accelerator provides automatic DDoS protection via AWS Shield Standard, enhancing the security of your applications."
"What is the role of the AWS Global Accelerator Traffic Dial?","To control the percentage of traffic routed to an endpoint group","To control the maximum number of connections to an endpoint","To monitor the health of endpoints","To encrypt traffic between the accelerator and the endpoints","The Traffic Dial is used to control the percentage of traffic routed to an endpoint group, allowing for gradual rollouts and testing."
"You need to implement a solution that provides both content caching and global traffic acceleration. Which combination of AWS services is most suitable?","Amazon CloudFront and AWS Global Accelerator","Amazon S3 and AWS Global Accelerator","Amazon EC2 and AWS Global Accelerator","Amazon RDS and AWS Global Accelerator","Combining Amazon CloudFront for content caching and AWS Global Accelerator for global traffic acceleration provides a comprehensive solution for optimising application performance."
"What is the recommended method for configuring AWS Global Accelerator for an application that uses dynamic IP addresses?","Use an Application Load Balancer (ALB) or Network Load Balancer (NLB) as an endpoint","Use an EC2 instance with an Elastic IP address","Use a Lambda function as an endpoint","Use an S3 bucket as an endpoint","Using an ALB or NLB as an endpoint is the recommended method because they can handle dynamic IP addresses and perform health checks."
"Which AWS service should you use if you need to route traffic based on DNS names in addition to accelerating TCP/UDP traffic?","Amazon Route 53 with AWS Global Accelerator","AWS Global Accelerator alone","Amazon CloudFront alone","AWS Direct Connect alone","Use Route 53 in conjunction with Global Accelerator to route traffic based on DNS names, providing more flexibility in traffic management."
"What is the primary difference between AWS Global Accelerator and AWS Transit Gateway?","Global Accelerator optimises traffic routing to applications, while Transit Gateway connects VPCs","Global Accelerator caches content, while Transit Gateway routes network traffic","Global Accelerator provides DDoS protection, while Transit Gateway provides network segmentation","Global Accelerator is used for storage, while Transit Gateway is used for compute","Global Accelerator optimises traffic routing to applications, while Transit Gateway connects VPCs and on-premises networks."
"You have a globally distributed application that requires both HTTP and TCP traffic acceleration. Which AWS services should you use?","AWS Global Accelerator and Amazon CloudFront","Amazon Route 53 and AWS Direct Connect","AWS Direct Connect and AWS Global Accelerator","Amazon S3 and Amazon CloudFront","Using Global Accelerator for TCP acceleration and CloudFront for HTTP caching provides a comprehensive solution for both types of traffic."
"What is the purpose of the 'Client Affinity' setting in AWS Global Accelerator?","To ensure that a user is consistently routed to the same endpoint","To ensure that traffic is evenly distributed across all endpoints","To prioritise traffic based on user location","To encrypt traffic based on user identity","Client Affinity ensures that a user is consistently routed to the same endpoint, which is useful for stateful applications."
"You are using AWS Global Accelerator and notice that traffic is not being evenly distributed across your endpoints. What could be the cause?","Endpoint health checks are failing","Incorrect weight configuration for endpoint groups","Firewall rules are blocking traffic","DNS resolution issues","If endpoint health checks are failing or the weights are misconfigured the distribution will not be even."
"How can you ensure that your AWS Global Accelerator setup is highly available?","By deploying endpoints in multiple AWS regions","By using a single EC2 instance as an endpoint","By using a single Network Load Balancer as an endpoint","By configuring a single static IP address","Deploying endpoints in multiple AWS regions ensures high availability by providing redundancy in case of an outage."
"What is a common use case for AWS Global Accelerator in the context of real-time communication applications?","Reducing latency and improving connection reliability","Caching static content","Storing user profiles","Transcoding video streams","Real-time communication applications such as video conferencing and VoIP benefit from Global Accelerator's reduced latency and improved connection reliability."
"Which of the following AWS services is required as a minimum to use AWS Global Accelerator?","Virtual Private Cloud (VPC)","Elastic Compute Cloud (EC2)","Simple Storage Service (S3)","Identity and Access Management (IAM)","At a minimum, a Virtual Private Cloud (VPC) is required as a basic network setup in AWS to use AWS Global Accelerator."
"You are migrating an existing application to AWS and want to improve its global performance. Which AWS service should you consider using?","AWS Global Accelerator","Amazon S3 Transfer Acceleration","Amazon CloudFront","AWS Direct Connect","AWS Global Accelerator improves global performance by optimising traffic routing and providing static IP addresses."
"How does AWS Global Accelerator simplify the management of IP addresses for your application?","By providing static IP addresses that remain constant even when endpoints change","By automatically rotating IP addresses for security","By using dynamic IP addresses that are automatically assigned","By requiring you to manage IP addresses manually","Global Accelerator provides static IP addresses that remain constant, making it easier to manage and whitelist IP addresses."
"Which CloudWatch metric is most useful for monitoring the health of endpoints in AWS Global Accelerator?","HealthCheckSuccessPercent","CPUUtilization","NetworkPacketsIn","DiskQueueDepth","HealthCheckSuccessPercent provides insights into the success rate of health checks performed on endpoints."
"What happens when you disable an endpoint group in AWS Global Accelerator?","Traffic is rerouted to other healthy endpoint groups","Traffic is dropped","Traffic is queued until the endpoint group is re-enabled","Traffic is automatically shifted to a backup region","When you disable an endpoint group, traffic is rerouted to other healthy endpoint groups, ensuring continuous availability."
"Which of the following is NOT a supported endpoint type for AWS Global Accelerator?","AWS Lambda function","Application Load Balancer","Network Load Balancer","EC2 Instance","AWS Lambda functions are not directly supported as endpoints for AWS Global Accelerator. You would typically put a load balancer in front of the Lambda to work around this."
"You are configuring AWS Global Accelerator for an application that has strict compliance requirements. Which feature can help you meet these requirements?","Static IP addresses for predictable network access","Automatic scaling for handling traffic spikes","Content caching for reducing latency","DNSSEC support for secure DNS resolution","Static IP addresses in Global Accelerator provide predictable network access, which is important for compliance requirements such as whitelisting."
"How can you use AWS Global Accelerator to test new versions of your application before releasing them to all users?","By using Traffic Dial to gradually shift traffic to new endpoint groups","By using Route 53 to perform weighted routing","By using CloudFront to cache content","By using Direct Connect to establish a private connection","By using Traffic Dial, you can gradually shift traffic to new endpoint groups containing the new version of your application, allowing you to test it before releasing it to all users."
"In AWS Global Accelerator, what is the purpose of setting a 'Traffic Dial' value of 0% for an endpoint group?","To completely stop traffic from being routed to the endpoint group","To reduce traffic to the endpoint group during peak hours","To prioritise traffic to other endpoint groups","To increase the health check frequency for the endpoint group","Setting a Traffic Dial value of 0% will completely stop traffic from being routed to that endpoint group which can be used for temporarily disabling it."
"When using AWS Global Accelerator, what is the effect of enabling 'Preserve Client IP Addresses' on an Application Load Balancer (ALB) endpoint?","It allows the ALB to see the original client IP address","It encrypts the traffic between the Accelerator and the ALB","It compresses the traffic between the Accelerator and the ALB","It allows the ALB to block certain IP addresses","Enabling 'Preserve Client IP Addresses' allows the ALB to see the original client IP address, which is important for logging and security purposes."
"What is the primary purpose of AWS Global Accelerator?","To improve application availability and performance for global users","To provide a content delivery network (CDN) service","To provide a managed DNS service","To provide a fully managed database service","AWS Global Accelerator is designed to improve application availability and performance for a global audience by directing traffic to the optimal endpoint."
"With AWS Global Accelerator, what are the static IP addresses provided for?","To serve as a fixed entry point to your application","To restrict traffic to specific geographic locations","To encrypt data in transit","To provide dynamic routing capabilities","The static IP addresses provided by Global Accelerator serve as a fixed entry point to your application, making it easier for users to connect regardless of endpoint changes."
"How does AWS Global Accelerator improve application availability?","By rerouting traffic to healthy endpoints in different AWS Regions","By providing automatic scaling for EC2 instances","By providing a global load balancing solution for databases","By providing enhanced DDoS protection at the edge","Global Accelerator improves availability by monitoring the health of your application endpoints and rerouting traffic to healthy endpoints in different AWS Regions if necessary."
"What type of endpoints can be used with AWS Global Accelerator?","Network Load Balancers, Application Load Balancers, EC2 Instances and Elastic IPs","Only Application Load Balancers","Only EC2 Instances","Only Elastic IPs","Global Accelerator supports Network Load Balancers, Application Load Balancers, EC2 Instances and Elastic IPs as endpoints."
"What is the traffic engineering feature of AWS Global Accelerator primarily used for?","To direct user traffic to the closest healthy endpoint","To encrypt traffic between users and endpoints","To filter malicious traffic","To compress data for faster transfer","The traffic engineering feature of Global Accelerator intelligently directs user traffic to the closest healthy endpoint based on location, health, and configuration."
"What is the name of the component in AWS Global Accelerator which directs traffic to the optimal endpoint?","Accelerator","Endpoint Group","Listener","Rule","The Accelerator is the main component that directs traffic to the optimal endpoint based on health and location."
"In AWS Global Accelerator, what does an Endpoint Group represent?","A collection of endpoints within a specific AWS Region","A security group that controls access to the accelerator","A set of rules for routing traffic","A group of users who can access the application","An Endpoint Group represents a collection of endpoints (e.g., NLBs, ALBs, EC2 instances) within a specific AWS Region."
"How does AWS Global Accelerator help with failover scenarios?","By automatically rerouting traffic to healthy endpoints in another region","By automatically backing up data to another region","By automatically scaling up EC2 instances","By automatically applying security patches","Global Accelerator automatically reroutes traffic to healthy endpoints in another region if an endpoint in the primary region becomes unavailable, ensuring high availability."
"What is the benefit of using static IP addresses provided by AWS Global Accelerator compared to using dynamic DNS?","They provide a fixed entry point that doesn't change, even if your endpoints change","They automatically update DNS records with the latest IP address","They offer lower latency than dynamic DNS","They offer better security features than dynamic DNS","Static IP addresses offer a fixed entry point to your application, which doesn't change even if your endpoints change, simplifying connectivity and improving reliability."
"Which AWS service does AWS Global Accelerator primarily integrate with to route traffic to applications?","Elastic Load Balancing (ELB)","Amazon Route 53","AWS CloudFront","AWS Lambda","Global Accelerator integrates with Elastic Load Balancing (ELB) services, such as Application Load Balancers and Network Load Balancers, to route traffic to your applications."
"How does AWS Global Accelerator minimise latency for users?","By directing traffic through AWS's global network to the closest endpoint","By caching content closer to users","By compressing data before sending it","By optimising database queries","Global Accelerator minimises latency by leveraging AWS's global network to direct traffic through the most optimal path to the closest healthy endpoint."
"What is the term for the percentage of traffic that an endpoint group is eligible to receive in AWS Global Accelerator?","Traffic Dial","Weight","Load Factor","Endpoint Share","Traffic Dial refers to the percentage of traffic that an endpoint group is eligible to receive. Adjusting this allows you to control traffic distribution."
"What type of traffic does AWS Global Accelerator support?","TCP and UDP","Only HTTP traffic","Only HTTPS traffic","Only TCP traffic","Global Accelerator supports both TCP and UDP traffic, making it suitable for a wide range of applications."
"When configuring AWS Global Accelerator, what is the purpose of a Listener?","To process incoming connections based on port and protocol","To define the health check configuration for endpoints","To define the security policies for the accelerator","To monitor the performance of the accelerator","A Listener processes incoming connections based on the port and protocol you configure, defining how traffic is accepted by the accelerator."
"What is the key difference between AWS Global Accelerator and AWS CloudFront?","Global Accelerator optimises traffic to application endpoints, while CloudFront caches content closer to users","Global Accelerator is designed for static content, while CloudFront is designed for dynamic content","Global Accelerator only works with HTTP traffic, while CloudFront works with all types of traffic","Global Accelerator provides DDoS protection, while CloudFront does not","Global Accelerator focuses on optimising traffic to application endpoints across AWS Regions, while CloudFront is a CDN designed to cache content closer to users for faster delivery."
"What does AWS Global Accelerator use to determine the best endpoint for a user's traffic?","AWS's global network and traffic patterns","User's DNS server location","User's IP address alone","A random selection of available endpoints","Global Accelerator uses AWS's global network and traffic patterns to determine the optimal endpoint for a user's traffic, taking into account factors like location, health, and network conditions."
"How can you update the endpoints associated with an AWS Global Accelerator?","By modifying the Endpoint Group configuration","By creating a new accelerator","By modifying the Listener configuration","By manually updating DNS records","You update the endpoints associated with a Global Accelerator by modifying the Endpoint Group configuration, adding or removing endpoints as needed."
"In AWS Global Accelerator, what happens if all endpoints in an Endpoint Group become unhealthy?","Traffic is routed to endpoints in another region if available","The accelerator temporarily stops forwarding traffic","Traffic is randomly distributed to any available endpoint","The service automatically creates new endpoints","If all endpoints in an Endpoint Group become unhealthy, Global Accelerator will route traffic to endpoints in another region if a failover configuration is in place."
"What is the cost model for AWS Global Accelerator?","Hourly fee plus data transfer charges","Fixed monthly fee","Pay-per-request","Free to use with other AWS services","The cost model for Global Accelerator is based on an hourly fee for the accelerator itself, plus data transfer charges for traffic that flows through it."
"What is the minimum number of static IP addresses provided by AWS Global Accelerator?","Two","One","Three","Four","Global Accelerator provides two static IP addresses, ensuring redundancy and high availability."
"Which of the following is NOT a benefit of using AWS Global Accelerator?","Content caching","Improved application availability","Lower latency","Simplified global traffic management","Content caching is not a feature of Global Accelerator, which focuses on traffic routing and endpoint optimisation. Content caching is provided by CloudFront."
"How can you monitor the health of your endpoints in AWS Global Accelerator?","Using CloudWatch metrics and health checks","Using AWS Trusted Advisor","Using AWS Config","Using AWS CloudTrail","You can monitor the health of your endpoints using CloudWatch metrics and configuring health checks within Global Accelerator."
"Can you use AWS Global Accelerator to improve the performance of applications hosted outside of AWS?","Yes, by using Elastic IP addresses as endpoints","No, it only works with applications hosted in AWS","Yes, by using Network Load Balancers as endpoints","No, it requires direct integration with EC2 instances","Global Accelerator can be used with applications hosted outside of AWS as long as they are reachable via Elastic IP addresses within your AWS environment."
"Which type of load balancer is commonly used as an endpoint for AWS Global Accelerator to handle HTTP/HTTPS traffic?","Application Load Balancer (ALB)","Classic Load Balancer (CLB)","Network Load Balancer (NLB)","Gateway Load Balancer (GWLB)","Application Load Balancers (ALBs) are commonly used as endpoints for Global Accelerator to handle HTTP/HTTPS traffic, providing advanced routing and request inspection capabilities."
"What is the benefit of using AWS Global Accelerator with a Network Load Balancer (NLB)?","Improved TCP and UDP traffic performance","Automatic SSL/TLS certificate management","Content caching","Enhanced DDoS protection for web applications","Using Global Accelerator with an NLB provides improved TCP and UDP traffic performance due to Global Accelerator's intelligent routing and AWS's global network."
"What is the purpose of setting a traffic dial to 0% in AWS Global Accelerator?","To completely stop traffic to an endpoint group for maintenance","To redirect traffic to a backup accelerator","To increase traffic to another endpoint group","To delete the endpoint group","Setting the traffic dial to 0% completely stops traffic to an endpoint group, allowing you to perform maintenance or testing without affecting users."
"How does AWS Global Accelerator contribute to improved security?","By providing static IP addresses which simplifies whitelisting","By providing automatic security patching for EC2 instances","By providing a built-in web application firewall (WAF)","By automatically encrypting data in transit","The static IP addresses provided by Global Accelerator allow you to simplify whitelisting rules in your security groups and firewalls, improving security management."
"What is the maximum number of accelerators that can be created in a single AWS account by default?","20","5","100","Unlimited","By default, you can create up to 20 accelerators in a single AWS account. This limit can be increased by contacting AWS support."
"When should you consider using AWS Global Accelerator instead of AWS CloudFront?","When you need to accelerate dynamic content delivery","When you need content caching at the edge","When you need DDoS protection for static websites","When you need to improve the performance of real-time applications","Global Accelerator is more appropriate for accelerating dynamic content delivery and improving the performance of real-time applications, while CloudFront is primarily used for content caching at the edge."
"Which protocol is preferred to make full use of AWS Global Accelerator performance improvements?","TCP","HTTP","HTTPS","UDP","TCP is preferred because it benefits most from the connection optimisations offered by Global Accelerator."
"What is the main advantage of using AWS Global Accelerator for gaming applications?","Reduced latency and improved user experience","Content caching for game assets","Automatic scaling of game servers","Protection against cheating","Reduced latency and improved user experience are the primary advantages of using Global Accelerator for gaming applications, ensuring smoother gameplay for players worldwide."
"What does the term 'Anycast' refer to in the context of AWS Global Accelerator?","The routing technique used to direct traffic to the nearest endpoint","The load balancing algorithm used by the accelerator","The security protocol used to encrypt traffic","The method of distributing content across multiple edge locations","Anycast is the routing technique used by Global Accelerator to direct traffic to the nearest and healthiest endpoint, ensuring optimal performance."
"You want to ensure that traffic from a specific region is always routed to a specific endpoint group. How can you achieve this with AWS Global Accelerator?","By configuring custom routing policies in the Endpoint Group","By using geo-location routing in Route 53","By creating separate accelerators for each region","By using Lambda@Edge","You can achieve this by configuring custom routing policies in the Endpoint Group within Global Accelerator, allowing you to specify which regions should be routed to specific endpoints."
"Which health check options are available for AWS Global Accelerator Endpoint Groups?","HTTP, HTTPS, and TCP","ICMP, UDP, and DNS","Only HTTP","Only TCP","Health checks can be configured using HTTP, HTTPS, and TCP protocols to determine the health of endpoints."
"Which AWS service can be used to log the API calls made to AWS Global Accelerator?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS X-Ray","AWS CloudTrail can be used to log the API calls made to Global Accelerator, providing an audit trail for security and compliance purposes."
"How can you integrate AWS Global Accelerator with your existing CI/CD pipeline?","By automating the creation and update of accelerators using AWS CloudFormation or AWS SDKs","By using AWS CodeDeploy to deploy changes to accelerator endpoints","By manually updating the accelerator configuration after each deployment","By using AWS CodePipeline to build and deploy the accelerator configuration","You can integrate Global Accelerator with your CI/CD pipeline by automating the creation and update of accelerators using AWS CloudFormation or AWS SDKs, ensuring consistent and repeatable deployments."
"If you have multiple AWS Regions, how does AWS Global Accelerator determine the best region to route traffic to?","Based on the health and proximity of endpoints in each region","Based on the price of resources in each region","Based on the compliance requirements of each region","Based on the number of users in each region","Global Accelerator determines the best region to route traffic to based on the health and proximity of endpoints in each region, ensuring optimal performance and availability."
"What is the advantage of using AWS Global Accelerator for media streaming applications?","Reduced buffering and improved playback quality","Content caching for video files","Automatic transcoding of video streams","Protection against content piracy","Global Accelerator helps to reduce buffering and improve playback quality for media streaming applications by optimising traffic routing to the closest healthy endpoint."
"You are using AWS Global Accelerator to route traffic to multiple EC2 instances. How can you ensure that traffic is evenly distributed across these instances?","By using a Network Load Balancer as an endpoint group","By manually configuring routing rules in Global Accelerator","By using a custom routing algorithm","By setting a traffic dial for each instance","Using a Network Load Balancer (NLB) as an endpoint group for Global Accelerator ensures that traffic is evenly distributed across the EC2 instances behind the NLB."
"How can you use AWS Global Accelerator to implement a blue/green deployment strategy?","By creating two Endpoint Groups and adjusting the traffic dial to switch between them","By using AWS CodeDeploy to deploy changes to the accelerator configuration","By creating two separate accelerators and switching between them","By using Route 53 weighted records","You can implement a blue/green deployment strategy by creating two Endpoint Groups (one for the blue environment and one for the green environment) and adjusting the traffic dial to switch traffic between them."
"Which of the following statements is true regarding AWS Global Accelerator endpoint weights?","Weights determine the proportion of traffic an endpoint receives within its Endpoint Group","Weights control the overall cost of using the accelerator","Weights determine the geographic region an endpoint serves","Weights control the health check frequency for the endpoint","Endpoint weights determine the proportion of traffic an endpoint receives within its Endpoint Group, allowing you to control traffic distribution among endpoints."
"What is the maximum number of endpoints allowed in a single Endpoint Group in AWS Global Accelerator?","Unlimited, the limit is only on the resources behind those endpoints","10","100","255","The limit is on the resources behind the endpoints. You can have as many endpoints as your underlying infrastructure can support."
"How does AWS Global Accelerator handle traffic during peak hours?","By intelligently routing traffic across multiple regions to prevent congestion","By caching content closer to users during peak hours","By automatically scaling up EC2 instances","By dropping traffic to maintain performance","Global Accelerator handles traffic during peak hours by intelligently routing traffic across multiple regions to prevent congestion and maintain optimal performance."
"Which of the following scenarios is best suited for using AWS Global Accelerator?","A global e-commerce application with users worldwide","A static website hosted in a single AWS Region","A small internal application used by a limited number of users","A development environment with minimal traffic","Global Accelerator is best suited for global e-commerce applications with users worldwide, where low latency and high availability are critical."
"What is the relationship between AWS Global Accelerator and AWS Shield?","Global Accelerator provides standard DDoS protection through AWS Shield Standard, with the option to upgrade to AWS Shield Advanced for enhanced protection","Global Accelerator includes AWS Shield Advanced by default","AWS Shield is not compatible with AWS Global Accelerator","AWS Shield provides the static IP addresses used by Global Accelerator","Global Accelerator provides standard DDoS protection through AWS Shield Standard, with the option to upgrade to AWS Shield Advanced for enhanced protection."
"You need to ensure that your AWS Global Accelerator remains highly available. What steps should you take?","Configure endpoints in multiple AWS Regions and ensure proper health checks are in place","Only use endpoints with high CPU utilisation","Only use Elastic IP endpoints","Disable all health checks","To ensure high availability, configure endpoints in multiple AWS Regions and ensure proper health checks are in place to automatically reroute traffic in case of failures."
"What is the purpose of the 'Client Affinity' setting in AWS Global Accelerator?","To ensure that a user's requests are consistently routed to the same endpoint","To allow clients to choose their preferred endpoint","To optimise traffic routing based on client location","To encrypt traffic between the client and the endpoint","The 'Client Affinity' setting ensures that a user's requests are consistently routed to the same endpoint, providing session persistence."
"How does AWS Global Accelerator improve the reliability of applications with clients that use mobile networks?","By compensating for the packet loss inherent in mobile networks","By caching content closer to mobile users","By automatically optimising the application code for mobile devices","By prioritising traffic from mobile networks","Global Accelerator improves reliability for clients using mobile networks by compensating for packet loss, ensuring a more stable and consistent connection."
"When configuring AWS Global Accelerator, what is the significance of 'Latency-based routing'?","Traffic is routed to the endpoint with the lowest latency for the user","Traffic is routed based on the cost of the endpoint","Traffic is routed randomly","Traffic is routed based on the geographic location of the endpoint","Latency-based routing in Global Accelerator means that traffic is routed to the endpoint with the lowest latency for the user, ensuring optimal performance."
"What is the primary purpose of AWS Global Accelerator?","To improve the availability and performance of your applications for local and global users.","To provide a content delivery network (CDN) service.","To manage AWS Identity and Access Management (IAM) roles.","To monitor the health of EC2 instances.","Global Accelerator uses the AWS global network to direct user traffic to the optimal endpoint based on health, location, and policies, improving application performance and availability."
"What type of addresses does AWS Global Accelerator provide to clients for accessing your application?","Static Anycast IP addresses","Dynamic Elastic IP addresses","Private IP addresses","VPC IP addresses","Global Accelerator provides static Anycast IP addresses, which are announced from multiple AWS edge locations, allowing users to connect to your application from the nearest edge location."
"In AWS Global Accelerator, what is an 'endpoint'?","A resource where your application is hosted, such as an EC2 instance or Network Load Balancer.","A geographic location where Global Accelerator is deployed.","A set of rules for routing traffic.","A security group for controlling access to your application.","An endpoint in Global Accelerator is a resource, such as an EC2 instance or Network Load Balancer, that hosts your application and receives traffic from Global Accelerator."
"What is a 'listener' in the context of AWS Global Accelerator?","A process that checks for connection requests from clients using a specific protocol and port.","A service that monitors the health of your application.","A component that manages the security of your application.","A tool for analysing traffic patterns.","A listener in Global Accelerator processes incoming connection requests based on the protocol (TCP or UDP) and port you configure."
"Which AWS service is commonly used as an endpoint with AWS Global Accelerator to distribute incoming traffic across multiple targets within a region?","Network Load Balancer (NLB)","Application Load Balancer (ALB)","Classic Load Balancer (CLB)","S3 bucket","Global Accelerator integrates well with Network Load Balancers (NLBs) to distribute traffic to multiple targets within a region, enhancing availability and scalability."
"What benefit does AWS Global Accelerator provide in terms of failover?","It provides fast, predictable failover to healthy endpoints in different AWS Regions.","It completely prevents any downtime during endpoint failures.","It automatically scales your application based on traffic demands.","It reduces the cost of running your application.","Global Accelerator provides fast failover to healthy endpoints in different AWS Regions, minimising the impact of endpoint failures on user experience."
"What is the 'traffic dial' feature in AWS Global Accelerator used for?","To control the percentage of traffic directed to different endpoints within a Region.","To adjust the encryption level of traffic.","To change the AWS Region where traffic is routed.","To block specific IP addresses from accessing the application.","The traffic dial feature allows you to adjust the percentage of traffic directed to different endpoints within a Region, enabling gradual rollouts and testing."
"Which of the following protocols are supported by AWS Global Accelerator?","TCP and UDP","HTTP and HTTPS","SMTP and FTP","SSH and Telnet","Global Accelerator supports both TCP and UDP protocols, making it suitable for a wide range of applications."
"What is the advantage of using static IP addresses provided by AWS Global Accelerator over using Elastic IP addresses?","Static IP addresses remain constant even during regional failovers.","Elastic IP addresses are not compatible with load balancers.","Elastic IP addresses are more expensive.","Static IP addresses are easier to configure.","The static IP addresses provided by Global Accelerator remain constant even during regional failovers, ensuring consistent access for users."
"How does AWS Global Accelerator improve the latency of your application?","By routing user traffic through the AWS global network to the endpoint closest to the user.","By caching static content at edge locations.","By compressing data before sending it to the user.","By optimising database queries.","Global Accelerator routes user traffic through the AWS global network to the nearest edge location, reducing latency and improving application performance."
"What is the function of 'Endpoint Groups' in AWS Global Accelerator?","To associate endpoints with specific AWS Regions.","To define security groups for your application.","To manage DNS records for your application.","To create backups of your application.","Endpoint Groups are used to associate endpoints with specific AWS Regions, allowing you to control where your application's traffic is routed."
"Which factor does AWS Global Accelerator use to determine the optimal endpoint for directing user traffic?","Health of the endpoint, geographic location of the user, and configured traffic policies.","The size of the EC2 instance hosting the application.","The number of concurrent users accessing the application.","The type of operating system running on the endpoint.","Global Accelerator uses the health of the endpoint, the geographic location of the user, and configured traffic policies to determine the optimal endpoint for directing traffic."
"In AWS Global Accelerator, what happens if all endpoints in a Region become unhealthy?","Global Accelerator automatically reroutes traffic to healthy endpoints in another Region.","Global Accelerator stops routing traffic until the endpoints become healthy.","Global Accelerator attempts to automatically repair the unhealthy endpoints.","Global Accelerator sends an alert to the administrator.","If all endpoints in a Region become unhealthy, Global Accelerator automatically reroutes traffic to healthy endpoints in another Region, ensuring high availability."
"Which of the following is a use case where AWS Global Accelerator would be most beneficial?","A globally distributed gaming application.","A static website hosted on S3.","A single-region internal application.","A batch processing job running on EC2.","Global Accelerator is most beneficial for globally distributed applications that require low latency and high availability, such as gaming applications."
"What is the relationship between AWS Global Accelerator and AWS CloudFront?","Global Accelerator improves performance for dynamic content, while CloudFront improves performance for static content.","Global Accelerator replaces CloudFront.","CloudFront replaces Global Accelerator.","Global Accelerator and CloudFront are the same service.","Global Accelerator is designed to accelerate dynamic content and improve application availability, while CloudFront is designed to accelerate static content delivery."
"How can you monitor the performance of your AWS Global Accelerator?","Using Amazon CloudWatch metrics.","Using AWS CloudTrail logs.","Using AWS Config rules.","Using AWS Trusted Advisor.","You can monitor the performance of your Global Accelerator using Amazon CloudWatch metrics, which provide insights into traffic patterns, endpoint health, and other key performance indicators."
"Which of the following is a key component of AWS Global Accelerator that allows you to specify which ports and protocols to listen for incoming traffic on?","Listener","Endpoint Group","Accelerator","Traffic Dial","Listeners are the key component that allows you to specify which ports and protocols to listen for incoming traffic on, and then forwards the traffic to the configured endpoint groups."
"What is the purpose of 'client affinity' in AWS Global Accelerator?","To ensure that a user's requests are consistently routed to the same endpoint.","To prioritise traffic from certain clients.","To encrypt traffic between clients and endpoints.","To block traffic from malicious clients.","Client affinity ensures that a user's requests are consistently routed to the same endpoint, which can be important for stateful applications."
"What is the difference between AWS Global Accelerator and AWS Route 53?","Global Accelerator provides static IP addresses and accelerates traffic, while Route 53 is a DNS service.","Global Accelerator is a DNS service, while Route 53 accelerates traffic.","Global Accelerator and Route 53 are the same service.","Global Accelerator is used for static content, while Route 53 is used for dynamic content.","Global Accelerator provides static IP addresses and accelerates traffic to endpoints, while Route 53 is a Domain Name System (DNS) service that translates domain names into IP addresses."
"Which of the following AWS services can be used as an endpoint for AWS Global Accelerator?","Amazon EC2 instances, Network Load Balancers (NLBs), and Application Load Balancers (ALBs).","Amazon S3 buckets.","Amazon RDS databases.","Amazon DynamoDB tables.","Amazon EC2 instances, Network Load Balancers (NLBs), and Application Load Balancers (ALBs) can be used as endpoints for AWS Global Accelerator."
"How does AWS Global Accelerator help with application availability during a regional outage?","By automatically routing traffic to healthy endpoints in other regions.","By automatically restarting the application in the affected region.","By automatically backing up data to another region.","By automatically scaling the application to handle increased traffic.","Global Accelerator automatically routes traffic to healthy endpoints in other regions during a regional outage, ensuring high availability for your application."
"What is the primary benefit of using AWS Global Accelerator for a real-time communication application (e.g., video conferencing)?","Reduced latency and improved reliability.","Lower cost compared to other AWS services.","Increased security for communication channels.","Simplified configuration for network routing.","The primary benefit of using Global Accelerator for real-time communication applications is reduced latency and improved reliability by routing traffic through the AWS global network."
"When configuring AWS Global Accelerator, what is the 'weight' setting in an endpoint group used for?","To determine the proportion of traffic that is directed to each endpoint in the group.","To specify the geographic location of the endpoint.","To define the security group for the endpoint.","To set the maximum number of connections allowed to the endpoint.","The 'weight' setting in an endpoint group is used to determine the proportion of traffic that is directed to each endpoint in the group, allowing for load balancing and traffic shaping."
"What is the role of the AWS Global Accelerator 'Accelerator' resource?","It represents the Global Accelerator instance itself, providing the static IP addresses.","It manages the health checks for endpoints.","It defines the security policies for the application.","It monitors the traffic patterns for the application.","The 'Accelerator' resource represents the Global Accelerator instance itself and provides the static IP addresses that clients use to connect to your application."
"Which AWS CLI command is used to create a new AWS Global Accelerator?","aws globalaccelerator create-accelerator","aws ec2 create-global-accelerator","aws accelerator create","aws network create-global-accelerator","The `aws globalaccelerator create-accelerator` command is used to create a new AWS Global Accelerator."
"What is the purpose of health checks in AWS Global Accelerator?","To ensure that traffic is only routed to healthy endpoints.","To encrypt traffic between clients and endpoints.","To monitor the CPU utilisation of endpoints.","To balance the load across multiple endpoints.","Health checks in Global Accelerator ensure that traffic is only routed to healthy endpoints, improving application availability and user experience."
"How does AWS Global Accelerator interact with AWS Shield?","Global Accelerator provides enhanced DDoS protection in conjunction with AWS Shield.","Global Accelerator replaces AWS Shield.","AWS Shield replaces Global Accelerator.","Global Accelerator is not compatible with AWS Shield.","Global Accelerator provides enhanced DDoS protection in conjunction with AWS Shield, helping to protect your application from malicious traffic."
"Which of the following is a limitation of AWS Global Accelerator?","It only supports TCP and UDP protocols.","It cannot be used with on-premises infrastructure.","It is only available in certain AWS Regions.","It requires a dedicated internet connection.","A limitation of Global Accelerator is that it only supports TCP and UDP protocols, which may not be suitable for all types of applications."
"How can you integrate AWS Global Accelerator with your existing DNS infrastructure?","By creating an ALIAS record in your DNS that points to the Global Accelerator's static IP addresses.","By migrating your DNS records to AWS Route 53.","By configuring Global Accelerator to use your existing DNS servers.","By using a custom DNS resolver in Global Accelerator.","You can integrate Global Accelerator with your existing DNS infrastructure by creating an ALIAS record in your DNS that points to the Global Accelerator's static IP addresses."
"What is the cost model for AWS Global Accelerator?","You pay for the number of accelerator hours and the amount of data transferred.","You pay a fixed monthly fee.","You pay based on the number of endpoints.","You pay based on the number of listeners.","The cost model for Global Accelerator is based on the number of accelerator hours and the amount of data transferred through the accelerator."
"When should you consider using AWS Global Accelerator instead of AWS CloudFront?","When your application requires low latency for dynamic, real-time traffic.","When you need to cache static content at edge locations.","When you need to manage DNS records.","When you need to secure your application with a web application firewall.","You should consider using Global Accelerator when your application requires low latency for dynamic, real-time traffic, as it optimises the path between users and your endpoints."
"What type of traffic does AWS Global Accelerator primarily accelerate?","Dynamic traffic","Static content","Email traffic","Database queries","AWS Global Accelerator primarily accelerates dynamic traffic by routing it over the AWS global network to the nearest endpoint."
"What is the purpose of the 'Update Accelerator Attributes' action in AWS Global Accelerator?","To modify the description or name of the accelerator.","To add or remove endpoints.","To change the protocol of the listener.","To update the health check settings.","The 'Update Accelerator Attributes' action allows you to modify the description or name of the accelerator, providing a way to update its metadata."
"In AWS Global Accelerator, what does 'propagation delay' refer to?","The time it takes for changes to the accelerator configuration to propagate to the AWS global network.","The delay in routing traffic to the optimal endpoint.","The time it takes for health checks to complete.","The delay in establishing a connection between the client and the endpoint.","'Propagation delay' refers to the time it takes for changes to the accelerator configuration to propagate to the AWS global network and become effective."
"Which of the following is a best practice for configuring AWS Global Accelerator?","Distribute your endpoints across multiple AWS Regions for high availability.","Use a single endpoint for all traffic to minimise costs.","Disable health checks to improve performance.","Use a large number of listeners to handle different types of traffic.","Distributing your endpoints across multiple AWS Regions is a best practice for configuring Global Accelerator, ensuring high availability and resilience."
"What is the benefit of using AWS Global Accelerator with Network Load Balancers (NLBs)?","Global Accelerator provides static IP addresses for the NLBs and improves traffic routing.","NLBs provide static IP addresses for Global Accelerator.","Global Accelerator and NLBs perform the same function.","Global Accelerator is not compatible with NLBs.","Global Accelerator provides static IP addresses for the NLBs and improves traffic routing through the AWS global network, enhancing performance and availability."
"What feature in AWS Global Accelerator allows you to test new versions of your application by gradually shifting traffic to different endpoints?","Traffic dial","Health checks","Client affinity","Endpoint groups","The traffic dial feature allows you to test new versions of your application by gradually shifting traffic to different endpoints, enabling controlled rollouts."
"Which AWS service can be used to automate the deployment and configuration of AWS Global Accelerator?","AWS CloudFormation","AWS Systems Manager","AWS Config","AWS CloudTrail","AWS CloudFormation can be used to automate the deployment and configuration of AWS Global Accelerator, allowing you to manage your infrastructure as code."
"What security benefit does AWS Global Accelerator provide?","It helps to mask the origin of your application servers.","It encrypts traffic between clients and endpoints.","It automatically blocks malicious traffic.","It manages user access control policies.","Global Accelerator helps to mask the origin of your application servers, making it more difficult for attackers to target your infrastructure directly."
"What is the primary use case for associating an AWS Global Accelerator with a single AWS Region?","To improve application performance and availability within that region.","To reduce the cost of using Global Accelerator.","To simplify the configuration of Global Accelerator.","To comply with regional data residency requirements.","While Global Accelerator is primarily designed for multi-region deployments, associating it with a single AWS Region can still improve application performance and availability within that region by utilising the AWS global network."
"How does AWS Global Accelerator handle TCP connections?","It uses TCP proxying to establish connections between clients and endpoints.","It converts TCP connections to UDP connections.","It terminates TCP connections at the edge locations.","It bypasses TCP connections and uses a proprietary protocol.","Global Accelerator uses TCP proxying to establish connections between clients and endpoints, optimising the path and improving performance."
"What is the maximum number of static IP addresses that can be associated with a single AWS Global Accelerator?","Two","One","Four","Unlimited","A single AWS Global Accelerator can be associated with two static IP addresses by default, providing redundancy and high availability."
"You need to ensure that traffic from a specific client is always routed to the same endpoint for session persistence. How can you achieve this with AWS Global Accelerator?","Enable client affinity on the listener.","Configure endpoint weights.","Use health checks to monitor endpoint availability.","Create multiple accelerators for each client.","Enabling client affinity on the listener ensures that traffic from a specific client is always routed to the same endpoint for session persistence."
"Which component of AWS Global Accelerator is responsible for monitoring the health of your application endpoints?","Health Checks","Listeners","Accelerators","Endpoint Groups","Health Checks are responsible for monitoring the health of your application endpoints, ensuring that traffic is only routed to healthy resources."
"What is the key benefit of using Anycast IP addresses with AWS Global Accelerator?","They allow users to connect to the nearest AWS edge location, reducing latency.","They provide enhanced security for your application.","They simplify the configuration of network routing.","They reduce the cost of using Global Accelerator.","Anycast IP addresses allow users to connect to the nearest AWS edge location, reducing latency and improving the overall user experience."
"When using AWS Global Accelerator with multiple endpoint groups, how is traffic distributed between the groups?","Based on the traffic dial setting for each endpoint group.","Based on the health of the endpoints within each group.","Based on the geographic location of the users.","Based on the number of endpoints in each group.","Traffic is distributed between endpoint groups based on the traffic dial setting for each endpoint group, allowing you to control the proportion of traffic directed to each region."
"Which AWS Global Accelerator feature is most useful for A/B testing new application features?","Traffic dial","Health checks","Client affinity","Endpoint groups","The traffic dial feature is most useful for A/B testing new application features, as it allows you to gradually shift traffic to different endpoints."
"What is a key factor to consider when choosing between AWS Global Accelerator and AWS Transit Gateway for network connectivity?","Global Accelerator is designed for accelerating application traffic, while Transit Gateway is designed for connecting VPCs.","Global Accelerator is designed for connecting VPCs, while Transit Gateway is designed for accelerating application traffic.","Global Accelerator and Transit Gateway perform the same function.","Global Accelerator is more cost-effective than Transit Gateway.","Global Accelerator is designed for accelerating application traffic and providing static IP addresses, while Transit Gateway is designed for connecting VPCs and on-premises networks."
"Which of the following is an advantage of using AWS Global Accelerator with containerised applications running on Amazon ECS or EKS?","Improved application performance and availability for global users.","Simplified deployment and management of containers.","Reduced cost of running containerised applications.","Increased security for containerised applications.","Using Global Accelerator with containerised applications running on ECS or EKS improves application performance and availability for global users by optimising traffic routing."
"What is the primary function of AWS Global Accelerator?","To improve the availability and performance of applications for global users.","To provide a content delivery network.","To manage DNS records.","To provide a firewall service.","AWS Global Accelerator improves the availability and performance of applications by directing traffic to optimal endpoints around the world."
"In AWS Global Accelerator, what is an Endpoint?","The destination for traffic, such as an EC2 instance or Application Load Balancer.","A regional edge location used for caching content.","A virtual private cloud (VPC).","A route table entry.","An Endpoint in Global Accelerator represents the actual destination for user traffic."
"What type of addresses does AWS Global Accelerator provide for your application?","Static, anycast IP addresses","Dynamic, unicast IP addresses","Ephemeral, broadcast IP addresses","Regional IP addresses","Global Accelerator provides static, anycast IP addresses that serve as a fixed entry point to your application."
"Which AWS service is typically used as an endpoint for AWS Global Accelerator to distribute traffic across multiple instances?","Application Load Balancer (ALB)","Simple Queue Service (SQS)","Simple Notification Service (SNS)","Elastic Beanstalk","Application Load Balancers are commonly used as endpoints for Global Accelerator to distribute traffic to multiple backend instances."
"What is the benefit of using AWS Global Accelerator over directly exposing your Application Load Balancer (ALB) to the internet?","Global Accelerator provides improved global performance and resilience.","ALBs cannot be directly exposed to the internet.","ALB is more expensive.","Global Accelerator provides better security features.","Global Accelerator provides performance benefits with its static anycast IPs and intelligent traffic routing."
"Which traffic policy setting in AWS Global Accelerator allows you to control the percentage of traffic directed to different endpoints?","Weight","Priority","Affinity","Health Check","The 'Weight' setting in Global Accelerator allows you to define the percentage of traffic directed to each endpoint."
"What does the term 'anycast' refer to in the context of AWS Global Accelerator?","The ability of Global Accelerator to announce the same IP address from multiple AWS edge locations.","The ability of Global Accelerator to only route TCP traffic.","The ability of Global Accelerator to filter malicious traffic.","The ability of Global Accelerator to cache content at the edge.","Anycast enables Global Accelerator to announce the same IP address from multiple edge locations, routing users to the nearest healthy endpoint."
"How does AWS Global Accelerator improve fault tolerance?","By automatically rerouting traffic to healthy endpoints in different AWS Regions.","By providing a built-in DDoS protection service.","By encrypting all traffic.","By creating automatic backups of your application.","Global Accelerator improves fault tolerance by automatically rerouting traffic to healthy endpoints in other AWS Regions if an endpoint fails."
"Which type of application would benefit most from using AWS Global Accelerator?","A globally distributed, latency-sensitive application.","A static website hosted on S3.","An application with only local users.","A batch processing job.","Global Accelerator is designed for globally distributed, latency-sensitive applications that require high availability and performance."
"What is the function of health checks in AWS Global Accelerator?","To monitor the health of endpoints and automatically remove unhealthy endpoints from traffic distribution.","To monitor the health of the Global Accelerator service itself.","To provide security vulnerability scans for endpoints.","To ensure that all traffic is encrypted.","Health checks in Global Accelerator ensure that only healthy endpoints receive traffic, improving application availability."
"What happens to traffic if all endpoints in a particular AWS Region become unhealthy when using AWS Global Accelerator?","Global Accelerator automatically reroutes traffic to healthy endpoints in other AWS Regions.","Global Accelerator stops routing traffic to the application.","Global Accelerator queues the traffic until the endpoints recover.","Global Accelerator returns an error to the user.","If all endpoints in one region are unhealthy, Global Accelerator will automatically reroute traffic to healthy endpoints in other regions, maintaining application availability."
"Which protocol does AWS Global Accelerator use for traffic routing?","TCP and UDP","HTTP","SMTP","FTP","AWS Global Accelerator supports both TCP and UDP protocols for traffic routing."
"Which AWS service integrates with AWS Global Accelerator to provide content caching capabilities?","CloudFront","S3","Lambda","Elasticache","While Global Accelerator focuses on routing, CloudFront provides content caching. They work together for comprehensive delivery solutions."
"How does AWS Global Accelerator handle regional failover?","Automatically reroutes traffic to healthy endpoints in other regions.","Requires manual configuration to reroute traffic.","Stops routing traffic until the failed region recovers.","Displays an error page to users.","Global Accelerator automatically handles regional failover by rerouting traffic to available and healthy endpoints in other regions."
"What is the main pricing component for AWS Global Accelerator?","Hourly fee plus data transfer usage","Fixed monthly fee","Pay-per-request","Free tier","Global Accelerator charges an hourly fee for using the accelerator plus a charge based on the amount of data transferred."
"Can AWS Global Accelerator be used with on-premises infrastructure?","Yes, by using public IP addresses of the on-premises endpoints.","No, it only supports AWS resources.","Yes, using AWS Direct Connect only.","Yes, using AWS VPN only.","Global Accelerator can be used with on-premises infrastructure if the on-premises resources have public IP addresses."
"What is the 'Client affinity' feature in AWS Global Accelerator used for?","To ensure that a user's traffic is consistently routed to the same endpoint.","To prioritise traffic from certain clients.","To block traffic from malicious clients.","To provide discounted pricing for certain clients.","Client affinity ensures that traffic from a specific client is always routed to the same endpoint."
"Which of the following is a limitation of AWS Global Accelerator?","It does not support UDP traffic for custom routing.","It requires all endpoints to be in the same AWS region.","It cannot be used with Application Load Balancers.","It doesn't automatically scale endpoints.","AWS Global Accelerator does not support UDP traffic for custom routing."
"How do you configure custom routing for UDP traffic in AWS Global Accelerator?","It's not possible to configure custom routing for UDP traffic.","Using traffic policies.","By setting weights on endpoint groups.","By creating UDP load balancers.","Global Accelerator doesn't support UDP traffic for custom routing."
"You need to ensure that your globally distributed web application has a consistent entry point for users, even during regional failures. Which AWS service should you use?","AWS Global Accelerator","Amazon Route 53","AWS CloudFront","AWS WAF","AWS Global Accelerator provides static anycast IP addresses that serve as a consistent entry point, even during regional failures."
"When configuring AWS Global Accelerator, what is an 'Endpoint Group'?","A collection of endpoints in a specific AWS Region.","A collection of users who access the application.","A collection of security groups.","A collection of CloudWatch metrics.","An Endpoint Group represents a set of endpoints located in a particular AWS Region."
"What is the purpose of setting a 'Listener' in AWS Global Accelerator?","To define the ports and protocols that Global Accelerator accepts traffic on.","To monitor the health of endpoints.","To create security groups.","To define the routing policy.","Listeners define the ports and protocols that Global Accelerator accepts traffic on, acting as the entry point for incoming requests."
"Which AWS service can be used to monitor the performance and health of your AWS Global Accelerator?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch can be used to monitor the performance and health of your Global Accelerator, providing insights into traffic and endpoint status."
"What is the key difference between AWS Global Accelerator and AWS CloudFront?","Global Accelerator improves performance for both HTTP and non-HTTP traffic, while CloudFront is primarily for caching HTTP content.","Global Accelerator is only for static content, while CloudFront is for dynamic content.","Global Accelerator is more expensive than CloudFront.","Global Accelerator uses edge locations while CloudFront uses regional edge caches.","Global Accelerator focuses on optimising the path to endpoints, while CloudFront is a CDN caching content at edge locations."
"What happens to the static IP addresses provided by AWS Global Accelerator when you delete the accelerator?","The IP addresses are released and become available for use by other AWS customers.","The IP addresses are reserved for your account indefinitely.","The IP addresses are transferred to your AWS account.","The IP addresses are moved to a new Global Accelerator.","When an Accelerator is deleted, the associated static IP addresses are released and become available for other AWS customers."
"You want to route different percentages of traffic to two different endpoint groups based on a weighting scheme. Which AWS Global Accelerator feature should you use?","Traffic dial","Endpoint priority","Client affinity","Health checks","Traffic dial feature allows configuring a percentage of traffic routed to different endpoint groups."
"What type of DNS records are associated with AWS Global Accelerator's static IP addresses?","A records","CNAME records","MX records","TXT records","AWS Global Accelerator provides static IP addresses and therefore uses A records."
"Which of the following is a valid use case for Client IP address preservation when using AWS Global Accelerator?","When you want to track the source IP address of incoming requests for security or analytics purposes.","When you want to encrypt the traffic between the client and the application.","When you want to use custom DNS servers.","When you want to cache the content at edge locations.","Client IP address preservation is important for tracking the original source IP address, which is useful for security and analytics purposes."
"You are designing a globally distributed gaming application and need to minimize latency for players. Which AWS service is best suited for this requirement?","AWS Global Accelerator","Amazon S3","Amazon EC2","Amazon RDS","AWS Global Accelerator is ideal for reducing latency for globally distributed applications like gaming, by routing traffic to the nearest healthy endpoint."
"When using AWS Global Accelerator, what is the primary benefit of enabling flow affinity?","Ensures that all packets for a given connection are routed to the same endpoint.","Prioritises TCP connections.","Increases bandwidth for individual connections.","Encrypts all connections.","Flow affinity ensures that all packets for a connection are routed to the same endpoint, improving session persistence and performance."
"What does it mean to 'advertise' an IP address in the context of AWS Global Accelerator?","To make the static IP address reachable from the internet.","To assign the IP address to an EC2 instance.","To register the IP address with a DNS server.","To encrypt the IP address.","Advertising an IP address means making it reachable from the internet, allowing users to connect to your application through the static IP addresses provided by Global Accelerator."
"In AWS Global Accelerator, what happens when an endpoint fails a health check?","Traffic is automatically rerouted to other healthy endpoints in the same region or different regions.","The application becomes unavailable.","An error message is displayed to the user.","Traffic is queued until the endpoint recovers.","When an endpoint fails a health check, Global Accelerator automatically reroutes traffic to other healthy endpoints, ensuring continuous availability."
"Can you use AWS Global Accelerator with resources outside of AWS, such as on-premises servers?","Yes, as long as the resources have public IP addresses.","No, it only works with AWS resources.","Yes, but only through AWS Direct Connect.","Yes, but only through AWS VPN.","Global Accelerator can work with resources outside of AWS, provided they have public IP addresses that can be used as endpoints."
"What is the relationship between AWS Global Accelerator and Elastic Load Balancing (ELB)?","Global Accelerator can use ELBs as endpoints to distribute traffic within a region.","Global Accelerator replaces ELB.","Global Accelerator and ELB are mutually exclusive.","ELB manages Global Accelerator.","Global Accelerator uses ELBs as endpoints to distribute traffic within a specific AWS Region."
"Which AWS service helps you protect your application from common web exploits and bots when using AWS Global Accelerator?","AWS WAF (Web Application Firewall)","AWS Shield","AWS Inspector","AWS Trusted Advisor","AWS WAF helps protect applications from common web exploits and bots, which can be used in conjunction with Global Accelerator."
"What is the primary benefit of using static IP addresses with AWS Global Accelerator?","They provide a consistent entry point for users, regardless of changes in your backend infrastructure.","They are cheaper than dynamic IP addresses.","They provide enhanced encryption.","They automatically scale based on traffic.","Static IP addresses provide a consistent entry point, simplifying DNS configuration and providing resilience against changes in backend infrastructure."
"You want to migrate your application's backend infrastructure without impacting users' access. Which AWS service can help you achieve this with minimal downtime?","AWS Global Accelerator","Amazon EC2 Auto Scaling","AWS CloudFormation","AWS Lambda","AWS Global Accelerator provides a static entry point, allowing you to migrate your backend infrastructure without disrupting user access, as they will continue to use the same IP addresses."
"What does the term 'peering location' refer to in the context of AWS Global Accelerator?","A location where AWS interconnects its network with other networks to improve performance.","A location where you can configure security groups.","A location where you can create VPCs.","A location where you can create endpoints.","A peering location refers to a site where AWS interconnects its network with other networks, enhancing performance for Global Accelerator users."
"You need to configure AWS Global Accelerator to direct traffic to different endpoints based on the client's geographical location. Is this possible with standard Global Accelerator features?","No, Global Accelerator does not support geo-based routing.","Yes, by using traffic policies.","Yes, by configuring endpoint weights.","Yes, by using AWS Lambda@Edge.","Global Accelerator doesn't directly support geo-based routing."
"Which of the following is a benefit of using AWS Global Accelerator with a globally distributed e-commerce application?","Improved page load times and a more responsive user experience.","Reduced storage costs.","Enhanced database performance.","Simplified application deployment.","Global Accelerator improves page load times and the overall user experience by directing users to the nearest healthy endpoint, reducing latency."
"What is the purpose of the AWS Global Accelerator's 'Update Accelerator Attributes' API call?","To modify settings like flow logs configuration.","To add or remove endpoints.","To create new listeners.","To create endpoint groups.","The 'Update Accelerator Attributes' API allows you to modify settings like enabling flow logs for your Global Accelerator."
"How can you ensure that AWS Global Accelerator is routing traffic to the most optimal endpoints?","By using health checks and traffic policies.","By manually configuring DNS records.","By using AWS CloudTrail.","By using AWS Config.","Health checks and traffic policies ensure that Global Accelerator routes traffic to the healthiest and most optimal endpoints."
"In the context of AWS Global Accelerator, what is a 'static anycast IP address'?","An IP address that is advertised from multiple AWS edge locations simultaneously.","An IP address that is only used for static content.","An IP address that is randomly assigned to endpoints.","An IP address that is only accessible from within a VPC.","A static anycast IP address is advertised from multiple AWS edge locations concurrently, providing a single entry point for users globally."
"How does AWS Global Accelerator integrate with AWS Identity and Access Management (IAM)?","IAM is used to control access to Global Accelerator resources and actions.","IAM is used to manage endpoints.","IAM is used to configure traffic policies.","IAM is used to monitor health checks.","IAM is used to control who can access and manage your Global Accelerator resources and actions."
"What is a common use case for enabling flow logs in AWS Global Accelerator?","To capture and analyse traffic patterns for troubleshooting and auditing purposes.","To encrypt all traffic passing through the accelerator.","To automatically scale endpoints based on traffic volume.","To configure health checks.","Flow logs capture traffic patterns, aiding in troubleshooting and auditing network traffic."
"Can you use AWS Global Accelerator to improve the performance of a database server?","No, Global Accelerator is designed for application traffic, not database connections.","Yes, by routing database connections through the accelerator.","Yes, but only with specific database types.","Yes, by caching database queries at edge locations.","Global Accelerator isn't designed for improving the performance of direct database connections but rather for improving application traffic routing."
"What is the maximum number of static IP addresses you can associate with an AWS Global Accelerator?","Two","One","Four","Unlimited","Each Global Accelerator is allocated two static IP addresses."
"What is the primary purpose of AWS Global Accelerator?","To improve application availability and performance for a global audience","To provide a managed DNS service","To offer a content delivery network (CDN)","To manage and automate software deployments","AWS Global Accelerator directs user traffic to optimal endpoints based on health, geography, and configured weights, thereby improving application availability and performance for users worldwide."
"What type of addresses does AWS Global Accelerator use to route traffic?","Static Anycast IP addresses","Dynamic IP addresses","Elastic IP addresses","Private IP addresses","Global Accelerator uses static Anycast IP addresses. These are advertised from multiple AWS edge locations simultaneously, providing fault tolerance and low latency."
"Which of the following is a benefit of using AWS Global Accelerator's static IP addresses?","They provide a fixed entry point to your application regardless of regional failures","They automatically update DNS records","They can be easily changed","They are region-specific","Global Accelerator's static IP addresses provide a consistent, fixed entry point to your application. This means that even if there are regional failures, users can still access the application."
"In AWS Global Accelerator, what is an 'endpoint group'?","A collection of endpoints within a specific AWS region","A group of users with specific access permissions","A set of load balancing rules","A collection of AWS accounts","An endpoint group is a collection of endpoints (such as Application Load Balancers, Network Load Balancers, or EC2 instances) within a specific AWS region. Global Accelerator directs traffic to the optimal endpoint group."
"How does AWS Global Accelerator improve application availability?","By providing automatic failover to healthy endpoints in different regions","By encrypting data in transit","By caching static content","By offering a managed database service","Global Accelerator improves availability by continuously monitoring the health of your application endpoints. If an endpoint fails, Global Accelerator automatically redirects traffic to healthy endpoints in other regions."
"Which of the following AWS services can be used as an endpoint for AWS Global Accelerator?","Network Load Balancer (NLB)","Amazon S3","Amazon EC2 Auto Scaling group","Amazon RDS","AWS Global Accelerator supports Network Load Balancers (NLBs), Application Load Balancers (ALBs), and EC2 instances as endpoints."
"What is the function of traffic dials in AWS Global Accelerator?","To control the percentage of traffic directed to each endpoint group","To automatically scale endpoint resources","To monitor network bandwidth usage","To optimise database query performance","Traffic dials allow you to control the percentage of traffic directed to each endpoint group. This enables you to gradually shift traffic to new deployments or perform A/B testing."
"What is a key advantage of AWS Global Accelerator compared to using only a DNS service for global traffic management?","Global Accelerator provides faster failover and improved performance through static Anycast IPs","DNS services offer better DDoS protection","DNS services are less expensive","DNS services support more endpoint types","Global Accelerator offers faster failover and improved performance because it uses static Anycast IP addresses and continuously monitors endpoint health. DNS relies on TTL values, which can result in slower failover times."
"Can AWS Global Accelerator route traffic to endpoints outside of the AWS network?","No, Global Accelerator only supports endpoints within AWS","Yes, through the use of custom routing configurations","Yes, by integrating with third-party CDNs","Yes, if the endpoint has a public IP address","Global Accelerator is designed to route traffic to endpoints within the AWS network, such as Network Load Balancers, Application Load Balancers, and EC2 instances. It does not directly support routing to endpoints outside of AWS."
"What routing policy can be used to route traffic in AWS Global Accelerator?","Geolocation routing","Weighted routing","Latency-based routing","Static routing","AWS Global Accelerator uses proximity based routing (i.e. the closest one), you can also specify weighted routing between the different accelerator groups so that traffic can be shifted."
"What is the primary function of AWS Global Accelerator?","To improve the availability and performance of applications for global users","To provide a content delivery network (CDN)","To host static websites","To manage DNS records","AWS Global Accelerator improves application availability and performance by directing user traffic to the optimal endpoint based on health, location, and policies."
"Which type of IP address does AWS Global Accelerator use to direct traffic to your application?","Static IP addresses","Dynamic IP addresses","Ephemeral IP addresses","Private IP addresses","Global Accelerator uses static IP addresses, providing a fixed entry point to your application and improving DNS caching."
"What type of endpoints can be used with AWS Global Accelerator?","Network Load Balancers, Application Load Balancers, EC2 Instances and Elastic IPs","Only EC2 Instances","Only Application Load Balancers","Only Network Load Balancers","Global Accelerator supports Network Load Balancers, Application Load Balancers, EC2 Instances and Elastic IPs as endpoints."
"What is the benefit of using AWS Global Accelerator's traffic dial feature?","It allows you to control the percentage of traffic directed to different endpoints.","It allows you to filter traffic based on IP address.","It allows you to encrypt traffic.","It allows you to compress traffic.","The traffic dial feature lets you control the percentage of traffic routed to each endpoint, enabling gradual deployments and A/B testing."
"How does AWS Global Accelerator improve application availability?","By continuously monitoring the health of your endpoints and automatically routing traffic away from unhealthy ones","By automatically scaling your EC2 instances","By backing up your data to multiple regions","By providing a firewall to protect against DDoS attacks","Global Accelerator monitors the health of your endpoints and automatically redirects traffic to healthy endpoints in other regions, improving application availability."
"Which AWS service is commonly used as an endpoint for AWS Global Accelerator to distribute incoming application traffic?","Network Load Balancer (NLB)","AWS Lambda","Amazon S3","Amazon CloudFront","The Network Load Balancer is typically configured to be behind Global Accelerator."
"What does an 'Accelerator' resource represent in AWS Global Accelerator?","A resource that directs traffic to optimal endpoints.","A container for EC2 instances.","A configuration for AWS Lambda functions.","A storage location for static website content.","An 'Accelerator' in Global Accelerator is the resource that directs traffic to your application endpoints via static IP addresses."
"How does AWS Global Accelerator typically reduce latency for users accessing applications?","By routing traffic over the AWS global network","By caching content closer to users","By compressing data before transmission","By using a faster DNS server","Global Accelerator routes traffic over the AWS global network, which is designed for low latency and high performance."
"When would you choose AWS Global Accelerator over Amazon CloudFront?","When you need to accelerate both TCP and UDP traffic and require consistent IP addresses.","When you need to cache static content closer to users.","When you need to protect your application from DDoS attacks.","When you need to analyse web traffic patterns.","Global Accelerator is ideal when you need to accelerate both TCP and UDP traffic for applications that are not primarily content-based and require consistent IP addresses."
"What is the role of an 'Endpoint Group' in AWS Global Accelerator?","To define a group of endpoints in a specific AWS Region.","To define security groups for your application.","To define the caching policy for your content.","To define the load balancing algorithm.","An 'Endpoint Group' in Global Accelerator is a group of endpoints (e.g., NLBs, ALB) in a specific AWS Region that are treated as a single unit for traffic routing."