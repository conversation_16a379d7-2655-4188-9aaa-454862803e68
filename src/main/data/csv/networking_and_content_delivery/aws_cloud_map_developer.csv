"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Cloud Map?","Service discovery","Content delivery","Data warehousing","Compute orchestration","AWS Cloud Map is primarily used for service discovery, allowing applications to find and connect to each other."
"In AWS Cloud Map, what is a 'Namespace'?","A container for service names","A geographical region","A set of IAM permissions","A DNS record type","A Namespace in Cloud Map is a container for service names. It defines the naming scheme and discovery mechanisms for services."
"Which type of namespace in AWS Cloud Map allows you to use your own domain name for service discovery?","Private DNS Namespace","HTTP Namespace","Public DNS Namespace","API Gateway Namespace","A Private DNS Namespace allows you to use your own domain name within your VPC for service discovery."
"When registering an instance with AWS Cloud Map, what information is typically included?","Instance ID and attributes like IP address and port","IAM role ARN","VPC ID only","Subnet mask","When registering an instance, you provide its ID and attributes such as IP address and port so other services can connect."
"What's the function of a 'Service' within AWS Cloud Map?","It represents a discoverable application or resource","It's an AWS account","It's a billing entity","It's a security group","A 'Service' in Cloud Map represents a discoverable application or resource that other services can locate."
"How does AWS Cloud Map integrate with other AWS services for service discovery?","Via DNS and HTTP APIs","Only through CloudWatch metrics","Only through IAM roles","Only through AWS Config rules","Cloud Map integrates with other services via DNS and HTTP APIs, making service discovery accessible from various applications."
"Which AWS service is commonly used with AWS Cloud Map to automatically register and deregister instances?","AWS ECS (Elastic Container Service)","AWS Lambda","AWS SQS (Simple Queue Service)","AWS SNS (Simple Notification Service)","AWS ECS is often used with Cloud Map to automatically register and deregister container instances as they are launched or terminated."
"What benefits does AWS Cloud Map offer over manually managing service discovery?","Centralised service registry and dynamic updates","Lower latency DNS resolution","Automatic cost optimisation","Simplified IAM role management","Cloud Map provides a centralised service registry with dynamic updates, simplifying management and reducing errors compared to manual methods."
"Which protocol can be used to query AWS Cloud Map for service instance information?","DNS and HTTP","SMTP and FTP","SNMP and SSH","TCP and UDP","Cloud Map can be queried using both DNS and HTTP protocols, offering flexibility in how services discover each other."
"What is the significance of 'Health Checks' in AWS Cloud Map?","To ensure service instances are healthy and available","To monitor CPU utilisation","To manage network bandwidth","To track API call latency","Health checks in Cloud Map ensure that only healthy and available service instances are returned during service discovery."
"In AWS Cloud Map, what is the purpose of a custom health check?","To monitor the health of an instance using a custom application logic","To monitor the EC2 instance health","To check DNS resolution speed","To verify the load balancer status","A custom health check allows you to define application-specific logic to determine the health of an instance."
"Which AWS CLI command would you use to register a new service in AWS Cloud Map?","aws service-discovery register-service","aws cloudmap create-service","aws ec2 register-instance","aws cloudformation create-stack","The command `aws service-discovery create-service` is used to register a new service in AWS Cloud Map."
"What is the pricing model for AWS Cloud Map?","Based on the number of service instances and queries","Based on data transfer only","Based on the number of namespaces only","Free of charge","AWS Cloud Map pricing is based on the number of service instances registered and the number of queries made for service discovery."
"How does AWS Cloud Map handle service versioning?","By using different service names or attributes for different versions","By automatically updating all instances to the latest version","By not supporting service versioning","By using separate AWS accounts for each version","Cloud Map allows you to manage service versions by using different service names or attributes to distinguish between versions."
"If a service instance in AWS Cloud Map fails a health check, what happens?","It is automatically removed from service discovery results","It is automatically restarted","It is quarantined for investigation","It is moved to a backup service registry","If a service instance fails a health check, Cloud Map automatically removes it from service discovery results, ensuring that only healthy instances are returned."
"What is the difference between a Public DNS Namespace and a Private DNS Namespace in AWS Cloud Map?","Public DNS is resolvable over the internet, Private DNS is only resolvable within a VPC","Public DNS is for testing purposes, Private DNS is for production","Public DNS uses Route 53, Private DNS uses internal resolvers","Public DNS allows wildcard entries, Private DNS doesn't","A Public DNS Namespace is resolvable over the internet, while a Private DNS Namespace is only resolvable within a VPC."
"Which AWS service can be used to configure and manage the DNS records created by AWS Cloud Map?","Amazon Route 53","AWS Certificate Manager","AWS CloudFront","AWS Shield","Amazon Route 53 is used to configure and manage the DNS records created by Cloud Map when using DNS namespaces."
"When should you consider using AWS Cloud Map over other service discovery mechanisms?","When you need dynamic service discovery and integration with other AWS services","When you only need static service discovery","When you don't have access to AWS services","When you need a simple key-value store","You should consider Cloud Map when you need dynamic service discovery and seamless integration with other AWS services like ECS and EC2."
"What is the purpose of the 'DeregisterInstance' API call in AWS Cloud Map?","To remove an instance from a service","To stop an EC2 instance","To delete a service","To remove a namespace","The `DeregisterInstance` API call is used to remove an instance from a service in AWS Cloud Map."
"What is the function of the 'GetService' API call in AWS Cloud Map?","To retrieve information about a specific service","To start a new service","To list all services in a namespace","To update a service's health check configuration","The `GetService` API call is used to retrieve information about a specific service, such as its ID and ARN."
"How can you improve the reliability of service discovery using AWS Cloud Map?","By configuring health checks and using multiple availability zones","By using a single availability zone for all services","By disabling health checks","By increasing the DNS TTL to a high value","Configuring health checks and deploying services across multiple availability zones improves the reliability of service discovery in Cloud Map."
"What is a typical use case for using an HTTP namespace in AWS Cloud Map?","Registering microservices that communicate over HTTP","Registering databases","Registering static web pages","Registering serverless functions","HTTP namespaces are commonly used for registering microservices that communicate over HTTP, as they can be queried using HTTP APIs."
"How can you control access to your AWS Cloud Map resources?","Using IAM policies","Using Network ACLs","Using Route 53 Resolver rules","Using CloudFront Origin Access Identities","Access to Cloud Map resources is controlled using IAM policies, allowing you to grant specific permissions to users and roles."
"What is the purpose of the 'ListServices' API call in AWS Cloud Map?","To retrieve a list of all services within a specified namespace","To start all services","To stop all services","To delete all services","The `ListServices` API call is used to retrieve a list of all services within a specified namespace in AWS Cloud Map."
"How does AWS Cloud Map support multi-region deployments?","By configuring separate namespaces in each region and synchronising data","By automatically replicating data across regions","By using a global load balancer","By using AWS Global Accelerator","Cloud Map supports multi-region deployments by configuring separate namespaces in each region and synchronising data between them."
"What is the role of a service registry in a microservices architecture, and how does AWS Cloud Map fit into this?","It allows microservices to discover each other, which is exactly what AWS Cloud Map provides","It enforces security policies across all microservices. AWS Cloud Map doesn't do this","It manages the deployment of microservices. AWS Cloud Map has no deployment features","It monitors the health of microservices. AWS Cloud Map doesn't monitor microservices directly","A service registry allows microservices to discover each other, and AWS Cloud Map acts as a service registry for microservices in AWS."
"What is the maximum length of a service name in AWS Cloud Map?","128 characters","64 characters","256 characters","32 characters","The maximum length of a service name in AWS Cloud Map is 128 characters."
"How does AWS Cloud Map improve application availability?","By providing automatic health checks and failover","By automatically scaling EC2 instances","By providing a content delivery network","By encrypting data in transit","Cloud Map improves application availability by providing automatic health checks and ensuring that only healthy instances are returned during service discovery."
"What is the purpose of setting a TTL (Time to Live) value for DNS records in AWS Cloud Map?","To control how long DNS resolvers cache the records","To set the expiration date for a service","To limit the number of queries to a service","To prioritise DNS queries","Setting a TTL value controls how long DNS resolvers cache the records, affecting how quickly changes propagate."
"Which AWS service can be used to monitor the health of services registered in AWS Cloud Map?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch can be used to monitor the health of services registered in Cloud Map, allowing you to track metrics and set alarms."
"What is the purpose of the 'ListInstances' API call in AWS Cloud Map?","To retrieve a list of all registered instances for a specific service","To create new instances","To delete existing instances","To update instance attributes","The `ListInstances` API call is used to retrieve a list of all registered instances for a specific service in AWS Cloud Map."
"What type of data can you store as attributes for a service instance in AWS Cloud Map?","Key-value pairs","JSON documents","XML files","Binary data","You can store key-value pairs as attributes for a service instance in Cloud Map, allowing you to store metadata about the instance."
"How can you automate the registration of services with AWS Cloud Map in a CI/CD pipeline?","Using AWS CloudFormation or Terraform","Using AWS CodeBuild alone","Using AWS CodePipeline alone","Using AWS Config rules","You can automate the registration of services with Cloud Map in a CI/CD pipeline using AWS CloudFormation or Terraform."
"What security best practice should you follow when using AWS Cloud Map?","Grant least privilege IAM permissions","Enable public access to all namespaces","Store credentials in plaintext attributes","Disable health checks","Granting least privilege IAM permissions is a key security best practice when using Cloud Map to control access to resources."
"Which AWS service can be used to integrate AWS Cloud Map with a serverless application?","AWS Lambda","AWS Step Functions","AWS API Gateway","AWS CloudFront","AWS Lambda can be used to integrate Cloud Map with a serverless application, allowing Lambda functions to discover and connect to other services."
"When using AWS Cloud Map with Amazon ECS, what is the recommended way to handle service discovery?","Using ECS service discovery integration","Using manual DNS configuration","Using load balancer health checks only","Using AWS CloudFormation alone","The recommended way to handle service discovery with ECS is to use ECS service discovery integration, which automatically registers and deregisters tasks with Cloud Map."
"What is the purpose of 'Service Discovery Filtering' in AWS Cloud Map?","To filter instances based on attributes","To filter namespaces","To filter services based on health check status","To filter IAM policies","Service discovery filtering allows you to filter instances based on attributes, enabling more precise service discovery."
"What happens if you delete a namespace in AWS Cloud Map that still contains services?","The deletion will fail until all services are deleted","The services will be automatically moved to a default namespace","The services will continue to operate without service discovery","The services will be automatically deleted","The deletion of a namespace will fail until all services within that namespace are deleted."
"How can you monitor the performance of your AWS Cloud Map service discovery queries?","Using Amazon CloudWatch metrics","Using AWS CloudTrail logs","Using AWS Config rules","Using AWS X-Ray traces","You can monitor the performance of Cloud Map queries using Amazon CloudWatch metrics, such as latency and query count."
"What is the 'DiscoverInstances' API call used for in AWS Cloud Map?","To discover instances for a given service","To create new instances","To delete existing instances","To update instance attributes","The `DiscoverInstances` API call is used to discover instances for a given service, allowing applications to find available service endpoints."
"What does it mean when an AWS Cloud Map service instance has a 'SERVING' status?","The instance is healthy and available","The instance is starting up","The instance is being deregistered","The instance is experiencing errors","A 'SERVING' status indicates that the instance is healthy and available for service discovery."
"Which of the following is NOT a valid attribute data type that can be associated with a service instance in AWS Cloud Map?","Binary","String","Number","Boolean","Binary is not a valid attribute data type in AWS Cloud Map. Only String and Number are allowed."
"You need to implement service discovery across multiple AWS accounts. How can you achieve this using AWS Cloud Map?","By using Resource Access Manager (RAM) to share namespaces","By creating a VPN connection between the accounts","By replicating the Cloud Map configuration in each account","By using AWS Organizations to manage the accounts","You can achieve service discovery across multiple AWS accounts by using Resource Access Manager (RAM) to share namespaces with other accounts."
"How do you configure AWS Cloud Map to use a custom domain name for service discovery?","By creating a Public DNS Namespace and configuring Route 53","By creating a HTTP Namespace and configuring Route 53","By creating a Private DNS Namespace and configuring Route 53","By creating a Private DNS Namespace and configuring a custom DNS server","To use a custom domain name, you need to create a Private DNS Namespace and configure Route 53 to resolve the domain within your VPC."
"What is the recommended way to manage AWS Cloud Map resources as infrastructure as code?","Using AWS CloudFormation or Terraform","Using AWS CLI scripts","Using AWS Management Console","Using AWS SDK only","Using AWS CloudFormation or Terraform is the recommended way to manage Cloud Map resources as infrastructure as code."
"You are using AWS Cloud Map with ECS and need to ensure that instances are deregistered when they are no longer healthy. What is the best approach?","Configure health checks in Cloud Map and ECS service discovery","Use a lifecycle hook to deregister instances","Manually deregister instances using the AWS CLI","Rely on the default ECS deregistration process","Configuring health checks in both Cloud Map and ECS service discovery ensures that unhealthy instances are automatically deregistered."
"What is the purpose of the 'UpdateInstanceCustomHealthStatus' API in AWS Cloud Map?","To update the health status of a service instance based on custom logic","To update the attributes of a service instance","To update the DNS record for a service instance","To update the health check configuration of a service","The 'UpdateInstanceCustomHealthStatus' API allows you to update the health status of a service instance based on custom application logic."
"When using AWS Cloud Map for microservices, how can you implement blue/green deployments?","By registering new versions of the service with different attributes and using filtering","By creating separate namespaces for each environment","By using different AWS accounts for each environment","By using traffic shifting in AWS Route 53","You can implement blue/green deployments by registering new versions of the service with different attributes and using filtering in Cloud Map."
"You want to ensure that only services within a specific VPC can discover each other using AWS Cloud Map. What type of namespace should you use?","Private DNS Namespace","Public DNS Namespace","HTTP Namespace","API Gateway Namespace","A Private DNS Namespace ensures that service discovery is limited to services within the VPC."
"What are the limitations of using AWS Cloud Map for service discovery?","Limited scalability compared to other solutions","Limited integration with non-AWS services","Limited support for different service discovery protocols","Limited options for custom health checks","While Cloud Map integrates well with AWS, its integration with non-AWS services may require additional configuration and adaptation."
"What is the primary function of AWS Cloud Map?","Service discovery","Content delivery","Data warehousing","Compute orchestration","Cloud Map is primarily used for service discovery, allowing applications to find and connect to each other."
"In AWS Cloud Map, what does a 'namespace' represent?","A logical grouping of services","A network access control list","A billing account","A deployment region","A namespace in Cloud Map represents a logical grouping of services, often corresponding to an environment or application."
"What is a 'service instance' in the context of AWS Cloud Map?","A specific instance of a service that can be discovered","A virtual machine image","A database schema","A code deployment package","A service instance represents a specific instance of a service, containing attributes like IP address and port."
"Which AWS service is commonly used to register service instances dynamically with AWS Cloud Map?","AWS ECS","AWS Lambda","Amazon SQS","Amazon SNS","AWS ECS is often used to register service instances dynamically with Cloud Map as containers are launched and terminated."
"What type of health check can be configured with AWS Cloud Map?","HTTP and DNS","TCP and UDP","ICMP and SSH","SMTP and FTP","Cloud Map supports HTTP and DNS health checks to verify the health of registered service instances."
"Which of the following is a benefit of using AWS Cloud Map for service discovery?","Reduced complexity in service-to-service communication","Automatic scaling of compute resources","Simplified database management","Automated code deployments","Cloud Map reduces complexity in service-to-service communication by providing a central registry for service discovery."
"What is the difference between a Public and Private DNS namespace in AWS Cloud Map?","Public DNS namespaces are resolvable from the internet, while Private DNS namespaces are only resolvable within a VPC","Public DNS namespaces are cheaper than Private DNS namespaces","Public DNS namespaces support more service instances than Private DNS namespaces","Public DNS namespaces offer higher availability than Private DNS namespaces","Public DNS namespaces are resolvable from the internet, while Private DNS namespaces are only resolvable within a VPC."
"How does AWS Cloud Map integrate with AWS CloudWatch?","To provide metrics on service discovery operations","To automatically scale service instances","To manage access control policies","To store service logs","Cloud Map integrates with CloudWatch to provide metrics on service discovery operations, such as the number of queries and latency."
"Which protocol is used by AWS Cloud Map to resolve service names?","DNS and HTTP","TCP and UDP","ICMP and SSH","SMTP and FTP","Cloud Map uses DNS and HTTP protocols to resolve service names to service instances."
"What is the purpose of the AWS Cloud Map API?","To programmatically interact with Cloud Map","To monitor EC2 instance health","To manage S3 bucket policies","To configure VPC settings","The Cloud Map API allows developers to programmatically interact with Cloud Map to register, discover, and manage services."
"Which of the following is a typical use case for AWS Cloud Map in a microservices architecture?","Allowing microservices to discover and connect to each other without hardcoding IP addresses","Storing configuration data for microservices","Managing the deployment of microservices","Monitoring the performance of microservices","Cloud Map allows microservices to discover and connect to each other dynamically, eliminating the need for hardcoding IP addresses."
"How does AWS Cloud Map help with blue/green deployments?","By allowing you to register both blue and green environments and switch traffic between them","By automating the deployment of new versions of your application","By automatically rolling back deployments if errors occur","By providing a centralised logging system for deployments","Cloud Map helps with blue/green deployments by allowing you to register both environments and seamlessly switch traffic between them."
"What security measures should be considered when using AWS Cloud Map with Private DNS namespaces?","Ensure that only authorised resources within the VPC can access the namespace","Encrypt all traffic between services","Restrict access to the Cloud Map API","Enable multi-factor authentication for all users","It is crucial to ensure that only authorised resources within the VPC can access the Private DNS namespace to prevent unauthorised service discovery."
"Which of the following AWS Cloud Map concepts enables you to define attributes associated with service instances?","Custom Attributes","Tags","Metadata","Labels","Custom Attributes allow you to define specific information related to service instances, enabling richer service discovery."
"How can you control access to AWS Cloud Map resources, such as namespaces and services?","Using IAM policies","Using VPC security groups","Using network ACLs","Using AWS Firewall Manager","IAM policies are used to control access to Cloud Map resources, allowing you to define who can create, read, update, and delete namespaces and services."
"What is the role of the AWS Cloud Map console?","To provide a graphical interface for managing Cloud Map resources","To monitor the health of EC2 instances","To manage S3 bucket contents","To configure VPC settings","The Cloud Map console provides a graphical interface for managing namespaces, services, and service instances."
"What is the significance of 'service name' in the context of AWS Cloud Map?","It's the identifier used to discover a service","It's the DNS record type","It's the health check type","It's the IAM role associated with a service","The service name is used to discover a service, allowing clients to query Cloud Map for the endpoints of the service."
"How does AWS Cloud Map support multi-region deployments?","By allowing you to create namespaces and services in multiple regions","By automatically replicating data across regions","By providing a global load balancer","By simplifying cross-account access","Cloud Map supports multi-region deployments by allowing you to create namespaces and services in different regions, enabling service discovery across regions."
"What is the impact of deregistering a service instance in AWS Cloud Map?","The instance is no longer discoverable","The instance is terminated","The instance is automatically restarted","The instance is moved to a different namespace","Deregistering a service instance means it is no longer discoverable through Cloud Map, effectively removing it from the service registry."
"Which pricing model does AWS Cloud Map use?","Pay-per-query and per-resource","Fixed monthly fee","Pay-per-instance","Free tier with limited usage","Cloud Map charges based on the number of queries and the number of resources (namespaces, services, and instances) you create."
"How can you use AWS Cloud Map to manage external services that are not running on AWS?","By manually registering the external service's endpoints","By creating a VPN connection to the external network","By using AWS Direct Connect","By configuring a NAT gateway","You can manually register the endpoints of external services in Cloud Map, allowing you to discover them alongside your AWS-based services."
"What is the relationship between AWS Cloud Map and Route 53?","Cloud Map can use Route 53 to resolve service names to IP addresses","Cloud Map replaces Route 53 functionality","Route 53 is used to manage Cloud Map namespaces","Cloud Map and Route 53 are completely independent services","Cloud Map can use Route 53 to resolve service names to IP addresses, leveraging Route 53's DNS capabilities."
"Which of the following is a recommended practice for using AWS Cloud Map in a highly available environment?","Use multiple DNS resolvers and configure health checks","Store service configurations in S3","Use a single Availability Zone for all resources","Disable caching for service discovery","Using multiple DNS resolvers and configuring health checks ensures that service discovery remains available even if individual resolvers or instances fail."
"What is the maximum number of custom attributes you can associate with a service instance in AWS Cloud Map?","20","10","50","100","You can associate up to 20 custom attributes with each service instance in AWS Cloud Map."
"How can you troubleshoot service discovery issues with AWS Cloud Map?","By checking CloudWatch metrics, DNS resolution, and health check status","By examining VPC flow logs","By using AWS X-Ray","By analysing CloudTrail logs","Troubleshooting involves checking CloudWatch metrics for query volume and latency, verifying DNS resolution is working correctly, and ensuring health checks are passing."
"What is the purpose of service discovery when using AWS Cloud Map with a container orchestration system like Kubernetes?","To automatically update service endpoints as containers are scaled and redeployed","To manage the container registry","To configure container networking","To automate container deployments","Service discovery allows applications running within containers to find and connect to other services, even as containers are scaled and redeployed."
"How does AWS Cloud Map support A/B testing?","By allowing you to register different versions of a service and route traffic to them based on custom attributes","By automatically deploying different versions of your application","By providing a centralised configuration management system","By automating the process of rolling back failed deployments","Cloud Map supports A/B testing by allowing you to register different versions of a service and route traffic to them based on custom attributes, enabling you to compare their performance."
"Which of the following features is NOT directly supported by AWS Cloud Map?","Load balancing","Health checking","Service registration","Service discovery","Load balancing is typically handled by separate services like Application Load Balancer (ALB) or Network Load Balancer (NLB)."
"What is the primary benefit of using AWS Cloud Map with AWS Lambda functions?","Simplifying the invocation of Lambda functions by name","Automatically scaling Lambda functions based on demand","Managing the deployment of Lambda functions","Monitoring the performance of Lambda functions","Cloud Map simplifies the invocation of Lambda functions by name, allowing other services to discover and connect to them without needing to know their specific ARNs."
"How can you ensure that AWS Cloud Map is integrated into your CI/CD pipeline?","By using the AWS Cloud Map API to automatically register and deregister service instances during deployments","By manually updating the Cloud Map configuration after each deployment","By using AWS CodePipeline to trigger Cloud Map updates","By creating a CloudFormation template to manage Cloud Map resources","Automating the registration and deregistration of service instances during deployments using the Cloud Map API ensures that your service registry is always up-to-date."
"What is a 'health check custom configuration' in AWS Cloud Map?","Allows you to specify advanced health check parameters","Allows you to disable health checks","Allows you to change the health check interval","Allows you to integrate third-party monitoring tools","A health check custom configuration allows you to specify advanced parameters like the path for HTTP health checks or the query for DNS health checks."
"When using AWS Cloud Map in a hybrid cloud environment, what needs to be considered?","Connectivity between your on-premises network and AWS","Compliance requirements","Data encryption","Server sizing","Connectivity between your on-premises network and AWS via VPN or Direct Connect is crucial for discovering services in both environments."
"What is the purpose of the 'TTL' (Time To Live) setting in AWS Cloud Map?","Determines how long DNS records are cached","Defines the lifetime of a service instance","Sets the duration for health checks","Controls the maximum number of service instances","The TTL setting determines how long DNS records are cached, affecting the speed of service discovery and the frequency of updates."
"Which AWS CLI command is used to create a new namespace in AWS Cloud Map?","aws service-discovery create-namespace","aws cloudmap create-namespace","aws sd create-namespace","aws servicediscovery create-ns","The correct command is `aws service-discovery create-namespace`."
"You want to use AWS Cloud Map to register a service. Which of the following steps is essential?","Creating a namespace and defining a service within it","Creating an EC2 instance","Configuring a load balancer","Setting up a database","Creating a namespace and defining a service within it are essential steps to start using AWS Cloud Map."
"How does AWS Cloud Map contribute to increased application resilience?","By dynamically updating service locations, reducing the impact of individual instance failures","By automatically scaling compute resources","By providing a centralised logging system","By encrypting data in transit","Cloud Map contributes to resilience by dynamically updating service locations, allowing applications to quickly adapt to instance failures."
"What type of endpoint does AWS Cloud Map support for service instances?","IP addresses, DNS names, and custom endpoints","Only IP addresses","Only DNS names","Only custom endpoints","Cloud Map supports a variety of endpoints for service instances including IP addresses, DNS names, and custom endpoints."
"What is the relationship between AWS Cloud Map and AWS App Mesh?","AWS Cloud Map can be used to discover services for App Mesh","AWS App Mesh manages Cloud Map resources","They are completely independent services","AWS Cloud Map replaces the need for App Mesh","AWS Cloud Map can be used to discover the services that are managed by App Mesh."
"Which statement best describes the 'Service Registry' function of AWS Cloud Map?","A central repository of information about services and their locations","A tool for monitoring service health","A mechanism for deploying new services","A feature for managing IAM roles","The 'Service Registry' function of AWS Cloud Map provides a central repository of information about services and their locations, allowing applications to discover and connect to each other."
"Which health check type would be most suitable for a service exposing an HTTP API using AWS Cloud Map?","HTTP Health Check","DNS Health Check","TCP Health Check","ICMP Health Check","An HTTP Health Check is most suitable for a service exposing an HTTP API because it can verify the service's response to HTTP requests."
"What is the role of 'Discoverability' in AWS Cloud Map?","The ability of applications to locate and connect to services","The process of deploying new service versions","The monitoring of service performance","The management of service configurations","Discoverability refers to the ability of applications to locate and connect to services registered in Cloud Map."
"When using AWS Cloud Map for service discovery, what is the key advantage of using a DNS namespace over an HTTP namespace?","DNS resolution is typically faster and more efficient","HTTP namespaces offer more advanced features","HTTP namespaces are easier to configure","DNS namespaces support more custom attributes","DNS resolution is typically faster and more efficient than HTTP-based service discovery, making it a better choice for performance-critical applications."
"In the context of AWS Cloud Map, what is the purpose of service attributes?","To provide metadata about service instances for filtering and routing","To configure security settings for the service","To define the deployment strategy for the service","To specify the resources allocated to the service","Service attributes provide metadata about service instances that can be used for filtering and routing traffic, enabling more sophisticated service discovery scenarios."
"Which of the following is NOT a typical use case for AWS Cloud Map?","Centralised secrets management","Dynamic service discovery","Microservice architecture","Hybrid cloud integration","Centralised secrets management is typically handled by services like AWS Secrets Manager or HashiCorp Vault, not AWS Cloud Map."
"Which AWS Cloud Map concept helps in creating fault-tolerant systems?","Health checks","Service instances","Namespaces","Custom attributes","Health checks are crucial for fault tolerance, as they allow Cloud Map to automatically remove unhealthy instances from service discovery."
"How can you integrate AWS Cloud Map with a legacy application that doesn't support DNS-based service discovery?","Use the AWS Cloud Map API to query for service endpoints and integrate the results into the application","Migrate the application to use DNS-based service discovery","Deploy the application in a container","Use a load balancer to route traffic to the application","You can use the AWS Cloud Map API to query for service endpoints and integrate the results into the legacy application's configuration or logic."
"What is the purpose of the 'RegisterInstance' API call in AWS Cloud Map?","To add a new service instance to a service within a namespace","To create a new namespace","To update an existing service","To delete a service instance","The RegisterInstance API call is used to add a new service instance to a service, registering it with the Cloud Map service registry."
"What does the AWS Cloud Map 'ListServices' API return?","A list of all services within a specified namespace","A list of all instances for a specific service","A list of all namespaces in the AWS account","A list of all health checks configured in Cloud Map","The ListServices API returns a list of all services that exist within a specified namespace."
"What is the primary function of AWS Cloud Map?","Service discovery","Content delivery","Data warehousing","Load balancing","AWS Cloud Map is primarily used for service discovery, allowing applications to find and connect to each other."
"In AWS Cloud Map, what is a 'namespace'?","A container for service names","A storage location for DNS records","A collection of IAM users","A virtual network interface","A namespace in Cloud Map is a container for service names, enabling logical grouping and organisation."
"Which DNS record type is commonly used with AWS Cloud Map for service discovery?","SRV","TXT","CNAME","MX","SRV records are commonly used with Cloud Map to specify the host and port for a service."
"What AWS service is commonly used in conjunction with AWS Cloud Map for container orchestration?","Amazon ECS","Amazon S3","Amazon EC2","AWS Lambda","Amazon ECS (Elastic Container Service) often integrates with Cloud Map for service discovery within containerised applications."
"Which of the following is a benefit of using AWS Cloud Map for service discovery?","Dynamically updated service locations","Centralised IAM policy management","Automated cost optimisation","Simplified disaster recovery planning","Cloud Map automatically updates service locations as instances change, ensuring applications can always find the correct endpoints."
"What type of health check is supported by AWS Cloud Map?","HTTP and DNS","TCP and UDP","ICMP and ARP","SMTP and POP3","Cloud Map supports both HTTP and DNS health checks to ensure service endpoints are healthy and available."
"When integrating AWS Cloud Map with Amazon ECS, where is the service name and endpoint information stored?","AWS Cloud Map registry","ECS Task Definition","EC2 instance metadata","VPC route table","Service name and endpoint details are stored in the Cloud Map registry, allowing ECS tasks to discover services."
"What is the purpose of the 'DeregisterInstance' API call in AWS Cloud Map?","Removes an instance from a service registry","Creates a new service","Updates the health check status","Lists all registered services","'DeregisterInstance' removes a specific instance from the service registry, indicating it's no longer available."
"What happens when a service instance in AWS Cloud Map fails a health check?","Traffic is automatically routed away from the instance","The instance is automatically terminated","The instance is moved to a different namespace","An alert is triggered in AWS CloudWatch","When a service instance fails a health check, Cloud Map stops routing traffic to it, improving application availability."
"Which of the following is a supported namespace type in AWS Cloud Map?","HTTP","Private DNS","Public DNS","SMTP","Cloud Map supports both Private and Public DNS namespaces, enabling different service discovery options."
"How does AWS Cloud Map contribute to microservices architectures?","Centralised service registry and discovery","Automated code deployment","Enhanced data encryption","Simplified IAM role management","Cloud Map provides a centralised service registry, enabling microservices to easily discover and communicate with each other."
"What is a 'Service Instance' in the context of AWS Cloud Map?","A specific endpoint for a service","A collection of namespaces","A set of IAM permissions","A virtual network interface","A service instance is a specific endpoint (e.g., IP address and port) where a service is running."
"Which of these is a possible method for registering instances with AWS Cloud Map?","Using the AWS SDK or CLI","Automatic registration through CloudTrail","Manual registration through the AWS Management Console only","Importing data from a CSV file","You can register instances with Cloud Map programmatically using the AWS SDK or CLI, allowing for automated registration."
"How can you control access to AWS Cloud Map resources?","IAM policies","Network ACLs","Security Groups","VPC endpoints","IAM policies are used to control access to Cloud Map resources, ensuring only authorised users and services can interact with the service."
"What is the benefit of using AWS Cloud Map with auto scaling groups?","Dynamic endpoint updates","Automated patching","Cost optimisation of EC2 instances","Improved security posture","Cloud Map automatically updates service endpoints as instances in an auto scaling group scale up or down, ensuring applications can always find available resources."
"What is the role of the 'DiscoverInstances' API call in AWS Cloud Map?","Retrieves a list of healthy instances for a service","Creates a new service","Updates the configuration of an existing service","Deletes a service","'DiscoverInstances' retrieves a list of healthy instances for a specific service, allowing applications to find available endpoints."
"What is the primary difference between a Private DNS namespace and a Public DNS namespace in AWS Cloud Map?","Visibility of the DNS records","Supported health check types","Maximum number of service instances","Cost of operation","Private DNS namespaces are only visible within your VPC, while Public DNS namespaces are visible on the public internet."
"Which of the following best describes the 'TTL' setting in AWS Cloud Map DNS records?","Time To Live","Time To Launch","Total Transfer Limit","Task Termination Limit","'TTL' (Time To Live) specifies how long DNS resolvers should cache the DNS record for a Cloud Map service."
"How does AWS Cloud Map simplify the deployment of new application versions?","Dynamic service discovery","Automated rollbacks","Simplified blue/green deployments","Automated security patching","Cloud Map allows applications to dynamically discover the new version of a service as it's deployed, simplifying the deployment process."
"Which AWS service is commonly used to monitor the health of services registered with AWS Cloud Map?","Amazon CloudWatch","Amazon CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch is commonly used to monitor the health of services registered with Cloud Map, providing insights into application performance."
"In AWS Cloud Map, what is the purpose of a 'custom attribute'?","To store metadata about a service instance","To define IAM permissions","To configure DNS routing policies","To specify the instance type","Custom attributes allow you to store metadata about a service instance, enabling more granular service discovery based on specific properties."
"How does AWS Cloud Map integrate with AWS Lambda functions?","Service discovery for Lambda functions","Automated Lambda deployment","Simplified Lambda version control","Centralised Lambda logging","Cloud Map can be used for service discovery, allowing Lambda functions to discover and interact with other services."
"When using AWS Cloud Map, what is the impact of frequent DNS record changes on application performance?","Increased latency due to DNS propagation","Reduced security due to DNS spoofing","Increased CPU utilisation on the DNS server","Reduced network bandwidth","Frequent DNS record changes can lead to increased latency as DNS resolvers need to update their caches."
"What is the purpose of the 'ListServices' API call in AWS Cloud Map?","Retrieves a list of all services in a namespace","Creates a new service","Updates the configuration of an existing service","Deletes a service","'ListServices' retrieves a list of all services within a specified namespace, allowing you to discover available services."
"Which of the following is a key benefit of using AWS Cloud Map in a multi-region deployment?","Simplified service discovery across regions","Automated failover between regions","Centralised logging across regions","Reduced network latency across regions","Cloud Map simplifies service discovery across regions, allowing applications to find services regardless of their location."
"What type of data can be stored in the attributes of a service instance in AWS Cloud Map?","Key-value pairs","JSON documents","XML files","Binary data","Service instance attributes in Cloud Map can store key-value pairs, allowing you to add metadata to each instance."
"How can you automate the registration of EC2 instances with AWS Cloud Map?","Using AWS Systems Manager Automation","Using AWS CloudTrail triggers","Using Amazon S3 event notifications","Using AWS Config rules","AWS Systems Manager Automation can be used to automate the registration of EC2 instances with Cloud Map, simplifying management."
"Which of the following is a best practice when using AWS Cloud Map?","Use descriptive service names","Store sensitive data in custom attributes","Disable health checks to reduce costs","Register instances manually whenever possible","Using descriptive service names helps with organisation and makes it easier to discover the right services."
"What is the role of the 'UpdateInstanceCustomHealthStatus' API call in AWS Cloud Map?","Updates the health status of an instance","Creates a new health check","Deletes an existing health check","Lists all health checks","'UpdateInstanceCustomHealthStatus' allows you to manually update the health status of an instance, useful for custom health check implementations."
"How does AWS Cloud Map improve application resilience?","Automatic failover to healthy instances","Automated security patching","Simplified disaster recovery planning","Enhanced data encryption","Cloud Map improves application resilience by automatically routing traffic to healthy instances, ensuring continuous availability."
"What is the relationship between AWS Cloud Map and DNS?","Cloud Map uses DNS for service discovery","Cloud Map replaces DNS","Cloud Map manages DNS infrastructure","Cloud Map is independent of DNS","Cloud Map uses DNS for service discovery, leveraging DNS infrastructure to resolve service endpoints."
"Which AWS Cloud Map component acts as the central repository for service metadata?","Service Registry","DNS Resolver","Health Checker","Load Balancer","The Service Registry acts as the central repository, storing information about registered services and their instances."
"In AWS Cloud Map, how do you specify the port number for a service instance?","Using a custom attribute","Using a DNS record type","Using a service name prefix","Using a VPC endpoint policy","The port number is typically specified using a custom attribute associated with the service instance."
"What is the purpose of configuring a health check for a service in AWS Cloud Map?","To automatically detect and remove unhealthy instances","To automatically scale the service based on load","To encrypt data in transit","To provide enhanced security","Configuring a health check allows Cloud Map to automatically detect and remove unhealthy instances, ensuring only healthy instances receive traffic."
"Which AWS service can you integrate with AWS Cloud Map to dynamically configure load balancing?","Elastic Load Balancing (ELB)","Amazon CloudFront","AWS Global Accelerator","Amazon API Gateway","Elastic Load Balancing (ELB) can be integrated with Cloud Map to dynamically configure load balancing based on service instance health and availability."
"When troubleshooting service discovery issues with AWS Cloud Map, what should you check first?","IAM permissions and DNS resolution","Network ACLs and Security Groups","CloudTrail logs and CloudWatch metrics","VPC endpoints and route tables","IAM permissions and DNS resolution are crucial for ensuring services can properly register and discover each other."
"What does 'service discovery' accomplish within a distributed application context when using AWS Cloud Map?","Locating and connecting services","Managing application configuration","Scaling resources based on demand","Securing communication channels","Service discovery enables components to automatically locate and connect to other services."
"Which network protocol does AWS Cloud Map commonly use for service discovery?","DNS","HTTP","TCP","UDP","AWS Cloud Map relies on DNS for service discovery resolution, allowing applications to locate service endpoints."
"What is a core benefit of using AWS Cloud Map over manually managing service endpoints?","Dynamic updates and simplified management","Reduced operational costs","Enhanced security compliance","Automated deployment processes","Cloud Map dynamically updates service endpoints and simplifies the management of distributed services."
"Which AWS Cloud Map resource represents an individual instance of a service?","Endpoint","Service Instance","Namespace","Service Definition","A Service Instance represents a specific endpoint or instance of a service registered within Cloud Map."
"When using AWS Cloud Map with AWS Fargate, how are service instances typically registered?","Automatically via ECS task definitions","Manually via the AWS CLI","Imported from a CSV file","Registered from EC2 instance metadata","Service instances are typically registered automatically through ECS task definitions when using AWS Fargate."
"How does AWS Cloud Map assist in decoupling services within a microservices architecture?","Centralised discovery and dynamic addressing","Automated load balancing","Enhanced data encryption","Simplified logging and monitoring","Cloud Map's centralised discovery and dynamic addressing enable microservices to operate independently while discovering each other."
"What type of information is stored in the 'Service Definition' within AWS Cloud Map?","Metadata about the service","IAM permissions for the service","Network configuration settings","Deployment configuration details","The Service Definition stores metadata about the service, such as health check configuration and custom attributes."
"When should you choose a Public DNS namespace over a Private DNS namespace in AWS Cloud Map?","When services need to be accessible from the internet","When services are only accessible within a VPC","When you need enhanced security features","When you require low latency connections","Use a Public DNS namespace when your services need to be accessible from the internet."
"How does the integration of AWS Cloud Map with container orchestration tools like Kubernetes improve application scalability?","Dynamic endpoint registration and discovery","Automated resource provisioning","Enhanced security compliance","Simplified deployment pipelines","Dynamic endpoint registration and discovery facilitate the scaling of containerised applications by ensuring services can always locate each other."
"What is the purpose of the AWS Cloud Map console?","To manage service registries and DNS configurations","To monitor network traffic","To configure security groups","To manage EC2 instances","The AWS Cloud Map console is used to manage service registries, namespaces, and DNS configurations."
"Which API operation would you use to fetch all service instances associated with a specific service in AWS Cloud Map?","DiscoverInstances","ListServices","GetService","RegisterInstance","The `DiscoverInstances` API operation retrieves a list of service instances associated with a particular service."
"What is a practical use case for assigning custom attributes to service instances in AWS Cloud Map?","Versioning and environment differentiation","Security group assignments","IAM role configurations","Load balancing algorithm selection","Custom attributes can be used to differentiate service instances based on version or environment, enabling targeted routing or configuration."
"How can you ensure high availability of your AWS Cloud Map service registry?","Using a multi-region deployment","Enabling VPC flow logs","Configuring AWS Config rules","Deploying across multiple Availability Zones","While Cloud Map is highly available by design, deploying supporting services (like the applications using it) across multiple Availability Zones enhances overall resilience."
"In AWS Cloud Map, what mechanism ensures that only healthy service instances are returned during service discovery?","Health checks and status propagation","Network ACLs and Security Groups","IAM role restrictions","DNS query filtering","Health checks ensure that only instances reporting a healthy status are returned during service discovery."
"What is the primary function of AWS Cloud Map?","Discover and connect registered services.","Monitor the health of EC2 instances.","Manage IAM roles and permissions.","Deploy containerised applications.","AWS Cloud Map's main purpose is to help applications discover and connect to each other, by maintaining an updated registry of service locations."
"Which of the following is a component of AWS Cloud Map?","Service Registry","Load Balancer","Security Group","IAM Policy","A Service Registry is where you define the name and other attributes for your service."
"What type of resource record can AWS Cloud Map create in Route 53?","A (Address)","CNAME (Canonical Name)","MX (Mail Exchange)","TXT (Text)","Cloud Map can create A records in Route 53 to point to the IP addresses of your services."
"In AWS Cloud Map, what does a 'namespace' represent?","A logical grouping of services.","A security boundary for resources.","A set of IAM permissions.","A cost allocation tag.","A namespace in Cloud Map is a logical group that you can use to organise your services."
"What is the purpose of a 'health check' in AWS Cloud Map?","To automatically remove unhealthy instances from service discovery.","To monitor CPU utilisation of EC2 instances.","To scan for security vulnerabilities.","To optimise database queries.","Health checks are used by Cloud Map to determine whether instances are healthy and should be included in service discovery."
"How can you integrate AWS Cloud Map with ECS?","By specifying Cloud Map attributes in the ECS task definition.","By creating a CloudWatch alarm to monitor ECS tasks.","By configuring an IAM role for ECS to access Cloud Map.","By manually updating service discovery records in Cloud Map.","ECS tasks can be automatically registered and deregistered with Cloud Map by specifying Cloud Map attributes in the task definition."
"Which AWS service can be used to query AWS Cloud Map for service instance information?","AWS SDK","AWS CloudTrail","AWS Config","Amazon Inspector","The AWS SDK provides APIs that applications can use to query Cloud Map for service instance information."
"What happens when an instance fails a health check in AWS Cloud Map?","The instance is automatically deregistered from service discovery.","The instance is moved to a different Availability Zone.","The instance's CPU is throttled.","The instance's network traffic is rerouted.","When an instance fails a health check, Cloud Map automatically deregisters it from service discovery to prevent traffic from being routed to unhealthy instances."
"Which of the following is a benefit of using AWS Cloud Map?","Simplified service discovery.","Automated cost optimisation.","Enhanced security auditing.","Improved database performance.","Cloud Map simplifies service discovery by providing a central registry for service locations and health statuses."
"What type of namespace can be created within AWS Cloud Map?","Private DNS and HTTP.","Public DNS and API Gateway.","VPC and IAM.","EC2 and S3.","Cloud Map allows you to create private DNS namespaces (within your VPC) and HTTP namespaces (for applications outside your VPC)."
"How does AWS Cloud Map integrate with Kubernetes?","Through custom resource definitions (CRDs).","By using a sidecar container.","By configuring network policies.","By creating IAM roles.","Cloud Map can integrate with Kubernetes through custom resource definitions (CRDs), allowing you to manage Kubernetes services within Cloud Map."
"Which of the following is a key difference between using a DNS-based namespace and an HTTP namespace in AWS Cloud Map?","HTTP namespaces are discoverable from outside the VPC.","DNS namespaces require a domain name.","HTTP namespaces are limited to a single region.","DNS namespaces support only A records.","HTTP namespaces are generally used for service discovery outside of a VPC and do not require a domain name."
"What is the role of the Service Discovery API in AWS Cloud Map?","To programmatically interact with Cloud Map.","To configure health checks.","To manage namespaces.","To create Route 53 records.","The Service Discovery API provides a programmatic interface for registering, discovering, and managing services in Cloud Map."
"How can you automatically register EC2 instances with AWS Cloud Map?","By using the AWS Cloud Map API and a custom script.","By enabling Auto Scaling group integration.","By configuring a Lambda function to monitor EC2 instance launches.","By using AWS Config rules.","You can automate EC2 instance registration using the AWS Cloud Map API and a script that runs on instance launch."
"What type of traffic does AWS Cloud Map primarily manage?","Inter-service traffic.","Internet traffic.","Database traffic.","Email traffic.","Cloud Map is designed to manage the communication between microservices and other services within your application environment."
"In AWS Cloud Map, what does 'instance attributes' refer to?","Key-value pairs associated with a service instance.","IAM roles assigned to a service.","CloudWatch metrics collected for a service.","Tags applied to a service.","Instance attributes are key-value pairs that provide additional information about a service instance, such as its version or deployment environment."
"How does AWS Cloud Map help in dynamic environments?","By automatically updating service locations as instances change.","By predicting future traffic patterns.","By automatically scaling EC2 instances.","By optimising database queries.","Cloud Map automatically updates service locations when instances are added, removed, or change their IP addresses, making it ideal for dynamic environments."
"What is the relationship between AWS Cloud Map and Route 53 Auto Naming?","Route 53 Auto Naming uses Cloud Map under the hood.","Cloud Map replaces Route 53 Auto Naming.","They are two independent services.","Cloud Map is used for internal service discovery, while Route 53 Auto Naming is for external.","Route 53 Auto Naming is a simplified interface built on top of AWS Cloud Map for automatically registering services with Route 53."
"Which of the following is a use case for AWS Cloud Map in a microservices architecture?","Service discovery and load balancing.","API gateway management.","Container orchestration.","Database schema management.","Cloud Map helps microservices discover each other and provides load balancing by resolving service names to instance locations."
"What information does AWS Cloud Map store about a service?","Endpoint locations, service names, and health status.","CPU utilisation, memory usage, and network traffic.","IAM roles, security groups, and network ACLs.","Cost allocation tags, billing information, and resource IDs.","Cloud Map primarily stores endpoint locations (IP addresses, ports), service names, and health status to facilitate service discovery."
"How does AWS Cloud Map contribute to high availability?","By removing unhealthy instances from service discovery.","By automatically creating backups of EC2 instances.","By replicating data across multiple Availability Zones.","By optimising database queries.","Cloud Map ensures high availability by automatically removing unhealthy instances from service discovery, preventing traffic from being routed to them."
"Which of the following is a typical workflow when using AWS Cloud Map?","Register services, discover instances, connect to services.","Deploy applications, monitor performance, optimise costs.","Create IAM roles, configure security groups, manage permissions.","Build Docker images, push to ECR, deploy to ECS.","The typical workflow involves registering services with Cloud Map, discovering instances of those services, and then connecting to the appropriate endpoint."
"How can you monitor the health of services registered with AWS Cloud Map?","By using Cloud Map's built-in health checks.","By configuring CloudWatch alarms.","By using AWS X-Ray.","By analysing VPC Flow Logs.","Cloud Map provides built-in health checks that automatically monitor the health of registered service instances."
"What is the pricing model for AWS Cloud Map?","Based on the number of API requests and health checks.","Based on the amount of data transferred.","Based on the number of registered services.","Based on the number of namespaces created.","Cloud Map pricing is based on the number of API requests you make and the number of health checks you configure."
"Which AWS service can be used to deploy applications that integrate with AWS Cloud Map?","AWS ECS and EKS.","AWS Lambda.","AWS S3.","AWS CloudFront.","AWS ECS (Elastic Container Service) and EKS (Elastic Kubernetes Service) are commonly used to deploy applications that integrate with Cloud Map."
"What security considerations are important when using AWS Cloud Map?","Controlling access to the Cloud Map API using IAM.","Encrypting data at rest in Cloud Map.","Monitoring network traffic to Cloud Map.","Securing the underlying EC2 instances.","It's crucial to control access to the Cloud Map API using IAM to prevent unauthorised access and modification of service discovery information."
"Which of the following is a benefit of using AWS Cloud Map over manually managing service endpoints?","Automation and reduced operational overhead.","Improved security posture.","Reduced infrastructure costs.","Enhanced database performance.","Cloud Map automates the process of registering and updating service endpoints, reducing the manual effort and potential for errors."
"What is the purpose of the AWS Cloud Map Resolver endpoint?","To resolve service names to IP addresses.","To configure health checks.","To manage namespaces.","To register services.","The Resolver endpoint is used to resolve service names to IP addresses, enabling applications to discover and connect to each other."
"How can you ensure that your applications use the latest service information from AWS Cloud Map?","By configuring a short DNS TTL or using the HTTP API for frequent updates.","By creating a CloudWatch event to trigger a service update.","By using AWS Config to monitor changes to service endpoints.","By enabling auto-scaling for the service instances.","To ensure applications use the latest service information, configure a short DNS TTL (Time To Live) or use the HTTP API for frequent updates."
"Which feature of AWS Cloud Map allows you to specify custom attributes for each service instance?","Instance Attributes","Service Properties","Namespace Tags","Resource Policies","Instance Attributes allow you to attach custom metadata to each service instance, providing additional information for discovery and configuration."
"When should you choose an HTTP namespace over a DNS namespace in AWS Cloud Map?","When you need service discovery outside of a VPC.","When you require strict DNS compliance.","When you are using Kubernetes.","When you need to integrate with Route 53.","An HTTP namespace is suitable for services that need to be discovered outside of a VPC, as it doesn't rely on DNS for resolution."
"What is the benefit of integrating AWS Cloud Map with AWS Fargate?","Automatic service discovery and scaling.","Simplified container deployment.","Enhanced security for containerised applications.","Reduced costs for container execution.","Cloud Map enables automatic service discovery and scaling for Fargate applications, streamlining the management of containerised workloads."
"How does AWS Cloud Map support multi-region deployments?","By providing service discovery across multiple regions.","By automatically replicating data across regions.","By load balancing traffic across regions.","By providing a single endpoint for all regions.","Cloud Map enables service discovery across multiple regions, allowing applications to discover and connect to services regardless of their location."
"What is the purpose of the 'TTL' (Time To Live) setting in a DNS namespace in AWS Cloud Map?","To control how long DNS records are cached.","To define the lifespan of service instances.","To set the maximum time for health checks.","To specify the duration of API calls.","The TTL setting determines how long DNS records are cached, impacting how quickly changes to service locations are propagated."
"Which AWS service can be used to monitor the API calls made to AWS Cloud Map?","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS X-Ray","AWS CloudTrail logs API calls made to Cloud Map, providing an audit trail of actions performed on the service."
"How can you use AWS Cloud Map to implement blue/green deployments?","By updating the service records to point to the new environment.","By creating a new namespace for the green environment.","By using Route 53 weighted routing.","By configuring a Canary deployment strategy.","By updating the service records in Cloud Map to point to the new (green) environment, you can switch traffic between the blue and green deployments."
"What is the primary advantage of using AWS Cloud Map for service discovery in a serverless architecture?","Centralised and dynamic endpoint management.","Automated cost optimisation.","Simplified security configuration.","Enhanced application performance.","Cloud Map provides centralised and dynamic endpoint management for serverless functions and APIs, simplifying service discovery in a serverless environment."
"Which AWS service does AWS Cloud Map integrate with to provide DNS resolution for services within a VPC?","Route 53 Resolver","VPC Peering","Direct Connect","AWS PrivateLink","Cloud Map integrates with Route 53 Resolver to provide DNS resolution for services within a VPC, allowing applications to discover services using familiar DNS names."
"What type of information is *not* typically stored directly within AWS Cloud Map?","Application code","Service endpoint addresses","Service health status","Custom instance attributes","Application code is not stored in Cloud Map, it mainly focuses on service endpoints, health and custom attributes for discovery."
"If a service registered in AWS Cloud Map becomes unhealthy, what automated action does Cloud Map take?","Removes the unhealthy instance from DNS/API discovery results","Replaces the unhealthy instance with a new instance","Sends an alert to the operations team","Scales down the unhealthy instance","Cloud Map automatically removes the unhealthy instance from DNS and API discovery results so traffic doesn't route there."
"For services running outside of AWS, how can they be registered with AWS Cloud Map?","Using the Cloud Map HTTP API","Through automatic integration with AWS Systems Manager","By manually configuring Route 53 records","By deploying an AWS Cloud Map agent on the external server","Services can be registered with the Cloud Map using the HTTP API which gives flexibility to register from anywhere."
"When designing an AWS Cloud Map solution, what factor most significantly impacts the cost?","The number of API calls made to register, update or resolve services","The amount of data stored in the Cloud Map registry","The number of namespaces and services defined","The complexity of the health check configurations","The primary cost driver for Cloud Map is API calls made to the service for registration, updates, and resolution."
"How does AWS Cloud Map assist with implementing a circuit breaker pattern in a distributed system?","By removing failing services from service discovery, preventing cascading failures","By providing a central dashboard to monitor service health","By automatically retrying failed requests","By load balancing traffic across healthy service instances","Circuit breaker pattern is implemented using AWS Cloud Map by removing failing instances automatically so the system doesnt cascade failures"
"You are building a mobile application that needs to discover services running in your AWS environment. How can AWS Cloud Map facilitate this?","By using HTTP namespaces to allow service discovery without needing a VPC","By configuring a VPN connection between the mobile app and the AWS VPC","By creating a public DNS record for each service","By embedding the AWS SDK in the mobile application","HTTP namespaces can be used to facilitate discovery of services without needing a VPC"
"Your organisation is using AWS Service Catalog. How can you use AWS Cloud Map to improve the discoverability of provisioned products?","By registering provisioned products as services in Cloud Map","By creating CloudWatch dashboards to monitor provisioned products","By using AWS Config to track changes to provisioned products","By integrating AWS Cloud Map with AWS IAM Identity Center","Registering provisioned products as services allows them to be discovered in the same way as any other service."
"Which of the following is a key advantage of using AWS Cloud Map with a container orchestration service like Amazon ECS or EKS?","Automated service registration and deregistration based on container lifecycle events","Centralized monitoring of container resource utilisation","Simplified container image management","Automated scaling of container resources based on demand","Cloud Map provides automated registration based on lifecycle events of containers for example, deployment and termination"
"Your company has a strict requirement to encrypt all data at rest. How is this requirement addressed with AWS Cloud Map?","Data in Cloud Map is encrypted at rest by default using AWS KMS","You must manually configure encryption for each namespace","Encryption at rest is not supported by AWS Cloud Map","You need to use a third-party service to encrypt the data before registering it with Cloud Map","Data in Cloud Map is encrypted at rest by default using AWS KMS. This means security is already provisioned"
"Which of the following is a supported use case for AWS Cloud Map?","Service Discovery in Microservices Architecture","Database Migration","Data Warehousing","Serverless computing","Cloud Map is a way to implement service discovery in the modern microservices architecture."
"In AWS Cloud Map, what is the best practice for managing the lifecycle of service instances that are ephemeral?","Using health checks to automatically deregister unhealthy instances","Manually deregistering instances when they are terminated","Increasing the TTL for DNS records to prevent stale entries","Implementing a caching layer to store service instance information","Cloud Map will use the health check to automatically deregister any unhealthy instances."
"What is the primary function of AWS Cloud Map?","Discovering and connecting services.","Managing EC2 instances.","Storing configuration data.","Monitoring application performance.","Cloud Map's primary function is to enable service discovery, allowing applications to find and connect to each other using DNS or HTTP APIs."
"Which protocol can AWS Cloud Map use for service discovery?","DNS and HTTP","SMTP and FTP","TCP and UDP","IMAP and POP3","Cloud Map supports service discovery via standard DNS queries and HTTP API calls."
"When registering a service instance with AWS Cloud Map, what information is typically included?","Service name, IP address, and port","IAM role, VPC ID, and AMI ID","Security group, subnet ID, and key pair","Auto Scaling group name, load balancer ARN, and launch configuration","When registering a service instance, you typically include details like the service name, IP address, and port so that other services can locate and connect to it."
"Which AWS service integrates with Cloud Map for automatic service instance registration?","Auto Scaling groups.","AWS Lambda.","Amazon SQS.","Amazon SNS.","Auto Scaling groups can be configured to automatically register and deregister service instances with Cloud Map as instances are launched or terminated."
"What is a 'namespace' in the context of AWS Cloud Map?","A container for service names and DNS settings.","A virtual network in AWS.","A collection of IAM policies.","A security group for EC2 instances.","In Cloud Map, a namespace is a container for your service names and associated DNS or HTTP settings, providing a logical grouping for service discovery."
"Which of the following is NOT a benefit of using AWS Cloud Map?","Simplified service discovery.","Automated scaling of infrastructure.","Centralised service registry.","Dynamic health checking.","Cloud Map simplifies service discovery, offers a centralised registry, and supports dynamic health checking. It does not handle automated scaling of infrastructure."
"What type of health check can be configured in AWS Cloud Map?","HTTP, TCP, and DNS","CPU utilisation, memory utilisation, and disk I/O","Network latency, packet loss, and jitter","Custom metric, CloudWatch alarm, and SNS notification","Cloud Map supports HTTP, TCP, and DNS health checks to ensure that only healthy service instances are returned during discovery."
"What is the purpose of the 'DiscoverInstances' API call in AWS Cloud Map?","To retrieve a list of healthy service instances.","To register a new service instance.","To update the health status of a service instance.","To delete a service namespace.","The DiscoverInstances API call is used to query Cloud Map and retrieve a list of healthy instances for a specific service."
"In AWS Cloud Map, what is the relationship between a 'service' and a 'service instance'?","A service is a definition, and a service instance is a running copy of that service.","A service instance manages multiple services.","They are interchangeable terms referring to the same entity.","A service is a version of a service instance.","A service is a definition or template, whereas a service instance represents a specific running instance of that service with its own endpoint and health status."
"How does AWS Cloud Map support blue/green deployments?","By enabling different service versions with different DNS records.","By automatically scaling the blue environment based on traffic.","By managing IAM roles for blue/green environments.","By creating separate Cloud Map namespaces for blue and green environments.","Cloud Map can facilitate blue/green deployments by associating different service versions with different DNS records within the same namespace, allowing for traffic switching."
"What is the primary function of AWS Cloud Map?","Service discovery","Content delivery","Data warehousing","Compute orchestration","AWS Cloud Map is a service discovery service, which allows applications to discover the location of other services."
"Which protocol is commonly used by AWS Cloud Map for registering and discovering services?","HTTP/HTTPS","SMTP","FTP","SNMP","AWS Cloud Map uses HTTP/HTTPS for registering and discovering services, making it compatible with a wide range of applications and environments."
"What is a 'Namespace' in the context of AWS Cloud Map?","A container for service discovery resources","A storage location for service data","A virtual network for service communication","A security group for service access control","A Namespace in AWS Cloud Map is a container for service discovery resources, such as services and instances."
"Which type of health check is supported directly by AWS Cloud Map to monitor the health of service instances?","HTTP health check","Ping health check","TCP health check","Memory utilisation check","AWS Cloud Map supports HTTP health checks to monitor the health of service instances, allowing for automated deregistration of unhealthy instances."
"How does AWS Cloud Map integrate with Amazon ECS (Elastic Container Service)?","It provides service discovery for ECS tasks","It manages ECS cluster scaling","It stores ECS task definitions","It monitors ECS container resource usage","AWS Cloud Map provides service discovery for ECS tasks, allowing containers to discover and communicate with each other based on service names."
"In AWS Cloud Map, what is a 'Service Instance'?","A specific endpoint of a service","A group of services","A security policy applied to a service","A cost allocation tag for a service","A Service Instance in AWS Cloud Map represents a specific endpoint of a service, including attributes like IP address and port."
"What is the benefit of using AWS Cloud Map with AWS App Mesh?","Provides dynamic service discovery for microservices","Enables serverless computing","Automates infrastructure provisioning","Simplifies data encryption","AWS Cloud Map, when integrated with AWS App Mesh, provides dynamic service discovery for microservices, enabling efficient communication and routing within the mesh."
"Which feature of AWS Cloud Map enables services to discover each other using custom DNS names?","Private DNS Namespaces","Public DNS Namespaces","HTTP Namespaces","Service Instance Attributes","AWS Cloud Map Private DNS Namespaces allow services to discover each other using custom DNS names within a VPC, providing a private and secure discovery mechanism."
"What is the cost model for AWS Cloud Map?","Pay per API call and resource created","Fixed monthly fee","Pay per GB of data transferred","Free tier with limited features","AWS Cloud Map's cost model is based on pay per API call and the number of resources created, such as namespaces, services, and instances."
"How does AWS Cloud Map improve application availability?","By automatically deregistering unhealthy service instances","By automatically scaling compute resources","By providing multi-region data replication","By enabling fault-tolerant network configurations","AWS Cloud Map improves application availability by automatically deregistering unhealthy service instances, ensuring that only healthy endpoints are used for service discovery."