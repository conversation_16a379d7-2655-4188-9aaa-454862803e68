"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary purpose of AWS Direct Connect?","To establish a dedicated network connection from your premises to AWS","To provide a VPN connection to AWS","To provide a content delivery network","To manage AWS Identity and Access Management","AWS Direct Connect enables you to create a dedicated network connection from your on-premises environment to AWS, bypassing the public internet."
"Which of these is NOT a benefit of using AWS Direct Connect?","Reduced network costs","Increased bandwidth","More consistent network experience","Unlimited free data transfer","Direct Connect can often reduce network costs and provide more consistent network performance, but data transfer is not unlimitedly free."
"What is the smallest bandwidth AWS Direct Connect connection typically available?","50 Mbps","100 Mbps","1 Gbps","10 Gbps","AWS Direct Connect typically offers connections starting at 1 Gbps."
"What is a Virtual Interface (VIF) in the context of AWS Direct Connect?","A logical interface created on a Direct Connect connection","A physical port on an AWS router","A type of EC2 instance","A virtual private gateway","A Virtual Interface (VIF) is a logical interface created on a Direct Connect connection to enable access to AWS services."
"What type of BGP peering is used for accessing public AWS services over Direct Connect?","Public peering","Private peering","VPN peering","Transit peering","Public peering allows you to access public AWS services like S3 over Direct Connect."
"What type of BGP peering is used for accessing resources in your VPC over Direct Connect?","Private peering","Public peering","VPN peering","Transit peering","Private peering allows you to access resources in your VPC over Direct Connect."
"Which AWS service is often used in conjunction with Direct Connect to extend your on-premises network into the AWS Cloud?","Virtual Private Cloud (VPC)","Simple Storage Service (S3)","Elastic Compute Cloud (EC2)","Relational Database Service (RDS)","Direct Connect often used with VPC to create a hybrid cloud."
"What is a Direct Connect Gateway?","A global resource that enables connections to multiple VPCs in different regions","A regional resource that enables connections to a single VPC","A tool for monitoring Direct Connect bandwidth usage","A service for encrypting data in transit over Direct Connect","A Direct Connect Gateway allows you to connect a Direct Connect connection to multiple VPCs in different AWS regions."
"What is the recommended way to ensure high availability when using Direct Connect?","Establish two or more Direct Connect connections in different locations","Use a single Direct Connect connection with a very high bandwidth","Rely on the redundancy provided by AWS","Only use Direct Connect for non-critical applications","To ensure high availability, it's recommended to establish multiple Direct Connect connections at separate physical locations."
"Which routing protocol is used to exchange routing information between your network and AWS over Direct Connect?","Border Gateway Protocol (BGP)","Open Shortest Path First (OSPF)","Routing Information Protocol (RIP)","Enhanced Interior Gateway Routing Protocol (EIGRP)","BGP is the standard routing protocol used for Direct Connect."
"What is the purpose of setting a BGP Autonomous System Number (ASN) when configuring Direct Connect?","To identify your network to AWS for routing purposes","To encrypt the BGP traffic","To filter unwanted routes","To configure access control lists","The ASN identifies your network and is necessary for BGP peering."
"Which of the following is a key consideration when choosing a Direct Connect location?","Proximity to your data centre","Availability of power and cooling","Cost of office space","Availability of snacks","Proximity to your data centre is a key factor for latency and performance."
"What is the function of Link Aggregation Control Protocol (LACP) when using Direct Connect?","To bundle multiple physical connections into a single logical connection","To encrypt data in transit","To monitor network latency","To manage access control lists","LACP aggregates multiple physical connections to increase bandwidth and provide redundancy."
"What does the term 'LAG' refer to in the context of AWS Direct Connect?","Link Aggregation Group","Latency Average Graph","Logical Access Gateway","Load Allocation Group","LAG stands for Link Aggregation Group, a bundle of multiple physical connections."
"What is the purpose of Direct Connect Resiliency Toolkit?","To automate failover between Direct Connect connections","To monitor the CPU usage of Direct Connect connections","To encrypt the data sent through Direct Connect","To manage the AWS firewall","The Direct Connect Resiliency Toolkit helps automate failover in case of a connection failure."
"How does AWS Direct Connect help in achieving regulatory compliance?","By providing a private, dedicated connection that avoids the public internet","By automatically encrypting all data in transit","By storing audit logs for compliance purposes","By providing pre-configured compliance templates","By keeping the data off the internet, you have more control."
"What is the AWS recommended method for encrypting data in transit over a Direct Connect connection?","IPsec VPN","SSL/TLS","AWS KMS","Direct Connect automatically encrypts data","While Direct Connect provides a dedicated connection, encryption is generally handled by IPsec VPN, SSL/TLS or application level encryption."
"What is the role of a Direct Connect Partner?","To assist with setting up and managing Direct Connect connections","To provide cloud storage services","To manage AWS accounts","To provide security audits","Direct Connect Partners can assist with the technical aspects of establishing and managing Direct Connect."
"Which AWS service can be used to monitor the performance and health of your Direct Connect connection?","CloudWatch","CloudTrail","Trusted Advisor","Config","CloudWatch is used to monitor performance metrics like bandwidth usage and latency."
"When using Direct Connect, how is billing typically handled?","You are billed for port hours and data transfer","You are billed per EC2 instance","You are billed per S3 object stored","You are billed for every API call","Billing is based on port hours (the time the connection is active) and data transfer out of AWS."
"What is the maximum number of virtual interfaces (VIFs) you can create per Direct Connect connection by default?","50","1","10","100","By default, you can create 50 VIFs per Direct Connect connection."
"You need to move a very large dataset to AWS. Which AWS service, combined with Direct Connect, would be most efficient?","Snowball","Storage Gateway","CloudFront","Elastic File System (EFS)","Snowball and Direct Connect is optimised for transporting large volumes of data."
"What is the purpose of a Direct Connect Location?","A facility where you can colocate your equipment to connect to AWS","A region where AWS services are available","A virtual network in AWS","A security zone within AWS","Direct Connect Locations are facilities where you can connect your on-premises equipment to the AWS network."
"Which AWS service can be used to provide a secure tunnel over the Direct Connect link, particularly useful for encrypted data transfer?","AWS VPN","AWS Shield","AWS WAF","AWS Certificate Manager","AWS VPN, specifically a Site-to-Site VPN, can be used to create an encrypted tunnel over the Direct Connect link."
"You are experiencing high latency over your Direct Connect connection. What is the first troubleshooting step you should take?","Check the physical connectivity of your Direct Connect connection","Reboot your EC2 instances","Increase the bandwidth of your connection","Contact AWS Support without any initial investigation","Checking the physical connection is the most logical first step."
"What is the maximum transmission unit (MTU) size that is supported by AWS Direct Connect?","1500 or 9001 (Jumbo Frames)","576","1024","65535","Direct Connect supports both standard (1500) and Jumbo Frames (9001) MTU sizes."
"Which of the following is NOT a typical use case for AWS Direct Connect?","Connecting a branch office to the internet","Creating a hybrid cloud environment","Migrating large datasets to AWS","Providing low-latency access to AWS services","Direct Connect is not designed for connecting branch offices to the public internet. It's for private connections to AWS."
"You need to establish a Direct Connect connection to a VPC that resides in a different AWS Region than your Direct Connect location. How can you achieve this?","Using a Direct Connect Gateway","Using VPC Peering","Using a Transit Gateway in the same region as your Direct Connect location","You cannot connect to a VPC in a different region","A Direct Connect Gateway allows you to connect to VPCs in different regions."
"Which AWS service is best suited for ensuring that your Direct Connect traffic is prioritised over other network traffic?","Quality of Service (QoS)","Traffic Mirroring","Network ACLs","Security Groups","QoS mechanisms can be used to prioritise traffic based on its importance."
"What is the purpose of Bidirectional Forwarding Detection (BFD) when used with AWS Direct Connect?","To detect link failures more quickly than standard BGP keepalive timers","To encrypt the BGP traffic","To provide load balancing across multiple connections","To monitor network bandwidth utilization","BFD provides faster link failure detection, improving failover times."
"What is the AWS Shared Responsibility Model's implication for security when using Direct Connect?","AWS is responsible for the security of the AWS cloud, and you are responsible for the security of your data and applications","You are responsible for the security of both the AWS cloud and your data and applications","AWS is responsible for all aspects of security","AWS is responsible for the physical security of the Direct Connect connection, you are responsible for everything on-prem","AWS manages the physical security of the AWS infrastructure, but you're responsible for securing your data and applications."
"What is the purpose of configuring a private Autonomous System Number (ASN) for your Direct Connect connection?","To avoid conflicts with public ASNs","To encrypt the BGP traffic","To prioritise your traffic","To hide your network from the internet","Private ASNs are used within private networks to avoid conflicts with globally routable ASNs."
"What is the benefit of using Direct Connect in conjunction with AWS Transit Gateway?","Simplified network architecture and routing between multiple VPCs and on-premises networks","Increased EC2 instance sizes","Reduced S3 storage costs","Automated patching of operating systems","Transit Gateway simplifies routing in complex hybrid cloud environments."
"You have a Direct Connect connection that is experiencing intermittent packet loss. What is a possible cause?","A faulty cable or network interface","A misconfigured security group","Insufficient EC2 instance memory","Too many S3 buckets","A faulty cable or network interface can cause packet loss."
"What is the purpose of setting up a BGP community string when configuring Direct Connect?","To influence routing decisions","To encrypt the BGP traffic","To provide access control","To monitor network bandwidth utilization","BGP community strings can be used to influence routing behaviour, such as setting preferences for inbound or outbound traffic."
"Which of the following is NOT a factor that affects the cost of using AWS Direct Connect?","The amount of data transferred out of AWS","The port speed of the Direct Connect connection","The number of virtual interfaces","The number of EC2 instances you have","Data transfer out of AWS and the port speed are key cost factors."
"What is the purpose of the AWS Direct Connect SiteLink feature?","To create a direct connection between two on-premises locations using the AWS global network","To encrypt data between on-premises locations","To manage AWS Identity and Access Management roles","To provide a content delivery network","SiteLink creates a connection between two on-premises locations using the AWS Global Network."
"How do you ensure that your data is protected in transit when using AWS Direct Connect?","Implement encryption using IPsec VPN or other methods","AWS automatically encrypts all data in transit","Direct Connect automatically encrypts the data","Direct Connect does not support data encryption","While Direct Connect provides a private connection, encryption is still your responsibility using techniques like IPsec VPN."
"What is the recommended way to handle failover between two Direct Connect connections?","Using BGP route advertisement and withdrawal","Manually switching traffic between connections","Shutting down the failed connection","Relying on AWS to automatically handle failover","BGP is used to advertise and withdraw routes, allowing for automatic failover."
"What is a good use case for implementing Jumbo Frames (MTU 9001) with Direct Connect?","Transferring large files with minimal overhead","Running web servers","Accessing small database records","Using standard file sharing protocols","Jumbo Frames reduce overhead when transferring large amounts of data."
"Which of the following is a typical responsibility of an AWS Direct Connect Partner?","Providing physical cabling and installation services","Managing AWS accounts","Developing cloud applications","Providing security audits","Direct Connect Partners often provide physical cabling and installation services."
"How can you verify the integrity of data transferred over AWS Direct Connect?","By using checksums or other data integrity mechanisms","Direct Connect automatically verifies data integrity","By using AWS Trusted Advisor","Direct Connect does not provide a data integrity mechanism","While Direct Connect reduces the risk of interception, it's important to implement checksums, or similar data integrity mechanisms."
"You need to connect your on-premises network to multiple VPCs in the same AWS Region using Direct Connect. What is the simplest way to achieve this?","Using a Direct Connect Gateway","Using VPC Peering","Creating multiple Direct Connect connections","Using a VPN connection","Direct Connect Gateway simplifies connectivity to multiple VPCs."
"What is the primary benefit of using AWS Direct Connect over a traditional VPN connection for connecting to AWS?","Lower latency and more consistent network performance","Automatic data encryption","Lower cost","Simplified security configuration","Direct Connect provides lower latency and more consistent performance compared to VPN."
"What is the purpose of the Direct Connect LAG (Link Aggregation Group) 'minimum links' setting?","To ensure a minimum number of links are active before traffic is sent","To set a maximum bandwidth limit","To encrypt the traffic on a minimum number of links","To provide redundancy on a minimum number of links","The 'minimum links' setting ensures a minimum number of links are active, providing a baseline level of bandwidth and redundancy."
"When configuring BGP for Direct Connect, what is the significance of the 'hold time'?","The amount of time a BGP speaker will wait for a keepalive message from its peer before declaring the connection down","The amount of time it takes to establish a Direct Connect connection","The amount of time to hold a session","The amount of time a route remains in the routing table","The hold time dictates how long a BGP speaker waits for a keepalive message before declaring a peer as unavailable."
"What is the default BGP hold time value that AWS uses for Direct Connect connections?","90 seconds","3 seconds","60 seconds","180 seconds","AWS defaults to a BGP hold time of 90 seconds for Direct Connect."
"In AWS Direct Connect, which option offers the lowest latency for data transfer?","Direct Connect with Jumbo Frames (9001 MTU)","Direct Connect with standard MTU (1500)","Site-to-Site VPN over the public internet","Client VPN over the public internet","Using Direct Connect with Jumbo Frames minimises fragmentation and overheads, leading to lower latency."
"What is the purpose of enabling BFD (Bidirectional Forwarding Detection) on a Direct Connect virtual interface (VIF)?","Rapid fault detection to facilitate quicker failover","Encrypting the BGP traffic","Prioritising critical traffic","Managing access control lists for network traffic","BFD (Bidirectional Forwarding Detection) enables quicker detection of link failures, leading to a faster failover process."
"What is the best practice for ensuring network path redundancy on an AWS Direct Connect connection?","Establish multiple Direct Connect connections with diverse paths","Use only one Direct Connect connection with a high bandwidth","Configure route reflectors","Enable BGP multi-hop","Establishing multiple Direct Connect connections with diverse paths is the most robust approach for network path redundancy."
"With AWS Direct Connect, what does the term 'LAG' refer to?","Link Aggregation Group","Lowest Available Gateway","Latency Assurance Guarantee","Local Area Gateway","LAG allows you to bundle multiple physical connections into a single logical connection, increasing bandwidth and redundancy."
"What is the primary benefit of using AWS Direct Connect over connecting to AWS via the public Internet?","More consistent network performance","Lower cost","Simplified configuration","Automatic failover to other AWS regions","Direct Connect provides a dedicated network connection, resulting in more predictable and consistent network performance compared to the public internet."
"In AWS Direct Connect, what is the purpose of a Virtual Interface (VIF)?","To enable routing between your network and AWS","To encrypt the connection","To manage firewall rules","To monitor network traffic","A VIF enables routing between your network and AWS, allowing you to access AWS services such as EC2 and S3."
"Which Border Gateway Protocol (BGP) attribute is used by AWS Direct Connect to influence the path selection for inbound traffic from AWS to your network?","AS Path Prepending","Local Preference","MED (Multi Exit Discriminator)","Community Attribute","AS Path Prepending is used to influence the path selection for inbound traffic from AWS to your network, making your network appear less desirable."
"What is the minimum bandwidth available for a dedicated AWS Direct Connect connection?","1 Gbps","50 Mbps","100 Mbps","500 Mbps","Dedicated Direct Connect connections start at 1 Gbps."
"What is the purpose of the AWS Direct Connect Gateway?","To connect multiple VPCs in different regions to a single Direct Connect connection","To provide internet access to your VPCs","To manage security groups for Direct Connect","To monitor the health of Direct Connect connections","The Direct Connect Gateway enables you to connect multiple VPCs in different regions to a single Direct Connect connection, simplifying network management."
"Which AWS service can be used to monitor the performance and availability of your AWS Direct Connect connection?","CloudWatch","CloudTrail","Trusted Advisor","Config","CloudWatch can be used to monitor the performance and availability of your AWS Direct Connect connection, providing metrics and alarms."
"What is the 'jumbo frame' MTU size typically used with AWS Direct Connect?","9001","1500","576","68","Jumbo frames (MTU of 9001) can improve network throughput by reducing overhead."
"What is a common use case for AWS Direct Connect when considering data transfer?","Transferring large datasets between on-premises data centres and AWS","Hosting a static website","Managing user authentication","Running serverless functions","Direct Connect is useful for transferring large datasets between on-premises and AWS."
"When establishing an AWS Direct Connect connection, what is a 'BGP ASN'?","A unique identifier for your network","A type of encryption algorithm","A network security group","A virtual firewall appliance","A BGP ASN (Autonomous System Number) is a unique identifier for your network, used for routing with BGP."
"If you need to connect to both public AWS services (like S3) and private VPC resources using AWS Direct Connect, what type of virtual interface(s) do you need?","Public and Private VIFs","Only a Public VIF","Only a Private VIF","A Transit VIF","You need both a Public VIF to access public services and a Private VIF to access resources within your VPC."
"What does AWS Direct Connect provide regarding network traffic encryption?","It does not provide built-in encryption. Encryption must be implemented separately.","It automatically encrypts all traffic.","It encrypts traffic only between AWS regions.","It encrypts traffic only to S3.","Direct Connect itself does not provide built-in encryption. You must use other methods like VPN or application-level encryption."
"What is the purpose of configuring BFD (Bidirectional Forwarding Detection) on your AWS Direct Connect virtual interface?","To quickly detect link failures","To encrypt network traffic","To compress network traffic","To prioritise network traffic","BFD provides fast detection of link failures, allowing for quicker failover in case of connectivity issues."
"What is the AWS recommended best practice for ensuring high availability when using AWS Direct Connect?","Establish multiple Direct Connect connections in different locations","Use a single Direct Connect connection with high bandwidth","Rely solely on internet connectivity as a backup","Use only public virtual interfaces","Multiple Direct Connect connections in geographically diverse locations provide redundancy in case of failure."
"Which AWS service is commonly used in conjunction with AWS Direct Connect to extend your on-premises network into the AWS cloud?","Virtual Private Cloud (VPC)","Simple Storage Service (S3)","Elastic Compute Cloud (EC2)","Relational Database Service (RDS)","Direct Connect extends your on-premises network into AWS, and VPC provides a logically isolated section of the AWS Cloud where you can launch AWS resources."
"What is the difference between a 'hosted connection' and a 'dedicated connection' in AWS Direct Connect?","A hosted connection is provisioned by an AWS partner, while a dedicated connection is directly with AWS.","A hosted connection is cheaper, while a dedicated connection offers higher bandwidth.","A hosted connection is only for public services, while a dedicated connection is for VPCs.","A hosted connection uses IPsec VPN, while a dedicated connection uses BGP.","Hosted connections are provided by AWS partners, while dedicated connections are directly with AWS."
"What is the purpose of the 'Customer Gateway' in the context of AWS Direct Connect?","It represents your on-premises router","It represents AWS's router","It manages security groups","It monitors network traffic","The Customer Gateway represents the router on your side of the Direct Connect connection."
"What is the primary reason you might choose to use AWS Direct Connect over a VPN connection?","Lower latency and more consistent performance","Simplified setup","Lower cost","Automatic encryption","Direct Connect provides lower latency and more consistent network performance compared to VPN connections, due to the dedicated physical connection."
"What AWS resource is needed to establish a private virtual interface (VIF) when using AWS Direct Connect?","Virtual Private Gateway (VGW)","Internet Gateway (IGW)","NAT Gateway","Transit Gateway","A Virtual Private Gateway (VGW) is required for establishing a private VIF to connect to your VPC."
"Which of the following is a use case where AWS Direct Connect is highly beneficial?","Hybrid cloud environments","Web hosting for small websites","Sending email","Running batch processing jobs","Direct Connect provides a reliable and high-bandwidth connection that is beneficial for hybrid cloud environments."
"What is the maximum number of BGP routes advertised by AWS Direct Connect?","100","10","1000","500","AWS Direct Connect supports a maximum of 100 BGP routes advertised."
"Which AWS service can you use to create a site-to-site VPN connection as a backup to your AWS Direct Connect connection?","AWS VPN","AWS Shield","AWS WAF","AWS Firewall Manager","AWS VPN allows you to create a site-to-site VPN connection as a backup to your Direct Connect connection, providing redundancy."
"With AWS Direct Connect, which term refers to the physical port on the AWS side where your connection terminates?","AWS Router","AWS Endpoint","AWS Location","AWS Region","The physical port on the AWS side where your connection terminates is referred to as the AWS Location."
"What is the purpose of using AWS Direct Connect's MACsec (Media Access Control Security) option?","To encrypt the physical connection between your network and AWS","To encrypt data at rest in S3","To encrypt traffic between EC2 instances","To secure access to the AWS Management Console","MACsec is used to encrypt the physical layer of the Direct Connect connection, providing enhanced security for data in transit."
"When configuring your router for use with AWS Direct Connect, what routing protocol is required?","Border Gateway Protocol (BGP)","Open Shortest Path First (OSPF)","Routing Information Protocol (RIP)","Enhanced Interior Gateway Routing Protocol (EIGRP)","BGP is the required routing protocol for establishing connectivity between your network and AWS over Direct Connect."
"Which AWS service can be used to simplify the management of network connectivity between multiple AWS accounts and on-premises networks when using Direct Connect?","Transit Gateway","Route 53","CloudFront","API Gateway","Transit Gateway simplifies the management of network connectivity between multiple VPCs, AWS accounts, and on-premises networks, making it a valuable resource for Direct Connect deployments."
"What is the key difference between AWS Direct Connect and AWS Storage Gateway?","Direct Connect provides a dedicated network connection, while Storage Gateway provides a way to access on-premises storage.","Direct Connect provides data encryption, while Storage Gateway does not.","Direct Connect is used for compute resources, while Storage Gateway is used for databases.","Direct Connect is free, while Storage Gateway has a cost associated with it.","Direct Connect provides a dedicated network connection, while Storage Gateway integrates on-premises storage with AWS cloud storage."
"What is the purpose of the Direct Connect 'Connection Owner Account'?","The AWS account that owns the Direct Connect port and associated resources","The AWS account that pays the bill for Direct Connect usage","The AWS account with administrative privileges over the Direct Connect connection","The AWS account that monitors the Direct Connect connection","The Connection Owner Account is the AWS account that owns the Direct Connect port and associated resources."
"If you have multiple VPCs in the same AWS region, can you use a single Direct Connect connection to access all of them?","Yes, by using the Direct Connect Gateway","No, you need a separate Direct Connect connection for each VPC","Yes, by configuring VPC peering","No, Direct Connect only works with one VPC per AWS account","The Direct Connect Gateway allows a single Direct Connect connection to access multiple VPCs in different regions."
"What is the function of the 'authorization key' when ordering an AWS Direct Connect connection?","Allows the connection owner to share the connection with other AWS accounts","Encrypts the data transmitted over the connection","Grants administrative access to the Direct Connect console","Determines the bandwidth allocation for the connection","The authorization key allows the connection owner to share the Direct Connect connection with other AWS accounts."
"If you are experiencing packet loss on your AWS Direct Connect connection, which troubleshooting step should you take first?","Check your router configuration for MTU and QoS settings","Increase the bandwidth of your Direct Connect connection","Reboot your AWS Direct Connect router","Contact AWS Support immediately","Checking MTU and QoS settings on your router is a crucial first step in troubleshooting packet loss issues on a Direct Connect connection."
"What is the purpose of an AWS Direct Connect 'MAC Address'?","To uniquely identify a network interface on the AWS side of the connection","To encrypt the data transmitted over the connection","To authenticate the user accessing the AWS console","To monitor the bandwidth usage of the connection","The MAC Address uniquely identifies a network interface on the AWS side of the connection."
"When sharing an AWS Direct Connect connection with another AWS account, who is responsible for managing the virtual interfaces?","The account that accepts the connection","The account that owns the connection","AWS","Both accounts share responsibility","The account that accepts the shared connection is responsible for managing the virtual interfaces."
"What is the maximum transmission unit (MTU) size for AWS Direct Connect private virtual interfaces when using jumbo frames?","9001 bytes","1500 bytes","576 bytes","68 bytes","The MTU size for Direct Connect private virtual interfaces when using jumbo frames is 9001 bytes."
"Which type of AWS Direct Connect connection is best suited for customers who need less than 1 Gbps bandwidth?","Hosted connection","Dedicated connection","Transit connection","Partner connection","Hosted connections are best suited for customers who need less than 1 Gbps bandwidth."
"What is the benefit of using Link Aggregation Control Protocol (LACP) with AWS Direct Connect?","Dynamic link management and improved redundancy","Automatic failover between AWS regions","Encryption of data in transit","Compression of network traffic","LACP provides dynamic link management and improved redundancy by bundling multiple physical connections into a single logical connection."
"What is the first step you should take when setting up an AWS Direct Connect connection?","Choose an AWS Direct Connect location near your data centre","Create a virtual private gateway (VGW)","Order a Direct Connect connection from the AWS Management Console","Configure your on-premises router","The first step in setting up an AWS Direct Connect connection is to order a Direct Connect connection from the AWS Management Console."
"What is the primary reason to use a public virtual interface (VIF) with AWS Direct Connect?","To access public AWS services, such as S3","To access resources within a VPC","To connect to multiple AWS regions","To encrypt network traffic","A public virtual interface is used to access public AWS services, such as S3, without traversing the public internet."
"What does AWS Direct Connect 'Resilience Toolkit' help you achieve?","Automate failover and recovery procedures for Direct Connect","Encrypt data in transit over Direct Connect","Compress network traffic to reduce bandwidth usage","Monitor network performance and identify bottlenecks","The Resilience Toolkit helps automate failover and recovery procedures for Direct Connect, improving network availability."
"Which AWS service helps you manage and monitor your network infrastructure, including AWS Direct Connect?","AWS CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS CloudWatch provides metrics and monitoring for your network infrastructure, including AWS Direct Connect."
"When configuring BGP for AWS Direct Connect, what is the recommended session authentication method?","MD5 authentication","No authentication","Password authentication","Certificate authentication","MD5 authentication is the recommended method for BGP session authentication in AWS Direct Connect."
"Which of the following AWS Direct Connect connection types offers the highest bandwidth capacity?","100 Gbps dedicated connection","10 Gbps dedicated connection","1 Gbps dedicated connection","50 Mbps hosted connection","100 Gbps dedicated connections offer the highest bandwidth capacity."
"What is the purpose of using pre-shared keys (PSK) when configuring a VPN connection over AWS Direct Connect?","To authenticate the VPN connection","To encrypt the data transmitted over the connection","To manage the security groups","To configure the routing policies","Pre-shared keys are used to authenticate the VPN connection, ensuring that only authorized parties can establish a connection."
"Which of the following components is required to terminate the Direct Connect link on your end?","Customer Gateway Device","Virtual Private Gateway","Transit Gateway","Internet Gateway","A Customer Gateway Device (your router) is required to terminate the Direct Connect link on your end."
"Which of the following tasks are you responsible for when setting up AWS Direct Connect?","Configuring routing on your Customer Gateway device","Provisioning the Direct Connect hardware at the AWS location","Maintaining the physical infrastructure at the AWS location","Managing AWS side firewall rules","You are responsible for configuring routing on your Customer Gateway device to ensure traffic is correctly routed to and from AWS."
"What is the advantage of using AWS Direct Connect over the public internet when accessing data in Amazon S3?","Improved data transfer speeds and reduced latency","Automatic encryption of data in transit","Lower cost for data transfer","Simplified network configuration","Direct Connect provides improved data transfer speeds and reduced latency compared to the public internet when accessing data in S3."
"When using a hosted connection with AWS Direct Connect, who is responsible for the physical connection to AWS?","The AWS Direct Connect Partner","The customer","AWS","Shared between AWS and the customer","With a hosted connection, the AWS Direct Connect Partner is responsible for the physical connection to AWS."
"Which of the following actions will result in the termination of your AWS Direct Connect service?","Deleting the Virtual Interfaces associated with the Direct Connect connection","Disabling encryption","Deleting the Customer Gateway device","Terminating all your EC2 Instances","Deleting the Virtual Interfaces (VIFs) will result in the termination of your Direct Connect service as they define how your traffic reaches AWS resources."
"What is the primary function of AWS Direct Connect?","Establishing a dedicated network connection from your premises to AWS","Providing a virtual private network connection to AWS","Offering a content delivery network service","Managing AWS Identity and Access Management (IAM) roles","Direct Connect provides a dedicated, private network connection from your on-premises environment to AWS, bypassing the public internet."
"Which AWS service is most commonly used in conjunction with AWS Direct Connect to extend your on-premises network into the AWS cloud?","Virtual Private Gateway (VGW)","Simple Queue Service (SQS)","Elastic Load Balancer (ELB)","CloudFront","A Virtual Private Gateway (VGW) is used as the Amazon side anchor for a Direct Connect connection, enabling you to access resources within your VPC."
"Which of the following is a benefit of using AWS Direct Connect over a VPN connection?","Reduced network latency","Simplified management","Lower cost","Increased scalability of the number of connections","Direct Connect typically offers lower latency compared to VPN connections due to the dedicated, private network connection."
"What is the term for the location where an AWS Direct Connect connection is established?","Direct Connect location","Availability Zone","Edge Location","AWS Region","Direct Connect connections are established at specific Direct Connect locations, which are co-location facilities."
"You need to encrypt data in transit over your AWS Direct Connect connection. What should you do?","Establish an IPsec VPN over the Direct Connect connection.","Use AWS Certificate Manager to encrypt the data.","Enable encryption on the Direct Connect router.","Use AWS KMS to encrypt data.","To encrypt data in transit over Direct Connect, you need to establish an IPsec VPN connection over the dedicated Direct Connect link."
"Which of the following bandwidth options is commonly available for AWS Direct Connect connections?","1 Gbps and 10 Gbps","500 Mbps and 1 Gbps","250 Mbps and 500 Mbps","100 Mbps and 250 Mbps","AWS Direct Connect commonly offers bandwidth options of 1 Gbps and 10 Gbps, along with the option to use hosted connections with lower bandwidths."
"What is the purpose of the Border Gateway Protocol (BGP) when using AWS Direct Connect?","To exchange routing information between your network and AWS","To encrypt data in transit","To monitor network performance","To manage network access control lists (ACLs)","BGP is used to exchange routing information between your network and AWS over the Direct Connect connection, allowing for dynamic routing of traffic."
"You have two Direct Connect connections to different AWS Regions. What is this setup commonly used for?","Disaster recovery and redundancy","Increased bandwidth for a single Region","Simplified network management","Reduced cost of data transfer","Having Direct Connect connections to multiple Regions is primarily used for disaster recovery and redundancy, providing failover in case of an issue in one Region."
"What is a 'LAG' in the context of AWS Direct Connect?","Link Aggregation Group","Latency Avoidance Gateway","Logical Access Group","Load Allocation Gateway","A Link Aggregation Group (LAG) allows you to bundle multiple Direct Connect connections into a single, logical connection for increased bandwidth and redundancy."
"When using AWS Direct Connect, which routing policy allows you to advertise the same prefixes to AWS in both public and private Virtual Interfaces (VIFs)?","Route filtering using BGP communities","As-path prepending","Local preference","Route summarization","Route filtering using BGP communities allows you to control which routes are advertised over public and private VIFs."
"What is the primary purpose of AWS Direct Connect?","Establishing a dedicated network connection from your premises to AWS","Providing VPN access to your AWS resources","Load balancing traffic across multiple AWS regions","Encrypting data at rest in S3","Direct Connect creates a private, dedicated network connection between your on-premises infrastructure and AWS, bypassing the public internet."
"Which AWS service can be used to monitor the performance and health of an AWS Direct Connect connection?","CloudWatch","CloudTrail","Trusted Advisor","Config","CloudWatch provides metrics and monitoring capabilities for Direct Connect connections, virtual interfaces, and bandwidth utilisation."
"Which of the following AWS Direct Connect connection types offers the highest bandwidth?","100 Gbps","1 Gbps","50 Mbps","10 Mbps","Direct Connect offers connection speeds up to 100 Gbps for high-bandwidth requirements."
"What is a 'Virtual Interface' (VIF) in the context of AWS Direct Connect?","A logical connection over a Direct Connect link","A physical network cable connecting to the Direct Connect router","A software defined network within AWS","A web-based management console for Direct Connect","A Virtual Interface allows you to access public and private AWS resources over a Direct Connect connection. It's a logical partition of the physical connection."
"What routing protocol is commonly used to exchange routes between your network and AWS over Direct Connect?","BGP (Border Gateway Protocol)","OSPF (Open Shortest Path First)","RIP (Routing Information Protocol)","ICMP (Internet Control Message Protocol)","BGP is the standard routing protocol used to exchange routing information between your network and AWS via Direct Connect. It allows for dynamic route advertisement and learning."
"In AWS Direct Connect, what is the purpose of the 'LAG' (Link Aggregation Group) feature?","To bundle multiple physical connections into a single logical connection","To automatically failover to a secondary Direct Connect location","To encrypt traffic traversing the Direct Connect connection","To prioritize traffic based on application requirements","LAG allows you to combine multiple Direct Connect connections to increase bandwidth and provide redundancy, treating them as a single logical connection."
"When establishing an AWS Direct Connect connection, which of the following is NOT a typical task for the customer?","Installing and maintaining the physical cross-connect at the colocation facility","Configuring AWS Identity and Access Management (IAM) roles","Creating virtual interfaces","Setting up BGP routing with AWS","The physical cross-connect is typically the responsibility of the customer but it needs to be coordinated by AWS."
"Which of the following is a benefit of using AWS Direct Connect over a traditional VPN connection?","More predictable network performance","Lower setup costs","Easier initial configuration","Automatic encryption of all traffic","Direct Connect offers more consistent and predictable network performance compared to VPN because it bypasses the public internet and provides a dedicated connection."
"What is the primary difference between a 'Public' and a 'Private' Virtual Interface (VIF) in AWS Direct Connect?","Public VIF accesses public AWS services; Private VIF accesses VPC resources","Public VIF is unencrypted; Private VIF is encrypted","Public VIF is more expensive; Private VIF is cheaper","Public VIF uses a different routing protocol than Private VIF","A Public VIF allows you to access public AWS services such as S3 and DynamoDB. A Private VIF allows you to access resources within your VPC."
"Which of the following AWS services is MOST suitable for securely extending your on-premises network to AWS for applications requiring low latency and high bandwidth?","AWS Direct Connect","AWS Site-to-Site VPN","AWS Storage Gateway","Amazon SQS","Direct Connect offers a dedicated network connection with lower latency and higher bandwidth compared to VPN, making it suitable for demanding applications."