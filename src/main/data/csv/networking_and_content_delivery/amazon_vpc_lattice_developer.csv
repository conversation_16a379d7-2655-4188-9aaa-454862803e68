"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"With Amazon VPC Lattice, what is the primary function of a Target Group?","To register compute resources that receive traffic","To define security group rules for services","To manage network ACLs","To store application logs","Target Groups register compute resources (e.g., EC2 instances, containers) and route requests to them based on defined health checks and routing rules."
"In Amazon VPC Lattice, what type of authentication can be used for service-to-service communication?","Mutual TLS (mTLS)","Basic Authentication","IP Address Filtering","Password Authentication","Mutual TLS (mTLS) provides strong, certificate-based authentication between services, ensuring secure communication."
"What is a key benefit of using Amazon VPC Lattice for service networking?","Simplified service discovery and traffic management","Reduced EC2 instance size requirements","Automated database backups","Increased EBS volume performance","VPC Lattice simplifies service discovery, traffic management, and security policies, allowing developers to focus on building applications."
"How does Amazon VPC Lattice handle service discovery?","Through integration with AWS Cloud Map","Using DNS records manually configured","By querying EC2 metadata","Via hardcoded IP addresses","VPC Lattice integrates with AWS Cloud Map, providing a central registry for service discovery within your VPC."
"In Amazon VPC Lattice, what is the purpose of a Listener?","To define how traffic is routed to target groups","To monitor application health","To create firewall rules","To configure load balancing algorithms","A Listener defines the protocol and port on which VPC Lattice accepts traffic and the rules that determine how that traffic is routed to target groups."
"Which AWS service does Amazon VPC Lattice integrate with for centralised governance and auditability?","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS IAM","AWS CloudTrail tracks API calls made to VPC Lattice, providing audit logs for governance and compliance."
"What is the primary advantage of using Amazon VPC Lattice over traditional load balancers for microservices?","Centralised service networking across multiple VPCs and accounts","Unlimited scaling of individual services","Automatic creation of IAM roles","Direct integration with on-premises networks","VPC Lattice allows you to create a single service network that spans multiple VPCs and AWS accounts, simplifying service-to-service communication."
"When using Amazon VPC Lattice, how can you implement traffic shifting for canary deployments?","By configuring weighted target group rules","Using DNS weighting policies","By manually updating routing tables","Via direct modification of application code","VPC Lattice supports weighted target group rules, allowing you to shift traffic gradually to new versions of your services for canary deployments."
"What is the maximum request size supported by Amazon VPC Lattice?","128 KB","64 KB","32 KB","256 KB","Amazon VPC Lattice supports requests up to 128 KB in size."
"How does Amazon VPC Lattice contribute to improved security posture?","By enforcing consistent security policies across services","By automatically patching EC2 instances","By encrypting data at rest in S3","By providing DDoS protection for databases","VPC Lattice enables you to define and enforce consistent security policies (e.g., authentication, authorisation) across all services in your service network."
"What is the purpose of Health Checks in Amazon VPC Lattice Target Groups?","To ensure that only healthy instances receive traffic","To monitor network latency","To verify security group configurations","To automatically scale EC2 instances","Health checks ensure that traffic is routed only to healthy instances in the target group, improving application availability and resilience."
"Which component in Amazon VPC Lattice is responsible for routing requests based on HTTP headers?","Listeners with rule-based routing","Target Groups","Service Networks","Service Registries","Listeners with rule-based routing can inspect HTTP headers and route traffic accordingly, enabling advanced routing strategies."
"What is the role of a Service Network in Amazon VPC Lattice?","To define a logical boundary for service-to-service communication","To store service configuration data","To provide DNS resolution for services","To manage IAM permissions","A Service Network provides a logical boundary within which services can communicate with each other, simplifying network management and security."
"How does Amazon VPC Lattice simplify the deployment of microservices?","By automating service registration and discovery","By automatically generating code for microservices","By providing a managed database solution","By handling container orchestration","VPC Lattice automates service registration and discovery, removing the need for manual configuration and reducing operational overhead."
"What is a key difference between Amazon VPC Lattice and a traditional Application Load Balancer (ALB)?","VPC Lattice provides service networking across multiple VPCs and accounts","ALB supports more protocols","ALB offers higher throughput","VPC Lattice integrates with on-premises networks","VPC Lattice extends the capabilities of load balancing to provide a service networking layer that spans multiple VPCs and accounts, enabling cross-account and cross-VPC service communication."
"Which authentication method is best suited for securing inter-service communication in Amazon VPC Lattice?","Mutual TLS (mTLS)","Basic Authentication","IP address filtering","API Keys","Mutual TLS provides strong authentication by verifying both the client and server using certificates, ensuring a high level of security for inter-service communication."
"What is the impact of using Amazon VPC Lattice on the complexity of service-to-service communication?","It simplifies service discovery and reduces network configuration overhead","It increases network latency due to additional hops","It requires manual management of DNS records","It complicates security policy enforcement","Amazon VPC Lattice simplifies service-to-service communication by providing a managed service networking layer that automates service discovery and reduces network configuration overhead."
"You have microservices running in different AWS accounts. How can you enable them to communicate securely using Amazon VPC Lattice?","By creating a service network that spans multiple accounts","By establishing VPC peering connections between accounts","By configuring public DNS records","By using a VPN connection","VPC Lattice allows you to create a service network that spans multiple AWS accounts, enabling secure and seamless communication between microservices running in different accounts."
"Which of the following is a key advantage of using Amazon VPC Lattice for managing traffic between microservices?","Centralised traffic management and observability","Reduced cost of EC2 instances","Simplified database administration","Increased EBS volume performance","Amazon VPC Lattice provides centralised traffic management and observability, allowing you to monitor and control traffic flow between microservices from a single pane of glass."
"What is the purpose of the service discovery feature in Amazon VPC Lattice?","To automatically discover and register microservices","To monitor the health of EC2 instances","To manage IAM roles and permissions","To generate code for microservices","Service discovery in Amazon VPC Lattice automatically discovers and registers microservices, simplifying the process of connecting services and reducing manual configuration."
"How can you use Amazon VPC Lattice to implement blue/green deployments for your microservices?","By configuring weighted target group rules to shift traffic between blue and green environments","By manually updating DNS records to point to the new environment","By using a separate load balancer for each environment","By terminating the old environment after deploying the new one","Weighted target group rules allow you to gradually shift traffic from the blue environment to the green environment, enabling seamless blue/green deployments."
"In Amazon VPC Lattice, what is the role of a 'rule' within a Listener?","To specify how traffic is routed based on conditions like HTTP headers or paths","To define security group rules for the listener","To configure health check settings for target groups","To set the protocol and port for the listener","A 'rule' within a Listener determines how traffic is routed to target groups based on specified conditions, such as HTTP headers, paths, or query parameters."
"When using Amazon VPC Lattice, how do you ensure that only authorised services can access a particular microservice?","By implementing mutual TLS (mTLS) authentication","By using security groups to filter traffic","By configuring network ACLs","By enabling VPC Flow Logs","Mutual TLS (mTLS) authentication ensures that both the client and server are authenticated using certificates, providing a secure and reliable method for authorising services."
"What is a key consideration when planning to migrate existing microservices to Amazon VPC Lattice?","Ensuring compatibility with existing service discovery mechanisms","Migrating databases to a different region","Re-architecting applications to use a different protocol","Switching to a different programming language","When migrating to VPC Lattice, ensure that your existing service discovery mechanisms are compatible, or plan to migrate to AWS Cloud Map for seamless integration."
"Which of the following best describes the 'service mesh' functionality provided by Amazon VPC Lattice?","It enables centralised control and observability of service-to-service traffic","It automatically scales EC2 instances based on traffic patterns","It provides a managed database solution for microservices","It generates code for microservices based on API definitions","VPC Lattice acts as a service mesh by providing centralised control and observability of service-to-service traffic, simplifying the management of microservice architectures."
"What type of load balancing does Amazon VPC Lattice primarily provide?","Application-layer (HTTP/HTTPS) load balancing","Network-layer (TCP/UDP) load balancing","Global load balancing","Database load balancing","Amazon VPC Lattice focuses on application-layer load balancing, enabling routing decisions based on HTTP headers, paths, and other application-level attributes."
"How does Amazon VPC Lattice integrate with AWS Identity and Access Management (IAM)?","To control access to VPC Lattice resources and services","To manage EC2 instance profiles","To configure security group rules","To create VPC endpoints","VPC Lattice integrates with IAM to control access to VPC Lattice resources and services, allowing you to define granular permissions for users and roles."
"What is the maximum number of target groups that can be associated with a Listener in Amazon VPC Lattice?","Multiple","One","Two","Three","A listener in VPC Lattice can be associated with multiple target groups, allowing for complex routing scenarios based on rules."
"Which of the following is a key benefit of using Amazon VPC Lattice in a multi-tenant environment?","Isolation and security between tenants","Simplified database management","Automated scaling of EC2 instances","Reduced cost of S3 storage","VPC Lattice provides isolation and security between tenants by allowing you to create separate service networks and enforce distinct security policies for each tenant."
"How can you monitor the performance of your microservices using Amazon VPC Lattice?","By integrating with Amazon CloudWatch for metrics and logs","By using VPC Flow Logs to capture network traffic","By enabling AWS X-Ray for distributed tracing","By analysing EC2 instance CPU utilisation","VPC Lattice integrates with Amazon CloudWatch for metrics and logs, providing detailed insights into the performance of your microservices and enabling you to identify and resolve issues quickly."
"What is the benefit of using Amazon VPC Lattice for traffic management in a containerised environment (e.g., ECS, EKS)?","Simplified service discovery and routing between containers","Automated deployment of container images","Reduced cost of container orchestration","Improved security of container registries","VPC Lattice simplifies service discovery and routing between containers, allowing you to build more scalable and resilient containerised applications."
"How does Amazon VPC Lattice handle authentication and authorisation for service-to-service communication?","Using mutual TLS (mTLS) and IAM policies","By relying on security groups only","By using network ACLs","By requiring hardcoded API keys","VPC Lattice uses mutual TLS (mTLS) for authentication and IAM policies for authorisation, ensuring secure and controlled access between services."
"Which of the following is a primary use case for Amazon VPC Lattice in a microservices architecture?","Enabling secure and controlled service-to-service communication","Managing database connections for microservices","Orchestrating container deployments","Monitoring EC2 instance CPU utilisation","VPC Lattice enables secure and controlled service-to-service communication, simplifying the management of microservices architectures."
"What is the relationship between Amazon VPC Lattice and AWS Cloud Map?","VPC Lattice uses AWS Cloud Map for service discovery","AWS Cloud Map uses VPC Lattice for traffic routing","They are completely independent services","VPC Lattice replaces AWS Cloud Map","VPC Lattice integrates with AWS Cloud Map for service discovery, allowing services to be registered and discovered dynamically."
"How can you use Amazon VPC Lattice to control access to a microservice based on the source IP address?","By configuring listener rules with IP-based conditions","By using security groups attached to the target group","By configuring network ACLs for the VPC","By enabling VPC Flow Logs","You can configure listener rules with IP-based conditions to control access to a microservice based on the source IP address."
"What is a key operational benefit of using Amazon VPC Lattice?","Simplified network management and reduced operational overhead","Automated database backups","Increased EBS volume performance","Reduced cost of EC2 instances","VPC Lattice simplifies network management and reduces operational overhead by providing a managed service networking layer."
"In Amazon VPC Lattice, what is the primary purpose of setting up a 'default' target group for a Listener?","To handle traffic that doesn't match any other rule","To monitor application health","To configure load balancing algorithms","To create firewall rules","The default target group handles traffic that doesn't match any of the other defined rules, ensuring that all requests are routed to a valid target."
"When using Amazon VPC Lattice with AWS Fargate, how do you register the Fargate tasks as targets?","By registering the Fargate tasks with the target group","By configuring the Fargate service to use the VPC Lattice endpoint","By using the Fargate launch type with VPC Lattice","By manually updating the DNS records for the Fargate tasks","You register the Fargate tasks with the target group in VPC Lattice, allowing traffic to be routed to the containers running in Fargate."
"Which AWS service is commonly used with Amazon VPC Lattice to implement observability and monitoring for microservices?","Amazon CloudWatch","AWS Config","AWS Trusted Advisor","AWS IAM","Amazon CloudWatch is commonly used with VPC Lattice to implement observability and monitoring for microservices, providing metrics, logs, and alarms."
"How can you use Amazon VPC Lattice to implement rate limiting for a microservice?","By configuring listener rules with rate limiting actions","By using AWS WAF to protect the endpoint","By configuring security group rules","By enabling VPC Flow Logs","You can configure listener rules with rate limiting actions to control the number of requests that a microservice can receive, protecting it from overload."
"In Amazon VPC Lattice, what is the significance of the 'priority' attribute when configuring listener rules?","It determines the order in which rules are evaluated","It specifies the weight for traffic distribution","It sets the CPU priority for the target group","It defines the security level for the listener","The 'priority' attribute determines the order in which rules are evaluated, with lower numbers indicating higher priority."
"Which of the following is a key security feature provided by Amazon VPC Lattice for microservices?","Mutual TLS (mTLS) authentication between services","Automated patching of EC2 instances","Encryption of data at rest in S3","DDoS protection for databases","VPC Lattice provides mutual TLS (mTLS) authentication between services, ensuring secure and authenticated communication."
"What is the maximum number of services that can be associated with a Service Network in Amazon VPC Lattice?","Multiple","One","Two","Three","A Service Network in VPC Lattice can be associated with multiple services, allowing you to manage a complex network of microservices."
"How does Amazon VPC Lattice contribute to faster application development cycles?","By simplifying service discovery and reducing network configuration","By automatically generating code for microservices","By providing a managed database solution","By handling container orchestration","VPC Lattice simplifies service discovery and reduces network configuration, allowing developers to focus on building features and reducing development time."
"When integrating Amazon VPC Lattice with Amazon ECS, how do you ensure that traffic is routed to the correct container instances?","By registering the ECS service as a target group in VPC Lattice","By configuring the ECS task definition to use the VPC Lattice endpoint","By using the ECS load balancer integration with VPC Lattice","By manually updating DNS records for the ECS service","You register the ECS service as a target group in VPC Lattice, allowing traffic to be routed to the correct container instances based on the service configuration."
"What is the primary purpose of using annotations with Amazon VPC Lattice?","Annotations allow you to associate metadata with resources for organizational purposes","Annotations are used to define security group rules for the Lattice service","Annotations are used to provide user authentication","Annotations are used to automatically scale the Lattice environment","Annotations are used to associate metadata with VPC Lattice resources. These annotations are useful for organizational and tagging purposes."
"When using Amazon VPC Lattice, what is a common reason you might choose to create multiple Listeners on a single service?","To handle traffic on different ports or protocols","To load balance traffic across multiple availability zones","To isolate traffic between different microservices","To create separate security groups for different traffic types","Multiple Listeners are often created to manage incoming requests on different ports or using different protocols (e.g., HTTP vs. HTTPS) for the same service."
"How does Amazon VPC Lattice help to reduce the operational burden associated with managing a large number of microservices?","By automating service discovery, traffic management, and security policies","By automatically generating code for microservices","By providing a managed database solution","By handling container orchestration","Amazon VPC Lattice automates service discovery, traffic management, and security policies, which significantly reduces the operational burden of managing a large number of microservices."
"What is the primary function of Amazon VPC Lattice?","To simplify service-to-service communication in a microservices architecture","To provide a managed NAT gateway","To replace VPC peering connections","To act as a firewall for EC2 instances","VPC Lattice provides a simplified way to connect, secure, and monitor service-to-service communication for microservices applications."
"In Amazon VPC Lattice, what is a 'target'?","A compute resource (e.g., EC2 instance, container) registered with a target group","A security group associated with a service","A subnet within the VPC","An IAM role used for authentication","A 'target' in VPC Lattice represents the actual compute resource that is part of a service and handles requests."
"What is a 'listener' in the context of Amazon VPC Lattice?","A process that checks for incoming traffic on a specific port and protocol","A rule that determines which target group receives traffic","A log that records all network activity","A monitoring dashboard for service health","A 'listener' in VPC Lattice checks for incoming traffic on a specified port and protocol, directing it to the appropriate target group."
"Which of the following is a key benefit of using Amazon VPC Lattice for service networking?","Centralised traffic management and observability","Automated scaling of EC2 instances","Simplified database administration","Improved DNS resolution across VPCs","VPC Lattice provides centralised control and visibility over service-to-service traffic, enabling better management and troubleshooting."
"How does Amazon VPC Lattice handle service discovery?","It uses DNS-based service discovery integrated with AWS Cloud Map","It relies on static IP addresses for each service","It requires manual configuration of service endpoints","It uses a custom service discovery protocol","VPC Lattice integrates with AWS Cloud Map to provide DNS-based service discovery, allowing services to find each other dynamically."
"What type of authentication is supported by Amazon VPC Lattice?","Mutual TLS (mTLS)","Password-based authentication","Kerberos","LDAP","VPC Lattice supports mutual TLS (mTLS) for strong authentication between services."
"What is the purpose of a 'rule' in an Amazon VPC Lattice listener configuration?","To define the conditions under which traffic is forwarded to a target group","To specify the security group for the service","To configure the logging level for the service","To set the health check interval for targets","A 'rule' in a VPC Lattice listener defines the conditions (e.g., path-based routing, header-based routing) for forwarding traffic to a specific target group."
"Which AWS service is commonly used in conjunction with Amazon VPC Lattice for service discovery?","AWS Cloud Map","AWS Route 53","AWS Directory Service","AWS Systems Manager","AWS Cloud Map is frequently integrated with VPC Lattice to provide a managed service registry for service discovery."
"How can you implement blue/green deployments using Amazon VPC Lattice?","By creating separate target groups for the blue and green environments and switching traffic between them using listener rules","By using AWS CodeDeploy to deploy the new version to the same target group","By manually updating the DNS records for the service","By using rolling updates within the same target group","Blue/green deployments can be implemented by having separate target groups for the blue and green environments, and then using listener rules to shift traffic between them."
"What type of traffic can Amazon VPC Lattice handle?","HTTP and gRPC traffic","Only HTTP traffic","Only TCP traffic","Any type of network traffic","VPC Lattice is designed to handle HTTP and gRPC traffic, common protocols for microservices communication."
"What is the difference between a 'service network' and a 'service' in Amazon VPC Lattice?","A service network is a collection of services; a service is a single application component.","A service network is the same as a service.","A service is a collection of service networks; a service network is a single application component.","A service network is used for internal traffic only, while a service is used for external traffic.","A service network is a logical grouping of services that can communicate with each other, while a service represents a single application component."
"How can you control access to services within an Amazon VPC Lattice service network?","By using IAM policies attached to the service network and services","By configuring security groups on the target groups","By using Network ACLs on the subnets","By configuring route tables in the VPC","VPC Lattice uses IAM policies attached to the service network and services to control access and authorisation."
"When configuring health checks for targets in Amazon VPC Lattice, what is the purpose of the 'unhealthy threshold'?","The number of consecutive health check failures before a target is considered unhealthy","The number of consecutive health check successes before a target is considered healthy","The interval between health checks","The timeout period for a health check","The 'unhealthy threshold' defines how many consecutive health check failures must occur before a target is considered unhealthy and removed from service."
"Which of the following is a typical use case for Amazon VPC Lattice?","Connecting microservices running in different VPCs and accounts","Replacing a traditional load balancer for a monolithic application","Creating a VPN connection between two data centres","Managing network access control lists (NACLs)","VPC Lattice is particularly useful for connecting microservices that may be running in different VPCs and even different AWS accounts, simplifying cross-account and cross-VPC communication."
"How does Amazon VPC Lattice improve observability for service-to-service communication?","By providing detailed traffic logs and metrics that can be integrated with monitoring tools","By automatically creating CloudWatch dashboards for each service","By enabling tracing across all services in the network","By providing real-time alerts for service failures","VPC Lattice enhances observability by providing detailed traffic logs and metrics, which can be integrated with monitoring tools like CloudWatch and Prometheus."
"What is the benefit of using path-based routing in Amazon VPC Lattice?","It allows you to route traffic to different target groups based on the URL path","It improves the security of your services by filtering traffic based on the URL path","It reduces the latency of requests by optimising the routing path","It simplifies the configuration of health checks by using the URL path","Path-based routing allows you to direct traffic to different target groups based on the URL path, enabling scenarios like versioning and A/B testing."
"Which of the following is an advantage of using gRPC with Amazon VPC Lattice?","gRPC provides efficient binary serialisation and supports streaming requests","gRPC is simpler to configure than HTTP","gRPC is more widely supported by web browsers than HTTP","gRPC automatically encrypts all traffic","gRPC offers efficient binary serialisation and support for streaming requests, making it suitable for high-performance microservices communication."
"How does Amazon VPC Lattice simplify cross-account service access?","It allows you to share service networks and services across AWS accounts using Resource Access Manager (RAM)","It automatically creates VPC peering connections between accounts","It uses IAM roles to grant access to services in other accounts","It allows you to directly modify the security groups in other accounts","VPC Lattice simplifies cross-account service access by allowing you to share service networks and services across AWS accounts using Resource Access Manager (RAM)."
"What security benefit does mutual TLS (mTLS) provide in Amazon VPC Lattice?","It verifies the identity of both the client and the server, preventing man-in-the-middle attacks","It automatically encrypts all traffic between services","It blocks traffic from untrusted IP addresses","It protects against DDoS attacks","Mutual TLS (mTLS) provides strong authentication by verifying the identity of both the client and the server, helping to prevent man-in-the-middle attacks."
"How can you integrate Amazon VPC Lattice with your existing CI/CD pipeline?","By using AWS CloudFormation or Terraform to automate the deployment of service networks and services","By manually configuring the service network after each deployment","By using a custom script to register services with the service network","By deploying the service network using the AWS Management Console","You can integrate VPC Lattice with your CI/CD pipeline by using infrastructure-as-code tools like AWS CloudFormation or Terraform to automate the deployment of service networks and services."
"What is the purpose of the 'access log' feature in Amazon VPC Lattice?","To record all requests and responses that pass through the service network","To monitor the CPU utilisation of the target instances","To track changes to the service network configuration","To troubleshoot network connectivity issues","The 'access log' feature in VPC Lattice records all requests and responses that pass through the service network, providing valuable insights for auditing, debugging, and security analysis."
"Which of the following factors influences the cost of using Amazon VPC Lattice?","The amount of data processed, the number of requests, and the duration of service usage","The number of VPCs connected to the service network","The number of target groups configured","The amount of storage used by the access logs","The cost of using VPC Lattice depends on factors such as the amount of data processed, the number of requests, and the duration of service usage."
"How does Amazon VPC Lattice handle upgrades and maintenance?","AWS manages upgrades and maintenance transparently, without requiring any action from the user","Users are responsible for scheduling and performing upgrades manually","VPC Lattice automatically upgrades services during off-peak hours","Users need to create new service networks for each upgrade","AWS manages upgrades and maintenance transparently for VPC Lattice, without requiring any intervention from the user."
"What is the purpose of the 'default action' in an Amazon VPC Lattice listener?","To specify the target group that receives traffic when no rules match","To define the default security group for the service","To configure the default logging level","To set the default health check settings","The 'default action' in a VPC Lattice listener specifies the target group that receives traffic when none of the defined rules match."
"How can you monitor the health of your services in Amazon VPC Lattice?","By using the built-in health check feature and integrating with Amazon CloudWatch","By manually checking the logs of each target instance","By using a third-party monitoring tool","By relying on the AWS Service Health Dashboard","You can monitor the health of your services by using the built-in health check feature in VPC Lattice and integrating it with Amazon CloudWatch for metrics and alerting."
"What is the maximum number of services that can be associated with a single Amazon VPC Lattice service network?","There is no fixed limit, but it is subject to service quotas.","10","100","1000","While there isn't a hard-coded limit, the number of services you can associate with a service network is subject to service quotas, which can be adjusted as needed."
"How does Amazon VPC Lattice help in achieving zero-trust security?","By providing granular access control policies and mutual TLS authentication","By automatically encrypting all traffic between services","By replacing traditional firewalls","By isolating services in separate VPCs","VPC Lattice contributes to zero-trust security by offering granular access control policies through IAM and implementing mutual TLS (mTLS) for strong authentication between services."
"Which of the following components is NOT directly part of Amazon VPC Lattice?","API Gateway","Service Network","Listener","Target Group","API Gateway is not a direct component of VPC Lattice, although you might use API Gateway alongside VPC Lattice for external access to services."
"How does Amazon VPC Lattice support canary deployments?","By routing a small percentage of traffic to a new version of a service using listener rules","By automatically creating a canary instance of each service","By using AWS CodeDeploy to deploy the canary release","By manually configuring the routing rules for the canary deployment","Canary deployments can be implemented by routing a small percentage of traffic to a new version of a service using listener rules, allowing you to test changes in production with minimal risk."
"What is the purpose of attaching an IAM policy to a service network in Amazon VPC Lattice?","To control which services and resources can access the service network","To define the security group rules for the service network","To configure the logging settings for the service network","To specify the health check configuration for the service network","Attaching an IAM policy to a service network allows you to control which services and AWS resources have permission to access the resources within that service network."
"How can you troubleshoot connectivity issues within an Amazon VPC Lattice service network?","By using the access logs, metrics, and CloudWatch integration to identify and diagnose problems","By manually pinging the target instances","By analysing the VPC flow logs","By using a network packet analyser","You can troubleshoot connectivity issues by using VPC Lattice's access logs, metrics, and integration with CloudWatch to identify and diagnose network problems."
"What is the role of a 'principal' in an Amazon VPC Lattice IAM policy?","The AWS account, IAM user, IAM role, or AWS service that is granted or denied access","The service network to which the policy is attached","The type of traffic being allowed or denied","The specific target group being accessed","In an IAM policy for VPC Lattice, the 'principal' specifies the AWS account, IAM user, IAM role, or AWS service that is being granted or denied access to the resources governed by the policy."
"How can you ensure high availability for services running in Amazon VPC Lattice?","By deploying targets across multiple Availability Zones","By enabling Multi-AZ for the service network","By using AWS Global Accelerator","By configuring a warm standby service","You can ensure high availability by deploying targets across multiple Availability Zones (AZs), ensuring that your service remains available even if one AZ experiences an outage."
"What is the purpose of configuring 'mutual authentication' in Amazon VPC Lattice?","To ensure that both the client and server verify each other's identities before establishing a connection","To encrypt all traffic between services","To prevent DDoS attacks","To restrict access to specific IP addresses","Configuring 'mutual authentication' (mTLS) ensures that both the client and server verify each other's identities before establishing a connection, providing a higher level of security."
"What type of monitoring is provided by Amazon VPC Lattice?","Real-time traffic metrics, access logs, and health check status","CPU utilisation of the target instances","Network latency between VPCs","Database query performance","VPC Lattice provides real-time traffic metrics, access logs, and health check status, which can be used to monitor the performance and health of your services."
"Which of the following AWS services can be used to collect and analyse logs from Amazon VPC Lattice?","Amazon CloudWatch Logs","Amazon S3","Amazon Athena","Amazon EBS","Amazon CloudWatch Logs is commonly used to collect and analyse logs generated by VPC Lattice, enabling you to monitor traffic and troubleshoot issues."
"What is the benefit of using Amazon VPC Lattice for microservices communication compared to traditional load balancers?","VPC Lattice provides service-to-service authentication and authorisation, centralised traffic management, and enhanced observability, features not typically available in traditional load balancers.","Traditional load balancers are less expensive.","Traditional load balancers can handle more traffic.","Traditional load balancers are easier to configure.","VPC Lattice offers service-to-service authentication and authorisation, centralised traffic management, and enhanced observability, functionalities not usually available in traditional load balancers, making it ideal for microservices architectures."
"What is the difference between Amazon VPC Lattice and AWS PrivateLink?","VPC Lattice is for service-to-service communication within and across VPCs, while PrivateLink is for securely exposing services to other VPCs or AWS accounts without using public IPs.","VPC Lattice is for external access to services, while PrivateLink is for internal access.","VPC Lattice is for HTTP traffic only, while PrivateLink is for TCP traffic.","VPC Lattice is less secure than PrivateLink.","VPC Lattice focuses on service-to-service communication within and across VPCs, providing a service mesh-like experience, while PrivateLink allows you to securely expose services to other VPCs or AWS accounts without using public IPs, creating a private connection."
"You want to allow only services within the same Amazon VPC Lattice service network to communicate with each other. How can you achieve this?","By using IAM policies that restrict access to the service network","By configuring security groups on the target groups","By using Network ACLs on the subnets","By disabling public access to the services","You can use IAM policies attached to the service network to restrict access, ensuring that only services within the same service network can communicate with each other."
"In Amazon VPC Lattice, what does the term 'service discovery' refer to?","The automatic detection and registration of services within the service network","The manual configuration of service endpoints","The process of configuring health checks for services","The process of monitoring the performance of services","Service discovery in VPC Lattice refers to the automatic detection and registration of services within the service network, allowing services to dynamically locate and communicate with each other."
"What is the primary use case for configuring a 'Weighted Target Group' in Amazon VPC Lattice?","To gradually roll out new versions of a service using weighted routing","To create a backup target group in case the primary target group fails","To load balance traffic across different regions","To prioritise traffic to specific target groups based on their performance","Weighted Target Groups are primarily used to gradually roll out new versions of a service, allowing you to send a percentage of traffic to the new version while monitoring its performance."
"Which component in Amazon VPC Lattice determines how incoming requests are routed to target groups based on specific conditions?","Listener rules","Target groups","Service networks","Access logs","Listener rules are used to define the conditions under which incoming requests are routed to specific target groups, enabling flexible routing based on factors like path, headers, or query parameters."
"You have multiple applications running within the same Amazon VPC. How can you use VPC Lattice to isolate network traffic between these applications?","By creating separate service networks for each application and using IAM policies to control access","By creating separate VPCs for each application","By using security groups to block traffic between the applications","By using Network ACLs to block traffic between the applications","You can create separate service networks for each application and use IAM policies to control access, ensuring that network traffic is isolated between the applications, even if they reside within the same VPC."
"What is the benefit of using the 'Least Outstanding Requests' routing algorithm in Amazon VPC Lattice?","It routes traffic to the target with the fewest active requests, improving response times","It routes traffic to the target with the lowest CPU utilisation","It routes traffic to the target with the most available memory","It routes traffic randomly to targets for even distribution","The 'Least Outstanding Requests' routing algorithm routes traffic to the target with the fewest active requests, helping to ensure that no single target is overwhelmed and improving overall response times."
"How does Amazon VPC Lattice help with compliance requirements?","By providing detailed access logs and audit trails of all service-to-service communication","By automatically encrypting all data at rest","By enforcing strict password policies","By providing built-in vulnerability scanning","VPC Lattice helps with compliance by providing detailed access logs and audit trails of all service-to-service communication, which can be used to demonstrate adherence to security and regulatory standards."
"What is the significance of setting up health checks in Amazon VPC Lattice?","To automatically remove unhealthy targets from the service network","To monitor the CPU utilization of targets","To ensure that all targets are running the latest version of the application","To prevent DDoS attacks","Health checks in VPC Lattice automatically remove unhealthy targets from the service network, ensuring that traffic is only routed to healthy and responsive instances."
"How can you enable cross-account access to services in Amazon VPC Lattice?","Using Resource Access Manager (RAM) to share the service network","Creating VPC peering connections between accounts","Using IAM roles to grant access to services in other accounts","Manually configuring the security groups in other accounts","Cross-account access to services in VPC Lattice is enabled by using Resource Access Manager (RAM) to share the service network with other AWS accounts."
"What is the primary purpose of VPC Lattice's integration with AWS Cloud Map?","To provide a service registry for service discovery","To manage VPC routing tables","To configure DNS settings for the VPC","To monitor the health of EC2 instances","The primary purpose of VPC Lattice's integration with AWS Cloud Map is to provide a service registry for service discovery, allowing services to locate each other dynamically based on their names and attributes."
"What is the key reason to use mutual TLS (mTLS) in Amazon VPC Lattice?","Enhanced security through mutual authentication between services","Improved network performance","Reduced operational overhead","Simplified service discovery","The key reason to use mutual TLS (mTLS) in Amazon VPC Lattice is to enhance security through mutual authentication, where both the client and the server verify each other's identities, preventing unauthorised access and man-in-the-middle attacks."
"You want to monitor the overall health and performance of your Amazon VPC Lattice service network. Which AWS service should you integrate with?","Amazon CloudWatch","AWS CloudTrail","AWS Config","Amazon Inspector","Amazon CloudWatch provides comprehensive monitoring capabilities, including metrics, logs, and alarms, making it the ideal service to integrate with for monitoring the health and performance of your VPC Lattice service network."
"What is the primary function of Amazon VPC Lattice?","To simplify service-to-service communication within and across VPCs and accounts","To provide a central repository for all application code","To act as a firewall for all inbound and outbound traffic","To manage EC2 instance patching and updates","VPC Lattice simplifies service-to-service communication by providing a consistent way to connect, secure, and monitor services regardless of their location (VPC or account)."
"In Amazon VPC Lattice, what is a 'Service'?","A logical grouping of compute resources that provides a specific functionality","A virtual machine instance running within a VPC","A collection of network access control lists","A static IP address assigned to an EC2 instance","In VPC Lattice, a 'Service' represents a logical grouping of compute resources (e.g., EC2 instances, containers, Lambda functions) that provides a specific functionality and can be accessed via Lattice."
"Which of the following is a key benefit of using Amazon VPC Lattice for microservices architecture?","Simplified service discovery and connectivity","Automatic scaling of EC2 instances","Centralised management of IAM roles","Automated creation of VPCs","VPC Lattice simplifies service discovery and connectivity by providing a centralised platform for managing service interactions."
"What type of authentication can be used with Amazon VPC Lattice for service-to-service communication?","Mutual TLS (mTLS)","Basic Authentication","IP Address filtering","Username/Password authentication","VPC Lattice supports mutual TLS (mTLS) for secure authentication between services."
"What is the purpose of a 'Target Group' in Amazon VPC Lattice?","To define the compute resources that comprise a service","To configure routing policies for network traffic","To specify security group rules for EC2 instances","To manage IAM permissions for users and groups","A 'Target Group' in VPC Lattice defines the compute resources (e.g., EC2 instances, containers) that comprise a service.  It allows Lattice to send traffic to the healthy targets."
"How does Amazon VPC Lattice integrate with AWS Cloud Map?","VPC Lattice can use Cloud Map to discover services","VPC Lattice automatically creates Cloud Map namespaces","VPC Lattice replicates Cloud Map data","VPC Lattice replaces Cloud Map functionality","VPC Lattice can integrate with Cloud Map allowing it to discover and connect to services registered within Cloud Map."
"Which layer of the OSI model does Amazon VPC Lattice primarily operate at?","Layer 7 (Application Layer)","Layer 4 (Transport Layer)","Layer 3 (Network Layer)","Layer 2 (Data Link Layer)","VPC Lattice primarily operates at Layer 7, enabling application-level routing, authentication, and observability."
"What type of traffic routing is supported by Amazon VPC Lattice?","Content-based routing","IP address-based routing","Port-based routing","MAC address-based routing","VPC Lattice supports content-based routing, allowing traffic to be routed based on the content of the request (e.g., headers, path)."
"What is the purpose of 'Listeners' in Amazon VPC Lattice?","To define the ports and protocols that a service accepts traffic on","To configure security group rules for a service","To manage IAM permissions for a service","To define health check configurations for a service","'Listeners' in VPC Lattice define the ports and protocols that a service accepts traffic on, enabling you to control how clients can connect to the service."
"How can you monitor traffic flowing through Amazon VPC Lattice?","Using Amazon CloudWatch metrics and logs","Using VPC Flow Logs","Using AWS Config","Using AWS Trusted Advisor","VPC Lattice provides comprehensive observability through Amazon CloudWatch metrics and logs, allowing you to monitor traffic patterns, identify bottlenecks, and troubleshoot issues."
"Which AWS service can be used to centrally manage VPC Lattice service network associations and policies?","AWS Resource Access Manager (RAM)","AWS Systems Manager","AWS CloudTrail","AWS Config","AWS Resource Access Manager (RAM) can be used to centrally manage and share VPC Lattice service network associations and policies across multiple AWS accounts."
"What is a 'Service Network' in Amazon VPC Lattice?","A logical boundary for grouping and managing services","A virtual private cloud","A subnet within a VPC","A collection of security group rules","A 'Service Network' in VPC Lattice is a logical boundary that allows you to group and manage services, making it easier to control access and routing policies."
"How does Amazon VPC Lattice help improve the security posture of microservices applications?","By providing built-in authentication and authorisation mechanisms","By automatically patching vulnerabilities in EC2 instances","By enforcing network access control lists","By providing automated backups of application data","VPC Lattice enhances security through built-in authentication and authorisation mechanisms like mTLS."
"What type of health checks are supported by Amazon VPC Lattice?","HTTP and TCP health checks","ICMP health checks","DNS health checks","UDP health checks","VPC Lattice supports HTTP and TCP health checks to ensure that traffic is only routed to healthy service instances."
"How can you implement blue/green deployments using Amazon VPC Lattice?","By using traffic shifting based on weighted target groups","By using AWS CodeDeploy for deployment automation","By using Route 53 for DNS-based routing","By using EC2 Auto Scaling groups","VPC Lattice supports traffic shifting based on weighted target groups, allowing you to gradually shift traffic from the blue environment to the green environment during deployments."
"What is the maximum number of services that can be associated with a single Amazon VPC Lattice service network?","Limited by AWS account quotas","Limited by the size of the VPC","Limited by the number of subnets","Limited by the number of security groups","The maximum number of services that can be associated with a single Amazon VPC Lattice service network is limited by AWS account quotas.  Consult the AWS documentation for current limits."
"What is the benefit of using Amazon VPC Lattice for inter-VPC communication compared to VPC peering?","Simplified management and security policies","Higher bandwidth and lower latency","Automatic network address translation (NAT)","Native IPv6 support","VPC Lattice simplifies inter-VPC communication by providing a centralised management plane and consistent security policies, eliminating the need for complex VPC peering configurations."
"How does Amazon VPC Lattice handle service discovery?","Through integration with AWS Cloud Map or using Lattice's own service registry","By using DNS records in Route 53","By using EC2 instance metadata","By using static IP addresses","VPC Lattice handles service discovery through integration with AWS Cloud Map or using its own built-in service registry, making it easier to locate and connect to services."
"What type of monitoring data can you collect using Amazon VPC Lattice?","Request latency, error rates, and traffic volume","CPU utilisation, memory usage, and disk I/O","Network bandwidth, packet loss, and round-trip time","Security group rules, IAM policies, and AWS Config rules","VPC Lattice allows you to collect request latency, error rates, and traffic volume to provide insights into service performance and health."
"How can you control access to services within an Amazon VPC Lattice service network?","By using IAM policies and Lattice service policies","By using network access control lists (ACLs)","By using security group rules","By using Route 53 policies","Access to services within a VPC Lattice service network can be controlled using IAM policies and Lattice service policies, allowing you to define who can access which services."
"Which of the following is a use case for Amazon VPC Lattice?","Modernising monolithic applications into microservices","Replacing traditional load balancers","Migrating data between on-premises and AWS","Managing EC2 instance lifecycles","VPC Lattice is particularly well-suited for modernising monolithic applications into microservices, simplifying service-to-service communication."
"What is the difference between a VPC Endpoint and a VPC Lattice Service Network?","A VPC Endpoint provides access to AWS services, while a Lattice Service Network manages service-to-service communication within your applications","A VPC Endpoint manages service-to-service communication, while a Lattice Service Network provides access to AWS services","They both perform the same function, providing network connectivity to services","A VPC Endpoint is used for internet access, while a Lattice Service Network is used for internal network access","A VPC Endpoint provides private connectivity to AWS services without using the public internet, whereas a VPC Lattice Service Network manages service-to-service communication within and across VPCs and accounts."
"Which of the following is NOT a core component of Amazon VPC Lattice?","Network ACL","Service","Service Network","Target Group","Network ACL is not a core component of VPC Lattice. VPC Lattice focuses on application-level networking, using Services, Service Networks, and Target Groups to manage traffic."
"What does Amazon VPC Lattice use for routing traffic between services?","Application layer information (e.g., HTTP headers)","IP addresses","MAC addresses","Port numbers","VPC Lattice uses application layer information like HTTP headers for content-based routing, enabling more sophisticated traffic management compared to traditional IP or port-based routing."
"Which security principle is heavily used within Amazon VPC Lattice to control service access?","Least Privilege","Defense in Depth","Shared Responsibility","Zero Trust","VPC Lattice promotes the principle of Least Privilege by allowing you to precisely define which services can access other services via IAM and service policies."
"What type of authentication is enabled using Mutual TLS (mTLS) in Amazon VPC Lattice?","Both the client and server verify each other's identities","Only the server verifies the client's identity","Only the client verifies the server's identity","No authentication is performed, mTLS only encrypts the traffic","Mutual TLS provides enhanced security by requiring both the client and the server to authenticate each other, ensuring that both parties are who they claim to be."
"How can you monitor the health of your services integrated with Amazon VPC Lattice?","By configuring HTTP or TCP health checks on Target Groups","By using VPC Flow Logs","By examining the logs of the EC2 instances","By using AWS Trusted Advisor","VPC Lattice enables monitoring of service health by configuring HTTP or TCP health checks on Target Groups, automatically routing traffic away from unhealthy instances."
"Which of the following is a primary advantage of using Amazon VPC Lattice for cross-account service communication?","Simplified cross-account network management and security","Lower latency for cross-account traffic","Automatic data encryption for cross-account data transfer","Free data transfer between accounts","VPC Lattice simplifies cross-account network management and security by providing a centralised platform for defining and enforcing communication policies across accounts."
"What is a key use case for content-based routing in Amazon VPC Lattice?","Routing traffic to different versions of a service based on HTTP headers","Routing traffic based on the source IP address","Routing traffic based on the port number","Routing traffic based on the time of day","Content-based routing enables the distribution of traffic based on characteristics like HTTP headers, allowing different versions or implementations of a service to handle specific request types."
"What is the role of AWS Resource Access Manager (RAM) in the context of Amazon VPC Lattice?","Sharing Service Networks and associated resources across AWS accounts","Managing IAM roles and policies","Monitoring the performance of Lattice services","Creating and managing VPCs","AWS RAM enables you to share VPC Lattice Service Networks and their associated resources across different AWS accounts, simplifying cross-account management."
"How does Amazon VPC Lattice contribute to observability in a microservices environment?","Provides detailed metrics and logs of service-to-service communication","Replaces the need for application-level logging","Automatically detects and resolves application errors","Offers a real-time dashboard of all application traffic","VPC Lattice enhances observability by providing detailed metrics and logs, which help monitor service interactions, identify bottlenecks, and troubleshoot issues."
"You want to migrate from a monolithic application to microservices. How can Amazon VPC Lattice assist in this process?","By simplifying service discovery, traffic management and security for the newly created microservices.","By automatically rewriting the code of the monolithic application into microservices.","By providing a database migration service to move data to microservices-specific databases.","By managing the entire deployment process of the microservices infrastructure.","VPC Lattice facilitates the migration to microservices by streamlining service discovery, traffic management, and security, making it easier to manage interactions between the new services."
"Which method can be used to control access to services in a VPC Lattice Service Network?","Using IAM policies","Using VPC Flow Logs","Using EC2 Security Groups","Using Network Access Control Lists","IAM policies are the primary mechanism for controlling access to services within a VPC Lattice Service Network, enabling fine-grained authorisation based on IAM principles."
"What type of health checks are used by Amazon VPC Lattice to determine if a target is ready to receive traffic?","HTTP and TCP health checks","Ping (ICMP) health checks","DNS health checks","UDP health checks","VPC Lattice utilises HTTP and TCP health checks to ensure that traffic is directed only to healthy targets, improving application availability and reliability."
"When you are using Amazon VPC Lattice, how can you implement a canary deployment strategy?","By using weighted target groups to gradually shift traffic to the new version.","By using EC2 Auto Scaling groups.","By using AWS CodeDeploy.","By using Amazon CloudFront.","Weighted target groups in VPC Lattice allow you to gradually shift traffic to a new version of a service, enabling canary deployments for testing and validation before full rollout."
"You have several microservices deployed in different VPCs. What advantage does Amazon VPC Lattice provide in this scenario?","Simplified connectivity and security management across all VPCs","Automatic scaling of EC2 instances in all VPCs","Centralised logging for all VPC traffic","Real-time monitoring of CPU utilisation in all VPCs","VPC Lattice simplifies connectivity and security management across different VPCs, eliminating the complexities of VPC peering and individual security group configurations."
"What is the relationship between a VPC Lattice Listener and a Target Group?","A Listener defines how traffic is accepted by a service, and a Target Group specifies the destinations for that traffic.","A Listener specifies the type of EC2 Instance that may be used, and a Target Group specifies where the logs are stored.","A Listener specifies the ports to use for the service, and a Target Group specifies the allowed users.","A Listener specifies the traffic origins and Target Group specifies the allowed IPs for a service.","A Listener defines the protocols and ports on which a service accepts traffic, while the Target Group specifies the backend instances or containers to which the listener forwards that traffic."
"Which of the following is a key benefit of using Amazon VPC Lattice over traditional API Gateways for internal microservices communication?","Simplified service-to-service communication and governance","Lower latency for API requests","Automatic API documentation generation","Built-in DDoS protection","VPC Lattice is specifically designed for service-to-service communication within internal microservices architectures, providing a simpler and more scalable solution compared to API Gateways, which are primarily designed for external API exposure."
"How does Amazon VPC Lattice integrate with existing VPC infrastructure?","It can be associated with one or more VPCs","It replaces the existing VPC infrastructure","It runs outside of any VPC","It modifies existing VPC routing tables","VPC Lattice can be associated with one or more VPCs, allowing services in those VPCs to communicate with each other through the Lattice service network."
"When using Amazon VPC Lattice, what happens if a service becomes unhealthy?","Traffic is automatically routed to healthy services in the same Target Group","The service is automatically restarted","The service is automatically removed from the Service Network","The service's DNS record is automatically updated","VPC Lattice automatically detects unhealthy services and routes traffic only to the healthy ones within the same Target Group, ensuring high availability and resilience."
"Which AWS service provides a service registry that can be integrated with Amazon VPC Lattice?","AWS Cloud Map","AWS Service Catalog","AWS Systems Manager Parameter Store","AWS Directory Service","AWS Cloud Map provides a service registry that can be integrated with VPC Lattice, allowing services to discover and connect to each other dynamically."
"How does Amazon VPC Lattice support zero-trust networking principles?","By requiring mutual TLS (mTLS) authentication between services","By automatically encrypting all network traffic","By enforcing strict network access control lists","By providing detailed audit logs of all network activity","VPC Lattice supports zero-trust networking by requiring mutual TLS (mTLS) authentication between services, ensuring that every service verifies the identity of the other before communication is allowed."
"What is the primary benefit of using Amazon VPC Lattice for hybrid cloud environments?","Seamlessly connect services running in AWS and on-premises environments","Automatically migrate applications from on-premises to AWS","Securely store data in both AWS and on-premises environments","Centrally manage all IT infrastructure from a single pane of glass","VPC Lattice enables seamless connectivity between services running in AWS and on-premises environments, simplifying hybrid cloud architectures."
"Which of the following is a valid use case for Amazon VPC Lattice regarding traffic management?","Traffic mirroring for testing and diagnostics","Content caching for static assets","Load balancing across multiple regions","Global accelerator for web applications","VPC Lattice supports traffic mirroring for testing and diagnostic purposes, allowing you to send a copy of production traffic to a monitoring or analysis service without impacting live users."
"When designing a microservices architecture using Amazon VPC Lattice, what factor is most important to consider for optimal performance?","Service discovery and registration efficiency","EC2 instance size","Database query optimisation","Code complexity","Efficient service discovery and registration are crucial for achieving optimal performance in a VPC Lattice-based microservices architecture, as it directly impacts the speed at which services can locate and connect to each other."
"How can you ensure that only authorised services can access a specific service within an Amazon VPC Lattice service network?","By configuring IAM policies that specify which services can invoke the target service","By using VPC Flow Logs","By using EC2 Security Groups","By using Route 53 Private Hosted Zones","IAM policies allow you to define precisely which services can access other services within a VPC Lattice service network, providing fine-grained authorisation."
"What type of deployment is simplified by using Amazon VPC Lattice's traffic management capabilities?","Canary Deployments","Database Deployments","Front-end Deployments","VPN Deployments","VPC Lattice's weighted target group functionality significantly simplifies the process of canary deployments, allowing for gradual traffic shifting to new service versions."
"What is the primary function of Amazon VPC Lattice?","Simplifying service-to-service communication.","Managing EC2 instance scaling.","Monitoring network traffic in VPCs.","Automating security patching.","VPC Lattice focuses on simplifying and securing service-to-service communication across different compute types and VPCs."
"In VPC Lattice, what represents a logical grouping of services?","Service network","Target group","Listener rule","Access log","A service network in VPC Lattice represents a logical boundary that groups related services, enabling consistent policies across them."
"Which component of VPC Lattice is responsible for routing traffic to healthy targets based on defined rules?","Listener","Access log","Security group","Transit gateway","Listeners in VPC Lattice define how traffic is routed to the target groups associated with a service based on defined rules."
"How does VPC Lattice enhance security for service communication?","By providing a centralised point for applying security policies.","By encrypting all traffic using IPsec tunnels.","By replacing traditional security groups.","By isolating services in separate AWS accounts.","VPC Lattice allows you to define and apply consistent security policies at a centralised point, simplifying security management across services."
"What type of traffic can VPC Lattice route based on HTTP headers or methods?","HTTP and HTTPS traffic","TCP traffic only","UDP traffic only","All traffic types","VPC Lattice supports advanced routing capabilities based on HTTP headers and methods, enabling granular control over traffic flow for HTTP/HTTPS applications."
"Which of the following AWS services integrates directly with VPC Lattice for service discovery?","AWS Cloud Map","AWS Route 53","AWS CloudWatch","AWS CloudTrail","AWS Cloud Map integrates directly with VPC Lattice, allowing services registered in Cloud Map to be easily discovered and accessed by Lattice services."
"What is a 'target' in the context of VPC Lattice?","An individual instance or container running a service.","A VPC peering connection.","An S3 bucket.","An IAM role.","A target in VPC Lattice represents the individual compute resources (instances, containers) that are running a specific service."
"Which of the following is a benefit of using VPC Lattice over traditional VPC peering for service communication?","Simplified security and traffic management.","Lower network latency.","Higher network bandwidth.","Reduced cost of data transfer.","VPC Lattice simplifies service communication by providing a centralised point for applying security, traffic management and observability."
"What type of authentication does VPC Lattice support for securing service-to-service communication?","Mutual TLS (mTLS)","Basic authentication","Kerberos","Digest authentication","VPC Lattice supports mutual TLS (mTLS) which enhances service-to-service authentication by verifying the identities of both the client and the server."
"How can you monitor traffic flowing through VPC Lattice?","Using access logs and metrics.","Using VPC Flow Logs only.","Using AWS Config rules only.","Using Amazon Inspector only.","VPC Lattice provides access logs and metrics that can be used to monitor and troubleshoot traffic flowing through the service network."
"In VPC Lattice, what is the purpose of a 'Listener Rule'?","To define how traffic is routed based on conditions.","To define the health check configuration for a service.","To define the scaling policy for a service.","To define the data retention policy for access logs.","Listener rules in VPC Lattice allow you to define the conditions under which traffic is routed to specific target groups."
"Which AWS service can be used to visualise VPC Lattice metrics and logs?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS X-Ray","Amazon CloudWatch can be used to visualise VPC Lattice metrics and logs, providing insights into service performance and traffic patterns."
"What is a key difference between VPC Lattice and a traditional Application Load Balancer (ALB)?","VPC Lattice supports service-to-service communication across VPCs and accounts.","VPC Lattice supports only TCP traffic.","ALB supports mTLS authentication.","ALB supports service discovery via Cloud Map.","VPC Lattice enables service-to-service communication across VPCs and accounts, whereas an ALB is typically used within a single VPC."
"Which of the following is a valid use case for VPC Lattice?","Modernising a monolithic application into microservices.","Migrating a database to the cloud.","Storing static website content.","Running batch processing jobs.","VPC Lattice is well-suited for modernising monolithic applications by facilitating communication between microservices."
"When configuring VPC Lattice, how do you specify the protocol and port for a service?","By configuring the listener.","By configuring the target group.","By configuring the service network.","By configuring the security group.","The protocol and port for a service in VPC Lattice are configured as part of the listener definition."
"What is the purpose of the 'Default Action' in a VPC Lattice Listener?","To specify the action to take when no other rules match.","To specify the action to take when a health check fails.","To specify the action to take when a security policy is violated.","To specify the action to take when a service is scaled down.","The default action in a VPC Lattice listener specifies what happens to traffic that doesn't match any of the configured rules."
"Which security principle is enhanced by using VPC Lattice for service communication?","Least privilege","Defence in depth","Shared responsibility","Availability","VPC Lattice allows you to implement the principle of least privilege by controlling exactly which services can communicate with each other."
"When integrating VPC Lattice with AWS Cloud Map, what information does Cloud Map provide?","Service discovery information.","Security group configurations.","Load balancing algorithms.","Auto Scaling policies.","Cloud Map provides service discovery information to VPC Lattice, allowing Lattice to dynamically route traffic to the correct service endpoints."
"Which of the following is a key benefit of using VPC Lattice for managing service dependencies?","Reduced complexity","Increased network bandwidth","Improved data encryption","Simplified database management","VPC Lattice reduces the complexity of managing service dependencies by providing a centralised point for defining and enforcing traffic policies."
"How does VPC Lattice help with observability in a microservices architecture?","By providing centralised access logs and metrics.","By automatically creating dashboards in CloudWatch.","By replacing existing logging infrastructure.","By encrypting all log data.","VPC Lattice provides centralised access logs and metrics, making it easier to observe and troubleshoot communication between microservices."
"What is the relationship between a VPC Lattice Service Network and a VPC?","A Service Network can span multiple VPCs.","A Service Network is confined to a single VPC.","A VPC can only have one Service Network associated with it.","A Service Network replaces a VPC.","A service network can span across multiple VPCs enabling you to manage communication between services irrespective of which VPC they are in."
"Which type of load balancing algorithm can VPC Lattice use?","Round Robin or Weighted Round Robin","Least Connections only","IP Hash only","Random","VPC Lattice supports round robin and weighted round robin load balancing algorithms allowing distribution of traffic across multiple target instances."
"What is the role of 'Targets' within a VPC Lattice Target Group?","To designate the backend instances that handle requests","To define the encryption algorithm for traffic","To specify the network ACL rules","To manage IAM permissions","'Targets' within a VPC Lattice Target Group represent the backend instances (EC2, containers) that process incoming requests for the service."
"How does VPC Lattice support blue/green deployments?","By using weighted target groups to gradually shift traffic","By automatically creating backup copies of instances","By implementing a canary deployment strategy","By providing automated rollback capabilities","VPC Lattice enables blue/green deployments by allowing gradual traffic shifting between target groups using weighted configurations."
"Which of these components IS NOT part of the Amazon VPC Lattice ecosystem?","Global Accelerator","Service network","Target group","Listener","Global Accelerator is not part of the Amazon VPC Lattice ecosystem, but is rather used to optimise the global reach of services."
"Can VPC Lattice be used to manage traffic between services deployed in different AWS accounts?","Yes, VPC Lattice supports cross-account service communication","No, VPC Lattice only supports communication within a single AWS account","VPC Lattice only supports cross-account communication if AWS Organizations is not used","VPC Lattice only support cross-account communication using dedicated AWS Direct Connect links","VPC Lattice enables cross-account service communication, simplifying service sharing and management across different AWS accounts."
"How does VPC Lattice impact the management of network security groups?","It simplifies security group management by providing a central point for policies","It replaces traditional security groups completely","It has no impact on security group management","It requires the use of a specific security group type","VPC Lattice simplifies security group management by providing a centralised location to define and apply consistent security policies."
"What is the purpose of defining a 'health check' within a VPC Lattice Target Group?","To monitor the health and availability of backend instances","To optimise network performance","To configure automatic scaling of instances","To restrict access based on IP addresses","Health checks in VPC Lattice monitor the availability and health of backend instances, enabling traffic to be routed only to healthy targets."
"How does VPC Lattice contribute to simplifying compliance in regulated industries?","By providing centralised audit logs and security policies","By automatically encrypting data at rest","By automating security patching of EC2 instances","By managing IAM roles and permissions","VPC Lattice contributes to simplifying compliance by providing centralised audit logs and enforcing consistent security policies across services."
"You want to allow only specific client services to access a backend service in VPC Lattice. How can you achieve this?","By using authentication policies with mTLS","By configuring network ACLs","By implementing AWS WAF rules","By configuring IAM roles","By using mTLS authentication policies, VPC Lattice can restrict access to a service based on the identity of the client."
"What is the primary advantage of using VPC Lattice for service discovery compared to hardcoding service endpoints?","Dynamic service discovery and automatic updates","Reduced network latency","Lower cost of implementation","Enhanced security through encryption","VPC Lattice offers dynamic service discovery, allowing services to automatically discover and communicate with each other, even as endpoints change."
"How can you ensure high availability for services using VPC Lattice?","By distributing targets across multiple Availability Zones","By enabling cross-region replication of data","By configuring automatic failover to a secondary AWS account","By implementing a CDN","VPC Lattice ensures high availability by distributing targets across multiple Availability Zones, allowing traffic to be automatically routed to healthy targets in different AZs."
"Which of the following statements is correct about VPC Lattice Service Network?","A Service Network simplifies service-to-service connectivity","A Service Network is a replacement for VPC peering","A Service Network allows inter-VPC communication but not inter-account communication","A Service Network is primarily used for hybrid cloud connectivity","A Service Network simplifies service-to-service connectivity"
"What is the purpose of the 'Access Logs' feature in VPC Lattice?","To record all traffic flowing through the Service Network","To enforce security policies","To perform health checks on backend instances","To manage IAM permissions","Access Logs in VPC Lattice record all traffic flowing through the Service Network, providing valuable insights for monitoring, troubleshooting, and auditing purposes."
"In a microservices architecture using VPC Lattice, how can you implement canary deployments?","By gradually shifting traffic to a new version of a service using weighted target groups","By automatically replicating traffic to both old and new versions","By manually deploying a new version alongside the old version","By using AWS CodeDeploy for automated deployments","Canary deployments involve gradually shifting traffic to a new version of a service."
"Which AWS service is typically used to store and analyse VPC Lattice access logs?","Amazon S3","Amazon RDS","Amazon DynamoDB","Amazon EBS","VPC Lattice access logs are typically stored in Amazon S3, which can then be analysed using other AWS services."
"What is the main advantage of using VPC Lattice over using an API Gateway for internal microservices communication?","Improved security and traffic management within the VPC","Reduced latency for external API calls","Simplified API documentation","Lower cost of operation","VPC Lattice provides improved security and traffic management within the VPC for microservices."
"How does VPC Lattice facilitate zero-trust security principles?","By enforcing strict authentication and authorisation policies for all service interactions","By automatically encrypting all data at rest and in transit","By replacing traditional network security groups","By providing centralised IAM role management","VPC Lattice facilitates zero-trust security by enforcing authentication and authorisation policies for all service interactions."
"What configuration determines which compute resources receive traffic from a VPC Lattice Service?","Target Group","Route Table","Security Group","Network ACL","The Target Group configuration determines which compute resources receive traffic from a VPC Lattice Service."
"Which of the following is a potential use case for using VPC Lattice with containerized applications?","Service mesh-like functionality for microservices in ECS or EKS","Load balancing traffic for a monolithic application","Creating a VPN connection to an on-premises network","Storing container images","VPC Lattice can provide service mesh-like functionality for microservices in ECS or EKS, offering features like traffic management, security, and observability."
"What type of routing does VPC Lattice use when you configure multiple target groups for the same service?","Weighted target groups","Round Robin DNS","Least Connections","Random","VPC Lattice supports using weighted target groups for routing traffic."
"What must you configure in VPC Lattice to enable HTTPS traffic for your services?","Listener","Network ACL","Security Group","Route Table","You must configure the Listener with the appropriate protocol settings to enable HTTPS traffic for your services in VPC Lattice."
"Which is NOT a benefit of using VPC Lattice?","Centralised Service Configuration","Improved Security","Automated Scaling","Increased Observability","Automated scaling is handled by services such as EC2 Auto Scaling and is not part of VPC Lattice."
"Which protocol does VPC Lattice use for service discovery?","HTTP","HTTPS","gRPC","DNS","DNS is used to discover service endpoints via the integrated AWS Cloud Map."
"When is it appropriate to choose Amazon VPC Lattice over Application Load Balancer (ALB)?","When you need cross-account and cross-VPC service communication","When you only need to load balance traffic within a single VPC","When you need advanced content-based routing","When you need to terminate SSL/TLS connections","VPC Lattice is appropriate when you need cross-account and cross-VPC service communication."
"Where are access logs for Amazon VPC Lattice sent?","Amazon S3","Amazon CloudWatch Logs","Amazon CloudTrail","Amazon SNS","Access logs for Amazon VPC Lattice are sent to Amazon S3 buckets."
"What protocol is primarily used by VPC Lattice for intra-service communication within a service network?","HTTP/HTTPS","TCP","UDP","ICMP","VPC Lattice primarily uses HTTP/HTTPS for intra-service communication within a service network, as it is designed for modern application architectures."
"Which of the following is a component used to define how requests are routed in VPC Lattice?","Listener Rule","Security Group","Network ACL","Route Table","A Listener Rule is used to define how requests are routed based on specified conditions, like HTTP headers or paths, in VPC Lattice."
"What is the primary purpose of Amazon VPC Lattice?","To simplify service-to-service communication in a microservices architecture.","To manage and scale container clusters.","To create and manage virtual private clouds.","To provide serverless compute resources.","VPC Lattice provides a consistent way to connect, secure, and monitor service-to-service communication across different compute types and networks."
"Which of the following protocols is supported by Amazon VPC Lattice for service communication?","HTTP/1.1, HTTP/2, and gRPC","SMTP, FTP, and SSH","TCP, UDP, and ICMP","BGP, OSPF, and RIP","VPC Lattice is designed to handle modern application traffic, supporting the common protocols used in microservices architectures."
"What is a 'service network' in the context of Amazon VPC Lattice?","A logical boundary for services within VPC Lattice, defining how they communicate.","A collection of EC2 instances running the same application.","A virtual private cloud (VPC) peered with other VPCs.","A group of AWS Lambda functions that work together.","A service network is a core concept in VPC Lattice, providing a centralised way to manage and control service communication."
"In Amazon VPC Lattice, what does a 'target group' represent?","A collection of compute resources (e.g., EC2 instances, containers, or Lambda functions) that serve traffic for a service.","A group of users with specific access permissions to the service.","A set of firewall rules applied to the service.","A database storing configuration information for the service.","Target groups define where VPC Lattice will route traffic for a specific service, allowing for dynamic scaling and load balancing."
"Which AWS Identity and Access Management (IAM) principle is used to control access to Amazon VPC Lattice resources?","Principle of Least Privilege","Principle of Separation of Duties","Principle of Defence in Depth","Principle of Shared Responsibility","IAM policies are used to grant only the necessary permissions to users and roles, following the principle of least privilege."
"How can you monitor traffic flowing through Amazon VPC Lattice?","Using Amazon CloudWatch metrics and access logs.","Using AWS CloudTrail logs only.","Using VPC Flow Logs only.","Using AWS Config rules only.","CloudWatch provides metrics for monitoring performance, and access logs capture details of traffic flowing through VPC Lattice."
"What is a key benefit of using Amazon VPC Lattice for service discovery?","It provides a centralised service registry, simplifying how services find and communicate with each other.","It automatically creates DNS records for all services.","It eliminates the need for service meshes.","It automatically provisions EC2 instances for new services.","VPC Lattice simplifies service discovery by providing a central point for services to register and discover each other."
"Which of the following is NOT a key feature of Amazon VPC Lattice?","Automated vulnerability scanning of services","Traffic management with weighted routing and header-based routing","Centralised service discovery","Mutual TLS (mTLS) authentication","Automated vulnerability scanning is not a native feature of VPC Lattice. Other services such as Amazon Inspector would be used in conjunction."
"How does Amazon VPC Lattice simplify security for microservices?","By providing mutual TLS (mTLS) authentication and fine-grained access control.","By automatically encrypting all data at rest.","By automatically patching vulnerabilities in services.","By completely isolating services from the internet.","VPC Lattice enhances security by enabling mTLS for secure communication and providing mechanisms for granular access control."
"What is the relationship between Amazon VPC Lattice and AWS PrivateLink?","VPC Lattice can use PrivateLink to securely connect to services in other VPCs or AWS accounts.","VPC Lattice replaces the need for PrivateLink.","PrivateLink replaces the need for VPC Lattice.","VPC Lattice and PrivateLink are unrelated services.","VPC Lattice leverages PrivateLink to securely connect to services across VPCs and accounts, avoiding the need for public internet exposure."
"In AWS VPC Lattice, what is a Service Network?","A logical boundary for grouping services.","A tool for monitoring network traffic.","A type of virtual private cloud.","A specific routing protocol.","A Service Network is a logical construct that allows you to group services together for management, security, and discovery within VPC Lattice."
"What is the primary purpose of a Listener in the context of AWS VPC Lattice?","To define how traffic is routed to a target group.","To monitor the health of instances.","To configure security groups.","To define the DNS records for services.","A Listener in VPC Lattice defines how traffic is routed to a target group, based on rules you configure, such as host-based routing or path-based routing."
"With AWS VPC Lattice, what is a Target Group?","A collection of compute resources that receive traffic.","A set of security rules.","A monitoring dashboard.","A billing metric for network usage.","A Target Group in VPC Lattice is a collection of compute resources (e.g., EC2 instances, containers) that receive traffic from the service network."
"Which protocol is commonly used for service-to-service communication within AWS VPC Lattice?","HTTP/2 or gRPC","SMTP","FTP","SNMP","VPC Lattice is optimised for modern service-to-service communication, typically using HTTP/2 or gRPC for efficient and flexible interactions."
"What security benefit does AWS VPC Lattice provide for microservices?","Centralised traffic management and zero trust capabilities.","Automated patching of operating systems.","Encryption of data at rest.","Enhanced DDoS protection at the perimeter.","VPC Lattice allows central traffic management and zero trust capabilities, improving security by controlling access between microservices."
"How does AWS VPC Lattice simplify service discovery in a microservices architecture?","It automatically discovers and registers services within the service network.","It relies on a separate DNS server for service discovery.","It requires manual configuration of service endpoints.","It uses broadcast messages for service location.","VPC Lattice simplifies service discovery by automatically discovering and registering services, enabling seamless communication without manual configuration."
"What is the role of an Access Log in AWS VPC Lattice?","To record traffic flowing through the service network.","To control user access to the VPC.","To monitor CPU usage of instances.","To manage network routing tables.","Access Logs in VPC Lattice record traffic flowing through the service network, providing insights into performance and security."
"When using AWS VPC Lattice, what is the benefit of 'Traffic Shaping'?","To control the rate of traffic to prevent overloading services.","To automatically scale the number of instances.","To compress data before transmission.","To encrypt traffic between services.","Traffic Shaping in VPC Lattice allows you to control the rate of traffic to prevent overloading services and maintain stability."
"How does AWS VPC Lattice improve the observability of your services?","By providing detailed metrics and logs about service traffic.","By automatically generating code documentation.","By offering predictive analysis of instance failures.","By performing automated security audits.","VPC Lattice improves observability by providing detailed metrics and logs about service traffic, enabling better monitoring and troubleshooting."
"What is the relationship between AWS VPC Lattice and an Application Load Balancer (ALB)?","VPC Lattice can replace the need for an ALB in many microservices scenarios.","ALBs are required for all VPC Lattice configurations.","VPC Lattice uses ALBs for internal service discovery.","VPC Lattice is only compatible with Classic Load Balancers.","VPC Lattice is designed as an alternative to using ALBs for internal microservices traffic, though ALBs can still be used for edge traffic. VPC Lattice offers enhanced service-to-service networking capabilities."