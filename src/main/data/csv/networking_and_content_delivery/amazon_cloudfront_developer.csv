"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon CloudFront, what is an Origin Access Identity (OAI) primarily used for?","Restricting access to content in an S3 bucket to only be served through CloudFront.","Encrypting the traffic between CloudFront and the origin.","Automatically invalidating the CloudFront cache.","Logging all requests to the origin server.","OAI ensures only CloudFront can access the content in the S3 bucket, enhancing security."
"Which of the following is NOT a valid CloudFront origin type?","AWS Lambda Function","HTTP server running on EC2","S3 bucket","Amazon CloudWatch Dashboard","CloudFront can use Lambda Functions, EC2 instances, and S3 buckets as origins. CloudWatch Dashboards are not a supported origin."
"What is the purpose of CloudFront's geo restriction feature?","To allow or deny users in specific geographic locations from accessing your content.","To automatically translate content into different languages based on the user's location.","To redirect users to different origins based on their location.","To provide different pricing tiers based on the user's location.","Geo restriction allows you to control the geographic distribution of your content by allowing or denying access to users based on their location."
"What does CloudFront use to determine which edge location will serve a request?","The user's DNS resolver location.","The size of the requested file.","The time of day.","The configured CloudFront price class.","CloudFront uses the user's DNS resolver location to determine the closest and most suitable edge location to serve the request."
"Which of the following is a benefit of using CloudFront with AWS Shield?","Enhanced protection against DDoS attacks.","Automatic data encryption at rest.","Reduced storage costs in S3.","Simplified origin configuration.","AWS Shield integrates with CloudFront to provide enhanced protection against Distributed Denial of Service (DDoS) attacks."
"What is the primary function of CloudFront invalidation?","To remove content from the CloudFront edge caches before its expiration time.","To encrypt content stored in the CloudFront edge caches.","To automatically backup content to another region.","To optimise content for different device types.","CloudFront invalidation removes specific content from the edge caches, ensuring users receive the latest version."
"What is the purpose of CloudFront Functions?","To run lightweight code at CloudFront edge locations to manipulate requests and responses.","To execute complex server-side code in the cloud.","To store and manage session state for web applications.","To provide a fully managed database service at the edge.","CloudFront Functions allows you to execute lightweight code to modify requests and responses close to the user, improving performance and customisation."
"Which of the following can be used to serve private content using CloudFront?","Signed URLs or Signed Cookies.","Public S3 buckets with default permissions.","CloudFront Functions with IP address restrictions.","CloudFront distributions without any origin.","Signed URLs or Signed Cookies are used to control access to private content served through CloudFront, ensuring only authorised users can access the content."
"What is the purpose of a CloudFront custom error page?","To display a user-friendly error message when a request fails.","To automatically retry failed requests.","To redirect users to a different website.","To automatically create a support ticket.","CloudFront custom error pages provide a better user experience by displaying helpful error messages instead of default error pages."
"Which of the following caching behaviours can you configure in CloudFront?","Cache based on query strings, cookies, and headers.","Always serve content directly from the origin.","Only cache static content.","Never cache any content.","CloudFront allows you to configure caching behaviour based on various factors, including query strings, cookies, and headers, to optimise caching performance."
"What is the purpose of the 'Price Class' setting in CloudFront?","To determine the geographic regions where your content will be cached, affecting cost and performance.","To set the pricing for your content.","To determine the storage class used in S3.","To define the encryption level for your content.","The Price Class setting determines the edge locations used by CloudFront, impacting cost and performance based on geographic coverage."
"You need to log all requests that CloudFront receives. Which feature should you enable?","Access Logging.","AWS CloudTrail.","Amazon CloudWatch Logs.","AWS Config.","Access Logging allows you to log all requests made to your CloudFront distribution, providing valuable insights into usage patterns."
"How does CloudFront handle dynamic content?","By caching the content with a short TTL or no caching, and retrieving the latest version from the origin on each request.","By caching the content indefinitely.","By refusing to serve dynamic content.","By converting dynamic content into static content.","CloudFront can handle dynamic content by caching it with a short TTL or no caching, ensuring the latest version is retrieved from the origin as needed."
"Which of the following is an advantage of using CloudFront over directly accessing content from an S3 bucket?","Improved performance and reduced latency for users.","Unlimited storage capacity.","Automatic encryption of data at rest.","Simplified access control management.","CloudFront improves performance and reduces latency by caching content closer to users, resulting in a faster user experience."
"What type of security does CloudFront offer?","Protection against network and application layer attacks.","Data encryption at rest.","Automatic backup and recovery.","Vulnerability scanning.","CloudFront integrates with AWS Shield and AWS WAF to provide protection against network and application layer attacks, enhancing the security of your content."
"In CloudFront, what is the purpose of TTL (Time To Live)?","To specify how long content remains in the edge cache before CloudFront checks for a newer version from the origin.","To set the maximum size of files that can be cached.","To define the geographic region where the content can be accessed.","To encrypt data in transit between CloudFront and the origin.","TTL defines how long content stays in the cache before CloudFront checks for an update from the origin."
"How can you invalidate all files in a CloudFront distribution?","Using a wildcard character (*) in the invalidation path.","Deleting the distribution and recreating it.","Disabling and re-enabling the distribution.","There is no way to invalidate all files at once.","You can use a wildcard character (*) in the invalidation path to invalidate all files in a CloudFront distribution."
"What is the best practice for serving both HTTP and HTTPS traffic using CloudFront?","Use the 'Redirect HTTP to HTTPS' viewer protocol policy.","Create two separate CloudFront distributions, one for HTTP and one for HTTPS.","Only serve content over HTTP.","Only serve content over HTTPS.","Using the 'Redirect HTTP to HTTPS' viewer protocol policy ensures all HTTP requests are redirected to HTTPS for secure communication."
"Which of the following is the function of a CloudFront Origin Group?","To provide failover and origin redundancy for your content.","To group multiple CloudFront distributions together.","To apply the same configuration to multiple origins.","To simplify origin access control.","Origin Groups enable failover and redundancy by allowing you to configure multiple origins for your content, improving availability."
"Which protocol can CloudFront use to communicate with a custom origin server?","HTTP and HTTPS","Only HTTPS","Only HTTP","FTP","CloudFront can communicate with a custom origin server using both HTTP and HTTPS protocols."
"How can you restrict access to specific files in your S3 bucket when using CloudFront?","Use signed URLs with specific expiration times.","Use S3 bucket policies with IP address restrictions.","Use CloudFront Functions to deny requests based on file type.","Use CloudFront's geo restriction feature.","Signed URLs allow you to grant time-limited access to specific files in your S3 bucket when using CloudFront."
"Which AWS service integrates with CloudFront to provide a web application firewall?","AWS WAF (Web Application Firewall)","AWS Shield","AWS Inspector","AWS GuardDuty","AWS WAF integrates with CloudFront to provide a web application firewall, protecting against common web exploits."
"What is the function of a CloudFront distribution's alternate domain names (CNAMEs)?","To allow users to access your content using your own domain name.","To automatically redirect users to different edge locations.","To encrypt traffic between CloudFront and the origin.","To manage SSL/TLS certificates.","CNAMEs allow users to access your content using your own domain name instead of the default CloudFront domain."
"What is the purpose of CloudFront Key Groups?","To manage public keys that CloudFront can use to verify signed URLs or signed cookies.","To group CloudFront distributions for easier management.","To control access to CloudFront management console.","To monitor CloudFront performance metrics.","Key Groups in CloudFront are used to manage public keys for verifying signed URLs or signed cookies, enhancing security and access control."
"Which setting in CloudFront determines how long objects stay in the cache before CloudFront forwards another request to the origin?","Minimum TTL, Default TTL, and Maximum TTL","Cache Duration","Invalidation Period","Retention Policy","Minimum TTL, Default TTL, and Maximum TTL settings control how long objects are cached before CloudFront checks with the origin."
"What is the impact of setting a very long TTL for your CloudFront distribution?","Content is cached for a longer period, reducing origin requests but potentially serving stale content.","Content is cached for a shorter period, increasing origin requests but ensuring fresh content.","Content is never cached.","Content is only cached during peak hours.","A long TTL reduces origin requests but increases the risk of serving stale content, while a short TTL ensures fresh content but increases origin requests."
"You need to serve different versions of your website content based on the user's device type (e.g., mobile or desktop). How can you achieve this with CloudFront?","Configure CloudFront to cache based on the 'User-Agent' header.","Create separate CloudFront distributions for each device type.","Use CloudFront Functions to rewrite the URL based on device type.","Use S3 bucket policies to restrict access based on device type.","Configuring CloudFront to cache based on the 'User-Agent' header allows you to serve different versions of content based on the user's device."
"What happens when an object expires in a CloudFront cache?","CloudFront forwards the next request for that object to the origin to verify if the cache is still valid.","CloudFront automatically deletes the object from the cache.","CloudFront serves a default error page.","CloudFront redirects the user to a different website.","When an object expires, CloudFront forwards the next request to the origin to validate the cache, ensuring the content is up-to-date."
"Which type of content is CloudFront best suited for?","Both static and dynamic content","Only static content","Only dynamic content","Database backups","CloudFront is designed to efficiently serve both static and dynamic content by caching static assets and dynamically fetching content from the origin."
"You want to configure CloudFront to compress content to reduce the size of files transferred to users. What setting should you enable?","Enable Gzip Compression","Enable Data Encryption","Enable Origin Shield","Enable Access Logging","Enabling Gzip Compression in CloudFront reduces file sizes, improving download speeds and reducing bandwidth costs."
"Which of the following is a benefit of using Origin Shield with CloudFront?","Reduces the load on your origin server by caching content at a regional edge cache.","Encrypts data at rest in the edge cache.","Automatically invalidates the cache.","Provides detailed analytics on user behaviour.","Origin Shield reduces the load on your origin server by caching content at a regional edge cache, improving performance and reducing costs."
"How does CloudFront handle requests for content that is not yet cached at an edge location?","It retrieves the content from the origin and caches it at the edge location.","It returns an error message to the user.","It redirects the user to the origin server.","It serves a default placeholder page.","CloudFront retrieves uncached content from the origin and caches it at the edge location for future requests."
"What is the purpose of setting up a custom SSL/TLS certificate for your CloudFront distribution?","To allow users to access your content over HTTPS using your own domain name.","To encrypt data at rest in the CloudFront cache.","To automatically renew your SSL/TLS certificates.","To monitor SSL/TLS certificate expiration dates.","A custom SSL/TLS certificate allows users to securely access your content over HTTPS using your own domain name."
"Which of the following is a valid CloudFront distribution state?","Enabled or Disabled","Creating, Updating, Deleting","Online or Offline","Active or Inactive","A CloudFront distribution can be either Enabled or Disabled, controlling whether it's actively serving content."
"What is the impact of using CloudFront with Amazon S3 Transfer Acceleration?","It has no impact","Speeds up uploads to S3 buckets.","Speeds up downloads from S3 buckets.","Reduces the cost of S3 storage.","Using CloudFront with S3 Transfer Acceleration speeds up uploads to S3 buckets by using CloudFront's globally distributed edge locations."
"Which feature of CloudFront allows you to add custom headers to requests sent to the origin?","Origin Request Policies","Cache Policies","Response Headers Policies","Custom Error Pages","Origin Request Policies allow you to add custom headers to requests sent to the origin, enabling customisation and control."
"What is the purpose of Response Headers Policies in CloudFront?","To add or modify HTTP headers in the responses that CloudFront sends to viewers.","To filter incoming HTTP requests based on headers.","To encrypt HTTP headers.","To log HTTP headers.","Response Headers Policies allow you to add or modify HTTP headers in the responses that CloudFront sends to viewers, enhancing security and control."
"How does CloudFront handle HTTP/2 connections?","CloudFront automatically supports HTTP/2 for compatible viewers.","You need to manually enable HTTP/2 in the CloudFront settings.","HTTP/2 is not supported by CloudFront.","HTTP/2 is only supported for specific regions.","CloudFront automatically supports HTTP/2 for compatible viewers, improving performance."
"What is the primary advantage of using CloudFront with AWS Global Accelerator?","Improved availability and performance by routing traffic through the AWS global network.","Reduced cost of CloudFront data transfer.","Simplified CloudFront configuration.","Enhanced security against DDoS attacks.","AWS Global Accelerator improves availability and performance by routing traffic through the AWS global network, complementing CloudFront's caching capabilities."
"You want to ensure that all requests to your CloudFront distribution are logged in real-time. Which AWS service should you integrate with CloudFront?","Amazon Kinesis Data Firehose","Amazon SQS","Amazon SNS","Amazon CloudWatch Events","Amazon Kinesis Data Firehose can be used to capture and deliver CloudFront access logs in real-time."
"When creating a CloudFront distribution, what is the purpose of the 'Origin Path' setting?","To specify a subdirectory on the origin server where the content is stored.","To define the root directory of the distribution.","To set the expiration date for the content.","To specify the domain name of the origin server.","The Origin Path setting specifies a subdirectory on the origin server where the content is stored, allowing you to serve content from a specific path."
"Which feature allows you to automatically redirect users from HTTP to HTTPS using CloudFront?","Viewer Protocol Policy","Origin Protocol Policy","Default Root Object","Custom Error Responses","The Viewer Protocol Policy allows you to automatically redirect users from HTTP to HTTPS, ensuring secure communication."
"What is the best way to implement A/B testing with CloudFront?","Use CloudFront Functions or Lambda@Edge to modify requests based on user cookies or query strings.","Create two separate CloudFront distributions for each version of the content.","Use S3 bucket policies to restrict access based on user groups.","There is no way to implement A/B testing with CloudFront.","CloudFront Functions or Lambda@Edge can be used to modify requests based on user cookies or query strings, enabling A/B testing scenarios."
"What is the benefit of using CloudFront with Lambda@Edge?","Allows you to run custom code at CloudFront edge locations to personalise content and modify requests/responses.","Reduces the cost of CloudFront data transfer.","Automatically invalidates the cache.","Provides detailed analytics on user behaviour.","Lambda@Edge allows you to run custom code at CloudFront edge locations, enabling content personalisation and request/response modification."
"How does CloudFront handle requests for compressed objects that are not supported by the viewer?","It automatically decompresses the objects before serving them to the viewer.","It returns an error message to the viewer.","It redirects the user to the origin server.","It serves a default placeholder page.","CloudFront automatically decompresses objects if the viewer doesn't support the compression format, ensuring content delivery."
"Which of the following is a valid use case for signed cookies in CloudFront?","Granting access to multiple restricted files to authenticated users.","Restricting access to specific files based on IP address.","Serving dynamic content.","Encrypting data at rest.","Signed cookies are used to grant access to multiple restricted files to authenticated users, simplifying access management."
"You want to use CloudFront to serve content from a private S3 bucket, but you don't want to use an Origin Access Identity (OAI). What is another option?","Use S3 bucket policies to allow access based on the CloudFront distribution's AWS account ID.","Use IAM roles to grant CloudFront access to the S3 bucket.","There is no other way to serve content securely from private buckets.","Use CloudFront Functions to rewrite the URL.","You can use S3 bucket policies to allow access based on the CloudFront distribution's AWS account ID without using an OAI."
"Which AWS service can be used to monitor CloudFront's performance metrics, such as cache hit ratio and data transfer?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch is used to monitor CloudFront's performance metrics, providing insights into cache hit ratio, data transfer, and other key metrics."
"You are configuring CloudFront to use a custom origin, and you need to configure the 'Origin Protocol Policy'. What does this policy determine?","The protocol (HTTP or HTTPS) that CloudFront uses to communicate with the origin.","The protocol that viewers use to access the CloudFront distribution.","The protocol used for logging requests to CloudFront.","The protocol used to encrypt data at rest in CloudFront.","The Origin Protocol Policy determines whether CloudFront uses HTTP or HTTPS to communicate with the origin server."
"What is the purpose of setting a Default Root Object in a CloudFront distribution?","To specify the object that CloudFront returns when a viewer requests the root URL of your distribution.","To define the root directory of the distribution.","To set the expiration date for the content.","To specify the domain name of the origin server.","The Default Root Object specifies the object that CloudFront returns when a user requests the root URL, such as index.html."
"What does the term 'Edge Location' refer to in the context of Amazon CloudFront?","A data centre where CloudFront caches content for faster distribution.","A virtual server used to host content.","The geographical location of the origin server.","A network security group.","An Edge Location is a data centre where CloudFront caches content to reduce latency and improve content delivery speed to users worldwide."
"What is the primary function of Amazon CloudFront?","Content Delivery Network (CDN)","Database Management","Compute Service","Serverless Functions","CloudFront is a CDN service that speeds up the distribution of your static and dynamic web content."
"Which of the following is a benefit of using Amazon CloudFront?","Reduced latency for users","Unlimited storage capacity","Free SSL certificates","Automated server patching","CloudFront reduces latency by caching content closer to users at edge locations."
"What type of content can Amazon CloudFront distribute?","Static and dynamic content","Only static content","Only dynamic content","Only database content","CloudFront can distribute both static content (e.g., images, videos) and dynamic content (e.g., API responses)."
"What is an 'Origin' in the context of Amazon CloudFront?","The source location where CloudFront retrieves content","A CloudFront edge location","A security policy for CloudFront","A CloudFront distribution configuration","The origin is the source location (e.g., S3 bucket, EC2 instance) from which CloudFront retrieves content."
"How does Amazon CloudFront improve website performance?","By caching content at edge locations","By optimising database queries","By reducing server CPU utilisation","By automatically scaling EC2 instances","CloudFront caches content at edge locations, reducing the distance data needs to travel and improving response times."
"Which AWS service is commonly used as an origin for Amazon CloudFront?","Amazon S3","Amazon EC2 Container Service (ECS)","Amazon CloudWatch","Amazon DynamoDB","Amazon S3 is frequently used as an origin for CloudFront to store and serve static content."
"What does TTL (Time To Live) control in Amazon CloudFront?","The duration for which content is cached at edge locations","The size of files that can be cached","The maximum number of requests per second","The lifetime of a CloudFront distribution","TTL determines how long content remains cached at CloudFront edge locations before it's refreshed from the origin."
"What is the purpose of 'Invalidation' in Amazon CloudFront?","To remove content from CloudFront edge caches","To create a new CloudFront distribution","To update the origin server","To encrypt content at edge locations","Invalidation is used to remove outdated or incorrect content from CloudFront edge caches, forcing a refresh from the origin."
"How can you secure content delivered through Amazon CloudFront?","Using HTTPS and signed URLs/cookies","Using only HTTP","Disabling caching","Allowing public access to the origin","HTTPS ensures secure communication between users and CloudFront, while signed URLs/cookies restrict access to authorised users."
"What are CloudFront 'Distributions'?","The configuration that defines how CloudFront delivers content","A collection of edge locations","A set of security rules","A pricing model for CloudFront","A CloudFront distribution is the configuration that specifies how CloudFront delivers content from your origin(s) to users."
"What is a CloudFront 'Edge Location'?","A data centre where CloudFront caches content","The origin server","The region where the CloudFront distribution is created","A security checkpoint for CloudFront","Edge locations are data centres around the world where CloudFront caches content to reduce latency for users."
"What is the purpose of 'Signed URLs' in Amazon CloudFront?","To restrict access to content based on time and IP address","To encrypt all content","To improve website SEO","To monitor website traffic","Signed URLs allow you to control who can access your content and for how long, typically based on a pre-signed URL."
"Which method can be used to restrict access to content in Amazon CloudFront based on geography?","Geo Restriction","IP Address Filtering","Referrer Blocking","Content Encryption","Geo Restriction allows you to control which countries can access your content through CloudFront."
"What is the function of the CloudFront 'Custom Error Pages' feature?","To display custom error pages to users when errors occur","To automatically fix errors on the origin server","To prevent errors from occurring","To log all errors in CloudWatch","Custom error pages allow you to provide a better user experience by displaying custom messages when errors (e.g., 404) occur."
"How does CloudFront support dynamic content delivery?","By caching content with short TTLs and using dynamic origin configuration","By only serving static content","By disabling caching altogether","By using pre-signed URLs only","CloudFront can cache dynamic content with short TTLs and can be configured to forward certain requests to the origin for dynamic processing."
"Which CloudFront feature allows you to perform A/B testing?","CloudFront Functions","CloudFront Geo Restriction","CloudFront Signed URLs","CloudFront Origin Groups","CloudFront Functions allows you to manipulate the request and response headers to redirect users to different versions of your content for A/B testing."
"What is the purpose of 'CloudFront Functions'?","Lightweight code execution at CloudFront edge locations","Full-fledged serverless application execution","Database management","Operating system patching","CloudFront Functions allow you to run lightweight code at edge locations to manipulate requests and responses for tasks like URL redirects and header manipulation."
"What is the difference between CloudFront Functions and Lambda@Edge?","CloudFront Functions have lower latency and are simpler to use for basic tasks","Lambda@Edge has lower latency and is simpler to use for basic tasks","CloudFront Functions can access the request body, while Lambda@Edge cannot","Lambda@Edge is cheaper than CloudFront Functions","CloudFront Functions are designed for simple tasks with minimal latency, while Lambda@Edge offers more functionality and flexibility for complex operations."
"What is the purpose of a 'Cache Key' in CloudFront?","To determine which version of the content to serve from the cache","To encrypt the cached content","To invalidate cached content","To store user session data","The cache key is used to identify unique objects in the CloudFront cache based on request parameters such as headers, query strings, and cookies."
"You need to distribute software downloads globally with minimal latency. Which AWS service is most suitable?","Amazon CloudFront","Amazon S3","Amazon EC2","Amazon RDS","CloudFront is specifically designed for distributing content with low latency to users around the world."
"How can you monitor the performance of your CloudFront distribution?","Using Amazon CloudWatch metrics","Using Amazon Inspector","Using Amazon Trusted Advisor","Using AWS Config","CloudWatch provides metrics like cache hit ratio, latency, and request counts, allowing you to monitor the performance of your CloudFront distribution."
"What does the 'Cache Hit Ratio' metric indicate in CloudFront?","The percentage of requests served from the cache","The percentage of requests forwarded to the origin","The number of edge locations used","The average latency for all requests","The cache hit ratio represents the percentage of requests that were served directly from the CloudFront cache, indicating how effectively the cache is being used."
"What is the purpose of the 'Origin Access Identity' (OAI) in CloudFront?","To restrict access to content in an S3 bucket only through CloudFront","To allow public access to content in an S3 bucket","To encrypt content stored in S3","To log all requests to an S3 bucket","OAI allows you to restrict direct access to your S3 bucket, ensuring that content can only be accessed through CloudFront."
"Which of the following is a valid use case for CloudFront's 'Origin Shield'?","To reduce load on the origin server by caching content in a regional edge cache","To encrypt data in transit between CloudFront and the origin","To provide DDoS protection for the origin server","To automatically scale the origin server based on traffic","Origin Shield is a regional edge cache that reduces load on the origin server by caching content closer to the edge locations, resulting in fewer requests to the origin."
"You want to serve different versions of a website based on the user's device type (e.g., mobile, desktop). How can you achieve this with CloudFront?","By using CloudFront Functions to inspect the 'User-Agent' header","By using CloudFront Geo Restriction","By using CloudFront Signed URLs","By using CloudFront Origin Groups","CloudFront Functions can be used to inspect the 'User-Agent' header and redirect users to the appropriate version of the website based on their device type."
"Which protocol can CloudFront use to communicate with the origin server?","HTTP or HTTPS","Only HTTP","Only HTTPS","Only FTP","CloudFront can use either HTTP or HTTPS to communicate with the origin server, allowing flexibility in configuring secure or non-secure connections."
"What is the purpose of 'Field-Level Encryption' in CloudFront?","To encrypt specific data fields in POST requests","To encrypt all data transmitted through CloudFront","To encrypt content stored at edge locations","To encrypt access logs","Field-Level Encryption allows you to encrypt specific data fields in POST requests, protecting sensitive information during transit."
"You need to implement a paywall for your video content, allowing only paying subscribers to access it. How can you achieve this with CloudFront?","By using Signed Cookies","By using Geo Restriction","By using custom error pages","By using Origin Shield","Signed cookies allow you to control access to content based on user authentication, making them suitable for implementing paywalls."
"Which CloudFront feature helps protect against DDoS attacks?","AWS Shield integration","Amazon Inspector integration","AWS Config integration","AWS Trusted Advisor integration","CloudFront integrates with AWS Shield to provide DDoS protection for your applications and infrastructure."
"How does CloudFront handle requests for objects that are not in the cache?","It retrieves the object from the origin server and caches it","It returns a 404 error","It automatically creates the object","It redirects the user to a different website","When an object is not found in the cache (a cache miss), CloudFront retrieves it from the origin server and caches it at the edge location for future requests."
"Which of the following is a valid CloudFront pricing component?","Data Transfer Out","Data Storage","Number of origin requests","Number of EC2 instances","CloudFront charges for data transferred out of CloudFront edge locations, the number of HTTP/HTTPS requests, and invalidation requests."
"What is the role of 'Alternate Domain Names' (CNAMEs) in CloudFront?","To use a custom domain name for your CloudFront distribution","To redirect traffic to a different website","To encrypt traffic between CloudFront and the origin","To monitor traffic to your CloudFront distribution","CNAMEs allow you to use your own domain name (e.g., www.example.com) for your CloudFront distribution instead of the default CloudFront domain."
"You want to log all requests that are processed by your CloudFront distribution. How can you achieve this?","By enabling access logging","By enabling CloudTrail","By enabling CloudWatch Logs","By enabling VPC Flow Logs","Access logging allows you to record all requests that are processed by your CloudFront distribution, providing valuable insights into traffic patterns and usage."
"What is the purpose of the 'Default Root Object' in CloudFront?","To specify the object to return when a user requests the root of your distribution","To specify the default error page","To specify the default caching behaviour","To specify the default origin server","The Default Root Object specifies the object (e.g., index.html) that CloudFront should return when a user requests the root of your distribution (e.g., example.com)."
"How can you force CloudFront to use HTTPS for communication with the origin server?","By setting the 'Origin Protocol Policy' to HTTPS Only","By enabling Geo Restriction","By enabling Signed URLs","By enabling access logging","Setting the 'Origin Protocol Policy' to HTTPS Only ensures that CloudFront always uses HTTPS when communicating with the origin server, enhancing security."
"What is the purpose of 'Lambda@Edge' in the context of Amazon CloudFront?","To run code at CloudFront edge locations to customise content delivery","To manage AWS Lambda functions","To monitor the performance of Lambda functions","To deploy serverless applications","Lambda@Edge allows you to run AWS Lambda functions at CloudFront edge locations, enabling you to customise content delivery based on user requests."
"You need to ensure that your CloudFront distribution automatically uses the latest version of your website files whenever they are updated in your S3 bucket. How can you achieve this?","By using cache invalidation","By using Geo Restriction","By using Signed URLs","By using Origin Shield","Cache invalidation removes outdated content from CloudFront edge locations, forcing CloudFront to retrieve the latest version of the files from the origin server."
"Which of the following is a valid way to integrate Amazon CloudFront with AWS WAF (Web Application Firewall)?","By associating a WAF web ACL with your CloudFront distribution","By enabling Geo Restriction","By enabling Signed URLs","By enabling Origin Shield","You can protect your CloudFront distribution from web attacks by associating a WAF web ACL, which defines the security rules that will be applied to incoming requests."
"What is the primary benefit of using Amazon CloudFront with Amazon S3?","Improved performance and scalability for delivering static content","Automated backups of S3 data","Enhanced security for S3 buckets","Reduced costs for S3 storage","CloudFront improves the performance and scalability of delivering static content stored in S3 by caching it at edge locations around the world."
"How does CloudFront help reduce the load on your origin server?","By caching content at edge locations and serving requests from the cache","By automatically scaling the origin server","By encrypting data in transit","By blocking malicious traffic","CloudFront reduces the load on your origin server by caching content at edge locations, so requests are served from the cache instead of the origin."
"Which of the following can be used to configure a CloudFront distribution?","AWS Management Console, AWS CLI, AWS SDKs","AWS CloudTrail, AWS Config, AWS CloudWatch","AWS IAM, AWS KMS, AWS Secrets Manager","AWS CodePipeline, AWS CodeBuild, AWS CodeDeploy","You can configure a CloudFront distribution using the AWS Management Console, AWS CLI (Command Line Interface), or AWS SDKs (Software Development Kits)."
"What is the difference between 'Whitelist' and 'Blacklist' geo restriction in CloudFront?","Whitelist allows only specified countries to access content, while blacklist blocks specified countries","Blacklist allows only specified countries to access content, while whitelist blocks specified countries","Whitelist encrypts traffic from specified countries, while blacklist does not","Blacklist encrypts traffic from specified countries, while whitelist does not","Whitelist (now known as 'Allowed Countries') allows only specified countries to access content, while blacklist (now known as 'Blocked Countries') blocks specified countries from accessing the content."
"How can you ensure that your CloudFront distribution always serves the latest version of a dynamic webpage?","By setting a very short TTL (Time To Live) for the cache","By disabling caching altogether","By using Signed URLs","By enabling Geo Restriction","Setting a short TTL ensures that CloudFront frequently checks the origin server for updates, minimising the chance of serving stale content."
"Which of the following is a valid use case for CloudFront Functions related to HTTP Headers?","Modifying request or response headers based on user location or device","Storing user session data","Encrypting the request body","Compressing the response body","CloudFront Functions are often used to modify request or response headers, enabling tasks like adding security headers or customising content based on user attributes."
"You want to implement a simple URL redirect using CloudFront. Which feature is most suitable for this?","CloudFront Functions","Lambda@Edge","Origin Groups","Invalidations","CloudFront Functions are ideal for simple URL redirects due to their low latency and ease of configuration."
"Which feature can be used to create a high availability architecture with CloudFront?","Origin Groups","Cache Policies","Function Associations","Invalidation Policies","Origin Groups provides the ability to automatically failover from a primary origin to a secondary origin in the event of the primary origin's failure."
"Which of the following best describes the purpose of a CloudFront 'Cache Policy'?","A CloudFront Cache Policy controls how long and in what fashion content is cached at edge locations","A CloudFront Cache Policy controls the access rights to objects stored in the S3 origin","A CloudFront Cache Policy controls which content is returned during an error","A CloudFront Cache Policy controls which requests are routed to the origin","A CloudFront Cache Policy is used to specify which parts of the client request (headers, cookies, and query strings) are used to create the cache key for an object."
"What is the primary function of Amazon CloudFront?","Content Delivery Network (CDN)","Compute Engine","Database Management System","Message Queue Service","CloudFront is a Content Delivery Network (CDN) that caches and distributes content from origin servers to users globally."
"Which AWS service is commonly used as an origin for content served by Amazon CloudFront?","S3 (Simple Storage Service)","Lambda","EC2 (Elastic Compute Cloud)","DynamoDB","S3 buckets are frequently used as origins for static content delivered via CloudFront."
"Which type of content can Amazon CloudFront NOT deliver?","Dynamic, static, streaming and live content","Static content only","Video content only","Downloadable content only","CloudFront can deliver a wide range of content types, including static, dynamic, streaming, and live video."
"What is the purpose of CloudFront's geo-restriction feature?","To control the geographical locations from which users can access your content","To automatically translate content into different languages based on the user's location","To optimise content delivery based on the user's internet speed","To filter content based on user demographics","Geo-restriction allows you to restrict access to your content based on the geographic location of the users making the requests."
"How does Amazon CloudFront improve website loading times?","By caching content closer to users","By optimising database queries","By reducing the size of images","By compressing HTML files","CloudFront caches content in edge locations around the world, bringing the content closer to the users and reducing latency."
"What is the purpose of CloudFront Functions?","To run lightweight code at CloudFront edge locations","To manage IAM roles and permissions","To monitor CloudFront performance metrics","To configure CloudFront pricing plans","CloudFront Functions allow you to run lightweight code at edge locations to manipulate requests and responses, providing custom logic close to users."
"Which of the following is a benefit of using Amazon CloudFront with AWS Shield?","Protection against DDoS attacks","Automatic failover to another region","Lower EC2 instance costs","Simplified IAM management","Using CloudFront with AWS Shield provides protection against DDoS attacks by absorbing malicious traffic at the edge."
"What protocol does Amazon CloudFront use for secure communication with viewers?","HTTPS","FTP","SMTP","HTTP","CloudFront uses HTTPS to encrypt communication between viewers and the edge locations."
"What happens when an edge location in Amazon CloudFront does not have the requested content?","It retrieves the content from the origin server","It returns an error to the user","It redirects the user to another website","It serves a default 'under construction' page","When an edge location doesn't have the requested content, it fetches it from the origin server and caches it for future requests."
"What is the purpose of 'Invalidations' in Amazon CloudFront?","To remove cached content from edge locations","To grant users specific permissions","To encrypt content at rest","To trigger Lambda functions","Invalidations are used to remove outdated content from CloudFront edge caches, ensuring that users receive the latest version of your content."
"What is the primary purpose of Amazon CloudFront in the AWS ecosystem?","Content Delivery Network (CDN)","Database Management System","Compute Instance Service","Email Sending Service","CloudFront is a CDN that caches content at edge locations to reduce latency and improve performance for users around the world."
"Which HTTP method is typically NOT cached by default in Amazon CloudFront?","POST","GET","HEAD","OPTIONS","CloudFront typically only caches GET and HEAD requests by default. POST requests, which usually involve data modification, are not cached."
"When configuring an Amazon CloudFront distribution, what is an 'Origin'?","The source location where CloudFront fetches content","A user's geographical location","A pricing plan for CloudFront","A security group associated with the distribution","The Origin is the server or storage location (e.g., an S3 bucket, EC2 instance, or custom origin) from which CloudFront retrieves the content to be distributed."
"Which Amazon CloudFront feature allows you to serve different content based on the viewer's device type?","Lambda@Edge Device Detection","Geo Restriction","Origin Shield","Invalidations","Lambda@Edge allows you to execute code at CloudFront edge locations, enabling you to modify content or behaviour based on device type or other factors."
"What is the purpose of using signed URLs or signed cookies with Amazon CloudFront?","To restrict access to content to authenticated users","To encrypt content at rest","To automatically scale the CloudFront distribution","To improve the speed of content delivery","Signed URLs and signed cookies allow you to control who can access your content, providing authentication and authorisation."
"Which Amazon CloudFront feature can help reduce origin load by caching content closer to the users and protecting the origin server?","Origin Shield","Field-Level Encryption","Invalidation","Geo Restriction","Origin Shield adds an extra layer of caching in a specific region to reduce load on the origin server."
"You need to ensure that your Amazon CloudFront distribution serves only HTTPS traffic. Which configuration option should you use?","Viewer Protocol Policy","Allowed HTTP Methods","Cache Policy","Origin Access Identity (OAI)","The Viewer Protocol Policy determines how CloudFront handles HTTP and HTTPS requests. Selecting 'Redirect HTTP to HTTPS' or 'HTTPS Only' enforces HTTPS traffic."
"Which Amazon CloudFront feature allows you to customize the content that CloudFront delivers based on attributes of the request, such as headers, cookies, or query strings?","Cache Policies","Origin Request Policies","Geo Restriction","Invalidation","Cache Policies determine which aspects of the request are used to generate the cache key, allowing for different versions of content to be cached based on request attributes."
"What is the purpose of using an Origin Access Identity (OAI) with Amazon CloudFront and an S3 bucket?","To restrict direct access to the S3 bucket, forcing users to access content only through CloudFront","To encrypt data stored in the S3 bucket","To automatically back up the S3 bucket to another region","To allow CloudFront to write data to the S3 bucket","An OAI is used to grant CloudFront access to your S3 bucket while preventing users from directly accessing the bucket without going through CloudFront. This enhances security."
"When should you invalidate a file in Amazon CloudFront?","When the file at the origin has been updated and you want the updated version served immediately","When you want to increase the cache duration of the file","When you want to reduce the origin's load","When you want to encrypt the file at the edge location","Invalidation is used to remove a file from CloudFront's cache, forcing CloudFront to retrieve the latest version from the origin on the next request."