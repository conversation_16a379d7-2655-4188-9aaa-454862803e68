"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"When integrating a private backend service with Amazon API Gateway using a VPC link, which component is required to establish the connection?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Internet Gateway","A Network Load Balancer is required to integrate a private backend service with Amazon API Gateway via a VPC link."
"A developer wants to secure their Amazon API Gateway REST API using AWS WAF. At which level can AWS WAF be associated?","Stage or API level","Resource level","Method level","Integration request level","AWS WAF can be associated with an Amazon API Gateway REST API at the stage or API level to protect against common web exploits."
"To enable caching for an Amazon API Gateway REST API stage, which setting needs to be configured?","Cache capacity","Throttling limits","Usage plans","Request validation","Configuring the cache capacity for an API Gateway stage enables caching of responses to reduce latency and the number of calls to the backend."
"A developer wants to pass parameters from the request path to a Lambda function integrated with Amazon API Gateway. How can this be achieved?","Mapping templates","Request validators","Authorizers","Usage plans","Mapping templates in API Gateway allow transformation of the request payload, including extracting path parameters, before sending it to the backend integration like a Lambda function."
"Which type of Amazon API Gateway endpoint is recommended for APIs accessed from the internet?","Edge-optimised","Regional","Private","VPC endpoint","Edge-optimised endpoints are recommended for public APIs as they use CloudFront to improve performance for geographically diverse clients."
"A developer needs to control access to their Amazon API Gateway REST API based on custom logic. Which type of authorizer should they use?","Lambda authorizer","IAM authorizer","Cognito user pool authorizer","Resource policy","A Lambda authorizer (formerly custom authorizer) allows developers to use a Lambda function to control access to API Gateway methods based on custom authentication and authorization logic."
"When deploying an Amazon API Gateway REST API, what does a deployment resource represent?","A snapshot of the API configuration","A specific stage of the API","A collection of API resources","A single API method","A deployment resource in API Gateway represents a snapshot of the API configuration that is deployed to a stage."
"A developer wants to limit the number of requests that clients can make to their Amazon API Gateway REST API. Which feature should they use?","Usage plans","API keys","Resource policies","Stage variables","Usage plans in API Gateway allow you to set throttling and quota limits on API requests for individual clients or groups of clients."
"To enable detailed monitoring for an Amazon API Gateway REST API, which service should be integrated?","Amazon CloudWatch Logs","AWS CloudTrail","Amazon S3","AWS Config","Amazon CloudWatch Logs can be integrated with API Gateway to log detailed information about API requests and responses for monitoring and troubleshooting."
"A developer is configuring CORS for their Amazon API Gateway REST API. Which HTTP header is crucial for allowing cross-origin requests?","Access-Control-Allow-Origin","Content-Type","Authorization","User-Agent","The `Access-Control-Allow-Origin` header is essential for enabling Cross-Origin Resource Sharing (CORS) in API Gateway, allowing web browsers to make requests from different domains."
"A developer is troubleshooting a 5xx error from their Amazon API Gateway REST API. Where should they look first for detailed error information?","CloudWatch Logs","API Gateway Dashboard","AWS CloudTrail","X-Ray Trace","CloudWatch Logs integrated with API Gateway provide detailed request and response logs, which are essential for troubleshooting 5xx errors."
"To enable tracing of requests through their Amazon API Gateway REST API and integrated services, which AWS service should a developer enable?","AWS X-Ray","Amazon CloudWatch Synthetics","AWS Config","Amazon Inspector","AWS X-Ray can be enabled for API Gateway stages to trace requests as they flow through the API and backend services, helping to identify performance bottlenecks and errors."
"A developer wants to deploy different versions of their Amazon API Gateway REST API simultaneously. How can they achieve this?","Using stages","Using resource policies","Using usage plans","Using API keys","API Gateway stages allow deploying different versions of an API simultaneously, each with its own configuration and endpoint."
"When using an Amazon API Gateway REST API with a Lambda integration, what is the purpose of the Integration Request?","To transform the incoming request before sending it to the Lambda function","To validate the incoming request parameters","To authenticate the caller","To define the response format","The Integration Request in API Gateway is used to transform the incoming request from the client into a format that the backend integration (like a Lambda function) can understand."
"A developer needs to secure their Amazon API Gateway REST API so that only authenticated users from a specific Cognito User Pool can access it. Which authorizer type should they configure?","Cognito user pool authorizer","Lambda authorizer","IAM authorizer","Resource policy","A Cognito user pool authorizer in API Gateway allows securing API methods using a specified Amazon Cognito user pool."
"To allow a client application running in a web browser to make requests to an Amazon API Gateway REST API hosted on a different domain, what needs to be configured?","CORS","Throttling","Caching","Request validation","Cross-Origin Resource Sharing (CORS) must be configured in API Gateway to allow web applications from one domain to access resources on another domain."
"A developer wants to use the AWS CLI to deploy an existing Amazon API Gateway REST API to a new stage named 'beta'. Which command would they use?","aws apigateway create-deployment --rest-api-id <api-id> --stage-name beta","aws apigateway deploy-api --rest-api-id <api-id> --stage-name beta","aws apigateway publish-stage --rest-api-id <api-id> --stage-name beta","aws apigateway update-stage --rest-api-id <api-id> --stage-name beta","The `aws apigateway create-deployment` command is used to deploy an API Gateway REST API to a specified stage."
"When configuring an Amazon API Gateway REST API, what is the primary purpose of a Resource?","Represents a logical path in the API","Defines the integration with the backend","Specifies the request and response models","Controls access to the API","A Resource in API Gateway represents a logical path (e.g., /users, /products) that clients can interact with."
"A developer needs to define the structure and format of the request and response payloads for their Amazon API Gateway REST API. Which component is used for this?","Models","Mapping templates","Request validators","Method responses","Models in API Gateway are used to define the schema (structure and format) of request and response payloads."
"To control who can invoke an Amazon API Gateway REST API using AWS Identity and Access Management (IAM), which type of authorizer should be used?","IAM authorizer","Lambda authorizer","Cognito user pool authorizer","Resource policy","An IAM authorizer in API Gateway allows using AWS IAM users, groups, and roles to control access to API methods."
"A developer wants to transform the response from a backend service before sending it back to the client through Amazon API Gateway. Which component should they use?","Integration Response","Method Response","Request Validator","Mapping Template","The Integration Response in API Gateway is used to transform the response received from the backend integration before it is sent back to the client."
"When configuring an Amazon API Gateway REST API, what is the purpose of a Method?","Represents an HTTP verb (e.g., GET, POST) for a resource","Defines the integration with the backend","Specifies the request and response models","Controls access to the API","A Method in API Gateway represents an HTTP verb (like GET, POST, PUT, DELETE) that can be performed on a specific resource."
"A developer needs to configure their Amazon API Gateway REST API to handle different response formats based on the `Accept` header in the client's request. Which feature allows this?","Content negotiation","Request validation","Mapping templates","Method responses","API Gateway supports content negotiation, allowing the API to return different response formats based on the `Accept` header in the client's request."
"To prevent clients from sending requests to an Amazon API Gateway REST API that do not conform to a defined schema, which feature should be used?","Request validation","Usage plans","Authorizers","Resource policies","Request validation in API Gateway ensures that incoming requests conform to defined models (schemas) before proceeding to the integration."
"A developer wants to export the definition of their Amazon API Gateway REST API to an OpenAPI (Swagger) file. Which AWS CLI command can be used?","aws apigateway get-export --rest-api-id <api-id> --stage-name <stage-name> --export-type oas30","aws apigateway export-api --rest-api-id <api-id> --stage-name <stage-name> --export-type oas30","aws apigateway generate-openapi --rest-api-id <api-id> --stage-name <stage-name>","aws apigateway download-api-definition --rest-api-id <api-id> --stage-name <stage-name>","The `aws apigateway get-export` command is used to export the definition of an API Gateway REST API in various formats, including OpenAPI (Swagger)."
"When integrating an Amazon API Gateway REST API with an HTTP endpoint, what is the purpose of the Integration Response's 'Selection Pattern'?","To map response status codes from the backend to API Gateway method response status codes","To transform the response payload","To validate the response headers","To control caching behaviour","The 'Selection Pattern' in an HTTP integration's Integration Response is used to map response status codes from the backend to corresponding Method Response status codes in API Gateway."
"A developer needs to secure their Amazon API Gateway REST API using a resource policy to allow access only from a specific VPC endpoint. Which principal should be specified in the policy?","aws:sourceVpc","aws:PrincipalOrgID","aws:SourceIp","aws:PrincipalAccount","The `aws:sourceVpc` condition key in an API Gateway resource policy can be used to restrict access to requests originating from a specific VPC endpoint."
"When using Amazon API Gateway with a Lambda integration, what is the purpose of the 'Use Lambda Proxy integration' option?","It simplifies the integration by passing the entire request event to the Lambda function and expecting a specific response format","It allows fine-grained control over request and response mapping","It enables caching for the Lambda function","It automatically handles authentication and authorization","The 'Use Lambda Proxy integration' option simplifies the integration by sending the entire request as input to the Lambda function and expecting the output in a specific format, reducing the need for extensive mapping templates."
"A developer wants to configure a custom domain name for their Amazon API Gateway REST API. Which AWS service is typically used in conjunction with API Gateway for this?","Amazon Route 53","Amazon CloudFront","AWS Certificate Manager","Amazon S3","Amazon Route 53 is typically used to manage DNS records and point a custom domain name to an API Gateway custom domain name."
"To monitor the number of requests to a specific method of their Amazon API Gateway REST API, which CloudWatch metric should a developer examine?","Count","Latency","IntegrationLatency","Errors","The `Count` metric in CloudWatch for API Gateway indicates the total number of requests received for a specific API, stage, or method."
"A developer is designing an Amazon API Gateway REST API and wants to enable request throttling at the account level. Which setting controls this?","Account-level throttling limits","Stage-level throttling limits","Method-level throttling limits","Usage plan throttling limits","Account-level throttling limits in API Gateway control the maximum rate and burst of requests allowed for all APIs in an AWS account."
"When using Amazon API Gateway with a private integration (VPC link), what is the primary benefit compared to a public integration?","Improved security by keeping traffic within the VPC","Lower latency","Reduced cost","Easier configuration","Private integrations with VPC links improve security by allowing API Gateway to access resources within a VPC without exposing them to the public internet."
"A developer needs to configure an Amazon API Gateway REST API to return a specific response to the client without forwarding the request to a backend. Which integration type should they use?","Mock integration","Lambda integration","HTTP integration","AWS service integration","A Mock integration in API Gateway allows the API to return a predefined response directly to the client without invoking a backend endpoint."
"To secure an Amazon API Gateway REST API using API keys and usage plans, where should the API key be included in the client's request?","In the `x-api-key` header","In the request body","In the query parameters","In the URL path","When using API keys for authorization in API Gateway, the API key should be included in the `x-api-key` header of the client's request."
"A developer is using Amazon API Gateway to expose an AWS service (e.g., SQS). Which integration type is most suitable?","AWS service integration","Lambda integration","HTTP integration","Mock integration","An AWS service integration in API Gateway allows direct integration with various AWS services, such as SQS, Kinesis, or DynamoDB."
"When configuring caching for an Amazon API Gateway REST API stage, what is the maximum size of a cached response?","1048576 bytes (1 MB)","524288 bytes (512 KB)","2097152 bytes (2 MB)","4194304 bytes (4 MB)","The maximum size of a cached response in Amazon API Gateway is 1048576 bytes (1 MB)."
"A developer needs to configure an Amazon API Gateway REST API to handle binary data in the request and response. Where is this configured?","Binary Media Types","Mapping Templates","Request Validators","Method Responses","Binary Media Types in API Gateway are configured at the API level to specify which content types should be treated as binary data."
"When using a Lambda authorizer with an Amazon API Gateway REST API, what is the expected output format from the Lambda function?","An IAM policy document","A simple boolean value","A JWT token","A signed URL","A Lambda authorizer is expected to return an IAM policy document that specifies the permissions for the caller."
"A developer wants to enable request tracing for a specific client using their Amazon API Gateway REST API. Which header should the client include in the request?","X-Amz-Trace-Id","X-Amz-Request-Id","X-Api-Key","Authorization","The `X-Amz-Trace-Id` header can be included in a client request to enable tracing for that specific request through API Gateway and integrated services."
"To configure request and response transformations for an Amazon API Gateway REST API using VTL (Velocity Template Language), which component is used?","Mapping templates","Models","Request validators","Method responses","Mapping templates in API Gateway use VTL to transform the request payload before sending it to the backend and the response payload before sending it back to the client."
"A developer is implementing a WebSocket API using Amazon API Gateway. Which backend integration type is commonly used for handling WebSocket messages?","Lambda function","HTTP endpoint","Mock integration","AWS service integration","Lambda functions are commonly used as backend integrations for handling messages in Amazon API Gateway WebSocket APIs."
"When configuring an Amazon API Gateway WebSocket API, what is the purpose of the `$connect` route?","To handle initial connection requests","To handle incoming messages","To handle disconnection requests","To handle errors","The `$connect` route in a WebSocket API handles the initial request to establish a connection."
"A developer needs to send messages from their backend service to connected clients of an Amazon API Gateway WebSocket API. Which API Gateway component is used for this?","Management API","REST API","Data Plane API","Control Plane API","The Management API in API Gateway is used by backend services to send messages to connected clients of a WebSocket API."
"A developer is troubleshooting issues with their Amazon API Gateway WebSocket API. Where can they find detailed logs for connection, message, and disconnection events?","CloudWatch Logs","API Gateway Dashboard","AWS CloudTrail","X-Ray Trace","CloudWatch Logs integrated with API Gateway provide detailed logs for events in a WebSocket API, including connections, messages, and disconnections."
"When configuring an Amazon API Gateway WebSocket API, what is the purpose of the `$disconnect` route?","To handle disconnection requests","To handle initial connection requests","To handle incoming messages","To handle errors","The `$disconnect` route in a WebSocket API handles requests to close a connection."
"A developer wants to define the format of messages exchanged in their Amazon API Gateway WebSocket API. Which component is used for this?","Models","Mapping templates","Route requests","Route responses","Models in API Gateway can be used to define the schema (structure and format) of messages for WebSocket APIs."
"To enable tracing for an Amazon API Gateway WebSocket API, which AWS service should a developer enable?","AWS X-Ray","Amazon CloudWatch Synthetics","AWS Config","Amazon Inspector","AWS X-Ray can be enabled for API Gateway WebSocket API stages to trace messages as they are processed."
"A developer needs to configure their Amazon API Gateway WebSocket API to route incoming messages based on the message content. Which feature allows this?","Route selection expression","Mapping templates","Request validators","Models","The route selection expression in a WebSocket API determines which route an incoming message is directed to based on its content."
"When using a Lambda function as a backend integration for an Amazon API Gateway WebSocket API, how does the Lambda function identify the connected client to send a message back?","Using the connection ID","Using the request ID","Using the API key","Using the user ID","The connection ID provided in the event to the Lambda function is used to identify the specific client connection to send messages back to using the Management API."
"When configuring an Amazon API Gateway HTTP API, what is the primary difference in routing compared to REST APIs?","HTTP APIs use routes, while REST APIs use resources and methods","HTTP APIs use resources and methods, while REST APIs use routes","HTTP APIs use stages, while REST APIs use deployments","HTTP APIs use custom domains, while REST APIs use default endpoints","HTTP APIs use a simpler routing mechanism based on routes, whereas REST APIs use a combination of resources and methods."
"A developer wants to secure their Amazon API Gateway HTTP API using OIDC (OpenID Connect) or OAuth 2.0. Which type of authorizer should they use?","JWT authorizer","Lambda authorizer","IAM authorizer","Cognito user pool authorizer","A JWT authorizer in API Gateway HTTP APIs allows securing API access using JSON Web Tokens (JWTs) from OIDC or OAuth 2.0 providers."
"A developer is migrating a REST API to an Amazon API Gateway HTTP API. What is a key consideration regarding features?","HTTP APIs have a smaller feature set compared to REST APIs","HTTP APIs have a larger feature set compared to REST APIs","HTTP APIs support all features of REST APIs","HTTP APIs only support a subset of REST API features","HTTP APIs offer a smaller feature set compared to REST APIs, focusing on core functionality for building efficient and cost-effective APIs."
"When configuring an Amazon API Gateway HTTP API, what is the purpose of a CORS configuration?","To allow cross-origin requests from web browsers","To secure the API using API keys","To enable caching of responses","To define request throttling limits","CORS configuration in HTTP APIs allows web browsers to make requests from a different domain than where the API is hosted."
"A developer wants to use the AWS CLI to create a new Amazon API Gateway HTTP API. Which command would they use?","aws apigatewayv2 create-api --name MyHttpApi --protocol HTTP","aws apigateway create-api --name MyHttpApi --protocol HTTP","aws apigateway create-http-api --name MyHttpApi","aws apigatewayv2 create-http-api --name MyHttpApi","The `aws apigatewayv2 create-api` command with `--protocol HTTP` is used to create a new Amazon API Gateway HTTP API."
"When integrating an Amazon API Gateway HTTP API with a Lambda function, what is the default payload format sent to the Lambda function?","Version 2.0","Version 1.0","Proxy integration format","Custom format","The default payload format for Lambda integrations in HTTP APIs is Version 2.0."
"A developer needs to configure a custom domain name for their Amazon API Gateway HTTP API. Which AWS service is typically used in conjunction with API Gateway for this?","Amazon Route 53","Amazon CloudFront","AWS Certificate Manager","Amazon S3","Amazon Route 53 is typically used to manage DNS records and point a custom domain name to an API Gateway custom domain name."
"To monitor the latency of requests to their Amazon API Gateway HTTP API, which CloudWatch metric should a developer examine?","Latency","Count","IntegrationLatency","Errors","The `Latency` metric in CloudWatch for API Gateway HTTP APIs indicates the end-to-end latency of requests."
"A developer is configuring an Amazon API Gateway HTTP API and wants to enable CORS. Which configuration option is used for this?","corsConfiguration","accessControlAllowOrigin","corsEnabled","allowCrossDomain","The `corsConfiguration` option is used to configure CORS for an Amazon API Gateway HTTP API."
"When using an Amazon API Gateway HTTP API with a Lambda integration, how can a developer access the request headers in the Lambda function?","Through the `headers` property in the event object","Through the `requestContext` property in the event object","Through environment variables","Through a separate API Gateway SDK","In the default Version 2.0 payload format for Lambda integrations in HTTP APIs, request headers are available through the `headers` property in the event object."
"A developer needs to configure an Amazon API Gateway HTTP API to return a specific response based on the status code returned by the backend. Which feature allows this?","Route responses","Integration responses","Models","Mapping templates","Route responses in HTTP APIs allow configuring different responses based on the status code returned by the backend integration."
"When using an Amazon API Gateway HTTP API, what is the primary benefit of using stages?","To manage different versions of the API","To control access to the API","To enable caching","To define request throttling limits","Stages in HTTP APIs are used to manage different versions of the API, allowing for independent deployment and configuration."
"A developer wants to use the AWS CLI to deploy an existing Amazon API Gateway HTTP API to a new stage named 'prod'. Which command would they use?","aws apigatewayv2 create-stage --api-id <api-id> --stage-name prod --deployment-id <deployment-id>","aws apigateway deploy-api --api-id <api-id> --stage-name prod","aws apigatewayv2 deploy-api --api-id <api-id> --stage-name prod","aws apigateway update-stage --api-id <api-id> --stage-name prod","The `aws apigatewayv2 create-stage` command is used to create a new stage for an HTTP API and associate it with a deployment."
"When configuring an Amazon API Gateway HTTP API, what is the purpose of a default stage?","A stage that is automatically created and associated with the $default route","A stage that is used for testing purposes","A stage that has caching enabled by default","A stage that has throttling enabled by default","A default stage in HTTP APIs is automatically created and associated with the `$default` route, which captures requests that don't match any other defined routes."
"A developer needs to secure their Amazon API Gateway HTTP API using a resource policy to allow access only from a specific IP address range. Which condition key should be used in the policy?","aws:SourceIp","aws:sourceVpc","aws:PrincipalOrgID","aws:PrincipalAccount","The `aws:SourceIp` condition key in an API Gateway resource policy can be used to restrict access to requests originating from a specific IP address range."
"When using an Amazon API Gateway HTTP API with a Lambda integration, how can a developer access the query string parameters in the Lambda function?","Through the `queryStringParameters` property in the event object","Through the `requestContext` property in the event object","Through environment variables","Through a separate API Gateway SDK","In the default Version 2.0 payload format for Lambda integrations in HTTP APIs, query string parameters are available through the `queryStringParameters` property in the event object."
"A developer wants to configure request throttling for a specific route in their Amazon API Gateway HTTP API. Where is this configured?","Route settings","Stage settings","API settings","Usage plan settings","Request throttling for specific routes in HTTP APIs is configured within the route settings."
"To monitor the number of 4xx errors for their Amazon API Gateway HTTP API, which CloudWatch metric should a developer examine?","4xxError","Count","Latency","IntegrationLatency","The `4xxError` metric in CloudWatch for API Gateway HTTP APIs indicates the number of client-side errors (status codes 400-499)."
"A developer is configuring an Amazon API Gateway HTTP API and wants to enable detailed logging of requests and responses. Which logging format should they use?","JSON","CSV","XML","Text","JSON is the recommended logging format for detailed access logging in Amazon API Gateway HTTP APIs."
"A developer needs to configure an Amazon API Gateway HTTP API to forward the original host header to the backend. Which setting controls this?","Append to the end of the URL","Use the original host header","Override with a new host header","Remove the host header","The 'Use the original host header' setting in an HTTP integration configuration allows forwarding the original host header to the backend."
"When using an Amazon API Gateway HTTP API, what is the primary benefit of using a default route?","To handle requests that do not match any other defined routes","To handle all GET requests","To handle all POST requests","To handle requests with invalid paths","The default route (`$default`) in HTTP APIs captures requests that do not match any other explicitly defined routes."
"A developer wants to use the AWS CLI to delete a specific route from their Amazon API Gateway HTTP API. Which command would they use?","aws apigatewayv2 delete-route --api-id <api-id> --route-id <route-id>","aws apigateway delete-route --api-id <api-id> --route-id <route-id>","aws apigatewayv2 remove-route --api-id <api-id> --route-id <route-id>","aws apigateway remove-route --api-id <api-id> --route-id <route-id>","The `aws apigatewayv2 delete-route` command is used to delete a specific route from an Amazon API Gateway HTTP API."
"When configuring an Amazon API Gateway HTTP API, what is the purpose of a integration?","To connect the API Gateway route to a backend endpoint","To define the request and response models","To control access to the API","To enable caching","An integration in HTTP APIs connects a route to a backend endpoint, such as a Lambda function or an HTTP endpoint."
"A developer needs to configure an Amazon API Gateway HTTP API to return a 404 response for requests that do not match any routes. How can this be achieved?","By configuring the default route to return a 404 response","By configuring a request validator","By configuring a method response","By configuring a usage plan","Configuring the default route (`$default`) in an HTTP API to return a 404 response is a common way to handle requests that do not match any other defined routes."
"When using an Amazon API Gateway HTTP API with a Lambda integration, how can a developer access the request body in the Lambda function?","Through the `body` property in the event object","Through the `requestContext` property in the event object","Through environment variables","Through a separate API Gateway SDK","In the default Version 2.0 payload format for Lambda integrations in HTTP APIs, the request body is available through the `body` property in the event object."
"A developer wants to configure a custom authorizer for their Amazon API Gateway HTTP API using a Lambda function. Which type of authorizer should they create?","Lambda authorizer","JWT authorizer","IAM authorizer","Cognito user pool authorizer","A Lambda authorizer can be created for HTTP APIs to use a Lambda function for custom authentication and authorization."
"To monitor the number of 5xx errors for their Amazon API Gateway HTTP API, which CloudWatch metric should a developer examine?","5xxError","Count","Latency","IntegrationLatency","The `5xxError` metric in CloudWatch for API Gateway HTTP APIs indicates the number of server-side errors (status codes 500-599)."
"A developer is configuring an Amazon API Gateway HTTP API and wants to enable detailed logging of requests and responses to an S3 bucket. Which permission is required for the API Gateway service principal?","s3:PutObject","s3:GetObject","s3:ListBucket","s3:DeleteObject","The API Gateway service principal requires the `s3:PutObject` permission to write access logs to an S3 bucket."
"When using an Amazon API Gateway HTTP API with a Kinesis Data Firehose integration, which permission is required for the API Gateway service principal?","firehose:PutRecord","firehose:GetRecord","firehose:ListStreams","firehose:DeleteStream","The API Gateway service principal requires the `firehose:PutRecord` permission to send access logs to a Kinesis Data Firehose stream."
"A developer needs to configure an Amazon API Gateway HTTP API to return a 400 response for requests with invalid query string parameters based on a defined schema. Which feature should they use?","Request validation","Route responses","Mapping templates","Authorizers","Request validation in HTTP APIs can be configured to validate query string parameters against a defined schema and return a 400 response for invalid requests."
"When using an Amazon API Gateway HTTP API, what is the primary benefit of using a custom domain name?","To provide a user-friendly and branded URL for the API","To improve API performance","To reduce API costs","To enhance API security","A custom domain name provides a user-friendly and branded URL for accessing an Amazon API Gateway HTTP API."
"A developer wants to use the AWS CLI to get the details of a specific stage of their Amazon API Gateway HTTP API. Which command would they use?","aws apigatewayv2 get-stage --api-id <api-id> --stage-name <stage-name>","aws apigateway get-stage --api-id <api-id> --stage-name <stage-name>","aws apigatewayv2 describe-stage --api-id <api-id> --stage-name <stage-name>","aws apigateway describe-stage --api-id <api-id> --stage-name <stage-name>","The `aws apigatewayv2 get-stage` command is used to retrieve details about a specific stage of an Amazon API Gateway HTTP API."
"When configuring an Amazon API Gateway HTTP API, what is the purpose of a route key?","A string that represents the HTTP method and resource path for a route","A unique identifier for a route","A string that represents the integration type for a route","A string that represents the authorizer for a route","A route key in HTTP APIs is a string that represents the combination of the HTTP method and the resource path for a specific route (e.g., `GET /users`)."
"A developer needs to configure an Amazon API Gateway HTTP API to return a 500 response for errors occurring in the backend integration. Which feature allows this?","Integration responses","Route responses","Models","Mapping templates","Integration responses in HTTP APIs can be configured to map errors from the backend integration to specific HTTP status codes, such as 500."
"When using an Amazon API Gateway HTTP API with a Lambda integration, how can a developer access the path parameters in the Lambda function?","Through the `pathParameters` property in the event object","Through the `requestContext` property in the event object","Through environment variables","Through a separate API Gateway SDK","In the default Version 2.0 payload format for Lambda integrations in HTTP APIs, path parameters are available through the `pathParameters` property in the event object."
"A developer wants to configure a JWT authorizer for their Amazon API Gateway HTTP API. Which information is required to configure the authorizer?","Issuer and Audience","API Key and Usage Plan ID","IAM Role ARN","Cognito User Pool ID","Configuring a JWT authorizer requires specifying the issuer and audience of the JWT tokens."
"To monitor the number of requests that result in a cold start for a Lambda function integrated with an Amazon API Gateway HTTP API, which CloudWatch metric should a developer examine?","Lambda cold starts","IntegrationLatency","Latency","Count","While there isn't a direct 'Lambda cold starts' metric in API Gateway, monitoring the `IntegrationLatency` metric and correlating it with Lambda's `Duration` and `Invocations` metrics can help identify cold starts."
"What is the primary function of Amazon API Gateway?","To create, publish, maintain, monitor, and secure APIs","To store static website content","To manage virtual machines","To analyse large datasets","API Gateway handles all tasks involved in accepting and processing up to hundreds of thousands of concurrent API calls, including traffic management, authorisation and access control, monitoring, and API version management."
"Which of the following integration types is used when API Gateway directly invokes a Lambda function?","AWS Lambda","HTTP Proxy","Mock","AWS Service","The AWS Lambda integration type lets your API expose AWS Lambda functions in the backend."
"What is the purpose of 'throttling' in the context of Amazon API Gateway?","To control the rate of requests to your API","To encrypt data in transit","To cache API responses","To transform request payloads","Throttling controls the rate of requests to prevent overload and ensure API availability."
"In Amazon API Gateway, what is a 'resource'?","An individual API endpoint","A group of API keys","A collection of Lambda functions","A set of IAM roles","A resource is a logical entity that an API can expose. Each resource has an identifier and can have one or more methods associated with it."
"What is the function of API Gateway 'Stages'?","To manage different versions of an API","To define user roles and permissions","To monitor API performance","To configure API security settings","Stages are used to manage and deploy different versions of your API, like 'dev', 'test', and 'prod'."
"Which of the following authentication methods is supported by Amazon API Gateway?","IAM roles","LDAP","Kerberos","Active Directory","API Gateway supports IAM roles for authentication, allowing you to control who can access your API using AWS Identity and Access Management."
"What is the purpose of 'API Gateway caching'?","To reduce latency and backend load","To encrypt data at rest","To enable cross-origin resource sharing (CORS)","To transform data formats","Caching in API Gateway helps reduce latency and the load on your backend by storing and serving frequently accessed data."
"What is the 'ANY' method in API Gateway used for?","To handle any HTTP method (GET, POST, PUT, DELETE, etc.)","To explicitly deny access to a resource","To redirect requests to another API","To create a default error response","The 'ANY' method allows an API Gateway resource to handle any HTTP method."
"You want to implement a custom authoriser in API Gateway to authenticate requests based on a JWT token. Which type of authoriser should you use?","Token-based authoriser","Request parameter-based authoriser","Header-based authoriser","Query string-based authoriser","A token-based authoriser (Lambda authoriser) is designed to authenticate requests using a token, such as a JWT."
"What is the purpose of 'mapping templates' in API Gateway?","To transform the request and response data","To define the API's structure","To configure security settings","To enable logging and monitoring","Mapping templates are used to transform the request payload from the client to a format suitable for the backend and to transform the response from the backend to a format suitable for the client."
"Which Amazon service is commonly used as the backend for an API deployed through API Gateway?","AWS Lambda","Amazon S3","Amazon EC2","Amazon CloudFront","AWS Lambda functions are commonly used as the backend for APIs deployed through API Gateway, providing serverless compute power."
"What does 'CORS' stand for in the context of API Gateway?","Cross-Origin Resource Sharing","Cross-Operating Resource System","Common-Origin Request Standard","Centralised Origin Routing Service","CORS (Cross-Origin Resource Sharing) is a mechanism that allows restricted resources (e.g. fonts, JavaScript, etc.) on a web page to be requested from another domain outside the domain from which the first resource was served."
"How can you monitor the performance of your API deployed through API Gateway?","Using Amazon CloudWatch metrics and logs","By manually checking server logs","Using AWS X-Ray alone","By monitoring network traffic","Amazon CloudWatch provides metrics and logs that allow you to monitor the performance and health of your API."
"What is the benefit of using 'API Gateway Usage Plans'?","To control access and set quotas for API consumers","To define API documentation","To monitor API traffic patterns","To configure API caching policies","Usage Plans allow you to control who can access your API and set quotas and throttling limits for different API consumers."
"What is the purpose of 'request validation' in API Gateway?","To ensure that the request payload conforms to the API's schema","To encrypt the request payload","To compress the request payload","To authenticate the user making the request","Request validation ensures that the request payload conforms to the API's schema, preventing invalid data from reaching the backend."
"Which of the following is a valid integration type for API Gateway?","HTTP","SMTP","FTP","SSH","HTTP integration allows API Gateway to forward requests to an HTTP endpoint."
"What is the purpose of the 'Test' invoke option in the API Gateway console?","To test the API endpoint without deploying it","To test the API's security settings","To test the API's caching configuration","To test the API's documentation","The 'Test' invoke option allows you to test the API endpoint directly from the console without deploying it to a stage."
"You want to deploy your API Gateway API across multiple AWS regions for high availability. What feature can you use?","Regional API endpoints","Edge-Optimised API endpoints","Private API endpoints","VPC API endpoints","Regional API endpoints can be deployed in multiple AWS regions."
"What type of endpoint should you choose on Amazon API Gateway to provide the lowest latency for your API to clients worldwide?","Edge-Optimised","Regional","Private","VPC","Edge-Optimised API endpoints use CloudFront to cache and distribute your API globally."
"What is the purpose of the `integration request` in API Gateway?","To configure how the request from the client is transformed before being sent to the backend","To define the API's documentation","To configure the API's security settings","To enable logging and monitoring","The integration request defines how the incoming request from the client is transformed before being sent to the backend service."
"What type of access can you secure through Amazon API Gateway Private Endpoints?","Access from your VPC only","Public access only","Access from specific IP addresses only","Access from AWS accounts only","Private Endpoints in API Gateway secure the API so that it can only be accessed from within your VPC."
"You want to automatically generate API documentation for your API Gateway API. Which feature can you use?","API Gateway's import/export functionality with Swagger/OpenAPI","CloudFormation","AWS Config","AWS CloudTrail","API Gateway supports exporting your API definition in Swagger/OpenAPI format, which can then be used to generate documentation."
"What is the function of 'Client Certificates' in API Gateway?","To authenticate the client making the request","To encrypt the data in transit","To cache the API responses","To authorise API requests based on IAM roles","Client certificates are used to authenticate the client making the request, adding an extra layer of security."
"In API Gateway, what does the 'Content Handling' setting control?","How binary data is handled","How errors are handled","How caching is handled","How authorisation is handled","The 'Content Handling' setting controls how API Gateway handles binary data in requests and responses."
"What is the purpose of 'binary media types' in API Gateway?","To enable the handling of binary data such as images or PDFs","To encrypt data in transit","To compress data","To validate data format","Binary media types allow API Gateway to handle binary data such as images or PDFs in requests and responses."
"Which of the following is a benefit of using API Gateway with AWS Lambda?","Serverless API development and management","Automatic database scaling","Simplified network configuration","Automated security patching for EC2 instances","Using API Gateway with Lambda enables serverless API development, as you don't need to manage servers or infrastructure."
"How can you restrict access to your API in API Gateway based on the caller's IP address?","Using resource policies","Using IAM policies","Using Lambda authorizers with IP address filtering","Using CORS policies","Resource policies allow you to restrict access to your API based on the caller's IP address."
"What is the function of the `$context` variable in API Gateway mapping templates?","To access request and connection metadata","To define the API's documentation","To configure security settings","To enable logging and monitoring","The `$context` variable provides access to request and connection metadata, such as request ID, identity, and source IP address."
"You need to implement custom logic to authenticate and authorise requests to your API. Which API Gateway feature should you use?","Lambda authorizers","IAM roles","Resource policies","Usage Plans","Lambda authorizers (custom authorisers) allow you to implement custom authentication and authorisation logic using Lambda functions."
"What is the difference between 'Edge-Optimised' and 'Regional' API Gateway endpoints?","Edge-Optimised uses CloudFront for global caching and Regional does not","Regional offers lower latency","Edge-Optimised supports more authentication methods","Regional is cheaper","Edge-Optimised endpoints use CloudFront to cache and distribute your API globally, reducing latency for clients worldwide, while Regional endpoints are deployed in a specific AWS region."
"Which feature in API Gateway allows you to prevent your API from being overwhelmed by too many requests?","Throttling","Caching","Authorisation","Validation","Throttling in API Gateway allows you to control the rate of requests to your API, preventing overload."
"What is the purpose of the 'Mock' integration type in API Gateway?","To return a static response without invoking a backend","To encrypt the request payload","To compress the request payload","To authenticate the user making the request","The 'Mock' integration type allows you to return a static response directly from API Gateway without invoking a backend service, useful for testing and prototyping."
"How can you deploy an API Gateway API programmatically?","Using AWS CloudFormation or the AWS CLI","Using the AWS Management Console only","By manually configuring each setting","Using AWS Config","You can deploy API Gateway APIs programmatically using AWS CloudFormation or the AWS CLI, allowing for infrastructure as code."
"What is a 'Deployment' in the context of Amazon API Gateway?","A snapshot of the API configuration that is deployed to a Stage","A single API request","A set of IAM permissions","A monitoring dashboard","A deployment is a snapshot of the API configuration that is deployed to a Stage, making it live and accessible."
"You want to forward the client's IP address to your backend service through API Gateway. How can you do this?","By mapping the `context.identity.sourceIp` variable to a request header","By enabling VPC integration","By using client certificates","By setting up a custom domain name","You can forward the client's IP address by mapping the `$context.identity.sourceIp` variable to a request header in the integration request mapping template."
"What is the maximum size of a request payload that API Gateway can handle by default?","10 MB","1 MB","100 KB","1 GB","API Gateway can handle request payloads up to 10 MB by default."
"How can you enable detailed logging of API requests and responses in API Gateway?","By enabling CloudWatch Logs","By enabling CloudTrail","By enabling S3 logging","By enabling VPC Flow Logs","You can enable detailed logging of API requests and responses by enabling CloudWatch Logs for your API Gateway API."
"Which integration type in API Gateway is best suited for proxying requests to an existing HTTP endpoint?","HTTP Proxy","AWS Lambda","Mock","AWS Service","The HTTP Proxy integration type allows you to proxy requests directly to an existing HTTP endpoint."
"What is the purpose of 'stage variables' in API Gateway?","To configure API behaviour differently for each stage","To define API documentation","To configure security settings","To enable logging and monitoring","Stage variables allow you to configure API behaviour differently for each stage (e.g., dev, test, prod), such as backend endpoints or API keys."
"What is the difference between an 'API Key' and a 'Client Certificate' in API Gateway?","API Keys are for identifying applications; Client Certificates are for authenticating clients","API Keys encrypt data; Client Certificates authorise requests","API Keys are free; Client Certificates cost money","API Keys are more secure than Client Certificates","API Keys are used to identify applications making requests to your API, while Client Certificates are used to authenticate the client making the request."
"Which of the following API Gateway features can help protect against common web exploits such as SQL injection and cross-site scripting (XSS)?","Request validation","Throttling","Caching","Authorisation","Request validation can help protect against common web exploits by ensuring that the request payload conforms to the API's schema and data types."
"How can you use API Gateway to transform a request before sending it to a Lambda function?","Using a request mapping template","Using a Lambda authorizer","Using a resource policy","Using a usage plan","A request mapping template in the integration request allows you to transform the request data before it's sent to the Lambda function."
"What is the role of 'IAM policies' in securing an API Gateway API?","To control who can access the API Gateway service itself","To define the API's structure","To configure security settings for the backend","To enable logging and monitoring","IAM policies are used to control who can access the API Gateway service itself and perform actions such as creating, updating, or deleting APIs."
"Which of the following is a benefit of using API Gateway?","Centralised API management","Automated database backups","Automatic scaling of EC2 instances","Simplified network configuration","API Gateway provides centralised API management, allowing you to create, publish, maintain, monitor, and secure APIs in a single place."
"You want to use API Gateway to expose a REST API that interacts with several different AWS services. Which integration type would be most appropriate?","AWS Service Proxy","HTTP Proxy","Lambda Proxy","Mock","The AWS Service Proxy integration type allows you to directly integrate with other AWS services, enabling you to build APIs that interact with multiple services."
"When using API Gateway, what is the main advantage of integrating with AWS WAF?","To protect your API from common web exploits","To enable caching of API responses","To control access to your API","To monitor API performance","Integrating with AWS WAF allows you to protect your API from common web exploits such as SQL injection, cross-site scripting (XSS), and DDoS attacks."
"You need to allow only authenticated users from your company's identity provider to access your API Gateway API. Which feature can you use?","Custom authorizer (Lambda authorizer)","IAM roles","Resource Policies","API Keys","A custom authorizer (Lambda authorizer) allows you to integrate with your company's identity provider to authenticate users and authorise access to your API."
"Which of the following is a key consideration when designing the API contract (request/response schemas) for your API Gateway API?","Backward compatibility","Cost optimisation","Performance monitoring","Security hardening","Maintaining backward compatibility is a key consideration when designing the API contract to avoid breaking existing clients when making changes to the API."
"You want to set different rate limits for different API consumers using API Gateway. How can you achieve this?","By using Usage Plans with different throttling settings","By using different API keys for each consumer","By using IAM policies to control access","By using resource policies to restrict access","Usage Plans allow you to define different throttling limits for different API consumers, providing fine-grained control over API usage."
"What is the primary function of Amazon API Gateway?","To create, manage, and secure APIs at any scale","To host static websites","To manage and deploy containerised applications","To store and retrieve data","API Gateway acts as a 'front door' for applications to access data, logic, or functionality from backend services, managing traffic, authorisation, and access control."
"Which of the following is a supported API type in Amazon API Gateway?","REST API","GraphQL API","SOAP API","gRPC API","REST APIs are a common type of API supported by API Gateway."
"What AWS service does API Gateway commonly integrate with to execute backend logic?","AWS Lambda","Amazon S3","Amazon EC2","Amazon CloudFront","API Gateway can trigger Lambda functions to execute serverless backend logic in response to API requests."
"What is the purpose of API Gateway throttling?","To protect backend services from being overwhelmed by too many requests","To improve the performance of API responses","To encrypt API traffic","To automatically scale API resources","Throttling helps to ensure the availability and performance of backend services by limiting the rate of incoming requests."
"What is the use of API Gateway resource policies?","To control who can access the API","To define the structure of API requests and responses","To define the API's caching behaviour","To specify the deployment stage of the API","Resource policies allow you to control access to your API based on source IP addresses, AWS accounts, or other criteria."
"What is the purpose of API Gateway stages?","To manage different versions of your API","To define the data format of API requests and responses","To control the level of API logging","To monitor API performance","Stages represent deployment environments for your API, such as 'dev', 'test', or 'prod', allowing you to manage different versions and configurations."
"Which of the following authentication methods is supported by Amazon API Gateway?","IAM roles and policies","Multi-Factor Authentication (MFA)","LDAP","Kerberos","IAM roles and policies can be used to control access to API Gateway APIs, allowing you to define fine-grained permissions."
"What is the benefit of using API Gateway's caching feature?","To reduce latency and improve API response times","To improve the security of API requests","To automatically scale API resources","To simplify API deployment","Caching allows API Gateway to store and serve frequently accessed data, reducing the load on backend services and improving API performance."
"What is the purpose of API Gateway authorizers?","To control access to your API based on custom logic","To transform API request and response data","To validate API request parameters","To monitor API traffic","Authorizers allow you to implement custom authentication and authorisation logic, such as validating JWT tokens or calling an external authentication service."
"What type of data transformation can be performed using API Gateway's request and response mapping?","Transforming data between different formats (e.g., JSON to XML)","Encrypting data in transit","Compressing API responses","Validating API request parameters","Mapping templates can be used to transform data between different formats, such as converting JSON to XML or vice versa."
"You need to create a WebSocket API using Amazon API Gateway. Which type of route is used to handle incoming messages?","\$default","\$connect","\$disconnect","\$message","The \$default route is used to handle incoming messages that don't match any other defined route in a WebSocket API."
"Which deployment strategy is best when updating an Amazon API Gateway API to avoid downtime?","Canary deployment","Blue/Green deployment","In-place deployment","Rolling deployment","A canary deployment allows you to release changes to a small subset of users before rolling them out to everyone, minimising the risk of downtime."
"What is the purpose of the 'integration request' in API Gateway?","To configure how API Gateway interacts with the backend service","To define the API's throttling limits","To specify the API's caching behaviour","To configure the API's logging settings","The integration request defines how API Gateway transforms and forwards the incoming request to the backend service."
"What is the purpose of the 'integration response' in API Gateway?","To configure how API Gateway transforms the response from the backend service","To define the API's authentication method","To specify the API's error handling","To configure the API's deployment stage","The integration response defines how API Gateway transforms the response received from the backend service before sending it back to the client."
"What is the purpose of defining a model in Amazon API Gateway?","To define the structure of request and response bodies","To define the API's authentication method","To specify the API's throttling limits","To configure the API's logging settings","Models define the structure of request and response bodies, allowing API Gateway to validate data and generate API documentation."
"When configuring API Gateway custom domain names, what AWS service is typically used to manage SSL/TLS certificates?","AWS Certificate Manager (ACM)","AWS Identity and Access Management (IAM)","Amazon Route 53","Amazon CloudFront","AWS Certificate Manager (ACM) is used to provision and manage SSL/TLS certificates for custom domain names in API Gateway."
"How can you monitor the performance and health of your APIs in Amazon API Gateway?","Amazon CloudWatch metrics and logs","AWS CloudTrail logs","Amazon S3 access logs","AWS Config rules","Amazon CloudWatch provides metrics and logs that can be used to monitor the performance and health of your APIs, including request latency, error rates, and cache hit ratio."
"What is the purpose of API Gateway's Usage Plans?","To control access to your API and define rate limits for different users or groups","To define the structure of API requests and responses","To configure the API's caching behaviour","To specify the API's deployment stage","Usage Plans allow you to control access to your API and define rate limits for different users or groups, providing fine-grained control over API usage."
"What is the main benefit of using Amazon API Gateway as a proxy for your backend services?","It simplifies the process of managing and securing APIs","It provides automatic scaling for backend services","It reduces the cost of running backend services","It eliminates the need for backend servers","API Gateway simplifies the process of managing and securing APIs by handling tasks such as authentication, authorisation, throttling, and request transformation."
"Which of the following is a valid method to secure an API Gateway API with IAM?","Attaching an IAM policy to the API Gateway resource","Attaching an IAM role to the client application","Configuring an IAM user for API access","Using temporary security credentials generated by STS","Attaching an IAM policy to the API Gateway resource allows you to control access based on IAM principles."
"In API Gateway, what is the purpose of 'Method Request'?","Defines the expected request parameters, headers and body for the API method","Defines the data transformation applied to the request before sending to the backend","Configures the backend integration type (e.g., Lambda, HTTP)","Specifies the caching behaviour for the API method","The 'Method Request' defines the expected structure of the incoming HTTP request, including parameters, headers and request body."
"You have an API that needs to be accessible only from a specific VPC. How can you achieve this using API Gateway?","Use a VPC Link with a Network Load Balancer (NLB)","Configure API Gateway to use a private subnet","Use AWS PrivateLink directly with API Gateway","Create a public API and restrict access using security groups","VPC Links allow your API Gateway to connect to resources within your VPC using an NLB, without exposing them to the public internet."
"What is the purpose of enabling API Gateway's CloudWatch Logs?","To troubleshoot API issues and monitor API performance","To store API request and response data in S3","To enable API caching","To configure API throttling","Enabling CloudWatch Logs provides detailed logging information that can be used to troubleshoot API issues, monitor performance, and gain insights into API usage patterns."
"What feature of API Gateway can be used to transform an XML response from a backend service into a JSON response for the client?","Mapping Templates","Authorizers","Stages","Usage Plans","Mapping Templates allow you to transform the format of request and response data, for example, converting XML to JSON."
"You want to protect your API Gateway endpoint against common web exploits. Which AWS service integrates with API Gateway to provide this protection?","AWS WAF (Web Application Firewall)","AWS Shield","AWS GuardDuty","AWS Inspector","AWS WAF can be integrated with API Gateway to protect your APIs against common web exploits such as SQL injection and cross-site scripting (XSS)."
"When using API Gateway with a Lambda function, which type of integration should be used for the most flexibility in controlling the request and response transformation?","Lambda Proxy Integration","Lambda Integration","HTTP Proxy Integration","Mock Integration","Lambda Proxy Integration provides the most flexibility, allowing the Lambda function to have complete control over the request and response."
"What is the maximum timeout for an API Gateway integration with a Lambda function?","30 seconds","60 seconds","90 seconds","120 seconds","The maximum timeout for an API Gateway integration with a Lambda function is 30 seconds."
"How can you expose a REST API built on EC2 instances via API Gateway?","Using HTTP proxy integration","Using Lambda integration and an Application Load Balancer (ALB)","Using VPC peering","Using an AWS Direct Connect connection","HTTP proxy integration allows you to proxy requests directly to your EC2 instances, provided they are accessible via HTTP/HTTPS."
"You have an API Gateway API that experiences frequent spikes in traffic. What feature can help to automatically scale the API to handle these spikes?","Automatic Scaling","Auto Scaling Groups","API Gateway automatically scales to handle traffic spikes","Lambda's scaling capabilities","API Gateway automatically scales to handle varying traffic levels without requiring any manual configuration of scaling policies."
"How can you implement authentication for an API Gateway API using JSON Web Tokens (JWTs)?","Use a Custom Authorizer","Use Cognito User Pools","Use IAM Roles","Use API Keys","A Custom Authorizer allows you to validate JWT tokens and control access to your API based on the claims within the token."
"What is the purpose of the 'Test' functionality within the API Gateway console?","To test API configuration before deploying","To monitor API performance in production","To generate API documentation","To configure API throttling limits","The 'Test' functionality allows you to send test requests to your API to verify that it is configured correctly before deploying it to a stage."
"You need to create documentation for your API Gateway API. What feature can assist with this?","API Gateway automatically generates OpenAPI (Swagger) definitions","API Gateway automatically generates code samples","API Gateway integrates with AWS CloudTrail","API Gateway provides a documentation generation wizard","API Gateway can export an OpenAPI (Swagger) definition that can be used to generate API documentation."
"What is the purpose of API Gateway's 'Request Validator'?","To validate the structure and content of incoming requests","To authenticate users accessing the API","To authorise users based on their role","To prevent denial-of-service attacks","The Request Validator checks incoming requests against a defined schema or model to ensure they are valid before being processed."
"What is the benefit of using API Gateway with a serverless architecture?","It simplifies the management and deployment of serverless APIs","It eliminates the need for backend services","It reduces the cost of running serverless applications","It automatically scales serverless functions","API Gateway simplifies the management and deployment of serverless APIs by handling tasks such as routing, authentication, authorisation, and request transformation."
"When using API Gateway to expose a backend service that requires client certificates, how can you configure API Gateway to forward the client certificate to the backend?","Configure the 'mutual TLS' setting","Use a Custom Authorizer to handle the certificate","Configure the 'Client Certificate' setting","Use a mapping template to extract the certificate","API Gateway supports mutual TLS authentication, where the client provides a certificate that API Gateway forwards to the backend service for verification."
"Which of the following API Gateway integration types allows you to integrate with a service running in a private VPC without exposing it to the public internet?","VPC Link","Lambda Integration","HTTP Proxy Integration","Mock Integration","A VPC Link allows API Gateway to integrate with resources in a private VPC without exposing them to the public internet."
"You need to implement rate limiting for your API based on the number of requests per minute. What API Gateway feature should you use?","Usage Plans","Authorizers","Stages","Models","Usage Plans allow you to define rate limits and quotas for different users or groups, controlling the number of requests they can make within a specific time period."
"What is the purpose of API Gateway's 'Mock Integration'?","To return a static response without invoking a backend service","To create a test API for development purposes","To simulate backend service errors","To automatically generate API documentation","Mock Integration allows you to return a static response without invoking a backend service, which is useful for testing and development."
"You want to integrate API Gateway with an AWS Step Functions state machine. Which integration type is most suitable?","AWS Service Integration","Lambda Integration","HTTP Integration","Mock Integration","AWS Service Integration allows direct integration with other AWS services like Step Functions, SQS, and SNS."
"What is the purpose of 'Deployment History' in API Gateway?","To track changes made to the API and revert to previous versions","To monitor API performance over time","To store API request and response data","To configure API caching settings","Deployment History allows you to track changes made to the API and revert to previous versions, which is useful for debugging and managing API updates."
"You are designing an API that requires handling large request payloads. What should you consider when using API Gateway?","API Gateway has a payload size limit of 10 MB","Use compression for large payloads","Configure API Gateway to stream the payload","Increase the API Gateway timeout","API Gateway has a payload size limit of 10 MB, so you need to design your API to handle payloads within this limit or consider alternative approaches."
"What type of endpoint does API Gateway create when you set it up to integrate with AWS Lambda?","Regional Endpoint","Edge-Optimised Endpoint","Private Endpoint","VPC Endpoint","API Gateway offers two endpoint types for Lambda integration: Regional and Edge-Optimized. Regional endpoints are specific to the region in which the API Gateway is deployed."
"How does API Gateway work with CORS (Cross-Origin Resource Sharing)?","API Gateway can automatically handle CORS requests","CORS must be configured manually in the backend service","API Gateway blocks all CORS requests by default","CORS is not relevant when using API Gateway","API Gateway can be configured to automatically handle CORS requests, simplifying the process of enabling cross-origin access to your API."
"When setting up a Lambda authorizer in API Gateway, what is the expected format of the response from the Lambda function?","A policy document defining access permissions","A JSON Web Token (JWT)","A set of user credentials","A success or failure indicator","The Lambda authorizer is expected to return a policy document that defines the access permissions for the API request."
"What is the primary difference between a Regional API and an Edge-Optimised API in API Gateway?","Edge-Optimised APIs utilise CloudFront for caching and improved latency","Regional APIs can only be deployed in one AWS region","Regional APIs offer more features than Edge-Optimised APIs","Edge-Optimised APIs require a custom domain name","Edge-Optimised APIs use CloudFront to distribute API requests globally, reducing latency for users in different geographic locations."
"What is the purpose of the 'Access Logging' feature in API Gateway?","To log API requests and responses for auditing and debugging","To monitor API performance in real-time","To control access to the API","To configure API caching","Access Logging allows you to log API requests and responses to CloudWatch Logs, which can be used for auditing, debugging, and monitoring API usage."
"You need to debug an issue with an API Gateway API. Which tool can provide detailed logs of API requests and responses?","Amazon CloudWatch Logs","AWS CloudTrail","Amazon S3 access logs","AWS Config rules","Amazon CloudWatch Logs is the primary tool for viewing detailed logs of API requests and responses, helping you to diagnose and resolve API issues."
"What is the recommended way to handle sensitive data, such as API keys, when integrating API Gateway with Lambda?","Store API keys in AWS Secrets Manager and retrieve them in the Lambda function","Embed API keys directly in the Lambda function code","Store API keys in environment variables","Store API keys in the API Gateway configuration","Storing API keys in AWS Secrets Manager is the recommended approach, as it provides secure storage and retrieval of sensitive data."
"What is the use of 'Binary Media Types' in API Gateway?","To allow binary data to be passed through the API","To convert API responses to binary format","To compress API request payloads","To encrypt API traffic","Binary Media Types allow binary data, such as images or files, to be passed through the API without being encoded as text."
"What is the primary function of Amazon API Gateway?","To create, publish, maintain, monitor, and secure APIs.","To manage and deploy EC2 instances.","To store and retrieve data.","To manage user identities and access.","API Gateway acts as a 'front door' for applications to access data, business logic, or functionality from your backend services."
"Which of the following methods is NOT a valid integration type for an API Gateway API?","AWS Lambda","HTTP Proxy","AWS SQS","Mock","API Gateway supports Lambda, HTTP Proxy, and Mock integrations. SQS is typically integrated via other means like Lambda functions."
"What is the purpose of API Gateway's 'throttling' feature?","To prevent API abuse and ensure service availability.","To encrypt data transmitted through the API.","To monitor API usage.","To transform request and response payloads.","Throttling limits the number of requests that can be submitted to an API over a specified period of time."
"Within Amazon API Gateway, what is a 'Stage'?","A deployment environment for your API (e.g., 'dev', 'prod', 'test').","A collection of API methods.","A security group for your API.","A data cache for API responses.","A Stage represents a snapshot of your API configuration that is deployed to a specific environment."
"Which authentication method is commonly used with Amazon API Gateway to control access to your APIs using short-term credentials?","AWS Identity and Access Management (IAM) roles.","Basic Authentication.","OAuth 1.0.","LDAP.","IAM roles and policies provide fine-grained access control to API Gateway resources, and are commonly used in combination with temporary credentials issued by STS (Security Token Service)."
"What type of transformation can be performed using API Gateway's request and response mapping templates?","Transforming data formats (e.g., XML to JSON).","Defining API authorisation rules.","Configuring caching behaviour.","Setting up custom error messages.","Mapping templates allow you to transform the format of requests before they are sent to the backend and the format of responses before they are sent to the client."
"When configuring API Gateway caching, what does the 'Time To Live' (TTL) setting define?","The duration for which cached responses are considered valid.","The maximum size of the cache.","The maximum number of requests that can be cached.","The time it takes for the cache to refresh.","The TTL setting determines how long responses remain in the cache before being refreshed from the backend."
"How can you monitor the performance and usage of your APIs deployed through Amazon API Gateway?","Using Amazon CloudWatch metrics and logs.","Using AWS Trusted Advisor.","Using AWS Config.","Using AWS X-Ray alone.","API Gateway integrates with CloudWatch to provide metrics, logs, and alarms for monitoring API performance and usage."
"What is the purpose of the 'Authorizer' feature in Amazon API Gateway?","To control access to your API based on custom logic.","To define the structure of your API requests.","To validate the API's SSL certificate.","To define the API's versioning strategy.","Authorizers (formerly known as custom authorisers) allow you to implement custom logic for authenticating and authorising API requests."
"Which feature in Amazon API Gateway allows you to expose different API versions to clients?","Stages.","Resources.","Methods.","Integrations.","Stages are the best way to version your APIs. You can deploy different versions of your API to different stages (e.g., v1, v2)."
"In Amazon API Gateway, what is the primary purpose of a 'Resource'?","It represents a logical entity that your API exposes.","It represents a Lambda function.","It represents a backend database.","It represents a security policy.","A 'Resource' in API Gateway represents a logical entity that your API exposes, such as '/users' or '/products'."
"What is the purpose of the 'Method Request' in Amazon API Gateway?","It defines the expected request parameters and payload structure from the client.","It defines the integration with a backend service.","It defines the HTTP status codes returned to the client.","It defines the caching behaviour of the API.","The 'Method Request' defines the expected request parameters (query strings, headers, path variables) and the payload structure that the client is expected to send."
"Which Amazon API Gateway feature allows you to limit the number of requests a client can make to your API?","Throttling","Caching","Authorisation","Transformation","Throttling allows you to limit the number of requests a client can make to your API within a specified time period, protecting your backend services."
"In Amazon API Gateway, what does 'Integration Request' refer to?","The configuration for connecting your API to a backend service.","The data transformation applied to the client request before sending it to the backend.","The validation rules applied to the client request.","The authentication mechanism used to secure the API.","'Integration Request' refers to the configuration that connects your API to your backend service (e.g., Lambda function, HTTP endpoint)."
"What is the purpose of 'API Gateway Authorisers' in Amazon API Gateway?","To control access to your API methods based on authentication and authorisation rules.","To transform the request and response data formats.","To cache API responses for improved performance.","To define the API's documentation and schema.","'API Gateway Authorisers' are used to control access to your API methods by authenticating and authorising incoming requests, typically using Lambda functions or Cognito."
"Which of the following is NOT a valid integration type in Amazon API Gateway?","AWS Service Proxy","HTTP Proxy","Mock","S3 Bucket","S3 Bucket is not a valid integration type, you integrate to an S3 bucket by using the AWS Service Proxy integration type."
"What is the primary benefit of using 'API Gateway Caching' in Amazon API Gateway?","Reduced latency and improved performance by serving cached responses.","Enhanced security through encrypted data storage.","Simplified integration with backend databases.","Automated deployment of API updates.","'API Gateway Caching' significantly reduces latency and improves performance by serving cached responses, reducing the load on your backend services."
"What is the purpose of 'Stages' in Amazon API Gateway?","To manage different deployment environments of your API (e.g., dev, test, prod).","To define the different versions of your API specification.","To manage access control policies for your API.","To define the data transformation rules for your API.","'Stages' in API Gateway allow you to manage different deployment environments (e.g., dev, test, prod) of your API, each with its own configuration and settings."
"You want to transform the format of a request body before sending it to your backend service using Amazon API Gateway. Which feature would you use?","Request Body Mapping","Caching","Throttling","Authorisation","Request Body Mapping allows you to transform the format of the request body from the client before sending it to your backend."
"When using Amazon API Gateway with AWS Lambda, what is a common use case for 'Lambda Proxy Integration'?","Passing the entire request to the Lambda function for processing.","Only passing specific parameters to the Lambda function.","Using Lambda to handle API Gateway caching.","Using Lambda to manage API Gateway security.","With 'Lambda Proxy Integration', the entire request (including headers, query parameters, body) is passed to the Lambda function, providing maximum flexibility."