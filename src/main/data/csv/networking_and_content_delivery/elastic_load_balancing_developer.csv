"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of Elastic Load Balancing (ELB) in AWS?","Distributing incoming application traffic across multiple targets","Storing static content for faster delivery","Managing user authentication and authorisation","Orchestrating container deployments","ELB distributes incoming application traffic across multiple targets, such as EC2 instances, containers, and IP addresses, to increase availability and scalability."
"Which type of Elastic Load Balancer is best suited for routing traffic based on the content of the request?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers operate at the application layer (HTTP/HTTPS) and can make routing decisions based on the content of the request (e.g., host header, path)."
"Which Elastic Load Balancer distributes Layer 4 traffic (TCP, UDP) as well as supports static IP addresses and ultra-low latencies?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers operate at the transport layer (TCP, UDP) and are designed for high performance and low latency. They also support static IP addresses."
"Which Elastic Load Balancer supports sticky sessions based on application cookies?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers support sticky sessions based on application cookies, allowing you to route requests from the same client to the same target."
"What is the purpose of a 'listener' in the context of Elastic Load Balancing?","To check the health of registered targets","To route traffic to different target groups based on rules","To monitor the overall load balancer performance","To configure access logs for the load balancer","A listener checks for connection requests and routes traffic to different target groups based on the rules configured on the listener."
"What is a 'target group' in Elastic Load Balancing?","A group of instances that receive traffic from the load balancer","A security group associated with the load balancer","A configuration setting for the load balancer's logging","A set of rules for routing traffic","A target group is a group of instances (or other targets) that receive traffic from the load balancer."
"Which health check protocol is supported by the Application Load Balancer?","HTTP/HTTPS","TCP only","UDP only","ICMP only","Application Load Balancers support HTTP/HTTPS health checks to ensure that targets are healthy and able to handle requests."
"Which Elastic Load Balancer is designed for high-performance, low-latency applications?","Network Load Balancer","Classic Load Balancer","Application Load Balancer","Gateway Load Balancer","Network Load Balancers are designed for high-performance, low-latency applications and can handle millions of requests per second."
"Which ELB provides TLS termination?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers supports TLS termination, allowing you to decrypt traffic at the load balancer and offload the processing from your backend servers."
"Which of the following is NOT a benefit of using Elastic Load Balancing?","Increased availability","Automatic scaling of backend servers","Improved security posture","Automated Patching of EC2 instances","ELB distributes traffic and increases availability, but it doesn't directly patch EC2 instances."
"What type of load balancer is typically used for Layer 7 traffic (e.g., HTTP, HTTPS)?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers are designed for Layer 7 traffic and can make routing decisions based on the content of the request."
"Which feature of Elastic Load Balancing allows you to distribute traffic to different target groups based on the host header in the HTTP request?","Host-based routing","Path-based routing","Layer 4 routing","Weighted routing","Host-based routing allows you to route traffic to different target groups based on the host header in the HTTP request."
"Which ELB type supports connection draining to ensure in-flight requests are completed before deregistering an instance?","All ELB types","Only Classic Load Balancer","Only Application Load Balancer","Only Network Load Balancer","All ELB types support connection draining (also known as deregistration delay) to ensure that in-flight requests are completed before deregistering an instance."
"How does Elastic Load Balancing contribute to high availability?","By automatically distributing traffic across healthy instances","By encrypting data at rest","By performing automatic backups","By providing a firewall for web applications","ELB increases high availability by automatically distributing traffic across multiple healthy instances and routing traffic away from unhealthy instances."
"Which of these is a key consideration when choosing between different types of Elastic Load Balancers?","The type of traffic your application handles","The number of available AWS regions","The programming language used to build your application","The instance types used in your application","The type of traffic (e.g., HTTP, HTTPS, TCP, UDP) is a key consideration when choosing between Application Load Balancers, Network Load Balancers, and Classic Load Balancers."
"What is the purpose of the 'deregistration delay' setting in Elastic Load Balancing?","To allow in-flight requests to complete before deregistering an instance","To prevent DDoS attacks","To automatically scale the number of instances","To configure health checks","The deregistration delay allows in-flight requests to complete before an instance is deregistered from the load balancer, preventing disruption to users."
"Which component of an Application Load Balancer is used to define the conditions for routing traffic to different target groups?","Rules","Listeners","Targets","Health checks","Rules are used to define the conditions for routing traffic to different target groups, based on things like host header or path."
"What is the primary purpose of using an Elastic Load Balancer's access logs?","To troubleshoot issues and analyse traffic patterns","To control access to the load balancer","To encrypt data in transit","To perform automatic backups","Access logs provide detailed information about requests made to the load balancer, which can be used for troubleshooting issues and analysing traffic patterns."
"Which routing algorithm is NOT supported by the Classic Load Balancer?","Least Outstanding Requests","Round Robin","Source IP Hash","Host-based routing","Host-based routing is a feature specific to Application Load Balancers, and not supported by Classic Load Balancers."
"What happens when an instance fails a health check in an Elastic Load Balancing setup?","The load balancer stops sending traffic to the unhealthy instance","The load balancer automatically restarts the unhealthy instance","The load balancer automatically replaces the unhealthy instance with a new instance","The load balancer sends an alert to the administrator","When an instance fails a health check, the load balancer stops sending traffic to it until it passes the health check again."
"Which of the following is an advantage of using Application Load Balancers over Classic Load Balancers?","Content-based routing","Lower cost","Support for a wider range of protocols","Simpler configuration","Application Load Balancers provide advanced features such as content-based routing, which is not available in Classic Load Balancers."
"Which Elastic Load Balancing component is responsible for monitoring the health of registered targets?","Health Checks","Listeners","Target Groups","Security Groups","Health checks are used to monitor the health of registered targets and ensure that the load balancer only sends traffic to healthy targets."
"Which of the following is NOT a supported target type for an Application Load Balancer?","EC2 instance","IP address","Lambda function","S3 bucket","S3 Buckets cannot be registered as target of an Application Load Balancer"
"You want to distribute traffic to different backend servers based on the URL path requested by the client. Which type of Elastic Load Balancer is most suitable for this scenario?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers are best suited for routing traffic based on the content of the request, such as the URL path."
"Which Elastic Load Balancer provides native integration with AWS WAF (Web Application Firewall)?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers provide native integration with AWS WAF, allowing you to protect your applications from web exploits."
"What is the purpose of 'prewarming' an Elastic Load Balancer?","To ensure the load balancer can handle anticipated traffic spikes","To configure access logs","To enable cross-zone load balancing","To configure health checks","Prewarming an ELB prepares it for expected traffic increases by ensuring there is enough capacity."
"Which protocol does a Network Load Balancer support for health checks?","TCP","HTTP","HTTPS","ICMP","Network Load Balancers primarily use TCP for health checks because they operate at Layer 4."
"Which Elastic Load Balancer provides support for WebSocket connections?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers provide native support for WebSocket connections, allowing for persistent connections between clients and servers."
"You need to implement load balancing for a gaming application that requires extremely low latency and supports UDP traffic. Which ELB should you use?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers are designed for high performance and low latency and support UDP traffic, making them suitable for gaming applications."
"Which ELB can route traffic to targets based on the source IP address of the client?","Classic Load Balancer","Application Load Balancer","Network Load Balancer","Gateway Load Balancer","Classic Load Balancers can be configured to use the source IP address hashing algorithm to distribute traffic to targets."
"Which feature can you use to automatically distribute incoming traffic across multiple Availability Zones using Elastic Load Balancing?","Cross-zone load balancing","Multi-region load balancing","Auto Scaling groups","Content-based routing","Cross-zone load balancing ensures that traffic is distributed evenly across all enabled Availability Zones, improving fault tolerance."
"When configuring an Application Load Balancer, what is the purpose of setting up multiple listeners?","To handle traffic on different ports or protocols","To monitor the load balancer's performance","To configure access logs","To define security groups","Multiple listeners allow the load balancer to handle traffic on different ports or protocols, enabling you to support various types of traffic on the same load balancer."
"What is the recommended approach for handling SSL/TLS certificates with an Application Load Balancer?","Using AWS Certificate Manager (ACM)","Storing certificates directly on the load balancer","Using self-signed certificates","Using third-party certificate providers","AWS Certificate Manager (ACM) is the recommended service for provisioning, managing, and deploying SSL/TLS certificates for use with Application Load Balancers."
"Which ELB is well-suited for Layer 3 traffic?","Gateway Load Balancer","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancers operate at layer 3 (the network layer)."
"Which health check setting determines how long an instance has to be unhealthy before the load balancer stops sending traffic to it?","Unhealthy threshold","Healthy threshold","Interval","Timeout","The unhealthy threshold specifies the number of consecutive health check failures required before the load balancer considers an instance unhealthy."
"Which Elastic Load Balancing feature enables you to direct specific users to a particular instance for debugging purposes?","Sticky sessions","Path-based routing","Host-based routing","Weighted routing","Sticky sessions ensure that requests from the same client are always routed to the same instance, which can be helpful for debugging."
"Which action would you take to ensure your Elastic Load Balancer can handle a sudden surge in traffic?","Pre-warm the load balancer","Increase the target group size","Enable connection draining","Configure access logs","Pre-warming an ELB ensures it is ready to handle a spike in incoming traffic by notifying AWS to allocate resources in advance."
"What is the benefit of using a Gateway Load Balancer in front of a fleet of network virtual appliances?","It simplifies the deployment and management of network virtual appliances","It provides content-based routing","It supports sticky sessions","It integrates with AWS WAF","Gateway Load Balancer simplifies the deployment and management of network virtual appliances by providing a single point of entry and distributing traffic across the appliance fleet."
"Which Elastic Load Balancer is ideal for load balancing between microservices in a containerised environment?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers are well-suited for load balancing between microservices due to their support for content-based routing and container integration."
"What is a key difference between the Application Load Balancer and the Network Load Balancer?","The Application Load Balancer operates at Layer 7, while the Network Load Balancer operates at Layer 4","The Application Load Balancer supports only TCP traffic, while the Network Load Balancer supports HTTP/HTTPS traffic","The Application Load Balancer is more expensive than the Network Load Balancer","The Application Load Balancer is simpler to configure than the Network Load Balancer","The Application Load Balancer operates at Layer 7 (application layer), while the Network Load Balancer operates at Layer 4 (transport layer)."
"What type of Elastic Load Balancer supports TLS offloading?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers provide support for TLS offloading, allowing you to decrypt traffic at the load balancer and offload the processing from your backend servers."
"Which of the following is a valid routing algorithm for the Network Load Balancer?","Hash of the source IP address, source port, destination IP address, destination port, and TCP sequence number","Round Robin","Least Outstanding Requests","Path-based routing","The Network Load Balancer supports a hashing algorithm based on the source IP address, source port, destination IP address, destination port, and TCP sequence number to distribute traffic to targets."
"When using Elastic Load Balancing, what is the purpose of configuring 'security groups' for your instances?","To control inbound and outbound traffic to the instances","To encrypt data at rest","To manage user access control","To monitor the health of the instances","Security groups are used to control inbound and outbound traffic to the instances, ensuring that only authorised traffic can reach your applications."
"Which Elastic Load Balancer is best for routing UDP traffic?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers are designed to handle both TCP and UDP traffic, making them suitable for applications that use UDP."
"You need to implement load balancing for an application that requires preserving the client's IP address for security and auditing purposes. Which type of Elastic Load Balancer is most suitable?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers preserve the client's IP address, which is essential for applications that require this information for security and auditing purposes."
"Which of the following is a key benefit of using Elastic Load Balancing in conjunction with Auto Scaling?","Automatic scaling of application capacity based on traffic demand","Automated backup of EC2 instances","Enhanced security for web applications","Simplified deployment of new application versions","Using ELB with Auto Scaling allows you to automatically scale your application capacity based on traffic demand, ensuring that you always have the resources needed to handle incoming requests."
"How does cross-zone load balancing affect the charges for data transfer when using Elastic Load Balancing?","You are not charged for data transfer between Availability Zones","Data transfer charges are higher between Availability Zones","Data transfer charges are lower between Availability Zones","Data transfer charges are the same regardless of Availability Zones","When cross-zone load balancing is enabled, you are not charged for data transfer between Availability Zones."
"You've configured health checks for your EC2 instances behind an Elastic Load Balancer, but they are all failing. What is the most likely cause?","The security group for your instances is not allowing traffic from the load balancer","The Auto Scaling group is not properly configured","The load balancer is not configured with a valid SSL certificate","The instances are running out of disk space","If health checks are failing, the most likely cause is that the security group for your instances is not allowing traffic from the load balancer's health check probes."
"What is the primary function of an Elastic Load Balancer (ELB)?","Distributing incoming application traffic across multiple targets","Storing static website content","Managing DNS records","Providing serverless compute resources","ELBs distribute incoming application traffic across multiple targets, such as EC2 instances, in multiple Availability Zones. This increases the availability and fault tolerance of your application."
"Which type of Elastic Load Balancer (ELB) is best suited for routing HTTP and HTTPS traffic and provides advanced request routing?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancers (ALB) are designed for HTTP and HTTPS traffic and offer advanced request routing features like content-based routing and host-based routing."
"Which Elastic Load Balancer (ELB) distributes Layer 4 (TCP, UDP) and Layer 7 (HTTP, HTTPS) traffic to instances within a single Availability Zone?","Classic Load Balancer","Application Load Balancer","Network Load Balancer","Gateway Load Balancer","Classic Load Balancers can distribute both Layer 4 and Layer 7 traffic to instances in a single Availability Zone."
"Which Elastic Load Balancer (ELB) is ideal for high-performance, low-latency applications due to its ability to handle millions of requests per second while preserving the source IP address of the client?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers (NLB) are designed for high-performance applications and can handle millions of requests per second while preserving the source IP address of the client."
"What is the purpose of a target group in the context of Elastic Load Balancing?","To define a set of targets, such as EC2 instances, that receive traffic from the load balancer","To manage the security groups associated with the load balancer","To configure the health checks for the load balancer","To specify the load balancing algorithm used by the load balancer","A target group defines a set of targets that the load balancer directs traffic to. This allows you to manage and scale your backend infrastructure independently of the load balancer."
"Which feature of an Application Load Balancer (ALB) allows you to route traffic to different target groups based on the content of the HTTP request (e.g., path, host header)?","Content-based routing","Sticky sessions","Cross-zone load balancing","Connection draining","Content-based routing allows you to route traffic to different target groups based on the content of the HTTP request, providing granular control over how traffic is distributed."
"What is the purpose of connection draining in the context of Elastic Load Balancing?","To allow existing connections to complete before deregistering a target","To encrypt traffic between the load balancer and the targets","To compress traffic between the load balancer and the clients","To automatically scale the number of targets based on demand","Connection draining allows existing connections to complete before deregistering a target, ensuring that no requests are dropped during scaling or deployment changes."
"What is the purpose of health checks in Elastic Load Balancing?","To monitor the health of the registered targets","To encrypt traffic between the load balancer and the targets","To automatically scale the load balancer based on demand","To prevent DDoS attacks on the application","Health checks monitor the health of registered targets and ensure that the load balancer only sends traffic to healthy instances."
"Which type of traffic does an Elastic Load Balancer (ELB) not natively support?","FTP","HTTP","HTTPS","TCP","Elastic Load Balancers are designed for web traffic (HTTP/HTTPS) and general TCP/UDP traffic. They do not natively support FTP."
"What is the benefit of using Cross-Zone Load Balancing with an Elastic Load Balancer (ELB)?","It distributes traffic evenly across all Availability Zones.","It encrypts traffic between the load balancer and the targets.","It automatically scales the load balancer based on demand.","It reduces the latency of requests to the application.","Cross-Zone Load Balancing distributes traffic evenly across all Availability Zones, improving fault tolerance and preventing overload in a single zone."
"Which routing algorithm is NOT supported by Classic Load Balancer?","Least Outstanding Requests","Round Robin","Least Connections","Source IP Hash","The Least Outstanding Requests routing algorithm is supported only by Application Load Balancer."
"Which Elastic Load Balancing feature protects against sudden spikes in traffic by queuing requests?","Surge Protection","Request Buffering","Connection Throttling","Traffic Shaping","Request buffering protects back-end instances from being overwhelmed by absorbing and holding traffic surges."
"If an EC2 instance fails its health check behind an ELB, what happens?","Traffic is automatically routed to the remaining healthy instances.","The entire ELB fails.","The instance is immediately terminated.","Traffic is paused to the failing instance.","When an EC2 instance fails a health check, the ELB automatically stops routing traffic to that instance and redirects it to other healthy instances."
"What is the difference between an Application Load Balancer (ALB) and a Network Load Balancer (NLB) in terms of target types?","ALBs support only HTTP/HTTPS targets, while NLBs support any TCP/UDP targets.","ALBs support IP addresses and lambda functions as targets, while NLBs do not.","ALBs support only EC2 instances, while NLBs support a wider range of target types.","ALBs are designed for high throughput, while NLBs are for complex routing.","ALBs support IP addresses and lambda functions as targets, while NLBs do not."
"Which feature of Elastic Load Balancing can be used to maintain user sessions with specific backend instances?","Sticky sessions","Connection draining","Content-based routing","Path-based routing","Sticky sessions (also known as session affinity) ensure that requests from the same client are consistently routed to the same backend instance."
"What is the main advantage of using an Application Load Balancer (ALB) compared to a Classic Load Balancer?","ALBs provide more advanced routing capabilities.","ALBs support only TCP traffic.","ALBs are cheaper to operate.","ALBs cannot handle HTTP traffic.","Application Load Balancers offer more advanced routing capabilities, such as content-based routing and host-based routing, which are not available in Classic Load Balancers."
"How does an Elastic Load Balancer (ELB) improve the availability of your application?","By distributing traffic across multiple Availability Zones and EC2 instances","By encrypting traffic between the client and the server","By providing a firewall to protect against attacks","By caching static content to reduce latency","Elastic Load Balancers improve availability by distributing traffic across multiple Availability Zones and EC2 instances. If one instance or Availability Zone fails, the load balancer automatically redirects traffic to the remaining healthy instances."
"What is the first step in setting up an Elastic Load Balancer?","Create the target groups","Configure the security group","Launch the EC2 instances","Enable CloudWatch monitoring","The first step in setting up an Elastic Load Balancer is to define and create the target groups that the ELB will route traffic to."
"What is the primary purpose of using multiple Elastic Load Balancers (ELBs) in an architecture?","To handle different types of traffic or to isolate different parts of the application","To reduce costs associated with load balancing","To simplify the management of the infrastructure","To improve the performance of individual EC2 instances","Multiple ELBs can be used to handle different types of traffic (e.g., one for HTTP and another for TCP) or to isolate different parts of the application, improving security and manageability."
"Which Elastic Load Balancing (ELB) type integrates directly with AWS Global Accelerator to improve performance for geographically dispersed users?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer (NLB) integrates directly with AWS Global Accelerator to improve performance for geographically dispersed users."
"You need to implement mutual authentication between your clients and the backend servers using Elastic Load Balancing. Which ELB feature can help with this?","TLS termination","Connection draining","Content-based routing","Sticky Sessions","TLS termination on the ELB allows you to offload the TLS handshake process from the backend servers. The load balancer decrypts the traffic and forwards it to the backend servers, which can then handle the application logic."
"How does an Application Load Balancer (ALB) handle requests when no rules match the incoming traffic?","It returns an HTTP 404 error.","It forwards the traffic to the default target group.","It drops the connection.","It redirects the traffic to a predefined error page.","If no rules match the incoming traffic, the ALB forwards the traffic to the default target group."
"What is the benefit of using a Gateway Load Balancer (GWLB)?","Deep packet inspection.","Load balancing web applications.","Reducing costs.","Running applications serverless.","Gateway Load Balancer is designed to allow you to easily deploy, scale, and manage virtual appliances such as intrusion detection systems, firewalls and deep packet inspection systems."
"Which of the following is NOT a factor when determining the cost of using Elastic Load Balancing (ELB)?","The number of listeners configured on the load balancer.","The amount of data processed by the load balancer.","The number of hours the load balancer is running.","The number of connections handled by the load balancer.","The number of listeners configured on the load balancer is not a direct factor in determining ELB costs. The costs are primarily based on the amount of data processed, the number of hours the load balancer is running, and the number of connections handled."
"Which of the following is a key security benefit provided by Elastic Load Balancing (ELB)?","It can be integrated with AWS WAF to protect against web exploits.","It automatically encrypts all traffic to the backend instances.","It provides built-in DDoS protection.","It completely isolates the backend instances from the internet.","ELB can be integrated with AWS WAF to protect against common web exploits, such as SQL injection and cross-site scripting."
"When configuring health checks for an Elastic Load Balancer (ELB), what protocol can be used?","HTTP, HTTPS, TCP","UDP only","ICMP only","SMTP only","Elastic Load Balancers support health checks using HTTP, HTTPS, and TCP protocols to monitor the health of backend instances."
"What happens when you delete an Elastic Load Balancer (ELB)?","All associated resources, such as EC2 instances, are also terminated.","The DNS record associated with the load balancer is immediately removed.","The configuration is saved and can be restored later.","All active connections are immediately closed.","When you delete an ELB, all active connections are immediately closed, and the load balancer stops routing traffic. It is crucial to ensure that all necessary resources are properly configured before deleting an ELB."
"You want to ensure that your Application Load Balancer (ALB) distributes traffic only to healthy instances within a specific Availability Zone. What configuration setting should you use?","Enable Cross-Zone Load Balancing","Disable Cross-Zone Load Balancing","Configure health checks with a specific Availability Zone","Use a Network Load Balancer instead","Disabling Cross-Zone Load Balancing ensures that the ALB distributes traffic only to healthy instances within the same Availability Zone, improving isolation and reducing latency in certain scenarios."
"What is the primary difference between using instance registration with an Elastic Load Balancer (ELB) versus using IP address registration?","Instance registration uses instance IDs, while IP address registration uses IP addresses of the targets.","Instance registration automatically updates when instances are launched or terminated, while IP address registration requires manual updates.","Instance registration supports only EC2 instances, while IP address registration supports a wider range of target types.","Instance registration is more secure than IP address registration.","Instance registration uses instance IDs, while IP address registration uses IP addresses of the targets."
"Which of the following scenarios is best suited for using a Classic Load Balancer?","Load balancing HTTP traffic with complex routing requirements.","Load balancing high-performance TCP traffic.","Load balancing traffic across multiple AWS accounts.","Load balancing applications that rely on sticky sessions and simple HTTP/HTTPS load balancing.","Classic Load Balancers are best suited for applications that rely on sticky sessions and simple HTTP/HTTPS load balancing. ALBs are better for complex routing, and NLBs are better for high-performance TCP traffic."
"You need to configure an Elastic Load Balancer (ELB) to route traffic to different backend instances based on the domain name requested by the client. Which feature should you use?","Host-based routing","Path-based routing","Sticky sessions","Connection draining","Host-based routing allows you to route traffic based on the domain name (host header) in the HTTP request, enabling you to serve multiple websites or applications from the same load balancer."
"What is the purpose of the 'idle timeout' setting on an Elastic Load Balancer (ELB)?","To specify the maximum amount of time that a connection can remain open without sending any data","To specify the maximum amount of time that a request can take to be processed by the backend instances","To specify the maximum amount of time that the load balancer will wait for a response from the backend instances","To specify the maximum amount of time that a health check can take to complete","The 'idle timeout' setting specifies the maximum amount of time that a connection can remain open without sending any data. If no data is sent within the idle timeout period, the connection is closed."
"What does the term 'pre-warming' refer to in the context of Elastic Load Balancing (ELB)?","Contacting AWS Support to provision additional capacity for an expected traffic surge","Configuring health checks to ensure that the backend instances are ready to receive traffic","Encrypting traffic between the load balancer and the backend instances","Configuring caching to reduce latency for static content","'Pre-warming' refers to contacting AWS Support to provision additional capacity for an expected traffic surge, ensuring that the ELB can handle the increased load without performance degradation."
"Which of the following is NOT a supported protocol for listeners on an Application Load Balancer (ALB)?","SMTP","HTTP","HTTPS","WebSocket","Application Load Balancers support HTTP, HTTPS and WebSockets but not SMTP."
"In the context of Elastic Load Balancing (ELB), what is a 'listener'?","A process that checks for connection requests","A rule that defines how traffic is routed to target groups","A security group that controls access to the load balancer","A network interface associated with the load balancer","In ELB, a 'listener' is a process that checks for connection requests, using the protocol and port that you configure."
"When configuring an Elastic Load Balancer (ELB), what is the purpose of the 'availability zone' setting?","To specify the availability zones in which the load balancer will operate","To specify the maximum number of instances that can be registered with the load balancer","To specify the security groups that will be associated with the load balancer","To specify the health check protocol that will be used by the load balancer","The 'availability zone' setting allows you to specify the availability zones in which the load balancer will operate, ensuring that it is distributed across multiple zones for high availability."
"Which type of load balancer is best suited for load balancing QUIC protocol traffic?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer is the preferred choice for non-HTTP(S) traffic, including QUIC."
"Which of the following is a benefit of integrating AWS Certificate Manager (ACM) with Elastic Load Balancing (ELB)?","ACM provides SSL/TLS certificates for secure connections.","ACM automates the scaling of the load balancer.","ACM provides detailed monitoring metrics for the load balancer.","ACM automatically configures the security groups for the load balancer.","AWS Certificate Manager (ACM) provides SSL/TLS certificates for secure connections, enabling you to offload the management of certificates to ACM and ensure secure communication between clients and the load balancer."
"You are using an Application Load Balancer (ALB) and want to implement a blue/green deployment strategy. How can you achieve this?","By creating two target groups, one for the blue environment and one for the green environment, and then switching traffic between them.","By using the ALB's built-in deployment feature, which automatically handles the blue/green deployment process.","By creating two separate ALBs, one for the blue environment and one for the green environment, and then switching the DNS record between them.","By using AWS CodeDeploy to manage the blue/green deployment process, which automatically integrates with the ALB.","You can implement a blue/green deployment strategy by creating two target groups, one for the blue environment and one for the green environment, and then switching traffic between them using ALB's routing rules."
"Which security principle is most closely related to using different target groups for different application components behind an Elastic Load Balancer (ELB)?","Principle of Least Privilege","Defence in Depth","Separation of Concerns","Shared Responsibility Model","Separation of Concerns focuses on breaking down a system into distinct components, each handling a specific aspect of the overall functionality. Using different target groups allows for managing and scaling each component independently."
"What is the purpose of configuring Access Logs for an Elastic Load Balancer (ELB)?","To capture detailed information about requests handled by the load balancer","To monitor the CPU utilisation of the backend instances","To prevent unauthorised access to the load balancer","To automatically scale the load balancer based on demand","Access Logs capture detailed information about requests handled by the load balancer, including the client IP address, request time, HTTP status code, and more. This information can be used for troubleshooting, monitoring, and security analysis."
"When configuring an Elastic Load Balancer (ELB), what does the term 'backend authentication' refer to?","Authenticating requests from the load balancer to the backend instances","Authenticating users who are accessing the application through the load balancer","Encrypting traffic between the load balancer and the backend instances","Preventing unauthorised access to the load balancer's configuration","Backend authentication refers to authenticating requests from the load balancer to the backend instances, ensuring that only authorised requests are processed."
"Which of the following statements is TRUE regarding the security groups associated with an Elastic Load Balancer (ELB)?","The security group for the load balancer must allow traffic on the listener ports from the clients.","The security group for the load balancer must allow all traffic from the internet.","The security group for the backend instances must allow traffic from the internet.","The security groups are automatically configured by the ELB and cannot be modified.","The security group for the load balancer must allow traffic on the listener ports from the clients, enabling them to access the application through the load balancer."
"You are troubleshooting an issue where your Application Load Balancer (ALB) is returning HTTP 502 errors. What is the most likely cause of this issue?","The backend instances are not responding to the load balancer's health checks.","The load balancer is not configured with a valid SSL/TLS certificate.","The client is sending invalid HTTP requests to the load balancer.","The load balancer is experiencing a traffic surge.","HTTP 502 errors (Bad Gateway) typically indicate that the backend instances are not responding to the load balancer's health checks, suggesting that the instances are unhealthy or overloaded."
"Which of the following is a key difference between an Application Load Balancer (ALB) and a Gateway Load Balancer (GWLB) in terms of the OSI model layers they operate at?","ALB operates at Layer 7, while GWLB operates at Layer 3.","ALB operates at Layer 4, while GWLB operates at Layer 7.","ALB operates at Layer 7, while GWLB operates at Layer 4.","ALB operates at Layer 3, while GWLB operates at Layer 7.","ALB operates at Layer 7 (Application Layer), making routing decisions based on the content of the HTTP requests. GWLB operates at Layer 3 (Network Layer), routing traffic based on IP addresses and ports."
"You want to configure an Elastic Load Balancer (ELB) to forward traffic to a Lambda function. Which type of ELB should you use?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","An Application Load Balancer (ALB) can be configured to forward traffic to Lambda functions, allowing you to build serverless applications that can handle HTTP requests."
"Which AWS service can be used to automatically discover and register targets with an Elastic Load Balancer (ELB)?","AWS Auto Scaling","AWS CloudFormation","AWS CloudWatch","AWS Config","AWS Auto Scaling groups can be configured to automatically discover and register targets (e.g., EC2 instances) with an ELB, simplifying the management of your backend infrastructure."
"What is the purpose of setting up a 'default action' for an Application Load Balancer (ALB) listener?","To specify the target group to which traffic is routed if no rules match the incoming request.","To define the health check protocol that is used by the ALB.","To specify the security group that is associated with the ALB.","To enable cross-zone load balancing for the ALB.","The 'default action' for an ALB listener specifies the target group to which traffic is routed if no rules match the incoming request. This ensures that all traffic is handled, even if it does not match any specific routing rules."
"In the context of Elastic Load Balancing (ELB), what does the term 'mutual authentication' typically refer to?","The backend servers authenticating the Load Balancer.","Mutual Authentication (mTLS) typically involves the client and server verifying each other's identities before establishing a secure connection.","The Load Balancer authenticating incoming traffic based on source IP address.","Using a password to access the ELB's configuration.","The client authenticating against the server before sending a request."
"When using AWS Global Accelerator in conjunction with Elastic Load Balancing (ELB), what is the primary benefit gained?","Improved global performance by routing traffic via AWS's global network","Reduced cost of load balancing","Simplified configuration of load balancing across multiple regions","Automatic failover to a disaster recovery region","The primary benefit of using AWS Global Accelerator with ELB is improved global performance. Global Accelerator intelligently routes traffic to the optimal endpoint based on location, health, and configuration."
"Which AWS Elastic Load Balancing type supports routing traffic based on the content of the request (e.g., HTTP header)?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers examine the content of the request to make routing decisions, such as host-based or path-based routing."
"What is a primary benefit of using Elastic Load Balancing in your architecture?","Increased application availability","Reduced storage costs","Enhanced network security","Simplified database management","ELB distributes incoming traffic across multiple targets, such as EC2 instances, in multiple Availability Zones, increasing application availability."
"Which Elastic Load Balancing type operates at Layer 4 of the OSI model?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers operate at the transport layer (Layer 4), handling TCP and UDP traffic."
"Which Elastic Load Balancing component checks the health of registered instances?","Health Checks","Listeners","Target Groups","Security Groups","ELB Health Checks monitor the status of registered instances and only route traffic to healthy ones."
"What is a Target Group in the context of Elastic Load Balancing?","A group of resources that receive traffic from a load balancer","A set of security rules for the load balancer","A collection of CloudWatch metrics for the load balancer","A geographical region for the load balancer","A Target Group defines the target for incoming traffic from a load balancer, such as EC2 instances or Lambda functions."
"Which Elastic Load Balancing type is best suited for load balancing UDP traffic?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers are designed to handle UDP traffic efficiently due to their low-latency and high-throughput capabilities."
"What is the purpose of sticky sessions (session affinity) in Elastic Load Balancing?","To route requests from the same client to the same target","To encrypt traffic between the load balancer and the targets","To distribute traffic based on the source IP address","To automatically scale the number of targets based on traffic volume","Sticky sessions ensure that requests from the same client are consistently routed to the same target, useful for maintaining stateful sessions."
"Which Elastic Load Balancing type supports containerised applications using dynamic port mapping?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancers are well-suited for containerised applications as they support dynamic port mapping with ECS and Kubernetes."
"What is the purpose of a listener in Elastic Load Balancing?","To check incoming connection requests","To define routing rules for traffic","To manage security groups for the load balancer","To monitor the health of backend servers","A listener checks for connection requests from clients, using the protocol and port that you configure."
"Which Elastic Load Balancing type provides static IP addresses per Availability Zone?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers provide a static IP address for each Availability Zone they are enabled in, useful for applications requiring fixed IP addresses."
"Which Elastic Load Balancing service is integrated with AWS WAF to provide protection against web exploits?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancer (ALB) integrates directly with AWS WAF, providing protection against web exploits such as SQL injection and cross-site scripting (XSS)."
"What is the main function of connection draining in Elastic Load Balancing?","To allow existing requests to complete during instance deregistration","To prevent DDoS attacks on backend servers","To encrypt traffic between the load balancer and instances","To automatically scale instances based on traffic volume","Connection draining allows the load balancer to stop sending new requests to an instance that is being deregistered or is unhealthy, while allowing existing requests to complete."
"Which Elastic Load Balancing option supports mutual authentication (mTLS)?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancer supports mutual authentication (mTLS) allowing for secure communication between the client and the load balancer."
"Which routing algorithm is supported by Classic Load Balancer?","Round Robin","Least Outstanding Requests","Source IP Hash","Content-Based Routing","Classic Load Balancer support Round Robin, Least Outstanding Requests and Source IP Hash algorithms."
"What is the purpose of X-Forwarded-For header when using Elastic Load Balancing?","To preserve the original client IP address","To encrypt traffic between the load balancer and instances","To enable sticky sessions","To monitor backend server health","The X-Forwarded-For header is used to preserve the original client IP address when traffic passes through a load balancer, which is necessary for logging and security purposes."
"Which Elastic Load Balancing service is designed for high-performance, low-latency workloads?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers are optimised for high-performance, low-latency workloads, making them suitable for gaming, streaming, and IoT applications."
"What is the difference between an instance health check and a target group health check in Elastic Load Balancing?","They both check the health of registered targets; target group checks are more customisable","Instance health checks are for Classic Load Balancers, target group health checks are for Application/Network Load Balancers.","Instance health checks are more granular than target group health checks","They are the same.","Instance health checks were used in Classic Load Balancers, target group health checks are a feature of Application Load Balancers and Network Load Balancers and offer greater customisation."
"What is the best practice for distributing your EC2 instances across Availability Zones when using Elastic Load Balancing?","Distribute instances evenly across multiple Availability Zones","Place all instances in a single Availability Zone for simplicity","Place instances in the Availability Zone with the lowest latency","Place all instances in the same subnet.","Distributing instances across multiple Availability Zones ensures high availability and fault tolerance in case of an outage in one zone."
"What does 'cross-zone load balancing' mean in the context of Elastic Load Balancing?","Distributing traffic evenly across all enabled Availability Zones","Load balancing across different AWS regions","Load balancing between on-premises and cloud resources","Load balancing between different AWS accounts","Cross-zone load balancing ensures that the load balancer distributes traffic evenly across all enabled Availability Zones, regardless of the number of instances in each zone."
"Which Elastic Load Balancing type allows you to inspect TLS headers?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancer allows you to inspect TLS headers"
"Which is a key feature of Gateway Load Balancer?","Support for virtual appliances","Content-based routing","Layer 4 load balancing","SSL termination","Gateway Load Balancer is specifically designed to support virtual appliances such as firewalls and intrusion detection systems."
"What is the purpose of deregistration delay in Elastic Load Balancing?","To minimise impact to users during instance removal","To encrypt data in transit","To balance load across availability zones","To improve instance launch time","Deregistration delay ensures that the load balancer allows existing requests to complete before an instance is fully removed, minimising impact to users."
"Which factor is important to consider when choosing between an Application Load Balancer and a Network Load Balancer?","The type of traffic (HTTP/HTTPS vs. TCP/UDP)","The size of the EC2 instances","The AWS region availability","The IAM role of the load balancer","The type of traffic is a crucial factor, as Application Load Balancers are designed for HTTP/HTTPS traffic, while Network Load Balancers are designed for TCP/UDP traffic."
"In Elastic Load Balancing, what does it mean when an EC2 instance is 'OutOfService'?","The instance has failed the health checks","The instance is being terminated","The instance is running at 100% CPU","The instance has been moved to a different Availability Zone","'OutOfService' indicates that the instance has failed the health checks configured in the load balancer."
"What is the purpose of pre-warming an Elastic Load Balancer?","To ensure the load balancer can handle expected traffic spikes","To encrypt traffic between the load balancer and instances","To enable cross-zone load balancing","To improve the overall security posture of the application","Pre-warming ensures that the load balancer has sufficient capacity to handle expected traffic spikes by requesting AWS to provision more resources in advance."
"Which AWS service can be used to monitor the performance metrics of an Elastic Load Balancer?","Amazon CloudWatch","AWS CloudTrail","AWS Config","AWS Trusted Advisor","Amazon CloudWatch collects and tracks metrics for ELB, which can be used to monitor performance, set alarms, and troubleshoot issues."
"Which security feature is typically associated with Elastic Load Balancing to enhance security?","Security Groups","Network ACLs","IAM Roles","VPC Endpoints","Security Groups are used to control inbound and outbound traffic to the load balancer and backend instances."
"What is the difference between a 'healthy' and 'unhealthy' target in Elastic Load Balancing?","Healthy targets receive traffic; unhealthy targets do not","Healthy targets have larger instance sizes","Healthy targets have more memory allocated","Healthy targets run in a different availability zone","Healthy targets pass the health checks configured in the load balancer and are eligible to receive traffic, while unhealthy targets do not."
"What is the main advantage of using Elastic Load Balancing with Auto Scaling?","Dynamically adjusts capacity based on traffic","Enhanced security posture","Reduced storage costs","Simplified database management","Elastic Load Balancing integrates with Auto Scaling to dynamically adjust the number of instances based on traffic demand, ensuring optimal performance and cost-efficiency."
"Which feature of Elastic Load Balancing helps in distributing traffic across multiple targets within a Target Group?","Load Balancing Algorithm","Connection Draining","Sticky Sessions","Health Checks","Load Balancing Algorithm"
"Which of the following scenarios is best suited for using a Network Load Balancer?","Load balancing HTTP traffic for a web application","Load balancing high-throughput TCP traffic","Load balancing requests based on URL path","Load balancing traffic to Lambda functions","Network Load Balancer"
"You are using an Application Load Balancer. What is the minimum number of Availability Zones that your load balancer must be deployed to for high availability?","Two","One","Three","It depends on the region","Two"
"What type of target can you register with a Network Load Balancer?","EC2 instances, IP addresses, and Application Load Balancers","Only EC2 instances","Only IP addresses","Only Application Load Balancers","EC2 instances, IP addresses, and Application Load Balancers"
"What is the primary use case for Classic Load Balancer?","For applications built in the EC2-Classic network.","For load balancing HTTP/HTTPS traffic to multiple EC2 instances.","For applications that require TLS termination at the load balancer.","For low latency applications.","For applications built in the EC2-Classic network."
"Which listener configuration specifies how an Elastic Load Balancer handles connection requests?","Protocol and port","Security group and target group","Health check path and interval","Availability Zone and subnet","Protocol and port"
"Which of the following is NOT a supported protocol for Application Load Balancer listeners?","TCP","HTTP","HTTPS","gRPC","TCP"
"What is the primary reason to use multiple listeners with an Application Load Balancer?","To handle different types of traffic (e.g., HTTP and HTTPS) on the same load balancer.","To distribute traffic across multiple Availability Zones.","To enable connection draining.","To configure different security groups for different types of traffic.","To handle different types of traffic (e.g., HTTP and HTTPS) on the same load balancer."
"What is the key characteristic of a Target Group that is used with an Application Load Balancer?","It contains a set of registered targets (e.g., EC2 instances) and their health check configuration.","It defines the SSL certificates used by the load balancer.","It specifies the routing algorithm used by the load balancer.","It determines the scaling policies for the load balancer.","It contains a set of registered targets (e.g., EC2 instances) and their health check configuration."
"Which of the following is NOT a supported health check setting for a Target Group?","Unhealthy threshold","Health check port","Timeout","Subnet","Subnet"
"What is the benefit of using multiple Target Groups with an Application Load Balancer?","Allows you to route traffic to different targets based on rules.","It provides redundancy for health checks.","It ensures that the load balancer can handle different protocols.","It reduces the cost of running the load balancer.","Allows you to route traffic to different targets based on rules."
"Which of the following is NOT a supported action type for a listener rule in an Application Load Balancer?","Forward","Redirect","Fixed-response","Deny","Deny"
"You need to redirect all HTTP traffic to HTTPS using an Application Load Balancer. Which listener rule action should you use?","Redirect","Forward","Fixed-response","Authenticate","Redirect"
"When configuring stickiness on an Application Load Balancer, what is the default stickiness type?","Duration-based cookie","Source IP address","Application-based cookie","None","Duration-based cookie"
"What is the purpose of the 'idle timeout' setting on an Elastic Load Balancer?","To close idle connections after a specified period of inactivity.","To automatically scale the number of instances.","To monitor the health of the instances.","To encrypt traffic between the load balancer and the instances.","To close idle connections after a specified period of inactivity."
"Which security benefit does Elastic Load Balancing provide?","It hides the internal architecture of your application from the public internet.","It encrypts all data in transit.","It automatically patches security vulnerabilities in your application.","It provides DDoS protection.","It hides the internal architecture of your application from the public internet."
"You want to use Application Load Balancer to forward traffic to a Lambda function. What type of target should you register in the Target Group?","Lambda Function","EC2 Instance","IP Address","Network Interface","Lambda Function"
"Which log format is available for Application Load Balancers to capture detailed request information?","Access logs","CloudTrail logs","VPC Flow logs","CloudWatch logs","Access logs"
"Which of the following is a use case for the Gateway Load Balancer?","Deep packet inspection.","Database load balancing.","Content delivery network.","Serverless application deployment.","Deep packet inspection."
"What OSI layer does Gateway Load Balancer operate at?","Layer 3","Layer 4","Layer 7","Layer 2","Layer 3"
"Which protocol is exclusively supported by the Gateway Load Balancer?","GENEVE","HTTPS","HTTP","SMTP","GENEVE"
"What is the primary function of Elastic Load Balancing (ELB) in AWS?","Distributing incoming application traffic across multiple targets.","Monitoring the health of EC2 instances.","Managing IAM roles and permissions.","Storing static website content.","ELB distributes incoming application traffic across multiple targets, such as EC2 instances, containers, and IP addresses, in multiple Availability Zones."
"Which type of Elastic Load Balancer is best suited for routing HTTP and HTTPS traffic and provides advanced request routing?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancer is designed for HTTP and HTTPS traffic and offers advanced request routing based on content, host, or path."
"What type of Elastic Load Balancer operates at the transport layer (Layer 4) and is ideal for TCP, UDP, and TLS traffic where ultra-high performance is required?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer operates at the transport layer (Layer 4) and is designed for high-performance, low-latency traffic."
"Which ELB feature allows you to register targets by their IP address, including targets outside of your VPC?","IP Address as Targets","Instance ID as Targets","Security Group as Targets","Hostname as Targets","ELB allows you to register targets by IP address, enabling you to route traffic to targets outside your VPC."
"What is the purpose of health checks in Elastic Load Balancing?","To monitor the health of registered targets and route traffic only to healthy targets.","To encrypt traffic between the load balancer and the targets.","To automatically scale the number of EC2 instances.","To configure SSL certificates.","Health checks monitor the health of registered targets and ensure that traffic is routed only to healthy targets, improving application availability."
"Which ELB type provides support for WebSocket connections?","Application Load Balancer and Network Load Balancer","Classic Load Balancer only","Network Load Balancer only","Classic Load Balancer and Network Load Balancer","Both Application Load Balancer and Network Load Balancer support WebSocket connections for real-time applications."
"What is the purpose of 'Connection Draining' (also known as 'Deregistration Delay') in Elastic Load Balancing?","To allow in-flight requests to complete when a target is deregistered or becomes unhealthy.","To immediately terminate all connections to a deregistered target.","To automatically restart unhealthy targets.","To distribute traffic evenly across all targets.","Connection draining allows in-flight requests to complete when a target is deregistered or becomes unhealthy, preventing interruption of user sessions."
"Which Elastic Load Balancing type is designed for high-performance and low-latency by operating at layer 4?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","The Network Load Balancer (NLB) operates at Layer 4, providing high throughput and low latency performance. It is ideal for TCP, UDP, and TLS traffic."
"What is the purpose of a Listener in Elastic Load Balancing?","To check the health status of registered instances.","To configure rules that define how the load balancer routes traffic.","To encrypt the traffic between the Load Balancer and the instances.","To manage the EC2 instances.","A listener checks for connection requests. You configure listeners with a protocol and port for connections from clients to the load balancer, and a protocol and port for connections from the load balancer to the targets."
"What is one of the key benefits of using Elastic Load Balancing (ELB) in a highly available architecture?","Improved fault tolerance and automatic failover.","Reduced storage costs.","Increased database performance.","Simplified network configuration.","ELB improves fault tolerance and provides automatic failover by distributing traffic across multiple healthy targets in different Availability Zones."
"Which load balancer type can route traffic based on the hostname in the HTTP header?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","Application Load Balancer can inspect the HTTP header, including the hostname, and route traffic based on the value of the hostname field."
"Which ELB feature enables you to forward traffic to different target groups based on the URL path in the request?","Path-based routing","Content-based routing","Host-based routing","Source IP routing","Path-based routing allows you to forward traffic to different target groups based on the URL path in the request, enabling granular control over traffic distribution."
"Which AWS service is commonly used with Elastic Load Balancing to automatically scale the number of EC2 instances based on traffic demand?","Auto Scaling","CloudWatch","IAM","VPC","Auto Scaling automatically adjusts the number of EC2 instances based on traffic demand, ensuring that your application can handle varying workloads."
"What is the purpose of the X-Forwarded-For header when using an Elastic Load Balancer?","To provide the original IP address of the client connecting to the load balancer.","To encrypt traffic between the load balancer and the backend instances.","To authenticate users accessing the application.","To monitor the health of the backend instances.","The X-Forwarded-For header contains the original IP address of the client, allowing backend servers to track the source of requests even though they come from the load balancer."
"Which Elastic Load Balancing type supports both IPv4 and IPv6 addresses?","Application Load Balancer and Network Load Balancer","Classic Load Balancer only","Network Load Balancer only","Classic Load Balancer and Network Load Balancer","Both Application Load Balancer and Network Load Balancer provide support for IPv4 and IPv6 addresses."
"What is a Target Group in the context of Elastic Load Balancing?","A group of registered targets (e.g., EC2 instances) that receive traffic from the load balancer.","A security group that controls access to the load balancer.","A group of users that have access to the load balancer's configuration.","A set of CloudWatch metrics that monitor the load balancer's performance.","A target group is a collection of registered targets, such as EC2 instances, containers, or IP addresses, that receive traffic from the load balancer."
"Which load balancer type is best used for microservices and container-based applications?","Application Load Balancer","Classic Load Balancer","Network Load Balancer","Gateway Load Balancer","The Application Load Balancer (ALB) is best suited for microservices and container-based applications, offering features like content-based routing and dynamic port mapping."
"When should you use a Network Load Balancer (NLB) over an Application Load Balancer (ALB)?","When you need extremely high performance and low latency for TCP and UDP traffic.","When you need to route traffic based on the content of the HTTP request.","When you need to support sticky sessions based on cookies.","When you need to use WebSockets.","Use NLB when you require extremely high performance and low latency for TCP, UDP, and TLS traffic."
"What is the benefit of using multiple Availability Zones (AZs) with Elastic Load Balancing?","Increased fault tolerance and high availability.","Reduced latency for all users.","Lower cost of operation.","Increased storage capacity.","Distributing targets across multiple AZs increases fault tolerance and ensures high availability by allowing the load balancer to continue routing traffic even if one AZ becomes unavailable."
"How does Elastic Load Balancing contribute to improving application security?","By offloading SSL termination and managing SSL certificates.","By automatically patching security vulnerabilities in EC2 instances.","By providing a built-in firewall for the application.","By encrypting all data at rest.","ELB improves application security by offloading SSL termination, allowing you to manage SSL certificates centrally and simplify security management."
"Which Elastic Load Balancer supports routing traffic based on the source IP address?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer can route traffic based on the source IP address of the client."
"What is the purpose of 'Sticky Sessions' (also known as 'Session Affinity') in Elastic Load Balancing?","To route requests from the same client to the same target.","To encrypt traffic between the load balancer and the targets.","To automatically scale the number of EC2 instances.","To distribute traffic evenly across all targets.","Sticky sessions ensure that requests from the same client are consistently routed to the same target, which is useful for maintaining session state in applications."
"Which Elastic Load Balancer would be the most appropriate for load balancing to targets that reside in peered VPCs or on-premises?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer is best suited for routing to targets in peered VPCs or on-premises environments due to its IP address target support."
"If you have an application that uses a Classic Load Balancer and you want to start using more advanced routing features based on the content of the request, what should you do?","Migrate to an Application Load Balancer.","Upgrade the Classic Load Balancer.","Create a new Network Load Balancer.","Use Route 53 for content-based routing.","Application Load Balancers support content-based routing, so you should migrate from Classic Load Balancer to Application Load Balancer."
"What is the role of 'Listeners' in the configuration of an Elastic Load Balancer?","To define the protocol, port, and rules for routing traffic.","To monitor the health of the registered targets.","To automatically scale the number of EC2 instances.","To configure SSL certificates.","Listeners define the protocol, port, and rules for routing traffic from the load balancer to the registered targets."
"Which ELB type allows you to inspect traffic at Layer 7 of the OSI model?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancer operates at Layer 7, allowing it to inspect the content of HTTP and HTTPS requests."
"Which feature of the Application Load Balancer allows you to direct traffic to different backend servers based on the user's language preference?","Content-based routing","Path-based routing","Host-based routing","Source IP routing","Content-based routing lets the Application Load Balancer direct traffic based on the content of the request. This could include headers like Accept-Language."
"Why would you use a Gateway Load Balancer?","To integrate with third-party virtual appliances for network security.","To provide content caching.","To distribute database traffic.","To route traffic to Lambda functions.","Gateway Load Balancer is specifically designed to integrate with third-party virtual appliances for deep packet inspection, intrusion detection and prevention, and other network security functions."
"Which service provides detailed metrics about your Elastic Load Balancer's performance, such as request count and latency?","CloudWatch","CloudTrail","Config","Trusted Advisor","CloudWatch provides detailed metrics about your ELB's performance, including request count, latency, and error rates, allowing you to monitor and troubleshoot your load balancer."
"What is the significance of the 'Availability Zone' attribute when configuring an Elastic Load Balancer?","Ensuring the load balancer is deployed in multiple AZs for high availability.","Defining the region where the load balancer will be deployed.","Specifying the security group for the load balancer.","Configuring the instance type for the load balancer.","Deploying the load balancer across multiple Availability Zones ensures high availability and fault tolerance by distributing traffic across different physical locations."
"You are using an Application Load Balancer and want to configure it to redirect all HTTP traffic to HTTPS. How can you achieve this?","By creating a listener rule that redirects HTTP traffic to HTTPS.","By enabling 'HTTPS Only' mode in the load balancer settings.","By configuring the security group to only allow HTTPS traffic.","By modifying the web server configuration to redirect HTTP to HTTPS.","You can create a listener rule in the Application Load Balancer that listens for HTTP traffic on port 80 and redirects it to HTTPS on port 443."
"Which Elastic Load Balancing type allows the load balancer to handle both TCP and UDP traffic on the same port?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer (NLB) can handle both TCP and UDP traffic on the same port."
"What is the best practice for configuring security groups when using Elastic Load Balancing?","Allow the load balancer's security group to receive traffic on the listener ports and allow the target's security group to receive traffic from the load balancer's security group.","Allow all traffic to the load balancer's security group and restrict traffic to the target's security group.","Restrict all traffic to the load balancer's security group and allow all traffic to the target's security group.","Allow all traffic to both the load balancer's and target's security groups.","The recommended practice is to allow the load balancer's security group to receive traffic on the listener ports and allow the target's security group to receive traffic from the load balancer's security group, ensuring secure and controlled communication."
"You need to implement a load balancer that forwards traffic to different target groups based on the country of origin of the request. Which ELB type is most suitable for this scenario and what feature would you use?","Application Load Balancer with Lambda functions for geo-location.","Network Load Balancer with source IP routing.","Classic Load Balancer with custom header manipulation.","Gateway Load Balancer with third-party firewall integration.","Application Load Balancer can integrate with Lambda functions to determine the country of origin based on the IP address and then route traffic accordingly."
"How do you configure an Elastic Load Balancer to use SSL/TLS for secure communication?","By uploading an SSL/TLS certificate to the load balancer.","By installing the SSL/TLS certificate on the backend instances.","By configuring the load balancer to use a specific encryption algorithm.","By enabling encryption on the VPC network.","You configure SSL/TLS by uploading an SSL/TLS certificate to the load balancer, which it uses to decrypt traffic from clients and encrypt traffic to backend instances (if configured)."
"Which ELB feature can help protect your application from distributed denial-of-service (DDoS) attacks?","Integration with AWS Shield.","Integration with AWS WAF.","Integration with AWS GuardDuty.","Integration with AWS Inspector.","ELB integrates with AWS Shield, a managed DDoS protection service, to help protect your application from DDoS attacks."
"What is the primary difference between the Classic Load Balancer and the Application Load Balancer?","Application Load Balancer supports content-based routing, while Classic Load Balancer does not.","Classic Load Balancer supports content-based routing, while Application Load Balancer does not.","Application Load Balancer only supports HTTP/HTTPS traffic, while Classic Load Balancer supports TCP traffic.","Classic Load Balancer has higher performance than Application Load Balancer.","The key difference is that Application Load Balancer supports content-based routing, which allows you to route traffic based on the content of the request (e.g., host header, path). Classic Load Balancer does not offer this feature."
"What is the main advantage of using Elastic Load Balancing with Auto Scaling?","Automatic scaling of EC2 instances based on traffic load.","Automatic backup of EC2 instances.","Automatic patching of EC2 instances.","Automatic monitoring of EC2 instances.","Combining ELB with Auto Scaling enables automatic scaling of EC2 instances based on traffic load, ensuring that your application can handle varying workloads while maintaining performance and availability."
"In terms of cost, what should you consider when choosing between different types of Elastic Load Balancers?","The number of connections and the amount of data processed by the load balancer.","The number of EC2 instances behind the load balancer.","The amount of storage used by the load balancer.","The region where the load balancer is deployed.","The cost of using ELB depends on factors like the number of connections, the amount of data processed, and the rules configured. NLB is generally more expensive than ALB for the same workload, but it provides higher performance."
"How can you ensure that your Elastic Load Balancer is configured to handle traffic spikes effectively?","By configuring Auto Scaling groups with appropriate scaling policies.","By increasing the size of the load balancer instance.","By enabling connection draining.","By configuring the load balancer to use a static IP address.","Auto Scaling groups allow you to automatically scale the number of EC2 instances behind the load balancer based on traffic load, ensuring that your application can handle traffic spikes effectively."
"Which Elastic Load Balancer type supports mutual authentication?","Gateway Load Balancer","Application Load Balancer","Network Load Balancer","Classic Load Balancer","The Gateway Load Balancer supports mutual authentication."
"Which Elastic Load Balancer would be most efficient to use for a high volume UDP based application?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancers offer the best performance for UDP applications."
"Which mechanism allows for a smooth transfer of traffic away from an unhealthy or deregistering instance behind a load balancer?","Connection Draining","Session Stickiness","Health Checks","Cross-Zone Load Balancing","Connection draining allows the load balancer to stop sending new requests to an instance while allowing existing connections to complete, ensuring a smooth transfer of traffic."
"You want to load balance traffic across multiple AWS regions. What AWS service should you consider alongside Elastic Load Balancing?","Global Accelerator","AWS Transit Gateway","AWS Direct Connect","AWS CloudFront","AWS Global Accelerator directs traffic to optimal endpoints based on user location, network congestion, and application health, making it ideal for load balancing across multiple regions."
"Which of the following is NOT a valid use case for Elastic Load Balancing?","High availability of EC2 instances","Distributing traffic to serverless functions","Load balancing database connections","Improving application security","Elastic Load Balancing is primarily designed for distributing traffic to EC2 instances, containers, and IP addresses. It is not typically used for load balancing database connections directly; that would require other solutions specific to database load balancing."
"When configuring an Application Load Balancer, what is the purpose of a 'rule'?","To define how the load balancer routes traffic based on conditions.","To specify the health check parameters.","To configure the security group settings.","To define the target group.","Rules define how the Application Load Balancer routes traffic based on conditions such as host header, path, or HTTP method."
"Which of the following actions is not a responsibility of Elastic Load Balancing?","Scaling compute capacity","Distributing incoming traffic","Monitoring target health","Providing SSL termination","Scaling compute capacity (e.g., adding or removing EC2 instances) is the responsibility of Auto Scaling, not Elastic Load Balancing."
"Which security feature is typically associated with Application Load Balancers to inspect and filter HTTP traffic?","AWS Web Application Firewall (WAF)","AWS Shield","AWS GuardDuty","Amazon Inspector","The AWS Web Application Firewall (WAF) integrates with Application Load Balancers to inspect and filter HTTP traffic, protecting against common web exploits and bots."
"What's the typical process for replacing an SSL certificate on an Application Load Balancer?","Upload the new certificate to AWS Certificate Manager (ACM) and update the listener to use it.","Recreate the Application Load Balancer with the new certificate.","Install the new certificate on each backend instance.","Update the DNS record to point to the new certificate.","The typical process is to upload the new certificate to AWS Certificate Manager (ACM) and then update the listener configuration on the Application Load Balancer to use the new certificate."
"Which AWS Elastic Load Balancing (ELB) type is best suited for load balancing HTTP, HTTPS, and WebSocket traffic?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancer is best suited for HTTP, HTTPS and Websocket traffic, operating at layer 7 of the OSI model and providing advanced routing capabilities."
"What is the primary benefit of using Elastic Load Balancing (ELB) in front of a group of EC2 instances?","Distributing incoming traffic across multiple EC2 instances","Encrypting data in transit","Automatically scaling the EC2 instances","Reducing the cost of EC2 instances","ELB distributes incoming application traffic across multiple targets, such as EC2 instances, in multiple Availability Zones, which increases the availability and fault tolerance of your application."
"In AWS, what is the purpose of 'Connection Draining' (also known as 'Deregistration Delay') in the context of Elastic Load Balancing (ELB)?","To allow in-flight requests to complete before an instance is deregistered","To automatically scale the number of EC2 instances","To encrypt connections between the load balancer and the instances","To monitor the health of EC2 instances","Connection draining ensures that the load balancer stops sending new requests to instances that are deregistering or unhealthy, allowing existing connections to complete. This minimises disruption to users."
"Which of the following is a key feature of the AWS Network Load Balancer (NLB) that distinguishes it from other ELB types?","It operates at the transport layer (Layer 4) of the OSI model","It supports content-based routing","It automatically scales the underlying infrastructure","It integrates with AWS WAF","NLB operates at layer 4, providing extremely high performance and low latency, forwarding TCP traffic directly to targets."
"You have an application that requires mutual TLS authentication. Which Elastic Load Balancing (ELB) type would you typically use?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancer supports mutual TLS authentication, allowing you to securely verify the identity of clients connecting to your application."
"When using Elastic Load Balancing (ELB), how can you ensure that traffic is only routed to healthy EC2 instances?","By configuring Health Checks","By using AWS Shield","By enabling Cross-Zone Load Balancing","By configuring security groups","ELB Health Checks monitor the health of registered instances, and only route traffic to instances that are deemed healthy. This enhances the availability and reliability of your application."
"Which Elastic Load Balancing (ELB) type is designed for handling high volumes of UDP traffic while maintaining low latency?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","Network Load Balancer is the best choice for handling high volumes of UDP traffic, providing extremely low latency and high throughput."
"You want to implement an Elastic Load Balancing (ELB) solution that supports routing traffic based on the hostname in the HTTP request. Which ELB type should you use?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","Application Load Balancer supports host-based routing, allowing you to route traffic based on the hostname in the HTTP request."
"In AWS Elastic Load Balancing (ELB), what is the purpose of 'Cross-Zone Load Balancing'?","Distributes traffic evenly across all enabled Availability Zones","Encrypts traffic between the load balancer and instances in different zones","Automatically scales the number of instances in each Availability Zone","Monitors the health of instances in different Availability Zones","Cross-Zone Load Balancing distributes traffic evenly across all registered instances in all enabled Availability Zones. Without it, each load balancer node only distributes traffic to instances in its own zone."
"You have an application that requires a single entry point for all traffic, including HTTP, HTTPS, and TCP. You also need deep packet inspection capabilities. Which Elastic Load Balancing (ELB) type would be most suitable?","Gateway Load Balancer","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer is designed for deep packet inspection and allows you to integrate third-party network appliances to your application traffic flow. It handles all types of traffic."
"Which type of Elastic Load Balancer (ELB) is best suited for routing HTTP and HTTPS traffic and provides advanced request routing based on content?","Application Load Balancer","Network Load Balancer","Classic Load Balancer","Gateway Load Balancer","The Application Load Balancer is designed for HTTP and HTTPS traffic and offers content-based routing, making it suitable for complex application architectures."
"Which Elastic Load Balancer (ELB) type operates at the transport layer (Layer 4) and is best suited for TCP, TLS and UDP traffic where ultra-high performance and low latency are required?","Network Load Balancer","Application Load Balancer","Classic Load Balancer","Gateway Load Balancer","The Network Load Balancer operates at Layer 4 and is designed for high performance and low latency, making it ideal for TCP, TLS, and UDP traffic."
"What is a key benefit of using Elastic Load Balancing (ELB) in front of your EC2 instances?","Increased availability and fault tolerance","Reduced EC2 instance cost","Automatic patching of EC2 instances","Simplified security group management","ELB distributes traffic across multiple EC2 instances, ensuring that your application remains available even if some instances fail, thus increasing availability and fault tolerance."
"Which Elastic Load Balancing (ELB) health check verifies the status of instances by sending requests and checking for expected HTTP response codes?","HTTP health check","TCP health check","ICMP health check","DNS health check","HTTP health checks send requests to instances and verify the response code, allowing ELB to determine if the instance is healthy and serving traffic correctly."
"How does Elastic Load Balancing (ELB) contribute to the scalability of an application?","By automatically distributing traffic across multiple instances","By automatically scaling the underlying infrastructure","By providing a caching layer","By optimising database queries","ELB automatically distributes incoming application traffic across multiple instances, enabling the application to handle increased load and scale horizontally."
"Which Elastic Load Balancer (ELB) is designed for deploying and managing virtual appliances, such as firewalls, intrusion detection and prevention systems, and deep packet inspection systems?","Gateway Load Balancer","Application Load Balancer","Network Load Balancer","Classic Load Balancer","The Gateway Load Balancer is designed for deploying and managing virtual appliances in the network path, providing features like implicit load balancing and scaling for these appliances."
"What is a 'listener' in the context of Elastic Load Balancing (ELB)?","A process that checks for connection requests","A server that hosts the application","A set of routing rules","A security group","A listener checks for connection requests from clients, using the protocol and port that you configure. The rules you define for a listener determine how the load balancer routes requests to its registered targets."
"How can you configure an Elastic Load Balancer (ELB) to handle traffic for multiple domains or subdomains?","Using host-based routing rules","Using path-based routing rules","Using fixed response actions","Using redirection actions","Host-based routing allows an Application Load Balancer to route requests to different target groups based on the hostname in the HTTP header, allowing it to handle traffic for multiple domains/subdomains."
"What is the primary use case for the 'Target Group' construct in Elastic Load Balancing (ELB) with Application Load Balancers?","To group EC2 instances for routing and health checks","To define security groups for the load balancer","To specify the pricing model for the load balancer","To configure the load balancer's logging settings","Target groups are used to group EC2 instances (or other targets) to which the load balancer can route traffic. They also define the health check settings for those instances."
"Which of the following is NOT a valid target type for an Application Load Balancer (ALB)?","EC2 instances","IP addresses","Lambda functions","S3 buckets","While ALB can target EC2 instances, IP addresses, and Lambda functions, it cannot directly target S3 buckets. S3 buckets are typically accessed directly or through other services like CloudFront."