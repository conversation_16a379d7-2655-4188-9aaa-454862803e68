"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"For AWS Saving Plans, what is the primary goal of committing to a consistent amount of compute usage?","To reduce compute costs compared to On-Demand pricing","To increase the performance of EC2 instances","To simplify the management of AWS resources","To gain access to AWS support services","Saving Plans offer significant discounts compared to On-Demand pricing in exchange for a commitment to a consistent amount of compute usage, measured in $/hour."
"Which AWS Saving Plan type offers the most flexibility in terms of instance type, operating system, and tenancy?","Compute Saving Plans","EC2 Instance Saving Plans","SageMaker Saving Plans","Data Saving Plans","Compute Saving Plans provide the most flexibility, allowing you to use a wide range of instance types, operating systems, and tenancies while still receiving discounted pricing."
"Which of the following AWS services is directly supported by EC2 Instance Saving Plans?","EC2","Lambda","RDS","DynamoDB","EC2 Instance Saving Plans are specifically designed for use with EC2 instances and provide discounts for usage of specific instance families in a region."
"What is the typical commitment duration offered for AWS Saving Plans?","1 or 3 years","3 or 5 years","6 months or 1 year","2 or 4 years","Saving Plans are typically offered with commitment durations of either 1 year or 3 years."
"How are AWS Saving Plans billed?","Hourly","Daily","Weekly","Monthly","Saving Plans are billed hourly based on the committed amount."
"What happens to your AWS compute usage if it exceeds the hourly commitment of your Saving Plan?","The excess usage is billed at On-Demand rates","The excess usage is billed at a discounted rate","The excess usage is automatically stopped","The excess usage is queued for later processing","Any usage exceeding the Savings Plan commitment is billed at the standard On-Demand rates."
"When purchasing AWS Saving Plans, which of the following factors does not influence the amount of savings you can achieve?","The AWS region where your compute resources are located","The level of commitment you make (amount per hour)","The size of your S3 buckets","The duration of the commitment (1 year vs. 3 years)","The size of your S3 buckets is not a factor, as Saving Plans apply to compute usage and not storage."
"What happens to an AWS Saving Plan if you no longer need the capacity that you committed to?","The Saving Plan commitment cannot be modified or cancelled","The unused portion can be sold on a marketplace","The unused portion can be transferred to another AWS account","The unused portion is refunded pro-rata","Saving Plans cannot be cancelled or modified after purchase. You are committed to the hourly rate for the entire duration."
"Which AWS tool can help you analyse your compute usage and provide recommendations for purchasing Saving Plans?","AWS Cost Explorer","AWS CloudWatch","AWS Trusted Advisor","AWS Config","AWS Cost Explorer provides detailed analysis of your AWS costs and usage, including recommendations for purchasing Savings Plans to optimise your spending."
"What is the benefit of using AWS Saving Plans instead of Reserved Instances (RIs)?","Greater flexibility in instance usage","Lower upfront costs","Guaranteed instance capacity","Dedicated hardware","Saving Plans offer greater flexibility than Reserved Instances because they apply to compute usage across different instance types and sizes within a family."
"Which AWS Saving Plan is most appropriate for sustained usage of a specific EC2 instance type in a specific region?","EC2 Instance Saving Plans","Compute Saving Plans","Database Saving Plans","SageMaker Saving Plans","EC2 Instance Savings Plans are ideal when you know you will consistently use a particular instance type in a particular region."
"How do AWS Saving Plans handle changes in EC2 instance pricing after the Saving Plan is purchased?","The Saving Plan rate remains fixed regardless of price changes","The Saving Plan rate is automatically adjusted to reflect the new prices","The Saving Plan is automatically cancelled","The Saving Plan savings are automatically increased","The Saving Plan rate remains fixed for the duration of the commitment, providing predictable cost savings."
"Which of the following is not a supported payment option for AWS Saving Plans?","Monthly payments","Upfront payments","Partial upfront payments","Hourly payments","Hourly payments is the billing rate but not a payment option at the time of purchase."
"Which AWS service can you use to monitor the utilisation and coverage of your Saving Plans?","AWS Cost Explorer","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Cost Explorer is the primary tool for monitoring the utilisation and coverage of your Savings Plans, allowing you to track your savings and identify potential areas for improvement."
"If you have both an EC2 Instance Saving Plan and a Compute Saving Plan, which one is applied first to eligible usage?","EC2 Instance Saving Plan","Compute Saving Plan","The Saving Plan that provides the highest discount is applied first","They are applied randomly","EC2 Instance Savings Plans are applied first to eligible usage before Compute Savings Plans."
"What is the difference between Standard Reserved Instances and AWS Saving Plans regarding instance size flexibility?","Saving Plans offer instance size flexibility, Standard Reserved Instances do not","Standard Reserved Instances offer instance size flexibility, Saving Plans do not","Both offer the same level of instance size flexibility","Neither offers instance size flexibility","Saving Plans provide greater flexibility as they automatically apply discounts to different instance sizes within the same family, whereas Standard Reserved Instances require manual modifications for size changes."
"If you purchase an AWS Saving Plan and later move your EC2 instances to a different region, will the Saving Plan still apply?","No, Saving Plans are region-specific","Yes, Saving Plans apply globally across all regions","Yes, but a fee applies","Yes, but only if the instances are in the same Availability Zone","Savings Plans are region-specific so the savings will only apply to the region they were purchased for."
"Which AWS compute service cannot be covered by Saving Plans?","EC2","Fargate","Lambda","ECS on EC2 with external launch type","Saving Plans can cover EC2, Fargate and Lambda."
"How do you determine the optimal Saving Plan commitment amount for your AWS environment?","By analysing historical compute usage patterns","By guessing and adjusting later","By using a fixed percentage of your total AWS budget","By consulting with an AWS sales representative only","Analysing historical compute usage patterns is crucial for determining the optimal Saving Plan commitment amount."
"What happens if you terminate an EC2 instance that is covered by an EC2 Instance Saving Plan?","The Saving Plan continues to apply to other instances of the same type in the region","The Saving Plan is automatically cancelled","The unused portion of the Saving Plan is refunded","The Saving Plan is paused until a new instance is launched","The Savings Plan will continue to be applied to other instances in your estate, of the same family and region, even if the original instance is terminated."
"What is a key advantage of AWS Saving Plans over Spot Instances for predictable workloads?","Saving Plans provide predictable pricing, while Spot Instances can fluctuate","Saving Plans offer higher performance","Saving Plans are easier to manage","Saving Plans are cheaper","Saving Plans offer predictable pricing and reserved capacity, while Spot Instances' pricing can fluctuate and may be terminated."
"Which of the following AWS Cost Explorer reports can help you identify potential Saving Plan purchase opportunities?","Savings Plans Purchase Recommendations","Reserved Instance Utilization Report","Cost Allocation Report","Cost and Usage Report","The Savings Plans Purchase Recommendations report provides specific recommendations for purchasing Savings Plans based on your historical usage."
"When using AWS Saving Plans, are you guaranteed a specific amount of compute capacity?","No, Saving Plans do not guarantee capacity","Yes, Saving Plans guarantee capacity in the same way as Reserved Instances","Yes, Saving Plans guarantee capacity but only for specific instance types","Yes, Saving Plans guarantee capacity but only during peak hours","Saving Plans provide discounted pricing for compute usage, but do not guarantee capacity. Instance capacity is still subject to availability."
"Which of the following is the correct unit of measurement for AWS Saving Plans commitment?","$/hour","$/month","$/year","$/instance","Savings Plans commitments are measured in $/hour, representing the amount you commit to spend on compute resources per hour."
"Can you combine AWS Saving Plans with other discount programs, such as Reserved Instances?","Yes, but Savings Plans are applied first","Yes, but Reserved Instances are applied first","No, they cannot be combined","Yes, but only with the permission of AWS support","Savings Plans and Reserved Instances can coexist. Savings Plans are applied first to eligible usage."
"If you have multiple AWS accounts in an organisation, can you apply a Saving Plan across all accounts?","Yes, through consolidated billing","No, Saving Plans are account-specific","Yes, but only if all accounts are in the same region","Yes, but only for EC2 Instance Saving Plans","Through consolidated billing, Savings Plans purchased in the master account can be shared and applied to eligible usage across all linked accounts within the organisation."
"What is the main benefit of using Savings Plans over On-Demand instances in terms of billing?","Savings Plans offer significantly lower prices than On-Demand instances","Savings Plans provide more detailed billing reports than On-Demand instances","Savings Plans allow you to pause billing when instances are not in use","Savings Plans offer the option to pay for compute resources in advance","Savings Plans offer significantly lower prices than On-Demand instances in exchange for a commitment."
"If you are using AWS Lambda extensively, which type of Saving Plan would be most beneficial?","Compute Saving Plans","EC2 Instance Saving Plans","Lambda Saving Plans","RDS Saving Plans","Compute Savings Plans are the best option for savings on Lambda usage."
"In terms of commitment, what happens if the hourly equivalent of your monthly spend is less than your Savings Plan commitment?","You are billed for the full Saving Plan commitment regardless","You are only billed for the actual usage","The unused commitment is carried over to the next month","The unused commitment is refunded","You are billed for the full Savings Plan commitment regardless of your actual usage."
"What is the relationship between AWS Saving Plans and Availability Zones?","Saving Plans are not tied to specific Availability Zones","Saving Plans guarantee instance availability in specific Availability Zones","Saving Plans are only available in certain Availability Zones","Saving Plans require you to choose Availability Zones at the time of purchase","Saving Plans are not tied to specific Availability Zones, offering flexibility in where you run your compute workloads."
"When purchasing AWS Saving Plans, what is the effect of choosing a longer commitment term (e.g., 3 years vs. 1 year)?","Longer terms typically result in greater discounts","Longer terms guarantee higher instance performance","Longer terms allow for more flexible payment options","Longer terms simplify the billing process","Longer commitment terms typically result in greater discounts compared to shorter terms."
"What is the relationship between AWS Saving Plans and the AWS Free Tier?","Saving Plans apply after the AWS Free Tier benefits are exhausted","Savings Plans can be used instead of the AWS Free Tier","Saving Plans extend the duration of the AWS Free Tier","Savings Plans do not interact with the AWS Free Tier","Saving Plans provide cost savings after the AWS Free Tier benefits are fully used."
"If you are running a mix of EC2, Fargate, and Lambda, which Saving Plan type is most suitable for overall cost optimisation?","Compute Saving Plans","EC2 Instance Saving Plans","Fargate Saving Plans","Lambda Saving Plans","Compute Savings Plans offer the best flexibility if using EC2, Fargate and Lambda as it applies to all these types of compute workload."
"What happens if you purchase an EC2 Instance Saving Plan for an instance type that is later discontinued by AWS?","The Saving Plan continues to apply to the newer equivalent instance type","The Saving Plan is automatically cancelled and refunded","The Saving Plan can be transferred to a different region","The Saving Plan becomes invalid","The Savings Plan would be used on the newer equivalent instance type."
"You need to purchase a Saving Plan, but you are unsure if it will cover your usage. What should you do?","Start with a smaller commitment and increase it later if needed","Purchase the maximum commitment amount available","Rely solely on On-Demand instances to avoid any commitment","Guess the commitment amount based on limited historical data","Starting with a smaller commitment and increasing it later if needed is a safer approach to avoid overcommitting."
"What is the key benefit of using Compute Saving Plans over EC2 Instance Saving Plans?","Compute Saving Plans provide more flexibility across instance families and regions","Compute Saving Plans offer lower prices","Compute Saving Plans guarantee instance capacity","Compute Saving Plans are easier to manage","Compute Savings Plans offer more flexibility as they can apply across different instance families, sizes, operating systems, and regions, unlike EC2 Instance Savings Plans."
"You have purchased a Saving Plan and are seeing less savings than expected. What should you investigate?","Whether your compute usage is consistently matching your hourly commitment","Whether your instances are running in the correct Availability Zone","Whether your operating system is supported by the Saving Plan","Whether your network bandwidth is sufficient","Investigating whether your compute usage consistently matches your hourly commitment is crucial for maximising savings."
"How can you modify an existing AWS Saving Plan after it has been purchased?","Saving Plans cannot be modified after purchase","You can increase the commitment amount, but not decrease it","You can change the commitment duration, but not the amount","You can change the payment option, but not the commitment amount","Savings Plans cannot be modified after purchase."
"You are using a Compute Saving Plan and want to optimise your savings further. What should you consider?","Consolidating workloads to fewer instance families","Diversifying workloads across more instance families","Switching to a different operating system","Migrating to a different AWS region","Consolidating workloads to fewer instance families can help maximise savings by ensuring consistent utilisation of the Savings Plan commitment."
"Which AWS service can provide detailed reports on your Saving Plan savings and utilisation?","AWS Cost Explorer","AWS CloudTrail","AWS CloudWatch","AWS Config","AWS Cost Explorer provides comprehensive reports on Savings Plan savings and utilisation."
"Can you transfer an AWS Saving Plan to another AWS account that is not part of your organisation?","No, Saving Plans cannot be transferred outside of an organisation","Yes, Saving Plans can be freely transferred between any AWS account","Yes, but only with the permission of AWS support","Yes, but only if the accounts are in the same region","Savings Plans cannot be transferred outside of an organisation."
"You have purchased a Savings Plan and the same day AWS announces a new instance generation. What will happen to your Savings Plan?","The Savings Plan still applies to usage on older instances, or the new instances if they are in the same family.","The Savings Plan will automatically be updated to apply to the newer instance types.","Your Savings Plan will be terminated and you will be issued a refund.","You will have to purchase a new Savings Plan for the new instance generation.","The Savings Plan will remain in effect and applies to the older instances or equivalent instances in the new generation if you move to using them."
"What is the best practice for cost optimisation when using AWS Saving Plans?","Continuously monitor usage and adjust your infrastructure to maximise Saving Plan utilisation","Ignore usage patterns and rely solely on the Saving Plan for cost savings","Over-provision resources to ensure sufficient capacity","Purchase the largest Saving Plan available regardless of actual usage","Continuously monitoring usage and adjusting your infrastructure to maximise Savings Plan utilisation is a key best practice."
"How do Compute Savings Plans differ from EC2 Instance Savings Plans in terms of their coverage?","Compute Savings Plans cover EC2, Lambda and Fargate, while EC2 Instance Savings Plans only cover EC2.","Compute Savings Plans only cover EC2, while EC2 Instance Savings Plans also cover Lambda and Fargate.","Compute Savings Plans offer a fixed discount percentage, while EC2 Instance Savings Plans offer a variable discount.","There is no difference, they both cover the same services.","Compute Savings Plans provide broader coverage as they apply to EC2, Lambda, and Fargate, while EC2 Instance Savings Plans are specifically for EC2."
"In what scenario would an EC2 Instance Saving Plan be more beneficial than a Compute Saving Plan?","When you have a need for a specific EC2 instance type in a particular region over the long term","When you want to use a combination of EC2, Lambda and Fargate.","When your compute needs are highly variable and unpredictable.","When you need more control over resource allocation.","If you're committed to a specific instance family in a particular region, EC2 Instance Savings Plans give you better value."
"What happens to Savings Plan coverage if you change the tenancy of an EC2 instance?","Savings Plan coverage can still apply if the instance size is compatible with the Savings Plan.","Savings Plan coverage is automatically cancelled.","Savings Plan coverage will be adjusted by AWS Support.","Savings Plan coverage is doubled.","Savings Plan coverage can still apply if the instance size is compatible with the Savings Plan. Consider the operating system as well."
"Which AWS service will provide you with automated recommendations about which Savings Plan to purchase?","AWS Cost Explorer","AWS Trusted Advisor","AWS CloudWatch","AWS CloudTrail","AWS Cost Explorer makes recommendations about purchasing Savings Plans based on your AWS usage."
"When should you evaluate purchasing an AWS Savings plan?","When you have stable and predictable compute requirements","When you want to reduce data storage costs","When you have fluctuating compute needs","Only when you receive a recommendation from AWS support","You should evaluate purchasing Savings Plans when you have stable and predictable compute requirements."
"Which statement best describes the commitment characteristic of AWS Compute Saving Plans?","You commit to a consistent hourly spend on compute usage.","You commit to a specific instance type.","You commit to a certain amount of storage.","You commit to using a specific AWS region.","Compute Saving Plans provide significant savings over On-Demand pricing by committing to a consistent hourly spend."
"What is the primary benefit of using AWS Saving Plans over On-Demand pricing for eligible compute resources?","Reduced cost for compute resources.","Automatic scaling of compute resources.","Enhanced security for compute resources.","Improved performance of compute resources.","The primary benefit of using Saving Plans is the significant cost reduction compared to On-Demand pricing for eligible compute resources."
"What types of compute resources are covered under AWS Compute Saving Plans?","EC2 instances, Fargate, and Lambda","EC2 instances and S3","RDS instances and DynamoDB","Only EC2 instances","Compute Saving Plans cover EC2 instances, Fargate, and Lambda, offering flexibility across different compute services."
"Which of the following statements is true regarding the payment options for AWS Saving Plans?","You can choose between a No Upfront, Partial Upfront, or All Upfront payment option.","You must pay the full amount upfront.","You can only pay monthly.","You can only pay hourly.","AWS Saving Plans offer payment flexibility with options for No Upfront, Partial Upfront, or All Upfront payment, impacting the overall savings."
"What happens if your compute usage exceeds your AWS Compute Saving Plan commitment?","The exceeding usage is billed at On-Demand rates.","The exceeding usage is automatically covered by a new Saving Plan.","The exceeding usage is stopped.","The exceeding usage is billed at a higher rate than On-Demand.","When your compute usage exceeds your commitment, the additional usage is billed at standard On-Demand rates."
"For AWS Compute Saving Plans, what is the most effective way to maximise savings?","Optimise your compute usage to match your commitment as closely as possible.","Over-commit to ensure you always have enough capacity.","Under-commit to avoid unused capacity.","Use only the No Upfront payment option.","Optimising your compute usage to closely align with your Saving Plan commitment ensures you're fully utilising your investment and maximising savings."
"What is the minimum commitment duration for AWS Saving Plans?","1 year","3 months","6 months","3 years","The minimum commitment duration for Saving Plans is typically one year, with a longer commitment leading to greater savings."
"If you purchase an AWS Compute Saving Plan and then change your EC2 instance types, does the Saving Plan still apply?","Yes, the Saving Plan applies as long as the compute usage is within the same family.","No, the Saving Plan is tied to the original instance type.","Only if you contact AWS support to modify the Saving Plan.","Only if the new instance type is smaller than the original.","Compute Saving Plans offer flexibility, applying to any EC2 instance usage, regardless of instance type, operating system, or tenancy within the same family."
"Which type of AWS Saving Plan provides the most flexibility in terms of instance type and region?","Compute Saving Plan","EC2 Instance Saving Plan","Fargate Saving Plan","Lambda Saving Plan","Compute Saving Plans provide the greatest flexibility as they apply to a variety of compute resources, instance types, and regions."
"When purchasing an AWS Saving Plan, what factor should you consider to determine the appropriate hourly commitment?","Your historical compute usage patterns.","The current spot instance prices.","The number of IAM users in your account.","The size of your EBS volumes.","Analysing your historical compute usage patterns is essential for determining the appropriate hourly commitment, ensuring you're neither overpaying nor underutilising the Saving Plan."
"What is the primary difference between an AWS Compute Saving Plan and an AWS EC2 Instance Saving Plan?","The Compute Saving Plan offers more flexibility in terms of instance type and region.","The EC2 Instance Saving Plan covers more AWS services.","The Compute Saving Plan is more expensive.","The EC2 Instance Saving Plan has a shorter commitment period.","Compute Saving Plans are more flexible, applying to different instance types, OS, and regions, whereas EC2 Instance Saving Plans are tied to specific instance families within a region."
"What is the benefit of choosing an 'All Upfront' payment option for an AWS Saving Plan?","The highest discount rate.","The ability to cancel the Saving Plan at any time.","The option to change the instance type.","The shortest commitment period.","Choosing 'All Upfront' payment typically results in the highest discount rate compared to 'No Upfront' or 'Partial Upfront'."
"Which AWS service can you use to monitor your Saving Plans utilisation and savings?","AWS Cost Explorer","Amazon CloudWatch","AWS Config","AWS Trusted Advisor","AWS Cost Explorer provides detailed reports and visualisations to monitor Saving Plans utilisation, savings, and potential optimisation opportunities."
"How do AWS Saving Plans contribute to cost optimisation in the cloud?","By providing discounted pricing for consistent compute usage.","By automatically scaling resources up or down.","By providing enhanced security features.","By optimising network performance.","Saving Plans optimise costs by offering discounted pricing in exchange for a commitment to consistent compute usage."
"Can you combine AWS Saving Plans with other discount programs, such as Spot Instances?","Yes, Saving Plans are applied before Spot Instances.","No, Saving Plans cannot be combined with other discount programs.","Only if you contact AWS support.","Only for specific instance types.","Saving Plans are applied before other discounts such as Spot Instances. If an instance isn't covered by a saving plan it can then use Spot instances for further cost reduction."
"Which type of AWS Saving Plan is best suited for workloads that require specific instance types in a particular region?","EC2 Instance Saving Plan","Compute Saving Plan","Fargate Saving Plan","Lambda Saving Plan","EC2 Instance Saving Plans are the best option for workloads where instance families, sizes, and regions are fairly predictable."
"What should you do if you consistently underutilised your AWS Saving Plan commitment?","Consider modifying or purchasing a new Saving Plan with a lower commitment.","Contact AWS support to request a refund.","Ignore the underutilisation as long as your total costs are lower than On-Demand.","Automatically switch to On-Demand pricing.","If you are consistently using less than your commitment, you should consider modifying or purchasing a new Saving Plan with a lower commitment to better align with your actual usage."
"What is the impact of purchasing multiple AWS Saving Plans with overlapping coverage?","AWS automatically optimises the application of Savings Plans to maximise savings.","The Savings Plans are applied in the order they were purchased.","The Savings Plans are applied randomly.","The newer Savings Plan overrides the older one.","AWS automatically optimises the application of Savings Plans, choosing the most cost-effective combination to maximise savings, even with overlapping coverage."
"What is a key consideration when choosing between a 1-year and a 3-year commitment for an AWS Saving Plan?","The stability and predictability of your long-term compute needs.","The current interest rates.","The availability of AWS support.","The size of your organisation.","The key consideration is the stability and predictability of your long-term compute needs. A longer commitment (3-year) typically results in greater savings but requires a longer-term forecast of your usage."
"How can you determine the optimal AWS Saving Plan commitment for your organisation's compute resources?","By analysing your historical compute usage data using tools like AWS Cost Explorer.","By asking AWS support for a recommendation.","By randomly selecting a commitment amount.","By guessing based on your current budget.","Analysing historical compute usage data using tools like AWS Cost Explorer is crucial for determining the optimal commitment, ensuring alignment with your actual needs and maximising savings."
"Which of these is NOT a factor that influences the savings you achieve with AWS Saving Plans?","The number of IAM users in your account.","The instance family and size.","The region where you deploy your instances.","The payment option you choose.","The number of IAM users in your account does not directly influence the savings you achieve with Savings Plans. The savings are influenced by factors like instance type, region, and payment option."
"If you are migrating workloads to AWS, when is the best time to purchase AWS Saving Plans?","After you have a stable baseline of compute usage.","Before you start migrating.","While you are in the planning stage.","After the migration is complete.","It's best to purchase Saving Plans after you have a stable baseline of compute usage. This allows you to accurately assess your needs and avoid over or under-committing."
"Which AWS Saving Plan offers savings for EC2 instances regardless of the operating system used?","Compute Saving Plan","Linux EC2 Instance Saving Plan","Windows EC2 Instance Saving Plan","SQL Server EC2 Instance Saving Plan","Compute Savings Plan applies to any operating system."
"An organisation wants to reduce compute costs but is unsure about future instance type requirements. Which Saving Plan is most suitable?","Compute Saving Plan","EC2 Instance Saving Plan","Fargate Saving Plan","Reserved Instance","Compute Saving Plan is the most appropriate because it applies to different instance types."
"What happens to unused AWS Saving Plan capacity within an hour?","It is not carried over and is lost.","It is rolled over to the next hour.","It is credited to your account.","It is converted to Spot Instances.","Unused Saving Plan capacity within an hour is not carried over and is lost. It's important to optimise usage to align with your commitment."
"Which of the following is a key benefit of using AWS Saving Plans for serverless workloads on AWS Lambda?","Cost reduction for function execution.","Automatic scaling of function concurrency.","Enhanced security for function code.","Simplified deployment of function packages.","Saving Plans provide significant cost reduction for function execution compared to On-Demand pricing for Lambda."
"What is the recommended approach for rightsizing AWS Saving Plans after a period of sustained compute usage?","Analyse AWS Cost Explorer to identify optimisation opportunities.","Deactivate the current Savings Plan and set a new one.","Request AWS support to adjust the Savings Plan.","Increase commitment without analyzing resource usage.","Using AWS Cost Explorer data will provide accurate usage data for optimization and rightsizing."
"How can you ensure that your AWS Saving Plans are being effectively utilised across multiple AWS accounts within an organisation?","Implement cost allocation tags and use AWS Cost Explorer to track usage by account.","Manually track usage across all accounts.","Limit Saving Plan usage to a single account.","Ignore usage patterns across accounts.","Cost allocation tags and AWS Cost Explorer allows for tracking by usage and account and therefore are a way to monitor the effectiveness across multiple AWS accounts."
"If you plan to significantly change your infrastructure in the next year, which AWS Saving Plan commitment duration is the most appropriate?","1-year","3-year","5-year","10-year","A 1-year Saving Plan commitment is the most appropriate since it limits the risks of over or under commitment with the new infrastructure."
"Which AWS tool helps you visualise AWS Saving Plan coverage across your AWS accounts?","AWS Cost Explorer","AWS CloudTrail","AWS Config","AWS Trusted Advisor","AWS Cost Explorer allows for the viewing of coverage across accounts as it offers detailed reports and visualisations."
"What is the difference between a Saving Plan and Reserved Instance in AWS?","Saving Plans provide more flexibility in terms of instance types and regions.","Reserved Instances are automatically applied.","Reserved Instances offer only hourly based savings.","Saving Plans cannot be purchased via the marketplace.","Saving Plans are more flexible than Reserved Instances since they are not tied to a specific instance type."
"If you are running most of your instances within a specific region what type of Savings Plan would you choose to get the best discount if you know you need instances in that family?","EC2 Instance Saving Plan","Compute Saving Plan","Fargate Saving Plan","Lambda Saving Plan","If running in the same region you would select the EC2 instance savings plan for the instance family running there."
"Which factor is most important to consider when purchasing AWS Savings Plans?","Historical EC2 instance usage","AWS Account ID","Total number of AWS resources","Number of users in AWS IAM","Historical EC2 instance usage is the key factor to analyse to determine the right Savings Plan commitments."
"What happens if the on-demand price of an instance drops below the savings plan effective price?","You still pay the savings plan effective price","You pay the on-demand price","The savings plan is automatically paused","AWS refunds the difference","You still pay the savings plan effective price."
"What is the scope of an AWS Compute Saving Plan in terms of AWS accounts?","It can be shared across multiple accounts in your organisation","It applies to a single AWS account only","It applies only to the payer account","It can be transferred between accounts","Saving Plans can be shared across multiple accounts within the same organisation."
"You have purchased an AWS Compute Saving Plan. Which billing dimension is prioritised for the Saving Plan discount?","Compute usage","Storage usage","Network usage","Database usage","Compute usage is prioritised for Savings Plans discounts."
"What is the first step you should take before purchasing an AWS Saving Plan?","Analyse your historical AWS compute usage","Enable multi-factor authentication","Set up AWS Budgets","Create a list of all your EC2 instances","Analysing historical compute usage is the first step to identify trends and optimal commitment levels."
"Which AWS service does NOT benefit directly from AWS Compute Saving Plans?","Amazon S3","AWS Lambda","Amazon EC2","AWS Fargate","Amazon S3 does not benefit from compute saving plans."
"What is the best practice for setting the payment option of AWS Saving Plans?","Consider your cash flow and the desired level of discount","Always choose the 'All Upfront' option for the highest savings","Always choose the 'No Upfront' option to minimize initial investment","Choose the option that aligns with your existing billing cycle","Your current financial status and the level of discount will determine the best payment option."
"You have an AWS Compute Saving Plan. An EC2 instance is stopped for a day. What happens to the Saving Plan coverage for that instance?","You still pay the savings plan rate","The savings plan is suspended","The savings plan is transferred to another instance","The savings plan credit is lost","You still pay the savings plan rate as you are committed to that spend."
"Which AWS service helps you understand the potential savings from purchasing AWS Saving Plans?","AWS Cost Explorer","AWS CloudTrail","AWS Config","AWS IAM","AWS Cost Explorer is the service used to evaluate the potential savings from Saving Plans."
"What is the impact of purchasing an AWS Saving Plan on your existing Reserved Instances?","Saving Plans are applied first before Reserved Instances","Reserved Instances are applied first before Saving Plans","They cannot coexist","Reserved Instances are automatically converted to Saving Plans","Reserved Instances are applied first, then Saving Plans."
"You want to achieve the highest possible savings rate with AWS Saving Plans. What payment option should you choose?","All Upfront","No Upfront","Partial Upfront","Monthly","All Upfront payments usually get the highest savings."
"Your company is unsure if they will use the same instances the following year, what commitment should be chosen?","1 year","3 year","5 year","10 year","Choose a 1 year commitment if you are unsure of future needs."
"Which of the following tasks can be performed on the AWS Cost Explorer Saving Plans Recommendations page?","View your potential savings by purchasing a Saving Plan","View your current CPU Utilisation","Delete all ec2 Instances","Generate SSH keys","You can view your potential savings by purchasing a Saving Plan from the AWS Cost Explorer Saving Plans Recommendations page."
"Which of the following is the BEST definition of a Saving Plan Coverage in Cost Explorer?","How much of your instance usage is covered by a Saving Plan","The amount of disk space on your EC2 instances","The memory usage of your Lambda functions","The total network bandwidth used by your application","How much of your instance usage is covered by a Saving Plan is what it means by coverage."
"Where can one locate AWS Saving Plans in the AWS console?","Cost Management Console","IAM Console","EC2 Console","Cloudwatch Console","AWS Saving Plans can be located in the Cost Management Console."
"Can you move an AWS Saving Plan from one AWS account to another in AWS Organizations?","No, Saving Plans cannot be moved between accounts.","Yes, if both accounts are in the same consolidated billing family.","Yes, but only if the plan is unutilised.","Yes, but only with AWS support assistance.","No, Saving Plans cannot be moved between accounts."
"You have purchased an AWS Compute Savings Plan. What happens if you delete an EC2 instance covered by the plan?","The savings will automatically apply to another EC2 instance.","You will get a refund for the remaining portion of the savings plan.","The Savings plan no longer works","You continue to pay for the Savings Plan, and the discount can be applied to other eligible usage.","The savings will automatically apply to another EC2 instance."
