"What is the primary purpose of AWS AppSync?","Building GraphQL APIs backed by various data sources","Managing EC2 instances in a secure environment","Providing a serverless compute service for containerised applications","Storing and retrieving large amounts of data in a NoSQL database","AppSync is a managed GraphQL service that makes it easy to build data-driven mobile and web applications."
"Which of the following is a valid data source that can be integrated with AWS AppSync?","DynamoDB","CloudWatch Logs","S3 bucket","CloudFront distribution","AppSync natively integrates with DynamoDB as a data source."
"In AWS AppSync, what is a resolver?","A function that connects a GraphQL field to a data source","A security mechanism for controlling access to data","A type of GraphQL schema directive","A mechanism for caching API responses","Resolvers connect GraphQL fields to data sources, fetching or mutating data based on the request."
"What type of authentication methods does AWS AppSync support?","API Keys, IAM roles, Cognito User Pools, and OIDC","Only API Keys","Only IAM roles","Only Cognito User Pools","AppSync supports a variety of authentication methods, including API Keys, IAM roles, Cognito User Pools, and OIDC, providing flexibility and security."
"When using AWS AppSync with DynamoDB, which feature enables you to efficiently retrieve data using a key?","GetItem operation in the resolver mapping template","Scan operation in the resolver mapping template","Query operation with a filter expression","BatchGetItem operation across multiple tables","The GetItem operation in the resolver mapping template allows you to retrieve a single item from DynamoDB using its primary key, offering efficient data retrieval."
"What is the role of a 'pipeline resolver' in AWS AppSync?","To execute a series of operations on a data source, sequentially","To automatically scale the AppSync API based on traffic","To enforce authentication and authorisation rules","To cache responses from data sources","A pipeline resolver allows you to define a series of operations (functions) that are executed sequentially against a data source, enabling complex data transformations and business logic."
"Which AWS service is commonly used with AppSync for user authentication and authorisation?","Cognito","IAM","Lambda","CloudTrail","Cognito is frequently used with AppSync to handle user authentication and authorization, providing a secure way to manage user access to the API."
"In AWS AppSync, what is a 'schema' used for?","To define the structure of the data and operations exposed by the API","To configure the caching behaviour of the API","To specify the deployment region for the API","To manage the scaling limits of the API","The schema defines the data types, queries, mutations, and subscriptions available in the AppSync API, essentially dictating the structure and operations of the API."
"What does the '$context' object provide within an AWS AppSync resolver mapping template?","Information about the GraphQL request and the execution environment","A direct connection to the underlying data source","A set of pre-defined functions for data manipulation","A list of available environment variables","The $context object provides access to information about the incoming GraphQL request, the resolver execution environment, and the data source."
"How can you handle real-time data updates in AWS AppSync?","Using GraphQL Subscriptions","Using HTTP polling","Using Lambda triggers","Using SQS queues","GraphQL Subscriptions in AppSync enable real-time data updates by allowing clients to subscribe to specific events and receive updates when they occur."
"What is the purpose of 'request mapping templates' in AWS AppSync?","To transform the GraphQL request into a format suitable for the data source","To define the GraphQL schema","To configure authentication settings","To enable caching","Request mapping templates transform the incoming GraphQL request into a format that the data source understands, such as a DynamoDB PutItem request."
"What is the purpose of 'response mapping templates' in AWS AppSync?","To transform the data source response into a GraphQL response","To define the GraphQL schema","To configure authentication settings","To enable caching","Response mapping templates transform the data returned by the data source into the format expected by the GraphQL API, ensuring compatibility between the data source and the API response."
"What is the best way to handle authorisation at the field level in AWS AppSync?","Using @auth directives in the GraphQL schema","Using IAM policies attached to resolvers","Using API Keys with fine-grained permissions","Using Lambda authorizers","The @auth directive in the GraphQL schema allows you to define authorization rules at the field level, controlling access to specific fields based on various criteria."
"Which of the following is a benefit of using AWS AppSync?","Simplified development of real-time and offline-capable applications","Automatic management of EC2 instances","Cost-effective storage of large files","Enhanced security for VPC resources","AppSync simplifies the development of applications that require real-time updates and offline capabilities by providing features like GraphQL Subscriptions and local data storage."
"How does AWS AppSync handle offline data synchronisation?","By using local data storage and conflict resolution mechanisms","By automatically replicating data to multiple regions","By caching API responses on the client side","By providing a VPN connection to the data source","AppSync provides features for managing offline data synchronisation, including local data storage and conflict resolution mechanisms, ensuring data consistency even when the client is offline."
"You need to build a chat application with real-time updates. Which AWS service combination would be most suitable?","AWS AppSync and DynamoDB","AWS Lambda and SQS","Amazon API Gateway and EC2","Amazon CloudFront and S3","AppSync, with its support for GraphQL Subscriptions and DynamoDB as a scalable data store, is ideal for building real-time chat applications."
"What is the benefit of using AWS AppSync with Lambda functions?","To execute custom business logic and data transformations","To cache API responses","To manage user authentication","To configure DNS routing","Using Lambda functions with AppSync allows you to execute custom business logic and data transformations, providing flexibility and extensibility in your API."
"How can you monitor the performance of your AWS AppSync API?","Using CloudWatch metrics and logs","Using CloudTrail logs","Using VPC Flow Logs","Using S3 access logs","CloudWatch provides metrics and logs that allow you to monitor the performance of your AppSync API, identify bottlenecks, and optimise its performance."
"In AWS AppSync, what is a 'data source' abstraction?","A representation of the backend service that AppSync connects to","A type of GraphQL query","A way to define user roles and permissions","A caching mechanism","A data source in AppSync represents the backend service that AppSync connects to, such as DynamoDB, Lambda, or HTTP API."
"What is the use of AWS AppSync's 'conflict detection' feature?","To handle conflicting updates to data in offline-enabled applications","To detect security threats to the API","To identify performance bottlenecks","To prevent schema validation errors","Conflict detection in AppSync is crucial for handling conflicting updates to data in offline-enabled applications, ensuring data consistency across multiple devices and the cloud."
"Which of these AWS services is NOT directly supported as a data source in AWS AppSync without using a custom resolver?","Amazon SQS","DynamoDB","Lambda","HTTP API","Amazon SQS is not directly supported as a data source in AppSync without using a custom resolver (e.g., a Lambda function). AppSync directly supports DynamoDB, Lambda, and HTTP APIs."
"What is the purpose of 'VTL' (Velocity Template Language) in AWS AppSync?","To transform requests and responses in resolver mapping templates","To define the GraphQL schema","To configure authentication providers","To manage API caching","VTL is used in AppSync resolver mapping templates to transform requests and responses between the GraphQL API and the data sources."
"You are designing an AWS AppSync API for a mobile application that requires offline access and real-time updates. What feature should you implement?","GraphQL Subscriptions with local data storage and conflict resolution","API Key authentication","AWS WAF integration","CloudFront caching","GraphQL Subscriptions enable real-time updates, and local data storage with conflict resolution supports offline access, making this the best choice for the given requirements."
"Which of the following is a typical use case for AWS AppSync subscriptions?","Pushing real-time updates to connected clients","Running scheduled batch jobs","Storing session data","Performing image resizing","AppSync subscriptions are designed to push real-time updates to connected clients, making them ideal for applications like chat apps or live dashboards."
"What is the recommended way to handle errors in AWS AppSync resolvers?","Using the '$util.error' function in the response mapping template","Ignoring errors and letting the client handle them","Using try-catch blocks in the GraphQL schema","Relying on the data source to handle all errors","The '$util.error' function allows you to create custom error messages and return them to the client, providing better error handling and debugging capabilities."
"Which of the following is an advantage of using GraphQL with AWS AppSync over REST APIs?","Efficient data fetching with the ability to request only the required data","Automatic scaling of backend infrastructure","Built-in support for caching","Simplified authentication configuration","GraphQL allows clients to request only the data they need, reducing over-fetching and improving efficiency compared to REST APIs."
"What type of API does AWS AppSync create?","GraphQL API","REST API","SOAP API","RPC API","AWS AppSync is a managed service that creates GraphQL APIs. It simplifies the development of data-driven mobile and web applications by providing a flexible way to query and update data from multiple sources."
"How can you integrate AWS AppSync with an existing REST API?","By using an HTTP data source and configuring resolvers to call the REST API","By automatically converting the REST API into a GraphQL API","By importing the REST API definition into AppSync","By creating a proxy server in front of the REST API","AppSync can integrate with existing REST APIs by configuring an HTTP data source and creating resolvers that call the REST API endpoints. This allows you to leverage existing services while benefiting from GraphQL's features."
"What is the purpose of the 'caching' feature in AWS AppSync?","To improve API performance by storing frequently accessed data","To store user session data","To cache data in the client application","To cache API requests for offline access","Caching in AppSync improves API performance by storing frequently accessed data in a cache, reducing the need to repeatedly fetch data from the data source."
"Which of the following is a key component of the AWS AppSync architecture?","Resolvers","EC2 instances","VPC","Load balancers","Resolvers are a key component of the AWS AppSync architecture. They connect GraphQL fields to data sources, fetching or mutating data based on the request, and are essential for integrating data sources with the GraphQL API."
"You want to limit the number of requests that can be made to your AWS AppSync API from a specific IP address. What feature can you use?","API Key throttling","IAM policy rate limiting","CloudFront geo-restriction","WAF rate limiting","API Key throttling in AWS AppSync allows you to limit the number of requests that can be made using a specific API key, which can be used to control access from specific IP addresses or clients."
"What is the function of 'Direct Lambda Resolver' in AppSync?","To invoke a Lambda function directly without using VTL mapping templates","To create a GraphQL schema from a Lambda function","To deploy a Lambda function from AppSync","To monitor the performance of Lambda functions","Direct Lambda resolvers simplify the process of connecting to AWS Lambda functions as data sources by removing the need to write VTL mapping templates for request and response transformation."
"What is the role of the AWS AppSync console?","To define and manage GraphQL schemas, data sources, and resolvers","To monitor EC2 instances","To manage S3 buckets","To configure VPC settings","The AWS AppSync console provides a user interface for defining and managing GraphQL schemas, data sources, resolvers, and other AppSync configurations."
"You need to implement fine-grained access control in your AWS AppSync API based on user attributes. Which authentication method is most suitable?","Cognito User Pools with custom attributes and @auth directives","API Keys with IP address filtering","IAM roles with resource-based policies","OIDC with client credentials flow","Cognito User Pools with custom attributes and @auth directives allow you to implement fine-grained access control based on user attributes, providing a flexible way to manage user access to specific data."
"What is the purpose of the 'Schema Stitching' feature in AWS AppSync?","To combine multiple GraphQL APIs into a single API endpoint","To automatically generate GraphQL schemas from data sources","To validate the GraphQL schema","To convert a REST API into a GraphQL API","Schema Stitching enables you to combine multiple GraphQL APIs into a single, unified API endpoint, simplifying the management and consumption of multiple APIs."
"In AWS AppSync, what is the advantage of using the 'BatchInvoke' operation in a Lambda resolver?","To invoke multiple Lambda functions in parallel","To reduce the number of invocations for related data","To cache Lambda function responses","To simplify the Lambda function code","The 'BatchInvoke' operation in a Lambda resolver allows you to invoke a Lambda function multiple times with different arguments in a single request, reducing the overhead of multiple invocations and improving performance."
"Which of the following is a valid use case for AWS AppSync mutations?","Creating, updating, or deleting data in the data source","Retrieving data from the data source","Subscribing to real-time data updates","Caching data for faster access","AppSync mutations are used to create, update, or delete data in the data source, allowing clients to modify the data through the GraphQL API."
"What is the purpose of 'AWS Amplify' when used with AWS AppSync?","To simplify the development of client applications that consume the AppSync API","To automatically generate GraphQL schemas","To deploy the AppSync API","To monitor the performance of the AppSync API","AWS Amplify provides libraries and tools to simplify the development of client applications (web, mobile, etc.) that consume the AppSync API, including features for authentication, data access, and real-time updates."
"Which data source type in AWS AppSync requires you to write custom VTL mapping templates for both request and response?","HTTP API","DynamoDB","Lambda","None of the above","When using HTTP API as a data source in AppSync, you must write custom VTL mapping templates for both the request and the response to transform the data between GraphQL and the HTTP endpoint."
"What is the purpose of using IAM roles with AWS AppSync resolvers?","To grant AppSync permission to access the data source","To authenticate users accessing the API","To manage API Keys","To cache API responses","IAM roles are used to grant AppSync the necessary permissions to access the data source, such as DynamoDB or Lambda, ensuring that AppSync can securely interact with the data source on behalf of the API."
"How can you secure your AWS AppSync API against common web exploits?","By integrating with AWS WAF","By using IAM roles","By enabling caching","By configuring VPC endpoints","Integrating with AWS WAF allows you to protect your AppSync API against common web exploits, such as SQL injection and cross-site scripting (XSS), by using WAF's rule sets and custom rules."
"You need to transform the data returned by a DynamoDB query before it is sent to the client in AWS AppSync. Where do you perform this transformation?","In the response mapping template of the resolver","In the DynamoDB table itself","In a Lambda function triggered by DynamoDB Streams","In the request mapping template of the resolver","The transformation of data returned by a DynamoDB query is performed in the response mapping template of the resolver, allowing you to format the data as needed for the GraphQL API."
"Which of the following can be used to trigger AWS AppSync Subscriptions?","Mutations","Queries","Direct Data source events","API Key Rotation","AppSync Subscriptions are triggered by Mutations. When a mutation is executed, it can notify subscribed clients of the change, enabling real-time updates."
"What is the function of AWS AppSync's 'Lambda Authorizer'?","To implement custom authentication logic using a Lambda function","To automatically generate Lambda functions from a GraphQL schema","To monitor Lambda function performance","To cache Lambda function responses","Lambda Authorizers allow you to implement custom authentication logic using a Lambda function, providing flexibility to handle various authentication scenarios beyond the built-in methods."
"What is the use of Federated GraphQL with AWS AppSync?","To combine multiple GraphQL schemas from different sources into a single schema","To automatically scale AWS AppSync resources based on traffic","To implement fine-grained access control for GraphQL fields","To monitor the performance of GraphQL queries","Federated GraphQL allows you to combine multiple GraphQL schemas from different services or teams into a single, unified schema, simplifying API management and consumption."
"Which of the following is the main advantage of using GraphQL with AWS AppSync for mobile applications?","Reduced data transfer and improved performance","Simplified server-side logic","Automatic scaling of backend services","Enhanced security for mobile applications","GraphQL allows mobile applications to request only the data they need, reducing data transfer and improving performance, especially on limited bandwidth connections."
"You are building an AWS AppSync API that needs to integrate with an external API that requires specific headers. How do you add these headers?","In the request mapping template of the HTTP resolver","In the GraphQL schema definition","Using environment variables","By setting HTTP headers directly on the AppSync API","You can add custom headers to the request in the request mapping template of the HTTP resolver, allowing you to configure the request to match the requirements of the external API."
"In AWS AppSync, what is the primary purpose of a resolver?","To translate requests from GraphQL to a data source and back","To define the GraphQL schema","To manage user authentication","To monitor API usage","Resolvers connect fields in your GraphQL schema to data sources. They translate GraphQL requests into data source requests, and transform the data source responses back into the format specified in your schema."
"What is the function of the AWS AppSync GraphQL schema?","Defines the structure and type system of the API","Manages API keys and authentication","Configures the API caching strategy","Defines the data sources for the API","The GraphQL schema defines the data types, queries, mutations, and subscriptions available in the API, ensuring type safety and discoverability."
"In AWS AppSync, what is a data source?","The underlying system where your data is stored (e.g., DynamoDB, Lambda)","A user interface for interacting with the API","A set of predefined GraphQL queries","A logging service for API requests","A data source in AppSync represents the backend storage or compute service that holds the actual data for your API. This can be a database, a Lambda function, or another HTTP endpoint."
"Which of the following AWS services can be used as a data source for AWS AppSync?","DynamoDB","S3","CloudFront","CloudWatch","AppSync supports multiple data sources, including DynamoDB, Lambda, HTTP endpoints, and other AppSync APIs."
"In AWS AppSync, what is the purpose of a Pipeline Resolver?","To execute a series of operations in a specific order","To handle real-time subscriptions","To manage API quotas","To cache API responses","Pipeline Resolvers allow you to chain together multiple functions (called 'functions') to perform complex operations, enabling better modularity and code reuse."
"When configuring an AWS AppSync resolver, which of the following is a valid mapping template language?","Apache Velocity Template Language (VTL)","JSON","YAML","XML","AWS AppSync uses Apache Velocity Template Language (VTL) for mapping requests and responses between GraphQL and data sources.  While newer offerings allow direct Lambda resolvers, VTL is still commonly used."
"Which AWS AppSync feature enables real-time data updates via WebSockets?","Subscriptions","Queries","Mutations","Directives","AppSync Subscriptions allow clients to subscribe to specific events and receive real-time updates via WebSockets when those events occur."
"What type of authentication methods are supported by AWS AppSync?","API keys, IAM roles, Cognito user pools, OIDC","Basic authentication only","API keys only","IAM roles only","AppSync supports a variety of authentication methods, including API keys, IAM roles, Cognito user pools, and OpenID Connect (OIDC)."
"What is the purpose of the AWS AppSync API key?","To control access to the API","To encrypt data in transit","To enable caching","To monitor API performance","API keys are a simple way to control access to your AppSync API, particularly for public or client-side applications."
"How can you protect your AWS AppSync API from Distributed Denial of Service (DDoS) attacks?","By using AWS Shield","By enabling caching","By using API keys","By using IAM roles","AWS Shield provides DDoS protection for your AppSync API, mitigating common attack patterns."
"What is the benefit of using AWS Lambda as a data source for AWS AppSync?","Allows custom business logic to be executed","Reduces DynamoDB costs","Increases API security","Simplifies schema design","Lambda functions allow you to execute custom business logic in response to GraphQL requests, enabling complex operations and data transformations."
"How does AWS AppSync handle authorization?","Using IAM roles, Cognito user pools, or custom authorizers","Using API keys only","Using OAuth 2.0 only","Using IP address filtering","AppSync supports a range of authorization mechanisms, including IAM roles, Cognito user pools, and custom authorizers (Lambda functions) that allow you to implement fine-grained access control."
"What is the function of the 'request' mapping template in an AWS AppSync resolver?","Transforms the GraphQL request into a data source request","Transforms the data source response into a GraphQL response","Defines the GraphQL schema","Configures the caching behaviour","The 'request' mapping template takes the incoming GraphQL request and transforms it into a format that the data source can understand."
"What is the function of the 'response' mapping template in an AWS AppSync resolver?","Transforms the data source response into a GraphQL response","Transforms the GraphQL request into a data source request","Defines the GraphQL schema","Configures the caching behaviour","The 'response' mapping template takes the data source's response and transforms it into a format that the GraphQL client expects."
"Which feature of AWS AppSync helps improve API performance by reducing latency?","Caching","Subscriptions","Mutations","Directives","Caching allows you to store frequently accessed data, reducing the need to fetch it from the data source every time, thus improving performance and reducing latency."
"What is the purpose of AWS AppSync Direct Lambda Resolvers?","To directly connect Lambda functions to GraphQL fields without VTL","To run complex analytical queries","To manage user authentication","To monitor API usage","Direct Lambda resolvers simplify the integration of Lambda functions with AppSync by removing the need for VTL mapping templates."
"In AWS AppSync, what is the purpose of the 'typename' field?","To identify the GraphQL type of an object","To define the GraphQL schema","To manage user authentication","To monitor API usage","The `typename` field is automatically added to GraphQL objects and used by the client to determine the type of object being returned, which is important for features like caching and UI rendering."
"What is the role of AWS Cognito User Pools in AWS AppSync authentication?","To manage user registration, authentication, and authorisation","To store API keys","To provide DDoS protection","To cache API responses","Cognito User Pools provide a scalable and secure way to manage user identities, including registration, authentication, and authorization, integrating seamlessly with AppSync for user-based access control."
"Which AWS AppSync feature allows you to define custom logic for authorization decisions?","Custom Authorizers","API Keys","IAM Roles","Cognito User Pools","Custom Authorizers allow you to use Lambda functions to implement custom authorization logic, providing fine-grained control over API access based on specific business rules."
"What is the purpose of AWS AppSync's 'Conflicts' feature when using Delta Sync?","To manage data conflicts during offline mutations","To define the GraphQL schema","To manage user authentication","To monitor API usage","The 'Conflicts' feature is designed to handle data conflicts that can arise when performing offline mutations and synchronizing data back to the server, ensuring data consistency."
"Which AWS service can you integrate with AWS AppSync to provide user authentication and authorization?","AWS Cognito","AWS IAM","AWS CloudWatch","AWS CloudTrail","AWS Cognito is a common choice for user authentication and authorization within AWS, and it integrates directly with AWS AppSync."
"In AWS AppSync, what is a Mutation used for?","To modify data","To retrieve data","To subscribe to real-time updates","To cache data","Mutations are used to create, update, or delete data. They are the way you change data in your data sources through your API."
"What is the purpose of AWS AppSync's ability to implement 'Optimistic Updates' in client applications?","To provide a better user experience by updating the UI immediately before the server confirms the change","To reduce DynamoDB costs","To increase API security","To simplify schema design","Optimistic Updates improve the user experience by allowing the UI to update immediately, assuming the mutation will succeed on the server. If the mutation fails, the UI is reverted to its previous state."
"How can you monitor the performance of your AWS AppSync API?","Using AWS CloudWatch","Using AWS CloudTrail","Using AWS X-Ray","Using AWS Config","AWS CloudWatch provides metrics and logs that allow you to monitor the performance, health, and usage of your AppSync API."
"What type of data can you cache with AWS AppSync?","GraphQL Query results","API keys","IAM roles","Cognito User Pools","AppSync allows you to cache the results of GraphQL queries to improve performance and reduce latency."
"In AWS AppSync, what is the purpose of the '@aws_iam' directive?","To authorize access using IAM roles","To define the GraphQL schema","To manage user authentication","To monitor API usage","The `@aws_iam` directive allows you to use IAM roles to control access to specific fields in your GraphQL schema, enabling fine-grained access control based on IAM policies."
"How does AWS AppSync support offline access for mobile applications?","By using Delta Sync and local caching","By enabling caching only","By using API keys only","By using IAM roles only","Delta Sync and local caching allow mobile applications to continue to function even when the device is offline, synchronizing data when connectivity is restored."
"What is the primary advantage of using GraphQL with AWS AppSync over traditional REST APIs?","Clients can request only the data they need","GraphQL is faster than REST","GraphQL is more secure than REST","GraphQL is easier to learn than REST","GraphQL allows clients to specify exactly the data they need, reducing over-fetching and improving performance."
"What does the `aws-exports.js` file typically contain when working with AWS Amplify and AWS AppSync?","Configuration information for connecting to the AppSync API","AWS CLI credentials","IAM role definitions","CloudFormation template","The `aws-exports.js` file, often generated by Amplify, includes the API endpoint, authentication type, and other necessary configuration details for connecting your client application to the AppSync API."
"How can you enable logging for your AWS AppSync API requests?","By configuring CloudWatch Logs","By enabling caching","By using API keys","By using IAM roles","You can configure CloudWatch Logs to capture detailed logs of your API requests and responses, providing valuable insights for debugging and monitoring."
"What does the term 'fan-out' refer to in the context of AWS AppSync Subscriptions?","The ability to send real-time updates to multiple clients simultaneously","The process of caching API responses","The process of authenticating users","The process of resolving data sources","'Fan-out' refers to the ability of AppSync Subscriptions to efficiently distribute real-time updates to a large number of connected clients."
"Which AWS service can be used to store images and other binary files for use with an AWS AppSync API?","Amazon S3","Amazon DynamoDB","Amazon RDS","Amazon EC2","Amazon S3 is commonly used to store images and other binary files, and its URLs can be returned via AppSync resolvers."
"What is the function of the `context` object in an AWS AppSync resolver mapping template?","Provides access to information about the request, the source data, and the operation","Defines the GraphQL schema","Manages user authentication","Monitors API usage","The `context` object provides access to a wealth of information, including arguments passed to the GraphQL field, the identity of the caller, the source data for the field (in the case of a field on an object), and more."
"Which of the following is NOT a supported authentication type in AWS AppSync?","LDAP","API keys","IAM roles","Cognito user pools","LDAP is not directly supported as an authentication mechanism in AppSync. However, you can use a custom authoriser to integrate with an LDAP directory."
"What is the maximum number of resolvers that can be attached to a single GraphQL field in AWS AppSync?","One, or multiple in a Pipeline Resolver","Two","Three","Unlimited","A single field can have one resolver attached, however you can use a Pipeline Resolver to attach multiple functions which are then chained together to resolve a single field."
"What is the purpose of the AWS AppSync 'Delta Sync' feature?","To efficiently synchronise data between the cloud and client devices, only transferring the changes","To cache API responses","To manage user authentication","To monitor API usage","Delta Sync optimises data synchronisation by only transmitting the incremental changes between the client and the server, reducing bandwidth usage and improving performance for offline-capable applications."
"What is the maximum size of a single GraphQL query that can be processed by AWS AppSync?","The size limits are documented and depend on other settings","1 MB","10 MB","100 MB","AWS AppSync has documented size limits for queries, mutations and responses which can be discovered in the documentation."
"What is the purpose of the AWS AppSync Console?","To design, build, and test GraphQL APIs","To store data","To monitor network traffic","To manage EC2 instances","The AWS AppSync Console is a web-based interface for creating, configuring, and testing your GraphQL APIs."
"Which AWS service is commonly used with AWS AppSync to manage user authentication and authorization?","AWS Cognito","AWS IAM","AWS CloudWatch","AWS CloudTrail","AWS Cognito is a user identity and data synchronisation service and can be used for authentication and authorization in the context of using AWS AppSync."
"What does the term 'N+1 problem' refer to in the context of GraphQL and AWS AppSync?","A situation where a single GraphQL query results in many database queries","A common type of DDoS attack","A security vulnerability in GraphQL","An issue with caching in AWS AppSync","The 'N+1 problem' occurs when resolving a list of items requires additional queries for each item, leading to inefficient data fetching.  AppSync offers features like data loaders and batch resolvers to mitigate this."
"In AWS AppSync, what is a directive?","A way to add metadata to GraphQL schema elements","A tool for managing API keys","A method for implementing custom authorizers","A caching strategy for API responses","Directives are used to add metadata to schema elements like fields, types, and queries. They can be used to control access, validate data, or modify the query execution."
"When using DynamoDB as a data source for AWS AppSync, what is a common way to efficiently retrieve multiple items by their primary key?","Using a BatchGetItem operation","Using a Query operation with a filter","Scanning the entire table","Using a Global Secondary Index","BatchGetItem allows you to retrieve multiple items from a DynamoDB table in a single request, improving performance when fetching related data."
"What is the best approach to handle errors when resolving a GraphQL field with an AWS AppSync resolver?","Use the $util.error() function to return a custom error message","Ignore the error and return null","Throw an exception","Retry the request indefinitely","The `$util.error()` function allows you to create custom error messages that are returned to the client, providing informative feedback about failures."
"Which feature of AWS AppSync can be used to invoke another AppSync API as a data source?","HTTP Data Source","DynamoDB Data Source","Lambda Data Source","Subscription","AppSync provides the ability to configure an API as a data source for another AppSync API, enabling composition of GraphQL APIs. This is done using the HTTP Data Source."
"In AWS AppSync, how can you implement field-level security to control which users can access specific fields in your schema?","Using directives like @aws_auth or custom authorizers with IAM roles","Using API keys","Enabling caching","Using subscriptions","Directives like `@aws_auth` along with IAM roles and custom authorizers allow you to define fine-grained access control policies that determine which users or roles can access specific fields."
"What is the purpose of the 'Merge Type' setting in an AWS AppSync Pipeline Resolver?","To combine the results from multiple functions into a single result","To split a single function into multiple functions","To define the GraphQL schema","To manage user authentication","The 'Merge Type' setting in a Pipeline Resolver determines how the results from each function are combined to produce the final result. Common merge types include `Default` (returning the result of the last function) and `Auto` (merging objects)."
"What is the cost model for AWS AppSync?","Pay for only what you use","Flat monthly fee","Pay for provisioned capacity","Free tier only","AppSync uses a pay-as-you-go pricing model, charging based on the number of queries, data transfer, and caching usage."
