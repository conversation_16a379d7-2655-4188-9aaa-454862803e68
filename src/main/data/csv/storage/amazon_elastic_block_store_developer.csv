"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Elastic Block Store (EBS), which CLI command is used to create a new volume?","aws ec2 create-volume","aws ebs new-volume","aws ec2 init-volume","aws ebs start-volume","'aws ec2 create-volume' is the correct command to create a new EBS volume."
"Which IAM permission is required for a developer to attach an EBS volume to an EC2 instance?","ec2:AttachVolume","ec2:MountVolume","ec2:ConnectVolume","ec2:BindVolume","'ec2:AttachVolume' is required to attach an EBS volume to an EC2 instance."
"A developer needs to enable encryption at rest for an EBS volume. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","EBS uses AWS KMS to manage encryption keys for data at rest."
"Which EBS feature allows for automatic backups of volumes?","EBS Snapshots","EBS Replication","EBS Mirroring","EBS Lifecycle Policies","EBS Snapshots provide point-in-time backups of EBS volumes."
"A developer wants to restrict access to a specific EBS volume. What should be configured?","IAM policy with resource-level permissions","Security group rules only","VPC endpoint policy only","EBS lifecycle policy only","IAM policies can restrict access to specific EBS volumes using resource-level permissions."
"Which CLI command is used to list all EBS snapshots?","aws ec2 describe-snapshots","aws ebs list-snapshots","aws ec2 get-snapshots","aws ebs show-snapshots","'aws ec2 describe-snapshots' lists all EBS snapshots."
"A developer needs to monitor EBS volume performance. Which CloudWatch metric should they use?","VolumeReadOps","InstanceCPUUtilization","NetworkPacketsIn","VolumeAttachmentState","'VolumeReadOps' tracks read operations on an EBS volume."
"Which EBS feature allows for resizing a volume without downtime?","Elastic Volumes","EBS Mirroring","EBS Lifecycle Policies","EBS Replication","Elastic Volumes allow resizing and changing performance of EBS volumes without downtime."
"A developer wants to automate EBS snapshot creation as part of CI/CD. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate EBS snapshot creation."
"Which EBS feature provides immutable backups for compliance?","EBS Snapshots with AWS Backup Vault Lock","EBS Lifecycle Policies only","EBS Replication only","EBS Mirroring only","EBS Snapshots stored in AWS Backup Vault Lock provide immutable backups for compliance."
"A developer needs to restore an EBS volume from a snapshot using the CLI. Which command should they use?","aws ec2 create-volume --snapshot-id","aws ec2 restore-volume","aws ebs recover-volume","aws ec2 import-snapshot","'aws ec2 create-volume --snapshot-id' creates a new EBS volume from a snapshot."
"Which EBS feature allows for sharing snapshots with another AWS account?","Snapshot sharing with specific AWS account IDs","EBS volume replication","EBS lifecycle policy sharing","EBS volume peering","EBS snapshots can be shared with other AWS accounts by specifying their account IDs."
"A developer wants to automate EBS volume deletion after instance termination. What should be configured?","DeleteOnTermination flag on the volume attachment","EBS lifecycle policy only","Snapshot sharing only","Elastic Volumes only","Setting the DeleteOnTermination flag ensures the EBS volume is deleted when the instance is terminated."
"Which CloudWatch metric helps monitor EBS burst balance for gp2 volumes?","BurstBalance","VolumeQueueLength","VolumeWriteOps","VolumeIdleTime","'BurstBalance' shows the percentage of I/O credits remaining for burstable EBS volumes."
"A developer needs to ensure EBS snapshots are encrypted by default. What should be configured?","Enable default encryption in EBS settings","Enable S3 bucket encryption","Enable CloudTrail logging","Enable IAM authentication","Default encryption in EBS settings ensures all new snapshots are encrypted."
"Which IAM permission is required to delete an EBS snapshot?","ec2:DeleteSnapshot","ec2:RemoveSnapshot","ec2:DestroySnapshot","ec2:TerminateSnapshot","'ec2:DeleteSnapshot' is required to delete an EBS snapshot."
"A developer wants to monitor EBS volume attachment state changes. Which AWS service should be used?","CloudWatch Events (EventBridge)","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Events (EventBridge) can trigger actions based on EBS volume state changes."
"Which CLI command is used to modify the size of an existing EBS volume?","aws ec2 modify-volume","aws ec2 resize-volume","aws ebs update-volume","aws ec2 change-volume","'aws ec2 modify-volume' is used to change the size or type of an EBS volume."
"A developer needs to automate EBS snapshot cleanup based on age. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate snapshot cleanup based on age."
"Which EBS feature allows for fast snapshot restores to reduce recovery time?","EBS Fast Snapshot Restore","EBS Lifecycle Policies only","EBS Replication only","EBS Mirroring only","EBS Fast Snapshot Restore enables low-latency restores from snapshots."
"A developer needs to monitor EBS snapshot creation events for auditing. Which AWS service should be used?","AWS CloudTrail","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudTrail records EBS snapshot creation events for auditing and compliance."
"Which EBS feature allows for creating a new volume in a different Availability Zone from a snapshot?","Create volume from snapshot in target AZ","EBS volume migration","EBS cross-region replication only","EBS lifecycle policy only","You can create a new EBS volume in any AZ from a snapshot."
"A developer wants to automate resizing EBS volumes based on CloudWatch metrics. Which AWS service is best suited?","AWS Lambda with CloudWatch Alarms","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Alarms can automate EBS volume resizing."
"Which CLI command is used to delete an EBS volume?","aws ec2 delete-volume","aws ec2 remove-volume","aws ebs destroy-volume","aws ec2 terminate-volume","'aws ec2 delete-volume' deletes an EBS volume."
"A developer needs to ensure EBS volumes are only attached to specific EC2 instances. What should be configured?","IAM policy with instance-level conditions","Security group rules only","EBS lifecycle policy only","Snapshot sharing only","IAM policies can restrict EBS volume attachment to specific EC2 instances."
"Which EBS feature allows for monitoring volume status checks?","CloudWatch EBS StatusCheckFailed metric","EBS lifecycle policy only","EBS Replication only","EBS Mirroring only","CloudWatch provides the StatusCheckFailed metric for EBS volumes."
"A developer wants to automate EBS snapshot sharing with another account. Which AWS service is best suited?","AWS Lambda with EventBridge","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by EventBridge can automate snapshot sharing."
"Which IAM permission is required to create an EBS snapshot?","ec2:CreateSnapshot","ec2:MakeSnapshot","ec2:InitSnapshot","ec2:StartSnapshot","'ec2:CreateSnapshot' is required to create an EBS snapshot."
"A developer needs to monitor EBS volume detach events. Which AWS service should be used?","CloudWatch Events (EventBridge)","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Events (EventBridge) can monitor and trigger actions on EBS volume detach events."
"Which EBS feature allows for cost optimisation by moving snapshots to lower-cost storage?","EBS Snapshot Archive","EBS Lifecycle Policies only","EBS Replication only","EBS Mirroring only","EBS Snapshot Archive moves snapshots to lower-cost storage for cost optimisation."
"A developer needs to automate EBS volume tagging for cost allocation. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate EBS volume tagging for cost allocation."
"Which EBS feature allows for monitoring the time a volume spends idle?","VolumeIdleTime CloudWatch metric","VolumeReadOps only","EBS Lifecycle Policies only","EBS Replication only","The VolumeIdleTime metric measures the time an EBS volume is idle."
"A developer wants to restrict EBS snapshot sharing to specific AWS accounts. What should be configured?","Snapshot permissions with account IDs","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","Snapshot permissions can be set to allow sharing with specific AWS accounts."
"Which CLI command is used to enable encryption by default for EBS volumes?","aws ec2 enable-ebs-encryption-by-default","aws ec2 set-default-encryption","aws ebs enable-default-encryption","aws ec2 encryption-default","'aws ec2 enable-ebs-encryption-by-default' enables default encryption for EBS volumes."
"A developer needs to monitor EBS volume queue length for performance tuning. Which CloudWatch metric should they use?","VolumeQueueLength","VolumeReadOps","VolumeWriteOps","VolumeIdleTime","'VolumeQueueLength' tracks the number of pending I/O requests for an EBS volume."
"Which EBS feature allows for creating a snapshot policy based on tags?","Amazon Data Lifecycle Manager (DLM)","EBS Replication only","EBS Mirroring only","Elastic Volumes only","Amazon DLM enables snapshot policies based on resource tags."
"A developer wants to automate EBS volume resizing as part of a deployment pipeline. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates can automate EBS volume resizing during deployments."
"Which IAM permission is required to modify an EBS volume?","ec2:ModifyVolume","ec2:ResizeVolume","ec2:UpdateVolume","ec2:ChangeVolume","'ec2:ModifyVolume' is required to modify an EBS volume."
"A developer needs to ensure EBS snapshots are retained for a specific period. What should be configured?","Snapshot lifecycle policy with retention rules","EBS Replication only","Elastic Volumes only","Snapshot sharing only","Snapshot lifecycle policies can enforce retention periods for EBS snapshots."
"Which EBS feature allows for monitoring the number of IOPS delivered to a volume?","VolumeReadOps and VolumeWriteOps CloudWatch metrics","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","VolumeReadOps and VolumeWriteOps metrics track IOPS for EBS volumes."
"A developer needs to automate EBS snapshot deletion protection. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for EBS snapshots."
"Which EBS feature allows for restricting volume attachment to specific EC2 instance tags?","IAM policy with tag-based conditions","Security group rules only","EBS lifecycle policy only","Snapshot sharing only","IAM policies can restrict EBS volume attachment using tag-based conditions."
"A developer wants to monitor EBS volume throughput. Which CloudWatch metric should they use?","VolumeThroughputPercentage","VolumeIdleTime","VolumeQueueLength","VolumeAttachmentState","'VolumeThroughputPercentage' tracks the throughput of an EBS volume as a percentage of its provisioned limit."
"Which CLI command is used to copy an EBS snapshot to another region?","aws ec2 copy-snapshot","aws ec2 migrate-snapshot","aws ebs transfer-snapshot","aws ec2 move-snapshot","'aws ec2 copy-snapshot' copies an EBS snapshot to another region."
"A developer needs to ensure EBS volumes are encrypted with a specific KMS key. What should be configured?","Specify the KMS key ID during volume creation","Enable S3 bucket encryption","Enable CloudTrail logging","Enable IAM authentication","Specifying the KMS key ID during volume creation ensures encryption with the desired key."
"Which IAM permission is required to enable Fast Snapshot Restore for an EBS snapshot?","ec2:EnableFastSnapshotRestores","ec2:StartFastSnapshotRestore","ec2:ActivateFastSnapshotRestore","ec2:InitFastSnapshotRestore","'ec2:EnableFastSnapshotRestores' is required to enable Fast Snapshot Restore."
"A developer wants to automate EBS volume performance modification based on workload. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate EBS volume performance changes."
"Which EBS feature allows for monitoring the time a volume spends in a busy state?","VolumeTotalReadTime and VolumeTotalWriteTime CloudWatch metrics","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","VolumeTotalReadTime and VolumeTotalWriteTime measure busy time for EBS volumes."
"A developer needs to restrict EBS snapshot deletion to specific IAM roles. What should be configured?","IAM policy with role-level permissions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","IAM policies can restrict EBS snapshot deletion to specific IAM roles."
"Which EBS feature allows for monitoring the number of bytes read from a volume?","VolumeReadBytes CloudWatch metric","VolumeReadOps only","EBS Lifecycle Policies only","EBS Replication only","The VolumeReadBytes metric tracks the number of bytes read from an EBS volume."
"A developer wants to automate EBS volume snapshot creation based on instance state changes. Which AWS service is best suited?","AWS Lambda with EventBridge","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by EventBridge can automate EBS snapshot creation based on instance state changes."
"Which EBS feature allows for monitoring the number of bytes written to a volume?","VolumeWriteBytes CloudWatch metric","VolumeWriteOps only","EBS Lifecycle Policies only","EBS Replication only","The VolumeWriteBytes metric tracks the number of bytes written to an EBS volume."
"A developer needs to restrict EBS volume modification to specific IAM users. What should be configured?","IAM policy with user-level permissions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","IAM policies can restrict EBS volume modification to specific IAM users."
"Which CLI command is used to disable default encryption for EBS volumes?","aws ec2 disable-ebs-encryption-by-default","aws ec2 unset-default-encryption","aws ebs disable-default-encryption","aws ec2 encryption-disable","'aws ec2 disable-ebs-encryption-by-default' disables default encryption for EBS volumes."
"A developer wants to monitor EBS volume attachment latency. Which CloudWatch metric should they use?","VolumeAttachmentStateChangeTime","VolumeIdleTime","VolumeQueueLength","VolumeReadOps","'VolumeAttachmentStateChangeTime' tracks the time taken for EBS volume attachment state changes."
"Which EBS feature allows for restricting snapshot sharing to specific regions?","Snapshot copy with region restrictions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","Snapshot copy operations can restrict sharing to specific regions."
"A developer needs to automate EBS volume performance monitoring and alerting. Which AWS service is best suited?","CloudWatch Alarms with Lambda","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudWatch Alarms can trigger Lambda functions for automated monitoring and alerting."
"Which IAM permission is required to tag an EBS volume?","ec2:CreateTags","ec2:TagVolume","ec2:AddTag","ec2:UpdateTags","'ec2:CreateTags' is required to tag an EBS volume."
"A developer wants to ensure EBS snapshots are only accessible from specific VPCs. What should be configured?","VPC endpoint policies with resource restrictions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","VPC endpoint policies can restrict access to EBS snapshots from specific VPCs."
"Which EBS feature allows for monitoring the time a snapshot spends in a pending state?","SnapshotStateChangeTime CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","SnapshotStateChangeTime tracks the time a snapshot spends in a pending state."
"A developer needs to automate EBS snapshot lifecycle policy updates. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate lifecycle policy updates for EBS snapshots."
"Which EBS feature allows for restricting volume modification to specific tags?","IAM policy with tag-based conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume modification using tag-based conditions."
"A developer wants to monitor EBS snapshot copy operations. Which AWS service should be used?","CloudTrail event logging","CloudWatch Alarms only","AWS Glue only","Amazon Inspector only","CloudTrail logs snapshot copy operations for auditing and monitoring."
"Which CLI command is used to enable Fast Snapshot Restore for an EBS snapshot?","aws ec2 enable-fast-snapshot-restores","aws ec2 activate-fast-snapshot-restore","aws ebs enable-fast-restore","aws ec2 start-fast-snapshot-restore","'aws ec2 enable-fast-snapshot-restores' enables Fast Snapshot Restore for an EBS snapshot."
"A developer needs to restrict EBS volume deletion to specific IAM users. What should be configured?","IAM policy with user-level permissions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","IAM policies can restrict EBS volume deletion to specific IAM users."
"Which EBS feature allows for monitoring the number of volumes in a specific state?","VolumeStatus CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","The VolumeStatus metric tracks the number of volumes in each state."
"A developer wants to automate EBS snapshot archiving based on age. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate snapshot archiving based on age."
"Which IAM permission is required to archive an EBS snapshot?","ec2:ArchiveSnapshot","ec2:MoveSnapshotToArchive","ec2:ArchiveEbsSnapshot","ec2:SnapshotArchive","'ec2:ArchiveSnapshot' is required to archive an EBS snapshot."
"A developer needs to ensure EBS volumes are only created in specific subnets. What should be configured?","IAM policy with subnet-level conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume creation to specific subnets."
"Which EBS feature allows for monitoring the time a volume spends in a deleted state?","VolumeDeletedTime CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","VolumeDeletedTime tracks the time a volume spends in a deleted state."
"A developer wants to automate EBS volume deletion based on tag values. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate EBS volume deletion based on tag values."
"Which EBS feature allows for restricting snapshot archiving to specific IAM roles?","IAM policy with role-level permissions","EBS lifecycle policy only","Security group rules only","Elastic Volumes only","IAM policies can restrict EBS snapshot archiving to specific IAM roles."
"A developer needs to monitor EBS volume state transitions for compliance. Which AWS service should be used?","CloudTrail event logging","CloudWatch Alarms only","AWS Glue only","Amazon Inspector only","CloudTrail logs EBS volume state transitions for compliance."
"Which CLI command is used to list all archived EBS snapshots?","aws ec2 describe-snapshots --storage-tier archive","aws ec2 list-archived-snapshots","aws ebs show-archived-snapshots","aws ec2 get-archived-snapshots","'aws ec2 describe-snapshots --storage-tier archive' lists all archived EBS snapshots."
"A developer wants to restrict EBS volume creation to specific AWS accounts. What should be configured?","IAM policy with account-level conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume creation to specific AWS accounts."
"Which EBS feature allows for monitoring the number of archived snapshots?","ArchivedSnapshotsCount CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","ArchivedSnapshotsCount tracks the number of archived EBS snapshots."
"A developer needs to automate EBS volume state change notifications. Which AWS service is best suited?","Amazon SNS with EventBridge","AWS Glue","Amazon Inspector","AWS Data Pipeline","SNS with EventBridge can automate notifications for EBS volume state changes."
"Which IAM permission is required to restore an archived EBS snapshot?","ec2:RestoreSnapshotFromArchive","ec2:UnarchiveSnapshot","ec2:RecoverSnapshot","ec2:RestoreEbsSnapshot","'ec2:RestoreSnapshotFromArchive' is required to restore an archived EBS snapshot."
"A developer wants to ensure EBS volumes are only attached to instances in specific VPCs. What should be configured?","IAM policy with VPC-level conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume attachment to instances in specific VPCs."
"Which EBS feature allows for monitoring the time a snapshot spends in the archive tier?","SnapshotArchiveTime CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","SnapshotArchiveTime tracks the time a snapshot spends in the archive tier."
"A developer needs to automate EBS snapshot restore operations as part of a disaster recovery plan. Which AWS service is best suited?","AWS Lambda with EventBridge","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by EventBridge can automate EBS snapshot restore operations for disaster recovery."
"Which EBS feature allows for restricting volume restore operations to specific IAM users?","IAM policy with user-level permissions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume restore operations to specific IAM users."
"A developer wants to monitor EBS snapshot restore events for auditing. Which AWS service should be used?","CloudTrail event logging","CloudWatch Alarms only","AWS Glue only","Amazon Inspector only","CloudTrail logs EBS snapshot restore events for auditing."
"Which CLI command is used to restore an EBS snapshot to a new volume?","aws ec2 create-volume --snapshot-id","aws ec2 restore-snapshot","aws ebs recover-snapshot","aws ec2 import-snapshot","'aws ec2 create-volume --snapshot-id' restores a snapshot to a new EBS volume."
"A developer needs to ensure EBS volumes are only restored in specific Availability Zones. What should be configured?","IAM policy with AZ-level conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS volume restore operations to specific AZs."
"Which EBS feature allows for monitoring the number of restore operations performed?","RestoreOperationsCount CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","RestoreOperationsCount tracks the number of restore operations performed on EBS volumes."
"A developer wants to automate EBS snapshot restore tagging for compliance. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tagging of restored EBS volumes for compliance."
"Which IAM permission is required to tag a restored EBS volume?","ec2:CreateTags","ec2:TagVolume","ec2:AddTag","ec2:UpdateTags","'ec2:CreateTags' is required to tag a restored EBS volume."
"A developer needs to monitor EBS volume restore failures. Which CloudWatch metric should they use?","RestoreFailures","VolumeIdleTime","VolumeQueueLength","VolumeReadOps","'RestoreFailures' tracks the number of failed EBS volume restore operations."
"Which EBS feature allows for restricting restore operations to specific resource tags?","IAM policy with tag-based conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict restore operations to resources with specific tags."
"A developer needs to automate EBS restore operation compliance checks. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate compliance checks for EBS restore operations."
"Which EBS feature allows for restricting restore operation tagging to specific IAM roles?","IAM policy with role-level permissions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict tagging of restore operations to specific IAM roles."
"A developer wants to monitor EBS restore operation tagging events. Which AWS service should be used?","CloudTrail event logging","CloudWatch Alarms only","AWS Glue only","Amazon Inspector only","CloudTrail logs tagging events for EBS restore operations."
"Which CLI command is used to list all restore operations for EBS volumes?","aws ec2 describe-volumes-modifications","aws ec2 list-restore-operations","aws ebs show-restore-operations","aws ec2 get-restore-operations","'aws ec2 describe-volumes-modifications' lists all restore operations for EBS volumes."
"A developer needs to ensure EBS restore operations are only performed in specific subnets. What should be configured?","IAM policy with subnet-level conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict EBS restore operations to specific subnets."
"Which EBS feature allows for monitoring the time a restore operation takes to complete?","RestoreOperationTime CloudWatch metric","VolumeIdleTime only","EBS Lifecycle Policies only","EBS Replication only","RestoreOperationTime tracks the duration of restore operations for EBS volumes."
"A developer wants to automate EBS restore operation notifications for failures. Which AWS service is best suited?","Amazon SNS with EventBridge","AWS Glue","Amazon Inspector","AWS Data Pipeline","SNS with EventBridge can automate notifications for failed EBS restore operations."
"Which IAM permission is required to update a restore operation tag in EBS?","ec2:CreateTags","ec2:TagRestoreOperation","ec2:AddTag","ec2:UpdateTags","'ec2:CreateTags' is required to update a restore operation tag in EBS."
"A developer needs to monitor EBS restore operation failures for compliance. Which AWS service should be used?","CloudTrail event logging","CloudWatch RestoreFailures metric only","AWS Glue only","Amazon Inspector only","CloudTrail logs restore operation failures for compliance."
"Which EBS feature allows for restricting restore operation notifications to specific resource tags?","IAM policy with tag-based conditions","EBS lifecycle policy only","Security group rules only","Snapshot sharing only","IAM policies can restrict restore operation notifications to resources with specific tags."