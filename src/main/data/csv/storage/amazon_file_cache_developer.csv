"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon File Cache, what is the primary purpose of a cache?","To provide low-latency access to frequently accessed data","To provide durable storage for infrequently accessed data","To provide a backup solution for on-premises file systems","To provide a disaster recovery solution for cloud-based file systems","A cache is designed to provide low-latency access to frequently accessed data, improving application performance."
"Which AWS service does Amazon File Cache integrate with to provide a fully managed NFS file system?","Amazon FSx for NetApp ONTAP","Amazon S3","Amazon EBS","Amazon EFS","Amazon File Cache integrates with Amazon FSx for NetApp ONTAP to provide a fully managed NFS file system."
"What type of client is required to access an Amazon File Cache?","NFSv4.1 client","SMB client","iSCSI client","FTP client","Amazon File Cache requires an NFSv4.1 client to access the file system."
"What is the benefit of using Amazon File Cache with an on-premises NFS file server?","It reduces latency for users accessing data from on-premises","It eliminates the need for on-premises storage","It provides a fully managed file system in the cloud","It automatically backs up on-premises data to the cloud","Amazon File Cache reduces latency for users accessing data from on-premises NFS file servers by caching frequently accessed data in the cloud."
"Which of the following is a key consideration when choosing the size of an Amazon File Cache?","The amount of frequently accessed data","The total amount of data in the on-premises file system","The number of users accessing the file system","The network bandwidth between the cache and the on-premises file system","The size of the cache should be based on the amount of frequently accessed data to maximise performance benefits."
"How does Amazon File Cache ensure data consistency between the cache and the on-premises file server?","By using a write-through cache","By using a write-back cache","By using a periodic synchronisation process","By using a distributed locking mechanism","Amazon File Cache uses a write-through cache to ensure data consistency between the cache and the on-premises file server."
"What is the purpose of the 'CacheReadWait' CloudWatch metric for Amazon File Cache?","To measure the time applications wait to read data from the cache","To measure the time it takes to write data to the cache","To measure the time it takes to read data from the cache","To measure the time it takes to synchronise data between the cache and the on-premises file server","The 'CacheReadWait' metric measures the time applications wait to read data from the cache, indicating potential performance bottlenecks."
"Which IAM permission is required to create an Amazon File Cache?","fsx:CreateCache","filecache:CreateCache","storage:CreateCache","ec2:CreateCache","The `fsx:CreateCache` IAM permission is required to create an Amazon File Cache."
"A developer is troubleshooting slow application performance when accessing files through Amazon File Cache. Which of the following steps should they take first?","Check the 'CacheReadWait' CloudWatch metric","Increase the size of the cache","Migrate the application to a different AWS region","Upgrade the on-premises file server","Checking the 'CacheReadWait' CloudWatch metric is the first step to identify potential performance bottlenecks in the cache."
"What is the maximum cache throughput that can be configured for Amazon File Cache?","4 GB/s","1 GB/s","10 GB/s","2 GB/s","The maximum cache throughput that can be configured for Amazon File Cache is 4 GB/s."
"What is the purpose of the 'DataRepositoryAssociation' in Amazon File Cache?","To link the cache to the data repository (e.g., FSx for NetApp ONTAP)","To link the cache to an S3 bucket","To link the cache to an on-premises file server","To link the cache to a backup volume","The 'DataRepositoryAssociation' links the cache to the data repository, allowing it to access and cache data."
"Which of the following is a valid data repository type for Amazon File Cache?","FSx for NetApp ONTAP","Amazon S3","Amazon EBS","Amazon EFS","Amazon File Cache currently supports FSx for NetApp ONTAP as a data repository."
"What is the purpose of the 'AutomaticExportPolicy' in Amazon File Cache?","To automatically export the cache to NFS clients","To automatically export the cache to SMB clients","To automatically export the cache to S3 buckets","To automatically export the cache to other AWS services","The 'AutomaticExportPolicy' automatically exports the cache to NFS clients, simplifying access management."
"Which NFS version is supported by Amazon File Cache?","NFSv4.1","NFSv3","NFSv4.0","NFSv4.2","Amazon File Cache supports NFSv4.1."
"What is the maximum number of caches that can be associated with a single FSx for NetApp ONTAP volume?","1","5","10","Unlimited","Only one cache can be associated with a single FSx for NetApp ONTAP volume."
"A developer needs to monitor the amount of data being read from the cache. Which CloudWatch metric should they use?","BytesReadFromCache","BytesWrittenToCache","CacheHitRatio","CacheMissRatio","'BytesReadFromCache' measures the amount of data being read from the cache."
"What is the purpose of the 'CachePercentDirty' CloudWatch metric for Amazon File Cache?","To measure the percentage of data in the cache that has been modified but not yet written to the data repository","To measure the percentage of data in the cache that is invalid","To measure the percentage of data in the cache that is being actively accessed","To measure the percentage of data in the cache that is being evicted","'CachePercentDirty' measures the percentage of data in the cache that has been modified but not yet written to the data repository."
"Which of the following is a valid deployment type for Amazon File Cache?","SHARED","PRIVATE","PUBLIC","DEDICATED","The only valid deployment type for Amazon File Cache is SHARED."
"What is the maximum number of NFS clients that can connect to an Amazon File Cache?","Limited by the network bandwidth and cache resources","10","100","Unlimited","The maximum number of NFS clients that can connect to an Amazon File Cache is limited by the network bandwidth and cache resources."
"A developer wants to ensure that data written to the cache is immediately written to the data repository. Which cache type should they use?","Write-through cache","Write-back cache","Read-only cache","Invalidate cache","A write-through cache ensures that data written to the cache is immediately written to the data repository."
"What happens when a file requested by a client is not found in the Amazon File Cache?","The cache retrieves the file from the data repository","The cache returns an error to the client","The cache creates an empty file","The cache redirects the client to the data repository","The cache retrieves the file from the data repository when a file is not found in the cache."
"A developer is using Amazon File Cache to accelerate access to data stored in an FSx for NetApp ONTAP volume. What is the first step they should take to configure the cache?","Create a DataRepositoryAssociation","Create a CacheExportPolicy","Create a NetworkInterface","Create a SecurityGroup","The first step is to create a DataRepositoryAssociation to link the cache to the FSx for NetApp ONTAP volume."
"Which of the following is a valid value for the 'ExportedFromVolume' parameter in the DataRepositoryAssociation?","The junction path of the FSx for NetApp ONTAP volume","The ARN of the FSx for NetApp ONTAP volume","The IP address of the FSx for NetApp ONTAP volume","The DNS name of the FSx for NetApp ONTAP volume","The 'ExportedFromVolume' parameter should be set to the junction path of the FSx for NetApp ONTAP volume."
"A developer is using the AWS CLI to create an Amazon File Cache. Which command should they use?","aws fsx create-cache","aws filecache create-cache","aws storagegateway create-cache","aws ec2 create-cache","The `aws fsx create-cache` command is used to create an Amazon File Cache."
"Which parameter is used to specify the size of the Amazon File Cache when creating it using the AWS CLI?","--cache-capacity","--storage-capacity","--size","--volume-size","The `--cache-capacity` parameter is used to specify the size of the Amazon File Cache."
"A developer needs to update the throughput capacity of an existing Amazon File Cache. Which AWS CLI command should they use?","aws fsx update-cache","aws filecache update-cache","aws storagegateway create-cache","aws ec2 update-cache","The `aws fsx update-cache` command is used to update an existing Amazon File Cache."
"Which parameter is used to specify the new throughput capacity when updating an Amazon File Cache using the AWS CLI?","--throughput-capacity","--cache-throughput","--performance-mode","--iops","The `--throughput-capacity` parameter is used to specify the new throughput capacity."
"A developer wants to delete an Amazon File Cache. Which AWS CLI command should they use?","aws fsx delete-cache","aws filecache delete-cache","aws storagegateway delete-cache","aws ec2 delete-cache","The `aws fsx delete-cache` command is used to delete an Amazon File Cache."
"A developer is creating a DataRepositoryAssociation and needs to specify the security groups for the cache. Which parameter should they use?","--security-group-ids","--vpc-security-group-ids","--cache-security-groups","--network-security-groups","The `--security-group-ids` parameter is used to specify the security groups for the cache."
"What is the purpose of the 'FileSystemId' parameter when creating an Amazon File Cache?","To specify the ID of the FSx for NetApp ONTAP file system","To specify the ID of the S3 bucket","To specify the ID of the on-premises file server","To specify the ID of the backup volume","The 'FileSystemId' parameter is used to specify the ID of the FSx for NetApp ONTAP file system."
"A developer is using CloudWatch to monitor the performance of their Amazon File Cache. Which metric indicates the percentage of read requests that are served from the cache?","CacheHitRatio","CacheMissRatio","BytesReadFromCache","BytesWrittenToCache","The 'CacheHitRatio' metric indicates the percentage of read requests that are served from the cache."
"A developer is using CloudWatch to monitor the performance of their Amazon File Cache. Which metric indicates the percentage of read requests that are not served from the cache?","CacheMissRatio","CacheHitRatio","BytesReadFromCache","BytesWrittenToCache","The 'CacheMissRatio' metric indicates the percentage of read requests that are not served from the cache."
"What does a high 'CacheMissRatio' indicate?","The cache is not effectively caching frequently accessed data","The cache is effectively caching frequently accessed data","The network connection between the cache and the data repository is slow","The cache is running out of storage space","A high 'CacheMissRatio' indicates that the cache is not effectively caching frequently accessed data."
"A developer is using the AWS CLI to describe an Amazon File Cache. Which command should they use?","aws fsx describe-caches","aws filecache describe-caches","aws storagegateway describe-caches","aws ec2 describe-caches","The `aws fsx describe-caches` command is used to describe an Amazon File Cache."
"Which parameter can be used to filter the results when describing Amazon File Caches using the AWS CLI?","--cache-ids","--file-system-ids","--data-repository-association-ids","--all","The `--cache-ids` parameter can be used to filter the results when describing Amazon File Caches."
"A developer is using the AWS Management Console to create an Amazon File Cache. Which AWS service should they navigate to?","Amazon FSx","Amazon S3","Amazon EBS","Amazon EFS","The developer should navigate to the Amazon FSx service to create an Amazon File Cache."
"A developer is creating an Amazon File Cache and needs to specify the subnet in which the cache will be created. Which parameter should they use?","--subnet-ids","--vpc-subnet-ids","--cache-subnet-ids","--network-subnet-ids","The `--subnet-ids` parameter is used to specify the subnet in which the cache will be created."
"What is the purpose of the 'KmsKeyId' parameter when creating an Amazon File Cache?","To specify the KMS key used to encrypt the cache","To specify the KMS key used to encrypt the data repository","To specify the KMS key used to encrypt the network traffic","To specify the KMS key used to encrypt the backup volume","The 'KmsKeyId' parameter is used to specify the KMS key used to encrypt the cache."
"A developer is using the AWS CLI to list the DataRepositoryAssociations for an Amazon File Cache. Which command should they use?","aws fsx describe-data-repository-associations","aws filecache describe-data-repository-associations","aws storagegateway describe-data-repository-associations","aws ec2 describe-data-repository-associations","The `aws fsx describe-data-repository-associations` command is used to list the DataRepositoryAssociations."
"Which parameter can be used to filter the results when listing DataRepositoryAssociations using the AWS CLI?","--data-repository-association-ids","--file-system-ids","--cache-ids","--all","The `--data-repository-association-ids` parameter can be used to filter the results."
"A developer is using the AWS CLI to delete a DataRepositoryAssociation. Which command should they use?","aws fsx delete-data-repository-association","aws filecache delete-data-repository-association","aws storagegateway delete-data-repository-association","aws ec2 delete-data-repository-association","The `aws fsx delete-data-repository-association` command is used to delete a DataRepositoryAssociation."
"What is the purpose of the 'NFS_FILE_HANDLE' attribute when accessing files through Amazon File Cache?","To uniquely identify a file within the cache","To specify the NFS version","To specify the file permissions","To specify the file owner","The 'NFS_FILE_HANDLE' attribute uniquely identifies a file within the cache."
"A developer is using the 'NFS_FILE_HANDLE' attribute to access a file through Amazon File Cache. What data type is this attribute?","Binary","String","Integer","Boolean","The 'NFS_FILE_HANDLE' attribute is a binary data type."
"A developer is using Amazon File Cache to accelerate access to data for a machine learning workload. Which type of data is best suited for caching?", "Frequently accessed training data", "Infrequently accessed model data", "Log files", "Configuration files", "Frequently accessed training data is best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a video editing application. Which type of data is best suited for caching?", "Frequently accessed video files", "Infrequently accessed project files", "Audio files", "Image files", "Frequently accessed video files are best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a genomics application. Which type of data is best suited for caching?", "Frequently accessed sequence files", "Infrequently accessed analysis scripts", "Reference genomes", "Variant call format (VCF) files", "Frequently accessed sequence files are best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a financial modelling application. Which type of data is best suited for caching?", "Frequently accessed market data", "Infrequently accessed model code", "Configuration files", "Log files", "Frequently accessed market data is best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a rendering application. Which type of data is best suited for caching?", "Frequently accessed texture files", "Infrequently accessed scene files", "Audio files", "Image files", "Frequently accessed texture files are best suited for caching."
"What is the maximum number of DataRepositoryAssociations that can be created for an Amazon File Cache?", "Limited by the cache resources", "1", "5", "10", "The maximum number of DataRepositoryAssociations is limited by the cache resources."
"A developer is using the AWS CLI to create a DataRepositoryAssociation and needs to specify the path to the data repository. Which parameter should they use?", "--data-repository-path", "--file-system-path", "--cache-path", "--volume-path", "The `--data-repository-path` parameter is used to specify the path to the data repository."
"A developer is using the AWS CLI to create a DataRepositoryAssociation and needs to specify the type of data repository. Which parameter should they use?", "--data-repository-type", "--file-system-type", "--cache-type", "--volume-type", "The `--data-repository-type` parameter is used to specify the type of data repository."
"Which of the following is a valid value for the '--data-repository-type' parameter?", "ONTAP", "S3", "EBS", "EFS", "ONTAP is a valid value for the '--data-repository-type' parameter."
"A developer is using the AWS CLI to create a DataRepositoryAssociation and needs to specify the Amazon FSx for NetApp ONTAP file system ID. Which parameter should they use?", "--file-system-id", "--volume-id", "--cache-id", "--data-repository-id", "The `--file-system-id` parameter is used to specify the Amazon FSx for NetApp ONTAP file system ID."
"A developer is using the AWS CLI to create a DataRepositoryAssociation and needs to specify the export path on the Amazon FSx for NetApp ONTAP volume. Which parameter should they use?", "--exported-from-volume", "--export-path", "--volume-path", "--data-repository-path", "The `--exported-from-volume` parameter is used to specify the export path on the Amazon FSx for NetApp ONTAP volume."
"A developer is using the AWS CLI to create a DataRepositoryAssociation and wants to automatically export the cache to NFS clients. Which parameter should they use?", "--automatic-export-policy", "--export-policy", "--nfs-export", "--auto-export", "The `--automatic-export-policy` parameter is used to automatically export the cache to NFS clients."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and needs to specify the client configurations. Which parameter should they use?", "--client-configurations", "--nfs-clients", "--clients", "--allowed-clients", "The `--client-configurations` parameter is used to specify the client configurations."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and needs to specify the clients allowed to access the cache. Which parameter should they use within the client configuration?", "--clients-allowed", "--client", "--allowed-client", "--client-id", "The `--clients-allowed` parameter is used to specify the clients allowed to access the cache."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and needs to specify the access level for the clients. Which parameter should they use within the client configuration?", "--access-level", "--access", "--permission", "--client-access", "The `--access-level` parameter is used to specify the access level for the clients."
"Which of the following is a valid value for the '--access-level' parameter?", "Both READ_ONLY and READ_WRITE", "READ_ONLY", "READ_WRITE", "None of the above", "Both READ_ONLY and READ_WRITE are valid values for the '--access-level' parameter."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and needs to specify whether to require root access for the clients. Which parameter should they use within the client configuration?", "--require-root-access", "--root-access", "--root", "--require-root", "The `--require-root-access` parameter is used to specify whether to require root access for the clients."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and wants to allow all clients to access the cache with read-only access. What should they specify for the '--clients-allowed' parameter?", "0.0.0.0/0", "*", "ALL", "ANY", "They should specify 0.0.0.0/0 for the '--clients-allowed' parameter."
"A developer is using the AWS CLI to create an AutomaticExportPolicy and wants to require root access for all clients. What should they specify for the '--require-root-access' parameter?", "TRUE", "YES", "1", "REQUIRE", "They should specify TRUE for the '--require-root-access' parameter."
"A developer is using Amazon File Cache and wants to ensure that the cache is automatically updated when changes are made to the data repository. Which feature should they use?", "Data repository events", "Cache invalidation", "Automatic synchronisation", "Real-time replication", "Data repository events can be used to ensure that the cache is automatically updated."
"A developer is using Amazon File Cache and wants to monitor the number of files in the cache. Which CloudWatch metric should they use?", "There is no direct CloudWatch metric for the number of files in the cache.", "CacheFileCount", "CacheObjectCount", "CacheEntryCount", "There is no direct CloudWatch metric for the number of files in the cache."
"A developer is using Amazon File Cache and wants to monitor the network throughput between the cache and the data repository. Which CloudWatch metric should they use?", "There is no direct CloudWatch metric for network throughput between the cache and the data repository. You can use standard network metrics for the EC2 instance.", "DataRepositoryThroughput", "CacheNetworkThroughput", "NetworkThroughput", "There is no direct CloudWatch metric for network throughput between the cache and the data repository. You can use standard network metrics for the EC2 instance."
"A developer is using Amazon File Cache to accelerate access to data for a genomics application. Which type of data is best suited for caching?", "Frequently accessed sequence files", "Infrequently accessed analysis scripts", "Reference genomes", "Variant call format (VCF) files", "Frequently accessed sequence files are best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a financial modelling application. Which type of data is best suited for caching?", "Frequently accessed market data", "Infrequently accessed model code", "Configuration files", "Log files", "Frequently accessed market data is best suited for caching."
"A developer is using Amazon File Cache to accelerate access to data for a rendering application. Which type of data is best suited for caching?", "Frequently accessed texture files", "Infrequently accessed scene files", "Audio files", "Image files", "Frequently accessed texture files are best suited for caching."
"A developer is using Amazon File Cache and wants to implement a data lifecycle management strategy to automatically tier data to cheaper storage based on access patterns. Which AWS service can they use?", "Amazon File Cache does not directly support data lifecycle management policies. This needs to be implemented on the data repository.", "Amazon S3 Lifecycle policies", "Amazon FSx Lifecycle policies", "AWS Storage Gateway Lifecycle policies", "Amazon File Cache does not directly support data lifecycle management policies. This needs to be implemented on the data repository."
