"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"A developer needs to configure application consistent snapshots in Elastic Disaster Recovery. Which AWS service is required to achieve this?","AWS Systems Manager.","AWS CloudWatch.","AWS Config.","AWS CloudTrail.","AWS Systems Manager is required to configure application consistent snapshots in Elastic Disaster Recovery."
"What is the purpose of the 'Recovery Time Objective (RTO)' in AWS Elastic Disaster Recovery?","Defines the maximum acceptable downtime after a disaster.","Specifies the instance type for recovery instances.","Determines the target AWS Region for replication.","Configures the encryption settings for replicated data.","The RTO defines the maximum acceptable downtime, influencing the speed of the recovery process."
"A developer is setting up replication settings for Elastic Disaster Recovery. Which of the following settings determines how frequently data is replicated?","Replication interval.","Staging area size.","Recovery point objective (RPO).","Launch template.","The replication interval determines how frequently data is replicated from the source to the staging area."
"How does AWS Elastic Disaster Recovery ensure minimal impact on the production environment during replication?","By using a lightweight agent on the source servers.","By performing full backups of the source servers.","By suspending replication during peak hours.","By requiring a dedicated network connection.","Elastic Disaster Recovery uses a lightweight agent on the source servers to ensure minimal impact on the production environment during replication."
"A company wants to use AWS Elastic Disaster Recovery to protect a database server. Which of the following is a recommended strategy for ensuring data consistency during failover?","Using application consistent snapshots.","Performing a full database backup before failover.","Stopping the database server before failover.","Relying on eventual consistency.","Using application consistent snapshots ensures data consistency during failover for database servers."
"What is the role of the 'AWS Replication Agent' in Elastic Disaster Recovery?","To replicate data from the source servers to the staging area.","To manage the failover process.","To monitor the health of the recovery instances.","To configure the replication settings.","The AWS Replication Agent replicates data from the source servers to the staging area."
"A developer needs to configure Elastic Disaster Recovery to use a specific instance type for the recovery instances. How can they do this?","By specifying the instance type in the launch template.","By creating a custom AMI with the desired instance type.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","The instance type for the recovery instances can be specified in the launch template."
"Which of the following is a valid target environment for recovery instances launched by Elastic Disaster Recovery?","AWS EC2.","AWS Lambda.","Amazon ECS.","AWS Fargate.","AWS EC2 is a valid target environment for recovery instances launched by Elastic Disaster Recovery."
"A company is using AWS Elastic Disaster Recovery to protect a large number of EC2 instances. Which deployment option is most suitable for managing the replication process at scale?","Using AWS CloudFormation templates.","Manually configuring each instance in the AWS Management Console.","Using AWS CLI scripts.","Using AWS SDKs to automate the configuration.","AWS CloudFormation templates are ideal for managing and automating the deployment and configuration of Elastic Disaster Recovery at scale."
"A developer needs to ensure that the data replicated by AWS Elastic Disaster Recovery is encrypted both in transit and at rest. How can this be achieved?","By enabling encryption on the replication settings.","By using a custom AMI with encryption enabled.","By configuring a VPN connection between the source and target environments.","By using AWS KMS to manage encryption keys.","Encryption can be enabled directly in the replication settings to ensure data is encrypted both in transit and at rest."
"A developer is tasked with setting up network configurations for Elastic Disaster Recovery. Which of the following is a key consideration for ensuring successful failover?","Configuring VPC peering between the source and target VPCs.","Using a dedicated VPN connection.","Configuring a direct connect link.","Using a public IP address for all recovery instances.","Configuring VPC peering between the source and target VPCs is a key consideration for ensuring successful failover."
"A developer is tasked with configuring application consistent snapshots in Elastic Disaster Recovery. Which AWS service is required to achieve this?","AWS Systems Manager.","AWS CloudWatch.","AWS Config.","AWS CloudTrail.","AWS Systems Manager is required to configure application consistent snapshots in Elastic Disaster Recovery."
"What is the primary function of the 'launch template' in AWS Elastic Disaster Recovery?","To define the configuration of the recovery instances.","To specify the replication settings for the protected servers.","To automate the failback process.","To monitor the health of the replication process.","The launch template defines the configuration of the recovery instances, including instance type, security groups, and other settings."
"Which of the following is a key consideration when choosing the appropriate AMI for the recovery instances in Elastic Disaster Recovery?","Operating system.","Cost.","Storage capacity.","Network bandwidth.","Operating system is a key consideration when choosing the appropriate AMI for the recovery instances."
"Which of the following is a key consideration when choosing the appropriate AMI for the recovery instances in Elastic Disaster Recovery?","Operating system.","Cost.","Storage capacity.","Network bandwidth.","Operating system is a key consideration when choosing the appropriate AMI for the recovery instances."
"What is the purpose of the 'Recovery Time Objective (RTO)' in AWS Elastic Disaster Recovery?","Defines the maximum acceptable downtime after a disaster.","Specifies the instance type for recovery instances.","Determines the target AWS Region for replication.","Configures the encryption settings for replicated data.","The RTO defines the maximum acceptable downtime, influencing the speed of the recovery process."
"How does AWS Elastic Disaster Recovery handle the scenario where the source server's operating system is not supported by Elastic Disaster Recovery?","The source server cannot be protected by Elastic Disaster Recovery.","Elastic Disaster Recovery automatically converts the source server's operating system to a supported one.","Elastic Disaster Recovery uses a generic AMI that is compatible with all operating systems.","The developer must manually create a custom AMI that is compatible with Elastic Disaster Recovery.","The source server cannot be protected by Elastic Disaster Recovery."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the staging area. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that the staging area uses the desired key pair."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"A developer is using Elastic Disaster Recovery to protect a Linux application. What is the recommended approach for ensuring application consistency during failover?","Use a pre-failover script to quiesce the application.","Manually quiesce the application before failover.","Rely on eventual consistency.","Stop the application before failover.","Using a pre-failover script to quiesce the application is the recommended approach for ensuring application consistency during failover for Linux applications."
"Which of the following is a valid action that can be performed after a failover in Elastic Disaster Recovery?","Resize the recovery instances.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Resizing the recovery instances is a valid action that can be performed after a failover."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched in a different VPC than their production instances. Is this possible?","Yes, Elastic Disaster Recovery supports cross-VPC recovery.","No, Elastic Disaster Recovery does not support cross-VPC recovery.","Yes, but it requires a custom configuration.","Yes, but it requires AWS Support to enable it.","Yes, Elastic Disaster Recovery supports cross-VPC recovery."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require low latency?","Faster recovery times.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers faster recovery times, which is crucial for applications that require low latency."
"A developer needs to configure Elastic Disaster Recovery to use a specific subnet for the recovery instances. How can they do this?","By specifying the subnet in the recovery settings.","By creating a custom AMI with the desired subnet.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the subnet in the recovery settings to ensure that your recovery instances are launched in the desired subnet."
"Which of the following is a valid replication schedule in Elastic Disaster Recovery?","Continuous.","Daily.","Weekly.","Monthly.","Continuous replication is a valid replication schedule in Elastic Disaster Recovery."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their data is replicated to a different AWS region and availability zone. What is the maximum distance between the source and target regions?","There is no limit to the distance between regions.","100 miles.","500 miles.","1000 miles.","There is no limit to the distance between the source and target regions."
"A developer is using Elastic Disaster Recovery to protect a Windows application. What is the recommended approach for ensuring application consistency during failover?","Use Volume Shadow Copy Service (VSS).","Manually quiesce the application before failover.","Rely on eventual consistency.","Stop the application before failover.","Using Volume Shadow Copy Service (VSS) is the recommended approach for ensuring application consistency during failover for Windows applications."
"Which of the following is a valid action that can be performed during a recovery drill in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed during a recovery drill."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched with the same instance ID as their production instances. Is this possible?","No, Elastic Disaster Recovery does not support preserving instance IDs.","Yes, but it requires a custom configuration.","Yes, it is supported by default.","Yes, but it requires AWS Support to enable it.","No, Elastic Disaster Recovery does not support preserving instance IDs."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require low latency?","Faster recovery times.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers faster recovery times, which is crucial for applications that require high availability."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the recovery instances. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that your recovery instances are launched in the desired subnet."
"Which of the following is a valid replication setting in Elastic Disaster Recovery?","Data retention policy.","Instance type.","Availability Zone.","Security group.","Data retention policy is a valid replication setting in Elastic Disaster Recovery."
"A developer wants to ensure that their recovery instances are launched in a specific Availability Zone. How can they configure this in Elastic Disaster Recovery?","By specifying the Availability Zone in the recovery settings.","By creating a custom AMI with the desired Availability Zone.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the Availability Zone in the recovery settings to ensure that your recovery instances are launched in the desired location."
"How does Elastic Disaster Recovery ensure data security during replication?","By encrypting data in transit and at rest.","By using a private network connection.","By limiting access to the staging area.","By using multi-factor authentication.","Elastic Disaster Recovery encrypts data both in transit and at rest to ensure data security."
"A developer is using Elastic Disaster Recovery to protect a database server. They want to minimise the impact of the recovery process on their production environment. What is the recommended approach?","Use a staging area for recovery.","Perform a full backup before recovery.","Stop the production server during recovery.","Replicate data to a different region.","Using a staging area for recovery minimises the impact on the production environment."
"Which of the following is a valid action that can be performed before a failover in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed before a failover."
"A developer is using Elastic Disaster Recovery to protect an application that requires a specific AMI. How can they ensure that the recovery instances use the correct AMI?","By specifying the AMI in the recovery settings.","By creating a custom launch template.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the AMI in the recovery settings to ensure that your recovery instances use the correct AMI."
"Which of the following is a valid data source for Elastic Disaster Recovery?","Amazon EC2 instances.","AWS Lambda functions.","Amazon ECS containers.","AWS Fargate tasks.","Amazon EC2 instances are a valid data source for Elastic Disaster Recovery."
"A company is using Elastic Disaster Recovery to protect their applications. They want to automate the process of failing back to the primary region after a disaster is resolved. Which Elastic Disaster Recovery feature can they use?","Failback automation.","Recovery drill.","Continuous replication.","Staging area.","The Failback automation feature allows you to automatically return your application to the primary region once the disaster is resolved."
"Which of the following is a key consideration when configuring security groups for Elastic Disaster Recovery?","Ensuring that the recovery instances can communicate with the production environment.","Optimising storage costs.","Encrypting data in transit.","Managing user permissions.","Ensuring that the recovery instances can communicate with the production environment is crucial for a successful failover."
"A developer needs to test the failover process for their application in Elastic Disaster Recovery. What is the recommended approach?","Perform a recovery drill.","Simulate a disaster by shutting down production servers.","Rely on AWS support to test the plan.","Review the Elastic Disaster Recovery documentation.","Performing a recovery drill is the recommended approach to test the failover process."
"Which of the following is a valid use case for Elastic Disaster Recovery?","Protecting applications running in multiple AWS regions.","Archiving historical data.","Optimising storage costs.","Encrypting data at rest.","Elastic Disaster Recovery can be used to protect applications running in multiple AWS regions."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their data is replicated to a different AWS account. Is this possible?","No, Elastic Disaster Recovery does not support cross-account replication.","Yes, but it requires a custom configuration.","Yes, it is supported by default.","Yes, but it requires AWS Support to enable it.","No, Elastic Disaster Recovery does not support cross-account replication."
"Which of the following is a key benefit of using Elastic Disaster Recovery over traditional disaster recovery solutions?","Reduced complexity.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers reduced complexity compared to traditional disaster recovery solutions."
"A developer needs to configure Elastic Disaster Recovery to use a specific encryption key. How can they do this?","By specifying the encryption key in the replication settings.","By creating a custom AMI with the desired encryption key.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the encryption key in the replication settings to ensure that your data is encrypted with the desired key."
"Which of the following is a valid metric for monitoring the health of Elastic Disaster Recovery?","Replication lag.","CPU utilisation.","Memory utilisation.","Disk I/O.","Replication lag is a valid metric for monitoring the health of Elastic Disaster Recovery."
"A developer is using Elastic Disaster Recovery to protect a database server. They want to minimise the impact of the recovery process on their production environment. What is the recommended approach?","Use a staging area for recovery.","Perform a full backup before recovery.","Stop the production server during recovery.","Replicate data to a different region.","Using a staging area for recovery minimises the impact on the production environment."
"Which of the following is a valid action that can be performed before a failover in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed before a failover."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched with the same private IP addresses as their production instances. Is this possible?","No, Elastic Disaster Recovery does not support preserving private IP addresses.","Yes, but it requires a custom configuration.","Yes, it is supported by default.","Yes, but it requires AWS Support to enable it.","No, Elastic Disaster Recovery does not support preserving private IP addresses."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require high availability?","Automated failover.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers automated failover, which is crucial for applications that require high availability."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the recovery instances. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that your recovery instances use the desired subnet."
"Which of the following is a valid replication type in Elastic Disaster Recovery?","Asynchronous.","Synchronous.","Semi-synchronous.","Quorum-based.","Asynchronous replication is a valid replication type in Elastic Disaster Recovery."
"What is the purpose of the 'Recovery point objective (RPO)' setting in AWS Elastic Disaster Recovery?","Defines the maximum acceptable data loss in case of a disaster.","Specifies the instance type for recovery instances.","Determines the target AWS Region for replication.","Configures the encryption settings for replicated data.","The RPO setting defines the maximum acceptable data loss, influencing the frequency of data replication."
"A developer is tasked with setting up cross-account disaster recovery using AWS Elastic Disaster Recovery. What IAM role must be configured in the source account?","A role that allows Elastic Disaster Recovery to replicate data to the target account.","A role that grants full administrative access to the target account.","A role that allows read-only access to the source EC2 instances.","A role that enables CloudWatch monitoring in the source account.","The IAM role in the source account must allow Elastic Disaster Recovery to replicate data to the target account, ensuring secure data transfer."
"How does AWS Elastic Disaster Recovery handle licensing for commercial operating systems and applications during failover?","It requires you to bring your own licenses (BYOL).","It automatically provides temporary licenses for the duration of the failover.","It uses open-source alternatives during the failover process.","It suspends the licensing requirements during disaster recovery.","AWS Elastic Disaster Recovery requires you to bring your own licenses (BYOL) for commercial operating systems and applications."
"A company wants to use AWS Elastic Disaster Recovery to protect a large number of EC2 instances. Which deployment option is most suitable for managing the replication process at scale?","Using AWS CloudFormation templates.","Manually configuring each instance in the AWS Management Console.","Using AWS CLI scripts.","Using AWS SDKs to automate the configuration.","AWS CloudFormation templates are ideal for managing and automating the deployment and configuration of Elastic Disaster Recovery at scale."
"What is the primary function of the 'launch template' in AWS Elastic Disaster Recovery?","To define the configuration of the recovery instances.","To specify the replication settings for the protected servers.","To automate the failback process.","To monitor the health of the replication process.","The launch template defines the configuration of the recovery instances, including instance type, security groups, and other settings."
"A developer needs to ensure that the data replicated by AWS Elastic Disaster Recovery is encrypted both in transit and at rest. How can this be achieved?","By enabling encryption on the replication settings.","By using a custom AMI with encryption enabled.","By configuring a VPN connection between the source and target environments.","By using AWS KMS to manage encryption keys.","Encryption can be enabled directly in the replication settings to ensure data is encrypted both in transit and at rest."
"Which of the following actions is performed during a 'recovery drill' in AWS Elastic Disaster Recovery?","Testing the failover process without impacting the production environment.","Failing over the production environment to the recovery site.","Failing back the recovery environment to the production site.","Performing a full backup of the production environment.","A recovery drill tests the failover process without impacting the production environment, ensuring the disaster recovery plan works as expected."
"A developer is troubleshooting a replication issue with AWS Elastic Disaster Recovery. Which AWS service can be used to monitor the replication status and identify potential errors?","AWS CloudWatch.","AWS CloudTrail.","AWS Config.","AWS Trusted Advisor.","AWS CloudWatch can be used to monitor the replication status and identify potential errors, providing insights into the health of the disaster recovery process."
"What is the purpose of the 'staging area' in AWS Elastic Disaster Recovery?","To provide a temporary location for recovery instances before they are fully launched.","To store the replicated data before it is transferred to the target environment.","To serve as a buffer for network traffic during failover.","To host the management console for Elastic Disaster Recovery.","The staging area provides a temporary location for recovery instances before they are fully launched, minimizing the impact on the production environment."
"A company wants to minimise costs associated with AWS Elastic Disaster Recovery. Which strategy can help reduce the expenses?","Using smaller instance types for the staging area.","Replicating data more frequently.","Using more expensive storage options.","Enabling continuous data replication.","Using smaller instance types for the staging area can help reduce costs, as these instances are only used during testing or failover."
"Which of the following is a valid action that can be performed before a failover in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed before a failover."
"How does AWS Elastic Disaster Recovery handle the scenario where the source server's operating system is not supported by Elastic Disaster Recovery?","The source server cannot be protected by Elastic Disaster Recovery.","Elastic Disaster Recovery automatically converts the source server's operating system to a supported one.","Elastic Disaster Recovery uses a generic AMI that is compatible with all operating systems.","The developer must manually create a custom AMI that is compatible with Elastic Disaster Recovery.","The source server cannot be protected by Elastic Disaster Recovery."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the staging area. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that the staging area uses the desired key pair."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"A developer is using Elastic Disaster Recovery to protect a Linux application. What is the recommended approach for ensuring application consistency during failover?","Use a pre-failover script to quiesce the application.","Manually quiesce the application before failover.","Rely on eventual consistency.","Stop the application before failover.","Using a pre-failover script to quiesce the application is the recommended approach for ensuring application consistency during failover for Linux applications."
"Which of the following is a valid action that can be performed after a failover in Elastic Disaster Recovery?","Resize the recovery instances.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Resizing the recovery instances is a valid action that can be performed after a failover."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched in a different VPC than their production instances. Is this possible?","Yes, Elastic Disaster Recovery supports cross-VPC recovery.","No, Elastic Disaster Recovery does not support cross-VPC recovery.","Yes, but it requires a custom configuration.","Yes, but it requires AWS Support to enable it.","Yes, Elastic Disaster Recovery supports cross-VPC recovery."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require low latency?","Faster recovery times.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers faster recovery times, which is crucial for applications that require high availability."
"A developer needs to configure Elastic Disaster Recovery to use a specific subnet for the recovery instances. How can they do this?","By specifying the subnet in the recovery settings.","By creating a custom AMI with the desired subnet.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the subnet in the recovery settings to ensure that your recovery instances are launched in the desired subnet."
"Which of the following is a valid replication schedule in Elastic Disaster Recovery?","Continuous.","Daily.","Weekly.","Monthly.","Continuous replication is a valid replication schedule in Elastic Disaster Recovery."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their data is replicated to a different AWS region and availability zone. What is the maximum distance between the source and target regions?","There is no limit to the distance between regions.","100 miles.","500 miles.","1000 miles.","There is no limit to the distance between the source and target regions."
"A developer is using Elastic Disaster Recovery to protect a Windows application. What is the recommended approach for ensuring application consistency during failover?","Use Volume Shadow Copy Service (VSS).","Manually quiesce the application before failover.","Rely on eventual consistency.","Stop the application before failover.","Using Volume Shadow Copy Service (VSS) is the recommended approach for ensuring application consistency during failover for Windows applications."
"Which of the following is a valid action that can be performed during a recovery drill in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed during a recovery drill."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched with the same instance ID as their production instances. Is this possible?","No, Elastic Disaster Recovery does not support preserving instance IDs.","Yes, but it requires a custom configuration.","Yes, it is supported by default.","Yes, but it requires AWS Support to enable it.","No, Elastic Disaster Recovery does not support preserving instance IDs."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require low latency?","Faster recovery times.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers faster recovery times, which is crucial for applications that require high availability."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the recovery instances. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that your recovery instances are launched in the desired subnet."
"Which of the following is a valid replication setting in AWS Elastic Disaster Recovery?","Data retention policy.","Instance type.","Availability Zone.","Security group.","Data retention policy is a valid replication setting in Elastic Disaster Recovery."
"A developer wants to ensure that their recovery instances are launched in a specific Availability Zone. How can they configure this in Elastic Disaster Recovery?","By specifying the Availability Zone in the recovery settings.","By creating a custom AMI with the desired Availability Zone.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the Availability Zone in the recovery settings to ensure that your recovery instances are launched in the desired location."
"How does Elastic Disaster Recovery ensure data security during replication?","By encrypting data in transit and at rest.","By using a private network connection.","By limiting access to the staging area.","By using multi-factor authentication.","Elastic Disaster Recovery encrypts data both in transit and at rest to ensure data security."
"A developer is using Elastic Disaster Recovery to protect a database server. They want to minimise the impact of the recovery process on their production environment. What is the recommended approach?","Use a staging area for recovery.","Perform a full backup before recovery.","Stop the production server during recovery.","Replicate data to a different region.","Using a staging area for recovery minimises the impact on the production environment."
"Which of the following is a valid action that can be performed before a failover in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed before a failover."
"A developer is using Elastic Disaster Recovery to protect an application that requires a specific AMI. How can they ensure that the recovery instances use the correct AMI?","By specifying the AMI in the recovery settings.","By creating a custom launch template.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the AMI in the recovery settings to ensure that your recovery instances use the correct AMI."
"Which of the following is a valid data source for Elastic Disaster Recovery?","Amazon EC2 instances.","AWS Lambda functions.","Amazon ECS containers.","AWS Fargate tasks.","Amazon EC2 instances are a valid data source for Elastic Disaster Recovery."
"A company is using Elastic Disaster Recovery to protect their applications. They want to automate the process of failing back to the primary region after a disaster is resolved. Which Elastic Disaster Recovery feature can they use?","Failback automation.","Recovery drill.","Continuous replication.","Staging area.","The Failback automation feature allows you to automatically return your application to the primary region once the disaster is resolved."
"Which of the following is a key consideration when configuring security groups for Elastic Disaster Recovery?","Ensuring that the recovery instances can communicate with the production environment.","Optimising storage costs.","Encrypting data in transit.","Managing user permissions.","Ensuring that the recovery instances can communicate with the production environment is crucial for a successful failover."
"A developer needs to test the failover process for their application in Elastic Disaster Recovery. What is the recommended approach?","Perform a recovery drill.","Simulate a disaster by shutting down production servers.","Rely on AWS support to test the plan.","Review the Elastic Disaster Recovery documentation.","Performing a recovery drill is the recommended approach to test the failover process."
"Which of the following is a valid use case for Elastic Disaster Recovery?","Protecting applications running in multiple AWS regions.","Archiving historical data.","Optimising storage costs.","Encrypting data at rest.","Elastic Disaster Recovery can be used to protect applications running in multiple AWS regions."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their data is replicated to a different AWS account. Is this possible?","No, Elastic Disaster Recovery does not support cross-account replication.","Yes, but it requires a custom configuration.","Yes, it is supported by default.","Yes, but it requires AWS Support to enable it.","No, Elastic Disaster Recovery does not support cross-account replication."
"Which of the following is a key benefit of using Elastic Disaster Recovery over traditional disaster recovery solutions?","Reduced complexity.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers reduced complexity compared to traditional disaster recovery solutions."
"A developer needs to configure Elastic Disaster Recovery to use a specific encryption key. How can they do this?","By specifying the encryption key in the replication settings.","By creating a custom AMI with the desired encryption key.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the encryption key in the replication settings to ensure that your data is encrypted with the desired key."
"Which of the following is a valid metric for monitoring the health of Elastic Disaster Recovery?","Replication lag.","CPU utilisation.","Memory utilisation.","Disk I/O.","Replication lag is a valid metric for monitoring the health of Elastic Disaster Recovery."
"A developer is using Elastic Disaster Recovery to protect a database server. They want to minimise the impact of the recovery process on their production environment. What is the recommended approach?","Use a staging area for recovery.","Perform a full backup before recovery.","Stop the production server during recovery.","Replicate data to a different region.","Using a staging area for recovery minimises the impact on the production environment."
"Which of the following is a valid action that can be performed before a failover in Elastic Disaster Recovery?","Test the application functionality.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Testing the application functionality is a valid action that can be performed before a failover."
"How does AWS Elastic Disaster Recovery handle the scenario where the source server's operating system is not supported by Elastic Disaster Recovery?","The source server cannot be protected by Elastic Disaster Recovery.","Elastic Disaster Recovery automatically converts the source server's operating system to a supported one.","Elastic Disaster Recovery uses a generic AMI that is compatible with all operating systems.","The developer must manually create a custom AMI that is compatible with Elastic Disaster Recovery.","The source server cannot be protected by Elastic Disaster Recovery."
"A developer needs to configure Elastic Disaster Recovery to use a specific key pair for the staging area. How can they do this?","By specifying the key pair in the replication settings.","By creating a custom AMI with the desired key pair.","By configuring a VPC peering connection.","By using AWS CloudFormation to launch the instances.","You can specify the key pair in the replication settings to ensure that the staging area uses the desired key pair."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"Which of the following is a valid action to perform on a protected server in Elastic Disaster Recovery?","Change the instance type.","Change the security group.","Change the subnet.","Change the AMI.","All of the above are valid actions to perform on a protected server in Elastic Disaster Recovery."
"A developer is using Elastic Disaster Recovery to protect a Linux application. What is the recommended approach for ensuring application consistency during failover?","Use a pre-failover script to quiesce the application.","Manually quiesce the application before failover.","Rely on eventual consistency.","Stop the application before failover.","Using a pre-failover script to quiesce the application is the recommended approach for ensuring application consistency during failover for Linux applications."
"Which of the following is a valid action that can be performed after a failover in Elastic Disaster Recovery?","Resize the recovery instances.","Optimise storage costs.","Encrypt data in transit.","Manage user permissions.","Resizing the recovery instances is a valid action that can be performed after a failover."
"A company is using Elastic Disaster Recovery to protect their applications. They want to ensure that their recovery instances are launched in a different VPC than their production instances. Is this possible?","Yes, Elastic Disaster Recovery supports cross-VPC recovery.","No, Elastic Disaster Recovery does not support cross-VPC recovery.","Yes, but it requires a custom configuration.","Yes, but it requires AWS Support to enable it.","Yes, Elastic Disaster Recovery supports cross-VPC recovery."
"Which of the following is a key benefit of using Elastic Disaster Recovery for protecting applications that require low latency?","Faster recovery times.","Lower storage costs.","Simplified management.","Enhanced security.","Elastic Disaster Recovery offers faster recovery times, which is crucial for applications that require high availability."
