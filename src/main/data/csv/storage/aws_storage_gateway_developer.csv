"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is the primary function of the AWS Storage Gateway service?","To seamlessly connect on-premises applications to AWS cloud storage","To provide a fully managed NoSQL database service","To offer a serverless compute service","To manage and automate infrastructure deployments","AWS Storage Gateway enables hybrid cloud storage by connecting on-premises applications to AWS cloud storage services."
"Which AWS Storage Gateway type is best suited for providing low-latency access to frequently used files stored in Amazon S3?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway caches frequently accessed files on-premises, providing low-latency access."
"A developer needs to write data directly to Amazon S3 using AWS Storage Gateway. Which gateway type should they use?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway allows applications to store files as objects in S3, using NFS and SMB protocols."
"Which AWS Storage Gateway type is used for virtual tape storage in AWS?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway provides a virtual tape library (VTL) for backup and archival in AWS."
"A company wants to use their existing on-premises backup software to store backups in AWS. Which AWS Storage Gateway type should they implement?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway integrates with existing backup software, allowing backups to be stored in AWS."
"Which protocol does the File Gateway use to allow on-premises applications to access files stored in Amazon S3?","NFS and SMB","iSCSI","FTP","HTTP","File Gateway uses NFS and SMB protocols to provide file access to on-premises applications."
"A developer is using Volume Gateway in cached mode. Where is the primary copy of the data stored?","Amazon S3","On-premises storage","Amazon EBS","Amazon Glacier","In cached mode, the primary copy of the data is stored in Amazon S3."
"What is the benefit of using Volume Gateway in stored mode?","Low latency access to the entire dataset","Reduced storage costs","Automatic data replication","Simplified disaster recovery","Stored mode provides low latency access to the entire dataset, as it is stored on-premises."
"Which AWS service is commonly used with Tape Gateway for long-term archival of virtual tapes?","Amazon S3 Glacier Deep Archive","Amazon EBS","Amazon RDS","Amazon EC2","Tape Gateway uses Amazon S3 Glacier Deep Archive for cost-effective long-term archival."
"A developer needs to access block storage volumes in AWS from their on-premises application. Which AWS Storage Gateway type should they use?","Volume Gateway","File Gateway","Tape Gateway","Direct Connect Gateway","Volume Gateway provides block storage volumes in AWS that can be accessed from on-premises applications using iSCSI."
"What is the maximum number of virtual tapes that can be stored in a single Tape Gateway?","1500","500","100","2000","A single Tape Gateway can store up to 1500 virtual tapes."
"Which AWS Storage Gateway type supports creating snapshots of on-premises volumes for disaster recovery?","Volume Gateway","File Gateway","Tape Gateway","Direct Connect Gateway","Volume Gateway allows you to create snapshots of on-premises volumes, which can be used for disaster recovery."
"A developer wants to use AWS Storage Gateway to provide a local cache for frequently accessed data from Amazon S3. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway provides a local cache for frequently accessed data from Amazon S3."
"Which AWS Storage Gateway type allows you to replace physical tapes with virtual tapes in AWS?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway allows you to replace physical tapes with virtual tapes in AWS."
"A developer is using Volume Gateway in cached mode. What happens when the on-premises cache is full?","The least recently used data is evicted","The gateway stops accepting new data","The gateway crashes","The oldest data is automatically archived to S3 Glacier","When the on-premises cache is full, the least recently used data is evicted."
"Which AWS Storage Gateway type supports both iSCSI and SMB protocols?","Volume Gateway","File Gateway","Tape Gateway","Direct Connect Gateway","Volume Gateway supports the iSCSI protocol, while File Gateway supports SMB."
"A developer needs to ensure that data written to Volume Gateway is durably stored in AWS. Which storage tier is used for the primary copy of the data?","Amazon S3","Amazon EBS","Amazon Glacier","Amazon EFS","The primary copy of data written to Volume Gateway is durably stored in Amazon S3."
"Which AWS Storage Gateway type is suitable for applications that require low-latency access to block storage?","Volume Gateway","File Gateway","Tape Gateway","Direct Connect Gateway","Volume Gateway is suitable for applications that require low-latency access to block storage."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution with minimal changes to their existing on-premises infrastructure. Which gateway type is the easiest to integrate?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway is the easiest to integrate with existing on-premises infrastructure, as it uses standard file protocols."
"Which AWS Storage Gateway type is used for archiving data that is rarely accessed?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway is used for archiving data that is rarely accessed."
"What is the maximum size of a volume that can be created with Volume Gateway?","32 TiB","16 TiB","64 TiB","1 TiB","The maximum size of a volume that can be created with Volume Gateway is 32 TiB."
"Which AWS Storage Gateway type supports data compression to reduce storage costs?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports data compression to reduce storage costs."
"A developer wants to use AWS Storage Gateway to create a disaster recovery solution for their on-premises applications. Which gateway type should they use?","Volume Gateway","File Gateway","Tape Gateway","Direct Connect Gateway","Volume Gateway can be used to create a disaster recovery solution for on-premises applications."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) in AWS?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) in AWS."
"A developer is using File Gateway and wants to control access to the files stored in Amazon S3. Which AWS service can they use to manage access permissions?","AWS KMS","Amazon EBS","Amazon RDS","Amazon EC2","AWS KMS can be used to encrypt the data stored in Amazon S3."
"Which AWS Storage Gateway type supports storing data in Amazon S3 Glacier?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports storing data in Amazon S3 Glacier."
"A developer needs to migrate data from their on-premises storage to Amazon S3 using AWS Storage Gateway. Which gateway type should they use?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway can be used to migrate data from on-premises storage to Amazon S3."
"Which AWS Storage Gateway type supports creating a local cache for frequently accessed data?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway supports creating a local cache for frequently accessed data."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that is compatible with their existing on-premises applications. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway is compatible with existing on-premises applications, as it uses standard file protocols."
"Which AWS Storage Gateway type supports storing data in Amazon S3 Standard?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway supports storing data in Amazon S3 Standard."
"A developer needs to create a backup of their on-premises data to AWS using AWS Storage Gateway. Which gateway type should they use?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway can be used to create a backup of on-premises data to AWS."
"What is the maximum number of volumes that can be created per Volume Gateway?","32","16","64","1","The maximum number of volumes that can be created per Volume Gateway is 32."
"Which AWS Storage Gateway type supports creating a virtual tape shelf (VTS) in AWS?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape shelf (VTS) in AWS."
"A developer wants to use AWS Storage Gateway to create a cost-effective storage solution for their infrequently accessed data. Which gateway type should they use?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway can be used to create a cost-effective storage solution for infrequently accessed data."
"Which AWS Storage Gateway type supports storing data in Amazon S3 Glacier Deep Archive?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports storing data in Amazon S3 Glacier Deep Archive."
"A developer is using Volume Gateway in stored mode. What happens when the on-premises storage is full?","The gateway stops accepting new data","The least recently used data is evicted","The gateway crashes","The oldest data is automatically archived to S3 Glacier","When the on-premises storage is full, the gateway stops accepting new data."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that is compatible with their existing backup software?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that is compatible with their existing backup software."
"A developer needs to create a hybrid cloud storage solution that provides both low-latency access to frequently accessed data and cost-effective storage for infrequently accessed data. Which gateway types should they use?","File Gateway and Tape Gateway","Volume Gateway and File Gateway","Tape Gateway and Volume Gateway","Direct Connect Gateway and File Gateway","File Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both low-latency access to frequently accessed data and cost-effective storage for infrequently accessed data."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to archive data for compliance purposes?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to archive data for compliance purposes."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that is easy to manage and maintain. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway is easy to manage and maintain, as it uses standard file protocols."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to store data for long-term retention?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to store data for long-term retention."
"What is the maximum number of snapshots that can be stored per volume for Volume Gateway?","64","32","128","100","The maximum number of snapshots that can be stored per volume for Volume Gateway is 64."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to meet regulatory requirements?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to meet regulatory requirements."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that is both secure and compliant. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway can be used to create a hybrid cloud storage solution that is both secure and compliant."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to reduce the cost of long-term data storage?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to reduce the cost of long-term data storage."
"A developer is using File Gateway and wants to encrypt the data stored in Amazon S3. Which AWS service can they use to encrypt the data?","AWS KMS","Amazon EBS","Amazon RDS","Amazon EC2","AWS KMS can be used to encrypt the data stored in Amazon S3."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to simplify data management?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to simplify data management."
"A developer needs to create a hybrid cloud storage solution that provides both on-premises access to data and off-site backup. Which gateway types should they use?","Volume Gateway and Tape Gateway","File Gateway and Volume Gateway","Tape Gateway and File Gateway","Direct Connect Gateway and File Gateway","Volume Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both on-premises access to data and off-site backup."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to improve data availability?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to improve data availability."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that is both scalable and reliable. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway can be used to create a hybrid cloud storage solution that is both scalable and reliable."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to protect data from loss or corruption?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to protect data from loss or corruption."
"What is the maximum number of iSCSI connections that are supported per Volume Gateway?","32","16","64","100","The maximum number of iSCSI connections that are supported per Volume Gateway is 32."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to meet compliance requirements for data retention?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to meet compliance requirements for data retention."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that is both cost-effective and secure. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway can be used to create a hybrid cloud storage solution that is both cost-effective and secure."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to simplify data recovery?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to simplify data recovery."
"What is the maximum cache storage that can be configured for a File Gateway?","32 TiB","16 TiB","64 TiB","1 TiB","The maximum cache storage that can be configured for a File Gateway is 32 TiB."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to integrate with existing backup and recovery solutions?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to integrate with existing backup and recovery solutions."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that provides both on-premises access to data and off-site disaster recovery. Which gateway types should they use?","Volume Gateway and Tape Gateway","File Gateway and Volume Gateway","Tape Gateway and File Gateway","Direct Connect Gateway and File Gateway","Volume Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both on-premises access to data and off-site disaster recovery."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to automate data archival and retrieval?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to automate data archival and retrieval."
"What is the maximum number of gateways that can be deployed per AWS account?","Unlimited","10","50","100","There is no limit to the number of gateways that can be deployed per AWS account."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to reduce the risk of data loss?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to reduce the risk of data loss."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that provides both on-premises performance and cloud scalability. Which gateway type should they choose?","File Gateway","Volume Gateway","Tape Gateway","Direct Connect Gateway","File Gateway can be used to create a hybrid cloud storage solution that is both on-premises performance and cloud scalability."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to improve data security?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to improve data security."
"What is the maximum throughput that can be achieved with a File Gateway?","1 Gbps","500 Mbps","2 Gbps","10 Gbps","The maximum throughput that can be achieved with a File Gateway is 1 Gbps."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to simplify data compliance?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to simplify data compliance."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that provides both on-premises access to data and cloud-based data protection. Which gateway types should they use?","Volume Gateway and Tape Gateway","File Gateway and Volume Gateway","Tape Gateway and File Gateway","Direct Connect Gateway and File Gateway","Volume Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both on-premises access to data and cloud-based data protection."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to automate data lifecycle management?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to automate data lifecycle management."
"What is the maximum number of files that can be stored in a single File Gateway?","Unlimited","10,000","1,000,000","100,000","There is no limit to the number of files that can be stored in a single File Gateway."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to improve data durability?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to improve data durability."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that provides both on-premises performance and cloud-based backup. Which gateway types should they use?","Volume Gateway and Tape Gateway","File Gateway and Volume Gateway","Tape Gateway and File Gateway","Direct Connect Gateway and File Gateway","Volume Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both on-premises performance and cloud-based backup."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to simplify data archiving?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to simplify data archiving."
"What is the maximum number of concurrent operations that can be performed on a Volume Gateway?","128","64","256","32","The maximum number of concurrent operations that can be performed on a Volume Gateway is 128."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to meet data sovereignty requirements?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to meet data sovereignty requirements."
"A developer wants to use AWS Storage Gateway to create a hybrid cloud storage solution that provides both on-premises performance and cloud-based disaster recovery. Which gateway types should they use?","Volume Gateway and Tape Gateway","File Gateway and Volume Gateway","Tape Gateway and File Gateway","Direct Connect Gateway and File Gateway","Volume Gateway and Tape Gateway can be used to create a hybrid cloud storage solution that provides both on-premises performance and cloud-based disaster recovery."
"Which AWS Storage Gateway type supports creating a virtual tape library (VTL) that can be used to automate data replication?","Tape Gateway","Volume Gateway","File Gateway","Direct Connect Gateway","Tape Gateway supports creating a virtual tape library (VTL) that can be used to automate data replication."
