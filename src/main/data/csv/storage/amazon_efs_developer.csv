"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In Amazon Elastic File System (EFS), which CLI command is used to create a new file system?","aws efs create-file-system","aws efs new-filesystem","aws efs init-filesystem","aws efs start-filesystem","'aws efs create-file-system' is the correct command to create a new file system in Amazon EFS."
"Which IAM permission is required for a developer to mount an EFS file system?","elasticfilesystem:ClientMount","elasticfilesystem:MountTarget","elasticfilesystem:AttachVolume","elasticfilesystem:Connect","'elasticfilesystem:ClientMount' is required to mount an EFS file system."
"A developer needs to enable encryption at rest for an Amazon EFS file system. Which AWS service manages the encryption keys?","AWS Key Management Service (KMS)","AWS Secrets Manager","AWS Certificate Manager","AWS CloudHSM","Amazon EFS uses AWS KMS to manage encryption keys for data at rest."
"Which Amazon EFS feature allows for sharing file systems across multiple VPCs?","VPC peering with mount targets","Cross-region replication","File system federation","Direct Connect only","VPC peering with mount targets enables sharing EFS file systems across VPCs."
"A developer wants to restrict access to an EFS file system to specific EC2 instances. Which configuration should be used?","Security group rules and NFS client authorisation","Parameter group settings","File system policy only","IAM user group","Security group rules and NFS client authorisation restrict access to EFS file systems."
"Which CLI command is used to create a mount target for an EFS file system?","aws efs create-mount-target","aws efs add-mount-point","aws efs attach-mount","aws efs link-mount","'aws efs create-mount-target' creates a mount target for an EFS file system."
"A developer needs to monitor Amazon EFS throughput. Which CloudWatch metric should they use?","BurstCreditBalance","FileSystemSize","DataReadIOBytes","NetworkThroughput","'BurstCreditBalance' shows the available throughput credits for an EFS file system."
"Which Amazon EFS feature allows for automatic data tiering between storage classes?","Lifecycle management","Data set federation","File system mirroring","Mount target replication","Lifecycle management enables automatic data tiering between storage classes in Amazon EFS."
"A developer wants to connect to Amazon EFS using IAM authentication. What must be enabled on the file system?","IAM authentication for EFS","Kerberos authentication","LDAP integration","SAML federation","IAM authentication must be enabled to use IAM for connecting to Amazon EFS."
"Which Amazon EFS feature provides immutable storage for file system backups?","EFS Backup with AWS Backup","File system mirroring","Mount target replication","Lifecycle management only","EFS Backup with AWS Backup provides immutable storage for file system backups in Amazon EFS."
"A developer needs to automate Amazon EFS file system creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate file system provisioning as part of CI/CD pipelines for Amazon EFS."
"Which Amazon EFS feature allows for cross-region file system replication?","EFS Replication","Lifecycle management only","File system federation only","Mount target replication only","EFS Replication enables cross-region file system replication in Amazon EFS."
"A developer wants to monitor failed mount attempts in Amazon EFS. Which CloudWatch metric should be used?","ClientMountFailures","BurstCreditBalance","FileSystemSize","DataReadIOBytes","'ClientMountFailures' tracks failed mount attempts in Amazon EFS."
"Which CLI command is used to delete a file system in Amazon EFS?","aws efs delete-file-system","aws efs remove-filesystem","aws efs destroy-filesystem","aws efs drop-filesystem","'aws efs delete-file-system' deletes a file system in Amazon EFS."
"A developer needs to restrict Amazon EFS access to specific IAM roles. What should be configured?","IAM policies with role-level permissions","VPC endpoint policies only","File system policy only","Parameter group","IAM policies can restrict Amazon EFS access to specific IAM roles."
"Which Amazon EFS feature provides file system lifecycle policies?","Lifecycle management","File system mirroring only","Mount target replication only","EFS Replication only","Lifecycle management manages file system lifecycle policies in Amazon EFS."
"A developer wants to automate Amazon EFS deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Amazon EFS file systems."
"Which Amazon EFS feature allows for restricting access to specific mount targets?","IAM policy with mount target-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific mount targets in Amazon EFS."
"A developer needs to monitor Amazon EFS for excessive file system size. Which CloudWatch metric should be used?","FileSystemSize","BurstCreditBalance","ClientMountFailures","DataReadIOBytes","'FileSystemSize' shows the storage usage of a file system in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system tags?","IAM policy with tag-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon EFS file systems."
"A developer wants to automate Amazon EFS mount target creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate mount target provisioning as part of CI/CD pipelines for Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system policies?","IAM policy with policy-level permissions","VPC endpoint policy only","Mount target policy only","Parameter group","IAM policies can restrict access to specific file system policies in Amazon EFS."
"A developer needs to monitor Amazon EFS for excessive mount target creation. Which CloudWatch metric should be used?","MountTargetCount","FileSystemSize","BurstCreditBalance","ClientMountFailures","'MountTargetCount' tracks the number of mount targets in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system lifecycle policies?","IAM policy with lifecycle policy-level permissions","VPC endpoint policy only","Mount target policy only","Parameter group","IAM policies can restrict access to specific lifecycle policies in Amazon EFS."
"A developer wants to automate Amazon EFS file system tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for file systems in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target tags?","IAM policy with mount target tag-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon EFS mount targets."
"A developer needs to monitor Amazon EFS for excessive file system tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch FileSystemSize metric","ClientMountFailures metric","BurstCreditBalance metric","CloudTrail logs file system tag changes in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target endpoints?","VPC security group rules","Parameter group settings","File system policy only","IAM user group","Security group rules can restrict access to specific endpoints in Amazon EFS mount targets."
"A developer wants to automate Amazon EFS mount target tag management. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate tag management for mount targets in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target lifecycle policies?","IAM policy with mount target lifecycle policy-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific lifecycle policies in Amazon EFS mount targets."
"A developer needs to monitor Amazon EFS for excessive mount target tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch MountTargetCount metric","FileSystemSize metric","BurstCreditBalance metric","CloudTrail logs mount target tag changes in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target import endpoints?","VPC security group rules","Parameter group settings","File system policy only","IAM user group","Security group rules can restrict access to specific import endpoints in Amazon EFS mount targets."
"A developer wants to automate Amazon EFS mount target lifecycle policy updates. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate lifecycle policy updates for mount targets in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target import schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to import schedules by region in Amazon EFS mount targets."
"A developer needs to automate Amazon EFS file system import job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate import job provisioning as part of CI/CD pipelines for Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system import actions?","IAM policy with import action-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific import actions in Amazon EFS file systems."
"A developer wants to monitor Amazon EFS for excessive file system import jobs. Which CloudWatch metric should be used?","ImportJobCount","MountTargetCount","FileSystemSize","BurstCreditBalance","'ImportJobCount' tracks the number of import jobs in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system import formats?","IAM policy with import format-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific import formats in Amazon EFS file systems."
"A developer needs to ensure Amazon EFS is only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","File system policy only","Mount target policy only","Security group rules can restrict access to specific IP ranges in Amazon EFS."
"Which Amazon EFS feature allows for monitoring import job failures?","CloudWatch ImportFailure metric","MountTargetCount metric","FileSystemSize metric","BurstCreditBalance metric","The ImportFailure metric in CloudWatch tracks failed import jobs in Amazon EFS."
"A developer wants to automate Amazon EFS import retention policies. Which AWS service can help?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate import retention policies for file systems in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system import endpoints?","VPC security group rules","Parameter group settings","File system policy only","IAM user group","Security group rules can restrict access to specific import endpoints in Amazon EFS file systems."
"A developer needs to monitor Amazon EFS for excessive import job tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch ImportJobCount metric","MountTargetCount metric","FileSystemSize metric","CloudTrail logs import job tag changes in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific file system import schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to import schedules by region in Amazon EFS file systems."
"A developer needs to automate Amazon EFS mount target export job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate export job provisioning as part of CI/CD pipelines for Amazon EFS mount targets."
"Which Amazon EFS feature allows for restricting access to specific mount target export actions?","IAM policy with export action-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific export actions in Amazon EFS mount targets."
"A developer wants to monitor Amazon EFS for excessive export jobs. Which CloudWatch metric should be used?","ExportJobCount","ImportJobCount","MountTargetCount","FileSystemSize","'ExportJobCount' tracks the number of export jobs in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific mount target export formats?","IAM policy with export format-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific export formats in Amazon EFS mount targets."
"A developer needs to ensure Amazon EFS is only accessible from specific IP ranges for mount target exports. What should be configured?","Security group rules with IP restrictions","Parameter group settings","File system policy only","Mount target policy only","Security group rules can restrict access to specific IP ranges for mount target exports in Amazon EFS."
"Which Amazon EFS feature allows for monitoring mount target export job failures?","CloudWatch ExportFailure metric","ImportFailure metric","MountTargetCount metric","FileSystemSize metric","The ExportFailure metric in CloudWatch tracks failed export jobs in Amazon EFS mount targets."
"A developer needs to automate Amazon EFS export job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate export job provisioning as part of CI/CD pipelines for Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific export job actions?","IAM policy with export action-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific export actions in Amazon EFS."
"A developer wants to monitor Amazon EFS for excessive export job tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch ExportJobCount metric","MountTargetCount metric","FileSystemSize metric","CloudTrail logs export job tag changes in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific export job schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to export job schedules by region in Amazon EFS."
"A developer needs to automate Amazon EFS export job deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Amazon EFS export jobs."
"Which Amazon EFS feature allows for restricting access to specific export job formats?","IAM policy with export format-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific export formats in Amazon EFS."
"A developer wants to monitor Amazon EFS for excessive export job failures. Which CloudWatch metric should be used?","ExportFailure","ImportFailure","MountTargetCount","FileSystemSize","'ExportFailure' tracks the number of failed export jobs in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific export job tags?","IAM policy with tag-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon EFS export jobs."
"A developer needs to ensure Amazon EFS export jobs are only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","File system policy only","Export job policy only","Security group rules can restrict access to specific IP ranges for export jobs in Amazon EFS."
"Which Amazon EFS feature allows for monitoring export job failures?","CloudWatch ExportFailure metric","ImportFailure metric","MountTargetCount metric","FileSystemSize metric","The ExportFailure metric in CloudWatch tracks failed export jobs in Amazon EFS."
"A developer needs to automate Amazon EFS backup scheduling. Which AWS service is best suited?","AWS Backup","AWS Glue","Amazon Inspector","AWS Data Pipeline","AWS Backup can automate backup scheduling for Amazon EFS file systems."
"Which Amazon EFS feature allows for restricting access to specific backup jobs?","IAM policy with backup job-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific backup jobs in Amazon EFS."
"A developer wants to monitor Amazon EFS for backup job failures. Which CloudWatch metric should be used?","BackupFailure","ExportFailure","ImportFailure","MountTargetCount","'BackupFailure' tracks the number of failed backup jobs in Amazon EFS."
"Which CLI command is used to describe an EFS file system's backup policy?","aws efs describe-backup-policy","aws efs get-backup-policy","aws efs show-backup-policy","aws efs list-backup-policy","'aws efs describe-backup-policy' retrieves the backup policy for an EFS file system."
"A developer needs to restrict Amazon EFS backup access to specific IAM users. What should be configured?","IAM policies with user-level permissions","VPC endpoint policies only","File system policy only","Parameter group","IAM policies can restrict Amazon EFS backup access to specific IAM users."
"Which Amazon EFS feature provides backup job lifecycle management?","AWS Backup lifecycle policies","File system mirroring only","Mount target replication only","EFS Replication only","AWS Backup lifecycle policies manage backup job lifecycle in Amazon EFS."
"A developer wants to automate Amazon EFS backup deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Amazon EFS backups."
"Which Amazon EFS feature allows for restricting access to specific backup job tags?","IAM policy with tag-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon EFS backup jobs."
"A developer needs to ensure Amazon EFS backups are only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","File system policy only","Backup job policy only","Security group rules can restrict access to specific IP ranges for backups in Amazon EFS."
"Which Amazon EFS feature allows for monitoring backup job failures?","CloudWatch BackupFailure metric","ExportFailure metric","ImportFailure metric","MountTargetCount metric","The BackupFailure metric in CloudWatch tracks failed backup jobs in Amazon EFS."
"A developer needs to automate Amazon EFS backup job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate backup job provisioning as part of CI/CD pipelines for Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific backup job actions?","IAM policy with backup action-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific backup actions in Amazon EFS."
"A developer wants to monitor Amazon EFS for excessive backup job tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch BackupFailure metric","ExportFailure metric","ImportFailure metric","CloudTrail logs backup job tag changes in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific backup job schedules by region?","IAM policy with region-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to backup job schedules by region in Amazon EFS."
"A developer needs to automate Amazon EFS backup job deletion protection. Which AWS service can help?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for Amazon EFS backup jobs."
"Which Amazon EFS feature allows for restricting access to specific backup job formats?","IAM policy with backup format-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific backup formats in Amazon EFS."
"A developer wants to monitor Amazon EFS for excessive backup job failures. Which CloudWatch metric should be used?","BackupFailure","ExportFailure","ImportFailure","MountTargetCount","'BackupFailure' tracks the number of failed backup jobs in Amazon EFS."
"Which Amazon EFS feature allows for restricting access to specific backup job tags?","IAM policy with tag-level permissions","VPC endpoint policy only","File system policy only","Parameter group","IAM policies can restrict access to specific tags in Amazon EFS backup jobs."
"A developer needs to ensure Amazon EFS backup jobs are only accessible from specific IP ranges. What should be configured?","Security group rules with IP restrictions","Parameter group settings","File system policy only","Backup job policy only","Security group rules can restrict access to specific IP ranges for backup jobs in Amazon EFS."
"Which Amazon EFS feature allows for monitoring backup job failures?","CloudWatch BackupFailure metric","ExportFailure metric","ImportFailure metric","MountTargetCount metric","The BackupFailure metric in CloudWatch tracks failed backup jobs in Amazon EFS."
