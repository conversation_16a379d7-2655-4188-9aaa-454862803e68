"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"In AWS Backup, which CLI command is used to create a backup plan?","aws backup create-backup-plan","aws backup new-plan","aws backup start-backup-plan","aws backup init-plan","'aws backup create-backup-plan' is the correct command to create a backup plan in AWS Backup."
"Which IAM permission is required for a developer to start a backup job in AWS Backup?","backup:StartBackupJob","backup:CreateBackupVault","backup:PutBackupSelection","backup:RestoreBackupJob","'backup:StartBackupJob' is required to start a backup job in AWS Backup."
"A developer needs to enable cross-region backup in AWS Backup. Which feature should be used?","Backup vault replication","Lifecycle management","Backup plan tagging","Backup job scheduling","Backup vault replication enables cross-region backup in AWS Backup."
"Which AWS Backup feature allows for automatic deletion of backups after a specified period?","Lifecycle policies","Backup vault access policies","Backup job scheduling","Backup plan tagging","Lifecycle policies allow automatic deletion of backups after a specified retention period."
"A developer wants to restrict access to a backup vault to specific IAM roles. What should be configured?","Backup vault access policy","Backup plan policy","Resource-based policy","Lifecycle policy","Backup vault access policies restrict access to specific IAM roles."
"Which CLI command is used to list all backup jobs in AWS Backup?","aws backup list-backup-jobs","aws backup describe-backup-jobs","aws backup get-backup-jobs","aws backup show-backup-jobs","'aws backup list-backup-jobs' lists all backup jobs in AWS Backup."
"A developer needs to monitor AWS Backup job failures. Which CloudWatch metric should be used?","BackupJobFailed","BackupVaultSize","BackupPlanCount","BackupSelectionCount","'BackupJobFailed' tracks failed backup jobs in AWS Backup."
"Which AWS Backup feature allows for resource-level permissions?","IAM policies with resource ARNs","Backup vault access policies only","Lifecycle policies only","Backup plan tags only","IAM policies with resource ARNs allow resource-level permissions in AWS Backup."
"A developer wants to automate backup job creation as part of CI/CD. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates automate backup job provisioning as part of CI/CD pipelines for AWS Backup."
"Which AWS Backup feature provides immutable storage for backups?","Backup Vault Lock","Lifecycle management only","Backup plan tagging only","Backup job scheduling only","Backup Vault Lock provides immutable storage for backups in AWS Backup."
"A developer needs to restore a resource from a backup in AWS Backup. Which CLI command should they use?","aws backup start-restore-job","aws backup restore-resource","aws backup recover-job","aws backup restore-backup-job","'aws backup start-restore-job' is the correct command to initiate a restore operation in AWS Backup."
"Which AWS Backup feature allows for scheduled backup jobs?","Backup plans with scheduled rules","Backup vault lifecycle policies","Backup job tagging","Backup vault replication","Backup plans with scheduled rules enable automated, scheduled backup jobs in AWS Backup."
"A developer wants to monitor backup vault storage usage. Which CloudWatch metric should they use?","BackupVaultSize","BackupJobFailed","BackupPlanCount","BackupSelectionCount","'BackupVaultSize' tracks the total storage used by a backup vault in AWS Backup."
"Which IAM permission is required to delete a backup vault in AWS Backup?","backup:DeleteBackupVault","backup:RemoveBackupVault","backup:DestroyBackupVault","backup:TerminateBackupVault","'backup:DeleteBackupVault' is required to delete a backup vault in AWS Backup."
"A developer needs to ensure backups are encrypted at rest in AWS Backup. Which configuration is required?","Enable encryption with a KMS key","Enable S3 bucket versioning","Enable CloudTrail logging","Enable IAM authentication","AWS Backup uses KMS keys to encrypt backups at rest."
"Which AWS Backup feature allows for tagging backups for cost allocation?","Resource tags on backup jobs","Lifecycle policies only","Backup vault access policies only","Backup plan rules only","Resource tags on backup jobs enable cost allocation and tracking in AWS Backup."
"A developer wants to restrict backup job creation to a specific resource type. What should be configured?","IAM policy with resource type condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict backup job creation to specific resource types using conditions."
"Which CLI command is used to describe a backup vault in AWS Backup?","aws backup describe-backup-vault","aws backup get-backup-vault","aws backup show-backup-vault","aws backup list-backup-vault","'aws backup describe-backup-vault' provides details about a backup vault in AWS Backup."
"A developer needs to automate backup plan updates. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates can automate updates to backup plans in AWS Backup."
"Which AWS Backup feature allows for cross-account backup sharing?","Backup vault access policy with cross-account permissions","Lifecycle policy only","Backup plan tag only","Backup job scheduling only","Backup vault access policies can be configured to allow cross-account backup sharing in AWS Backup."
"A developer needs to monitor AWS Backup restore job failures. Which CloudWatch metric should be used?","RestoreJobFailed","BackupJobFailed","BackupVaultSize","BackupPlanCount","'RestoreJobFailed' tracks failed restore jobs in AWS Backup."
"Which AWS Backup feature allows for backup job notifications?","SNS topic integration","S3 event notifications","Lambda triggers only","CloudWatch Logs only","SNS topic integration enables backup job notifications in AWS Backup."
"A developer wants to restrict restore job actions to specific users. What should be configured?","IAM policy with action-level permissions","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore job actions to specific users in AWS Backup."
"Which CLI command is used to delete a backup plan in AWS Backup?","aws backup delete-backup-plan","aws backup remove-backup-plan","aws backup destroy-backup-plan","aws backup terminate-backup-plan","'aws backup delete-backup-plan' deletes a backup plan in AWS Backup."
"A developer needs to ensure backup jobs are only run during non-peak hours. What should be configured?","Scheduled rules in backup plans","Lifecycle policy only","Backup vault access policy only","Resource tag only","Scheduled rules in backup plans allow backup jobs to run during specific time windows."
"Which AWS Backup feature allows for backup job monitoring and alerting?","CloudWatch Alarms integration","S3 event notifications only","Lambda triggers only","Backup plan tags only","CloudWatch Alarms integration enables monitoring and alerting for backup jobs in AWS Backup."
"A developer wants to automate backup vault policy updates. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation templates can automate backup vault policy updates in AWS Backup."
"Which IAM permission is required to list backup vaults in AWS Backup?","backup:ListBackupVaults","backup:DescribeBackupVault","backup:GetBackupVault","backup:ShowBackupVaults","'backup:ListBackupVaults' is required to list backup vaults in AWS Backup."
"A developer needs to ensure backup data is retained for compliance. Which AWS Backup feature should be used?","Lifecycle policies with retention rules","Backup plan tags only","Backup vault access policy only","Resource tag only","Lifecycle policies with retention rules ensure backup data is retained for compliance in AWS Backup."
"Which AWS Backup feature allows for backup job tagging for automation?","Resource tags on backup jobs","Lifecycle policies only","Backup vault access policies only","Backup plan rules only","Resource tags on backup jobs enable automation and tracking in AWS Backup."
"A developer needs to automate backup job notifications for failed jobs. Which AWS service is best suited?","Amazon SNS with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Amazon SNS with CloudWatch Events can automate notifications for failed backup jobs in AWS Backup."
"Which AWS Backup feature allows for backup job selection based on resource tags?","Tag-based backup selection","Lifecycle policy only","Backup vault access policy only","Backup plan rule only","Tag-based backup selection enables backup jobs to be triggered based on resource tags."
"A developer wants to restrict backup vault deletion to specific users. What should be configured?","IAM policy with user-level permissions","Backup plan tag only","Lifecycle policy only","Backup job tag only","IAM policies can restrict backup vault deletion to specific users in AWS Backup."
"Which CLI command is used to list all backup plans in AWS Backup?","aws backup list-backup-plans","aws backup describe-backup-plans","aws backup get-backup-plans","aws backup show-backup-plans","'aws backup list-backup-plans' lists all backup plans in AWS Backup."
"A developer needs to ensure backup jobs are only created for resources in a specific VPC. What should be configured?","IAM policy with VPC condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict backup job creation to resources in a specific VPC using conditions."
"Which AWS Backup feature allows for backup job retention beyond the default period?","Custom lifecycle policies","Backup plan tags only","Backup vault access policy only","Resource tag only","Custom lifecycle policies allow retention of backup jobs beyond the default period."
"A developer wants to automate backup job tagging for cost allocation. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate backup job tagging for cost allocation."
"Which IAM permission is required to update a backup plan in AWS Backup?","backup:UpdateBackupPlan","backup:ModifyBackupPlan","backup:EditBackupPlan","backup:ChangeBackupPlan","'backup:UpdateBackupPlan' is required to update a backup plan in AWS Backup."
"A developer needs to monitor AWS Backup for excessive backup job creation. Which CloudWatch metric should be used?","BackupJobCreated","BackupVaultSize","BackupPlanCount","BackupSelectionCount","'BackupJobCreated' tracks the number of backup jobs created in AWS Backup."
"Which AWS Backup feature allows for backup job selection based on resource type?","Resource type-based backup selection","Lifecycle policy only","Backup vault access policy only","Backup plan tag only","Resource type-based backup selection enables backup jobs to be triggered for specific resource types in AWS Backup."
"A developer needs to automate backup plan deletion protection. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for AWS Backup plans."
"Which AWS Backup feature allows for restricting backup job actions to specific users?","IAM policy with action-level permissions","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict backup job actions to specific users in AWS Backup."
"A developer wants to monitor AWS Backup for excessive restore job creation. Which CloudWatch metric should be used?","RestoreJobCreated","BackupJobCreated","BackupVaultSize","BackupPlanCount","'RestoreJobCreated' tracks the number of restore jobs created in AWS Backup."
"Which AWS Backup feature allows for backup job selection based on backup vault?","Vault-based backup selection","Tag-based backup selection only","Resource type-based selection only","Lifecycle policy only","Vault-based backup selection enables backup jobs to be targeted to specific backup vaults."
"A developer needs to ensure backup jobs are only accessible from specific IP ranges. What should be configured?","VPC endpoint policies with IP restrictions","Lifecycle policy only","Backup vault access policy only","Backup plan tag only","VPC endpoint policies can restrict access to AWS Backup jobs from specific IP ranges."
"Which CLI command is used to update a backup plan in AWS Backup?","aws backup update-backup-plan","aws backup modify-backup-plan","aws backup edit-backup-plan","aws backup change-backup-plan","'aws backup update-backup-plan' updates a backup plan in AWS Backup."
"A developer wants to automate backup job retention policy updates. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate retention policy updates for backup jobs."
"Which IAM permission is required to restore a resource from a backup in AWS Backup?","backup:StartRestoreJob","backup:RestoreResource","backup:RecoverJob","backup:RestoreBackupJob","'backup:StartRestoreJob' is required to restore a resource from a backup in AWS Backup."
"A developer needs to monitor AWS Backup for excessive backup vault creation. Which CloudWatch metric should be used?","BackupVaultCreated","BackupJobCreated","BackupPlanCount","BackupSelectionCount","'BackupVaultCreated' tracks the number of backup vaults created in AWS Backup."
"Which AWS Backup feature allows for backup job selection based on backup plan tags?","Tag-based backup plan selection","Vault-based selection only","Resource type-based selection only","Lifecycle policy only","Tag-based backup plan selection enables backup jobs to be triggered based on backup plan tags."
"A developer needs to automate backup vault deletion protection. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for AWS Backup vaults."
"Which AWS Backup feature allows for restricting restore job actions to specific users?","IAM policy with action-level permissions","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore job actions to specific users in AWS Backup."
"A developer wants to monitor AWS Backup for excessive backup plan creation. Which CloudWatch metric should be used?","BackupPlanCreated","BackupVaultCreated","BackupJobCreated","BackupSelectionCount","'BackupPlanCreated' tracks the number of backup plans created in AWS Backup."
"Which AWS Backup feature allows for backup job selection based on restore point tags?","Tag-based restore point selection","Vault-based selection only","Resource type-based selection only","Lifecycle policy only","Tag-based restore point selection enables backup jobs to be triggered based on restore point tags."
"A developer needs to ensure restore jobs are only accessible from specific IP ranges. What should be configured?","VPC endpoint policies with IP restrictions","Lifecycle policy only","Backup vault access policy only","Backup plan tag only","VPC endpoint policies can restrict access to AWS Backup restore jobs from specific IP ranges."
"Which CLI command is used to list all restore jobs in AWS Backup?","aws backup list-restore-jobs","aws backup describe-restore-jobs","aws backup get-restore-jobs","aws backup show-restore-jobs","'aws backup list-restore-jobs' lists all restore jobs in AWS Backup."
"A developer wants to automate restore point tagging for compliance. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore point tagging for compliance."
"Which IAM permission is required to update a restore point in AWS Backup?","backup:UpdateRecoveryPoint","backup:ModifyRestorePoint","backup:EditRestorePoint","backup:ChangeRestorePoint","'backup:UpdateRecoveryPoint' is required to update a restore point in AWS Backup."
"A developer needs to monitor AWS Backup for excessive restore point tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch RecoveryPointCreated metric","BackupVaultSize metric","BackupPlanCount metric","CloudTrail logs restore point tag changes in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on resource type?","Resource type-based restore point selection","Vault-based selection only","Tag-based selection only","Lifecycle policy only","Resource type-based restore point selection enables restore points to be selected for specific resource types in AWS Backup."
"A developer needs to automate restore job notifications for completion events. Which AWS service is best suited?","Amazon SNS with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Amazon SNS with CloudWatch Events can automate notifications for completed restore jobs in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on resource tags?","Tag-based restore point selection","Vault-based selection only","Resource type-based selection only","Lifecycle policy only","Tag-based restore point selection enables restore points to be selected based on resource tags."
"A developer wants to restrict restore job creation to a specific resource type. What should be configured?","IAM policy with resource type condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore job creation to specific resource types using conditions."
"Which CLI command is used to describe a restore job in AWS Backup?","aws backup describe-restore-job","aws backup get-restore-job","aws backup show-restore-job","aws backup list-restore-job","'aws backup describe-restore-job' provides details about a restore job in AWS Backup."
"A developer needs to ensure restore jobs are only created for resources in a specific VPC. What should be configured?","IAM policy with VPC condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore job creation to resources in a specific VPC using conditions."
"Which AWS Backup feature allows for restore job retention beyond the default period?","Custom lifecycle policies","Backup plan tags only","Backup vault access policy only","Resource tag only","Custom lifecycle policies allow retention of restore jobs beyond the default period."
"A developer wants to automate restore job tagging for compliance. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore job tagging for compliance."
"Which IAM permission is required to update a restore job in AWS Backup?","backup:UpdateRestoreJob","backup:ModifyRestoreJob","backup:EditRestoreJob","backup:ChangeRestoreJob","'backup:UpdateRestoreJob' is required to update a restore job in AWS Backup."
"A developer needs to monitor AWS Backup for excessive restore point tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch RecoveryPointCreated metric","BackupVaultSize metric","BackupPlanCount metric","CloudTrail logs restore point tag changes in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on resource type?","Resource type-based restore point selection","Vault-based selection only","Tag-based selection only","Lifecycle policy only","Resource type-based restore point selection enables restore points to be selected for specific resource types in AWS Backup."
"A developer needs to automate restore point retention policy updates. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate retention policy updates for restore points."
"Which AWS Backup feature allows for restore point tagging for cost allocation?","Resource tags on restore points","Lifecycle policies only","Backup vault access policies only","Backup plan rules only","Resource tags on restore points enable cost allocation and tracking in AWS Backup."
"A developer wants to restrict restore point creation to a specific resource type. What should be configured?","IAM policy with resource type condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore point creation to specific resource types using conditions."
"Which CLI command is used to describe a restore point in AWS Backup?","aws backup describe-recovery-point","aws backup get-restore-point","aws backup show-restore-point","aws backup list-restore-point","'aws backup describe-recovery-point' provides details about a restore point in AWS Backup."
"A developer needs to ensure restore points are only created for resources in a specific VPC. What should be configured?","IAM policy with VPC condition","Backup vault access policy only","Lifecycle policy only","Backup plan tag only","IAM policies can restrict restore point creation to resources in a specific VPC using conditions."
"Which AWS Backup feature allows for restore point retention beyond the default period?","Custom lifecycle policies","Backup plan tags only","Backup vault access policy only","Resource tag only","Custom lifecycle policies allow retention of restore points beyond the default period."
"A developer wants to automate restore point tagging for cost allocation. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate restore point tagging for cost allocation."
"Which IAM permission is required to update a restore point tag in AWS Backup?","backup:TagResource","backup:UpdateRecoveryPoint","backup:EditRestorePoint","backup:ChangeRestorePoint","'backup:TagResource' is required to update a restore point tag in AWS Backup."
"A developer needs to monitor AWS Backup for excessive restore point tag changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch RecoveryPointCreated metric","BackupVaultSize metric","BackupPlanCount metric","CloudTrail logs restore point tag changes in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on backup plan tags?","Tag-based backup plan selection","Vault-based selection only","Resource type-based selection only","Lifecycle policy only","Tag-based backup plan selection enables restore points to be selected based on backup plan tags."
"A developer needs to automate restore point deletion protection for compliance. Which AWS service is best suited?","AWS CloudFormation","AWS Glue","Amazon Inspector","AWS Data Pipeline","CloudFormation can enable or disable deletion protection for AWS Backup restore points for compliance."
"Which AWS Backup feature allows for restricting restore point deletion to specific IAM roles?","IAM policy with role-level permissions","Backup plan tag only","Lifecycle policy only","Backup job tag only","IAM policies can restrict restore point deletion to specific IAM roles in AWS Backup."
"A developer wants to monitor AWS Backup for restore point deletion events. Which AWS service should be used?","CloudTrail event logging","CloudWatch RecoveryPointCreated metric","BackupVaultSize metric","BackupPlanCount metric","CloudTrail logs restore point deletion events in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on backup vault?","Vault-based restore point selection","Tag-based selection only","Resource type-based selection only","Lifecycle policy only","Vault-based restore point selection enables restore points to be selected based on backup vaults."
"A developer needs to automate restore point lifecycle policy updates. Which AWS service is best suited?","AWS Lambda with CloudWatch Events","AWS Glue","Amazon Inspector","AWS Data Pipeline","Lambda functions triggered by CloudWatch Events can automate lifecycle policy updates for restore points."
"Which IAM permission is required to update a restore point lifecycle policy in AWS Backup?","backup:UpdateRecoveryPointLifecycle","backup:ModifyRestorePointLifecycle","backup:EditRestorePointLifecycle","backup:ChangeRestorePointLifecycle","'backup:UpdateRecoveryPointLifecycle' is required to update a restore point lifecycle policy in AWS Backup."
"A developer needs to monitor AWS Backup for restore point lifecycle policy changes. Which AWS service should be used?","CloudTrail event logging","CloudWatch RecoveryPointCreated metric","BackupVaultSize metric","BackupPlanCount metric","CloudTrail logs restore point lifecycle policy changes in AWS Backup."
"Which AWS Backup feature allows for restore point selection based on lifecycle policy?","Lifecycle policy-based restore point selection","Vault-based selection only","Tag-based selection only","Resource type-based selection only","Lifecycle policy-based restore point selection enables restore points to be selected based on lifecycle policies in AWS Backup."