openapi: 3.1.0
info:
  title: Quiz CRUD API
  description: API for CRUD operations on quizzes
  version: 1.0.0
  license:
    name: All Rights Reserved
    url: https://en.wikipedia.org/wiki/All_rights_reserved
servers:
  - url: /
tags:
  - name: quiz
    description: Operations on quizzes
  - name: quiz question
    description: Operations on quiz questions
  - name: quiz answer
    description: Operations on quiz question answers
  - name: quiz generation and submission
    description: Operations on quiz generation and submission
  - name: quiz category
    description: Operations on quiz categories
  - name: oauth
    description: Operations related to OAuth
  - name: privileges
    description: Operations related to user privileges
  - name: feature toggles
    description: Operations related to feature toggles
  - name: payment
    description: Operations related to payment processing
  - name: plans
    description: Operations related to subscription plans
  - name: AI quiz creation
    description: Operations related to AI-driven quiz generation
security:
  - basicAuth: []
  - bearerAuth: []
paths:
  /admin/features:
    $ref: paths/admin_features.yaml
  /admin/features/{toggleName}:
    $ref: paths/admin_features_{toggleName}.yaml
  /stripe/create-checkout-session:
    $ref: paths/stripe_create-checkout-session.yaml
  /stripe/create-portal-session:
    $ref: paths/stripe_create-portal-session.yaml
  /stripe/webhook:
    $ref: paths/stripe_webhook.yaml
  /oauth/{oauthProvider}/state:
    $ref: paths/oauth_{oauthProvider}_state.yaml
  /oauth/callback:
    $ref: paths/oauth_callback.yaml
  /quiz:
    $ref: paths/quiz.yaml
  /quiz/crud:
    $ref: paths/quiz_crud.yaml
  /quiz/crud/import/csv:
    $ref: paths/quiz_crud_import_csv.yaml
  /quiz/crud/{id}:
    $ref: paths/quiz_crud_{id}.yaml
  /quiz/crud/{id}/question:
    $ref: paths/quiz_crud_{id}_question.yaml
  /quiz/crud/{id}/question/{question_id}:
    $ref: paths/quiz_crud_{id}_question_{question_id}.yaml
  /quiz/crud/{id}/question/{question_id}/answer:
    $ref: paths/quiz_crud_{id}_question_{question_id}_answer.yaml
  /quiz/crud/{id}/question/{question_id}/answer/{answer_id}:
    $ref: paths/quiz_crud_{id}_question_{question_id}_answer_{answer_id}.yaml
  /quiz/{id}:
    $ref: paths/quiz_{id}.yaml
  /quiz/{id}/generation/{generationId}/submission:
    $ref: paths/quiz_{id}_generation_{generationId}_submission.yaml
  /quiz/category/crud:
    $ref: paths/quiz_category_crud.yaml
  /quiz/category/crud/{id}:
    $ref: paths/quiz_category_crud_{id}.yaml
  /quiz/category/crud/{id}/subcategories:
    $ref: paths/quiz_category_crud_{id}_subcategories.yaml
  /user/stats:
    $ref: paths/user_stats.yaml
  /user/profile:
    $ref: paths/user_profile.yaml
  /search:
    $ref: paths/search.yaml
  /user/{userId}/roles:
    $ref: paths/user_{userId}_roles.yaml
  /user/{userId}/roles/{role}:
    $ref: paths/user_{userId}_roles_{role}.yaml
  /user/password_change:
    $ref: paths/user_password_change.yaml
  /user/password_reset_mail:
    $ref: paths/user_password_reset_mail.yaml
  /user/password_reset:
    $ref: paths/user_password_reset.yaml
  /plans:
    $ref: paths/plans.yaml
  /plans/{planId}:
    $ref: paths/plans_{planId}.yaml
  /plans/quiz-counts:
    $ref: paths/plans_quiz-counts.yaml
  /plans/{planId}/sample-questions:
    $ref: paths/plans_{planId}_sample-questions.yaml
  /quiz/ai-create:
    $ref: paths/quiz_ai-create.yaml
  /quiz/ai-create/status/{jobId}:
    $ref: paths/quiz_ai-create_status_{jobId}.yaml
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication using username/password
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from OAuth or after Basic authentication
