post:
  summary: Initiate AI quiz creation
  description: Initiates an asynchronous job to create a quiz using AI.
  operationId: initiateAiQuizCreation
  tags:
    - quiz
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../components/schemas/AiQuizCreationRequest.yaml'
  responses:
    '202':
      description: Accepted - AI quiz creation job initiated
      content:
        application/json:
          schema:
            $ref: '../components/schemas/AiQuizCreationResponse.yaml'
    '400':
      description: Bad Request - Invalid input
      content:
        application/json:
          schema:
            $ref: '../components/schemas/ExceptionMessage.yaml'
    '401':
      description: Unauthorized - User not authenticated
      content:
        application/json:
          schema:
            $ref: '../components/schemas/ExceptionMessage.yaml'
    '403':
      description: Forbidden - User lacks required roles
      content:
        application/json:
          schema:
            $ref: '../components/schemas/ExceptionMessage.yaml'
    '429':
      description: Too Many Requests - Daily AI quiz creation limit reached
      content:
        application/json:
          schema:
            $ref: '../components/schemas/DailyQuizLimitExceptionResponse.yaml'
