openapi: 3.1.0
info:
  title: Quiz CRUD API
  description: API for CRUD operations on quizzes
  version: 1.0.0
  license:
    name: All Rights Reserved
    url: https://en.wikipedia.org/wiki/All_rights_reserved
servers:
  - url: /
security:
  - basicAuth: []
  - bearerAuth: []
tags:
  - name: quiz
    description: Operations on quizzes
  - name: quiz question
    description: Operations on quiz questions
  - name: quiz answer
    description: Operations on quiz question answers
  - name: quiz generation and submission
    description: Operations on quiz generation and submission
  - name: quiz category
    description: Operations on quiz categories
  - name: oauth
    description: Operations related to OAuth
  - name: privileges
    description: Operations related to user privileges
  - name: feature toggles
    description: Operations related to feature toggles
  - name: payment
    description: Operations related to payment processing
  - name: plans
    description: Operations related to subscription plans
  - name: AI quiz creation
    description: Operations related to AI-driven quiz generation
paths:
  /admin/features:
    get:
      summary: Get all feature toggles
      description: Returns all feature toggles and their current state. Requires ADMIN role.
      operationId: getAllFeatureToggles
      tags:
        - feature toggles
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: boolean
                example:
                  role-based-access-backend: true
                  role-based-access-frontend: false
                  upgrade-pathways: true
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /admin/features/{toggleName}:
    get:
      summary: Get a specific feature toggle
      description: Returns the state of a specific feature toggle. Requires ADMIN role.
      operationId: getFeatureToggle
      tags:
        - feature toggles
      parameters:
        - name: toggleName
          in: path
          required: true
          schema:
            type: string
          description: The name of the feature toggle
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: boolean
                example: true
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    put:
      summary: Update a feature toggle
      description: Updates the state of a specific feature toggle. Requires ADMIN role.
      operationId: updateFeatureToggle
      tags:
        - feature toggles
      parameters:
        - name: toggleName
          in: path
          required: true
          schema:
            type: string
          description: The name of the feature toggle
        - name: enabled
          in: query
          required: true
          schema:
            type: boolean
          description: The new state of the feature toggle
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: boolean
                example: true
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /stripe/create-checkout-session:
    post:
      summary: Create a Stripe checkout session
      description: Creates a checkout session for a subscription plan
      operationId: createCheckoutSession
      tags:
        - payment
      security:
        - basicAuth: []
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StripeCheckoutSessionRequest'
      responses:
        '200':
          description: Checkout session created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StripeCheckoutSessionResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /stripe/create-portal-session:
    post:
      summary: Create a Stripe customer portal session
      description: Creates a customer portal session for managing subscriptions
      operationId: createCustomerPortalSession
      tags:
        - payment
      security:
        - basicAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: Customer portal session created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StripeCustomerPortalResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /stripe/webhook:
    post:
      summary: Handle Stripe webhook events
      description: Processes webhook events from Stripe
      operationId: handleWebhook
      tags:
        - payment
      security: []
      parameters:
        - name: Stripe-Signature
          in: header
          required: true
          schema:
            type: string
          description: Stripe signature header for webhook verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: string
              description: Raw webhook payload from Stripe
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                type: string
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /oauth/{oauthProvider}/state:
    get:
      summary: Retrieve OAuth state string
      description: |
        Retrieves the state string for a given OAuth provider. The state is generated for the oauth provider, which gives it back to us once the user is authenticated.
        This is a good way to prevent CSRF requests. Underneath it all, the string is a JWT so we can also verify it came from us by checking the signature.
      operationId: getOAuthStateString
      tags:
        - oauth
      parameters:
        - name: oauthProvider
          in: path
          required: true
          schema:
            type: string
            enum:
              - linkedin
          description: The name of the OAuth provider
        - name: redirectTo
          in: query
          required: false
          schema:
            type: string
            default: /app
          description: The path to redirect to after successful authentication
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: string
                example: stateStringExample
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /oauth/callback:
    get:
      summary: Handle OAuth callback
      description: Process OAuth callback with authorization code
      operationId: handleOauthCallback
      tags:
        - oauth
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
          description: Authorization code from OAuth provider
        - name: state
          in: query
          required: true
          schema:
            type: string
          description: State parameter for verification
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz:
    post:
      summary: Generate a random quiz for the user
      description: Generate a random quiz for the user by selecting a quiz accessible to the user based on their roles.
      operationId: generateRandomQuiz
      tags:
        - quiz generation and submission
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizGenerationRequest'
      responses:
        '200':
          description: Successfully generated quiz
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizGenerationResponse'
            text/csv:
              schema:
                $ref: '#/components/schemas/QuizGenerationResponseCSV'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: No accessible quizzes found for user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '429':
          description: Too Many Requests - Daily quiz limit reached
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyQuizLimitExceptionResponse'
  /quiz/crud:
    post:
      summary: Create a new quiz.
      description: Create a new quiz.
      operationId: createQuiz
      tags:
        - quiz
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuizRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateQuizResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    get:
      summary: Find all quizzes
      description: Find all quizzes
      operationId: findAllQuizzes
      tags:
        - quiz
      parameters:
        - name: title
          in: query
          description: Filter quizzes by title (partial match)
          schema:
            type: string
        - name: page_size
          in: query
          description: Maximum number of quizzes to return per page.
          schema:
            type: integer
            default: 10
            minimum: 10
            maximum: 100
        - name: page_number
          in: query
          description: Which page of the results to return
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizzes'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/import/csv:
    post:
      summary: Import a quiz from a file
      description: Import a quiz from a file.
      operationId: importQuiz
      tags:
        - quiz
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The file containing the quiz to import.
                createQuizRequest:
                  $ref: '#/components/schemas/CreateQuizRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateQuizResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/{id}:
    get:
      summary: Find a quiz by Id
      description: Find a quiz by Id
      operationId: findQuizById
      tags:
        - quiz
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    put:
      summary: Update a quiz
      description: Update a quiz by Id
      operationId: updateQuiz
      tags:
        - quiz
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQuizRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateQuizResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    delete:
      summary: Delete a quiz by Id
      description: Delete a quiz by Id
      operationId: deleteQuiz
      tags:
        - quiz
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/{id}/question:
    post:
      summary: Add a questions to a quiz
      description: Add a question to a quiz.
      operationId: createQuizQuestion
      tags:
        - quiz question
      parameters:
        - name: id
          description: the unique identifier of the quiz
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuizQuestionRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateQuizQuestionResponse'
        '404':
          description: Quiz Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    get:
      summary: Find all questions for a quiz
      description: Find all questions for a quiz
      operationId: findAllQuizQuestions
      tags:
        - quiz question
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question
          in: query
          required: false
          schema:
            type: string
          description: the search terms to filter questions by
        - name: page_size
          in: query
          description: Maximum number of questions to return per page.
          schema:
            type: integer
            default: 10
            minimum: 10
            maximum: 100
        - name: page_number
          in: query
          description: Which page of the results to return
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizQuestions'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Quiz Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/{id}/question/{question_id}:
    get:
      summary: Find a question by Id
      description: Find a question by Id
      operationId: findQuizQuestionById
      tags:
        - quiz question
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizQuestion'
        '404':
          description: Quiz or Quiz Question Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    put:
      summary: Update a question
      description: Update a question
      operationId: updateQuizQuestion
      tags:
        - quiz question
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQuizQuestionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateQuizQuestionResponse'
        '404':
          description: Quiz or Quiz Question Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    delete:
      summary: Delete a question
      description: Delete a question
      operationId: deleteQuizQuestion
      tags:
        - quiz question
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizQuestion'
        '404':
          description: Quiz or Quiz Question Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/{id}/question/{question_id}/answer:
    post:
      summary: Add an answer to a quiz question
      description: Add an answer to a quiz question.
      operationId: createQuizAnswer
      tags:
        - quiz answer
      parameters:
        - name: id
          description: the unique identifier of the quiz
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: question_id
          description: the unique identifier of the quiz question
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuizAnswerRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateQuizAnswerResponse'
        '404':
          description: Quiz or question Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    get:
      summary: Find all the answers for a quiz question
      description: Find all the answers for a quiz question
      operationId: findAllQuizAnswers
      tags:
        - quiz answer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz question
        - name: answer
          in: query
          required: false
          schema:
            type: string
          description: the search terms to filter answers by
        - name: page_size
          in: query
          description: Maximum number of answers to return per page.
          schema:
            type: integer
            default: 10
            minimum: 10
            maximum: 100
        - name: page_number
          in: query
          description: Which page of the results to return
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizAnswers'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Question Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/crud/{id}/question/{question_id}/answer/{answer_id}:
    get:
      summary: Find an answer by Id
      description: Find an answer by Id
      operationId: findQuizAnswerById
      tags:
        - quiz answer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
        - name: answer_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the answer to the question
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizAnswer'
        '404':
          description: Quiz or Quiz Question or Quiz Answer Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    put:
      summary: Update an answer
      description: Update an answer
      operationId: updateQuizAnswer
      tags:
        - quiz answer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
        - name: answer_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the answer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQuizAnswerRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateQuizAnswerResponse'
        '404':
          description: Quiz or Quiz Question or Quiz Answer Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    delete:
      summary: Delete an answer
      description: Delete an answer
      operationId: deleteQuizAnswer
      tags:
        - quiz answer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the question
        - name: answer_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the answer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizAnswer'
        '404':
          description: Quiz or Quiz Question or Quiz Answer Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/{id}:
    post:
      summary: Generate a quiz for the user
      description: Generate a quiz for the user by selecting a number of the questions and shuffling them.
      operationId: generateQuiz
      tags:
        - quiz generation and submission
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz to generate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizGenerationRequest'
      responses:
        '200':
          description: Successfully generated quiz
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizGenerationResponse'
            text/csv:
              schema:
                $ref: '#/components/schemas/QuizGenerationResponseCSV'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Quiz Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '429':
          description: Too Many Requests - Daily quiz limit reached
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyQuizLimitExceptionResponse'
  /quiz/{id}/generation/{generationId}/submission:
    post:
      summary: Submit the answers to a quiz for the user
      description: Submit the answers to a quiz for the user
      operationId: submitQuiz
      tags:
        - quiz generation and submission
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz to submit
        - name: generationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the quiz generation to submit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizSubmissionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizSubmissionResponse'
        '404':
          description: Quiz Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '429':
          description: Too Many Requests - Daily quiz submission limit reached
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyQuizLimitExceptionResponse'
  /quiz/category/crud:
    post:
      summary: Create a new quiz category
      description: Create a new quiz category
      operationId: createQuizCategory
      tags:
        - quiz category
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuizCategoryRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateQuizCategoryResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    get:
      summary: Find all quiz categories
      description: Find all quiz categories
      operationId: findAllQuizCategories
      tags:
        - quiz category
      parameters:
        - name: title
          in: query
          description: Filter quiz categories by title (partial match)
          schema:
            type: string
        - name: page_size
          in: query
          description: Maximum number of quiz categories to return per page.
          schema:
            type: integer
            default: 10
            minimum: 10
            maximum: 100
        - name: page_number
          in: query
          description: Which page of the results to return
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizCategories'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/category/crud/{id}:
    get:
      summary: Find a quiz category by Id
      description: Find a quiz category by Id
      operationId: findQuizCategoryById
      tags:
        - quiz category
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizCategory'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        default:
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    put:
      summary: Update a quiz category
      description: Update a quiz category by Id
      operationId: updateQuizCategory
      tags:
        - quiz category
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQuizCategoryRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateQuizCategoryResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    delete:
      summary: Delete a quiz category by Id
      description: Delete a quiz category by Id
      operationId: deleteQuizCategory
      tags:
        - quiz category
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizCategory'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/category/crud/{id}/subcategories:
    get:
      summary: Find all subcategories for a category
      description: Find all direct subcategories for a given category ID
      operationId: findSubcategoriesByCategoryId
      tags:
        - quiz category
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the parent category
          schema:
            type: string
            format: uuid
        - name: page_size
          in: query
          description: Maximum number of subcategories to return per page
          schema:
            type: integer
            default: 10
            minimum: 10
            maximum: 100
        - name: page_number
          in: query
          description: Which page of the results to return
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizCategories'
        '404':
          description: Parent category not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        default:
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/stats:
    get:
      summary: Get User Statistics
      description: Retrieves all user statistics for the logged-in user.
      operationId: getUserStats
      security:
        - basicAuth: []
        - bearerAuth: []
      tags:
        - user
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserStatistics'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        default:
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/profile:
    get:
      summary: get the current user's profile
      description: get the current user's profile
      operationId: getUserProfile
      security:
        - bearerAuth: []
      tags:
        - user
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        default:
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /search:
    get:
      summary: Search across quizzes and categories
      description: Search for quizzes and categories using a search term
      operationId: search
      tags:
        - search
      parameters:
        - name: q
          in: query
          description: Search term to filter quizzes and categories
          required: true
          schema:
            type: string
            minLength: 1
        - name: page_size
          in: query
          description: Maximum number of items to return per page
          required: false
          schema:
            type: integer
            default: 10
        - name: page_index
          in: query
          description: Index of the page to return (zero-based)
          required: false
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResults'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/{userId}/roles:
    post:
      tags:
        - privileges
      summary: Grant a role to a user
      description: Grants a specific role to a user. Requires ADMIN role.
      operationId: grantRole
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GrantRoleRequest'
      responses:
        '200':
          description: Role granted successfully
        '400':
          description: Invalid role name
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '403':
          description: Not authorized to grant roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
    get:
      tags:
        - privileges
      summary: Get user roles
      description: Returns all roles assigned to a user. Requires ADMIN role.
      operationId: getUserRoles
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the user
      responses:
        '200':
          description: List of user roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoles'
        '403':
          description: Not authorized to view roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/{userId}/roles/{role}:
    delete:
      tags:
        - privileges
      summary: Revoke a role from a user
      description: Revokes a specific role from a user. Requires ADMIN role.
      operationId: revokeRole
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the user
        - name: role
          in: path
          required: true
          schema:
            type: string
            enum:
              - admin
              - editor
          description: The role to revoke
      responses:
        '204':
          description: Role revoked successfully
        '403':
          description: Not authorized to revoke roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: User not found or role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/password_change:
    post:
      summary: Change user password
      description: Changes the password for the currently authenticated user
      operationId: changePassword
      security:
        - basicAuth: []
        - bearerAuth: []
      tags:
        - user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserPasswordRequest'
      responses:
        '200':
          description: Password successfully changed
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/password_reset_mail:
    post:
      summary: Request password reset email
      description: Sends a password reset link to the user's email address
      operationId: passwordResetMail
      tags:
        - user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordEmailRequest'
      responses:
        '200':
          description: Password reset email sent successfully
        '400':
          description: Invalid email address
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /user/password_reset:
    post:
      summary: Reset user password
      description: Reset a user's password using the token received by email
      operationId: resetPassword
      tags:
        - user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password successfully reset
        '400':
          description: Invalid request or passwords don't match
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Invalid token or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /plans:
    get:
      summary: Get all available plans
      description: Returns a list of all available subscription plans ordered by price
      operationId: getAllPlans
      tags:
        - plans
      security:
        - basicAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Plan'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /plans/{planId}:
    get:
      summary: Get a specific plan by ID
      description: Returns detailed information about a specific subscription plan
      operationId: getPlanById
      tags:
        - plans
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the plan to retrieve
      security:
        - basicAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plan'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /plans/quiz-counts:
    get:
      summary: Get quiz counts by plan
      description: Returns the number of quizzes available for each subscription plan
      operationId: getQuizCountsByPlan
      tags:
        - plans
      security:
        - basicAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlanQuizCounts'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /plans/{planId}/sample-questions:
    get:
      summary: Get sample questions for a plan
      description: Returns a list of sample questions for a specific subscription plan
      operationId: getSampleQuestions
      tags:
        - plans
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the plan
      security:
        - basicAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PlanSampleQuestion'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
  /quiz/ai-create:
    post:
      summary: Initiate AI quiz creation
      description: Initiates an asynchronous job to create a quiz using AI.
      operationId: initiateAiQuizCreation
      tags:
        - quiz
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AiQuizCreationRequest'
      responses:
        '202':
          description: Accepted - AI quiz creation job initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiQuizCreationResponse'
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '401':
          description: Unauthorized - User not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '403':
          description: Forbidden - User lacks required roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '429':
          description: Too Many Requests - Daily AI quiz creation limit reached
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyQuizLimitExceptionResponse'
  /quiz/ai-create/status/{jobId}:
    get:
      summary: Get AI quiz creation job status
      description: Retrieves the status and attempt count for a specific AI quiz creation job.
      operationId: getAiQuizCreationJobStatus
      tags:
        - quiz
      parameters:
        - name: jobId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the AI quiz creation job
      responses:
        '200':
          description: OK - Job status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiQuizCreationStatusResponse'
        '401':
          description: Unauthorized - User not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '403':
          description: Forbidden - User lacks required roles or is not the job owner
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
        '404':
          description: Not Found - Job ID not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication using username/password
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from OAuth or after Basic authentication
  schemas:
    ExceptionMessage:
      description: Generic exception message.
      type: object
      properties:
        message:
          type: string
    StripeCheckoutSessionRequest:
      description: Request body for creating a Stripe checkout session.
      type: object
      required:
        - planRole
      properties:
        planRole:
          type: string
          description: The role associated with the plan being purchased
          example: aws
        yearly:
          type: boolean
          description: Whether the subscription is yearly or monthly
          default: false
          example: true
    StripeCheckoutSessionResponse:
      description: Response body for creating a Stripe checkout session.
      type: object
      required:
        - checkoutUrl
      properties:
        checkoutUrl:
          type: string
          description: The URL to redirect the user to for checkout
          example: https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0
    StripeCustomerPortalResponse:
      description: Response body for creating a Stripe customer portal session.
      type: object
      required:
        - portalUrl
      properties:
        portalUrl:
          type: string
          description: The URL to redirect the user to for the customer portal
          example: https://billing.stripe.com/p/session/live_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0
    TokenResponse:
      description: Response body for authentication tokens.
      type: object
      properties:
        access_token:
          type: string
          format: JWT
          description: The access token
        access_token_expires_in:
          type: integer
          format: int64
          description: The lifetime in seconds of the access token
        refresh_token:
          type: string
          description: The refresh token
        refresh_token_expires_in:
          type: integer
          format: int64
          description: The lifetime in seconds of the refresh token
        redirect_to:
          type: string
          description: The path to redirect to after successful authentication
      required:
        - access_token
        - access_token_expires_in
        - refresh_token
        - refresh_token_expires_in
        - redirect_to
    QuizGenerationRequest:
      description: Request body for generating a quiz.
      type: object
      properties:
        seed:
          type: string
          description: the seed by which all randomness is set. A random seed will be used when randomising the quiz questions.
        max_questions:
          description: Maximum number of questions in the quiz.
          type: integer
    QuizAvailableAnswer:
      description: Represents an available answer for a quiz question.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the answer
        answer:
          type: string
          description: The answer
    QuizQuestionWithAvailableAnswers:
      description: Represents a quiz question with its available answers.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the question
        question:
          type: string
          description: The question
        answers:
          type: array
          items:
            $ref: '#/components/schemas/QuizAvailableAnswer'
    QuizGenerationResponse:
      description: Response body for generating a quiz.
      type: object
      properties:
        quizId:
          type: string
          format: uuid
        generationId:
          type: string
          format: uuid
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuizQuestionWithAvailableAnswers'
    QuizGenerationResponseCSV:
      description: Response body for generating a quiz in CSV format.
      type: object
      properties:
        question:
          type: string
          description: The question
        answer1:
          type: string
          description: A possible answer
        answer2:
          type: string
          description: A possible answer
        answer3:
          type: string
          description: A possible answer
        answer4:
          type: string
          description: A possible answer
    DailyQuizLimitExceptionResponse:
      description: Response body for daily quiz limit exception.
      type: object
      properties:
        message:
          type: string
          description: Technical error message
          example: Daily quiz start limit reached
        startLimit:
          type: boolean
          description: Whether this is a start limit (true) or submission limit (false)
          example: true
        userFriendlyMessage:
          type: string
          description: User-friendly message that can be displayed to the user
          example: You have reached your daily limit of 3 quizzes. Please try again tomorrow or upgrade your plan for unlimited quizzes.
      required:
        - message
        - startLimit
        - userFriendlyMessage
    Pagination:
      type: object
      description: A paginated list of objects
      properties:
        size:
          description: number of objects in the page
          type: integer
          format: int32
        page_index:
          type: integer
          format: int32
          description: The index (zero-based) of the current page
    Quiz:
      description: Represents a quiz.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
    PaginatedListOfQuizzes:
      description: A paginated list of quizzes.
      allOf:
        - $ref: '#/components/schemas/Pagination'
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/Quiz'
    CreateQuizRequest:
      description: Request body for creating a new quiz.
      type: object
      required:
        - title
      properties:
        title:
          type: string
          description: The title of the quiz
        categoryId:
          type: string
          format: uuid
          description: The ID of the category this quiz belongs to
        requiredRoles:
          type: array
          items:
            type: string
          description: List of roles required to access this quiz. If empty, quiz is accessible to all authenticated users.
          example:
            - admin
            - instructor
    CreateQuizResponse:
      description: Response body for creating a new quiz.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
    UpdateQuizRequest:
      description: Request body for updating a quiz.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The ID of the quiz to update
        title:
          type: string
          description: The new title of the quiz
        categoryId:
          type: string
          format: uuid
          description: The new category ID for the quiz
        requiredRoles:
          type: array
          items:
            type: string
          description: List of roles required to access this quiz. If empty, quiz is accessible to all authenticated users.
          example:
            - admin
            - instructor
    UpdateQuizResponse:
      description: Response body for updating a quiz.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
    QuizQuestion:
      description: Represents a quiz question.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the question
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        question:
          type: string
          description: The question
    PaginatedListOfQuizQuestions:
      description: A paginated list of quiz questions.
      allOf:
        - $ref: '#/components/schemas/Pagination'
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/QuizQuestion'
    CreateQuizQuestionRequest:
      description: Request body for creating a new quiz question.
      type: object
      properties:
        question:
          type: string
          description: The text of the question
        disabled:
          type: boolean
          description: Whether the question should be initially disabled. A disabled question will not be included in any generated quizzes.
    CreateQuizQuestionResponse:
      description: Response body for creating a new quiz question.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the question
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        question:
          type: string
          description: The question
        disabled:
          type: boolean
          description: Whether the question is disabled. A disabled question will not be included in any generated quizzes.
    UpdateQuizQuestionRequest:
      description: Request body for updating a quiz question.
      type: object
      properties:
        question:
          type: string
          description: The updated text of the question
        disabled:
          type: boolean
          description: Whether the question should be disabled. A disabled question will not be included in any generated quizzes.
    UpdateQuizQuestionResponse:
      description: Response body for updating a quiz question.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the question
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        question:
          type: string
          description: The question
        disabled:
          type: boolean
          description: Whether the question is disabled. A disabled question will not be included in any generated quizzes.
    QuizAnswer:
      description: Represents a quiz answer.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the answer
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        questionId:
          type: string
          format: uuid
          description: The unique identifier of the quiz question
        answer:
          type: string
          description: The answer
    PaginatedListOfQuizAnswers:
      description: A paginated list of quiz answers.
      allOf:
        - $ref: '#/components/schemas/Pagination'
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/QuizAnswer'
    CreateQuizAnswerRequest:
      description: Request body for creating a new quiz answer.
      type: object
      properties:
        answer:
          type: string
        correct:
          type: boolean
        rationale:
          type: string
    CreateQuizAnswerResponse:
      description: Response body for creating a new quiz answer.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the answer
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        questionId:
          type: string
          format: uuid
          description: The unique identifier of the quiz question
        answer:
          type: string
          description: The answer
        correct:
          type: boolean
        rationale:
          type: string
    UpdateQuizAnswerRequest:
      description: Request body for updating a quiz answer.
      type: object
      properties:
        answer:
          type: string
        correct:
          type: boolean
        rationale:
          type: string
    UpdateQuizAnswerResponse:
      description: Response body for updating a quiz answer.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the answer
        quizId:
          type: string
          format: uuid
          description: The unique identifier of the quiz
        questionId:
          type: string
          format: uuid
          description: The unique identifier of the quiz question
        answer:
          type: string
          description: The answer
        correct:
          type: boolean
        rationale:
          type: string
    QuizAnswerSubmission:
      description: Represents a quiz answer submission.
      type: object
      properties:
        questionId:
          type: string
          format: uuid
        answerId:
          type: string
          format: uuid
    QuizSubmissionRequest:
      description: Request body for submitting a quiz.
      type: object
      properties:
        answers:
          type: array
          items:
            $ref: '#/components/schemas/QuizAnswerSubmission'
    QuizCorrectedAnswer:
      description: Represents a corrected quiz answer.
      type: object
      properties:
        questionId:
          type: string
          format: uuid
        submittedAnswerId:
          type: string
          format: uuid
        correctAnswerId:
          type: string
          format: uuid
        correctAnswerRationale:
          type: string
    QuizSubmissionResponse:
      description: Response body for submitting a quiz.
      type: object
      properties:
        quizId:
          type: string
          format: uuid
        generationId:
          type: string
          format: uuid
        submissionId:
          type: string
          format: uuid
        score:
          type: integer
          format: int32
        answers:
          type: array
          items:
            $ref: '#/components/schemas/QuizCorrectedAnswer'
    QuizCategory:
      description: Represents a quiz category.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
    PaginatedListOfQuizCategories:
      description: A paginated list of quiz categories.
      allOf:
        - $ref: '#/components/schemas/Pagination'
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/QuizCategory'
    CreateQuizCategoryRequest:
      description: Request body for creating a new quiz category.
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
    CreateQuizCategoryResponse:
      description: Response body for creating a new quiz category.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
    UpdateQuizCategoryRequest:
      description: Request body for updating a quiz category.
      type: object
      properties:
        title:
          type: string
        description:
          type: string
    UpdateQuizCategoryResponse:
      description: Response body for updating a quiz category.
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
    UserStatistics:
      description: Represents user statistics.
      type: object
      properties:
        recordedAt:
          type: string
          format: date-time
          description: The moment at which the statistic was recorded.
        statisticName:
          type: string
          description: The name of the statistic.
        statisticValue:
          type: number
          format: float
          description: The value recorded for the statistic at the time.
    User:
      description: Represents a user.
      type: object
      properties:
        email:
          type: string
          format: email
          description: the email address of the user
        username:
          type: string
          description: the username of the user
    SearchResults:
      description: Represents search results.
      type: object
      properties:
        quizzes:
          $ref: '#/components/schemas/PaginatedListOfQuizzes'
        categories:
          $ref: '#/components/schemas/PaginatedListOfQuizCategories'
    UserRoles:
      description: Represents the roles of a user.
      type: object
      properties:
        roles:
          type: array
          items:
            type: string
            enum:
              - admin
              - editor
          description: List of roles assigned to the user
          example:
            - editor
            - admin
    GrantRoleRequest:
      description: Request body for granting a role to a user.
      type: object
      required:
        - role
      properties:
        role:
          type: string
          description: The role to grant (e.g. "admin", "editor")
          example: editor
          enum:
            - admin
            - editor
    UpdateUserPasswordRequest:
      description: Request body for updating a user password.
      type: object
      properties:
        currentPassword:
          type: string
          description: the current password
        newPassword:
          type: string
          description: the new password
        newPasswordConfirmation:
          type: string
          description: the new password confirmation
    ResetPasswordEmailRequest:
      type: object
      description: the request to receive a password reset link
      properties:
        email:
          type: string
          format: email
          description: the email address of the user requesting a password change
    ResetPasswordRequest:
      type: object
      description: the request to reset a password
      properties:
        nonce:
          type: string
          description: a server generated string acting like a temporary password
        newPassword:
          type: string
          description: the new password
        newPasswordConfirmation:
          type: string
          description: the new password confirmation
    Plan:
      description: Represents a subscription plan.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the plan
        name:
          type: string
          description: The name of the plan
          example: AWS
        description:
          type: string
          description: A detailed description of the plan
          example: Specialized AWS certification preparation quizzes
        role:
          type: string
          description: The role associated with this plan
          example: aws
        monthlyPrice:
          type: number
          format: double
          description: The monthly price of the plan
          example: 14.99
        yearlyPrice:
          type: number
          format: double
          description: The yearly price of the plan
          example: 149.99
        features:
          type: array
          items:
            type: string
          description: List of features included in this plan
          example:
            - All Free features
            - Unlimited attempts
            - Access to all AWS certification quizzes
        sampleQuestions:
          type: array
          items:
            type: string
          description: List of sample question texts for this plan
          example:
            - Which API call should you use to extend the visibility timeout for a specific message that your application is processing from an Amazon SQS queue?
      required:
        - id
        - name
        - role
        - monthlyPrice
        - yearlyPrice
    PlanQuizCounts:
      description: Represents the number of quizzes available for each plan.
      type: object
      properties:
        counts:
          type: object
          additionalProperties:
            type: integer
          description: A map of plan IDs to the number of quizzes available for that plan
          example:
            11111111-1111-1111-1111-111111111111: 10
            *************-2222-2222-************: 25
            *************-3333-3333-************: 50
      required:
        - counts
    PlanSampleQuestion:
      description: Represents a sample question for a plan.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier of the question
        text:
          type: string
          description: The text of the question
          example: Which API call should you use to extend the visibility timeout for a specific message that your application is processing from an Amazon SQS queue?
      required:
        - id
        - text
    AiQuizCreationRequest:
      description: Request body for initiating AI quiz creation.
      type: object
      required:
        - topic
      properties:
        topic:
          type: string
          description: The topic or subject for the AI to generate the quiz on.
          example: Quantum Physics
    AiQuizCreationResponse:
      description: Response body for initiating AI quiz creation.
      type: object
      required:
        - jobId
      properties:
        jobId:
          type: string
          format: uuid
          description: The unique identifier of the asynchronous AI quiz creation job.
          example: 123e4567-e89b-12d3-a456-************
    AiQuizCreationStatusResponse:
      description: Response body for retrieving the status of an AI quiz creation job.
      type: object
      required:
        - jobId
        - status
        - attemptCount
      properties:
        jobId:
          type: string
          format: uuid
          description: The unique identifier of the asynchronous AI quiz creation job.
          example: 123e4567-e89b-12d3-a456-************
        status:
          type: string
          description: The current status of the AI quiz creation job.
          enum:
            - PENDING
            - IN_PROGRESS
            - COMPLETED
            - FAILED
          example: IN_PROGRESS
        attemptCount:
          type: integer
          description: The number of attempts made to create the quiz.
          minimum: 0
          example: 1
        quizId:
          type: string
          format: uuid
          description: The ID of the created quiz, if the job is completed successfully.
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
