# Epic: List Incomplete Quizzes

## Description

This epic describes the changes required to add an endpoint to list quiz generations which have been generated but never submitted.

## Acceptance Criteria

- [ ] A new endpoint `/quizzes/incomplete` is added to the backend.
- [ ] The endpoint returns a list of the last 10 incomplete quiz generations, in reverse date order (most recent first).
- [ ] The endpoint is secured and requires appropriate authentication/authorization.
- [ ] The change is tested at all levels (unit, controller, db, acceptance).
- [ ] The change is documented, including the OpenAPI specification.

## Tasks

### 1. Backend Implementation

#### 1.1. Controller

- [ ] Add a new endpoint `/quizzes/incomplete` to the `QuizGenerationController`.
- [ ] The endpoint should call the `QuizGenerationService` to retrieve the last 10 incomplete quiz generations in reverse date order.
- [ ] The endpoint should return a list of `QuizGeneration` objects.
- [ ] The endpoint should be secured and require appropriate authentication/authorization.

#### 1.2. Service

- [ ] Add a new method `listLast10IncompleteQuizGenerations()` to the `QuizGenerationService`.
- [ ] The method should retrieve the last 10 incomplete quiz generations in reverse date order.
- [ ] The method should return a list of `QuizGeneration` objects.

#### 1.3. Repository

- [ ] Add a new method to retrieve the last 10 incomplete QuizGenerations in reverse date order with a custom query in `QuizGenerationRepository`.

### 2. Testing

#### 2.1. Unit Tests

- [ ] Add unit tests for the `QuizGenerationController` to verify the new endpoint.
- [ ] Add unit tests for the `QuizGenerationService` to verify the new method.

#### 2.2. Database Tests

- [ ] Add database tests for the repository method with the custom query.

#### 2.3. Controller tests

- [ ] Add mockMVC tests for the controller to verify the de-serialisation of the model classes
- [ ] Add mockMVC tests for the controller to verify authentication

#### 2.3. Acceptance tests

- [ ] Add acceptance tests to verify the new endpoint.

The following acceptance test scenarios should be implemented:

- User can retrieve a list of the last 10 incomplete quiz generations in reverse date order
- User retrieves an empty list of incomplete quiz generations when all quizzes have been submitted
- Endpoint returns an error when the user is not authenticated

### 3. Documentation

#### 3.1. OpenAPI Specification

- [ ] Update the OpenAPI (splits) specification to include the new endpoint.

```yaml
paths:
  /quizzes/incomplete:
    get:
      summary: Find the last 10 incomplete quiz generations
      description: Find the last 10 quiz generations which have been generated but never submitted, in reverse date order (most recent first)
      operationId: findLast10IncompleteQuizGenerations
      tags:
        - quiz generation and submission
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedListOfQuizGenerations'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExceptionMessage'
```

- [ ] Update the OpenAPI specification to include the new endpoint.

## Implementation Details

- The `QuizGenerationService` should have a method `listLast10IncompleteQuizGenerations()` to retrieve the last 10 incomplete quiz generations in reverse date order.
- The `QuizGenerationController` should have an endpoint `/quizzes/incomplete` to expose the last 10 incomplete quiz generations.
- The endpoint should be secured and require appropriate authentication/authorization.
- The OpenAPI specification should be updated to include the new endpoint.
- The `listLast10IncompleteQuizGenerations()` method in `QuizGenerationService` should invoke the new repository method to retrieve the last 10 incomplete quiz generations in reverse date order. It should do no special heavy lifting, instead the custom query in the repository should do the work.
- The QuizGenerationRepository should have a new method annotated with a custom query to retrieve the last 10 generations without a matching submission, ordered by date descending.

## Checkpoints

- [ ] Backend implementation completed.
- [ ] Unit tests completed.
- [ ] Database tests completed.
- [ ] Controller tests comleted.
- [ ] Acceptance tests completed.
- [ ] OpenAPI specification updated.
