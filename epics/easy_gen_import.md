# Easy Gen import

As an engineer
I would like to easily generate a quiz and import it
so that I don't have to do this manually.

See:

- src/main/bin/import.sh
- src/main/bin/import_dev_eu_west_1.sh
- src/main/bin/import_all.sh
- ../f_quiz_generation/src/quiz_gemini.py
- src/docs/openapi.yaml


```bash
curl --location 'http://localhost:8080/quiz/crud/import/csv' \
--header 'Authorization: Basic bHBpY3F1ZXQ6dG90bw==' \
--form 'file=@"path/to/your/quiz.csv";type=application/csv' \
--form 'createQuizRequest={"title":"Quiz Title","requiredRoles":["aws"]};type=application/json'
```

## Plan

1. Create a new Python script called `gen_import_quiz.py` in `f_quiz_generation/src` that automates the quiz generation and import process. This script will use `argparse` and take the following arguments:
    - `--topic`: The topic of the quiz to generate. If provided, the category lookup is not necessary.
    - `--questions`: The number of questions to generate.
    - `--level`: The level of the exam.
    - `--base-url`: The base URL of the API (defaults to `https://api.dev.quizalto.com/`).
    - `--username`: The username to use for authentication (or use the `F_QUIZ_USERNAME` environment variable).
    - `--password`: The password to use for authentication (or use the `F_QUIZ_PASSWORD` environment variable).
    - `--category-id`: The ID of the category of the quiz. If provided without a `--topic`, the script will query the API using the provided `--base-url`, `--username`, and `--password` to get the category name and use it as the topic.
    - `--required-roles`: A comma-separated list of roles required to take the quiz (defaults to empty, making the quiz public).

The script will then use `quiz_gemini.py` to generate a quiz with the given `--topic`, `--questions`, and `--level`, using the appropriate prompt file based on the level, and use the `import.sh` script to import the generated quiz into the database, using the provided `--base-url`, `--username`, `--password`, `--category-id`, and `--required-roles`.
