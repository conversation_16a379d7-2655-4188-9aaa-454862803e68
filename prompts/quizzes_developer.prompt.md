I want to prepare for the AWS Associate Developer certification exam, so please do not include fundational questions has these have already been asked elsewhere.

The idea here is to go deeper in the topic from the perspective of a developer.
You can for example provide a mixture of conceptual questions, practical questions referencing the AWS CLI and IAM permissions.
You can also leverage Scenario based questions focusing on nuanced real-world usage, emphasising development, integration, security, performance, optimisation and troubleshooting.
