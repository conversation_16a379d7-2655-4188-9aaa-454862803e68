# crewai == 0.30.5
# poetry == 1.8.3
# diagrams == 0.23.4

# the below packages are used to 'talk' to pdfs
# chainlit == 1.1.0 # https://pypi.org/project/chainlit/
# langchain == 0.1.20 # https://pypi.org/project/langchain/
# langchain-community == 0.0.38 # https://pypi.org/project/langchain-community/
# pypdf == 4.2.0 # https://pypi.org/project/pypdf/
# chromadb == 0.5.0 # https://pypi.org/project/chromadb/
# langchainhub == 0.1.15 # https://pypi.org/project/langchainhub/

# download pdfs
# lxml==5.2.1 # dependency of pywebcopy
# lxml-html-clean==0.1.1 # the troublemaker
# pywebcopy == 7.0.2 # https://pypi.org/project/pywebcopy/

# sentiment analysis
# nltk

# download and parse markdowns
# markdown==3.6 # https://pypi.org/project/Markdown/
# html2text==2024.2.26 # https://pypi.org/project/html2text/

# openapi chatgpt
# openai == 1.35.3 # https://pypi.org/project/openai/

# Google Gemini
google-genai == 1.16.1
requests == 2.31.0
