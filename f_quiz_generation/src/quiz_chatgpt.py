import logging
import argparse
import os
from openai import OpenAI

import re

CSV_RECORD_REGEX=r'(?:^|,)(?=[^"]|(")?)"?((?(1)[^"]*|[^,"]*))"?(?=,|$)'

def args_parse() -> argparse.Namespace:
    """
    Parses command-line arguments for quiz generation.

    Returns:
        An object containing the parsed arguments.
    """
    # Create the argument parser
    parser: argparse.ArgumentParser = argparse.ArgumentParser(description="Create a quiz from the llm's knowledge.")

    parser.add_argument(
        "--model",
        required=False,
        help="The name of the model which interacts with the RAG",
        default="gpt-3.5-turbo",
        type=str
    )
    parser.add_argument(
        "--topic",
        nargs='+',
        required=True,
        help="The topic of the quiz to generate from the document",
        type=str
    )
    parser.add_argument(
        "--questions",
        required=False,
        type=int,
        help="The number of questions to ask as part of this quiz",
        default=5
    )
    parser.add_argument(
        "--batch-size",
        required=False,
        type=int,
        help="The number of questions to ask as part of this quiz in a single batch",
        default=5
    )
    parser.add_argument(
        "--level",
        required=False,
        help="The level of the exam (CCP, Associate, Architect)",
        default="CCP",
        type=str
    )

    # Parse the arguments
    args: argparse.Namespace = parser.parse_args()
    return args

def run_query(model: str, topic: list[str], num_questions: int = 5, level: str = "CCP") -> str:
    """
    Generates multiple choice questions about a given topic using the OpenAI API.

    Args:
        model: The name of the OpenAI model to use.
        topic: The topic of the quiz questions.
        num_questions: The number of questions to generate.
        level: The level of the exam (CCP, Associate, Architect).

    Returns:
        The content of the message containing the generated questions in CSV format.
    """

    client: OpenAI = OpenAI(
        # This is the default and can be omitted
        api_key=os.environ.get("OPENAI_API_KEY"),
    )

    query = f"""
Could you create {num_questions} multiple choice questions about **{topic}**, so that I can prepare for the AWS {level} exam?
I may have already asked you to create some questions previously so don't necessarily go for the obvious ones straight away.
Please provide a CSV file with a header for the question, the correct answer, exactly 3 wrong answers and the rationale for the correct answer, in that order.
Absolutely make sure that there is only one correct answer to each question and that the other available answers are truely incorrect.
Please do not ask binary questions (yes/no or true/false) as you will struggle to provide 3 wrong answers.
Answers should only contain the text of the answer and NOT be prefixed (for example with A, B, C, D) as they will be presented in random order.
Questions should also ABSOLUTELY NOT be numbered, as they will be presented in random order.
Answers should also ABSOLUTELY NOT be numbered, as they will be presented in random order.
Please make sure to always include the header row.
Please make sure to always include the topic in the question as the questions may eventually be chosen across multiple topics.
Please make sure to not repeat questions you have already asked. It is Ok to ask a similar question phrased differently.
Please make sure to always double-quote (not single quoted) each question and answer and the rationale too so that it will not interfere with the CSV structure should individual values contain commas.
Please make sure that none of the CSV records start with a comma as it interferes with the structure of the output.
Please make sure to not include extra carriage returns so that each record is one one line exactly and contains all the requested fields.
Please make sure to NOT add spaces between the CSV fields (after the commas) which are used in the fields delimitations.
Please make sure the correct answer is in the correct position in the CSV record. I will render the correct answer differently so it's important I know that the first answer is the correct one.
Please make sure the always provide all of the question, the correct answer, exactly 3 wrong answers and the rationale for the correct answer, in that order for each csv record.
You will use British English spelling.
ABSOLUTELY DO NOT USE 'None of the above' or 'All of the above' as an answer.
Please make sure to surround the csv content with markdown syntax to separate it from other regular content.
"""

    try:
        result: str = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": f"{query}",
                }
            ],
            model=model,
        )

        return result.choices[0].message.content
    except Exception as e:
        logging.error(f"Error during OpenAI API call: {e}")
        return ""


def filter_lines(text: str, regex: str) -> list[str]:
    """
    Filters lines from a text that match a regular expression.

    Args:
        text: The text to filter.
        regex: The regular expression to match.

    Returns:
        A list of lines that match the regular expression.
    """
    logging.info("Filtering lines based on regex.")
    lines: list[str] = text.splitlines()
    filtered_lines: list[str] = []
    for line in lines:
        logging.debug(f"assessing dialog output line : {line}")
        if len(re.findall(regex, line)) == 6:
           filtered_lines += [line]

    logging.info(f"Filtered lines, found {len(filtered_lines)} matching lines.")
    return filtered_lines

def main(model: str, topic: list[str], questions: int, batch_size: int, level: str) -> None:
    """
    Generates and prints multiple choice questions to stdout in batches.

    Args:
        model: The name of the OpenAI model to use.
        topic: The topic of the quiz questions.
        questions: The total number of questions to generate.
        batch_size: The number of questions to generate in each batch.
        level: The level of the exam (CCP, Associate, Architect).

    Example:
        main(model="gpt-3.5-turbo", topic=["EC2"], questions=10, batch_size=5, level="CCP")

    """
    num_batches: int = args.questions // args.batch_size
    questions_per_batch: int = args.questions // num_batches
    logging.info(f"number of batches: {num_batches}")
    logging.info(f"questions per batches: {questions_per_batch}")

    for i in range(0, num_batches):
        logging.info(f"Processing batch {i+1}/{num_batches}")
        unparsed_result: str = run_query(model=args.model, topic=args.topic, num_questions=questions_per_batch, level=level)
        filtered_lines: list[str] = filter_lines(unparsed_result, CSV_RECORD_REGEX)
        for idx, line in enumerate(filtered_lines):
            if (i !=0 and idx !=0) or (i == 0):
                # will print the line if on the first batch
                # will print the line if not on the first batch and not on the first line
                print(line)
            logging.debug(f"Printing line {idx+1}/{len(filtered_lines)}: {line}")


if __name__ == "__main__":
    """
    Entry point of the script.
    Configures logging, parses arguments, and runs the main function.

    Example:
        python quiz_chatgpt.py --topic "EC2" --questions 10 --batch-size 5 --level "CCP"

    """
    logging.basicConfig(level=logging.INFO)
    args: argparse.Namespace = args_parse()
    main(model=args.model, topic=args.topic, questions=args.questions, batch_size=args.batch_size, level=args.level)
