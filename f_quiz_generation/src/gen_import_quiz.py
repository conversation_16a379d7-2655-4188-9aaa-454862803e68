import argparse
import os
import subprocess
import json
import logging
import csv
import sys
import requests

def check_csv_format(file_path):
    """
    Checks the correctness of a CSV file against a predefined format.

    Args:
        file_path (str): The path to the CSV file.

    Returns:
        bool: True if the CSV format is correct, False otherwise.
    """
    expected_columns = ["question", "correct_answer", "wrong_answer1", "wrong_answer2", "wrong_answer3", "rationale"]

    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader)  # Read header row

            if header != expected_columns:
                raise ValueError(f"Header mismatch in '{file_path}'. Expected {expected_columns}, got {repr(header)}. Problematic line: {','.join(header)}")

            for i, row in enumerate(reader):
                if len(row) != len(expected_columns):
                    raise ValueError(f"Row {i + 2} in '{file_path}' has {len(row)} columns, expected {len(expected_columns)}. Problematic row (repr): {repr(row)}. Problematic line: {','.join(row)}")

                # Check for disallowed phrases in answers and length constraints
                if len(row[0]) > 512:
                    raise ValueError(f"Row {i + 2} in '{file_path}' contains question longer than 512 characters: '{row[0]}'. Questions must be less than or equal to 512 characters.")

                answers = [row[1], row[2], row[3], row[4]] # correct_answer, wrong_answer1, wrong_answer2, wrong_answer3
                for answer in answers:
                    if "All of the above" in answer or "None of the above" in answer:
                        raise ValueError(f"Row {i + 2} in '{file_path}' contains disallowed phrase in answer: '{answer}'. Phrases 'All of the above' and 'None of the above' are not allowed.")
                    if len(answer) > 512:
                        raise ValueError(f"Row {i + 2} in '{file_path}' contains answer longer than 512 characters: '{answer}'. Answers must be less than or equal to 512 characters.")

        print(f"CSV file '{file_path}' is correctly formatted.")
        return True
    except FileNotFoundError:
        raise FileNotFoundError(f"File not found at {file_path}")
    except ValueError as ve:
        raise ve # Re-raise the ValueError with specific message
    except Exception as e:
        raise Exception(f"An unexpected error occurred while checking CSV: {e}")

def main():

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description="Generate and import quizzes.")
    parser.add_argument("--topic", help="The topic of the quiz to generate.")
    parser.add_argument("--title", help="The title of the quiz upon importing. Defaults to the topic provided.", default=None)
    parser.add_argument("--questions", type=int, help="The number of questions to generate. Default is 100.", default=100)
    parser.add_argument("--base-url", default="https://api.dev.quizalto.com/", help="The base URL of the API. Defult is https://api.dev.quizalto.com/")
    parser.add_argument("--username", help="The username to use for authentication (or use the F_QUIZ_USERNAME environment variable).")
    parser.add_argument("--password", help="The password to use for authentication (or use the F_QUIZ_PASSWORD environment variable).")
    parser.add_argument("--category-id", help="The ID of the category of the quiz.")
    parser.add_argument("--required-roles", help="A comma-separated list of roles required to take the quiz (defaults to empty, making the quiz public).")
    parser.add_argument("--guidance-file", help="Path to a guidance file containing instructions for the generative ai to modulate the quiz to generate, optional. For example, it will provde context as to why you would like the quiz generated and sets the tone or the difficulty level.")
    parser.add_argument("--csv-file", help="Path to the output CSV file containing the quiz questions and answers.")
    parser.add_argument("--metadata-file", help="Path to the metadata file containing additional information about the quiz.")

    args = parser.parse_args()

    # Check if required arguments are missing
    if not args.topic:
        parser.error("--topic must be provided to generate a quiz.")

    if not args.csv_file:
        parser.error("--csv-file must be provided to save the quiz output.")

    if not args.metadata_file:
        parser.error("--metadata-file must be provided to save the quiz metadata.")

    # Handle username and password
    username = args.username or os.environ.get("F_QUIZ_USERNAME")
    password = args.password or os.environ.get("F_QUIZ_PASSWORD")

    if not username:
        parser.error("--username must be provided or the F_QUIZ_USERNAME environment variable must be set.")
    if not password:
        parser.error("--password must be provided or the F_QUIZ_PASSWORD environment variable must be set.")

    # make an absolute path to this python script
    abs_script_path = os.path.abspath(__file__)

    # make an absolute path to the quiz_gemini.py script
    abs_quiz_gemini_path = os.path.join(os.path.dirname(abs_script_path), "quiz_gemini.py")

    # Generate the quiz
    command = [
        "python3",
        abs_quiz_gemini_path,
        "--topic", args.topic,
        "--questions", str(args.questions)
    ]

    if args.guidance_file:
        command.extend(["--guidance-file", args.guidance_file])

    # Run the command and grab the output
    abs_csv_file_path = os.path.abspath(args.csv_file)

    # Run the command and grab the output
    abs_csv_file_path = os.path.abspath(args.csv_file)

    if args.questions > 0:
        logger.info(f"Running command: {' '.join(command)}")
        quiz_generation_command_run = subprocess.run(command, capture_output=True, text=True)
        quiz_generation_command_run.check_returncode()

        # Save the output to a CSV file
        # check if the file already exists. If it does, we will append to it, otherwise we will overwrite it.
        if os.path.exists(abs_csv_file_path):
            csv_write_mode= "a"
            logger.info(f"File {abs_csv_file_path} already exists. Appending to it.")
            # We will strip the header from the output, since it should already be present in the file
            csv_content = "\n".join(quiz_generation_command_run.stdout.splitlines()[1:])

            # Ensure the appended content starts on a new line if the file doesn't end with one
            with open(abs_csv_file_path, 'r', newline='', encoding='utf-8') as f_read:
                f_read.seek(0, os.SEEK_END) # Go to the end of the file
                if f_read.tell() > 0: # Check if file is not empty
                    f_read.seek(f_read.tell() - 1, os.SEEK_SET) # Go back one character
                    last_char = f_read.read(1)
                    if last_char != '\n':
                        csv_content = '\n' + csv_content
        else:
            csv_write_mode = "w"
            csv_content = quiz_generation_command_run.stdout

        with open(abs_csv_file_path, csv_write_mode) as f:
            f.write(csv_content)
        logger.info(f"Quiz generated and saved to {abs_csv_file_path}")

    # Validate the CSV file always, regardless of whether questions were generated or not.
    # This ensures that even pre-existing CSV files passed via --csv-file are validated.
    logger.info(f"Checking CSV file format for {abs_csv_file_path}...")
    try:
        check_csv_format(abs_csv_file_path)
        logger.info(f"CSV file {abs_csv_file_path} format check passed.")
    except ValueError as ve:
        logger.error(f"CSV validation failed: {ve}")
        raise # Re-raise the caught ValueError
    except FileNotFoundError as fnfe:
        logger.error(f"CSV validation failed: {fnfe}")
        raise # Re-raise the caught FileNotFoundError
    except Exception as e:
        logger.error(f"An unexpected error occurred during CSV validation: {e}")
        raise # Re-raise any other unexpected exceptions

    # Handle username and password
    username = args.username or os.environ.get("F_QUIZ_USERNAME")
    password = args.password or os.environ.get("F_QUIZ_PASSWORD")

    if not username:
        parser.error("--username must be provided or the F_QUIZ_USERNAME environment variable must be set.")
    if not password:
        parser.error("--password must be provided or the F_QUIZ_PASSWORD environment variable must be set.")

    category_id = args.category_id
    if not category_id:
        logger.info(f"Category ID not provided. Searching for category with topic '{args.topic}'...")
        search_url = f"{args.base_url.rstrip('/')}/search"
        try:
            response = requests.get(search_url, params={"q": args.topic}, auth=(username, password))
            response.raise_for_status()
            search_results = response.json()
            categories = search_results.get("categories", {}).get("items", [])
            if not categories:
                raise RuntimeError(f"Could not find category for topic '{args.topic}'")
            # Assuming the first result is the correct one for exact match
            category_id = categories[0].get("id")
            if not category_id:
                 raise RuntimeError(f"Could not retrieve category ID for topic '{args.topic}' from search results.")
            logger.info(f"Found category ID '{category_id}' for topic '{args.topic}'.")
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Error searching for category '{args.topic}': {e}")
        except Exception as e:
            raise RuntimeError(f"An unexpected error occurred during category lookup: {e}")

    # make an absolute path to this python script
    abs_script_path = os.path.abspath(__file__)

    # make an absolute path to the quiz_gemini.py script
    abs_quiz_gemini_path = os.path.join(os.path.dirname(abs_script_path), "quiz_gemini.py")

    # Generate the quiz
    command = [
        "python3",
        abs_quiz_gemini_path,
        "--topic", args.topic,
        "--questions", str(args.questions)
    ]

    if args.guidance_file:
        command.extend(["--guidance-file", args.guidance_file])

    # Run the command and grab the output
    abs_csv_file_path = os.path.abspath(args.csv_file)

    # Run the command and grab the output
    abs_csv_file_path = os.path.abspath(args.csv_file)

    if args.questions > 0:
        logger.info(f"Running command: {' '.join(command)}")
        quiz_generation_command_run = subprocess.run(command, capture_output=True, text=True)
        quiz_generation_command_run.check_returncode()

        # Save the output to a CSV file
        # check if the file already exists. If it does, we will append to it, otherwise we will overwrite it.
        if os.path.exists(abs_csv_file_path):
            csv_write_mode= "a"
            logger.info(f"File {abs_csv_file_path} already exists. Appending to it.")
            # We will strip the header from the output, since it should already be present in the file
            csv_content = "\n".join(quiz_generation_command_run.stdout.splitlines()[1:])

            # Ensure the appended content starts on a new line if the file doesn't end with one
            with open(abs_csv_file_path, 'r', newline='', encoding='utf-8') as f_read:
                f_read.seek(0, os.SEEK_END) # Go to the end of the file
                if f_read.tell() > 0: # Check if file is not empty
                    f_read.seek(f_read.tell() - 1, os.SEEK_SET) # Go back one character
                    last_char = f_read.read(1)
                    if last_char != '\n':
                        csv_content = '\n' + csv_content
        else:
            csv_write_mode = "w"
            csv_content = quiz_generation_command_run.stdout

        with open(abs_csv_file_path, csv_write_mode) as f:
            f.write(csv_content)
        logger.info(f"Quiz generated and saved to {abs_csv_file_path}")

    # Validate the CSV file always, regardless of whether questions were generated or not.
    # This ensures that even pre-existing CSV files passed via --csv-file are validated.
    logger.info(f"Checking CSV file format for {abs_csv_file_path}...")
    try:
        check_csv_format(abs_csv_file_path)
        logger.info(f"CSV file {abs_csv_file_path} format check passed.")
    except ValueError as ve:
        logger.error(f"CSV validation failed: {ve}")
        raise # Re-raise the caught ValueError
    except FileNotFoundError as fnfe:
        logger.error(f"CSV validation failed: {fnfe}")
        raise # Re-raise the caught FileNotFoundError
    except Exception as e:
        logger.error(f"An unexpected error occurred during CSV validation: {e}")
        raise # Re-raise any other unexpected exceptions

    # Create the metadata
    title = args.title if args.title else args.topic
    metadata = {
        "file": f"{abs_csv_file_path}",
        "title": title,
        "category": category_id,
        "requiredRoles": args.required_roles.split(",") if args.required_roles else [],
    }

    # Save the metadata to the file
    abs_metadata_file_path = os.path.abspath(args.metadata_file)
    with open(abs_metadata_file_path, "w") as f:
        json.dump(fp=f, obj=metadata)

    logger.info(f"Metadata saved to {abs_metadata_file_path}")


    # make an absolute path to the import script
    abs_import_script_path = os.path.join(os.path.dirname(abs_script_path), "../../src/main/bin/import.sh")
    if not os.path.exists(abs_import_script_path):
        logger.error(f"Import script not found at {abs_import_script_path}. Please check the path.")
        raise FileNotFoundError(f"Import script not found at {abs_import_script_path}. Please check the path.")

    # Import the quiz using the API
    import_command = [
        abs_import_script_path,
        "--base-url", args.base_url,
        "--file", abs_csv_file_path,
        "--category", category_id,
        "--title", title
    ]

    # only pass the username and password if they are provided.
    # Otherwise, rely on the environment variables.
    if args.username:
        import_command.extend(["--username", username])
    if args.password:
        import_command.extend(["--password", password])

    if args.required_roles:
        import_command.extend(["--required-roles", args.required_roles])

    # execute the import command
    if args.questions <= 0:
        logger.info("No questions generated, skipping import command.")
        return

    logger.info(f"Running import command: {' '.join(import_command)}")
    import_command_run = subprocess.run(import_command, capture_output=True, text=True)
    if (import_command_run.returncode != 0):
        logger.error("Import command failed with return code: " + str(import_command_run.returncode))
        logger.error("Import command error output: " + import_command_run.stderr)
        raise RuntimeError("Import command failed. Check the logs for more details.")

    logger.info("Import command output: " + import_command_run.stdout)


if __name__ == "__main__":
    main()
