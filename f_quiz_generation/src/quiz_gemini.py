import logging
import argparse
import os
import csv
import io
from google import genai
from typing import List, Optional
from argparse import Namespace

# CSV_RECORD_REGEX=r'(?:^|,)(?=[^"]|(")?)"?((?(1)[^"]*|[^,"]*))"?(?=,|$)'

def args_parse() -> Namespace:
    """
    Parses command-line arguments for quiz generation.

    Returns:
        An object containing the parsed arguments.
    """
    # Create the argument parser
    parser: argparse.ArgumentParser = argparse.ArgumentParser(description="Create a quiz from the LLM's knowledge.")

    parser.add_argument(
        "--model",
        required=False,
        help="The name of the model to use. Defaults to 'gemini-2.0-flash-001'",
        default="gemini-2.0-flash-001",
        type=str
    )
    parser.add_argument(
        "--topic",
        required=True,
        help="The topic of the quiz to generate.",
        type=str
    )
    parser.add_argument(
        "--questions",
        required=False,
        type=int,
        help="The number of questions to ask as part of this quiz. Defaults to 100.",
        default=100
    )
    parser.add_argument(
        "--batch-size",
        required=False,
        type=int,
        help="The number of questions to ask as part of this quiz in a single batch. Defaults to 35.",
        default=35
    )
    parser.add_argument(
        "--guidance-file",
        required=False,
        help="Path to the file containing some instructions that will guide the AI into generating the quiz you want, such as providing the reasons why you would like to generate the quiz, or setting the tone or difficulty level. Optional.",
        type=str
    )

    # Parse the arguments
    args: Namespace = parser.parse_args()
    return args

def run_query(model: str, topic: List[str], num_questions: int = 5, prompt_guidance: str = "") -> str:
    """
    Generates multiple choice questions about a given topic using the Gemini API.

    Args:
        model: The name of the Gemini model to use.
        topic: The topic of the quiz questions.
        num_questions: The number of questions to generate.
        prompt_guidance: The content of a prompt file containing instructions to guide the quiz generation.

    Returns:
        The content of the message containing the generated questions in CSV format.
    """

    client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))

    query = f"""
Could you create {num_questions} multiple choice questions about **{topic}**?

{prompt_guidance}

Please provide a CSV file with a header for the question, the correct answer, exactly 3 wrong answers and the rationale for the correct answer, in that order.
Absolutely make sure that there is only one correct answer to each question and that the other available answers are truely incorrect.
Please do not ask binary questions (yes/no or true/false) as you will struggle to provide 3 wrong answers.
Answers should only contain the text of the answer and NOT be prefixed (for example with A, B, C, D) as they will be presented in random order.
Questions should also ABSOLUTELY NOT be numbered, as they will be presented in random order.
Answers should also ABSOLUTELY NOT be numbered, as they will be presented in random order.
Please make sure to always include the header row: "question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale".
Please make sure to always include the topic in the question as the questions may eventually be chosen across multiple topics.
Please make sure to not repeat questions you have already asked. It is Ok to ask a similar question phrased differently.
Please make sure to always double-quote (not single quote) each question and answer and the rationale too so that it will not interfere with the CSV structure should individual values contain commas.
Please make sure to use single-quote (not double quote) inside the questions or answers, as it can interfere with the CSV structure if not observed. The double quote is only used as the CSV field delimiter.
Please make sure that none of the CSV records start with a comma as it interferes with the structure of the output.
Please make sure to not include extra carriage returns so that each record is one one line exactly and contains all the requested fields.
Please make sure to NOT add spaces between the CSV fields (after the commas) which are used in the fields delimitations.
Please make sure the correct answer is in the correct position in the CSV record. I will render the correct answer differently so it's important I know that the first answer is the correct one.
Please make sure the always provide all of the question, the correct answer, exactly 3 wrong answers and the rationale for the correct answer, in that order for each csv record.

You will use British English spelling.
ABSOLUTELY DO NOT USE 'None of the above' or 'All of the above' as an answer. You have a tendency to not place it as the correct answer and it's messing the quiz.
Please make sure to surround the csv content with markdown syntax to separate it from other regular content.

Here is an example of questions which got rejected and should not be generated:
```csv
"Which of the following is a key consideration when planning a migration using AWS Application Migration Service (MGN)?","Application dependencies","Network bandwidth","Cost optimisation","All of the above","'All of the above' Not allowed."
"Which of the following is a valid Sizing Policy when configuring video transcoding jobs in Elastic Transcoder?","Fit","Stretch","Fill","All of the above","'All of the above' Not allowed."
"When setting up an AWS Elemental MediaConnect flow, which of the following factors affects the overall cost?","The amount of data transferred","The number of destinations configured","The region where the flow is deployed","All of the above","'All of the above' Not allowed."
"Which of the following video codecs is supported as an output by AWS Elemental MediaConvert?","H.264","VP9","AV1","All of the above","'All of the above' Not allowed."
"Which of the following AWS services can be used to manage the security of migrated servers after using Application Migration Service (MGN)?","AWS Security Hub","AWS Systems Manager","AWS CloudTrail","All of the above","'All of the above' Not allowed."
"What type of testing is particularly important when rehosting a mainframe application with AWS Mainframe Modernization service?","Functional testing","Performance testing","Regression testing","All of the above","'All of the above' Not allowed."
"When configuring AWS DataSync, what is the purpose of the "exclude" filter?","To exclude specific files or directories from the transfer","To exclude specific users from accessing the data","To exclude specific network ranges from the transfer","To exclude specific S3 bucket policies","Invalid quoting, should have used 'exclude'"
```

Here is an example I did for DynamoDB for the AWS Certified professional exam:

```csv
"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"What is a use case for DynamoDB Streams?","Building real-time applications","Storing large binary files","Running complex analytical queries","Managing user access control","DynamoDB Streams can be used to build real-time applications by capturing and reacting to changes in the table data as they occur."
"What is the purpose of the Amazon DynamoDB Backup and Restore feature?","To create on-demand backups of table data","To encrypt data in transit","To scale read and write capacity","To manage user permissions","The Backup and Restore feature in DynamoDB allows users to create on-demand backups of their table data, which can be restored at any time."
"What does the DynamoDB Consistent Read operation ensure?","That the most recent write is returned","That the cheapest read operation is performed","That the data is eventually consistent","That the read is faster than an eventual consistent read","A consistent read operation in DynamoDB ensures that the most recent write is returned, providing strong consistency for read requests."
"What is the function of the DynamoDB Table Class?","To reduce storage costs for infrequently accessed data","To increase write throughput","To manage access control policies","To enable multi-region replication","The DynamoDB Table Class allows users to choose a cost-optimised storage class for infrequently accessed data, reducing storage costs."
```

Here is an example I did for AWS RDS for the AWS Associate developer exam:

```csv
"question","correct_answer","wrong_answer1","wrong_answer2","wrong_answer3","rationale"
"You are monitoring the performance of your AWS RDS instance and notice a high number of database connections. Which CloudWatch metric indicates this?","DatabaseConnections","CPUUtilization","FreeableMemory","NetworkReceiveThroughput","DatabaseConnections shows the number of active database connections to the instance."
"A developer needs to create an AWS RDS instance using the AWS CLI. Which command is used to initiate the creation process?","aws rds create-db-instance","aws rds run-db-instance","aws rds provision-db-instance","aws rds start-db-instance","The `aws rds create-db-instance` command is used to create a new RDS DB instance."
"You need to restore an AWS RDS instance to a specific point in time within your backup retention period. Which feature allows you to do this?","Point-in-Time Restore","Manual Snapshot Restore","Automated Backup Restore","Read Replica Promotion","Point-in-Time Restore allows you to restore a DB instance to any specific second within the backup retention period."
"What is the purpose of Amazon RDS Deletion Protection?","To prevent accidental deletion of your database instance","To encrypt database data","To control database access","To monitor database activity","Amazon RDS Deletion Protection prevents accidental deletion of your database instance, adding an extra layer of protection for critical databases."
"You are optimising the performance of an AWS RDS instance and notice a high number of write IOPS. Which action can you take to improve write performance?","Increase the Provisioned IOPS for the storage volume.","Increase the allocated storage.","Enable Multi-AZ.","Create a read replica.","Increasing the Provisioned IOPS for the storage volume is the most direct way to improve write performance for I/O-intensive workloads."
```

"""

    try:
        response = client.models.generate_content(model=model, contents=query)
        return response.text
    except Exception as e:
        logging.error(f"Error during Gemini API call: {e}")
        return ""


def filter_lines(text: str) -> List[str]:
    """
    Filters and processes lines from a text containing CSV data.

    Filters out lines that:
    - Cannot be parsed as valid CSV records.
    - Do not have exactly 6 fields.
    - Contain the phrases 'All of the above' or 'None of the above' in any field.

    Replaces double quotes (") with single quotes (') within each field of valid records.

    Args:
        text: The text containing CSV data.

    Returns:
        A list of processed lines that meet the filtering criteria.
    """
    logging.info("Filtering and processing lines using the csv library.")
    filtered_lines: List[str] = []
    # Use io.StringIO to treat the text as a file
    
    text_io = io.StringIO(text)
    reader = csv.reader(text_io)

    for line_num, fields in enumerate(reader):
        logging.debug(f"Processing line {line_num + 1}: {fields}")

        # Check if the record has exactly 6 fields
        if len(fields) != 6:
            logging.debug(f"Excluding line {line_num + 1} due to incorrect number of fields ({len(fields)}): {fields}")
            continue

        # Check if any field contains forbidden phrases
        contains_forbidden = False
        for field in fields:
            if "All of the above" in field or "None of the above" in field:
                logging.debug(f"Excluding line {line_num + 1} due to forbidden phrase in field '{field}': {fields}")
                contains_forbidden = True
                break

        if contains_forbidden:
            continue

        # Replace double quotes with single quotes in each field
        processed_fields = [field.replace('"', "'") for field in fields]

        # Reconstruct the CSV line
        # Use a temporary StringIO object to write the processed fields back to a CSV string
        output_io = io.StringIO()
        writer = csv.writer(output_io)
        writer.writerow(processed_fields)
        processed_line = output_io.getvalue().strip() # strip to remove trailing newline

        filtered_lines.append(processed_line)

    logging.info(f"Filtered and processed lines, found {len(filtered_lines)} valid records.")
    return filtered_lines

def main(model: str, topic: List[str], questions: int, batch_size: int, guidance_file: str) -> None:
    """
    Generates multiple choice questions about a given topic using the Gemini API.

    Args:
        model: The name of the Gemini model to use.
        topic: The topic of the quiz questions.
        questions: The total number of questions to generate.
        batch_size: The number of questions to generate in each batch.
        guidance_file: Path to a prompt file containing instructions for guiding the quiz generation.
    """
    logging.info(f"batch size: {batch_size}")
    logging.info(f"questions: {questions}")

    if (batch_size > questions):
        batch_size = questions  # constrain the batch size to make sense

    num_batches: int = questions // batch_size
    questions_per_batch: int = questions // num_batches
    logging.info(f"number of batches: {num_batches}")
    logging.info(f"questions per batches: {questions_per_batch}")

    prompt_guidance=""
    if args.guidance_file:
        with open(args.guidance_file, "r") as f:
            guidance = f.read()

    for i in range(0, num_batches):
        logging.info(f"Processing batch {i+1}/{num_batches}")
        unparsed_result: str = run_query(model=model, topic=topic, num_questions=questions_per_batch, prompt_guidance=prompt_guidance)
        filtered_lines: List[str] = filter_lines(unparsed_result)

        for idx, line in enumerate(filtered_lines):
            if (i !=0 and idx !=0) or (i == 0):
                # will print the line if on the first batch
                # will print the line if not on the first batch and not on the first line
                print(line)
            logging.debug(f"Printing line {{idx+1}}/{{len(filtered_lines)}}: {{line}}")


if __name__ == "__main__":
    """
    Entry point of the script.
    Configures logging, parses arguments, and runs the main function.

    Example:
        python quiz_gemini.py --model gemini-2.0-flash-001 --topic "EC2" --questions 10 --batch-size 5 --guidance "I just want to do this for fun"

    """
    logging.basicConfig(level=logging.INFO)

    args = args_parse()
    main(model=args.model, topic=args.topic, questions=args.questions, batch_size=args.batch_size, guidance_file=args.guidance_file)
