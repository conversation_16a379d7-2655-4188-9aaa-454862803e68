import { axiosInstance } from '../utils/axiosConfig';
import { storeTokens, clearTokens, storeBasicAuth, clearUserProfile } from '../utils/auth';

export const authService = {

    /**
     * Authenticates a user using username/email and password.
     * Initially uses basic auth, then switches to JWT if available.
     *
     * @param {string} usernameOrEmail - The user's username or email
     * @param {string} password - The user's password
     * @returns {Promise<Object>} User profile and token information
     * @throws {Error} If authentication fails
     */
    async loginWithCredentials(usernameOrEmail, password) {
        try {
            const basicAuth = btoa(`${usernameOrEmail}:${password}`);
            storeBasicAuth(basicAuth);

            const response = await axiosInstance.get('/user/profile');

            if (response.data.tokens) {
                storeTokens(response.data.tokens);
            }

            // Store the user profile in session storage
            sessionStorage.setItem('userProfile', JSON.stringify(response.data));

            // Clear any legacy storage
            localStorage.removeItem('userProfile');
            localStorage.removeItem('userInfo');

            return response.data;
        } catch (error) {
            clearTokens();
            clearUserProfile();
            throw error;
        }
    },

    /**
     * Handles OAuth callback by exchanging authorization code for tokens.
     *
     * @param {string} code - OAuth authorization code
     * @param {string} state - OAuth state parameter for verification
     * @returns {Promise<Object>} Token response from the server
     * @throws {Error} If OAuth callback processing fails
     */
    async handleOAuthCallback(code, state) {
        try {
            const response = await axiosInstance.get('/oauth/callback', {
                params: { code, state }
            });
            storeTokens(response.data);

            // Fetch and store the user profile
            try {
                const profileResponse = await this.getProfile();
                sessionStorage.setItem('userProfile', JSON.stringify(profileResponse));
            } catch (profileError) {
                console.warn('Failed to fetch user profile after OAuth login:', profileError);
            }

            return response.data;
        } catch (error) {
            clearTokens();
            clearUserProfile();
            throw error;
        }
    },

    /**
     * Registers a new user account.
     *
     * @param {Object} userData - User registration data
     * @returns {Promise<Object>} Created user profile
     * @throws {Error} If registration fails
     */
    async signup(userData) {
        const response = await axiosInstance.post('/user/signup', userData);
        return response.data;
    },

    /**
     * Logs out the current user and clears authentication data.
     *
     * @returns {Promise<void>}
     */
    async logout() {
        try {
            await axiosInstance.post('/auth/logout');
        } catch (error) {
            console.warn('Logout endpoint error:', error);
        } finally {
            clearTokens();
            clearUserProfile();
        }
    },

    /**
     * Retrieves the current user's profile information.
     *
     * @returns {Promise<Object>} User profile data
     * @throws {Error} If profile retrieval fails
     */
    async getProfile() {
        const response = await axiosInstance.get('/user/profile');
        return response.data;
    },

    /**
     * Refreshes the current JWT access token using the refresh token.
     *
     * @returns {Promise<Object>} New token response
     * @throws {Error} If token refresh fails
     */
    async refreshToken() {
        const refreshToken = sessionStorage.getItem('refreshToken');
        if (!refreshToken) {
            throw new Error('No refresh token available');
        }

        const response = await axiosInstance.post('/oauth/refresh', null, {
            headers: {
                'X-Refresh-Token': refreshToken,
                'Content-Type': 'application/json'
            }
        });
        storeTokens(response.data);
        return response.data;
    },

    /**
     * Checks if the user is currently authenticated.
     *
     * @returns {boolean} True if user has valid authentication credentials
     */
    isAuthenticated() {
        const accessToken = sessionStorage.getItem('accessToken');
        const basicAuth = sessionStorage.getItem('encodedUsernameAndPassword');
        return !!(accessToken || basicAuth);
    }
};
