
import org.apache.tools.ant.filters.ReplaceTokens
import com.github.jengelman.gradle.plugins.shadow.transformers.*

buildscript {
    repositories {
	    mavenCentral()
    }
}

plugins {
	id 'java'
	id 'org.springframework.boot' version '3.2.4'
	id 'io.spring.dependency-management' version '1.1.4'
	id 'io.freefair.lombok' version '8.6'
	id 'com.palantir.git-version' version '3.0.0'
	id 'com.github.johnrengelman.shadow' version '8.1.1'
	id 'jacoco'
	id 'checkstyle'
	id "net.serenity-bdd.serenity-gradle-plugin" version "4.0.19"
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'idea'


tasks.register('openApiSplit', Exec) {
    group "openapi tools"
    description "Split the OpenAPI documentation into multiple files."
    executable "npx"
    args = [
            "@redocly/cli",
            "split",
            "src/docs/openapi.yaml",
            "--outDir",
            "src/docs/openapi/split"
    ]
}

tasks.register('openApiBundle', Exec) {
    group "openapi tools"
    description "Bundle the split OpenAPI documentation into a single file."
    executable "npx"
    args = [
            "@redocly/cli",
            "bundle",
            "src/docs/openapi/split/openapi.yaml",
            "-o",
            "src/docs/openapi.yaml"
    ]
}

tasks.register('openApiJoin', Exec) {
    group "openapi tools"
    description "Join the split OpenAPI documentation."
    executable "npx"
    args = [
            "@redocly/cli",
            "join",
            "src/docs/openapi/split/index.yaml",
            "--output",
            "build/generated/openapi.yaml"
    ]
}





group = 'fukoka'
version = "git describe --long --dirty --always".execute().text.trim()

apply from : 'gradle/openapi.gradle'
apply from : 'gradle/docker.gradle'
apply from : 'gradle/project_info.gradle'
apply from : 'gradle/database.gradle'

java {
	sourceCompatibility = '17'
}

jar {
	manifest {
		attributes 'Main-Class': 'fukoka.fquiz.Application'
	}
}


sourceSets {
    dbTest {
        java {
            srcDir 'src/dbTest/java'
            compileClasspath += sourceSets.main.output + sourceSets.main.compileClasspath
            runtimeClasspath += sourceSets.main.output + sourceSets.main.runtimeClasspath
        }
        resources {
            srcDir 'src/dbTest/resources'
        }
    }

    awsLambda {
        // the classes of the application are included
        compileClasspath += sourceSets.main.output
        runtimeClasspath += sourceSets.main.output
    }

    acceptanceTest
}

configurations {
    dbTestImplementation.extendsFrom implementation
    dbTestRuntimeOnly.extendsFrom runtimeOnly
    dbTestAnnotationProcessor.extendsFrom annotationProcessor

    // dependencies of the application which are included for the aws lambda
    // since we'd be deployed in the cloud using lambda, we don't want to include the tomcat embed dependencies and
    // we will be connecting to a real database so we can also remove h2.
    // since we're in a lambda environment, we don't need the actuator either
    awsLambdaImplementation.extendsFrom implementation
    awsLambdaRuntimeClasspath.extendsFrom productionRuntimeClasspath
//			.exclude(group: 'org.apache.tomcat.embed')
//			.exclude(group: 'com.h2database', module: 'h2')
//			.exclude(group: "org.springframework.boot", module: "spring-boot-starter-actuator")

}


shadowJar {
	archiveClassifier = 'aws_lambda'
	from sourceSets.awsLambda.output
	configurations = [project.configurations.awsLambdaRuntimeClasspath]

	// Required for Spring
	mergeServiceFiles()
	append 'META-INF/spring.handlers'
	append 'META-INF/spring.schemas'
	append 'META-INF/spring.tooling'
	append 'META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports'
	append 'META-INF/spring/org.springframework.boot.actuate.autoconfigure.web.ManagementContextConfiguration.imports'
	transform(PropertiesFileTransformer) {
		paths = ['META-INF/spring.factories' ]
		mergeStrategy = "append"
	}

}


repositories {
	mavenCentral()
	maven {
        url "https://download.red-gate.com/maven/release"
    }
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation "org.springframework.boot:spring-boot-starter-security"
	implementation 'org.apache.commons:commons-csv:1.8'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'io.jsonwebtoken:jjwt:0.12.6'
	implementation 'org.apache.commons:commons-csv:1.12.0'
	implementation 'org.simplejavamail:simple-java-mail:8.11.2'
	implementation 'org.simplejavamail:spring-module:8.11.2'
	implementation 'org.simplejavamail:batch-module:8.11.2'
	runtimeOnly 'org.postgresql:postgresql:42.7.3'
	implementation 'org.flywaydb:flyway-core:11.4.1'
	implementation 'org.flywaydb:flyway-database-postgresql:11.4.1'
    implementation 'com.stripe:stripe-java:29.0.0'

	// aws lambda - see https://github.com/aws/serverless-java-container/wiki/Quick-start---Spring-Boot3
	awsLambdaImplementation 'com.amazonaws.serverless:aws-serverless-java-container-springboot3:2.0.1'
	// external calls
	implementation 'com.squareup.retrofit2:retrofit:2.9.0'
	implementation 'com.squareup.retrofit2:converter-jackson:2.9.0'
	implementation 'com.squareup.okhttp3:okhttp:4.10.0'
	implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'


	// devtools
	developmentOnly 'org.springframework.boot:spring-boot-devtools'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testImplementation 'org.assertj:assertj-core:3.25.3'
	testImplementation ('com.github.javafaker:javafaker:1.0.1'){ exclude( module: 'snakeyaml' ) }
	testImplementation 'org.testcontainers:testcontainers:1.19.3'
	testImplementation 'org.testcontainers:postgresql:1.19.3'
	testImplementation 'org.dbunit:dbunit:2.7.3'
	testImplementation 'com.github.database-rider:rider-spring:1.41.0'
	testImplementation 'com.github.database-rider:rider-junit5:1.41.0'

	acceptanceTestImplementation 'org.springframework.boot:spring-boot-starter-test'
	acceptanceTestImplementation 'org.junit.platform:junit-platform-suite:1.10.2'
	acceptanceTestImplementation 'io.cucumber:cucumber-java:7.15.0'
	acceptanceTestImplementation 'io.cucumber:cucumber-junit-platform-engine:7.15.0'
	acceptanceTestImplementation 'io.cucumber:cucumber-spring:7.15.0'
	acceptanceTestImplementation 'io.rest-assured:rest-assured:5.4.0'
	acceptanceTestImplementation 'org.assertj:assertj-core:3.25.3'
	acceptanceTestImplementation 'com.fasterxml.jackson.core:jackson-databind:2.13.0'
	acceptanceTestImplementation ('com.github.javafaker:javafaker:1.0.1'){ exclude( module: 'snakeyaml' ) }
	acceptanceTestImplementation 'org.awaitility:awaitility:4.2.0'

	// Serenity BDD dependencies - minimal set needed for Cucumber with JUnit 5
	acceptanceTestImplementation "net.serenity-bdd:serenity-core:4.0.19"        // Core Serenity functionality
	acceptanceTestImplementation "net.serenity-bdd:serenity-cucumber:4.0.19"     // Cucumber integration
	acceptanceTestImplementation "net.serenity-bdd:serenity-rest-assured:4.0.19" // RestAssured integration
	acceptanceTestImplementation "net.serenity-bdd:serenity-spring:4.0.19"      // Spring integration
	acceptanceTestImplementation "net.serenity-bdd:serenity-junit5:4.0.19"      // JUnit 5 integration

	// Database test dependencies
	dbTestImplementation sourceSets.main.output
	dbTestImplementation 'org.springframework.boot:spring-boot-starter-test'
	dbTestImplementation 'org.assertj:assertj-core:3.25.3'
	dbTestImplementation 'org.testcontainers:testcontainers:1.19.3'
	dbTestImplementation 'org.testcontainers:postgresql:1.19.3'
	dbTestImplementation 'org.testcontainers:junit-jupiter:1.19.3'
	dbTestImplementation 'org.dbunit:dbunit:2.7.3'
	dbTestImplementation 'com.github.database-rider:rider-spring:1.41.0'
	dbTestImplementation 'com.github.database-rider:rider-junit5:1.41.0'
}

tasks.named('test') {
	useJUnitPlatform()
	finalizedBy jacocoTestReport
}

serenity {
    reports = ["single-page-html"]
    testRoot = "fukoka.fquiz"
    requirementsDir = "src/acceptanceTest/resources/features"
    outputDirectory = "${project.reporting.baseDir}/serenity"
}

tasks.register('acceptanceTest', Test) {
    description = 'Runs acceptance tests.'
    group = 'verification'

    testClassesDirs = sourceSets.acceptanceTest.output.classesDirs
    classpath = sourceSets.acceptanceTest.runtimeClasspath

    systemProperty 'spring.profiles.active', System.getProperty('profile', 'docker-compose-prism')
    // Pass cucumber.filter.tags from project property to system property
    if (project.hasProperty('cucumber.filter.tags')) {
        systemProperty 'cucumber.filter.tags', project.getProperty('cucumber.filter.tags')
    }

    useJUnitPlatform()
    testLogging.showStandardStreams = true
    systemProperties System.getProperties()

    finalizedBy aggregate
}


tasks.named("bootBuildImage") {
    imageName = "docker.quizalto.com/${project.group}/${project.name}:${project.version}"
    createdDate = "now"
    docker {
        publishRegistry {
            username = docker_registry_username
            password = docker_registry_password
            url = "docker.quizalto.com"
        }
    }

    // Simple health check configuration
    environment = [
        "BP_HEALTH_CHECKER_ENABLED": "true"
    ]

    buildpacks = [
        "urn:cnb:builder:paketo-buildpacks/java",
        "docker.io/paketobuildpacks/health-checker:2.9.0",
        "docker.io/paketobuildpacks/new-relic"
    ]
    bindings = ["${projectDir}/src/main/docker/buildpack-bindings/new-relic:/platform/bindings/new-relic"]
}

springBoot {
    buildInfo()
}

tasks.register('downloadGoogleChecks') {
    def configFile = file("${rootDir}/checkstyle/google_checks.xml")
    outputs.file(configFile)

    doLast {
        configFile.parentFile.mkdirs()
        new URL('https://raw.githubusercontent.com/checkstyle/checkstyle/master/src/main/resources/google_checks.xml')
            .withInputStream { i -> configFile.withOutputStream { it << i } }
    }
}

checkstyle {
    toolVersion = '10.21.4'
    configFile = project.file("${rootDir}/checkstyle/fukoka_checks.xml")
    configDirectory = project.file("${rootDir}/checkstyle")
    maxWarnings = 0
    showViolations = true
    ignoreFailures = false
    sourceSets = [sourceSets.main]    // Only check main sourceSet
}

tasks.withType(Checkstyle).configureEach {
    reports {
        xml.required = true
        html.required = true
    }
}

// --- Move dbTest registration block here ---
tasks.register('dbTest', Test) {
    description = 'Runs database tests.'
    group = 'verification'

    // Add JaCoCo configuration here
    jacoco {
        destinationFile = file("${buildDir}/jacoco/dbTest.exec")
        // Optional: Configure includes/excludes if needed for dbTest specifically
        // includes = ['fukoka.fquiz.repositories.*']
        // excludes = ['fukoka.fquiz.api.*']
    }

    testClassesDirs = sourceSets.dbTest.output.classesDirs
    classpath = sourceSets.dbTest.runtimeClasspath

    useJUnitPlatform()

    // Ensure database tests run after main tests
    mustRunAfter test
}

jacocoTestReport {
    // dependsOn test // This is implicitly handled by executionData sources now
    dependsOn test, dbTest // Explicitly depend on both test tasks

    // Configure sources for the report
    sourceSets sourceSets.main

    // Specify execution data files from both tasks
    executionData fileTree(buildDir).include("/jacoco/test.exec", "/jacoco/dbTest.exec")

    reports {
        xml.required = true // Keep XML for the parser script
        csv.required = false
        html.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.8
            }
        }
    }
}

// --- dbTest registration block was moved from here ---

check.dependsOn dbTest

tasks.named('processDbTestResources') {
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}

// Task to clean acceptance test artifacts
tasks.register('cleanTarget', Delete) {
    description = 'Cleans the target directory'
    group = 'build'

    // Delete target directory contents
    // Delete target directory contents
    delete fileTree("${rootDir}/target").include("**/*")

}

// Enhance the standard clean task to also clean the target directory
tasks.named('clean') {
    dependsOn tasks.named('cleanTarget')
}

tasks.named('cleanAcceptanceTest'){
    dependsOn tasks.named('cleanTarget')
}
